package azure

import (
	"bytes"
	"context"
	"kc/internal/ondc/config"
	"log"

	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/blob"
)

type AzureClient struct {
	Container string
	Blob      string
	Client    *config.AzureConfig
}

func (az *AzureClient) GetAzureClient(ctx context.Context) (*azblob.Client, error) {
	credential, err := azblob.NewSharedKeyCredential(az.Client.AccountName, az.Client.AccountKey)
	if err != nil {
		log.Fatal("failed to get credentials, error is ", err)
		return nil, err
	}

	serviceClient, err := azblob.NewClientWithSharedKeyCredential(az.Client.URL, credential, nil)
	if err != nil {
		log.Fatal("failed to get NewServiceClientWithSharedKey, error is ", err)
		return nil, err
	}
	return serviceClient, nil
}

func (az *AzureClient) UploadFileToBlob(ctx context.Context, data *[]byte, contentType string) error {
	client, err := az.GetAzureClient(ctx)
	if err != nil {
		log.Fatal("failed to connect to client, error is ", err)
		return err
	}
	blobContentReader := bytes.NewReader(*data)
	_, err = client.UploadStream(ctx, az.Container, az.Blob, blobContentReader, &azblob.UploadStreamOptions{
		// Metadata: map[string]string{"WhatsApp": "Data"},
		// Tags:     map[string]string{"ContentType": "content_type"},
		HTTPHeaders: &blob.HTTPHeaders{
			BlobContentType: &contentType,
		},
	})
	if err != nil {
		return err
	}
	return nil
}
