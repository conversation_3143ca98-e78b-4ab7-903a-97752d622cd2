package routes

func (app *App) setupLogisticsRoutes() {
	// EasyEcom Routes
	easyEcomRoutes := app.router.Group("/easyecom")
	easyEcomRoutes.POST("/order/create", app.handler.CreateEasyEcomOrder)
	easyEcomRoutes.POST("/order/details", app.handler.GetEasyEcomOrderDetails)
	easyEcomRoutes.POST("/webhook", app.handler.EasyEcomWebhook)

	// ShipRocket Routes
	shipRocketRoutes := app.router.Group("/shiprocket")
	shipRocketRoutes.POST("/service/ability", app.handler.CheckShipRocketServiceAbility)

	// Delhivery Routes
	delhiveryRoutes := app.router.Group("/delhivery")
	delhiveryRoutes.POST("/service/ability", app.handler.CheckDelhiveryServiceAbility)
	delhiveryRoutes.POST("/track", app.handler.TrackDelhiveryCourier)
	delhiveryRoutes.POST("/webhook", app.handler.DelhiveryWebhook)

	// Shipway Routes
	shipwayRoutes := app.router.Group("/shipway")
	shipwayRoutes.POST("/create_order", app.handler.CreateShipwayOrder)
	shipwayRoutes.POST("/webhook", app.handler.HandleShipwayWebhook)
	shipwayRoutes.GET("/webhook", app.handler.TestShipwayWebhook)
	shipwayRoutes.POST("/order_details", app.handler.GetShipwayOrderDetails)

	// ExpressBees Routes
	expressBeesRoutes := app.router.Group("/expressbees")
	expressBeesRoutes.POST("/webhook", app.handler.ExpressBeesWebhook)

	// Shipdelight Routes
	shipdelightRoutes := app.router.Group("/shipdelight")
	shipdelightRoutes.POST("/webhook", app.handler.ShipDelightWebhook)

	// Ekart Routes
	ekartRoutes := app.router.Group("/ekart")
	ekartRoutes.POST("/webhook", app.handler.EkartWebhook)
	ekartRoutes.GET("/webhook", app.handler.EkartWebhook)
}
