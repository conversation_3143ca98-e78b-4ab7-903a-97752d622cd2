package routes

func (app *App) setupIGMRoutes() {
	igmRoutes := app.router.Group("/igm")
	igmRoutes.POST("/tickets", app.handler.GetIGMTickets)
	igmRoutes.GET("/ticket", app.handler.GetIGMTicketDetails)
	igmRoutes.POST("/ticket", app.handler.CreateIGMTicket)
	igmRoutes.PUT("/ticket", app.handler.UpdateIGMTicket)
	igmRoutes.GET("/faq", app.handler.GetIGMFAQ)
	igmRoutes.POST("/orders", app.handler.GetIGMOrders)
	igmRoutes.GET("/categories", app.handler.GetIssuesCategories)
	igmRoutes.POST("/media", app.handler.MediaUpload)
	igmRoutes.GET("/conversations", app.handler.GetIGMConversations)
	igmRoutes.POST("/conversations", app.handler.AddIGMConversations)
}
