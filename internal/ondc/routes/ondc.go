package routes

func (app *App) setupONDCRoutes() {
	app.router.POST("/on_subscribe", app.handler.OnSubscribe)
	app.router.POST("/search", app.handler.Search)
	app.router.POST("/on_search", app.handler.OnSearch)
	app.router.POST("/select", app.handler.Select)
	app.router.POST("/on_select", app.handler.OnSelect)
	app.router.POST("/init", app.handler.Init)
	app.router.POST("/on_init", app.handler.OnInit)
	app.router.POST("/confirm", app.handler.Confirm)
	app.router.POST("/on_confirm", app.handler.OnConfirm)
	app.router.POST("/status", app.handler.Status)
	app.router.POST("/on_status", app.handler.OnStatus)
	app.router.POST("/cancel", app.handler.Cancel)
	app.router.POST("/on_cancel", app.handler.OnCancel)
	app.router.POST("/issue", app.handler.Issues)
	app.router.POST("/on_issue", app.handler.OnIssues)
	app.router.POST("/issue_status", app.handler.IssueStatus)
	app.router.POST("/on_issue_status", app.handler.OnIssueStatus)
	app.router.GET("/ondc-site-verification.html", app.handler.SiteVerificationHandler)
	app.router.GET("/categories", app.handler.Categories)
	app.router.POST("/categories", app.handler.GetCategories)
}
