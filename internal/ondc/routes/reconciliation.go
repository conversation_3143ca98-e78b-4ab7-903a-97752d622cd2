package routes

func (app *App) setupReconciliationRoutes() {
	reconciliationRoutes := app.router.Group("/reconciliation")
	reconciliationRoutes.POST("/gross_sales", app.handler.GrossSales)
	reconciliationRoutes.POST("/order_placed", app.handler.OrderPlaced)
	reconciliationRoutes.POST("/average_order_value", app.handler.AverageOrderValue)
	reconciliationRoutes.POST("/confirmed_order", app.handler.ConfirmedValue)
	reconciliationRoutes.POST("/delivered_order", app.handler.DeliveredOrder)
	reconciliationRoutes.POST("/dispatched_order", app.handler.DispatchedOrderNotDelivered)
	reconciliationRoutes.POST("/cancelled_order", app.handler.CancelledOrder)
	reconciliationRoutes.POST("/pending_confirmation", app.handler.PendingConfirmation)
	reconciliationRoutes.POST("/pending_delivery", app.handler.PendingDispatch)
	reconciliationRoutes.POST("/transit_order", app.handler.TransitOrder)
	reconciliationRoutes.POST("/returned_order", app.handler.OrdersReturned)
	reconciliationRoutes.POST("/delayed_order", app.handler.DelayedOrder)
	reconciliationRoutes.POST("/average_delivery_time", app.handler.AverageDeliveryTime)
	reconciliationRoutes.POST("/average_confirmation_time", app.handler.AverageConfirmationTime)
	reconciliationRoutes.POST("/add_reconciliation_data", app.handler.AddDataForReconciliation)
	reconciliationRoutes.POST("/get_reconciliation_data", app.handler.GetReconciliationData)
}
