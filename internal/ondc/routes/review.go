package routes

func (app *App) setupReviewRoutes() {
	reviewRatingsRoutes := app.router.Group("/product_review")
	reviewRatingsRoutes.POST("/add_review", app.handler.AddProductReview)
	reviewRatingsRoutes.POST("/add_multiple_reviews", app.handler.AddMultipleProductsReviews)
	reviewRatingsRoutes.POST("/get_reviews", app.handler.GetProductReviewRatingsWidgets)
	reviewRatingsRoutes.POST("/get_product_reviews", app.handler.GetProductReviews)
	reviewRatingsRoutes.POST("/update_review_media", app.handler.UpdateReviewMedia)
}
