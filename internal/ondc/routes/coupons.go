package routes

func (app *App) setupCouponRoutes() {
	couponRoutes := app.router.Group("/coupons")
	couponRoutes.GET("/name_map", app.handler.GetCouponNameMap)
	couponRoutes.GET("/by_code", app.handler.GetCouponByCode)
	couponRoutes.GET("/by_name", app.handler.GetCouponByName)

	couponRoutes.POST("", app.handler.GetAllCoupons)
	couponRoutes.GET("/:id", app.handler.GetCouponByID)
	couponRoutes.POST("/edit", app.handler.EditCoupon)
	couponRoutes.POST("/add", app.handler.AddCoupon)

	// CRUD endpoints for brands
	couponRoutes.GET("/brands/all_ids", app.handler.GetAllBrandIds)
	couponRoutes.GET("/brand/:coupon_id", app.handler.GetCouponBrandsByCouponID)

	// CRUD endpoints for cohorts
	couponRoutes.GET("/cohorts/all_ids", app.handler.GetAllCohortIds)
	couponRoutes.GET("/cohort/:coupon_id", app.handler.GetCouponCohortsByCouponID)

	// CRUD endpoints for rules
	couponRoutes.GET("/rules/:coupon_id", app.handler.GetCouponRulesByCouponID)

	// Add user coupon
	couponRoutes.POST("/user/add", app.handler.AddUserCoupon)
	couponRoutes.POST("/user/deactivate", app.handler.DeactivateUserCoupon)
}
