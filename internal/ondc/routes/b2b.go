package routes

import (
	"kc/internal/ondc/middleware"
	"time"
)

func (app *App) setupB2BRoutes() {
	b2bDashboardAuthRoutes := app.router.Group("/b2b")
	b2bDashboardAuthRoutes.Use(middleware.CSRFMiddleware())
	b2bDashboardAuthRoutes.Use(middleware.RateLimitMiddleware(100, time.Minute))
	b2bDashboardAuthRoutes.Use(middleware.AuthMiddleware())
	// Protected routes for B2B dashboard
	b2bDashboardAuthRoutes.POST("/get_orders_extended", app.handler.GetOrdersExtended)

	b2bDashboardRoutes := app.router.Group("/b2b")
	b2bDashboardRoutes.POST("/meta", app.handler.GetB2BMeta)
	b2bDashboardRoutes.POST("/get_orders", app.handler.GetOrders)
	b2bDashboardRoutes.POST("/cancel_order", app.handler.CancelOrderB2B)
	b2bDashboardRoutes.POST("/get_calling_orders", app.handler.GetCallingOrders)
	b2bDashboardRoutes.POST("/update_order_status", app.handler.UpdateB2BOrderStatus)
	b2bDashboardRoutes.POST("/update_order_address", app.handler.UpdateOrderAddress)
	b2bDashboardRoutes.POST("/export_orders", app.handler.ExportOrders)
	b2bDashboardRoutes.POST("/export_orders_csv", app.handler.ExportOrdersCSV)
	b2bDashboardRoutes.POST("/export_product_catalogue", app.handler.ExportProductCatalogue)
	b2bDashboardRoutes.POST("/export_product_catalogue_csv", app.handler.ExportProductCatalogueCSV)

	// these are the export routes for created by sanket
	b2bDashboardRoutes.POST("/export_orders_generic", app.handler.ExportOrdersGeneric)                         // this returns the records at order level called via brandpulse
	b2bDashboardRoutes.POST("/export_orders_generic/csv", app.handler.ExportOrdersGenericCSV)                  // this returns the csv at order level called directly by b2b
	b2bDashboardRoutes.POST("/export_orders_generic_skulevel", app.handler.ExportOrdersGenericSKULevel)        //  this returns records at sku level called via brandpulse
	b2bDashboardRoutes.POST("/export_orders_generic_skulevel/csv", app.handler.ExportOrdersGenericSKULevelCSV) // this returns csv at sku level called directly by b2b

	b2bDashboardRoutes.POST("/get_pending_shipments", app.handler.GetPendingShipments)
	b2bDashboardRoutes.POST("/get_brand_insights", app.handler.GetBrandInsights)
	b2bDashboardRoutes.GET("/carriers", app.handler.GetCarriers)
	b2bDashboardRoutes.POST("/update_order_meta", app.handler.UpdateOrderMetaData)
	b2bDashboardRoutes.POST("/archive_order", app.handler.ArchiveOrder)
	b2bDashboardRoutes.POST("/get_ndr_orders", app.handler.GetNDROrders)
	b2bDashboardRoutes.POST("/update_ndr_order", app.handler.UpdateNDROrder)
	b2bDashboardRoutes.POST("/add_ndr_order", app.handler.AddNDROrder)
	b2bDashboardRoutes.POST("/escalate_to_3pl", app.handler.EscalateTo3PL)
	b2bDashboardRoutes.POST("/get_order_activity", app.handler.GetOrderActivityLogs)
	b2bDashboardRoutes.POST("/get_categories", app.handler.GetB2BCategories)
	b2bDashboardRoutes.POST("/get_products", app.handler.GetB2BProducts)
	b2bDashboardRoutes.POST("/update_bulk_data", app.handler.UpdateBulkData)
	b2bDashboardRoutes.POST("/get_bulk_action_logs", app.handler.GetBulkActionLogs)
	b2bDashboardRoutes.POST("/bill_difference", app.handler.BillDifference)
	b2bDashboardRoutes.POST("/update_order", app.handler.UpdateThirdPartySellerOrder)
	b2bDashboardRoutes.POST("/create_order", app.handler.CreateThirdPartySellerOrder)
	b2bDashboardRoutes.POST("/orders_payment_details", app.handler.GetOrdersPaymentDetails)
	b2bDashboardRoutes.POST("/export_orders_payment_details", app.handler.ExportOrderPaymentDetails)
	b2bDashboardRoutes.POST("/update_product_details", app.handler.UpdateProductDetails)
	b2bDashboardRoutes.POST("/add_product", app.handler.AddProduct)
	b2bDashboardRoutes.POST("/update_primary_waybill", app.handler.UpdatePrimaryWaybill)
	b2bDashboardRoutes.POST("/fetch_waybills", app.handler.FetchWallWaybills)
	b2bDashboardRoutes.POST("/get_waybill_data", app.handler.GetWayBillDetails)
	b2bDashboardRoutes.POST("/generate_picklist", app.handler.GetPickListPDF)
	b2bDashboardRoutes.POST("/generate_picklist_for_order", app.handler.CreatePickListForOrder)
	b2bDashboardRoutes.POST("/push_order_to_oms", app.handler.PushOrderToOMS)
	b2bDashboardRoutes.POST("/re_push_order_to_oms", app.handler.RePushOrderToOMS)
	b2bDashboardRoutes.POST("/push_order_to_delhivery", app.handler.PushOrderToDelhivery)
	b2bDashboardRoutes.POST("/push_order_to_ekart", app.handler.PushOrderToEkart)
	b2bDashboardRoutes.POST("/create_enhanced_index", app.handler.CreateElasticIndex)

	b2bDashboardRoutes.POST("/order/payment_details", app.handler.GetOrderPaymentDetails)
	b2bDashboardRoutes.POST("/sales-insights/kpi-cards", app.handler.GetKPICards)
	b2bDashboardRoutes.POST("/sales-insights/sku-leaderboard", app.handler.GetSKULeaderboard)
	b2bDashboardRoutes.POST("/sales-insights/sku-trends", app.handler.GetSKUTrends)

	// OMS
	b2bDashboardRoutes.POST("/create_ext_invoice", app.handler.CreateExtInvoice)
	b2bDashboardRoutes.POST("/create_shipping_label", app.handler.GenerateShippingLabel)
	b2bDashboardRoutes.POST("/generate_manifest", app.handler.GenerateManifest)
	b2bDashboardRoutes.POST("/convert_urls_to_zip", app.handler.ConvertUrlsToZip)
	b2bDashboardRoutes.POST("/get_shipment_created_invoices", app.handler.GetShipmentCreatedInvoices)
	b2bDashboardRoutes.POST("/get_shipment_created_labels", app.handler.GetShipmentCreatedLabels)
	b2bDashboardRoutes.POST("/get_shipment_created_manifests", app.handler.GetShipmentCreatedManifests)

	// seller
	b2bDashboardRoutes.POST("/get_sellers", app.handler.GetSellers)
	b2bDashboardRoutes.POST("/update_seller", app.handler.UpdateSellerDetails)
	b2bDashboardRoutes.POST("/create_seller", app.handler.AddSellerDetails)
	b2bDashboardRoutes.GET("/get_vendor_codes", app.handler.GetVendorCodes)

	b2bDashboardRoutes.POST("/get_ndr_orders_v2", app.handler.GetNDROrdersV2)
	b2bDashboardRoutes.POST("/update_ndr_order_v2", app.handler.UpdateNDROrderV2)

	b2bDashboardRoutes.POST("/export_ndr_orders", app.handler.ExportNDROrders)
	b2bDashboardRoutes.POST("/export_sku_leaderboard", app.handler.ExportLeaderBoardOrders)

	// loyalty proxy endpoints
	b2bDashboardRoutes.POST("/loyalty/get_user_tier_info", app.handler.GetUserTierInfo)
	b2bDashboardRoutes.POST("/loyalty/orders_history", app.handler.GetUsersOrderHistory)
	b2bDashboardRoutes.POST("/loyalty/tier_upgrade_history", app.handler.GetUsersTierUpgradeHistory)
	b2bDashboardRoutes.POST("/loyalty/coins_wallet", app.handler.GetUsersCoinsWallet)
	b2bDashboardRoutes.POST("/loyalty/cashback_wallet", app.handler.GetUsersCashbackWallet)

	//wrappers for invoice and label gen
	b2bDashboardRoutes.POST("/create_and_save_ext_invoice", app.handler.CreateAndSaveExtInvoice)
	b2bDashboardRoutes.POST("/create_and_save_printing_label", app.handler.CreateAndSavePrintingLabel)
	// inventory endpoints
	b2bDashboardRoutes.POST("/inventory/add_product", app.handler.AddProductInventory)
	b2bDashboardRoutes.POST("/inventory/export_products", app.handler.ExportInventoryProducts) // this returns the records at order level called via brandpulse
}
