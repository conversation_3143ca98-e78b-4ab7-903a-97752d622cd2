package routes

func (app *App) setupKiranaBazarRoutes() {
	kiranaBazarRoutes := app.router.Group("/kirana_bazar")
	kiranaBazarRoutes.POST("/product/details", app.handler.GetProductDetails)
	kiranaBazarRoutes.POST("/get_cart", app.handler.GetKiranaBazarCart)
	kiranaBazarRoutes.POST("/sync_cart", app.handler.UpsertKiranaBazarCart)
	kiranaBazarRoutes.POST("/order", app.handler.CreateKiranaBazarOrder)
	kiranaBazarRoutes.DELETE("/order", app.handler.CancelKiranaBazarOrder)
	kiranaBazarRoutes.POST("/get_order_cart", app.handler.GetCartDetailsForOrderDetails)
	kiranaBazarRoutes.POST("/update_zoff_order", app.handler.UpdateZoffOrder)
	kiranaBazarRoutes.POST("/orders", app.handler.GetKiranaBazarOrder)
	kiranaBazarRoutes.POST("/coupons", app.handler.GetCoupons)
	kiranaBazarRoutes.POST("/applicable_coupons", app.handler.GetApplicableCoupons)
	kiranaBazarRoutes.POST("/bill_details", app.handler.GetBillDetails)
	kiranaBazarRoutes.POST("/suggested_products", app.handler.GetSuggestedProducts)
	kiranaBazarRoutes.POST("/service/ability", app.handler.CheckServiceAbility)
	kiranaBazarRoutes.POST("/meta", app.handler.GetKiranaBazarMeta)
	kiranaBazarRoutes.POST("/web_meta", app.handler.GetKiranaBazarWebMeta)
	kiranaBazarRoutes.POST("/cart_screen_meta", app.handler.GetKiranaBazarCartScreenMeta)
	kiranaBazarRoutes.POST("/order_status", app.handler.GetOrderStatus)
	kiranaBazarRoutes.POST("/order_invoice/create", app.handler.CreateOrderInvoice)
	kiranaBazarRoutes.POST("/map_order_status", app.handler.MapOrderStatus)
	kiranaBazarRoutes.POST("/rewards", app.handler.GetUserRewards)
	kiranaBazarRoutes.POST("/bottom_sheet_coupons", app.handler.GetCouponsBottomSheet)
	kiranaBazarRoutes.POST("/loyalty_order", app.handler.CreateUserLoyaltyRewardOrder)
	kiranaBazarRoutes.POST("/category_widgets", app.handler.GetCategoryWidgets)
	kiranaBazarRoutes.POST("/ordering_onboarding", app.handler.GetOrderingOnboarding)
	kiranaBazarRoutes.POST("/recommended_products", app.handler.GetRecommendedProducts)

	kiranaBazarRoutesV2 := app.router.Group("/kirana_bazar/v2")
	kiranaBazarRoutesV2.POST("/get_products", app.handler.SearchV2)
	kiranaBazarRoutesV2.POST("/suggested_products", app.handler.GetSuggestedProductsV2)
	kiranaBazarRoutesV2.POST("/bill_details", app.handler.GetBillDetailsV2)
	kiranaBazarRoutesV2.POST("/sync_cart", app.handler.UpsertKiranaBazarCartV2)
	kiranaBazarRoutesV2.POST("/check_coupon", app.handler.CheckCoupon)
	kiranaBazarRoutesV2.POST("/search", app.handler.ProductsSearch)
	kiranaBazarRoutesV2.POST("/order", app.handler.CreateKiranaBazarOrderV2)

	kiranaBazarRoutesV3 := app.router.Group("/kirana_bazar/v3")
	kiranaBazarRoutesV3.POST("/bill_details", app.handler.GetBillDetailsV3)
	kiranaBazarRoutesV3.POST("/check_coupon", app.handler.CheckCouponV2)

}
