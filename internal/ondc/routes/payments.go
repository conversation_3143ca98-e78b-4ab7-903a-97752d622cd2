package routes

func (app *App) setupPaymentsRoutes() {
	// Zoho Routes
	routes := app.router.Group("/payment")
	routes.POST("/initiate_payment", app.handler.InitializePayment)
	routes.POST("/validate_payment", app.handler.ValidatePayment)
	routes.POST("/refund_advance_payment", app.handler.RefundAdvancePayment)
	routes.POST("/recon/refresh_order", app.handler.RefreshOrderPayment)

	routes.POST("/initiate_payment_and_create_order", app.handler.InitializePaymentAndCreateKiranaBazarOrder)
	routes.POST("/rp_webhook_type1", app.handler.RazorpayWebhookType1)
	routes.POST("/b2b/check_advance_payment_possibility", app.handler.CheckAdvancePaymentPossibility)
	routes.POST("/b2b/initiate_payment", app.handler.InitiatePaymentFromB2B)

	routes.POST("/get_qr_rnn", app.handler.GetQRRNN)
}
