package routes

func (app *App) setupExotelCallsRoutes() {

	app.router.POST("/webhooks/exotel/status", app.handler.ExotelStatusCallback)
	// Protected API endpoints
	callRoutes := app.router.Group("/api/v1/calls")
	{
		callRoutes.POST("/initiate", app.handler.InitiateCall)
		callRoutes.GET("/details/:call_id", app.handler.GetCallDetails)
		// callRoutes.GET("/recording/:order_id", app.handler.GetOrderCallRecordings)
		callRoutes.POST("/status", app.handler.GetOrderCallStatus)
		callRoutes.POST("/history", app.handler.GetOrderCallHistory) // Changed to POST
	}

}
