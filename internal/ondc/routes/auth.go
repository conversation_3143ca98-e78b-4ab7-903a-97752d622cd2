package routes

import (
	"kc/internal/ondc/middleware"
	"time"
)

func (app *App) setupAuthRoutes() {
	// Auth routes with rate limiting
	authRoutes := app.router.Group("/auth")
	authRoutes.Use(middleware.RateLimitMiddleware(100, time.Minute))

	authRoutes.POST("/login", app.handler.Login)
	authRoutes.POST("/refresh", app.handler.RefreshToken)
	authRoutes.POST("/logout", app.handler.Logout)

	// Protected profile route
	authRoutes.Use(middleware.AuthMiddleware())
	authRoutes.GET("/profile", app.handler.GetProfile)

	trueCallerRoutes := app.router.Group("/truecaller")
	trueCallerRoutes.POST("/webhook", app.handler.TrueCallerWebhook)
	trueCallerRoutes.POST("/resolve_webhook", app.handler.ResolveWebhook)
}
