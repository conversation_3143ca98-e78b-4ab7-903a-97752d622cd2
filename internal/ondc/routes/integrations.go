package routes

func (app *App) setupIntegrationRoutes() {
	// Zoho Routes
	zohoRoutes := app.router.Group("/zoho")
	zohoRoutes.POST("/webhook", app.handler.ZohoWebhook)
	zohoRoutes.GET("/webhook", app.handler.ZohoWebhook)

	// Exotel Routes
	exotelRoutes := app.router.Group("/exotel")
	exotelRoutes.GET("/webhook", app.handler.ExotelWebhook)
	exotelRoutes.GET("/webhook/:seller", app.handler.ExotelWebhook)
	exotelRoutes.GET("/reactivation/webhook", app.handler.ExotelReactivationWebhook)
	exotelRoutes.POST("/ivr", app.handler.CreateIVRCall)
	exotelRoutes.POST("/ivr/callback", app.handler.ExotelIVRStatusCallback)
	exotelRoutes.POST("/ivr/webhook/:type", app.handler.ExotelIVRWebhookHandler)
	exotelRoutes.GET("/ivr/webhook/:type", app.handler.ExotelIVRWebhookHandler)
	exotelRoutes.GET("/ivr/phone_masking/:type", app.handler.ExotelPhoneMaskingWebhook)
}
