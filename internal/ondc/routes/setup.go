package routes

import (
	"kc/internal/ondc/api"
	"kc/internal/ondc/infrastructure/logging"
	"kc/internal/ondc/middleware"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

type App struct {
	router       *gin.Engine
	handler      *api.Handler
	promRegistry *prometheus.Registry
}

var logger = logging.GetLogrusLogger("http_error_logger")

func cORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// Define your allowed web origins (browsers that send Origin header)
		allowedWebOrigins := []string{
			"https://b2b-dev.retailpulse.ai",  // Your B2B frontend
			"https://b2b.retailpulse.ai",      // Production B2B
			"https://admin.retailpulse.ai",    // Admin panel
			"https://webapps.retailpulse.ai/", // Web apps
			"http://localhost:3000",           // Development
			"http://localhost:3001",           // Development
			"http://localhost:8080",           // Development
			"https://shop.kirana.club/",       // Web apps
			"https://kcbazar.retailpulse.ai",  // Kirana Bazar
		}

		// Strategy: Handle different client types
		if origin != "" {
			// Browser request with Origin header - check against allowed list
			isAllowed := false
			for _, allowedOrigin := range allowedWebOrigins {
				if origin == allowedOrigin {
					isAllowed = true
					break
				}
			}

			if isAllowed {
				// ✅ Known web origin - set specific origin (required for credentials)
				c.Writer.Header().Set("Access-Control-Allow-Origin", origin)
			} else {
				// ❌ Unknown web origin - reject
				c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
			}
		} else {

			c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		}

		// Set common CORS headers for all requests
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With, X-API-Key")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")
		c.Writer.Header().Set("Access-Control-Max-Age", "86400") // Cache preflight for 24 hours

		// Handle preflight requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

func SetupRoutes(router *gin.Engine, handler *api.Handler, promRegistry *prometheus.Registry) {
	router.Use(cORSMiddleware())
	app := App{
		router:       router,
		handler:      handler,
		promRegistry: promRegistry,
	}

	// Add middleware
	app.router.Use(middleware.LoggerContextMiddleware())
	app.router.Use(middleware.TrackMetrics())
	app.router.Use(middleware.ErrorLoggerMiddleware(logger))

	// Prometheus metrics
	app.router.GET("/metrics", gin.WrapH(promhttp.HandlerFor(app.promRegistry, promhttp.HandlerOpts{})))

	app.router.POST("/test", app.handler.Test)

	// Setup all route groups
	app.setupQueueMonitorRoutes()
	app.setupHealthCheckRoutes()
	app.setupONDCRoutes()
	app.setupAddressRoutes()
	app.setupKiranaBazarRoutes()
	app.setupB2BRoutes()
	app.setupReconciliationRoutes()
	app.setupIGMRoutes()
	app.setupLogisticsRoutes()
	app.setupReviewRoutes()
	app.setupIntegrationRoutes()
	app.setupPaymentsRoutes()
	app.setupRefundsRoutes()
	app.setupOMSRoutes()
	app.setupCustomerSupportRoutes()
	app.setupExotelCallsRoutes()
	app.setupPostDeliveryRoutes()
	app.setupAuthRoutes()
	app.setupAppOpenMetaRoutes()
	app.setupCouponRoutes()
	app.setupPersonalizationRoutes()
	app.setupRewardRoutes()
	app.setupFinancialsRoutes()
}
