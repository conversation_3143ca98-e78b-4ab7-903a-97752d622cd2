package routes

func (app *App) setupCustomerSupportRoutes() {
	flowCsRoutes := app.router.Group("/cs/flow")
	flowCsRoutes.POST("/get_ticket_creation_flow", app.handler.GetCsTicketFlow)
	flowCsRoutes.POST("/submit", app.handler.AppSubmitFlowResponse)

	appCsRoutes := app.router.Group("/cs/app")
	appCsRoutes.POST("/get_support_tickets", app.handler.AppGetSupportTickets)
	appCsRoutes.POST("/get_support_orders", app.handler.AppGetSupportOrders)
	appCsRoutes.POST("/submit_ticket", app.handler.AppSubmitTicket)
	appCsRoutes.POST("/submit_rating", app.handler.SubmitRating)

	appB2bRoutes := app.router.Group("/cs/b2b")
	appB2bRoutes.POST("/get_support_tickets", app.handler.GetSupportTickets)
	appB2bRoutes.POST("/get_messages", app.handler.GetMessages)
	appB2bRoutes.POST("/send_message", app.handler.SendMessage)
	appB2bRoutes.POST("/get_meta", app.handler.GetCsMeta)
	appB2bRoutes.POST("/get_ticket_details", app.handler.GetCSTicketDetails)
	appB2bRoutes.POST("/ticket_action", app.handler.PerformTicketAction)
	appB2bRoutes.POST("/get_support_insights", app.handler.GetSupportInsights)

	appCsWebhookRoutes := app.router.Group("/cs/webhook")
	appCsWebhookRoutes.POST("/update_media", app.handler.UpdateMedia)
}
