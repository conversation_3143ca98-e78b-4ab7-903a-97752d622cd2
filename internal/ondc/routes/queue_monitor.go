package routes

func (app *App) setupQueueMonitorRoutes() {
	queueMonitorRoutes := app.router.Group("/queue")
	queueMonitorRoutes.GET("/monitor/ivr_queue", app.handler.ServeQueueMonitor)
	queueMonitorRoutes.GET("/metrics/ivr_queue", app.handler.ServeQueueMetrics)
	queueMonitorRoutes.GET("/metrics/order_cancel_queue", app.handler.ServeOrderCancelQueueMetrics)
	queueMonitorRoutes.GET("/monitor/order_cancel_queue", app.handler.ServeOrderCancellationMonitor)

	queueMonitorRoutes.GET("/metrics/timed_functions_queue", app.handler.TimedFunctionsQueueMetrics)
	queueMonitorRoutes.GET("/monitor/timed_functions_queue", app.handler.TimedFunctionsQueueMonitor)
}
