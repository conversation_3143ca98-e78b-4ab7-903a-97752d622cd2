package bifrostRepo

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"os"
	"sync"
	"time"

	"github.com/google/uuid"
	amqp "github.com/rabbitmq/amqp091-go"
)

type RabbitMQProducer struct {
	connection    *amqp.Connection
	channel       *amqp.Channel
	mutex         sync.Mutex
	messageQueue  []Message
	isRetrying    bool
	isInitialized bool
}

type Message struct {
	Queue      string
	Data       interface{}
	Durable    bool
	Properties amqp.Publishing
}

// singleton instance
var (
	instance *RabbitMQProducer
	once     sync.Once
)

func SetEnvForRabbitMQ(hostname, port, username, password string) {
	os.Setenv("RABBITMQ_HOSTNAME", "************")
	os.Setenv("RABBITMQ_PORT", "5672")
	os.Setenv("RABBITMQ_USERNAME", "kc")
	os.Setenv("RABBITMQ_PASSWORD", "rabbit@1qp")
}

func GetInstance() *RabbitMQProducer {
	once.Do(func() {
		instance = &RabbitMQProducer{
			messageQueue: make([]Message, 0),
		}
	})
	return instance
}

func (r *RabbitMQProducer) Initialize() error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if r.isInitialized {
		return nil
	}

	config := struct {
		hostname string
		port     string
		username string
		password string
	}{
		hostname: os.Getenv("RABBITMQ_HOSTNAME"),
		port:     os.Getenv("RABBITMQ_PORT"),
		username: os.Getenv("RABBITMQ_USERNAME"),
		password: os.Getenv("RABBITMQ_PASSWORD"),
	}

	if config.hostname == "" || config.username == "" || config.password == "" {
		return errors.New("missing required RabbitMQ environment variables")
	}

	if config.port == "" {
		config.port = "5672"
	}

	maxRetries := 5
	var err error

	for attempt := 0; attempt < maxRetries; attempt++ {
		log.Printf("[*] Attempting to initialize RabbitMQ connection (Attempt %d/%d)...", attempt+1, maxRetries)

		url := fmt.Sprintf("amqp://%s:%s@%s:%s/", config.username, config.password, config.hostname, config.port)
		r.connection, err = amqp.Dial(url)
		if err != nil {
			backoffTime := time.Duration(math.Min(float64(1000*math.Pow(2, float64(attempt))), 30000)) * time.Millisecond
			log.Printf("[*] Connection failed, retrying in %.2f seconds...", backoffTime.Seconds())
			time.Sleep(backoffTime)
			continue
		}

		r.channel, err = r.connection.Channel()
		if err != nil {
			r.connection.Close()
			continue
		}

		// Set up connection closure monitoring
		go func() {
			<-r.connection.NotifyClose(make(chan *amqp.Error))
			log.Println("RabbitMQ connection closed. Resetting state...")
			r.mutex.Lock()
			r.connection = nil
			r.channel = nil
			r.isInitialized = false
			r.mutex.Unlock()
		}()

		r.isInitialized = true
		log.Println("[*] RabbitMQ Producer initialized")

		// Process any messages in the fallback queue
		go r.processFallbackQueue()
		return nil
	}

	return fmt.Errorf("failed to initialize RabbitMQ after %d attempts", maxRetries)
}

func (r *RabbitMQProducer) SendToQueue(queue string, message interface{}, durable bool, properties amqp.Publishing) error {
	r.mutex.Lock()

	if !r.isInitialized || r.channel == nil {
		r.mutex.Unlock()
		log.Println("[!] Channel not available. Reinitializing...")
		err := r.Initialize()
		if err != nil {
			// Add to fallback queue if initialization fails
			r.mutex.Lock()
			r.messageQueue = append(r.messageQueue, Message{
				Queue:      queue,
				Data:       message,
				Durable:    durable,
				Properties: properties,
			})
			r.mutex.Unlock()
			return err
		}
	} else {
		r.mutex.Unlock()
	}
	r.mutex.Lock()
	defer r.mutex.Unlock()

	// Declare queue
	_, err := r.channel.QueueDeclare(
		queue,   // name
		durable, // durable
		false,   // delete when unused
		false,   // exclusive
		false,   // no-wait
		nil,     // arguments
	)
	if err != nil {
		return err
	}

	// Publish message
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err = r.channel.PublishWithContext(ctx,
		"",    // exchange
		queue, // routing key
		false, // mandatory
		false, // immediate
		properties,
	)

	if err != nil {
		// Add to fallback queue if publish fails
		r.messageQueue = append(r.messageQueue, Message{
			Queue:      queue,
			Data:       message,
			Durable:    durable,
			Properties: properties,
		})
		return err
	}

	return nil
}

func (r *RabbitMQProducer) processFallbackQueue() {
	r.mutex.Lock()
	if r.isRetrying || !r.isInitialized {
		r.mutex.Unlock()
		return
	}
	r.isRetrying = true
	r.mutex.Unlock()

	defer func() {
		r.mutex.Lock()
		r.isRetrying = false
		r.mutex.Unlock()
	}()

	log.Println("[*] Processing fallback queue...")

	for len(r.messageQueue) > 0 {
		r.mutex.Lock()

		if len(r.messageQueue) == 0 {
			r.mutex.Unlock()
			break
		}

		msg := r.messageQueue[0]
		r.messageQueue = r.messageQueue[1:]

		r.mutex.Unlock()

		err := r.SendToQueue(msg.Queue, msg.Data, msg.Durable, msg.Properties)
		if err != nil {
			log.Printf("[!] Failed to process fallback message: %v", err)

			r.mutex.Lock()
			r.messageQueue = append(r.messageQueue, msg)
			r.mutex.Unlock()

			return
		}
	}
}

func (r *RabbitMQProducer) Close() error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if r.channel != nil {
		if err := r.channel.Close(); err != nil {
			return err
		}
	}

	if r.connection != nil {
		if err := r.connection.Close(); err != nil {
			return err
		}
	}

	r.isInitialized = false
	log.Println("[*] RabbitMQ Producer connection closed")
	return nil
}

// Helper function for sending messages
func SendMessage(queue string, message interface{}, durable bool, properties amqp.Publishing, logs bool) error {
	producer := GetInstance()
	err := producer.SendToQueue(queue, message, durable, properties)
	if err != nil {
		log.Printf("Error in producer: %v", err)
		return err
	}
	if logs {
		log.Printf("Message sent to queue %s: %v", queue, message)
	}
	return nil
}

// AddMixpanelEventMessage sends a Mixpanel event message
func AddMixpanelEventMessage(userId string, messageData map[string]any) {
	queue := "MixpanelEventQueue"
	messageType := "AddMixpanelEvent"
	messageId := uuid.New().String()

	message := map[string]interface{}{
		"type":      messageType,
		"userId":    userId,
		"messageId": messageId,
		"data":      messageData,
	}

	jsonMessage, err := json.Marshal(message)
	if err != nil {
		fmt.Println("Error in marshalling message data: ", err)
	}

	properties := amqp.Publishing{
		ContentType:     "application/json",
		ContentEncoding: "utf-8",
		DeliveryMode:    2,
		Priority:        5,
		MessageId:       messageId,
		Timestamp:       time.Now(),
		Body:            jsonMessage,
	}

	err = SendMessage(queue, message, true, properties, false)
	if err != nil {
		fmt.Println("Error in sending message: ", err)
	}
}
