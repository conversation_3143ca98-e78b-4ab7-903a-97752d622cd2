package sqlRepo

import (
	"context"
	"errors"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/infrastructure/logging"
	"net/http"

	"github.com/go-sql-driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var logger = logging.GetLogrusLogger("db")

type Repository struct {
	Db *gorm.DB
}

func NewRepository(db *gorm.DB) *Repository {
	return &Repository{
		Db: db,
	}
}

func handleDbErr(err error) error {
	if err != nil {
		var mysqlErr *mysql.MySQLError
		logger.Error(context.Background(), "db call failed. error is %v", err.Error())
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return exceptions.GenerateNewServerError(exceptions.DBRecordNotFoundError, err, "not able to find results", http.StatusOK)
		} else if errors.As(err, &mysqlErr) && mysqlErr.Number == 1062 {
			return exceptions.GenerateNewServerError(exceptions.DBDuplicateKeyError, err, "key already exists", http.StatusOK)
		}

		return exceptions.GenerateNewServerError(exceptions.DBReadError, err, "not able to read level data", http.StatusInternalServerError)
	}
	return nil
}

type DbCalls interface {
	Find(condition map[string]interface{}, data interface{}) (interface{}, error)
	Create(data interface{}) (interface{}, error)
	Upsert(data interface{}) (interface{}, error)
	Update(oldDao interface{}, newDao interface{}) (interface{}, int64, error)
	CustomQuery(data interface{}, query string) (interface{}, error)
	Save(data interface{}) (interface{}, error)
	BeginTx(ctx context.Context) (*gorm.DB, error)
	CreateTx(tx *gorm.DB, data interface{}) (interface{}, error)
	CustomQueryTx(tx *gorm.DB, data interface{}, query string) (interface{}, error)
}

func (repo *Repository) GetDummyData() string {
	// Perform database query using GORM or any other ORM of your choice
	// Here's a dummy example that returns a fixed string
	return "Dummy data from database"
}

func (repo *Repository) Find(condition map[string]interface{}, data interface{}) (interface{}, error) {
	tx := repo.Db.Find(data, condition)
	err := tx.Error
	numOfRows := tx.RowsAffected

	if numOfRows == 0 {
		//logger.Error(context.Background(), "no rows found for the given condition", condition, data)
		err = gorm.ErrRecordNotFound
	}
	return data, handleDbErr(err)
}

func (repo *Repository) Create(data interface{}) (interface{}, error) {
	err := repo.Db.Create(data).Error
	return data, handleDbErr(err)
}

func (repo *Repository) Upsert(data interface{}) (interface{}, error) {
	err := repo.Db.Debug().Clauses(clause.OnConflict{UpdateAll: true}).Save(data).Error
	return data, handleDbErr(err)
}

func (repo *Repository) Save(data interface{}) (interface{}, error) {
	err := repo.Db.Debug().Clauses(clause.OnConflict{DoNothing: true}).Save(data).Error
	return data, handleDbErr(err)
}

func (repo *Repository) Update(oldDao interface{}, newDao interface{}) (interface{}, int64, error) {
	queryResult := repo.Db.Debug().Clauses(clause.Returning{}).Model(oldDao).Where(oldDao).Updates(newDao)
	rowsAffected := queryResult.RowsAffected
	err := queryResult.Error
	return oldDao, rowsAffected, handleDbErr(err)
}

func (repo *Repository) CustomQuery(data interface{}, query string) (interface{}, error) {
	err := repo.Db.Raw(query).Scan(data).Error
	return data, handleDbErr(err)
}

func (repo *Repository) BeginTx(ctx context.Context) (*gorm.DB, error) {
	tx := repo.Db.Begin()
	if tx.Error != nil {
		return nil, handleDbErr(tx.Error)
	}
	return tx, nil
}

func (repo *Repository) CreateTx(tx *gorm.DB, data interface{}) (interface{}, error) {
	err := tx.Create(data).Error
	return data, handleDbErr(err)
}

func (repo *Repository) CustomQueryTx(tx *gorm.DB, data interface{}, query string) (interface{}, error) {
	err := tx.Raw(query).Scan(data).Error
	return data, handleDbErr(err)
}

func (repo *Repository) FindTx(tx *gorm.DB, condition map[string]interface{}, data interface{}) (interface{}, error) {
	queryResult := tx.Find(data, condition)
	err := queryResult.Error
	numOfRows := queryResult.RowsAffected

	if numOfRows == 0 {
		err = gorm.ErrRecordNotFound
	}
	return data, handleDbErr(err)
}

func (repo *Repository) UpdateTx(tx *gorm.DB, oldDao interface{}, newDao interface{}) (interface{}, int64, error) {
	queryResult := tx.Debug().Clauses(clause.Returning{}).Model(oldDao).Where(oldDao).Updates(newDao)
	rowsAffected := queryResult.RowsAffected
	err := queryResult.Error
	return oldDao, rowsAffected, handleDbErr(err)
}
