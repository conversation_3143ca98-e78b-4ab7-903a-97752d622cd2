package sqlRepo

import (
    "fmt"
    "strings"
)

// QueryBuilder provides a fluent interface for building SQL SELECT queries.
// It supports SELECT, FROM, JOIN, WHERE, ORDER BY, and LIMIT clauses.
type QueryBuilder struct {
    selectColumns  []string
    defaultTable   string
    defaultLimit   *int
    defaultOffset  *int
    joins          []string
    leftJoins      []string
    whereAndClause []string
    orderBy        []string
    limitClause    string
    args           []interface{}
}

// QueryBuilderOptions contains configuration options for initializing a QueryBuilder.
type QueryBuilderOptions struct {
    TableName      string
    DefaultColumns []string
    DefaultLimit   *int
    DefaultOffset  *int
}

// NewQueryBuilder creates a new QueryBuilder instance with the provided options.
// Sets up default table name, columns, limit, and offset if specified.
func NewQueryBuilder(qbo *QueryBuilderOptions) *QueryBuilder {
    return &QueryBuilder{
        defaultTable:  qbo.TableName,
        selectColumns: qbo.DefaultColumns,
        defaultLimit:  qbo.DefaultLimit,
        defaultOffset: qbo.DefaultOffset,
    }
}

// WhereAnd adds WHERE conditions that will be joined with AND operators.
// Keys should contain column conditions (e.g., "user_id = ?") and values contain the corresponding parameters.
// Returns the QueryBuilder instance for method chaining.
func (qb *QueryBuilder) WhereAnd(keys []string, values []interface{}) *QueryBuilder {
    if len(keys) == 0 || len(values) == 0 {
        return qb
    }
    qb.whereAndClause = append(qb.whereAndClause, keys...)
    qb.args = append(qb.args, values...)
    return qb
}

// WithColumns adds additional columns to the SELECT clause.
// Appends to existing columns rather than replacing them.
// Returns the QueryBuilder instance for method chaining.
func (qb *QueryBuilder) WithColumns(columns []string) *QueryBuilder {
    if len(columns) == 0 {
        return qb
    }
    qb.selectColumns = append(qb.selectColumns, columns...)
    return qb
}

// WithPagination sets LIMIT and OFFSET clauses for the query.
// Uses provided values or falls back to default values set during initialization.
// Returns the QueryBuilder instance for method chaining.
func (qb *QueryBuilder) WithPagination(limit, offset *int) *QueryBuilder {
    var l, o *int
    if qb.defaultLimit != nil {
        l = qb.defaultLimit
    }
    if qb.defaultOffset != nil {
        o = qb.defaultOffset
    }
    if limit != nil {
        l = limit
    }
    if offset != nil {
        o = offset
    }

    if l != nil {
        qb.limitClause = fmt.Sprintf("LIMIT %d", *l)
    }
    if o != nil {
        qb.limitClause += fmt.Sprintf(" OFFSET %d", *o)
    }

    return qb
}

// WithJoins adds INNER JOIN clauses to the query.
// Each string in the slice should be a complete JOIN statement.
// Returns the QueryBuilder instance for method chaining.
func (qb *QueryBuilder) WithJoins(joins []string) *QueryBuilder {
    qb.joins = append(qb.joins, joins...)
    return qb
}

// WithLeftJoins adds LEFT JOIN clauses to the query.
// Each string in the slice should be a complete LEFT JOIN statement.
// Returns the QueryBuilder instance for method chaining.
func (qb *QueryBuilder) WithLeftJoins(leftJoins []string) *QueryBuilder {
    qb.leftJoins = append(qb.leftJoins, leftJoins...)
    return qb
}

// WithOrderBy adds ORDER BY clauses to the query.
// Each string should specify a column and optional direction (e.g., "name ASC", "created_at DESC").
// Returns the QueryBuilder instance for method chaining.
func (qb *QueryBuilder) WithOrderBy(orderBy []string) *QueryBuilder {
    if len(orderBy) == 0 {
        return qb
    }
    qb.orderBy = append(qb.orderBy, orderBy...)
    return qb
}

// ToSQL builds and returns the complete SQL query string.
// Combines all added clauses (SELECT, FROM, JOIN, WHERE, ORDER BY, LIMIT) into a single query.
// Automatically substitutes placeholder values with actual arguments.
func (qb *QueryBuilder) ToSQL() string {
    var queryParts []string

    // SELECT clause
    queryParts = append(queryParts, fmt.Sprintf("SELECT %s", strings.Join(qb.selectColumns, ", ")))

    // FROM clause
    queryParts = append(queryParts, fmt.Sprintf("FROM %s", qb.defaultTable))

    // LEFT JOIN clauses
    if len(qb.leftJoins) > 0 {
        queryParts = append(queryParts, strings.Join(qb.leftJoins, " LEFT JOIN "))
    }

    // JOIN clauses
    if len(qb.joins) > 0 {
        queryParts = append(queryParts, strings.Join(qb.joins, " "))
    }

    // WHERE AND clause
    if len(qb.whereAndClause) > 0 {
        queryParts = append(queryParts, fmt.Sprintf("WHERE %s", strings.Join(qb.whereAndClause, " AND ")))
    }

    // ORDER BY clause
    if len(qb.orderBy) > 0 {
        queryParts = append(queryParts, fmt.Sprintf("ORDER BY %s", strings.Join(qb.orderBy, ", ")))
    }

    // LIMIT clause
    if qb.limitClause != "" {
        queryParts = append(queryParts, qb.limitClause)
    }

    query := strings.Join(queryParts, "\n")
    if len(qb.args) > 0 {
        query = buildQueryWithArgs(query, qb.args)
    }
    return query
}

// buildQueryWithArgs replaces SQL placeholders (?) with actual argument values.
// Used internally by ToSQL() to substitute parameters into the query string.
func buildQueryWithArgs(query string, args []interface{}) string {
    result := query
    for _, arg := range args {
        result = strings.Replace(result, "?", fmt.Sprintf("%v", arg), 1)
    }
    return result
}