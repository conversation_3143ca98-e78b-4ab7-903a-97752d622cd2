package firebaseRepo

import (
	"context"
	firebase2 "kc/internal/ondc/infrastructure/firebase"

	"cloud.google.com/go/firestore"
	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/auth"
	"firebase.google.com/go/v4/db"
)

type FirebaseRepo struct {
	RtDb      *db.Client
	MetaDb    *db.Client
	Firestore *firestore.Client
	Auth      *auth.Client
}

func NewFirebaseRepo(fb *firebase.App, metaDbUrl string) *FirebaseRepo {
	return &FirebaseRepo{
		RtDb:      firebase2.GetRealtimeDb(context.Background(), fb),
		MetaDb:    firebase2.GetMetaDb(context.Background(), fb, metaDbUrl),
		Firestore: firebase2.GetFirestore(context.Background(), fb),
		Auth:      firebase2.GetAuth(context.Background(), fb),
	}
}
