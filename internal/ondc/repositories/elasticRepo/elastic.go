package elasticRepo

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	"github.com/elastic/go-elasticsearch/v8"
	"github.com/elastic/go-elasticsearch/v8/esapi"
)

// Generic document structure with common fields
// This can be embedded in specific entity types
type BaseDocument struct {
	ID string `json:"id,omitempty"`
}

// Query represents a search term for an entity
type Query struct {
	Value string `json:"value"`
}

// SearchHit represents a single hit in the search results
type SearchHit struct {
	Index  string          `json:"_index"`
	ID     string          `json:"_id"`
	Score  float64         `json:"_score"`
	Source json.RawMessage `json:"_source"`
}

// SearchResult represents the response structure from Elasticsearch
type SearchResult struct {
	Took     int  `json:"took"`
	TimedOut bool `json:"timed_out"`
	Hits     struct {
		Total struct {
			Value    int    `json:"value"`
			Relation string `json:"relation"`
		} `json:"total"`
		MaxScore float64     `json:"max_score"`
		Hits     []SearchHit `json:"hits"`
	} `json:"hits"`
}

// SearchOptions contains parameters for search customization
type SearchOptions struct {
	From           int
	Size           int
	FilterField    string
	FilterValue    []string
	ExcludeField   string
	ExcludeValue   []string
	PreferredField string
	PreferredValue string
	BoostValue     float64
	SearchFields   []string
	// SellerWeights maps seller names to their boost weights
	SellerWeights map[string]float64
	// DiversifySellers enables the seller diversity feature
	DiversifySellers bool
	// InnerHitsSize controls how many top products per seller to fetch
	InnerHitsSize int
	// Sort options for the query
	Sort []map[string]string
	// EnablePhonetic enables phonetic matching for search terms
	EnablePhonetic bool
	// PhoneticFields specifies which fields should use phonetic matching
	PhoneticFields []string
	// Fuzziness level for fuzzy matching (AUTO, 0, 1, 2)
	Fuzziness string
}

// DefaultSearchOptions provides sensible defaults
func DefaultSearchOptions() SearchOptions {
	return SearchOptions{
		From:             0,
		Size:             10,
		BoostValue:       2.0,
		SearchFields:     []string{"queries.value"},
		DiversifySellers: false,
		InnerHitsSize:    5,
		Sort:             []map[string]string{{"_score": "desc"}},
		EnablePhonetic:   false,
		PhoneticFields:   []string{"queries.value"},
		Fuzziness:        "AUTO",
	}
}

// ElasticSearchClient handles the connection and queries to Elasticsearch
type ElasticSearchClient struct {
	client *elasticsearch.Client
}

// NewElasticSearchClient creates a new client for Elasticsearch
func NewElasticSearchClient(addresses []string, username, password string) (*ElasticSearchClient, error) {
	cfg := elasticsearch.Config{
		Addresses: addresses,
	}

	// Add authentication if credentials are provided
	if username != "" && password != "" {
		cfg.Username = username
		cfg.Password = password
	}

	client, err := elasticsearch.NewClient(cfg)
	if err != nil {
		return nil, fmt.Errorf("error creating the client: %s", err)
	}

	return &ElasticSearchClient{
		client: client,
	}, nil
}

// Search performs a generic search against the specified index
func (es *ElasticSearchClient) Search(ctx context.Context, index []string, searchTerm string, options SearchOptions) (*SearchResult, error) {
	// Create the request body
	requestBody := map[string]interface{}{}

	// Add the query
	requestBody["query"] = buildQuery(searchTerm, options)["query"]

	// Add sort if specified
	if len(options.Sort) > 0 {
		requestBody["sort"] = options.Sort
	}

	// For diversified results, we'll use a different approach since field collapsing
	// is not available for text fields. We'll handle diversity in post-processing.

	// Convert the request body to JSON
	body, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("error marshaling query: %s", err)
	}

	// If diversification is requested, we need to get more results
	// to ensure we have enough for post-processing diversity
	tempSize := options.Size
	if options.DiversifySellers {
		// Get more results to ensure seller diversity
		expandedSize := options.Size * 5
		if expandedSize > 1000 {
			expandedSize = 1000 // Elasticsearch max size limit
		}
		options.Size = expandedSize
	}

	// Perform the search request
	req := esapi.SearchRequest{
		Index:          index,
		Body:           bytes.NewReader(body),
		TrackTotalHits: true,
		From:           &options.From,
		Size:           &options.Size,
		Pretty:         true,
	}

	res, err := req.Do(ctx, es.client)
	if err != nil {
		return nil, fmt.Errorf("error executing search: %s", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		var e map[string]interface{}
		if err := json.NewDecoder(res.Body).Decode(&e); err != nil {
			return nil, fmt.Errorf("error parsing error response: %s", err)
		}
		return nil, fmt.Errorf("search error: %v", e)
	}

	// Parse the response
	var result SearchResult
	if err := json.NewDecoder(res.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("error parsing response: %s", err)
	}

	// Restore original size for post-processing
	options.Size = tempSize

	return &result, nil
}

// SearchEntities is a generic function to search for entities and unmarshal them to the specified type
func (es *ElasticSearchClient) SearchEntities(ctx context.Context, index []string, searchTerm string, options SearchOptions, entities interface{}) (int, error) {
	result, err := es.Search(ctx, index, searchTerm, options)
	if err != nil {
		return 0, err
	}

	// Different processing depending on whether diversify is enabled
	if options.DiversifySellers {
		return es.processDiversifiedResults(result, entities, options)
	} else {
		return es.processStandardResults(result, entities)
	}
}

// processStandardResults handles normal search results without diversification
func (es *ElasticSearchClient) processStandardResults(result *SearchResult, entities interface{}) (int, error) {
	// Extract all source documents and build a JSON array
	sources := make([]json.RawMessage, len(result.Hits.Hits))
	for i, hit := range result.Hits.Hits {
		sources[i] = hit.Source
	}

	// Marshal the array of sources
	sourcesJSON, err := json.Marshal(sources)
	if err != nil {
		return 0, fmt.Errorf("error marshaling sources: %s", err)
	}

	// Unmarshal into the provided entities slice
	if err := json.Unmarshal(sourcesJSON, entities); err != nil {
		return 0, fmt.Errorf("error unmarshaling entities: %s", err)
	}

	return result.Hits.Total.Value, nil
}

// processDiversifiedResults handles diversified results with client-side grouping
func (es *ElasticSearchClient) processDiversifiedResults(result *SearchResult, entities interface{}, options SearchOptions) (int, error) {
	// For diversified results, we need to post-process to ensure seller diversity
	// Group products by seller
	sellerProducts := make(map[string][]json.RawMessage)

	// Process all hits and group by seller
	for _, hit := range result.Hits.Hits {
		// Extract seller from source
		var productMap map[string]interface{}
		if err := json.Unmarshal(hit.Source, &productMap); err != nil {
			return 0, fmt.Errorf("error unmarshaling product: %s", err)
		}

		seller, ok := productMap["seller"].(string)
		if !ok {
			continue
		}

		productMap["score"] = hit.Score // Add score to the product map
		productSource, err := json.Marshal(productMap)
		if err != nil {
			return 0, fmt.Errorf("error marshaling product with score: %s", err)
		}
		// Add product to its seller group
		if _, exists := sellerProducts[seller]; !exists {
			sellerProducts[seller] = []json.RawMessage{productSource}
		} else {
			sellerProducts[seller] = append(sellerProducts[seller], productSource)
		}
	}

	// Now build a list with representation from all sellers in a round-robin fashion
	var diversifiedSources []json.RawMessage

	// First, get prioritized sellers
	type sellerWeight struct {
		seller string
		weight float64
	}

	weightedSellers := make([]sellerWeight, 0)

	// Add sellers with explicit weights first
	for seller, weight := range options.SellerWeights {
		if _, ok := sellerProducts[seller]; ok {
			weightedSellers = append(weightedSellers, sellerWeight{seller, weight})
		}
	}

	// Add any other sellers with default weight
	for seller := range sellerProducts {
		found := false
		for _, sw := range weightedSellers {
			if sw.seller == seller {
				found = true
				break
			}
		}
		if !found {
			weightedSellers = append(weightedSellers, sellerWeight{seller, 1.0})
		}
	}

	// Sort sellers by weight in descending order
	sort.Slice(weightedSellers, func(i, j int) bool {
		return weightedSellers[i].weight > weightedSellers[j].weight
	})

	// Calculate how many products per seller to include
	productsPerSeller := 1 // Default minimum value
	if len(sellerProducts) > 0 {
		productsPerSeller = options.Size / len(sellerProducts)
		if productsPerSeller < 1 {
			productsPerSeller = 1
		}
	}
	// Set a maximum per seller to avoid domination
	maxProductsPerSeller := options.InnerHitsSize
	if productsPerSeller > maxProductsPerSeller {
		productsPerSeller = maxProductsPerSeller
	}

	// First pass: add top N products from each seller
	for _, sw := range weightedSellers {
		products := sellerProducts[sw.seller]
		count := len(products)
		if count > productsPerSeller {
			count = productsPerSeller
		}

		for i := 0; i < count; i++ {
			diversifiedSources = append(diversifiedSources, products[i])
		}

		// Update the seller's product list for second pass
		if len(products) > productsPerSeller {
			sellerProducts[sw.seller] = products[productsPerSeller:]
		} else {
			sellerProducts[sw.seller] = nil
		}
	}

	// // Second pass: continue round-robin with any remaining slots and products
	// if len(diversifiedSources) < options.Size {
	// 	// Refill the weighted sellers list with only those that have remaining products
	// 	weightedSellers = weightedSellers[:0]
	// 	for seller, products := range sellerProducts {
	// 		if len(products) > 0 {
	// 			weight := 1.0
	// 			if w, ok := options.SellerWeights[seller]; ok {
	// 				weight = w
	// 			}
	// 			weightedSellers = append(weightedSellers, sellerWeight{seller, weight})
	// 		}
	// 	}

	// 	// Sort again by weight
	// 	sort.Slice(weightedSellers, func(i, j int) bool {
	// 		return weightedSellers[i].weight > weightedSellers[j].weight
	// 	})

	// 	// Add more products in round-robin fashion
	// 	for len(diversifiedSources) < options.Size && len(weightedSellers) > 0 {
	// 		// Start a new round
	// 		i := 0
	// 		for i < len(weightedSellers) && len(diversifiedSources) < options.Size {
	// 			seller := weightedSellers[i].seller
	// 			products := sellerProducts[seller]

	// 			if len(products) > 0 {
	// 				// Add one more product
	// 				diversifiedSources = append(diversifiedSources, products[0])

	// 				// Update remaining products
	// 				if len(products) > 1 {
	// 					sellerProducts[seller] = products[1:]
	// 				} else {
	// 					sellerProducts[seller] = nil
	// 					// Remove this seller from weightedSellers since it has no more products
	// 					weightedSellers = append(weightedSellers[:i], weightedSellers[i+1:]...)
	// 					continue // Skip incrementing i since we've removed the current element
	// 				}
	// 			}

	// 			i++
	// 		}

	// 		// If no more products from any seller, break
	// 		if i == 0 {
	// 			break
	// 		}
	// 	}
	// }

	// Limit to requested size
	if len(diversifiedSources) > options.Size {
		diversifiedSources = diversifiedSources[:options.Size]
	}

	// Marshal the array of sources
	sourcesJSON, err := json.Marshal(diversifiedSources)
	if err != nil {
		return 0, fmt.Errorf("error marshaling sources: %s", err)
	}

	// Unmarshal into the provided entities slice
	if err := json.Unmarshal(sourcesJSON, entities); err != nil {
		return 0, fmt.Errorf("error unmarshaling entities: %s", err)
	}

	return result.Hits.Total.Value, nil
}

func (es *ElasticSearchClient) ProcessStandardResults(result *SearchResult, entities interface{}) (int, error) {
	return es.processStandardResults(result, entities)
}

// ProcessDiversifiedResults handles diversified results with client-side grouping
// This exposes the existing function to be used by other packages
func (es *ElasticSearchClient) ProcessDiversifiedResults(result *SearchResult, entities interface{}, options SearchOptions) (int, error) {
	return es.processDiversifiedResults(result, entities, options)
}

// GetSellerScores extracts seller scores from search results
func (es *ElasticSearchClient) GetSellerScores(result *SearchResult) map[string]float64 {
	sellerScores := make(map[string]float64)

	for _, hit := range result.Hits.Hits {
		// Extract seller from source
		var productMap map[string]interface{}
		if err := json.Unmarshal(hit.Source, &productMap); err != nil {
			continue
		}

		seller, ok := productMap["seller"].(string)
		if !ok {
			continue
		}

		// Store highest score per seller
		if score, exists := sellerScores[seller]; !exists || hit.Score > score {
			sellerScores[seller] = hit.Score
		}
	}

	return sellerScores
}

// buildQuery creates the Elasticsearch query for searching
func buildQuery(searchTerm string, options SearchOptions) map[string]interface{} {
	var matchQuery map[string]interface{}

	// Build different query types depending on whether enhanced search is enabled
	if options.EnablePhonetic {
		// Create a multi-tier search approach with different matching techniques
		shouldClauses := []map[string]interface{}{}

		// 1. Exact match with highest boost (using nested query for nested fields)
		shouldClauses = append(shouldClauses, map[string]interface{}{
			"nested": map[string]interface{}{
				"path": "queries",
				"query": map[string]interface{}{
					"match": map[string]interface{}{
						"queries.value": map[string]interface{}{
							"query": searchTerm,
							"boost": 3.0, // Exact matches get highest boost
						},
					},
				},
				"score_mode": "max",
			},
		})

		// 2. Fuzzy match for typo tolerance
		shouldClauses = append(shouldClauses, map[string]interface{}{
			"nested": map[string]interface{}{
				"path": "queries",
				"query": map[string]interface{}{
					"match": map[string]interface{}{
						"queries.value": map[string]interface{}{
							"query":     searchTerm,
							"fuzziness": options.Fuzziness,
							"boost":     2.0, // Fuzzy matches get medium boost
						},
					},
				},
				"score_mode": "max",
			},
		})

		// 3. Ngram matching (properly handling nested fields)
		shouldClauses = append(shouldClauses, map[string]interface{}{
			"nested": map[string]interface{}{
				"path": "queries",
				"query": map[string]interface{}{
					"match": map[string]interface{}{
						"queries.value.ngram": map[string]interface{}{
							"query": searchTerm,
							"boost": 1.0,
						},
					},
				},
				"score_mode": "max",
			},
		})

		// 4. Edge ngram matching (properly handling nested fields)
		shouldClauses = append(shouldClauses, map[string]interface{}{
			"nested": map[string]interface{}{
				"path": "queries",
				"query": map[string]interface{}{
					"match": map[string]interface{}{
						"queries.value.edge_ngram": map[string]interface{}{
							"query": searchTerm,
							"boost": 1.5, // Edge ngrams slightly higher priority
						},
					},
				},
				"score_mode": "max",
			},
		})

		// Create the combined query
		matchQuery = map[string]interface{}{
			"bool": map[string]interface{}{
				"should":               shouldClauses,
				"minimum_should_match": 1,
			},
		}
	} else {
		// Standard query without enhanced matching
		matchQuery = map[string]interface{}{
			"multi_match": map[string]interface{}{
				"query":  searchTerm,
				"fields": options.SearchFields,
				"type":   "best_fields",
			},
		}
	}

	// Helper function to add exclusion to any query
	addExclusion := func(query map[string]interface{}) map[string]interface{} {
		if options.ExcludeField != "" && len(options.ExcludeValue) > 0 {
			// Get the existing bool query or create one
			boolQuery, exists := query["query"].(map[string]interface{})["bool"].(map[string]interface{})
			if !exists {
				// Wrap the existing query in a bool query
				originalQuery := query["query"]
				query["query"] = map[string]interface{}{
					"bool": map[string]interface{}{
						"must": originalQuery,
					},
				}
				boolQuery = query["query"].(map[string]interface{})["bool"].(map[string]interface{})
			}

			// Add must_not clause
			boolQuery["must_not"] = map[string]interface{}{
				"terms": map[string]interface{}{
					options.ExcludeField: options.ExcludeValue,
				},
			}
		}
		return query
	}

	// If a specific filter is provided, use a bool query with must + filter
	if options.FilterField != "" && len(options.FilterValue) > 0 {
		query := map[string]interface{}{
			"query": map[string]interface{}{
				"bool": map[string]interface{}{
					"must": matchQuery,
					"filter": map[string]interface{}{
						"terms": map[string]interface{}{
							options.FilterField: options.FilterValue,
						},
					},
				},
			},
		}
		return addExclusion(query)
	}

	// If no filter but seller weights are provided, use function_score to boost based on weights
	if len(options.SellerWeights) > 0 {
		// Create functions for each seller weight
		functions := make([]map[string]interface{}, 0, len(options.SellerWeights))
		for seller, weight := range options.SellerWeights {
			functions = append(functions, map[string]interface{}{
				"filter": map[string]interface{}{
					"term": map[string]interface{}{
						"seller": seller,
					},
				},
				"weight": weight,
			})
		}

		query := map[string]interface{}{
			"query": map[string]interface{}{
				"function_score": map[string]interface{}{
					"query":      matchQuery,
					"functions":  functions,
					"boost_mode": "multiply", // Multiply the query score with weight
					"score_mode": "sum",      // Sum all the function scores
				},
			},
		}
		return addExclusion(query)
	}

	// If no filter but a preferred value is specified, boost its results
	if options.PreferredField != "" && options.PreferredValue != "" {
		query := map[string]interface{}{
			"query": map[string]interface{}{
				"bool": map[string]interface{}{
					"must": matchQuery,
					"should": map[string]interface{}{
						"term": map[string]interface{}{
							options.PreferredField: map[string]interface{}{
								"value": options.PreferredValue,
								"boost": options.BoostValue,
							},
						},
					},
				},
			},
		}
		return addExclusion(query)
	}

	// Default query with just the search term
	query := map[string]interface{}{
		"query": matchQuery,
	}
	return addExclusion(query)
}

// GetProductScores extracts scores from search results
func GetProductScores(hits []SearchHit) map[string]float64 {
	scoreMap := make(map[string]float64)

	for _, hit := range hits {
		// Extract seller from source
		var productMap map[string]interface{}
		if err := json.Unmarshal(hit.Source, &productMap); err != nil {
			continue
		}

		seller, ok := productMap["seller"].(string)
		if !ok {
			continue
		}

		// Track highest score per seller
		if existingScore, exists := scoreMap[seller]; !exists || hit.Score > existingScore {
			scoreMap[seller] = hit.Score
		}
	}

	return scoreMap
}

// LogSearchResults logs search result details for debugging
func LogSearchResults(result *SearchResult) {
	if result == nil {
		fmt.Println("Search result is nil")
		return
	}

	fmt.Printf("Total hits: %d\n", result.Hits.Total.Value)
	fmt.Printf("Max score: %f\n", result.Hits.MaxScore)

	// Log first few hits
	maxToLog := 5
	if len(result.Hits.Hits) < maxToLog {
		maxToLog = len(result.Hits.Hits)
	}

	for i := 0; i < maxToLog; i++ {
		hit := result.Hits.Hits[i]
		fmt.Printf("Hit %d: Score=%f, ID=%s\n", i, hit.Score, hit.ID)

		// Extract seller
		var productMap map[string]interface{}
		if err := json.Unmarshal(hit.Source, &productMap); err != nil {
			continue
		}

		if seller, ok := productMap["seller"].(string); ok {
			fmt.Printf("  Seller: %s\n", seller)
		}
	}
}

// SearchWithCategoryBoost performs a search with category boosting
func (es *ElasticSearchClient) SearchWithCategoryBoost(
	ctx context.Context,
	productIndex string,
	categoryIndex string,
	searchTerm string,
	options SearchOptions,
	productWeight float64,
	categoryWeight float64,
) (*SearchResult, error) {
	// Create a copy of options to avoid modifying the original
	searchOptions := options

	// If diversification is requested, we need to get more results
	tempSize := searchOptions.Size
	if searchOptions.DiversifySellers {
		// Get more results to ensure seller diversity
		expandedSize := searchOptions.Size * 5
		if expandedSize > 1000 {
			expandedSize = 1000 // Elasticsearch max size limit
		}
		searchOptions.Size = expandedSize
	}

	// Create the request body
	requestBody := map[string]interface{}{}
	requestBody["query"] = buildQuery(searchTerm, searchOptions)["query"]

	// Add sort if specified
	if len(searchOptions.Sort) > 0 {
		requestBody["sort"] = searchOptions.Sort
	}

	// Convert the request body to JSON
	body, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("error marshaling query: %s", err)
	}
	hitSize := searchOptions.Size * 2
	// Perform search against BOTH indices in a single request
	req := esapi.SearchRequest{
		Index:          []string{productIndex, categoryIndex}, // Search both indices
		Body:           bytes.NewReader(body),
		TrackTotalHits: true,
		From:           &searchOptions.From,
		Size:           &hitSize, // Double size to ensure we get enough results from both indices
		Pretty:         true,
	}

	res, err := req.Do(ctx, es.client)
	if err != nil {
		return nil, fmt.Errorf("error executing search: %s", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		var e map[string]interface{}
		if err := json.NewDecoder(res.Body).Decode(&e); err != nil {
			return nil, fmt.Errorf("error parsing error response: %s", err)
		}
		return nil, fmt.Errorf("search error: %v", e)
	}

	// Parse the response
	var combinedResult SearchResult
	if err := json.NewDecoder(res.Body).Decode(&combinedResult); err != nil {
		return nil, fmt.Errorf("error parsing response: %s", err)
	}

	// Restore original size
	searchOptions.Size = tempSize

	// Maps to store product information and scores
	productScores := make(map[int]float64)       // product_id -> product search score
	categoryScores := make(map[int]float64)      // product_id -> category search score
	productInfo := make(map[int]json.RawMessage) // product_id -> product JSON

	// Process all hits to extract product and category scores
	for _, hit := range combinedResult.Hits.Hits {
		var item map[string]interface{}
		if err := json.Unmarshal(hit.Source, &item); err != nil {
			continue
		}

		productIDFloat, ok := item["product_id"].(float64)
		if !ok {
			continue
		}
		productID := int(productIDFloat)

		// Store the product info (from either index)
		productInfo[productID] = hit.Source

		if strings.Contains(hit.Index, categoryIndex) {
			// This is a category hit - update category score
			if score, exists := categoryScores[productID]; !exists || hit.Score > score {
				categoryScores[productID] = hit.Score
			}
		} else {
			// This is a product hit - update product score
			if score, exists := productScores[productID]; !exists || hit.Score > score {
				productScores[productID] = hit.Score
			}
		}
	}

	// Create a unified list of products with combined scores
	var unifiedHits []SearchHit

	// Process all unique products
	for productID, productJSON := range productInfo {
		// Get scores (default to 0.0 if not found)
		productScore := 0.0
		if score, exists := productScores[productID]; exists {
			productScore = score
		}

		categoryScore := 0.0
		if score, exists := categoryScores[productID]; exists {
			categoryScore = score
		}

		// Calculate combined score
		combinedScore := (productScore * productWeight) + (categoryScore * categoryWeight)

		// Create a hit with the updated score
		hit := SearchHit{
			ID:     fmt.Sprintf("%d", productID),
			Score:  combinedScore,
			Source: productJSON,
			Index:  productIndex, // Mark as product for consistency
		}

		unifiedHits = append(unifiedHits, hit)
	}

	// Sort hits by updated scores
	sortHitsByScore(unifiedHits)

	// Limit to the requested size
	if len(unifiedHits) > searchOptions.Size {
		unifiedHits = unifiedHits[:searchOptions.Size]
	}

	// Create result with the unified hits
	result := combinedResult
	result.Hits.Hits = unifiedHits

	return &result, nil
}

// sortHitsByScore sorts search hits by score in descending order
func sortHitsByScore(hits []SearchHit) {
	for i := 0; i < len(hits)-1; i++ {
		for j := i + 1; j < len(hits); j++ {
			if hits[j].Score > hits[i].Score {
				hits[i], hits[j] = hits[j], hits[i]
			}
		}
	}
}
