package cacheRepo

import (
	"context"
	"github.com/go-redis/redis/v8"
	"kc/internal/ondc/infrastructure/logging"
	"time"
)

var logger = logging.New("cache")

type Repository struct {
	RedisClient *redis.Client
}

func NewRepository(redisClient *redis.Client) *Repository {
	return &Repository{
		RedisClient: redisClient,
	}
}

type CacheOperations interface {
	Find(ctx context.Context, key string) (string, error)
	Create(ctx context.Context, key string, value interface{}) (interface{}, error)
	Push(ctx context.Context, key string, value ...interface{}) error
	Range(ctx context.Context, key string, value interface{}) ([]string, error)
}

func (rep *Repository) Find(ctx context.Context, key string) (string, error) {
	val, err := rep.RedisClient.Get(ctx, key).Result()
	if err != nil {
		return "", err
	}
	return val, nil
}

func (rep *Repository) Create(ctx context.Context, key string, value interface{}) (interface{}, error) {
	_, err := rep.RedisClient.Set(ctx, key, value, time.Minute*5).Result()
	if err != nil {
		logger.Error(ctx, "error in saving ro redis, err is %s", err.Error())
	}
	return nil, err
}

func (rep *Repository) Push(ctx context.Context, key string, value ...interface{}) error {
	err := rep.RedisClient.LPush(ctx, key, value...).Err()
	if err != nil {
		logger.Error(ctx, "not able to push to redis list, error is %s", err.Error())
		return err
	}
	return nil
}

func (rep *Repository) Range(ctx context.Context, key string, value interface{}) ([]string, error) {
	resp, err := rep.RedisClient.LRange(ctx, key, 0, -1).Result()
	if err != nil {
		logger.Error(ctx, "error in saving ro redis, err is %s", err.Error())
		return nil, err
	}
	return resp, nil

}
