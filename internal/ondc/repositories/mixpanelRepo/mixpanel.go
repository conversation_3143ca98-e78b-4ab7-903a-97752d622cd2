package mixpanelRepo

import (
	"context"
	"encoding/json"
	"kc/internal/ondc/infrastructure/logging"
	"kc/internal/ondc/repositories/bifrostRepo"
	"log"
	"maps"
	"os"
	"strconv"
	"time"

	"github.com/mixpanel/mixpanel-go"
)

var logger = logging.New("mixpanelRepo")

type Repository struct {
	mixpanelClient *mixpanel.ApiClient
}

func NewRepository(mixpanelClient *mixpanel.ApiClient) *Repository {
	return &Repository{
		mixpanelClient: mixpanelClient,
	}
}

func (rep *Repository) NewEvent(name string, distinctID string, properties map[string]any, insertId ...string) *mixpanel.Event {
	newEvent := rep.mixpanelClient.NewEvent(name, distinctID, properties)

	if len(insertId) > 0 {
		newEvent.AddInsertID(insertId[0])
	}

	return newEvent
}

func convertToEpochSecond(epoch any) int64 {
	switch v := epoch.(type) {
	case string:
		// Only try parsing the string as an epoch time
		if i, err := strconv.ParseInt(v, 10, 64); err == nil {
			// Check digit count to determine if it's seconds, milliseconds, or microseconds
			switch {
			case i < 10000000000:
				// Less than 10 billion - treat as seconds
				return i
			case i < 10000000000000:
				// Less than 10 trillion - treat as milliseconds
				return i / 1000
			default:
				// Greater - treat as microseconds
				return i / 1000000
			}
		}
		// Fallback to current time
		return time.Now().Unix()

	case int:
		return int64(v)

	case int64:
		return v

	case int32:
		return int64(v)

	case float64:
		return int64(v)

	case float32:
		return int64(v)

	case time.Time:
		return v.Unix()

	default:
		// Fallback to current time for unsupported types
		return time.Now().Unix()
	}
}

func (rep *Repository) Track(ctx context.Context, event []*mixpanel.Event) error {
	for _, e := range event {
		propertiesCopy := maps.Clone(e.Properties)

		distinctId, _ := propertiesCopy["distinct_id"].(string)
		eventName := e.Name
		delete(propertiesCopy, "distinct_id")
		delete(propertiesCopy, "token")
		delete(propertiesCopy, "mp_lib")
		delete(propertiesCopy, "$lib_version")
		if propertiesCopy["time"] == 0 || propertiesCopy["time"] == nil || propertiesCopy["time"] == "" {
			propertiesCopy["time"] = time.Now().Unix()
		}
		propertiesCopy["time"] = convertToEpochSecond(propertiesCopy["time"].(int64))

		message := map[string]any{
			"event":      eventName,
			"attributes": propertiesCopy,
		}

		// logMessageToFile(distinctId, message)
		go bifrostRepo.AddMixpanelEventMessage(distinctId, message)
	}
	return nil
}

func (rep *Repository) PeopleSet(ctx context.Context, event []*mixpanel.PeopleProperties) error {
	return rep.mixpanelClient.PeopleSet(ctx, event)

}

// logMessageToFile appends a new message to the JSON file while maintaining valid JSON format
func logMessageToFile(distinctId string, message map[string]any) {
	const logFilePath = "mixpanel_events_ekart.json"

	// Create a log entry with timestamp
	logEntry := map[string]any{
		"timestamp":  time.Now().Format(time.RFC3339),
		"distinctId": distinctId,
		"message":    message,
	}

	// First, check if the file exists and read its current content
	var events []map[string]any
	fileData, err := os.ReadFile(logFilePath)
	if err == nil && len(fileData) > 0 {
		// File exists, parse the existing JSON array
		err = json.Unmarshal(fileData, &events)
		if err != nil {
			log.Printf("Error parsing existing JSON file: %v", err)
			// Attempt to recover by starting with an empty array
			events = []map[string]any{}
		}
	} else {
		// File doesn't exist or is empty, start with an empty array
		events = []map[string]any{}
	}

	// Add the new event to the array
	events = append(events, logEntry)

	// Convert the updated array to JSON
	jsonData, err := json.MarshalIndent(events, "", "    ")
	if err != nil {
		log.Printf("Error marshaling events array: %v", err)
		return
	}

	// Write the entire updated array back to the file
	err = os.WriteFile(logFilePath, jsonData, 0644)
	if err != nil {
		log.Printf("Error writing to log file: %v", err)
	}
}
