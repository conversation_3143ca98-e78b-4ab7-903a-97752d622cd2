package crons

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/utils"
	"time"

	models "github.com/Kirana-Club/3pl-go/pkg/models"
)

type OrderDetail struct {
	OrderId int64  `json:"order_id"`
	Seller  string `json:"seller"`
}

func (c *Cron) GetAWBNumberFromDelhivery() {
	orders := []OrderDetail{}
	query := fmt.Sprintf(`
		select
			ko.id as order_id,
			ko.seller
		from
			kiranaclubdb.kiranabazar_orders ko
		left join kiranaclubdb.kiranabazar_order_status kos on
			ko.id = kos.id
		where
			ko.seller in ("hugs", "apsara_tea")
			and (kos.awb_number is null
				or kos.awb_number = "")
			and ko.display_status in ("IN_TRANSIT", "CONFIRMED", "SHIPMENT_CREATED", "OTHERS", "PLACED")`)

	_, err := c.repository.CustomQuery(&orders, query)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("error while creating order on shipway, ", err))
		return
	}

	for _, order := range orders {
		awbNumber, err := c.Service.GetAWBNumberFromOrderIDInDelhivery(order.Seller, utils.GetKCOrderIDFromIntOrderID(order.OrderId))
		if err != nil {
			fmt.Println("error while getting awb number from delhivery", err, order.OrderId)
			continue
		}
		if awbNumber == "" || len(awbNumber) < 10 {
			fmt.Println("awb number not found", order.OrderId)
			continue
		}
		fmt.Println("awb number", order.OrderId, awbNumber)

		upsertQuery := fmt.Sprintf(`INSERT INTO kiranabazar_order_status
								( id, status, created_at, updated_at, expected_delivery_date,
								 error, courier, awb_number) VALUES (%d, NULL, '%s','%s',
								  NULL, NULL, '%s', '%s') ON DUPLICATE KEY
								   UPDATE updated_at=VALUES(updated_at), courier=VALUES(courier), awb_number=VALUES(awb_number)`,
			order.OrderId, time.Now().Format("2006-01-02 15:04:05"),
			time.Now().Format("2006-01-02 15:04:05"), "Delhivery", awbNumber)

		_, err = c.repository.CustomQuery(nil, upsertQuery)
		if err != nil {
			fmt.Println("error while updating awb number in kiranaclubdb.kiranabazar_order_status", err, order.OrderId)
		}
		time.Sleep(1 * time.Second)
	}
	slack.SendSlackMessage("fetched awb numbers from delhivery")

}

// syncDelhiveryPincodes is a cron function to fetch the data from delhivery api and save it to redis
func (c *Cron) syncDelhiveryPincodes() {
	ctx := context.Background()
	session, err := c.Service.TPLService.CreateSession(ctx, "delhivery", utils.ZOFF_FOODS)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("failed to create delhivery session, err = %s", err))
		return
	}

	bulkResponse, err := session.CheckServiceability(ctx, &models.PincodeServiceabilityRequest{
		Pincodes: []string{},
	})
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("failed to call delhivery api, err = %s", err))
		return
	}

	serviceAbilityResponse, err := json.Marshal(bulkResponse)
	if err != nil {
		slack.SendSlackMessage("not able to marshal the bulk response from delhivery api")
	}
	compressedBytes, err := utils.CompressJSON(serviceAbilityResponse)
	if err != nil {
		slack.SendSlackMessage("not able to compress the json")
		return
	}

	// Use time.Duration for Redis expiry
	expiry := 2 * 24 * time.Hour // 2 days in proper time.Duration
	err = c.Service.AzureRedis.RedisClient.Set(ctx, utils.DELHIVERY_DELIVERY_CODES, compressedBytes, expiry).Err()
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("not able to set value to redis, err = %s", err))
		return
	}

	rawData := make(map[string]utils.ServiceableJsonPincode)
	for _, dcs := range bulkResponse.DeliveryCodes {
		rawData[fmt.Sprintf("%d", dcs.PostalCode.Pin)] = utils.ServiceableJsonPincode{
			Pincode:   dcs.PostalCode.Pin,
			StateCode: dcs.PostalCode.StateCode,
			District:  dcs.PostalCode.District,
			Prepaid:   dcs.PostalCode.PrePaid,
			REPL:      dcs.PostalCode.Repl,
			COD:       dcs.PostalCode.COD,
			CASH:      dcs.PostalCode.Cash,
		}
	}

	utils.DELHIVERY_SERVICEABLE_PINCODES_MAP = rawData
}
