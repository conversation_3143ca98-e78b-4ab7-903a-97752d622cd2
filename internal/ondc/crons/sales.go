package crons

import (
	"fmt"
	"kc/internal/ondc/external/slack"
)

func (c *Cron) addSalesAnalysisSkusData() {
	numOfDays := []int{7, 30}
	for _, days := range numOfDays {
		_, err := c.Service.Inventory.GetSkusSalesData(days)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("⚠️ Failed to get sales data for last %d days: %v", days, err))
			continue
		}
	}
	slack.SendSlackMessage("✅ Sales data analysis for SKUs completed successfully.")
}
