package crons

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/external/unicommerce"
	"kc/internal/ondc/models/dao"
	"time"
)

func (c *Cron) updateUnicommerceOrderStatus() {
	orders := []dao.KiranaBazarUnicommerceOrder{}
	query := fmt.Sprintf(`select * from kiranabazar_unicommerce_order where created_at >= '%s'`, time.Now().Add(-15*24*time.Hour).Format("2006-01-02 15:04:05"))
	_, err := c.repository.CustomQuery(&orders, query)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("failed to update unicommerce status 0, %v", err))
		fmt.Println("getting issue updating unicommerce order status")
		return
	}
	for _, order := range orders {
		orderRespone := &unicommerce.UnicommerceCreateSaleOrderResponse{}
		err = json.Unmarshal(order.OrderResponse, orderRespone)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("%d, failed to update unmarshal json unicommerce %v", order.OrderID, err))
			continue
		}
		if orderRespone.Successful && len(orderRespone.Errors) == 0 {
			_, err := c.Service.GetUnicommerceOrderDetails(context.Background(), order.OrderID)
			if err != nil {
				slack.SendSlackMessage(fmt.Sprintf("%d, failed to update unicommerce status 1 %v", order.OrderID, err))
				fmt.Println("failed to fetch order details from unicommerce")
			}
		}
	}
	slack.SendSlackMessage(fmt.Sprintf("updated the unicommerce status, %v", err))
}
