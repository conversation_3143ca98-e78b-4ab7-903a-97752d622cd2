package crons

import (
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/repositories/sqlRepo"
	"time"
)

func handleExotelIVRQueueData(repo *sqlRepo.Repository, cuttOffTime string) {
	queueDataQuery := fmt.Sprintf(`select * from exotel_ivr_queue_data eiqd where created_at <= '%s'; `, cuttOffTime)
	queueData := []dao.ExotelIVRQueueData{}
	_, err := repo.CustomQuery(&queueData, queueDataQuery)
	if err != nil {
		slack.SendSlackMessage("failed to fetch data from exotel_ivr_queue_data")
	}
	queueDataBackup := []dao.ExotelIVRQueueDataBackup{}
	for _, data := range queueData {
		queueDataBackup = append(queueDataBackup, dao.ExotelIVRQueueDataBackup{
			ID:        data.ID,
			Data:      data.Data,
			Status:    data.Status,
			CreatedAt: data.CreatedAt,
			UpdatedAt: data.UpdatedAt,
			Comment:   data.Comment,
			Type:      data.Type,
		})
	}
	_, err = repo.Create(&queueDataBackup)
	if err != nil {
		slack.SendSlackMessage("failed to insert data into exotel_ivr_queue_data_backup")
	} else {
		queueDataDeleteQuery := fmt.Sprintf(`delete from exotel_ivr_queue_data eiqd where created_at <= '%s'; `, cuttOffTime)
		_, err = repo.CustomQuery(nil, queueDataDeleteQuery)
		if err != nil {
			slack.SendSlackMessage("failed to delete data from exotel_ivr_queue_data")
		}
	}
}

func handleExotelIVRData(repo *sqlRepo.Repository, cuttOffTime string) {

	ivrDataQuery := fmt.Sprintf(`select * from exotel_ivr_data eid where created_at <= '%s'; `, cuttOffTime)
	ivrData := []dao.ExotelIVRData{}
	_, err := repo.CustomQuery(&ivrData, ivrDataQuery)
	if err != nil {
		slack.SendSlackMessage("failed to fetch data from exotel_ivr_data")
	}
	ivrDataBackup := []dao.ExotelIVRDataBackup{}
	for _, data := range ivrData {
		ivrDataBackup = append(ivrDataBackup, dao.ExotelIVRDataBackup{
			ID:        data.ID,
			To:        data.To,
			From:      data.From,
			SID:       data.SID,
			Status:    data.Status,
			CreatedAt: data.CreatedAt,
			UpdatedAt: data.UpdatedAt,
		})
	}
	_, err = repo.Create(&ivrDataBackup)
	if err != nil {
		slack.SendSlackMessage("failed to insert data into exotel_ivr_data_backup")
	} else {
		ivrDataDeleteQuery := fmt.Sprintf(`delete from exotel_ivr_data eiqd where created_at <= '%s'; `, cuttOffTime)
		_, err = repo.CustomQuery(nil, ivrDataDeleteQuery)
		if err != nil {
			slack.SendSlackMessage("failed to delete data from exotel_ivr_data")
		}
	}
}

func handleExotelIVRAPILogs(repo *sqlRepo.Repository, cuttOffTime string) {

	ivrAPILogsQuery := fmt.Sprintf(`select * from exotel_ivr_api_logs eid where created_at <= '%s'; `, cuttOffTime)
	ivrAPILogsData := []dao.ExotelIVRAPILogs{}
	_, err := repo.CustomQuery(&ivrAPILogsData, ivrAPILogsQuery)
	if err != nil {
		slack.SendSlackMessage("failed to fetch data from exotel_ivr_api_logs")
	}
	ivrAPILogsDataBackup := []dao.ExotelIVRAPILogsBackup{}
	for _, data := range ivrAPILogsData {
		ivrAPILogsDataBackup = append(ivrAPILogsDataBackup, dao.ExotelIVRAPILogsBackup{
			ID:         data.ID,
			Request:    data.Request,
			Response:   data.Response,
			StatusCode: data.StatusCode,
			QueueID:    data.QueueID,
			CreatedAt:  data.CreatedAt,
			UpdatedAt:  data.UpdatedAt,
		})
	}
	_, err = repo.Create(&ivrAPILogsDataBackup)
	if err != nil {
		slack.SendSlackMessage("failed to insert data into exotel_ivr_api_logs")
	} else {
		ivrAPILogsDeleteQuery := fmt.Sprintf(`delete from exotel_ivr_api_logs eiqd where created_at <= '%s'; `, cuttOffTime)
		_, err = repo.CustomQuery(nil, ivrAPILogsDeleteQuery)
		if err != nil {
			slack.SendSlackMessage("failed to delete data from exotel_ivr_api_logs")
		}
	}
}

func handleExotelIVRExomlWebhookLogs(repo *sqlRepo.Repository, cuttOffTime string) {

	ivrExomlWebhookLogsQuery := fmt.Sprintf(`select * from exotel_ivr_exoml_webhook_logs eid where created_at <= '%s'; `, cuttOffTime)
	ivrExomlWebhookLogsData := []dao.ExotelIVRExomlWebhookLogs{}
	_, err := repo.CustomQuery(&ivrExomlWebhookLogsData, ivrExomlWebhookLogsQuery)
	if err != nil {
		slack.SendSlackMessage("failed to fetch data from exotel_ivr_exoml_webhook_logs")
	}
	ivrExomlWebhookLogsDataBackup := []dao.ExotelIVRExomlWebhookLogsBackup{}
	for _, data := range ivrExomlWebhookLogsData {
		ivrExomlWebhookLogsDataBackup = append(ivrExomlWebhookLogsDataBackup, dao.ExotelIVRExomlWebhookLogsBackup{
			ID:        data.ID,
			Request:   data.Request,
			CreatedAt: data.CreatedAt,
			UpdatedAt: data.UpdatedAt,
		})
	}
	_, err = repo.Create(&ivrExomlWebhookLogsDataBackup)
	if err != nil {
		slack.SendSlackMessage("failed to insert data into exotel_ivr_exoml_webhook_logs")
	} else {
		ivrExomlWebhookLogsDeleteQuery := fmt.Sprintf(`delete from exotel_ivr_exoml_webhook_logs eiqd where created_at <= '%s'; `, cuttOffTime)
		_, err = repo.CustomQuery(nil, ivrExomlWebhookLogsDeleteQuery)
		if err != nil {
			slack.SendSlackMessage("failed to delete data from exotel_ivr_exoml_webhook_logs")
		}
	}
}

func handleExotelIVROrderCancellationData(repo *sqlRepo.Repository, cuttOffTime string) {

	exotelIVROrderCancellationDataQuery := fmt.Sprintf(`select * from exotel_ivr_order_cancellation_data eid where created_at <= '%s'; `, cuttOffTime)
	exotelIVROrderCancellationData := []dao.ExotelIVROrderCancellationData{}
	_, err := repo.CustomQuery(&exotelIVROrderCancellationData, exotelIVROrderCancellationDataQuery)
	if err != nil {
		slack.SendSlackMessage("failed to fetch data from exotel_ivr_order_cancellation_data")
	}
	exotelIVROrderCancellationDataBackup := []dao.ExotelIVROrderCancellationDataBackup{}
	for _, data := range exotelIVROrderCancellationData {
		exotelIVROrderCancellationDataBackup = append(exotelIVROrderCancellationDataBackup, dao.ExotelIVROrderCancellationDataBackup{
			ID:                 data.ID,
			OrderID:            data.OrderID,
			MetaData:           data.MetaData,
			CancellationReason: data.CancellationReason,
			Status:             data.Status,
			CreatedAt:          data.CreatedAt,
			UpdatedAt:          data.UpdatedAt,
		})
	}
	_, err = repo.Create(&exotelIVROrderCancellationDataBackup)
	if err != nil {
		slack.SendSlackMessage("failed to insert data into exotel_ivr_order_cancellation_data")
	} else {
		exotelIVROrderCancellationDataDeleteQuery := fmt.Sprintf(`delete from exotel_ivr_order_cancellation_data eiqd where created_at <= '%s'; `, cuttOffTime)
		_, err = repo.CustomQuery(nil, exotelIVROrderCancellationDataDeleteQuery)
		if err != nil {
			slack.SendSlackMessage("failed to delete data from exotel_ivr_order_cancellation_data")
		}
	}
}

func (c *Cron) backupExotelData() {
	cuttOffTime := time.Now().Add(time.Hour * (-120)).Format("2006-01-02 15:04:05")
	handleExotelIVRQueueData(c.repository, cuttOffTime)
	handleExotelIVRData(c.repository, cuttOffTime)
	handleExotelIVRAPILogs(c.repository, cuttOffTime)
	handleExotelIVRExomlWebhookLogs(c.repository, cuttOffTime)
	handleExotelIVROrderCancellationData(c.repository, cuttOffTime)
}
