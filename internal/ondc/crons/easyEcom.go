package crons

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/easyecom"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/cacheRepo"
	orderstatus "kc/internal/ondc/service/orderStatus"
	"kc/internal/ondc/service/orderStatus/constants"
	"kc/internal/ondc/utils"
	"strings"
	"sync"
	"time"
)

var BANNER_WIDGET_STATUS []string = []string{"IN TRANSIT", "OUT FOR DELIVERY", "SHIPMENT CREATED", "PICKUP SCHEDULED"}
var USER_ORDER_STATUS_CACHE_EXPIRY = 15 * 24 * 60 * 60 // 15 days in seconds
var ORDER_CHECK_EXCLUDED_SELLERS = []string{utils.GO_DESI, utils.PANCHVATI, utils.HUGS, utils.MOTHERS_KITCHEN, utils.APSARA_TEA, utils.MANGALAM, utils.NUTRAJ, utils.LOTS, utils.MICHIS, utils.CRAVITOS, utils.SOOTHE, utils.MILDEN, utils.RSB_SUPER_STOCKIST, utils.KIRANA_CLUB, utils.BOLAS, utils.CHUK_DE, utils.KIRANACLUB_LOYALTY_REWARDS, utils.CANDYLAKE, utils.SUGANDH, utils.SOMNATH}

func UpdateUserOrderStatusInRedis(redisCache *cacheRepo.Repository, userIds map[string]string, operation string) {
	var wg sync.WaitGroup
	expiry := int64(USER_ORDER_STATUS_CACHE_EXPIRY)

	for userID, status := range userIds {
		wg.Add(1)

		go func(userID, status string) {
			defer wg.Done()
			key := fmt.Sprintf("user_order_status_%s", userID)

			switch operation {
			case "set":
				utils.CallGcpRedis(key, operation, status, &expiry)
				redisCache.RedisClient.Set(context.Background(), key, status, time.Second*time.Duration(USER_ORDER_STATUS_CACHE_EXPIRY))

			case "delete":
				utils.CallGcpRedis(key, operation, nil, nil)
				redisCache.RedisClient.Expire(context.Background(), key, time.Duration(0))

			default:
				fmt.Printf("Invalid operation: %s for userID: %s\n", operation, userID)
			}
		}(userID, status)
	}

	wg.Wait()
	fmt.Println("All operations completed. => ", operation)
}

func formatOrders(orders []dao.KiranaBazarOrder) string {
	var result strings.Builder

	for i, order := range orders {
		id := "N/A"
		if order.ID != nil {
			id = fmt.Sprintf("%d", *order.ID)
		}

		userID := "N/A"
		if order.UserID != nil {
			userID = *order.UserID
		}

		orderStatus := "N/A"
		if order.OrderStatus != nil {
			orderStatus = *order.OrderStatus
		}
		createdAt := order.CreatedAt.Format("2006-01-02")
		result.WriteString(fmt.Sprintf("%d. %s, %s, %s, %s, %s \n",
			i+1, id, userID, orderStatus, order.Seller, createdAt))
	}

	return result.String()
}

func (c *Cron) updateEasyEcomOrderStatus() {
	orders := []dao.KiranaBazarEasyEcomOrder{}
	query := fmt.Sprintf(`select * from kiranabazar_easyecom_order where created_at >= '%s'`, time.Now().Add(-15*24*time.Hour).Format("2006-01-02 15:04:05"))
	_, err := c.repository.CustomQuery(&orders, query)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("failed to update easyEcom status 0, %v", err))
		fmt.Println("getting issue updating easyecom order status")
	}
	for _, order := range orders {
		orderRespone := &dto.EasyEcomCreateOrderResponse{}
		err := json.Unmarshal(order.OrderResponse, orderRespone)
		if err != nil {
			continue
		}
		if orderRespone.Code == 200 {
			_, err := c.Service.GetEasyEcomOrderDetails(context.Background(), int(order.OrderID))
			if err != nil {
				slack.SendSlackMessage(fmt.Sprintf("%d, failed to update easyEcom status 1 %v", order.OrderID, err))
				fmt.Println("failed to fetch order details from easy ecom")
			}
		}
	}
	slack.SendSlackMessage(fmt.Sprintf("updated the easyecom status, %v", err))
}

func (c *Cron) checkEasyEcomOrderStatus() {
	orders := []dao.KiranaBazarOrder{}
	var excludedSellersClause string = ""
	if len(ORDER_CHECK_EXCLUDED_SELLERS) > 0 {
		excludedSellers := make([]string, len(ORDER_CHECK_EXCLUDED_SELLERS))
		for i, seller := range ORDER_CHECK_EXCLUDED_SELLERS {
			excludedSellers[i] = fmt.Sprintf("'%s'", seller)
		}
		excludedSellersClause = fmt.Sprintf("AND ko.seller NOT IN (%s)", strings.Join(excludedSellers, ", "))
	}

	query := fmt.Sprintf(`
		SELECT ko.id, ko.user_id, ko.transaction_id, ko.order_status, ko.seller, ko.created_at
		FROM kiranabazar_orders ko
		LEFT JOIN kiranabazar_easyecom_order keo ON ko.id = keo.order_id
		WHERE ko.created_at >= NOW() - INTERVAL 15 DAY
		AND ko.order_status = 'CONFIRMED'
		AND keo.order_id IS NULL
		%s;`, excludedSellersClause)

	_, err := c.repository.CustomQuery(&orders, query)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("failed to fetch kiranabazar orders to check status 0, %v", err))
		fmt.Println("getting issue to fetch kiranabazar orders to check status")
	}

	messageString := formatOrders(orders)
	if messageString == "" {
		slack.SendSlackMessage("All orders in KiranaBazar are present in EasyEcom")
		fmt.Println("All orders in KiranaBazar are present in EasyEcom")
		return
	}
	slack.SendSlackMessage(fmt.Sprintf("Orders which are not present in EasyEcom orders: \n%s", messageString))
	slack.SendSlackMessage(fmt.Sprintf("Verified and Reported orders which are not present in EasyEcom orders, %v", err))
}

func (c *Cron) updateKiranaBazarOrderStatus() {
	orders := []dao.KiranaBazarOrderStatus{}
	query := fmt.Sprintf(`select * from kiranabazar_order_status where updated_at >= '%s'`, time.Now().Add(-1*24*time.Hour).Format("2006-01-02 15:04:05"))
	_, err := c.repository.CustomQuery(&orders, query)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("failed to update the zoff order status 0, %v", err))
		fmt.Println("getting issue updating easyecom order status")
	}

	var addUserIds map[string]string = make(map[string]string)
	var remainingUserIds map[string]string = make(map[string]string)
	var removeUserIds map[string]string = make(map[string]string)

	for _, orderStatus := range orders {
		sot := dao.ShipwayOrderTracking{}
		_, err := c.repository.CustomQuery(&sot, fmt.Sprintf(`select * from shipway_order_tracking sot where order_id = %d;`, *orderStatus.ID))
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("%d, err while feching data for orderid from shipordertracking, %v", *orderStatus.ID, err))
			fmt.Println("err while feching data for orderid from shipordertracking, ", orderStatus.ID)
		}
		if sot.ID != 0 {
			continue
		}
		status := dto.EasyEcomGetOrderDetailsResponse{}
		err = json.Unmarshal(orderStatus.Status, &status)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("%d, failed to update the zoff order status 1, %v", *orderStatus.ID, err))
			fmt.Println("error in update KiranaBazarOrderStatus", err)
		}
		finalStatus := strings.ToUpper(status.Data[0].OrderStatus)
		if finalStatus == "OPEN" {
			finalStatus = "CONFIRMED"
		} else if finalStatus == "CANCELLED" {
			finalStatus = "CANCELLED"
		} else if finalStatus == "PRINTED" {
			finalStatus = "CONFIRMED"
		} else if finalStatus == "RETURNED" {
			finalStatus = "RETURNED"
		} else if status.Data[0].ShippingStatus != "" {
			finalStatus = strings.ToUpper(status.Data[0].ShippingStatus)
		}
		kbo := &dao.KiranaBazarOrder{}
		_, err = c.repository.Find(map[string]interface{}{
			"id": orderStatus.ID,
		}, kbo)
		if err != nil {
			fmt.Println("error fetching order details from the db")
			slack.SendSlackMessage(fmt.Sprintf("error fetching order details from the db for order %d, %v", *orderStatus.ID, err))
		}

		if *kbo.OrderStatus != finalStatus {
			if finalStatus == "DELIVERED" || finalStatus == "CANCELLED" || finalStatus == "RETURNED" {
				if finalStatus == "DELIVERED" {
					// call this service in a go routne
					go func(orderId int64) {
						c.Service.ActivateUserLoyaltyRewards(context.Background(), dto.ActivateUserLoyaltyRewardsRequest{
							UserID: *kbo.UserID,
							Data: dto.ActivateUserLoyaltyRewardsRequestData{
								OrderId: orderId,
							},
						})
					}(*orderStatus.ID)
				}

				// if seller is zoff_foods or RSB call this API again for Thailand Scheme
				// reqObject := map[string]interface{}{
				// 	"user_id":  kbo.UserID,
				// 	"order_id": *orderStatus.ID,
				// 	"amount":   status.Data[0].TotalAmount,
				// 	"status":   finalStatus,
				// }
				// utils.CallExternalAPIAsync(utils.PROGRESS_WIDGET_RESOLVER_API, "POST", reqObject, nil)

				c.Service.UpdateProgressWidget(context.Background(), &dto.UpdateProgressWidgetRequest{
					UserID: *kbo.UserID,
					Data: dto.UpdateProgressWidgetData{
						OrderID: fmt.Sprintf("%d", *orderStatus.ID),
						Status:  finalStatus,
						Amount:  status.Data[0].TotalAmount,
						Seller:  kbo.Seller,
					},
				})
			}
			// call api here to add expected delivery date in db
			referenceCode := fmt.Sprintf("KC_%06d", *orderStatus.ID)
			apiParams := map[string]interface{}{
				"reference_code": referenceCode,
			}
			trackingDetailsApiResponse, err, statusCode, retryCount := easyecom.CallEasyEcomAPI(kbo.Seller, "GET_TRACKING_DETAILS", nil, apiParams, 0)
			if err != nil {
				slack.SendSlackMessage(fmt.Sprintf("%d, failed to fetch trackingDetailsApiResponse after %d retry, %v", *orderStatus.ID, retryCount, err))
				fmt.Println("failed to fetch trackingDetailsApiResponse from easy ecom")
			}

			if statusCode == nil || *statusCode != 200 {
				if statusCode == nil {
					slack.SendSlackMessage(fmt.Sprintf("%d, status code error in trackingDetailsApiResponse after %d retry, %v", *orderStatus.ID, retryCount, err))
					fmt.Println("status code error in trackingDetailsApiResponse")
				} else {
					slack.SendSlackMessage(fmt.Sprintf("%d, status code error in trackingDetailsApiResponse after %d retry, %d", *orderStatus.ID, retryCount, *statusCode))
					fmt.Println("status code error in trackingDetailsApiResponse")
				}
			}

			trackingDetailsResponse := &dto.EasyEcomGetTrackingDetailsResponse{}
			err = json.Unmarshal(trackingDetailsApiResponse, trackingDetailsResponse)
			if err != nil {
				slack.SendSlackMessage(fmt.Sprintf("%d, failed to unmarshal trackingDetailsApiResponse, %v", *orderStatus.ID, err))
				fmt.Println("failed to unmarshal trackingDetailsApiResponse")
			}

			if len(trackingDetailsResponse.Data) > 0 &&
				trackingDetailsResponse.Data[0].ExpectedDeliveryDate != nil &&
				*trackingDetailsResponse.Data[0].ExpectedDeliveryDate != "" &&
				*trackingDetailsResponse.Data[0].ExpectedDeliveryDate != "0000-00-00 00:00:00" {
				timestamp, err := time.Parse("2006-01-02 15:04:05", *trackingDetailsResponse.Data[0].ExpectedDeliveryDate)
				if err == nil {
					c.repository.Update(&dao.KiranaBazarOrderStatus{
						ID: orderStatus.ID,
					}, &dao.KiranaBazarOrderStatus{
						ExpectedDeliveryDate: &timestamp,
					})
				} else {
					slack.SendSlackMessage(fmt.Sprintf("%d, failed to parse expected delivery date, %v", *orderStatus.ID, err))
					fmt.Println("failed to parse expected delivery date")
				}
			}

			// Replace the existing code block with this implementation
			orderStatuses := orderstatus.MapOrderStatus(finalStatus, "", orderstatus.OrderStatusResponse{})

			// Create the base request structure
			updateRequest := &dto.UpdateB2BOrderStatusRequest{
				UpdatedBy: "AUTOMATION",
				Data: dto.UpdateB2BOrderStatusData{
					OrderID:        fmt.Sprintf("%d", *orderStatus.ID),
					UpdatedBy:      "EasyEcom",
					OrderStatus:    finalStatus,
					UpdatedAt:      time.Now().UnixMilli(),
					OrderingModule: kbo.Seller,
					Source:         "AUTOMATION",
					OrderMeta: dto.OrderMeta{
						Note: "Status updated via api",
					},
				},
			}

			switch orderStatuses.DisplayStatus {
			case constants.RETURNED:
				updateRequest.Data.OrderMeta.ReturnedReason = finalStatus
				updateRequest.Data.OrderMeta.Note = "Returned status updated via api"
			case constants.DELIVERED:
				deliveredTime := time.Now().UnixMilli()
				updateRequest.Data.OrderMeta.DeliveredTimestamp = &deliveredTime
				updateRequest.Data.OrderMeta.Note = "Delivered marked via api"
			}

			_, err = c.Service.UpdateB2BOrderStatus(context.Background(), updateRequest)
			if err != nil {
				slack.SendSlackMessage(fmt.Sprintf("failed to update the order status to %s, %v", finalStatus, err))
				fmt.Printf("failed to update the order status to %s\n", finalStatus)
				return
			}

			if (kbo.UserID != nil) && (orderStatuses.DisplayStatus != constants.RETURNED) && (orderStatuses.DisplayStatus != constants.DELIVERED) {
				if includes(BANNER_WIDGET_STATUS, finalStatus) {
					addUserIds[*kbo.UserID] = fmt.Sprintf("%s::%s", kbo.Seller, finalStatus)
				} else if !includes(BANNER_WIDGET_STATUS, finalStatus) {
					remainingUserIds[*kbo.UserID] = fmt.Sprintf("%s::%s", kbo.Seller, finalStatus)
				}
			}
		}
	}

	for key := range remainingUserIds {
		if _, ok := addUserIds[key]; !ok {
			removeUserIds[key] = remainingUserIds[key]
		}
	}

	UpdateUserOrderStatusInRedis(c.Service.Cache, addUserIds, "set")
	UpdateUserOrderStatusInRedis(c.Service.Cache, removeUserIds, "delete")

	slack.SendSlackMessage(fmt.Sprintf("updated the zoff order status, %v", err))
}

func includes(slice []string, str string) bool {
	for _, item := range slice {
		if item == str {
			return true
		}
	}
	return false
}
