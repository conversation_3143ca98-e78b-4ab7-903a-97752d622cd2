package crons

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/brands"
	"log"
	"net/http"
	"sort"
	"strings"
	"time"
)

// Constants for better maintainability
const (
	maxAnnotationLength = 512
	httpTimeout         = 30 * time.Second
	mixpanelURL         = "https://mixpanel.com/api/app/projects/2551336/annotations"
	mixpanelAuth        = "Basic ZGF0YUZldGNoU2xhY2suNTQ4NDQyLm1wLXNlcnZpY2UtYWNjb3VudDpQcFB4MTlyakl6RUNZVmQzQWJpWkFON3ZPbGw4Y3YyNg=="
)

// Data structures
type ProductStatusChange struct {
	ProductID     int    `json:"product_id" gorm:"column:product_id"`
	FieldName     string `json:"field_name" gorm:"column:field_name"`
	PreviousValue string `json:"previous_value" gorm:"column:previous_value"`
	UpdatedValue  string `json:"updated_value" gorm:"column:updated_value"`
}

type BrandAnnotationData struct {
	Brand        string
	ActiveSKUs   []dto.SKULeaderboardItem
	InactiveSKUs []dto.SKULeaderboardItem
	OOSSKUs      []dto.SKULeaderboardItem
	InStockSKUs  []dto.SKULeaderboardItem
}

type CouponStatusChange struct {
	Code      string    `json:"code" gorm:"column:code"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"`
	CreatedBy string    `json:"created_by" gorm:"column:created_by"`
}

// ProductCategories holds categorized product IDs
type ProductCategories struct {
	Active   []int
	Inactive []int
	OOS      []int
	InStock  []int
}

// Main function - sends daily annotations to Mixpanel
func (c *Cron) sendDailyAnnotations() {
	errChan := make(chan string, 1000) // Buffer for errors

	// Process product status changes
	c.processProductStatusChanges(errChan)

	// Process coupon status changes
	c.processCouponStatusChanges(errChan)

	close(errChan)

	// Print all collected errors
	var errorStrings []string
	for err := range errChan {
		errorStrings = append(errorStrings, err)
	}

	if len(errorStrings) > 0 {
		slack.SendSlackDebugMessage(strings.Join(errorStrings, "; "))
	}
}

// Process product status changes and send annotations
func (c *Cron) processProductStatusChanges(errChan chan string) {
	allBrands := brands.GetAllBrands()
	if len(allBrands) == 0 {
		return
	}

	// Get yesterday's status changes
	statusChanges := c.getYesterdaysStatusChanges(errChan)
	if len(statusChanges) == 0 {
		errChan <- "No product status changes found"
		log.Println("No product status changes found")
		return
	}

	// Categorize products by status change
	categories := c.categorizeProductsByStatus(statusChanges)

	// Process each brand
	var allBrandData []BrandAnnotationData
	for _, brand := range allBrands {
		brandData := c.processBrandData(brand, categories, errChan)
		if brandData != nil {
			allBrandData = append(allBrandData, *brandData)
		}
	}

	// Send annotations if we have data
	if len(allBrandData) > 0 {
		c.sendMixpanelAnnotations(allBrandData, errChan)
	}
}

// Process coupon status changes with batching
func (c *Cron) processCouponStatusChanges(errChan chan string) {
	couponChanges := c.getYesterdaysCouponStatusChanges(errChan)
	if len(couponChanges) == 0 {
		return
	}

	// Format all coupon annotations and batch them
	var couponTexts []string
	for _, change := range couponChanges {
		couponText := fmt.Sprintf("Coupon %s created", change.Code)
		couponTexts = append(couponTexts, couponText)
	}

	// Send batched coupon annotations
	c.sendBatchedAnnotations(couponTexts, "Coupons", errChan)
}

func (c *Cron) sendBatchedAnnotations(items []string, category string, errChan chan string) {
	var annotations []string
	var currentAnnotation strings.Builder

	for _, item := range items {
		itemText := item + " | "

		// Check if adding this item would exceed the limit
		if currentAnnotation.Len()+len(itemText) > maxAnnotationLength {
			// Save current annotation and start a new one
			if currentAnnotation.Len() > 0 {
				annotations = append(annotations, strings.TrimSuffix(currentAnnotation.String(), " | "))
				currentAnnotation.Reset()
			}
		}

		currentAnnotation.WriteString(itemText)
	}

	// Add the last annotation if it has content
	if currentAnnotation.Len() > 0 {
		annotations = append(annotations, strings.TrimSuffix(currentAnnotation.String(), " | "))
	}

	// Send each annotation
	for i, annotation := range annotations {
		log.Printf("Sending %s Mixpanel annotation %d: %s", category, i+1, annotation)
		c.sendSingleMixpanelAnnotation(annotation, errChan)
	}
}

// Categorize products by their status changes
func (c *Cron) categorizeProductsByStatus(changes []ProductStatusChange) ProductCategories {
	categories := ProductCategories{
		Active:   []int{},
		Inactive: []int{},
		OOS:      []int{},
		InStock:  []int{},
	}

	for _, change := range changes {
		switch change.FieldName {
		case "is_active":
			if change.PreviousValue == "false" && change.UpdatedValue == "true" {
				categories.Active = append(categories.Active, change.ProductID)
			} else if change.PreviousValue == "true" && change.UpdatedValue == "false" {
				categories.Inactive = append(categories.Inactive, change.ProductID)
			}
		case "is_oos":
			if change.PreviousValue == "false" && change.UpdatedValue == "true" {
				categories.InStock = append(categories.InStock, change.ProductID)
			} else if change.PreviousValue == "true" && change.UpdatedValue == "false" {
				categories.OOS = append(categories.OOS, change.ProductID)
			}
		}
	}

	return categories
}

// Process data for a single brand
func (c *Cron) processBrandData(brand string, categories ProductCategories, errChan chan string) *BrandAnnotationData {
	// Get SKU data for different time ranges
	thirtyDaySKUs := c.getSKULeaderboard(brand, "30d", errChan)
	sevenDaySKUs := c.getSKULeaderboard(brand, "7d", errChan)

	if len(thirtyDaySKUs) == 0 && len(sevenDaySKUs) == 0 {
		return nil
	}

	// Categorize SKUs based on status changes
	brandData := BrandAnnotationData{Brand: brand}

	// Process 30-day data for Active/InStock
	brandData.ActiveSKUs = c.filterSKUsByProductIDs(thirtyDaySKUs, categories.Active)
	brandData.InStockSKUs = c.filterSKUsByProductIDs(thirtyDaySKUs, categories.InStock)

	// Process 7-day data for Inactive/OOS
	brandData.InactiveSKUs = c.filterSKUsByProductIDs(sevenDaySKUs, categories.Inactive)
	brandData.OOSSKUs = c.filterSKUsByProductIDs(sevenDaySKUs, categories.OOS)

	// Apply smart filtering based on brand size
	targetCount := c.calculateTargetCount(len(thirtyDaySKUs))
	brandData = c.applySmartFiltering(brandData, targetCount)

	// Only return if we have data
	if c.hasAnySKUs(brandData) {
		return &brandData
	}

	return nil
}

// Get SKU leaderboard for a brand
func (c *Cron) getSKULeaderboard(brand, dateRange string, errChan chan string) []dto.SKULeaderboardItem {
	res, err := c.Service.GetSKULeaderboard(context.Background(), &dto.GetSKULeaderboardRequest{
		Data: dto.GetSKULeaderboardRequestData{
			DateRange: dateRange,
			Seller:    []string{brand},
			SortBy:    "gtv",
			Limit:     100,
			Offset:    0,
		},
	})
	if err != nil {
		errMsg := fmt.Sprintf("Error getting SKU leaderboard for brand %s (%s): %v", brand, dateRange, err)
		errChan <- errMsg
		return []dto.SKULeaderboardItem{}
	}

	return res.SKULeaderboard.Items
}

// Filter SKUs by product IDs
func (c *Cron) filterSKUsByProductIDs(skus []dto.SKULeaderboardItem, productIDs []int) []dto.SKULeaderboardItem {
	var filtered []dto.SKULeaderboardItem
	for _, sku := range skus {
		if c.containsInt(productIDs, int(sku.ProductID)) {
			filtered = append(filtered, sku)
		}
	}
	return filtered
}

// Calculate target count based on SKU count
func (c *Cron) calculateTargetCount(skuCount int) int {
	if skuCount < 20 {
		return 5
	}

	// Top 30%, max 15
	targetCount := int(float64(skuCount) * 0.3)
	if targetCount > 15 {
		return 15
	}
	return targetCount
}

func (c *Cron) applySmartFiltering(brandData BrandAnnotationData, targetCount int) BrandAnnotationData {
	// Calculate how many SKUs we should show per category based on what we have
	totalSKUs := len(brandData.ActiveSKUs) + len(brandData.InactiveSKUs) +
		len(brandData.OOSSKUs) + len(brandData.InStockSKUs)

	if totalSKUs <= targetCount {
		// If we have fewer SKUs than target, return as is
		return brandData
	}

	// Apply filtering to each category independently, keeping top GTV SKUs
	brandData.ActiveSKUs = c.filterTopSKUsByGTV(brandData.ActiveSKUs, targetCount)
	brandData.InactiveSKUs = c.filterTopSKUsByGTV(brandData.InactiveSKUs, targetCount)
	brandData.OOSSKUs = c.filterTopSKUsByGTV(brandData.OOSSKUs, targetCount)
	brandData.InStockSKUs = c.filterTopSKUsByGTV(brandData.InStockSKUs, targetCount)

	return brandData
}

func (c *Cron) filterTopSKUsByGTV(skus []dto.SKULeaderboardItem, maxCount int) []dto.SKULeaderboardItem {
	if len(skus) <= maxCount {
		return skus
	}

	// Sort by GTV descending
	sort.Slice(skus, func(i, j int) bool {
		return skus[i].GTV > skus[j].GTV
	})

	// Return top N SKUs
	return skus[:maxCount]
}

// Check if brand data has any SKUs
func (c *Cron) hasAnySKUs(brandData BrandAnnotationData) bool {
	return len(brandData.ActiveSKUs) > 0 || len(brandData.InactiveSKUs) > 0 ||
		len(brandData.OOSSKUs) > 0 || len(brandData.InStockSKUs) > 0
}

// Helper function to check if slice contains an integer
func (c *Cron) containsInt(slice []int, item int) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

// Get yesterday's product status changes
func (c *Cron) getYesterdaysStatusChanges(errChan chan string) []ProductStatusChange {
	start, end := c.getYesterdayTimeRange()

	query := fmt.Sprintf(`
		SELECT product_id, field_name, previous_value, updated_value
		FROM kiranaclubdb.product_data_update_history 
		WHERE updated_at BETWEEN %d AND %d
		AND field_name IN ('is_active', 'is_oos')
	`, start.UnixMilli(), end.UnixMilli())

	var changes []ProductStatusChange
	_, err := c.repository.CustomQuery(&changes, query)
	if err != nil {
		errMsg := fmt.Sprintf("Error querying product status changes: %v", err)
		errChan <- errMsg
		return []ProductStatusChange{}
	}

	return changes
}

// Get yesterday's coupon status changes
func (c *Cron) getYesterdaysCouponStatusChanges(errChan chan string) []CouponStatusChange {
	start, end := c.getYesterdayTimeRange()

	query := fmt.Sprintf(`
		SELECT kc.code, kc.created_at, kc.created_by
		FROM kiranaclubdb.kiranabazar_coupons kc 
		JOIN kiranaclubdb.kiranabazar_coupon_brands kcb ON kc.id = kcb.coupon_id 
		WHERE kc.created_at BETWEEN '%s' AND '%s'
		AND kcb.is_active = 1
	`, start.Format("2006-01-02 15:04:05"), end.Format("2006-01-02 15:04:05"))

	var changes []CouponStatusChange
	_, err := c.repository.CustomQuery(&changes, query)
	if err != nil {
		errMsg := fmt.Sprintf("Error querying coupon status changes: %v", err)
		errChan <- errMsg
		return []CouponStatusChange{}
	}

	return changes
}

// Get yesterday's time range
func (c *Cron) getYesterdayTimeRange() (time.Time, time.Time) {
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	start := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location())
	end := start.Add(24 * time.Hour).Add(-time.Nanosecond)
	return start, end
}

// Send multiple annotations to Mixpanel with 512 character limit
func (c *Cron) sendMixpanelAnnotations(brandData []BrandAnnotationData, errChan chan string) {
	var annotations []string
	var currentAnnotation strings.Builder

	for _, brand := range brandData {
		brandText := c.formatBrandAnnotation(brand)

		// Check if adding this brand would exceed the limit
		if currentAnnotation.Len()+len(brandText) > maxAnnotationLength {
			// Save current annotation and start a new one
			if currentAnnotation.Len() > 0 {
				annotations = append(annotations, currentAnnotation.String())
				currentAnnotation.Reset()
			}
		}

		currentAnnotation.WriteString(brandText)
	}

	// Add the last annotation if it has content
	if currentAnnotation.Len() > 0 {
		annotations = append(annotations, currentAnnotation.String())
	}

	// Send each annotation
	for i, annotation := range annotations {
		log.Printf("Sending Mixpanel annotation %d: %s", i+1, annotation)
		c.sendSingleMixpanelAnnotation(annotation, errChan)
	}
}

// Format brand annotation with emojis
func (c *Cron) formatBrandAnnotation(brand BrandAnnotationData) string {
	var result strings.Builder

	result.WriteString(brand.Brand)
	result.WriteString(" | ")

	// Add each category with emoji
	categories := []struct {
		emoji string
		skus  []dto.SKULeaderboardItem
	}{
		{"🚫 OOS", brand.OOSSKUs},           // Out of Stock
		{"🚫 Inactive", brand.InactiveSKUs}, // Inactive
		{"✅ Instock", brand.InStockSKUs},   // In Stock
		{"✅ Active", brand.ActiveSKUs},     // Active
	}

	for _, category := range categories {
		if len(category.skus) > 0 {
			result.WriteString(fmt.Sprintf("\n%s: ", category.emoji))
			for i, sku := range category.skus {
				if i > 0 {
					result.WriteString(", ")
				}
				result.WriteString(sku.SKUName)
			}
			result.WriteString(" | ")
		}
	}

	result.WriteString("\n")
	return result.String()
}

// Send a single annotation to Mixpanel
func (c *Cron) sendSingleMixpanelAnnotation(annotation string, errChan chan string) {
	payload := map[string]interface{}{
		"date":        time.Now().Format("2006-01-02 15:04:05"),
		"description": annotation,
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		errMsg := fmt.Sprintf("Error marshaling Mixpanel payload: %v", err)
		errChan <- errMsg
		return
	}

	req, err := http.NewRequest("POST", mixpanelURL, bytes.NewBuffer(jsonData))
	if err != nil {
		errMsg := fmt.Sprintf("Error creating Mixpanel request: %v", err)
		errChan <- errMsg
		return
	}

	// Set headers
	req.Header.Set("accept", "application/json")
	req.Header.Set("authorization", mixpanelAuth)
	req.Header.Set("content-type", "application/json")

	client := &http.Client{Timeout: httpTimeout}
	resp, err := client.Do(req)
	if err != nil {
		errMsg := fmt.Sprintf("Error sending Mixpanel request: %v", err)
		errChan <- errMsg
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		errMsg := fmt.Sprintf("Error reading Mixpanel response: %v", err)
		errChan <- errMsg
		return
	}

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		errMsg := fmt.Sprintf("Mixpanel API error - Status: %d, Body: %s", resp.StatusCode, string(body))
		errChan <- errMsg
		return
	}

	log.Printf("Successfully sent Mixpanel annotation - Status: %d", resp.StatusCode)
}
