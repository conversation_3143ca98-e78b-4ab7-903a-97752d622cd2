package crons

import (
	"kc/internal/ondc/infrastructure/keyClient"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service"

	cronn "github.com/robfig/cron"
)

type Service struct {
	repository *sqlRepo.Repository
	KeyClient  keyClient.KeyClient
	Mixpanel   *mixpanelRepo.Repository
	Service    *service.Service
}

func NewService(repository *sqlRepo.Repository, client keyClient.KeyClient, mixpanel *mixpanelRepo.Repository, service *service.Service) *Service {
	return &Service{
		repository: repository,
		KeyClient:  client,
		Mixpanel:   mixpanel,
		Service:    service,
	}
}

type Cron struct {
	cron       *cronn.Cron
	repository *sqlRepo.Repository
	Service    *service.Service
}

func CronHandler(s *Service) *Cron {
	cronObject := cronn.New()
	return &Cron{
		repository: s.repository,
		cron:       cronObject,
		Service:    s.Service,
	}
}

type CronFunctions interface {
	updateEasyEcomOrderStatus6AM()
	updateEasyEcomOrderStatus730PM()
}

func (s *Service) CronsInit() error {

	newCronHandler := CronHandler(s)
	go newCronHandler.updateAWBMasterDataInSync() // this is a function started on startup not a crom -- @sanket did this

	err := newCronHandler.updateEasyEcomOrderStatus0930()
	if err != nil {
		return err
	}
	err = newCronHandler.updateEasyEcomOrderStatus1430()
	if err != nil {
		return err
	}
	err = newCronHandler.updateEasyEcomOrderStatus1930()
	if err != nil {
		return err
	}

	err = newCronHandler.updateKiranaBazarOrderStatus10()
	if err != nil {
		return err
	}
	err = newCronHandler.updateKiranaBazarOrderStatus15()
	if err != nil {
		return err
	}
	err = newCronHandler.updateKiranaBazarOrderStatus1955()
	if err != nil {
		return err
	}

	err = newCronHandler.checkEasyEcomOrderStatus6()
	if err != nil {
		return err
	}

	err = newCronHandler.syncUserOrderMetrics0200()
	if err != nil {
		return err
	}

	err = newCronHandler.backupExotelData0500()
	if err != nil {
		return err
	}
	err = newCronHandler.createOrderOnShipway39Min()
	if err != nil {
		return err
	}

	err = newCronHandler.syncAWBNumbersWithDelhivery()
	if err != nil {
		return err
	}

	err = newCronHandler.syncOrderWithShipway130()
	if err != nil {
		return err
	}

	err = newCronHandler.syncDelhiveryPinCodes1300()
	if err != nil {
		return err
	}

	err = newCronHandler.updateUnicommerceOrderStatusCron()
	if err != nil {
		return err
	}

	err = newCronHandler.sendWebengageEventsForDelayedOrders()
	if err != nil {
		return err
	}

	err = newCronHandler.addSalesAnalysisSkusDataCron()
	if err != nil {
		return err
	}
	err = newCronHandler.updateAWBMasterData5am()
	if err != nil {
		return err
	}

	err = newCronHandler.sendDailyWhatsapps()
	if err != nil {
		return err
	}

	err = newCronHandler.sendDailyannotations()
	if err != nil {
		return err
	}

	newCronHandler.cron.Start()
	return nil
}

// 0 0 5 * * *
func (c *Cron) updateAWBMasterData5am() error {
	return c.cron.AddFunc("0 0 5 * * *", c.updateAWBMasterData)
}

func (c *Cron) checkEasyEcomOrderStatus6() error {
	return c.cron.AddFunc("0 0 6 * * *", c.checkEasyEcomOrderStatus)
}

func (c *Cron) updateEasyEcomOrderStatus0930() error {
	// 07:30
	return c.cron.AddFunc("0 30 7 * * *", c.updateEasyEcomOrderStatus)
}

func (c *Cron) updateEasyEcomOrderStatus1430() error {
	// 14:30
	return c.cron.AddFunc("0 30 14 * * *", c.updateEasyEcomOrderStatus)
}

func (c *Cron) updateEasyEcomOrderStatus1930() error {
	// 19:30
	return c.cron.AddFunc("0 30 19 * * *", c.updateEasyEcomOrderStatus)
}

func (c *Cron) updateUnicommerceOrderStatusCron() error {
	// 08:30, 15:30, 20:30
	return c.cron.AddFunc("0 30 8,15,20 * * *", c.updateUnicommerceOrderStatus)
}

func (c *Cron) updateKiranaBazarOrderStatus10() error {
	// 8:00
	return c.cron.AddFunc("0 00 8 * * *", c.updateKiranaBazarOrderStatus)
}

func (c *Cron) updateKiranaBazarOrderStatus15() error {
	// 15:00
	return c.cron.AddFunc("0 00 15 * * *", c.updateKiranaBazarOrderStatus)
}

func (c *Cron) updateKiranaBazarOrderStatus1955() error {
	// 19:55
	return c.cron.AddFunc("0 55 19 * * *", c.updateKiranaBazarOrderStatus)
}

func (c *Cron) syncUserOrderMetrics0200() error {
	return c.cron.AddFunc("0 0 2 * * *", c.syncUserOrderMetrics)
}

func (c *Cron) backupExotelData0500() error {
	return c.cron.AddFunc("0 0 5 * * *", c.backupExotelData)
}

func (c *Cron) createOrderOnShipway39Min() error {
	// Run at minute 39 every 3 hours
	return c.cron.AddFunc("0 39 */3 * * *", c.createOrderOnShipway)
}

func (c *Cron) syncAWBNumbersWithDelhivery() error {
	// Run every 2 hours from 6 AM to 9 PM (6,8,10,12,14,16,18,20)
	// this is for hugs and apsara tea
	return c.cron.AddFunc("0 0 6-20/2 * * *", c.GetAWBNumberFromDelhivery)
}

func (c *Cron) syncOrderWithShipway130() error {
	return c.cron.AddFunc("0 30 1 * * *", c.syncOrderWithShipway)
}

func (c *Cron) syncDelhiveryPinCodes1300() error {
	return c.cron.AddFunc("0 0 13 * * *", c.syncDelhiveryPincodes)
}

func (c *Cron) sendWebengageEventsForDelayedOrders() error {
	return c.cron.AddFunc("0 0 17 * * *", c.sendWebEngageEventsForDelayedOrders)
}

func (c *Cron) sendDailyWhatsapps() error {
	// 10:00
	return c.cron.AddFunc("0 00 10 * * *", c.sendDailyWAs)
}

func (c *Cron) sendDailyannotations() error {
	return c.cron.AddFunc("0 00 1 * * *", c.sendDailyAnnotations)
}

func (c *Cron) addSalesAnalysisSkusDataCron() error {
	// Run every day at 6 AM
	return c.cron.AddFunc("0 0 6 * * *", c.addSalesAnalysisSkusData)
}
