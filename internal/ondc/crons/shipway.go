package crons

import (
	"context"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/logistics/couriers"
	"slices"
)

// createOrderOnShipway

type OrderAwbCourier struct {
	OrderID   uint64 `json:"order_id"`
	AWBNumber string `json:"awb_number"`
	Courier   string `json:"courier"`
	Seller    string `json:"seller"`
}

func (c *Cron) createOrderOnShipway() {
	oacs := []OrderAwbCourier{}
	query := fmt.Sprintf(`
	select
		kos.id as order_id,
		awb_number,
		courier,
		seller
	from
		kiranabazar_order_status kos
	join kiranabazar_orders ko on
		ko.id = kos.id
	where 
	delivery_status <> 'DELIVERED' and kos.id > 49000 and
		awb_number is not null
		and awb_number <> ""
		and awb_number not in (
		select
			awb_number
		from
			shipway_order_tracking sot) and awb_number not in (select awb_number from kiranabazar_awb_master);`)

	_, err := c.repository.CustomQuery(&oacs, query)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("error while creating order on shipway, ", err))
		return
	}

	for _, oac := range oacs {

		// not creating any order on shipway for delhivery @sanket stopped creating order on shipway fir delhivery
		courierDetails, err := couriers.GetCourierByName(&oac.Courier)
		if err != nil {
			continue
		}
		carrierID := courierDetails.ID
		if carrierID == "2" && slices.Contains([]string{"zoff_foods", "hugs", "apsara_tea"}, oac.Seller) {
			continue
		}

		// if not delhivery create order on shipway
		c.Service.CreateShipwayOrder(context.Background(), &dto.CreateShipwayOrderRequest{
			OrderID:     fmt.Sprintf("%d", oac.OrderID),
			ForceCreate: true,
		})
		fmt.Println("shipway order pushed for ", oac.OrderID)
	}

}

func (c *Cron) syncOrderWithShipway() {
	// stopping shipway polling -- author: sanket wable -- reason: data is getting updated wrong

	// startTime := time.Now().Add(-1 * time.Hour * 360).Format("2006-01-02 15:04:05")
	// fmt.Println("startTime = ", startTime)
	// limit := 10
	// offset := 0
	// for {
	// 	query := fmt.Sprintf(`select * from kiranaclubdb.shipway_api_logs sal where created_at > '%s' limit %d offset %d;`, startTime, limit, offset)
	// 	logs := []dao.ShipwayAPILogs{}
	// 	c.repository.CustomQuery(&logs, query)
	// 	for _, order := range logs {
	// 		var jsonMap map[string]interface{}
	// 		if err := json.Unmarshal(order.Request, &jsonMap); err != nil {
	// 			fmt.Println("err while unmarshalling the request", err)
	// 			continue
	// 		}
	// 		orderID, ok := jsonMap["order_id"].(string)
	// 		if !ok {
	// 			continue
	// 		}
	// 		_, err := c.Service.GetShipwayOrderDetails(context.Background(), &dto.ShipwayOrderFetchRequest{
	// 			OrderID: orderID,
	// 		})
	// 		if err != nil {
	// 			fmt.Println("err in fetching order details", err)
	// 			continue
	// 		}
	// 	}
	// 	offset += 10

	// }

}
