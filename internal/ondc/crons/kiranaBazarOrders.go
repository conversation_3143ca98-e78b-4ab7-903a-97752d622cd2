package crons

import (
	"context"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"strings"
	"time"

	"github.com/mixpanel/mixpanel-go"
)

func (c *Cron) syncUserOrderMetrics() {
	start := time.Now()
	ctx := context.Background()

	// Process in batches using limit-offset pagination
	const batchSize = 1000
	offset := 0
	totalProcessed := 0
	totalErrors := 0

	for {
		// Query a batch of users with limit-offset
		var userStats []dao.UserOrderStats

		// Use LIMIT and OFFSET for efficient pagination
		query := `
			SELECT
				ko.user_id,
				SUM(kop.amount) AS total_placed_order_amount,
				SUM(CASE WHEN ko.display_status = "DELIVERED" THEN kop.amount ELSE 0 END) AS total_delivered_order_amount,
				SUM(CASE WHEN ko.display_status = "CANCELLED" THEN kop.amount ELSE 0 END) AS total_cancelled_order_amount,
				SUM(CASE WHEN ko.display_status = "RETURNED" THEN kop.amount ELSE 0 END) AS total_returned_order_amount,
				COUNT(*) AS order_placed,
				COUNT(CASE WHEN ko.display_status = "DELIVERED" THEN 1 ELSE NULL END) AS order_delivered,
				COUNT(CASE WHEN ko.processing_status IN ("CONFIRMED", "PUSHED_TO_OMS", "SHIPMENT_CREATED") THEN 1 ELSE NULL END) AS confirmed_order,
				COUNT(CASE WHEN ko.display_status = "CANCELLED" THEN 1 ELSE NULL END) AS cancelled_order,
				COUNT(CASE WHEN ko.display_status = "RETURNED" THEN 1 ELSE NULL END) AS returned_order,
				SUM(CASE WHEN ko.processing_status IN ("CONFIRMED", "PUSHED_TO_OMS", "SHIPMENT_CREATED") THEN kop.amount ELSE 0 END) AS total_confirmed_order_amount
			FROM
				kiranabazar_orders ko
			LEFT JOIN
				kiranabazar_order_payments kop ON ko.id = kop.order_id
			GROUP BY
				ko.user_id
			LIMIT ? OFFSET ?
		`

		err := c.repository.Db.Raw(query, batchSize, offset).Scan(&userStats).Error
		if err != nil {
			msg := fmt.Sprintf("Failed to fetch data for user order metrics (batch at offset %d): %v", offset, err)
			slack.SendSlackMessage(msg)
			fmt.Println(msg)
			return
		}

		// Break the loop if no more records
		if len(userStats) == 0 {
			break
		}

		// Process this batch
		processedCount, errorCount := c.processBatch(ctx, userStats)

		totalProcessed += processedCount
		totalErrors += errorCount

		// Move to the next batch
		offset += batchSize

		// Log progress
		fmt.Printf("Processed batch: offset=%d, count=%d, errors=%d\n",
			offset-batchSize, processedCount, errorCount)

		// Optional: add a small delay to prevent overwhelming the database
		time.Sleep(50 * time.Millisecond)
	}

	duration := time.Since(start)
	msg := fmt.Sprintf("✅ Synced user order metrics successfully. Total processed: %d, Total errors: %d, Duration: %v",
		totalProcessed, totalErrors, duration)
	slack.SendSlackMessage(msg)
	fmt.Println(msg)
}

// processBatch handles a batch of user stats, updating DB and Mixpanel
func (c *Cron) processBatch(ctx context.Context, userStats []dao.UserOrderStats) (int, int) {
	if len(userStats) == 0 {
		return 0, 0
	}

	processedCount := 0
	errorCount := 0

	// 1. Prepare batch DB update
	valueStrings := make([]string, 0, len(userStats))
	valueArgs := make([]interface{}, 0, len(userStats)*11) // 11 columns per record

	// 2. Prepare Mixpanel batch
	var peopleProperties []*mixpanel.PeopleProperties

	// Process each user in the batch
	for _, stats := range userStats {
		// Skip empty user IDs
		if stats.UserID == "" {
			continue
		}

		// Add to DB batch
		valueStrings = append(valueStrings, "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")
		valueArgs = append(valueArgs,
			stats.UserID,
			stats.TotalPlacedOrderAmount,
			stats.TotalDeliveredOrderAmount,
			stats.OrderPlaced,
			stats.OrderDelivered,
			stats.ConfirmedOrder,
			stats.TotalConfirmedOrderAmount,
			stats.TotalCancelledOrderAmount,
			stats.CancelledOrder,
			stats.ReturnedOrder,
			stats.TotalReturnedOrderAmount,
		)

		// Add to Mixpanel batch
		userProperty := mixpanel.NewPeopleProperties(stats.UserID, map[string]any{
			"TOTAL_ORDERS_PLACED":    stats.OrderPlaced,
			"TOTAL_ORDERS_DELIVERED": stats.OrderDelivered,
			"TOTAL_CONFIRMED_ORDERS": stats.ConfirmedOrder,
			"TOTAL_CANCELLED_ORDERS": stats.CancelledOrder,
			"TOTAL_RETURNED_ORDERS":  stats.ReturnedOrder,
			"LIFETIME_ORDER_VALUE":   fmt.Sprintf("%.2f", stats.TotalPlacedOrderAmount),
			"DELIVERED_ORDER_VALUE":  fmt.Sprintf("%.2f", stats.TotalDeliveredOrderAmount),
			"CONFIRMED_ORDER_VALUE":  fmt.Sprintf("%.2f", stats.TotalConfirmedOrderAmount),
			"CANCELLED_ORDER_VALUE":  fmt.Sprintf("%.2f", stats.TotalCancelledOrderAmount),
			"RETURNED_ORDER_VALUE":   fmt.Sprintf("%.2f", stats.TotalReturnedOrderAmount),
		})
		peopleProperties = append(peopleProperties, userProperty)
		processedCount++
	}

	// Execute DB batch update if we have valid records
	if len(valueStrings) > 0 {
		upsertQuery := `
			INSERT INTO user_order_stats (
				user_id, 
				total_placed_order_amount, 
				total_delivered_order_amount, 
				order_placed, 
				order_delivered,
				confirmed_order,
				total_confirmed_order_amount,
				total_cancelled_order_amount,
				cancelled_order,
				returned_order,
				total_returned_order_amount
			) VALUES %s
			ON DUPLICATE KEY UPDATE
				total_placed_order_amount = VALUES(total_placed_order_amount),
				total_delivered_order_amount = VALUES(total_delivered_order_amount),
				order_placed = VALUES(order_placed),
				order_delivered = VALUES(order_delivered),
				confirmed_order = VALUES(confirmed_order),
				total_confirmed_order_amount = VALUES(total_confirmed_order_amount),
				total_cancelled_order_amount = VALUES(total_cancelled_order_amount),
				returned_order = VALUES(returned_order),
				total_returned_order_amount = VALUES(total_returned_order_amount),
				cancelled_order = VALUES(cancelled_order)
		`

		// Format the query with the correct number of value placeholders
		formattedQuery := fmt.Sprintf(upsertQuery, strings.Join(valueStrings, ", "))

		// Execute the batch query
		err := c.repository.Db.Exec(formattedQuery, valueArgs...).Error
		if err != nil {
			errorCount += len(valueStrings)
			msg := fmt.Sprintf("Failed to execute batch upsert: %v", err)
			slack.SendSlackMessage(msg)
			fmt.Println(msg)
		}
	}

	// Execute Mixpanel batch update
	if len(peopleProperties) > 0 {
		// Split into smaller batches if needed (Mixpanel might have limits)
		const mixpanelBatchSize = 200
		for i := 0; i < len(peopleProperties); i += mixpanelBatchSize {
			end := i + mixpanelBatchSize
			if end > len(peopleProperties) {
				end = len(peopleProperties)
			}

			batch := peopleProperties[i:end]
			err := c.Service.Mixpanel.PeopleSet(ctx, batch)
			if err != nil {
				msg := fmt.Sprintf("Failed to send batch to Mixpanel: %v", err)
				slack.SendSlackMessage(msg)
				fmt.Println(msg)
			}
		}
	}

	return processedCount, errorCount
}
