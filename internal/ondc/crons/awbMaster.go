package crons

import (
	"context"
	"fmt"
	"kc/internal/ondc/external/slack"
	"sync"
	"sync/atomic"
	"time"
)

func (c *Cron) updateAWBMasterDataInSync() {
	time.Sleep(time.Second * 20)
	for {
		waybillCourierData, err := c.Service.AWBMaster.DequeueAWBSync()
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("err while getting incomplete waybills data %v", err))
			time.Sleep(time.Second * 20)
			continue
		}
		if waybillCourierData == nil {
			time.Sleep(time.Second * 20)
			continue
		}

		waybills := make([]string, 0, len(waybillCourierData))
		for _, waybillCourier := range waybillCourierData {
			waybills = append(waybills, waybillCourier.WaybillNo)
		}

		err = c.Service.AWBMaster.SyncWayBillData(context.Background(), waybills)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("err while syncing waybill data queue %v", err))
			waybillString := ""
			for _, waybill := range waybills {
				waybillString += waybill + ","
			}
			slack.SendSlackMessage(fmt.Sprintf(" data here %s", waybillString))
		}

		// Handle completion error
		if err := c.Service.AWBMaster.CompleteAWBSync(waybillCourierData); err != nil {
			slack.SendSlackMessage(fmt.Sprintf("err while completing AWB sync %v", err))
		}

		time.Sleep(time.Second * 10)
	}
}

// this is the 5AM cron
func (c *Cron) updateAWBMasterData() {
	createdAt := time.Now().Add(-40 * 24 * time.Hour)

	var count int64 // Use atomic for thread-safe counting
	wg := sync.WaitGroup{}

	// Channel to limit concurrent goroutines
	semaphore := make(chan struct{}, 10) // Limit to 10 concurrent sync operations

	wg.Add(2)

	// Delhivery goroutine
	go func() {
		defer wg.Done()
		c.processCourierWaybills(createdAt, []string{"Delhivery"}, 49, &count, semaphore)
	}()

	// Ekart goroutine
	go func() {
		defer wg.Done()
		c.processCourierWaybills(createdAt, []string{"Ekart Large", "Ekart"}, 20, &count, semaphore)
	}()

	wg.Wait()
	finalCount := atomic.LoadInt64(&count)
	slack.SendSlackMessage(fmt.Sprintf("AWB sync completed for %d waybills", finalCount))
}

func (c *Cron) processCourierWaybills(createdAt time.Time, couriers []string, limit int64, count *int64, semaphore chan struct{}) {
	var offset int64 = 0

	for {
		waybills, err := c.Service.AWBMaster.GetIncompleteWaybills(limit, offset, createdAt, couriers)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("err while getting incomplete waybills data for %v: %v", couriers, err))
			break
		}
		if len(waybills) == 0 {
			break
		}

		waybillNumbers := make([]string, 0, len(waybills))
		for _, waybill := range waybills {
			waybillNumbers = append(waybillNumbers, waybill.AWBNumber)
		}

		// Use semaphore to limit concurrent operations
		semaphore <- struct{}{}
		go func(wbs []string) {
			defer func() { <-semaphore }()
			c.syncW(wbs)
		}(waybillNumbers)

		atomic.AddInt64(count, int64(len(waybills)))
		offset += limit
		time.Sleep(time.Second * 10)
	}
}

func (c *Cron) syncW(waybills []string) {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	err := c.Service.AWBMaster.SyncWayBillData(ctx, waybills)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("err while syncing waybill data 5AM cron %v", err))
	}
}
