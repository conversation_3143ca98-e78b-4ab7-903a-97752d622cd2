package crons

import (
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/external/whatsapp"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"net/url"
	"strings"
	"sync"
	"time"

	"gorm.io/datatypes"
)

type SendWebEngageUtility struct {
	OrderId      string         `json:"id" gorm:"column:order_id"`
	UserId       string         `json:"user_id" gorm:"column:user_id"`
	Seller       string         `json:"seller" gorm:"column:seller"`
	CreatedAt    string         `json:"created_at" gorm:"column:created_at"`
	OrderDetails datatypes.JSON `json:"order_details" gorm:"column:order_details"`
}

func (c *Cron) sendDailyWAs() {

	now := time.Now()

	endTime := now.Add(-24 * time.Hour)

	startTime := endTime.AddDate(0, 0, -14) // -14 because we already went back 1 day

	startTimeStr := startTime.Format("2006-01-02 15:04:05")
	endTimeStr := endTime.Format("2006-01-02 15:04:05")

	query := fmt.Sprintf(`
        select
    ko.id as order_id,
    ko.user_id,
    ko.seller,
    ko.created_at,
    kod.order_details
	from
    	kiranaclubdb.kiranabazar_orders ko
		left join kiranaclubdb.kiranabazar_order_details kod on    
    	ko.id = kod.order_id
	where
    	ko.display_status in ('PLACED', 'PENDING_CONFIRMATION')
    	and ko.created_at between '%s' and '%s'
    `, startTimeStr, endTimeStr)

	allOrders := make([]SendWebEngageUtility, 0)
	_, err := c.repository.CustomQuery(&allOrders, query)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("[CRON] Error while fetching orders for daily WhatsApp utility: %v ", err))
		return
	}

	if len(allOrders) == 0 {
		return
	}

	// Use a worker pool pattern with limited concurrency
	const maxWorkers = 10
	semaphore := make(chan struct{}, maxWorkers)
	var wg sync.WaitGroup
	errChan := make(chan error, len(allOrders))

	for _, order := range allOrders {
		wg.Add(1)
		go func(order SendWebEngageUtility, errChan chan error) {
			defer wg.Done()

			// Acquire semaphore
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			c.processOrder(order, errChan)
		}(order, errChan)
	}

	wg.Wait()

	if len(errChan) > 0 {
		//itrate on channnel build string and send slack message
		errorMessages := "Some Error Occured In Cron SendDailyWAs: OrderIds that went wrong - ["
		close(errChan)
		for err := range errChan {
			errorMessages += fmt.Sprintf("%s, ", err.Error())
		}
		errorMessages += "]"

		slack.SendSlackMessage(errorMessages)
	}
}

func (c *Cron) processOrder(order SendWebEngageUtility, errChan chan error) {

	if order.OrderDetails == nil {
		errChan <- fmt.Errorf("%s", order.OrderId)
		fmt.Printf("[CRON] Order details are nil for order %s\n", order.OrderId)
		return
	}

	orderDetails := dao.KiranaBazarOrderDetails{}
	err := json.Unmarshal(order.OrderDetails, &orderDetails)
	if err != nil {
		errChan <- fmt.Errorf("%s", order.OrderId)
		fmt.Printf("[CRON] Error while unmarshalling order details for order %s: %v\n", order.OrderId, err)
		return
	}

	// Calculate days left for auto cancel
	daysLeft, err := calculateDaysLeftForAutoCancel(order.CreatedAt)
	if err != nil {
		errChan <- fmt.Errorf("%s", order.OrderId)
		fmt.Printf("[CRON] Error calculating days left for order %s: %v\n", order.OrderId, err)
		return
	}

	dynamicLinkData := map[string]interface{}{
		"cta": map[string]interface{}{
			"name":     "WebViewOld",
			"nav_type": "Redirect to WebviewOld",
			"params": map[string]interface{}{
				"screenTitle": "",
				"showHeader":  false,
				"uri":         fmt.Sprintf("https://kcbazar.retailpulse.ai/orderDetails?id=%s&seller=%s", order.OrderId, order.Seller),
			},
		},
		"non_firebase_link": false,
	}

	link, err := whatsapp.GenerateDynamicLink(dynamicLinkData)
	if err != nil {
		errChan <- fmt.Errorf("%s[dl]", order.OrderId)
		fmt.Printf("error generating dynamic link: %v", err)
		return
	}

	link, _ = extractURLPath(link)

	webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:   []string{order.UserId},
		EventName: "Order in pending confirmation state",
		EventObject: map[string]interface{}{
			"order_id":                  order.OrderId,
			"seller":                    order.Seller,
			"order_value":               orderDetails.GetOrderValue(),
			"days_left_for_auto_cancel": daysLeft,
			"redirection_link":          link,
		},
	})
}

func calculateDaysLeftForAutoCancel(createdAtStr string) (int, error) {
	// Parse the RFC3339 timestamp format: "2025-06-27T20:31:21+05:30"
	createdAt, err := time.Parse(time.RFC3339, createdAtStr)
	if err != nil {
		return 0, fmt.Errorf("failed to parse created_at timestamp: %v", err)
	}

	// Calculate created_at + 5 days
	autoCancelTime := createdAt.AddDate(0, 0, 5)

	// Get current time
	currentTime := time.Now()

	// Calculate difference in hours and convert to days
	duration := autoCancelTime.Sub(currentTime)
	daysLeft := int(duration.Hours() / 24)

	// If the result is negative, it means the auto-cancel time has passed
	if daysLeft < 0 {
		return 0, nil
	}

	return daysLeft, nil
}

func extractURLPath(urlString string) (string, error) {
	parsedURL, err := url.Parse(urlString)
	if err != nil {
		return "", fmt.Errorf("error parsing URL: %v", err)
	}

	path := parsedURL.Path
	path = strings.TrimPrefix(path, "/")
	parts := strings.Split(path, "/")
	lastComponent := parts[len(parts)-1]

	return lastComponent, nil
}
