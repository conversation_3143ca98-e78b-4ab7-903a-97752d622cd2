package crons

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/repositories/cacheRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/utils"

	"gorm.io/datatypes"
)

type DispatchOrderEvent struct {
	DispatchTime int64          `gorm:"column:order_dispatched"`
	UserID       string         `gorm:"column:user_id"`
	AWBNumber    string         `gorm:"column:awb_number"`
	Courier      string         `gorm:"column:courier"`
	OrderID      int64          `gorm:"column:order_id"`
	Seller       string         `gorm:"column:seller"`
	OrderDetails datatypes.JSON `gorm:"column:order_details"`
	StatusCode   string         `gorm:"column:status_code"`
	Pdd          int64          `gorm:"column:pdd"`
}

type EventBatch struct {
	Orders    []DispatchOrderEvent
	EventName string
}

// SendWebEngageEventsForDispatchedOrders processes orders requiring notifications
func (c *Cron) sendWebEngageEventsForDelayedOrders() {
	// Create a context for cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Channel for collecting event batches to be processed
	eventBatchChan := make(chan EventBatch, 4) // Buffer for the 4 different event types

	// Use errGroup to collect errors from goroutines
	var wg sync.WaitGroup
	errChan := make(chan error, 4) // Channel to collect errors

	// Create worker pool for processing events
	const workerCount = 3
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			processEventBatches(ctx, eventBatchChan, c.Service.AzureRedis)
		}()
	}

	// Get base queries
	dispatchQuery := getDispatchBaseQuery()
	orderQuery := getOrderBaseQuery()

	// Launch data fetching goroutines
	wg.Add(4) // For the 4 different queries

	// Query 1: Orders dispatched 5-10 days ago
	go func() {
		defer wg.Done()
		orders, err := fetchEventsFor5Days(dispatchQuery, c.repository)
		if err != nil {
			errChan <- fmt.Errorf("5-day dispatch query error: %w", err)
			return
		}

		select {
		case <-ctx.Done():
			return
		case eventBatchChan <- EventBatch{Orders: orders, EventName: "5 Days from Order Dispatched"}:
		}
	}()

	// Query 2: Orders dispatched 10+ days ago
	go func() {
		defer wg.Done()
		orders, err := fetchEventsFor10Days(dispatchQuery, c.repository)
		if err != nil {
			errChan <- fmt.Errorf("10-day dispatch query error: %w", err)
			return
		}

		select {
		case <-ctx.Done():
			return
		case eventBatchChan <- EventBatch{Orders: orders, EventName: "10 Days from Order Dispatched"}:
		}
	}()

	// Query 3: Orders 1 day past promised delivery date
	go func() {
		defer wg.Done()
		orders, err := fetchOFDFor1Days(orderQuery, c.repository)
		if err != nil {
			errChan <- fmt.Errorf("1-day OFD query error: %w", err)
			return
		}

		select {
		case <-ctx.Done():
			return
		case eventBatchChan <- EventBatch{Orders: orders, EventName: "1 Days from Promised Delivery Date"}:
		}
	}()

	// Query 4: Orders 2+ days past promised delivery date
	go func() {
		defer wg.Done()
		orders, err := fetchOFDFor2Days(orderQuery, c.repository)
		if err != nil {
			errChan <- fmt.Errorf("2-day OFD query error: %w", err)
			return
		}

		select {
		case <-ctx.Done():
			return
		case eventBatchChan <- EventBatch{Orders: orders, EventName: "2 Days from Promised Delivery Date"}:
		}
	}()

	// Start a goroutine to handle errors and potentially cancel context
	// Wait for all query goroutines to finish
	wg.Wait()
	for err := range errChan {
		slack.SendSlackMessage(err.Error())
		// Consider if you want to cancel all operations on first error
		// cancel()
		// break
	}

	// Close channels
	close(eventBatchChan)
	close(errChan)
}

// processEventBatches processes batches of events from the channel
func processEventBatches(ctx context.Context, batchChan <-chan EventBatch, redis *cacheRepo.Repository) {
	for batch := range batchChan {
		select {
		case <-ctx.Done():
			return
		default:
			processOrderBatch(ctx, batch.Orders, batch.EventName, redis)
		}
	}
}

// processOrderBatch processes a batch of orders with proper concurrency control
func processOrderBatch(ctx context.Context, orders []DispatchOrderEvent, eventName string, redis *cacheRepo.Repository) {
	// Limit concurrent API calls
	maxConcurrent := 10
	semaphore := make(chan struct{}, maxConcurrent)

	var wg sync.WaitGroup
	for _, order := range orders {
		// Skip processing if context canceled
		redisClient := redis.RedisClient

		orderEventKey := fmt.Sprintf("%s::%d", eventName, order.OrderID)

		// Check if the key exists and get its value
		val, err := redisClient.Get(ctx, orderEventKey).Int()
		if err == nil && val == 1 {
			continue
		}

		select {
		case <-ctx.Done():
			return
		default:
		}

		// Copy order to avoid race conditions with loop variable
		orderCopy := order

		wg.Add(1)
		go func(redis *cacheRepo.Repository) {
			defer wg.Done()

			// Acquire semaphore slot
			select {
			case semaphore <- struct{}{}:
				defer func() { <-semaphore }() // Release slot when done
			case <-ctx.Done():
				return
			}

			// Process single order
			processOrder(ctx, orderCopy, eventName, redis)
		}(redis)
	}

	wg.Wait()

}

// processOrder handles a single order event
func processOrder(ctx context.Context, order DispatchOrderEvent, eventName string, redis *cacheRepo.Repository) {
	// Parse order details

	var orderDetails dao.KiranaBazarOrderDetails
	err := json.Unmarshal(order.OrderDetails, &orderDetails) // Note the address-of operator
	if err != nil {
		// Log error but continue with other orders
		slack.SendSlackMessage(fmt.Sprintf("Failed to unmarshal order details for order ID %d: %v",
			order.OrderID, err))
		return
	}

	// Create event payload
	eventObject := map[string]interface{}{
		"distinct_id":     order.UserID,
		"awb_number":      order.AWBNumber,
		"courier_name":    order.Courier,
		"tracking_link":   "",
		"order_id":        order.OrderID,
		"cart_value":      int(orderDetails.GetCartValue()),
		"order_value":     int(orderDetails.GetOrderValue()),
		"seller":          order.Seller,
		"ordering_module": utils.MakeTitleCase(order.Seller),
		"time":            normalizeTime(order.Pdd, order.DispatchTime),
	}

	if eventName == "5 Days from Order Dispatched" || eventName == "10 Days from Order Dispatched" {
		eventObject["order_current_status"] = "IN_TRANSIT"
	}

	// Send event
	select {
	case <-ctx.Done():
		return
	default:
		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{order.UserID},
			EventName:   eventName,
			EventObject: eventObject,
		})

		orderEventKey := fmt.Sprintf("%s::%d", eventName, order.OrderID)

		redis.RedisClient.Set(ctx, orderEventKey, 1, time.Hour*24*7).Err()
	}
}

func normalizeTime(pdd int64, dispatchTime int64) int64 {
	if pdd == 0 {
		currentTime := time.Now()

		dispatchedTime := time.UnixMilli(dispatchTime)

		standardDeliveryDate := dispatchedTime.AddDate(0, 0, 7)

		if standardDeliveryDate.Before(currentTime) {
			return standardDeliveryDate.UnixMilli()
		} else {
			return currentTime.AddDate(0, 0, 2).UnixMilli()
		}

	}
	return pdd
}

// getDispatchBaseQuery returns the base query for dispatch events
func getDispatchBaseQuery() string {
	return `
		select
			ko.user_id,
			kam.awb_number,
			kam.courier,
			kbr.order_id,
			ko.seller,
			kod.order_details ,
			kam.status_code,
			kbr.order_dispatched
		from
			kiranaclubdb.kc_bazar_reconciliation kbr
		join kiranaclubdb.kiranabazar_orders ko on
			kbr.order_id = ko.id
		join kiranaclubdb.kiranabazar_awb_master kam on
			kam.order_id = ko.id
		join kiranaclubdb.kiranabazar_order_details kod on
			kod.order_id = ko.id
		where
			ko.display_status not in ('CANCELLED', 'ARCHIVED')
			and ko.delivery_status in ('IN_TRANSIT', 'DELAYED')
			and kbr.order_shipment_created is not null
			and kbr.order_dispatched is not null
			and kbr.order_delivered is null
			and kbr.order_returned is null
			and kbr.order_ofd is null
			and kam.is_primary = 1
	`
}

// getOrderBaseQuery returns the base query for OFD events
func getOrderBaseQuery() string {
	return `
		select
			ko.user_id,
			kam.awb_number,
			kam.courier,
			kbr.order_id,
			ko.seller,
			kod.order_details ,
			kam.status_code,
			kbr.order_dispatched
		from
			kiranaclubdb.kc_bazar_reconciliation kbr
		join kiranaclubdb.kiranabazar_orders ko on
			kbr.order_id = ko.id
		join kiranaclubdb.kiranabazar_awb_master kam on
			kam.order_id = ko.id
		join kiranaclubdb.kiranabazar_order_details kod on
			kod.order_id = ko.id
		where
			ko.display_status not in ('CANCELLED', 'ARCHIVED')
			and ko.delivery_status in ('IN_TRANSIT', 'DELAYED')
			and kbr.order_shipment_created is not null
			and kbr.order_dispatched is not null
			and kbr.order_delivered is null
			and kbr.order_returned is null
			and kbr.order_ofd is null
			and kam.is_primary = 1
	`
}

// fetchOFDFor1Days fetches orders 1 day past promised delivery date
func fetchOFDFor1Days(query string, db *sqlRepo.Repository) ([]DispatchOrderEvent, error) {
	query += `
		AND kam.pdd > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 2 DAY)) * 1000
		AND kam.pdd < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY)) * 1000
	`

	var dispatchedOrders []DispatchOrderEvent
	_, err := db.CustomQuery(&dispatchedOrders, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching 1-day past PDD orders: %w", err)
	}

	return dispatchedOrders, nil
}

// fetchOFDFor2Days fetches orders 2+ days past promised delivery date
func fetchOFDFor2Days(query string, db *sqlRepo.Repository) ([]DispatchOrderEvent, error) {
	query += `
		AND kam.pdd < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 2 DAY)) * 1000
	`

	var dispatchedOrders []DispatchOrderEvent
	_, err := db.CustomQuery(&dispatchedOrders, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching 2+ days past PDD orders: %w", err)
	}

	return dispatchedOrders, nil
}

// fetchEventsFor10Days fetches orders dispatched 10+ days ago
func fetchEventsFor10Days(query string, db *sqlRepo.Repository) ([]DispatchOrderEvent, error) {
	query += `
		AND kbr.order_dispatched <= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 10 DAY)) * 1000
	`

	var dispatchedOrders []DispatchOrderEvent
	_, err := db.CustomQuery(&dispatchedOrders, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching 10+ day dispatched orders: %w", err)
	}

	return dispatchedOrders, nil
}

// fetchEventsFor5Days fetches orders dispatched between 5-10 days ago
func fetchEventsFor5Days(query string, db *sqlRepo.Repository) ([]DispatchOrderEvent, error) {
	query += `
		AND kbr.order_dispatched >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 10 DAY)) * 1000
		AND kbr.order_dispatched <= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 DAY)) * 1000
	`

	var dispatchedOrders []DispatchOrderEvent
	_, err := db.CustomQuery(&dispatchedOrders, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching 5-10 day dispatched orders: %w", err)
	}

	return dispatchedOrders, nil
}
