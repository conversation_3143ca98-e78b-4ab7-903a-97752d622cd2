package queue

import (
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// WaybillItem represents a waybill entry in the queue
type WaybillItem struct {
	ID          string     `json:"id" gorm:"primaryKey"`
	WaybillNo   string     `json:"waybill_no" gorm:"not null"`
	Courier     string     `json:"courier" gorm:"not null"`
	Status      string     `json:"status" gorm:"default:'pending'"` // pending, processing, completed
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"-"`
	ProcessedAt *time.Time `json:"processed_at,omitempty"`
}

func (*WaybillItem) TableName() string {
	return "kiranabazar_awb_sync_queue"
}

// MemoryQueue represents the in-memory queue
type MemoryQueue struct {
	items []*WaybillItem
	mutex sync.RWMutex
}

// NewMemoryQueue creates a new in-memory queue
func newMemoryQueue() *MemoryQueue {
	return &MemoryQueue{
		items: make([]*WaybillItem, 0),
	}
}

// Enqueue adds an item to the memory queue
func (mq *MemoryQueue) enqueue(item *WaybillItem) {
	mq.mutex.Lock()
	defer mq.mutex.Unlock()
	mq.items = append(mq.items, item)
}

// dequeue removes and returns items from the memory queue based on conditions
func (mq *MemoryQueue) dequeue() []*WaybillItem {
	mq.mutex.Lock()
	defer mq.mutex.Unlock()

	if len(mq.items) == 0 {
		return nil
	}

	var itemsToReturn []*WaybillItem
	fiveMinutesAgo := time.Now().Add(-5 * time.Minute)

	// Find items that are 5 minutes or older
	eligibleCount := 0
	for _, item := range mq.items {
		if item.CreatedAt.Before(fiveMinutesAgo) || item.CreatedAt.Equal(fiveMinutesAgo) {
			eligibleCount++
			if eligibleCount > 20 {
				break
			}
		} else {
			// Since items are ordered by creation time, we can break here
			break
		}
	}

	// If no items are eligible, return nil
	if eligibleCount == 0 {
		return nil
	}

	// Determine how many items to return (max 20, or all eligible if less than 20)
	itemsToTake := min(eligibleCount, 20)

	// Get the items to return
	itemsToReturn = make([]*WaybillItem, itemsToTake)
	copy(itemsToReturn, mq.items[:itemsToTake])

	// Remove the taken items from the queue
	mq.items = mq.items[itemsToTake:]

	return itemsToReturn
}

// GetLength returns the current length of the queue
func (mq *MemoryQueue) GetLength() int {
	mq.mutex.RLock()
	defer mq.mutex.RUnlock()
	return len(mq.items)
}

// GetOldestTime returns the creation time of the oldest item
func (mq *MemoryQueue) GetOldestTime() *time.Time {
	mq.mutex.RLock()
	defer mq.mutex.RUnlock()

	if len(mq.items) == 0 {
		return nil
	}
	return &mq.items[0].CreatedAt
}

// GetAllItems returns a copy of all items in the queue (for status/debug)
func (mq *MemoryQueue) GetAllItems() []*WaybillItem {
	mq.mutex.RLock()
	defer mq.mutex.RUnlock()

	items := make([]*WaybillItem, len(mq.items))
	copy(items, mq.items)
	return items
}

// QueueManager manages the waybill queue with in-memory queue and SQL persistence
type QueueManager struct {
	db            *gorm.DB
	memoryQueue   *MemoryQueue
	processingMap map[string]*WaybillItem // Track processing items by waybill_no
	mutex         sync.RWMutex
}

// NewQueueManager creates a new queue manager
func NewAWBSyncQueueManager(db *gorm.DB, env string) *QueueManager {
	qm := &QueueManager{
		db:            db,
		memoryQueue:   newMemoryQueue(),
		processingMap: make(map[string]*WaybillItem),
	}

	if env == "prod" {
		// Load pending items from database on startup (recovery)
		go qm.loadPendingItems()
	}

	return qm
}

// loadPendingItems loads pending items from database into memory queue (for recovery)
func (qm *QueueManager) loadPendingItems() {
	// Load pending items
	var items []WaybillItem
	qm.db.Where("status = ?", "pending").Order("created_at asc").Find(&items)

	for _, item := range items {
		itemCopy := item
		qm.memoryQueue.enqueue(&itemCopy)
	}

	// Also load processing items to the processing map
	var processingItems []WaybillItem
	qm.db.Where("status = ?", "processing").Find(&processingItems)

	qm.mutex.Lock()
	for _, item := range processingItems {
		itemCopy := item
		itemCopy.Status = "pending"
		qm.processingMap[fmt.Sprintf("%s-%s", item.WaybillNo, item.ID)] = &itemCopy
		qm.memoryQueue.enqueue(&itemCopy)
	}
	qm.mutex.Unlock()
	fmt.Printf("Loaded %d pending items and %d processing items from database\n", len(items), len(processingItems))
}

// Enqueue adds a new waybill item to the memory queue and persists to database
func (qm *QueueManager) Enqueue(waybillNo, courier string) error {
	item := &WaybillItem{
		ID:        uuid.New().String(),
		WaybillNo: waybillNo,
		Courier:   courier,
		Status:    "pending",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Save to database for persistence
	if err := qm.db.Create(item).Error; err != nil {
		return fmt.Errorf("failed to save to database: %w", err)
	}

	// Add to memory queue
	qm.memoryQueue.enqueue(item)

	fmt.Printf("Enqueued: %s - %s (Queue length: %d)\n", waybillNo, courier, qm.memoryQueue.GetLength())
	return nil
}

// Dequeue returns items from memory queue based on the specified conditions
func (qm *QueueManager) Dequeue() ([]*WaybillItem, error) {
	// Get items from memory queue
	items := qm.memoryQueue.dequeue()

	if len(items) == 0 {
		return nil, nil
	}

	// Update status in database and add to processing map
	if err := qm.moveToProcessing(items); err != nil {
		// If database update fails, we need to put items back to memory queue
		for _, item := range items {
			qm.memoryQueue.enqueue(item)
		}
		return nil, fmt.Errorf("failed to update items to processing: %w", err)
	}
	return items, nil
}

// CompleteItem marks an item as completed and removes it from processing map and database
func (qm *QueueManager) CompleteItem(item *WaybillItem) error {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()

	// Remove from database
	if err := qm.db.Where("id = ?", item.ID).Delete(&WaybillItem{}).Error; err != nil {
		return fmt.Errorf("failed to delete from database: %w", err)
	}

	// Remove from processing map
	delete(qm.processingMap, fmt.Sprintf("%s-%s", item.WaybillNo, item.ID))
	return nil
}

// CompleteItems marks multiple items as completed (for bulk operations)
func (qm *QueueManager) CompleteItems(waybillItems []*WaybillItem) error {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()

	// Start transaction
	tx := qm.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, waybillItem := range waybillItems {
		// Remove from database
		if err := tx.Where("id = ?", waybillItem.ID).Delete(&WaybillItem{}).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to delete %s from database: %w", waybillItem.WaybillNo, err)
		}

		// Remove from processing map
		delete(qm.processingMap, fmt.Sprintf("%s-%s", waybillItem.WaybillNo, waybillItem.ID))
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	return nil
}

// GetQueueStatus returns current queue information
func (qm *QueueManager) GetQueueStatus() map[string]interface{} {
	qm.mutex.RLock()
	defer qm.mutex.RUnlock()

	return map[string]interface{}{
		"pending_count":    qm.memoryQueue.GetLength(),
		"processing_count": len(qm.processingMap),
		"oldest_item_time": qm.memoryQueue.GetOldestTime(),
	}
}

// moveToProcessing updates items to processing status and adds to processing map
func (qm *QueueManager) moveToProcessing(items []*WaybillItem) error {
	tx := qm.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	now := time.Now()
	qm.mutex.Lock()
	defer qm.mutex.Unlock()

	for _, item := range items {
		item.Status = "processing"
		item.ProcessedAt = &now
		item.UpdatedAt = now
	}
	if err := tx.Save(items).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update items to processing: %w", err)
	}
	for _, item := range items {
		qm.processingMap[fmt.Sprintf("%s-%s", item.WaybillNo, item.ID)] = item
	}

	return tx.Commit().Error
}
