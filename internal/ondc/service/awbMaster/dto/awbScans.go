package dto

import "time"

// AWBScan represents the scan data for an AWB
type AWBScan struct {
	AWBNumber                string    `json:"awb_number"`
	CourierStatus            string    `json:"courier_status"`
	CourierStatusCode        string    `json:"courier_status_code"`
	CourierStatusType        string    `json:"courier_status_type"`
	CourierStatusDescription string    `json:"courier_status_description"`
	CourierName              string    `json:"courier_name"`
	StatusLocation           string    `json:"status_location"`
	ScanTimestamp            time.Time `json:"scan_timestamp"`
	InternalStatusCode       string    `json:"internal_status_code"`
}

type AwbCourier struct {
	AWBNumber string `json:"awb_number"`
	Courier   string `json:"courier"`
}
