package dto

import "time"

// CreateAWBRequest represents the request to create a new AWB
type CreateAWBRequest struct {
	AWBNumber                *string    `json:"awb_number"`
	ReferenceID              *string    `json:"reference_id"`
	OrderID                  *uint64    `json:"order_id"`
	Courier                  *string    `json:"courier"`
	Status                   *string    `json:"status"`
	ManifestedAt             *int64     `json:"manifested_at"`
	DispatchedAt             *int64     `json:"dispatched_at"`
	DeliveredAt              *int64     `json:"delivered_at"`
	ReturnedAt               *int64     `json:"returned_at"`
	RTODeliveredAt           *int64     `json:"rto_delivered_at"`
	FirstOFDAt               *int64     `json:"first_ofd_at"`
	PDD                      *int64     `json:"pdd"`
	EDD                      *int64     `json:"edd"`
	IsMaster                 *bool      `json:"is_master"`
	IsPrimary                *bool      `json:"is_primary"`
	IsActive                 *bool      `json:"is_active"`
	CreatedAt                *int64     `json:"created_at"`
	UpdatedAt                *int64     `json:"updated_at"`
	LastStatusUpdatedAt      *int64     `json:"last_status_updated_at"`
	IsMutable                *bool      `json:"is_mutable"`
	UpdatedBy                *string    `json:"updated_by"`
	IsMps                    *bool      `json:"is_mps"`
	StatusType               *string    `json:"status_type"`
	NSLCode                  *string    `json:"nsl_code"`
	AccountName              *string    `json:"account_name"`
	Instructions             *string    `json:"instructions"`
	ReachedDestinationAt     *int64     `json:"reached_destination_at"`
	ExpectedReturnAt         *int64     `json:"expected_return_at"`
	ReturnPromisedDeliveryAt *int64     `json:"return_promised_delivery_at"`
	OpenAPISyncTs            *time.Time `json:"open_api_sync_ts"`
	StatusCode               *string    `json:"status_code"`
}

// UpdateAWBStatusRequest represents the request to update an AWB status
type UpdateAWBStatusRequest struct {
	AWBNumber  string `json:"awb_number"`
	Status     string `json:"status"`
	StatusType string `json:"status_type,omitempty"`
	StatusTime int64  `json:"status_time,omitempty"`
	UpdatedBy  string `json:"updated_by,omitempty"`
}

// GetPrimaryAWBResponse represents the response for getting a primary AWB
type GetPrimaryAWBResponse struct {
	AWBNumber *string `json:"awb_number"`
	OrderID   *uint64 `json:"order_id"`
	Status    *string `json:"status"`
	Courier   *string `json:"courier"`
}

// GetOrderIDResponse represents the response when retrieving order IDs
type GetOrderIDResponse struct {
	AWBNumber string `json:"awb_number"`
	OrderID   uint64 `json:"order_id"`
}

// UpdatePrimaryRequest represents the request to update a primary AWB
type UpdatePrimaryRequest struct {
	AWBNumber string `json:"awb_number"`
	UpdatedBy string `json:"updated_by"`
}

type PrimaryWayBill struct {
	AWBNumber *string `json:"awb_number"`
	OrderID   *int64  `json:"order_id"`
}

type Scan struct {
	Name         string `json:"name"`
	Type         string `json:"type"`
	Code         string `json:"code"`
	Time         string `json:"time"`
	Instructions string `json:"instructions"`
	Location     string `json:"location"`
}
type AWBDetailsData struct {
	Scans                 []Scan `json:"scan"`
	Origin                string `json:"origin"`
	Destination           string `json:"destination"`
	SenderName            string `json:"sender_name"`
	ReferenceID           string `json:"reference_id"`
	PromisedDeliveryDate  string `json:"promised_delivery_date"`
	EstimatedDeliveryDate string `json:"estimated_delivery_date"`
}

// This represent the response for the get waybill full details, this will have open api response from delhivery, ekart, shiprocket....etc
type ShipmentData struct {
	AWB                        string  `json:"AWB"`
	Scans                      any     `json:"Scans"`
	Extras                     string  `json:"Extras"`
	Origin                     string  `json:"Origin"`
	Status                     any     `json:"Status"`
	Ewaybill                   any     `json:"Ewaybill"`
	Quantity                   string  `json:"Quantity"`
	CODAmount                  float64 `json:"CODAmount"`
	Consignee                  any     `json:"Consignee"`
	OrderType                  string  `json:"OrderType"`
	PickUpDate                 string  `json:"PickUpDate"`
	SenderName                 string  `json:"SenderName"`
	Destination                string  `json:"Destination"`
	ReferenceNo                string  `json:"ReferenceNo"`
	DeliveryDate               any     `json:"DeliveryDate"`
	PickedupDate               string  `json:"PickedupDate"`
	ReturnedDate               any     `json:"ReturnedDate"`
	ChargedWeight              any     `json:"ChargedWeight"`
	DispatchCount              int     `json:"DispatchCount"`
	InvoiceAmount              float64 `json:"InvoiceAmount"`
	PickupLocation             string  `json:"PickupLocation"`
	RTOStartedDate             string  `json:"RTOStartedDate"`
	DestRecieveDate            string  `json:"DestRecieveDate"`
	FirstAttemptDate           string  `json:"FirstAttemptDate"`
	ReverseInTransit           bool    `json:"ReverseInTransit"`
	OriginRecieveDate          string  `json:"OriginRecieveDate"`
	ExpectedReturnDate         string  `json:"ExpectedReturnDate"`
	OutDestinationDate         string  `json:"OutDestinationDate"`
	PromisedDeliveryDate       string  `json:"PromisedDeliveryDate"`
	EstimatedDeliveryDate      string  `json:"EstimatedDeliveryDate"`
	ReturnPromisedDeliveryDate string  `json:"ReturnPromisedDeliveryDate"`
	RTO                        bool    `json:"RTO"`
	IsMPS                      bool    `json:"IsMPS"`
	Vendor                     string  `json:"Vendor"`
	Weight                     string  `json:"Weight"`
	OrderID                    string  `json:"OrderID"`
	Delivered                  bool    `json:"Delivered"`
	DeliveryType               string  `json:"DeliveryType"`
	ExternalTrackingID         string  `json:"ExternalTrackingID"`
	ExpectedDeliveryDate       string  `json:"ExpectedDeliveryDate"`
	ReachedDestinationDate     string  `json:"ReachedDestinationDate"`
}
