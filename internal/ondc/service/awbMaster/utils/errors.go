package utils

import (
	"errors"
	"fmt"
)

// Common errors
var (
	ErrAWBNotFound           = errors.New("awb not found")
	ErrOrderIDNotFound       = errors.New("order id not found")
	ErrInvalidAWBNumber      = errors.New("invalid awb number")
	ErrInvalidUser           = errors.New("invalid user")
	ErrInvalidOrderID        = errors.New("invalid order id")
	ErrDuplicateAWB          = errors.New("duplicate awb entry")
	ErrNoPrimaryAWB          = errors.New("no primary awb found for order")
	ErrDatabaseOperation     = errors.New("database operation failed")
	ErrCacheOperation        = errors.New("cache operation failed")
	ErrServiceNotInitialized = errors.New("awb master service not initialized")
	ErrMoreOrderIDs          = errors.New("more than 10 orderids not supported")
)

// AWBError represents an error with AWB context
type AWBError struct {
	AWBNumber string
	Err       error
}

// Error returns the error message
func (e *AWBError) Error() string {
	return fmt.Sprintf("awb error for %s: %v", e.AWBNumber, e.Err)
}

// Unwrap returns the wrapped error
func (e *AWBError) Unwrap() error {
	return e.Err
}

// NewAWBError creates a new AWB error
func NewAWBError(awbNumber string, err error) *AWBError {
	return &AWBError{
		AWBNumber: awbNumber,
		Err:       err,
	}
}

// OrderError represents an error with order ID context
type OrderError struct {
	OrderID uint64
	Err     error
}

// Error returns the error message
func (e *OrderError) Error() string {
	return fmt.Sprintf("order error for %d: %v", e.OrderID, e.Err)
}

// Unwrap returns the wrapped error
func (e *OrderError) Unwrap() error {
	return e.Err
}

// NewOrderError creates a new order error
func NewOrderError(orderID uint64, err error) *OrderError {
	return &OrderError{
		OrderID: orderID,
		Err:     err,
	}
}
