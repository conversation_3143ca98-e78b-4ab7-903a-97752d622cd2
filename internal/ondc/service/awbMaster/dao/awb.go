package dao

import (
	"time"

	"gorm.io/datatypes"
)

// AWB represents an Air Waybill in the system
type AWB struct {
	AWBNumber                *string    `json:"awb_number"`
	ReferenceID              *string    `json:"reference_id"`
	OrderID                  *uint64    `json:"order_id"`
	Courier                  *string    `json:"courier"`
	Status                   *string    `json:"status"`
	ManifestedAt             *int64     `json:"manifested_at"`
	DispatchedAt             *int64     `json:"dispatched_at"`
	DeliveredAt              *int64     `json:"delivered_at"`
	ReturnedAt               *int64     `json:"returned_at"`
	RTODeliveredAt           *int64     `json:"rto_delivered_at"`
	FirstOFDAt               *int64     `json:"first_ofd_at"`
	PDD                      *int64     `json:"pdd" gorm:"column:pdd"`
	EDD                      *int64     `json:"edd" gorm:"edd"`
	IsMaster                 *bool      `json:"is_master"`
	IsPrimary                *bool      `json:"is_primary"`
	IsActive                 *bool      `json:"is_active"`
	CreatedAt                *int64     `json:"created_at"`
	UpdatedAt                *int64     `json:"updated_at"`
	LastStatusUpdatedAt      *int64     `json:"last_status_updated_at"`
	IsMutable                *bool      `json:"is_mutable"`
	UpdatedBy                *string    `json:"updated_by"`
	IsMps                    *bool      `json:"is_mps"`
	StatusType               *string    `json:"status_type"`
	NSLCode                  *string    `json:"nsl_code"`
	AccountName              *string    `json:"account_name"`
	Instructions             *string    `json:"instructions"`
	ReachedDestinationAt     *int64     `json:"reached_destination_at"`
	ExpectedReturnAt         *int64     `json:"expected_return_at"`
	ReturnPromisedDeliveryAt *int64     `json:"return_promised_delivery_at"`
	StatusCode               *string    `json:"status_code"`
	OpenAPISyncTS            *time.Time `json:"open_api_sync_ts"`
}

type AWBDetails struct {
	AWBNumber string         `json:"awb_number"`
	Details   datatypes.JSON `json:"details"`
	UpdatedAt time.Time      `json:"updated_at"`
}

// AWBStatus represents the possible statuses of an AWB
type AWBStatus string

const (
	StatusCreated        AWBStatus = "CREATED"
	StatusManifested     AWBStatus = "MANIFESTED"
	StatusIntransit      AWBStatus = "INTRANSIT"
	StatusOutForDelivery AWBStatus = "OUT_FOR_DELIVERY"
	StatusDelivered      AWBStatus = "DELIVERED"
	StatusReturned       AWBStatus = "RETURNED"
	StatusRTODelivered   AWBStatus = "RTO_DELIVERED"
	StatusCancelled      AWBStatus = "CANCELLED"
)

// TableName returns the database table name for the AWB model
func (AWB) TableName() string {
	return "kiranabazar_awb_master"
}

func (*AWBDetails) TableName() string {
	return "kiranabazar_awb_details"
}
