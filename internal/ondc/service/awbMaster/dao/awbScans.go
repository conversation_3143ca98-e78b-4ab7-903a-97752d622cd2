package dao

import "time"

// KiranaBazarAWBScan represents the scan data for an AWB
type KiranaBazarAWBScan struct {
	ID                       uint64    `gorm:"column:id;primaryKey;autoIncrement"`
	AWBNumber                string    `gorm:"column:awb_number;not null"`
	CourierStatus            string    `gorm:"column:courier_status;not null"`
	CourierStatusCode        string    `gorm:"column:courier_status_code"`
	CourierStatusType        string    `gorm:"column:courier_status_type"`
	CourierStatusDescription string    `gorm:"column:courier_status_description"`
	CourierName              string    `gorm:"column:courier_name"`
	StatusLocation           string    `gorm:"column:status_location"`
	ScanTimestamp            time.Time `gorm:"column:scan_timestamp;not null"`
	InternalStatusCode       string    `gorm:"column:internal_status_code"`
	Status                   string    `gorm:"column:status;not null"` // Fixed the tag to use gorm instead of json
}

// TableName returns the database table name for the KiranaBazarAWBScan model
func (KiranaBazarAWBScan) TableName() string {
	return "kiranabazar_awb_scans"
}
