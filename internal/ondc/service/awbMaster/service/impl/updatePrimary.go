package impl

import (
	"context"
	"fmt"
	"strings"
	"time"

	"kc/internal/ondc/service/awbMaster/dao"
	"kc/internal/ondc/service/awbMaster/dto"
	"kc/internal/ondc/service/awbMaster/utils"
)

// UpdatePrimaryWaybill sets the specified waybill as primary for an order
// and marks all other waybills for the same order as non-primary.
func (s *AWBMasterServiceImpl) UpdatePrimaryWaybill(ctx context.Context, request dto.UpdatePrimaryRequest) error {
	// Input validation
	if err := validateUpdatePrimaryRequest(request); err != nil {
		return err
	}

	// Create context with timeout
	ctx, cancel := s.getContextWithTimeout(ctx, 10)
	defer cancel()

	// Determine mutability based on user email domain
	isMutable := !strings.Contains(request.UpdatedBy, "kirana.club")

	// Get waybill details to find order ID
	awbD, err := s.GetWaybillDetails(ctx, request.AWBNumber, 0)
	if err != nil {
		return fmt.Errorf("failed to get waybill details: %w", err)
	}
	awbData := awbD[0]

	if awbData.OrderID == nil {
		return fmt.Errorf("waybill %s is not associated with any order", request.AWBNumber)
	}

	// Begin transaction
	tx := s.repository.Db.Begin()
	if tx.Error != nil {
		return utils.NewAWBError(request.AWBNumber, fmt.Errorf("failed to begin transaction: %w", tx.Error))
	}

	// Use defer with named return value to handle transaction completion
	var txErr error
	defer func() {
		// Recover from panic
		if r := recover(); r != nil {
			tx.Rollback()
			err = fmt.Errorf("panic occurred during transaction: %v", r)
		} else if err != nil || txErr != nil {
			tx.Rollback()
		} else {
			if commitErr := tx.Commit().Error; commitErr != nil {
				err = fmt.Errorf("failed to commit transaction: %w", commitErr)
			}
		}
	}()

	updates := map[string]interface{}{
		"is_primary": false,
		"is_mutable": isMutable,
		"updated_by": request.UpdatedBy,
		"updated_at": time.Now().UnixMilli(),
	}

	// Update all waybills for this order to non-primary
	if txErr = tx.Model(&dao.AWB{}).
		Where("order_id = ?", *awbData.OrderID).
		Updates(updates).Error; txErr != nil {
		return fmt.Errorf("failed to update existing waybills: %w", txErr)
	}

	// Set the requested waybill as primary
	updates = map[string]interface{}{
		"is_primary": true,
		"is_mutable": isMutable,
		"updated_by": request.UpdatedBy,
		"updated_at": time.Now().UnixMilli(),
	}

	if txErr = tx.Model(&dao.AWB{}).
		Where("awb_number = ?", request.AWBNumber).
		Updates(updates).Error; txErr != nil {
		return fmt.Errorf("failed to set primary waybill: %w", txErr)
	}
	return nil
}

// validateUpdatePrimaryRequest validates the input request
func validateUpdatePrimaryRequest(request dto.UpdatePrimaryRequest) error {
	if request.AWBNumber == "" {
		return utils.ErrInvalidAWBNumber
	}
	if request.UpdatedBy == "" {
		return utils.ErrInvalidUser
	}
	return nil
}
