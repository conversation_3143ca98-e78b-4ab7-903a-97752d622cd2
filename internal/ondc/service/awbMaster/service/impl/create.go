package impl

import (
	"context"
	"errors"
	"kc/internal/ondc/service/awbMaster/dao"
	"kc/internal/ondc/service/awbMaster/dto"
	"kc/internal/ondc/service/awbMaster/utils"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	// Timestamp format constants
	millisecondsThreshold int64 = 10000000000000      // 13 digits (milliseconds since epoch)
	secondsThreshold      int64 = 10000000000         // 10 digits (seconds since epoch)
	microsecondsThreshold int64 = 1000000000000000    // 16 digits (microseconds since epoch)
	nanosecondsThreshold  int64 = 1000000000000000000 // 19 digits (nanoseconds since epoch)

	// Timeout constants
	defaultTimeoutSeconds = 10
)

// TimestampField represents a field that contains a timestamp
type TimestampField struct {
	Name  string
	Value *int64
}

// Create handles the creation or update of an AWB record and returns the final AWB record
func (s *AWBMasterServiceImpl) Create(ctx context.Context, request dto.CreateAWBRequest) (*dao.AWB, error) {
	// Set timeout for the operation
	ctx, cancel := s.getContextWithTimeout(ctx, defaultTimeoutSeconds)
	defer cancel()

	// Validate request
	if err := validateCreateRequest(request); err != nil {
		return nil, err
	}

	// Normalize all timestamps to milliseconds
	normalizeRequestTimestamps(&request)

	// Begin transaction
	tx := s.repository.Db.Begin()
	if tx.Error != nil {
		return nil, utils.NewAWBError(getStringValue(request.AWBNumber), tx.Error)
	}

	// Use defer with named return values to handle transaction based on success/failure
	var finalAWB *dao.AWB
	var err error
	defer func() {
		// Recover from panic
		if r := recover(); r != nil {
			tx.Rollback()
			err = utils.NewAWBError(getStringValue(request.AWBNumber), errors.New("panic occurred during transaction"))
			finalAWB = nil
		} else if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	// Process the AWB creation or update
	finalAWB, err = processAWBUpsert(tx, request)
	if err != nil {
		return nil, err
	}

	// // Double check that we have the most up-to-date record
	// verifiedAWB, err := fetchLatestAWBRecord(tx, getStringValue(request.AWBNumber))
	// if err != nil {
	// 	return nil, err
	// }

	return finalAWB, nil
}

// validateCreateRequest validates the AWB create request
func validateCreateRequest(request dto.CreateAWBRequest) error {
	// Check required fields
	if request.AWBNumber == nil || *request.AWBNumber == "" {
		return utils.ErrInvalidAWBNumber
	}

	if request.OrderID == nil || *request.OrderID == 0 {
		return utils.ErrInvalidOrderID
	}

	// Additional validations can be added here
	// For example, checking for valid courier values, status values, etc.

	return nil
}

// normalizeRequestTimestamps normalizes all timestamps in the request to milliseconds
func normalizeRequestTimestamps(request *dto.CreateAWBRequest) {
	timestampFields := []TimestampField{
		{Name: "ManifestedAt", Value: request.ManifestedAt},
		{Name: "ReachedDestinationAt", Value: request.ReachedDestinationAt},
		{Name: "DispatchedAt", Value: request.DispatchedAt},
		{Name: "DeliveredAt", Value: request.DeliveredAt},
		{Name: "ReturnedAt", Value: request.ReturnedAt},
		{Name: "RTODeliveredAt", Value: request.RTODeliveredAt},
		{Name: "FirstOFDAt", Value: request.FirstOFDAt},
		{Name: "PDD", Value: request.PDD},
		{Name: "EDD", Value: request.EDD},
		{Name: "ExpectedReturnAt", Value: request.ExpectedReturnAt},
		{Name: "ReturnPromisedDeliveryAt", Value: request.ReturnPromisedDeliveryAt},
		{Name: "CreatedAt", Value: request.CreatedAt},
		{Name: "UpdatedAt", Value: request.UpdatedAt},
		{Name: "LastStatusUpdatedAt", Value: request.LastStatusUpdatedAt},
	}

	for _, field := range timestampFields {
		if field.Value != nil {
			*field.Value = normalizeTimestampToMilliseconds(*field.Value)
		}
	}
}

// normalizeTimestampToMilliseconds converts any timestamp format to milliseconds
func normalizeTimestampToMilliseconds(timestamp int64) int64 {
	// Don't process negative values or zero
	if timestamp <= 0 {
		return timestamp
	}

	// Convert based on apparent magnitude
	switch {
	case timestamp < secondsThreshold:
		// Likely seconds, convert to milliseconds
		return timestamp * 1000
	case timestamp < millisecondsThreshold:
		// Already in milliseconds
		return timestamp
	case timestamp < microsecondsThreshold:
		// Microseconds, convert to milliseconds
		return timestamp / 1000
	case timestamp < nanosecondsThreshold:
		// Nanoseconds, convert to milliseconds
		return timestamp / 1000000
	default:
		// Value is too large to be a reasonable timestamp, return as is
		return timestamp
	}
}

// processAWBUpsert handles creating or updating an AWB record
func processAWBUpsert(tx *gorm.DB, request dto.CreateAWBRequest) (*dao.AWB, error) {
	// Get current time in milliseconds
	now := time.Now().UnixMilli()

	// Check if the AWB exists
	existingAWB := &dao.AWB{}
	result := tx.Where("awb_number = ?", *request.AWBNumber).First(existingAWB)
	if result.Error != nil {
		if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// Database error occurred
			return nil, utils.NewAWBError(getStringValue(request.AWBNumber), result.Error)
		}

		// Record not found, create new record
		return createNewAWB(tx, request, now)
	}

	// Record exists, update it
	return updateExistingAWB(tx, request, existingAWB, now)
}

// createNewAWB creates a new AWB record
func createNewAWB(tx *gorm.DB, request dto.CreateAWBRequest, now int64) (*dao.AWB, error) {
	// Set creation and update timestamps
	if request.CreatedAt == nil {
		request.CreatedAt = &now
	}
	request.UpdatedAt = &now

	// Create AWB object
	awb, err := createAWBMasterObject(request)
	if err != nil {
		return nil, utils.NewAWBError(getStringValue(request.AWBNumber), err)
	}

	// Create record with ON CONFLICT handling
	err = tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "awb_number"}},
		DoNothing: true,
	}).Create(awb).Error

	if err != nil {
		return nil, utils.NewAWBError(getStringValue(request.AWBNumber), err)
	}

	return awb, nil
}

// updateExistingAWB updates an existing AWB record
func updateExistingAWB(tx *gorm.DB, request dto.CreateAWBRequest, existingAWB *dao.AWB, now int64) (*dao.AWB, error) {
	// Preserve created_at timestamp
	if request.CreatedAt == nil {
		request.CreatedAt = existingAWB.CreatedAt
	}

	// Set updated_at timestamp
	request.UpdatedAt = &now

	// Build update map with only non-nil fields
	updates := buildFieldUpdatesMap(request, existingAWB)

	// Only perform update if there are changes
	if len(updates) > 0 {
		err := tx.Model(&dao.AWB{}).Where("awb_number = ?", *request.AWBNumber).Updates(updates).Error
		if err != nil {
			return nil, utils.NewAWBError(getStringValue(request.AWBNumber), err)
		}
	}

	// Create updated object with combined fields
	return combineAWBFields(existingAWB, request), nil
}

// buildFieldUpdatesMap creates a map of field updates for non-nil request fields
func buildFieldUpdatesMap(request dto.CreateAWBRequest, existingAWB *dao.AWB) map[string]interface{} {
	updates := map[string]interface{}{}

	// Update only non-nil fields
	if request.ReferenceID != nil {
		updates["reference_id"] = request.ReferenceID
	}
	if request.OrderID != nil {
		updates["order_id"] = request.OrderID
	}
	if request.Courier != nil {
		updates["courier"] = request.Courier
	}
	if request.Status != nil && (request.LastStatusUpdatedAt != nil && existingAWB.LastStatusUpdatedAt != nil && *existingAWB.LastStatusUpdatedAt <= *request.LastStatusUpdatedAt) {
		updates["status"] = request.Status
	}
	if request.ManifestedAt != nil {
		updates["manifested_at"] = request.ManifestedAt
	}
	if request.ReachedDestinationAt != nil {
		updates["reached_destination_at"] = request.ReachedDestinationAt
	}
	if request.DispatchedAt != nil {
		updates["dispatched_at"] = request.DispatchedAt
	}
	if request.DeliveredAt != nil {
		updates["delivered_at"] = request.DeliveredAt
	}
	if request.ReturnedAt != nil {
		updates["returned_at"] = request.ReturnedAt
	}
	if request.RTODeliveredAt != nil {
		updates["rto_delivered_at"] = request.RTODeliveredAt
	}
	if request.FirstOFDAt != nil {
		updates["first_ofd_at"] = request.FirstOFDAt
	}
	if request.PDD != nil {
		updates["pdd"] = request.PDD
	}
	if request.EDD != nil {
		updates["edd"] = request.EDD
	}
	if request.ExpectedReturnAt != nil {
		updates["expected_return_at"] = request.ExpectedReturnAt
	}
	if request.ReturnPromisedDeliveryAt != nil {
		updates["return_promised_delivery_at"] = request.ReturnPromisedDeliveryAt
	}
	if request.IsMaster != nil {
		updates["is_master"] = request.IsMaster
	}
	if request.IsPrimary != nil {
		updates["is_primary"] = request.IsPrimary
	}
	if request.IsActive != nil {
		updates["is_active"] = request.IsActive
	}
	if request.LastStatusUpdatedAt != nil && (request.LastStatusUpdatedAt != nil && existingAWB.LastStatusUpdatedAt != nil && *existingAWB.LastStatusUpdatedAt <= *request.LastStatusUpdatedAt) {
		updates["last_status_updated_at"] = request.LastStatusUpdatedAt
	}
	if request.IsMutable != nil {
		updates["is_mutable"] = request.IsMutable
	}
	if request.UpdatedBy != nil {
		updates["updated_by"] = request.UpdatedBy
	}
	if request.IsMps != nil {
		updates["is_mps"] = request.IsMps
	}
	if request.StatusType != nil && (request.LastStatusUpdatedAt != nil && existingAWB.LastStatusUpdatedAt != nil && *existingAWB.LastStatusUpdatedAt <= *request.LastStatusUpdatedAt) {
		updates["status_type"] = request.StatusType
	}
	if request.NSLCode != nil && (request.LastStatusUpdatedAt != nil && existingAWB.LastStatusUpdatedAt != nil && *existingAWB.LastStatusUpdatedAt <= *request.LastStatusUpdatedAt) {
		updates["nsl_code"] = request.NSLCode
	}
	if request.StatusCode != nil && (request.LastStatusUpdatedAt != nil && existingAWB.LastStatusUpdatedAt != nil && *existingAWB.LastStatusUpdatedAt <= *request.LastStatusUpdatedAt) {
		updates["status_code"] = request.StatusCode
	}
	if request.CreatedAt != nil {
		updates["created_at"] = request.CreatedAt
	}
	if request.UpdatedAt != nil {
		updates["updated_at"] = request.UpdatedAt
	}
	if request.AccountName != nil {
		updates["account_name"] = request.AccountName
	}
	if request.Instructions != nil && (request.LastStatusUpdatedAt != nil && existingAWB.LastStatusUpdatedAt != nil && *existingAWB.LastStatusUpdatedAt <= *request.LastStatusUpdatedAt) {
		updates["instructions"] = request.Instructions
	}
	if request.OpenAPISyncTs != nil {
		updates["open_api_sync_ts"] = request.OpenAPISyncTs
	}

	return updates
}

// combineAWBFields creates a new AWB by combining existing and request fields
func combineAWBFields(existing *dao.AWB, request dto.CreateAWBRequest) *dao.AWB {
	// Start with existing fields
	combined := *existing

	// Override with non-nil fields from request
	if request.ReferenceID != nil {
		combined.ReferenceID = request.ReferenceID
	}
	if request.OrderID != nil {
		combined.OrderID = request.OrderID
	}
	if request.Courier != nil {
		combined.Courier = request.Courier
	}
	if request.Status != nil {
		combined.Status = request.Status
	}
	if request.ManifestedAt != nil {
		combined.ManifestedAt = request.ManifestedAt
	}
	if request.ReachedDestinationAt != nil {
		combined.ReachedDestinationAt = request.ReachedDestinationAt
	}
	if request.DispatchedAt != nil {
		combined.DispatchedAt = request.DispatchedAt
	}
	if request.DeliveredAt != nil {
		combined.DeliveredAt = request.DeliveredAt
	}
	if request.ReturnedAt != nil {
		combined.ReturnedAt = request.ReturnedAt
	}
	if request.RTODeliveredAt != nil {
		combined.RTODeliveredAt = request.RTODeliveredAt
	}
	if request.FirstOFDAt != nil {
		combined.FirstOFDAt = request.FirstOFDAt
	}
	if request.PDD != nil {
		combined.PDD = request.PDD
	}
	if request.EDD != nil {
		combined.EDD = request.EDD
	}
	if request.ExpectedReturnAt != nil {
		combined.ExpectedReturnAt = request.ExpectedReturnAt
	}
	if request.ReturnPromisedDeliveryAt != nil {
		combined.ReturnPromisedDeliveryAt = request.ReturnPromisedDeliveryAt
	}
	if request.IsMaster != nil {
		combined.IsMaster = request.IsMaster
	}
	if request.IsPrimary != nil {
		combined.IsPrimary = request.IsPrimary
	}
	if request.IsActive != nil {
		combined.IsActive = request.IsActive
	}
	if request.LastStatusUpdatedAt != nil {
		combined.LastStatusUpdatedAt = request.LastStatusUpdatedAt
	}
	if request.IsMutable != nil {
		combined.IsMutable = request.IsMutable
	}
	if request.UpdatedBy != nil {
		combined.UpdatedBy = request.UpdatedBy
	}
	if request.IsMps != nil {
		combined.IsMps = request.IsMps
	}
	if request.StatusType != nil {
		combined.StatusType = request.StatusType
	}
	if request.NSLCode != nil {
		combined.NSLCode = request.NSLCode
	}
	if request.StatusCode != nil {
		combined.StatusCode = request.StatusCode
	}
	if request.CreatedAt != nil {
		combined.CreatedAt = request.CreatedAt
	}
	if request.UpdatedAt != nil {
		combined.UpdatedAt = request.UpdatedAt
	}
	if request.AccountName != nil {
		combined.AccountName = request.AccountName
	}
	if request.Instructions != nil {
		combined.Instructions = request.Instructions
	}

	return &combined
}

// fetchLatestAWBRecord fetches the most up-to-date AWB record from the database
func fetchLatestAWBRecord(tx *gorm.DB, awbNumber string) (*dao.AWB, error) {
	awb := &dao.AWB{}
	err := tx.Where("awb_number = ?", awbNumber).First(awb).Error
	if err != nil {
		return nil, utils.NewAWBError(awbNumber, err)
	}
	return awb, nil
}

// getStringValue safely returns string value from a string pointer
func getStringValue(strPtr *string) string {
	if strPtr == nil {
		return ""
	}
	return *strPtr
}

// logAWBOperation logs AWB operations (for debugging and audit purposes)
func logAWBOperation(ctx context.Context, operation string, awbNumber string, err error) {
	// Implement logging as per your application's logging strategy
	// This is a placeholder for adding logging functionality
}

func createAWBMasterObject(req dto.CreateAWBRequest) (*dao.AWB, error) {
	return &dao.AWB{
		AWBNumber:                req.AWBNumber,
		ReferenceID:              req.ReferenceID,
		OrderID:                  req.OrderID,
		Courier:                  req.Courier,
		Status:                   req.Status,
		ManifestedAt:             req.ManifestedAt,
		ReachedDestinationAt:     req.ReachedDestinationAt,
		DispatchedAt:             req.DispatchedAt,
		DeliveredAt:              req.DeliveredAt,
		ReturnedAt:               req.ReturnedAt,
		RTODeliveredAt:           req.RTODeliveredAt,
		FirstOFDAt:               req.FirstOFDAt,
		PDD:                      req.PDD,
		EDD:                      req.EDD,
		ExpectedReturnAt:         req.ExpectedReturnAt,
		ReturnPromisedDeliveryAt: req.ReturnPromisedDeliveryAt,
		IsMaster:                 req.IsMaster,
		IsPrimary:                req.IsPrimary,
		IsActive:                 req.IsActive,
		CreatedAt:                req.CreatedAt,
		UpdatedAt:                req.UpdatedAt,
		LastStatusUpdatedAt:      req.LastStatusUpdatedAt,
		IsMutable:                req.IsMutable,
		UpdatedBy:                req.UpdatedBy,
		IsMps:                    req.IsMps,
		StatusType:               req.StatusType,
		NSLCode:                  req.NSLCode,
		StatusCode:               req.StatusCode,
		AccountName:              req.AccountName,
		Instructions:             req.Instructions,
		OpenAPISyncTS:            req.OpenAPISyncTs,
	}, nil

}
