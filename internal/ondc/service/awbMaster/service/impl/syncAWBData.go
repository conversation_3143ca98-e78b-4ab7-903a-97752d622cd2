package impl

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/service"
	"kc/internal/ondc/service/awbMaster/dao"
	"strings"
	"time"

	awbMasterDTO "kc/internal/ondc/service/awbMaster/dto"

	tplModels "github.com/Kirana-Club/3pl-go/pkg/models"

	"gorm.io/gorm/clause"
)

type WaybillAccountSellerMapping struct {
	Seller      string
	AccountName string
	WayBills    []string
	Courier     string
}

type OrderIDAccountSellerMapping struct {
	Seller      string
	AccountName string
	OrderIds    []string
	Courier     string
}

// TODO: @wable this is temporary change this properly
var accountNameDelhiverySellerNameMapping = map[string]string{
	"zoff_foods_heavy":    "zoff_foods-heavy",
	"zoff_foods_small":    "zoff_foods",
	"apsara1_surface":     "apsara_tea",
	"sheelz_surface":      "hugs-heavy",
	"sheelzindia_surface": "hugs",
	"kiranaclub":          "kiranaclub",
}

// SyncWayBillData accepts maximum of 49 waybill numbers and sync the data from the api to the our database
func (s *AWBMasterServiceImpl) SyncWayBillData(ctx context.Context, waybills []string) error {
	awbs := []dao.AWB{}
	if len(waybills) > 49 {
		return errors.New("waybill array cannot be greater than 49")
	}
	result := s.readOnlyRepository.Db.Where("awb_number IN ?", waybills).Find(&awbs)
	if result.Error != nil {
		return result.Error
	}
	err := s.repository.Db.Model(&dao.AWB{}).Where("awb_number IN ?", waybills).Update("open_api_sync_ts", time.Now()).Error
	if err != nil {
		fmt.Println("err = ", err)
		return err
	}
	mp := map[string]WaybillAccountSellerMapping{}
	for _, awb := range awbs {
		if _, exists := mp[*awb.AccountName]; !exists {
			mp[*awb.AccountName] = WaybillAccountSellerMapping{
				Seller:      "seller",
				AccountName: *awb.AccountName,
				WayBills:    []string{},
				Courier:     *awb.Courier,
			}
		}
		temp := mp[*awb.AccountName]
		temp.WayBills = append(temp.WayBills, *awb.AWBNumber)
		mp[*awb.AccountName] = temp
	}

	for key, val := range mp {
		oms := accountNameDelhiverySellerNameMapping[key] // get the exact name from the account name
		if strings.Contains(strings.ToUpper(val.Courier), "DELHIVERY") {
			err := handleDelhiveryAWBSync(ctx, val.WayBills, oms, s, val)
			if err != nil {
				return err
			}
		} else if strings.Contains(strings.ToUpper(val.Courier), "EKART") {
			err := handleEkartAWBSync(ctx, val.WayBills, oms, s, val)
			if err != nil {
				return err
			}
		}

	}
	return nil
}

func handleEkartAWBSync(ctx context.Context, awbs []string, oms string, s *AWBMasterServiceImpl, val WaybillAccountSellerMapping) error {
	session, err := s.TPLService.CreateSession(ctx, "ekart", "kiranaclub")
	if err != nil {
		fmt.Println("err = ", err)
		return err
	}

	// Process AWBs in batches of 20
	batchSize := 20
	for i := 0; i < len(awbs); i += batchSize {
		end := i + batchSize
		if end > len(awbs) {
			end = len(awbs)
		}

		batch := awbs[i:end]

		req := &tplModels.TrackingRequest{
			Waybills: &batch,
		}
		trackingResponses, err := session.TrackShipment(ctx, req)
		if err != nil {
			fmt.Printf("Error processing batch %d-%d: %v\n", i, end-1, err)
			continue // Continue with next batch instead of returning error
		}

		for _, request := range trackingResponses.ShipmentData {
			if request.Shipment.AWB == "" {
				continue
			}
			bt, _ := json.Marshal(request.Shipment)
			// save to awb details
			s.repository.Db.Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "awb_number"}},
				DoUpdates: clause.AssignmentColumns([]string{"details", "updated_at"}),
			}).Create(&dao.AWBDetails{
				AWBNumber: request.Shipment.AWB,
				Details:   bt,
				UpdatedAt: time.Now(),
			})

			orderID := service.ExtractOrderID(request.Shipment.OrderID)
			courier := "Ekart"
			updatedBy := "OPEN_API"
			TRUE := true

			var lastStatusUpdated *int64
			if request.Shipment.Status.StatusDateTime != "" {
				lsu, err := service.ConvertToEpochMillis(request.Shipment.Status.StatusDateTime)
				if err == nil {
					lastStatusUpdated = &lsu
				}
			}
			var pickedUpDate *int64
			if request.Shipment.PickUpDate != "" {
				pud, err := service.ConvertToEpochMillis(request.Shipment.PickUpDate)
				if err == nil {
					pickedUpDate = &pud
				}
			}
			var firstAttemptDate *int64
			if request.Shipment.FirstAttemptDate != "" {
				fad, err := service.ConvertToEpochMillis(request.Shipment.FirstAttemptDate)
				if err == nil {
					firstAttemptDate = &fad
				}
			}

			var deliveryDate *int64
			if request.Shipment.DeliveryDate != nil && request.Shipment.DeliveryDate.(string) != "" {
				dd, err := service.ConvertToEpochMillis(request.Shipment.DeliveryDate.(string))
				if err == nil {
					deliveryDate = &dd
				}
			}

			var returnedDate *int64
			if request.Shipment.ReturnedDate != nil && request.Shipment.ReturnedDate.(string) != "" {
				rd, err := service.ConvertToEpochMillis(request.Shipment.ReturnedDate.(string))
				if err == nil {
					returnedDate = &rd
				}
			}
			var pdd *int64
			if request.Shipment.PromisedDeliveryDate != "" {
				pd, err := service.ConvertToEpochMillis(request.Shipment.PromisedDeliveryDate)
				if err == nil {
					pdd = &pd
				}
			}

			var shipmentManifested *int64
			if len(request.Shipment.Scans) > 0 && request.Shipment.Scans[0].ScanDetail.ScanDateTime != "" {
				sm, err := service.ConvertToEpochMillis(request.Shipment.Scans[0].ScanDetail.ScanDateTime)
				if err == nil {
					shipmentManifested = &sm
				}
			}

			var reachedDestinationDate *int64
			if request.Shipment.DestRecieveDate != "" {
				rdd, err := service.ConvertToEpochMillis(request.Shipment.DestRecieveDate)
				if err == nil {
					reachedDestinationDate = &rdd
				}
			}

			var rTODeliveredAt *int64
			if request.Shipment.ReturnedDate != nil && (request.Shipment.ReturnedDate.(string) != "") {
				rda, err := service.ConvertToEpochMillis(request.Shipment.ReturnedDate.(string))
				if err == nil {
					rTODeliveredAt = &rda
				}
			}

			var expectedReturnAt *int64
			if request.Shipment.ExpectedReturnDate != "" {
				erd, err := service.ConvertToEpochMillis(request.Shipment.ExpectedReturnDate)
				if err == nil {
					expectedReturnAt = &erd
				}
			}

			var returnPromisedDeliveryAt *int64
			if request.Shipment.ReturnPromisedDeliveryDate != "" {
				rpdd, err := service.ConvertToEpochMillis(request.Shipment.ReturnPromisedDeliveryDate)
				if err == nil {
					returnPromisedDeliveryAt = &rpdd
				}
			}

			s.Create(context.Background(), awbMasterDTO.CreateAWBRequest{
				AWBNumber:                &request.Shipment.AWB,
				ReferenceID:              &request.Shipment.ReferenceNo,
				OrderID:                  &orderID,
				Courier:                  &courier,
				Status:                   &request.Shipment.Status.Status,
				IsActive:                 &TRUE,
				LastStatusUpdatedAt:      lastStatusUpdated,
				UpdatedBy:                &updatedBy,
				NSLCode:                  &request.Shipment.Status.StatusCode,
				StatusCode:               &request.Shipment.Status.StatusCode,
				StatusType:               &request.Shipment.Status.StatusType,
				Instructions:             &request.Shipment.Status.Instructions,
				ManifestedAt:             shipmentManifested,
				DispatchedAt:             pickedUpDate,
				DeliveredAt:              deliveryDate,
				ReturnedAt:               returnedDate,
				RTODeliveredAt:           rTODeliveredAt,
				FirstOFDAt:               firstAttemptDate,
				PDD:                      pdd,
				ReachedDestinationAt:     reachedDestinationDate,
				ExpectedReturnAt:         expectedReturnAt,
				ReturnPromisedDeliveryAt: returnPromisedDeliveryAt,
			})
		}
	}

	return nil
}

// handleDelhiveryAWBSync handles the delhivery awb sync
func handleDelhiveryAWBSync(ctx context.Context, awbs []string, oms string, s *AWBMasterServiceImpl, val WaybillAccountSellerMapping) error {
	session, err := s.TPLService.CreateSession(ctx, "delhivery", oms)
	if err != nil {
		return err
	}
	req := &tplModels.TrackingRequest{
		Waybills: &val.WayBills,
		OrderID:  nil,
	}
	trackingResponses, err := session.TrackShipment(ctx, req)
	if err != nil {
		return err
	}

	for _, request := range trackingResponses.ShipmentData {

		// convert to byte
		bt, _ := json.Marshal(request.Shipment)

		// save to awb details
		s.repository.Db.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "awb_number"}},
			DoUpdates: clause.AssignmentColumns([]string{"details", "updated_at"}),
		}).Create(&dao.AWBDetails{
			AWBNumber: request.Shipment.AWB,
			Details:   bt,
			UpdatedAt: time.Now(),
		})

		orderID := service.ExtractOrderID(request.Shipment.ReferenceNo)
		courier := "Delhivery"
		updatedBy := "OPEN_API"
		TRUE := true

		var lastStatusUpdated *int64
		if request.Shipment.Status.StatusDateTime != "" {
			lsu, err := service.ConvertToEpochMillis(request.Shipment.Status.StatusDateTime)
			if err == nil {
				lastStatusUpdated = &lsu
			}
		}
		var pickedUpDate *int64
		if request.Shipment.PickUpDate != "" {
			pud, err := service.ConvertToEpochMillis(request.Shipment.PickedupDate)
			if err == nil {
				pickedUpDate = &pud
			}
		}
		var firstAttemptDate *int64
		if request.Shipment.FirstAttemptDate != "" {
			fad, err := service.ConvertToEpochMillis(request.Shipment.FirstAttemptDate)
			if err == nil {
				firstAttemptDate = &fad
			}
		}

		var deliveryDate *int64
		if request.Shipment.DeliveryDate != nil && request.Shipment.DeliveryDate.(string) != "" {
			dd, err := service.ConvertToEpochMillis(request.Shipment.DeliveryDate.(string))
			if err == nil {
				deliveryDate = &dd
			}
		}

		var returnedDate *int64
		if request.Shipment.ReturnedDate != nil && request.Shipment.ReturnedDate.(string) != "" {
			rd, err := service.ConvertToEpochMillis(request.Shipment.ReturnedDate.(string))
			if err == nil {
				returnedDate = &rd
			}
		}
		var pdd *int64
		if request.Shipment.PromisedDeliveryDate != "" {
			pd, err := service.ConvertToEpochMillis(request.Shipment.PromisedDeliveryDate)
			if err == nil {
				pdd = &pd
			}
		}

		var shipmentManifested *int64
		if request.Shipment.Scans[0].ScanDetail.ScanDateTime != "" {
			sm, err := service.ConvertToEpochMillis(request.Shipment.Scans[0].ScanDetail.ScanDateTime)
			if err == nil {
				shipmentManifested = &sm
			}
		}

		var reachedDestinationDate *int64
		if request.Shipment.DestRecieveDate != "" {
			rdd, err := service.ConvertToEpochMillis(request.Shipment.DestRecieveDate)
			if err == nil {
				reachedDestinationDate = &rdd
			}
		}

		var rTODeliveredAt *int64
		if request.Shipment.ReturnedDate != nil && (request.Shipment.ReturnedDate.(string) != "") {
			rda, err := service.ConvertToEpochMillis(request.Shipment.ReturnedDate.(string))
			if err == nil {
				rTODeliveredAt = &rda
			}
		}

		var expectedReturnAt *int64
		if request.Shipment.ExpectedReturnDate != "" {
			erd, err := service.ConvertToEpochMillis(request.Shipment.ExpectedReturnDate)
			if err == nil {
				expectedReturnAt = &erd
			}
		}

		var returnPromisedDeliveryAt *int64
		if request.Shipment.ReturnPromisedDeliveryDate != "" {
			rpdd, err := service.ConvertToEpochMillis(request.Shipment.ReturnPromisedDeliveryDate)
			if err == nil {
				returnPromisedDeliveryAt = &rpdd
			}
		}

		s.Create(context.Background(), awbMasterDTO.CreateAWBRequest{
			AWBNumber:                &request.Shipment.AWB,
			ReferenceID:              &request.Shipment.ReferenceNo,
			OrderID:                  &orderID,
			Courier:                  &courier,
			Status:                   &request.Shipment.Status.Status,
			IsActive:                 &TRUE,
			LastStatusUpdatedAt:      lastStatusUpdated,
			UpdatedBy:                &updatedBy,
			NSLCode:                  &request.Shipment.Status.StatusCode,
			StatusCode:               &request.Shipment.Status.StatusCode,
			StatusType:               &request.Shipment.Status.StatusType,
			Instructions:             &request.Shipment.Status.Instructions,
			ManifestedAt:             shipmentManifested,
			DispatchedAt:             pickedUpDate,
			DeliveredAt:              deliveryDate,
			ReturnedAt:               returnedDate,
			RTODeliveredAt:           rTODeliveredAt,
			FirstOFDAt:               firstAttemptDate,
			PDD:                      pdd,
			ReachedDestinationAt:     reachedDestinationDate,
			ExpectedReturnAt:         expectedReturnAt,
			ReturnPromisedDeliveryAt: returnPromisedDeliveryAt,
		})
	}
	return nil
}

// SyncOrderData accepts maximum of 10 orderids which are also the reference ids and syncs the data from the api to the internal DB
func (s *AWBMasterServiceImpl) SyncOrderData(ctx context.Context, orderIDs []string) error {
	awbs := []dao.AWB{}
	if len(orderIDs) > 10 {
		return errors.New("waybill array cannot be greater than 49")
	}
	result := s.repository.Db.Where("order_id IN ?", orderIDs).Find(&awbs)
	if result.Error != nil {
		return result.Error
	}

	mp := map[string]OrderIDAccountSellerMapping{}
	for _, awb := range awbs {
		if _, exists := mp[*awb.AccountName]; !exists {
			mp[*awb.AccountName] = OrderIDAccountSellerMapping{
				Seller:      "seller",
				AccountName: *awb.AccountName,
				OrderIds:    []string{},
				Courier:     "Delhivery",
			}
		}
		temp := mp[*awb.AccountName]
		temp.OrderIds = append(temp.OrderIds, *awb.ReferenceID)
		mp[*awb.AccountName] = temp
	}
	for key, val := range mp {
		oms := accountNameDelhiverySellerNameMapping[key] // get the exact name from the account name
		if val.Courier == "Delhivery" {
			session, err := s.TPLService.CreateSession(ctx, "delhivery", oms)
			if err != nil {
				return err
			}
			req := &tplModels.TrackingRequest{
				Waybills: nil,
				OrderID:  val.OrderIds,
			}
			trackingResponses, err := session.TrackShipment(ctx, req)
			if err != nil {
				return err
			}
			for _, trackingResponse := range trackingResponses.ShipmentData {
				fmt.Println("trackingResponse", trackingResponse.Shipment.AWB)
			}
		}
	}
	return nil
}
