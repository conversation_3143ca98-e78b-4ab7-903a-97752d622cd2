package impl

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/service/awbMaster/dao"
	"kc/internal/ondc/service/awbMaster/dto"
	"kc/internal/ondc/service/awbMaster/utils"
	"strings"
	"time"

	tplModels "github.com/Kirana-Club/3pl-go/pkg/models"
)

// GetWaybillDetails returns the details for a specific waybill
func (s *AWBMasterServiceImpl) GetWaybillDetails(ctx context.Context, awbNumber string, orderID uint64) ([]dao.AWB, error) {
	ctx, cancel := s.getContextWithTimeout(ctx, 5)
	defer cancel()

	// Validate input
	if awbNumber == "" && orderID == 0 {
		return nil, utils.ErrInvalidAWBNumber
	}
	awbs := []dao.AWB{}
	var err error
	if orderID != 0 {
		err = s.repository.Db.Find(&awbs, map[string]interface{}{
			"order_id": orderID,
		}).Error
	} else if awbNumber != "" {
		err = s.repository.Db.Find(&awbs, map[string]interface{}{
			"awb_number": awbNumber,
		}).Error

	}
	if err != nil {
		return nil, err
	}
	return awbs, nil
}

// Enhanced GetWaybillOpenAPIResponse with better error handling and performance
func (s *AWBMasterServiceImpl) GetWaybillOpenAPIResponse(ctx context.Context, awbNumber string) (*dto.ShipmentData, error) {
	// Input validation
	if strings.TrimSpace(awbNumber) == "" {
		return nil, fmt.Errorf("AWB number cannot be empty")
	}

	// Create context with timeout for database operations
	dbCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Use string to scan the JSON data from database (GORM handles this better)
	var shipmentDataJSON string

	// Use parameterized query with context to prevent SQL injection and handle timeouts
	query := `SELECT details FROM kiranabazar_awb_details WHERE awb_number = ? LIMIT 1`
	err := s.repository.Db.WithContext(dbCtx).Raw(query, awbNumber).Scan(&shipmentDataJSON).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query shipment data for AWB %s: %w", awbNumber, err)
	}

	// Check if we got any data
	if strings.TrimSpace(shipmentDataJSON) == "" {
		return nil, fmt.Errorf("no shipment data found for AWB: %s", awbNumber)
	}

	// Unmarshal JSON string into Delhivery struct
	var shipmentDetails tplModels.ShipmentDetails
	if err := json.Unmarshal([]byte(shipmentDataJSON), &shipmentDetails); err != nil {
		return nil, fmt.Errorf("failed to unmarshal shipment data for AWB %s: %w", awbNumber, err)
	}

	// Convert to DTO using the existing helper function
	shipmentData := s.convertToShipmentDataDTO(shipmentDetails)

	return &shipmentData, nil
}

// Helper function to convert DAO to DTO
func (s *AWBMasterServiceImpl) convertToShipmentDataDTO(data tplModels.ShipmentDetails) dto.ShipmentData {
	return dto.ShipmentData{
		AWB:                        data.AWB,
		Scans:                      data.Scans,
		Extras:                     data.Extras,
		Origin:                     data.Origin,
		Status:                     data.Status,
		Ewaybill:                   data.Ewaybill,
		Quantity:                   data.Quantity,
		CODAmount:                  data.CODAmount,
		Consignee:                  data.Consignee,
		OrderType:                  data.OrderType,
		PickUpDate:                 data.PickUpDate,
		SenderName:                 data.SenderName,
		Destination:                data.Destination,
		ReferenceNo:                data.ReferenceNo,
		DeliveryDate:               data.DeliveryDate,
		PickedupDate:               data.PickedupDate,
		ReturnedDate:               data.ReturnedDate,
		ChargedWeight:              data.ChargedWeight,
		DispatchCount:              data.DispatchCount,
		InvoiceAmount:              data.InvoiceAmount,
		PickupLocation:             data.PickupLocation,
		RTOStartedDate:             data.RTOStartedDate,
		DestRecieveDate:            data.DestRecieveDate,
		FirstAttemptDate:           data.FirstAttemptDate,
		ReverseInTransit:           data.ReverseInTransit,
		OriginRecieveDate:          data.OriginRecieveDate,
		ExpectedReturnDate:         data.ExpectedReturnDate,
		OutDestinationDate:         data.OutDestinationDate,
		PromisedDeliveryDate:       data.PromisedDeliveryDate,
		ReturnPromisedDeliveryDate: data.ReturnPromisedDeliveryDate,
		RTO:                        data.RTO,
		IsMPS:                      data.IsMPS,
		Vendor:                     data.Vendor,
		Weight:                     data.Weight,
		OrderID:                    data.OrderID,
		Delivered:                  data.Delivered,
		DeliveryType:               data.DeliveryType,
		ExternalTrackingID:         data.ExternalTrackingID,
		ExpectedDeliveryDate:       data.ExpectedDeliveryDate,
	}
}
