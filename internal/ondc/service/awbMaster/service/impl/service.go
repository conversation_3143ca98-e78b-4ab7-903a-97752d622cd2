package impl

import (
	"context"

	"kc/internal/ondc/repositories/firebaseRepo"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/awbMaster/queue"
	"kc/internal/ondc/service/awbMaster/service"
	"kc/internal/ondc/service/awbMaster/utils"
	"sync"
	"time"

	tplgo "github.com/Kirana-Club/3pl-go"
	"github.com/go-redis/redis/v8"
)

// AWBMasterServiceImpl provides the implementation of AWBMasterService
type AWBMasterServiceImpl struct {
	redisClient        *redis.Client
	repository         *sqlRepo.Repository
	readOnlyRepository *sqlRepo.Repository
	mixpanel           *mixpanelRepo.Repository
	firebaseRepository *firebaseRepo.FirebaseRepo
	workerMutex        sync.RWMutex
	TPLService         *tplgo.Service
	awbSyncQueue       *queue.QueueManager
}

// ServiceConfig contains configuration for AWBMasterService
type ServiceConfig struct {
	RedisClient        *redis.Client
	Repository         *sqlRepo.Repository
	Mixpanel           *mixpanelRepo.Repository
	FirebaseRepo       *firebaseRepo.FirebaseRepo
	TPLService         *tplgo.Service
	ReadOnlyRepository *sqlRepo.Repository
}

var (
	instance *AWBMasterServiceImpl
	once     sync.Once
)

// NewAWBMasterService creates or returns the singleton instance of AWBMasterService
func NewAWBMasterService(config ServiceConfig, env string) (service.AWBMasterService, error) {
	var err error

	once.Do(func() {
		// Validate required dependencies
		if config.RedisClient == nil || config.Repository == nil {
			err = utils.ErrServiceNotInitialized
			return
		}

		awbSyncQueue := queue.NewAWBSyncQueueManager(config.Repository.Db, env)

		instance = &AWBMasterServiceImpl{
			redisClient:        config.RedisClient,
			repository:         config.Repository,
			mixpanel:           config.Mixpanel,
			firebaseRepository: config.FirebaseRepo,
			workerMutex:        sync.RWMutex{},
			TPLService:         config.TPLService,
			readOnlyRepository: config.ReadOnlyRepository,
			awbSyncQueue:       awbSyncQueue,
		}
	})

	if err != nil {
		return nil, err
	}

	if instance == nil {
		return nil, utils.ErrServiceNotInitialized
	}

	return instance, nil
}

// getContextWithTimeout creates a context with timeout
func (s *AWBMasterServiceImpl) getContextWithTimeout(ctx context.Context, seconds int) (context.Context, context.CancelFunc) {
	if ctx == nil {
		ctx = context.Background()
	}

	return context.WithTimeout(ctx, time.Duration(seconds)*time.Second)
}

func (s *AWBMasterServiceImpl) EnqueueAWBSync(waybillNo, courier string) error {
	return s.awbSyncQueue.Enqueue(waybillNo, courier)
}

func (s *AWBMasterServiceImpl) DequeueAWBSync() ([]*queue.WaybillItem, error) {
	return s.awbSyncQueue.Dequeue()
}

func (s *AWBMasterServiceImpl) CompleteAWBSync(waybillItems []*queue.WaybillItem) error {
	return s.awbSyncQueue.CompleteItems(waybillItems)
}
