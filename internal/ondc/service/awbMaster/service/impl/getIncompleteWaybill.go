package impl

import (
	"kc/internal/ondc/service/awbMaster/dao"
	"kc/internal/ondc/service/awbMaster/dto"
	"time"
)

func (s *AWBMasterServiceImpl) GetIncompleteWaybills(limit, offset int64, timeStamp time.Time, couriers []string) ([]dto.AwbCourier, error) {
	var waybills []dto.AwbCourier

	err := s.repository.Db.Model(&dao.AWB{}).
		Select("awb_number, courier").
		Where("created_at >= ? AND courier IN ?", timeStamp, couriers).
		Order("created_at ASC").
		Limit(int(limit)).
		Offset(int(offset)).
		Find(&waybills).Error

	return waybills, err
}
