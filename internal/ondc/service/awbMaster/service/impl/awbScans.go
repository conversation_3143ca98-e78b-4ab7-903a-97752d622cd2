package impl

import (
	"context"
	"encoding/json"
	"fmt"
	DTO "kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/awbMaster/dao"
	"kc/internal/ondc/service/awbMaster/dto"
	"sort"
	"sync"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// CreateAWBScans creates AWB scan records from log data
func (s *AWBMasterServiceImpl) CreateAWBScans(ctx context.Context, logData string) error {
	var shipmentLog DTO.DelhiveryWebhookRequest
	err := json.Unmarshal([]byte(logData), &shipmentLog)
	if err == nil && shipmentLog.Shipment.AWB != "" {
		return ProcessDelhiveryLog(s.repository.Db, logData)
	}

	// If that fails, try parsing as EkartWebhookRequest
	var ekartRequest DTO.EkartWebhookRequest
	err = json.Unmarshal([]byte(logData), &ekartRequest)
	if err == nil && ekartRequest.EntityID != "" {
		return ProcessEkartLog(s.repository.Db, logData)
	}

	// If that fails, try parsing as StatusLog
	var statusLog DTO.ShipDelightWebhookRequest
	err = json.Unmarshal([]byte(logData), &statusLog)
	if err == nil && statusLog.AirwayBillNo != "" {
		return ProcessShipdelightLogs(s.repository.Db, logData)
	}
	return fmt.Errorf("unrecognized log format")
}

// ProcessDelhiveryLog processes logs in the first format (Shipment style)
func ProcessDelhiveryLog(db *gorm.DB, logData string) error {
	var shipmentLog = DTO.DelhiveryWebhookRequest{}
	err := json.Unmarshal([]byte(logData), &shipmentLog)
	if err != nil {
		return fmt.Errorf("failed to parse shipment log: %w", err)
	}

	istLocation, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		return err
	}

	// Parse timestamp
	statusDateTime, err := time.ParseInLocation("2006-01-02T15:04:05.999999", shipmentLog.Shipment.Status.StatusDateTime, istLocation)
	if err != nil {
		return fmt.Errorf("failed to parse status date time: %w", err)
	}

	// Create scan record
	scan := dao.KiranaBazarAWBScan{
		AWBNumber:                shipmentLog.Shipment.AWB,
		CourierStatus:            shipmentLog.Shipment.Status.Status,
		CourierStatusType:        shipmentLog.Shipment.Status.StatusType,
		CourierStatusDescription: shipmentLog.Shipment.Status.Instructions,
		CourierStatusCode:        shipmentLog.Shipment.NSLCode,
		StatusLocation:           shipmentLog.Shipment.Status.StatusLocation,
		ScanTimestamp:            statusDateTime,
		InternalStatusCode:       shipmentLog.Shipment.ReferenceNo, // map internal status here
		CourierName:              "Delhivery",
		Status:                   shipmentLog.Shipment.NSLCode,
	}

	// Use upsert to handle duplicate entries
	result := db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "awb_number"}, {Name: "scan_timestamp"}, {Name: "status"}},
		DoUpdates: clause.AssignmentColumns([]string{"courier_status", "courier_status_code", "courier_status_type", "courier_status_description", "status_location", "internal_status_code", "courier_name"}),
	}).Create(&scan)

	return result.Error
}

// ProcessShipdelightLogs processes logs in the second format (Status style)
func ProcessShipdelightLogs(db *gorm.DB, logData string) error {
	var statusLog = DTO.ShipDelightWebhookRequest{}
	err := json.Unmarshal([]byte(logData), &statusLog)
	if err != nil {
		return fmt.Errorf("failed to parse status log: %w", err)
	}

	istLocation, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		return err
	}
	// Parse timestamp
	updatedDate, err := time.ParseInLocation("2006-01-02 15:04:05", statusLog.UpdatedDate, istLocation)
	if err != nil {
		return fmt.Errorf("failed to parse updated date: %w", err)
	}

	// Create scan record
	scan := dao.KiranaBazarAWBScan{
		AWBNumber:                statusLog.AirwayBillNo,
		CourierStatus:            statusLog.Status,
		CourierStatusType:        "SHIPDELIGHT",
		CourierStatusCode:        statusLog.LspStatusCode,
		CourierStatusDescription: statusLog.Remarks,
		CourierName:              statusLog.CourierName,
		StatusLocation:           statusLog.Location,
		ScanTimestamp:            updatedDate,
		InternalStatusCode:       statusLog.OrderNo, // map internal status code here
		Status:                   statusLog.StatusCode,
	}

	// Use upsert to handle duplicate entries
	result := db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "awb_number"}, {Name: "scan_timestamp"}, {Name: "status"}},
		DoUpdates: clause.AssignmentColumns([]string{"courier_status", "courier_status_code", "courier_status_type", "courier_status_description", "status_location", "internal_status_code", "courier_name"}),
	}).Create(&scan)

	return result.Error
}

// ProcessEkartLog processes logs in the third format (Ekart style)
func ProcessEkartLog(db *gorm.DB, logData string) error {
	var ekartRequest = DTO.EkartWebhookRequest{}
	err := json.Unmarshal([]byte(logData), &ekartRequest)
	if err != nil {
		return fmt.Errorf("failed to parse ekart log: %w", err)
	}

	// istLocation, err := time.LoadLocation("Asia/Kolkata")
	// if err != nil {
	// 	return err
	// }

	// Parse timestamp
	eventDate, err := DTO.ParseEkartDateTime(ekartRequest.EventDate)
	if err != nil {
		return fmt.Errorf("failed to parse event date: %w", err)
	}

	// Create scan record
	scan := dao.KiranaBazarAWBScan{
		AWBNumber:                ekartRequest.EntityID,
		CourierStatus:            ekartRequest.Event,
		CourierStatusType:        ekartRequest.Event,
		CourierStatusCode:        ekartRequest.Status,
		CourierStatusDescription: ekartRequest.Event,
		CourierName:              "Ekart",
		StatusLocation:           ekartRequest.Location,
		ScanTimestamp:            eventDate,
		InternalStatusCode:       ekartRequest.Event,
		Status:                   ekartRequest.Status,
	}

	// Use upsert to handle duplicate entries
	result := db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "awb_number"}, {Name: "scan_timestamp"}, {Name: "status"}},
		DoUpdates: clause.AssignmentColumns([]string{"courier_status", "courier_status_code", "courier_status_type", "courier_status_description", "status_location", "internal_status_code", "courier_name"}),
	}).Create(&scan)

	return result.Error
}

// GetAWBLatestStatus retrieves the latest scan for a specific AWB number
func (s *AWBMasterServiceImpl) GetAWBLatestStatus(ctx context.Context, awbNumber string) (*dto.AWBScan, error) {
	var scan dao.KiranaBazarAWBScan
	var awbScan dto.AWBScan
	result := s.repository.Db.Where("awb_number = ?", awbNumber).
		Order("scan_timestamp DESC").
		First(&scan)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("no scans found for AWB: %s", awbNumber)
		}
		return nil, fmt.Errorf("failed to get latest AWB status: %w", result.Error)
	}
	awbScan = dto.AWBScan{
		AWBNumber:                scan.AWBNumber,
		CourierStatus:            scan.CourierStatus,
		CourierStatusCode:        scan.CourierStatusCode,
		CourierStatusType:        scan.CourierStatusType,
		CourierStatusDescription: scan.CourierStatusDescription,
		CourierName:              scan.CourierName,
		StatusLocation:           scan.StatusLocation,
		ScanTimestamp:            scan.ScanTimestamp,
		InternalStatusCode:       scan.InternalStatusCode,
	}

	return &awbScan, nil
}

// GetAWBScans retrieves AWB scan data with concurrent processing for better performance
func (s *AWBMasterServiceImpl) GetAWBScans(ctx context.Context, awbNumber string) (*dto.AWBDetailsData, error) {
	// Input validation
	if awbNumber == "" {
		return nil, fmt.Errorf("AWB number cannot be empty")
	}

	// Create a context with timeout to prevent hanging operations
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Use channels and WaitGroup for concurrent operations
	var wg sync.WaitGroup
	scansChan := make(chan []dao.KiranaBazarAWBScan, 1)
	shipmentChan := make(chan *dto.ShipmentData, 1)
	errorsChan := make(chan error, 2)

	// Goroutine 1: Fetch AWB scans from database
	wg.Add(1)
	go func() {
		defer wg.Done()
		scans, err := s.fetchAWBScansFromDB(ctx, awbNumber)
		if err != nil {
			select {
			case errorsChan <- fmt.Errorf("failed to fetch AWB scans: %w", err):
			case <-ctx.Done():
			}
			return
		}
		select {
		case scansChan <- scans:
		case <-ctx.Done():
		}
	}()

	// Goroutine 2: Fetch shipment data from external API
	wg.Add(1)
	go func() {
		defer wg.Done()
		shipmentData, err := s.GetWaybillOpenAPIResponse(ctx, awbNumber)
		if err != nil {
			shipmentData = &dto.ShipmentData{}
		}
		select {
		case shipmentChan <- shipmentData:
		case <-ctx.Done():
		}
	}()

	// Wait for goroutines to complete or context to be cancelled
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	var scans []dao.KiranaBazarAWBScan
	var shipmentData *dto.ShipmentData

	// Collect results from goroutines
	select {
	case <-ctx.Done():
		return nil, fmt.Errorf("operation timed out or cancelled: %w", ctx.Err())
	case <-done:
		// All goroutines completed, collect results
		select {
		case scans = <-scansChan:
		default:
			return nil, fmt.Errorf("failed to retrieve AWB scans")
		}

		select {
		case shipmentData = <-shipmentChan:
		default:
			shipmentData = &dto.ShipmentData{}
		}

		// Check for any errors
		select {
		case err := <-errorsChan:
			return nil, err
		default:
		}
	}

	// Validate that we have scans data
	if len(scans) == 0 {
		return nil, fmt.Errorf("no scans found for AWB: %s", awbNumber)
	}

	// Process scans data
	awbScans := s.convertToAWBScans(scans)

	// Sort scans by timestamp (ascending order for chronological display)
	sort.Slice(awbScans, func(i, j int) bool {
		return awbScans[i].ScanTimestamp.Before(awbScans[j].ScanTimestamp)
	})

	// Convert to final scan format
	allScans := s.convertToScans(awbScans)

	// Build and return response
	awbData := dto.AWBDetailsData{
		Scans:                 allScans,
		Origin:                shipmentData.Origin,
		Destination:           shipmentData.Destination,
		SenderName:            shipmentData.SenderName,
		ReferenceID:           shipmentData.ReferenceNo,
		PromisedDeliveryDate:  shipmentData.PromisedDeliveryDate,
		EstimatedDeliveryDate: shipmentData.EstimatedDeliveryDate,
	}

	return &awbData, nil
}

// fetchAWBScansFromDB fetches AWB scans from the database
func (s *AWBMasterServiceImpl) fetchAWBScansFromDB(ctx context.Context, awbNumber string) ([]dao.KiranaBazarAWBScan, error) {
	var scans []dao.KiranaBazarAWBScan

	// Use the context in the database query for cancellation support
	result := s.repository.Db.WithContext(ctx).
		Where("awb_number = ?", awbNumber).
		Order("scan_timestamp DESC").
		Find(&scans)

	if result.Error != nil {
		return nil, result.Error
	}

	return scans, nil
}

// convertToAWBScans converts DAO objects to DTO AWBScan objects
func (s *AWBMasterServiceImpl) convertToAWBScans(scans []dao.KiranaBazarAWBScan) []dto.AWBScan {
	awbScans := make([]dto.AWBScan, 0, len(scans))

	for _, scan := range scans {
		awbScan := dto.AWBScan{
			AWBNumber:                scan.AWBNumber,
			CourierStatus:            scan.CourierStatus,
			CourierStatusCode:        scan.CourierStatusCode,
			CourierStatusType:        scan.CourierStatusType,
			CourierStatusDescription: scan.CourierStatusDescription,
			CourierName:              scan.CourierName,
			StatusLocation:           scan.StatusLocation,
			ScanTimestamp:            scan.ScanTimestamp,
			InternalStatusCode:       scan.InternalStatusCode,
		}
		awbScans = append(awbScans, awbScan)
	}

	return awbScans
}

// convertToScans converts AWBScan objects to final Scan format
func (s *AWBMasterServiceImpl) convertToScans(awbScans []dto.AWBScan) []dto.Scan {
	allScans := make([]dto.Scan, 0, len(awbScans))

	for _, scan := range awbScans {
		allScans = append(allScans, dto.Scan{
			Name:         scan.CourierStatus,
			Type:         scan.CourierStatusType,
			Code:         scan.CourierStatusCode,
			Time:         scan.ScanTimestamp.Format(time.RFC3339), // Use proper time formatting
			Instructions: scan.CourierStatusDescription,
			Location:     scan.StatusLocation,
		})
	}

	return allScans
}

// Additional helper method for bulk operations (if needed)
func (s *AWBMasterServiceImpl) GetMultipleAWBScans(ctx context.Context, awbNumbers []string) (map[string]*dto.AWBDetailsData, error) {
	if len(awbNumbers) == 0 {
		return nil, fmt.Errorf("no AWB numbers provided")
	}

	results := make(map[string]*dto.AWBDetailsData)
	var mu sync.RWMutex
	var wg sync.WaitGroup

	// Limit concurrent operations to prevent overwhelming the database
	semaphore := make(chan struct{}, 10) // Max 10 concurrent operations

	for _, awbNumber := range awbNumbers {
		wg.Add(1)
		go func(awb string) {
			defer wg.Done()

			// Acquire semaphore
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			data, err := s.GetAWBScans(ctx, awb)

			mu.Lock()
			if err != nil {
				// Log error but continue with other AWBs
				fmt.Printf("Error getting AWB scans for %s: %v\n", awb, err)
			} else {
				results[awb] = data
			}
			mu.Unlock()
		}(awbNumber)
	}

	wg.Wait()
	return results, nil
}
