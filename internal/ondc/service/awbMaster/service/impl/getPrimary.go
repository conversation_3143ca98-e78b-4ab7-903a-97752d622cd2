package impl

import (
	"context"
	"fmt"
	"sort"
	"strings"

	"kc/internal/ondc/service/awbMaster/dao"
	"kc/internal/ondc/service/awbMaster/utils"
)

// GetPrimaryWaybillForOrderID returns the primary waybill for an order ID
func (s *AWBMasterServiceImpl) GetPrimaryWaybillForOrderID(ctx context.Context, orderIDs []uint64) ([]dao.AWB, error) {
	ctx, cancel := s.getContextWithTimeout(ctx, 5)
	defer cancel()

	// Validate order ID
	if len(orderIDs) == 0 {
		return nil, utils.ErrInvalidOrderID
	}
	if len(orderIDs) > 10 {
		return nil, utils.ErrMoreOrderIDs
	}

	// Fetch all waybills for the given order IDs
	waybills := []dao.AWB{}
	err := s.repository.Db.Where("order_id in ?", orderIDs).Find(&waybills).Error
	if err != nil {
		return nil, err
	}

	// Create a map to group waybills by order ID for faster lookup
	waybillsByOrderID := make(map[uint64][]dao.AWB)
	for _, waybill := range waybills {
		if waybill.OrderID != nil {
			waybillsByOrderID[*waybill.OrderID] = append(waybillsByOrderID[*waybill.OrderID], waybill)
		}
	}

	// Create response for each order ID
	response := make([]dao.AWB, 0, len(orderIDs))
	for _, orderID := range orderIDs {
		waybillsForOrder, exists := waybillsByOrderID[orderID]

		var primaryWaybill *dao.AWB
		var err error
		if exists {
			primaryWaybill, err = GetPrimaryWaybill(waybillsForOrder, int64(orderID))
		}

		if err != nil {
			return nil, err
		}
		if primaryWaybill == nil {
			continue
		}
		response = append(response, *primaryWaybill)
	}

	return response, nil
}

func NormalizeStatus(status, statusType, nslCode string) string {
	// NSL code status mapping
	nslCodeStatusMap := map[string]string{
		"X-DDD3FD": "DISPATCHED",
		"ST-114":   "IN TRANSIT",
		"DTUP-210": "CANCELLED",
	}
	ndrCodes := map[string]bool{
		"EOD-74": true, "EOD-15": true, "EOD-104": true,
		"EOD-43": true, "EOD-86": true, "EOD-11": true,
		"EOD-69": true, "EOD-6": true,
	}

	status = strings.ToUpper(status)
	if status == "DELIVERED" && (statusType == "DL" || statusType == "Forward") {
		return "DELIVERED"
	}
	if (status == "RTO DELIVERED") || (status == "RTO" && statusType == "DL") || (status == "DELIVERED" && (statusType == "RT" || statusType == "RTO")) {
		return "RTO DELIVERED"
	}
	if (statusType == "RT") || (statusType == "RTO") {
		return "RTO"
	}
	if status == "PICKUP CANCELLED" {
		return "CANCELLED"
	}
	if _, exists := ndrCodes[nslCode]; exists {
		return "NDR"
	}
	if mappedStatus, exists := nslCodeStatusMap[nslCode]; exists {
		return mappedStatus
	}
	return status
}

func GetPrimaryWaybill(waybills []dao.AWB, orderID int64) (*dao.AWB, error) {

	if len(waybills) == 0 {
		return nil, fmt.Errorf("no waybills found for order_id")
	}

	var finalWaybills []dao.AWB
	for i, waybill := range waybills {
		normalisedStatus := NormalizeStatus(*waybill.Status, *waybill.StatusType, *waybill.NSLCode)
		waybills[i].Status = &normalisedStatus
	}

	// Prioritize "DELIVERED" status
	for _, w := range waybills {
		if w.Status != nil && *w.Status == "DELIVERED" {
			finalWaybills = append(finalWaybills, w)
		}
	}
	if len(finalWaybills) == 0 {
		// Filter out cancelled and not picked waybills
		for _, w := range waybills {
			if w.Status != nil && *w.Status != "CANCELLED" && *w.Status != "NOT PICKED" {
				finalWaybills = append(finalWaybills, w)
			}
		}
		// If all are cancelled or not picked, include only not picked
		if len(finalWaybills) == 0 {
			// Filter out cancelled waybills
			for _, w := range waybills {
				if w.Status != nil && *w.Status != "CANCELLED" {
					finalWaybills = append(finalWaybills, w)
				}
			}
			// If all are cancelled, include all waybills
			if len(finalWaybills) == 0 {
				finalWaybills = waybills
			}
		}
	}

	// Sort by manifested_at and return the latest waybill
	sort.Slice(finalWaybills, func(i, j int) bool {
		if finalWaybills[i].ManifestedAt != nil && finalWaybills[j].ManifestedAt != nil {
			return *finalWaybills[i].ManifestedAt > *finalWaybills[j].ManifestedAt
		}
		// If either manifested_at is nil, prefer the non-nil one
		if finalWaybills[i].ManifestedAt == nil && finalWaybills[j].ManifestedAt != nil {
			return false // j is preferred because it has a manifested date
		}
		return true // Either i has a date and j doesn't, or both are nil
	})
	return &finalWaybills[0], nil
}
