package impl

import (
	"context"
	"kc/internal/ondc/service/awbMaster/dao"
	"kc/internal/ondc/service/awbMaster/utils"
)

// GetAllWaybillsForOrderID returns all waybills for an order ID
func (s *AWBMasterServiceImpl) GetAllWaybillsForOrderID(ctx context.Context, orderID uint64) ([]dao.AWB, error) {
	// Validate order ID
	if orderID == 0 {
		return nil, utils.ErrInvalidOrderID
	}

	awbs := []dao.AWB{}
	err := s.repository.Db.Where("order_id = ?", orderID).Find(&awbs).Error
	if err != nil {
		return awbs, err
	}
	// Get all AWBs from repository
	return awbs, nil
}

func (s *AWBMasterServiceImpl) GetAllActiveWaybillsForOrderID(ctx context.Context, orderID uint64) ([]dao.AWB, error) {
	// Validate order ID
	if orderID == 0 {
		return nil, utils.ErrInvalidOrderID
	}

	awbs := []dao.AWB{}
	err := s.repository.Db.Where("order_id = ? AND is_active = 1", orderID).Find(&awbs).Error
	if err != nil {
		return awbs, err
	}
	// Get all AWBs from repository
	return awbs, nil
}
