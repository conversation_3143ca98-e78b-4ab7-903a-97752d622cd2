package service

import (
	"context"
	"kc/internal/ondc/service/awbMaster/dao"
	"kc/internal/ondc/service/awbMaster/dto"
	"kc/internal/ondc/service/awbMaster/queue"
	"time"
)

// AWBMasterService defines the methods for the AWB Master service
type AWBMasterService interface {
	// Create handles the creation or update of AWB data
	Create(ctx context.Context, request dto.CreateAWBRequest) (*dao.AWB, error)

	// GetPrimaryWaybillForOrderID returns the primary waybill for an order ID
	GetPrimaryWaybillForOrderID(ctx context.Context, orderIDs []uint64) ([]dao.AWB, error)

	// GetAllWaybillsForOrderID returns all waybills for an order ID
	GetAllWaybillsForOrderID(ctx context.Context, orderID uint64) ([]dao.AWB, error)

	// GetAllActiveWaybillsForOrderID returns all waybills for an order ID
	GetAllActiveWaybillsForOrderID(ctx context.Context, orderID uint64) ([]dao.AWB, error)

	// GetOrderIDsForWaybills returns order IDs corresponding to waybill numbers
	GetOrderIDsForWaybills(ctx context.Context, awbNumbers []string) ([]dto.GetOrderIDResponse, error)

	// UpdatePrimaryWaybill updates which waybill is primary for an order
	UpdatePrimaryWaybill(ctx context.Context, request dto.UpdatePrimaryRequest) error

	// GetWaybillDetails returns the details for a specific waybill
	GetWaybillDetails(ctx context.Context, awbNumber string, orderID uint64) ([]dao.AWB, error)

	// SyncWayBillData accepts maximum of 49 waybill numbers and sync the data from the api to the our database
	SyncWayBillData(ctx context.Context, waybills []string) error

	// SyncOrderData accepts maximum of 10 orderids which are also the reference ids and syncs the data from the api to the internal DB
	SyncOrderData(ctx context.Context, orderIDs []string) error

	// GetIncompleteWaybills returns all the waybill numbers where the data is not updated
	GetIncompleteWaybills(limit, offset int64, timeStamp time.Time, couriers []string) ([]dto.AwbCourier, error)

	// CreateAWBScans creates the awb scan data
	CreateAWBScans(ctx context.Context, logData string) error

	// GetAWBLatestStatus returns the latest scan for a specific AWB number
	GetAWBLatestStatus(ctx context.Context, awbNumber string) (*dto.AWBScan, error)

	// GetAWBScans returns all scans for a specific AWB number, sorted by timestamp
	GetAWBScans(ctx context.Context, awbNumber string) (*dto.AWBDetailsData, error)

	// EnqueueAWBSync adds a waybill to the sync queue
	EnqueueAWBSync(waybillNo, courier string) error

	// DequeueAWBSync retrieves items from the sync queue
	DequeueAWBSync() ([]*queue.WaybillItem, error)

	// CompleteAWBSync marks waybills as completed in the sync queue
	CompleteAWBSync(waybillItems []*queue.WaybillItem) error
}
