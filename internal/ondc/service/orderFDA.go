package service

import (
	"context"
	"errors"
	"fmt"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/service/logistics/couriers"
	orderS "kc/internal/ondc/service/orderStatus"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	"kc/internal/ondc/utils"
	"strconv"
	"time"

	"github.com/mixpanel/mixpanel-go"
)

func (s *Service) HandleFailedDeliveryOrder(ctx context.Context, request dto.OrderFailedDeliveryRequest) (response *dto.OrderFailedDeliveryResponse, err error) {
	// getting source
	source := "APP"
	if request.Data.Email == "IVR" {
		source = "AUTOMATION"
	} else if request.Data.Email == "SHIPWAY" {
		source = "AUTOMATION"
	} else if request.Data.Email != "" {
		source = "D2R"
	}

	orderStatus := request.Data.OrderStatus
	orderStatuses := orderS.MapOrderStatus(orderStatus, request.Data.StatusType, orderS.OrderStatusResponse{})
	orderID := request.Data.OrderID
	if orderID == "" {
		err = errors.New("orderID cannot be empty")
		return
	}

	orderid, err := strconv.Atoi(orderID)
	if err != nil {
		err = errors.New("orderID is not defined")
		return
	}
	orderDetails, err := GetOrderDetails(s.repository, orderID)
	if err != nil {
		return
	}

	orderid64 := int64(orderid)
	orderInfo, err := GetOrderInfo(s.repository, orderid64)
	if err != nil {
		return
	}

	orderApiStatus, err := GetOrderApiStatus(s.repository, orderid64)
	if err != nil {
		return
	}

	ndrStatus := displaystatus.NDR
	if request.Data.OrderStatus != "" {
		ndrStatus = request.Data.OrderStatus
	}

	if orderStatus == orderInfo.DisplayStatus {
		return
	}
	_, _, err = s.repository.Update(dao.KiranaBazarOrder{
		ID: &orderid64,
	}, dao.KiranaBazarOrder{
		OrderStatus:      &ndrStatus,
		UpdatedAt:        time.Now(),
		DeliveryStatus:   orderStatuses.ShipmentStatus,
		DisplayStatus:    orderStatuses.DisplayStatus,
		ProcessingStatus: orderStatuses.ProcessingStatus,
	})
	if err != nil {
		return
	}

	eventObject := map[string]interface{}{
		"distinct_id":     request.UserID,
		"order_id":        orderid64,
		"cart_value":      int(orderDetails.GetCartValue()),
		"order_value":     int(orderDetails.GetOrderValue()),
		"seller":          orderInfo.Seller,
		"explaination":    request.Data.Explanation,
		"source":          source,
		"ordering_module": utils.MakeTitleCase(orderInfo.Seller),
	}

	if orderInfo.TrackingLink != nil {
		eventObject["tracking_link"] = *orderInfo.TrackingLink
	}
	if orderApiStatus.AWBNumber != nil {
		eventObject["awb_number"] = *orderApiStatus.AWBNumber
	}
	if orderApiStatus.Courier != nil {
		eventObject["courier_name"] = couriers.GetActualCourierName(orderApiStatus.Courier)
	}

	if request.Data.Email != "" {
		eventObject["email"] = request.Data.Email
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order NDR", request.UserID, eventObject),
	})

	err = webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{request.UserID},
		EventName:   "Order NDR",
		EventObject: eventObject,
	})
	if err != nil {
		fmt.Println("failed to send webengage event")
	}

	go func(mp *mixpanelRepo.Repository, userID string, orderID int64, orderValue int, s *Service, note, email string, source, seller string, orderStatuses orderS.OrderStatusResponse) {
		trackingObject := map[string]interface{}{
			"distinct_id":             userID,
			"order_id":                orderID,
			"order_value":             orderValue,
			"status":                  orderStatus,
			"notes":                   note,
			"ordering_module":         utils.MakeTitleCase(seller),
			"seller":                  seller,
			"email":                   email,
			"source":                  source,
			"shipment_status":         orderStatuses.ShipmentStatus,
			"processing_status":       orderStatuses.ProcessingStatus,
			"display_status":          orderStatuses.DisplayStatus,
			"previous_display_status": orderInfo.DisplayStatus,
			"event_trigger":           "order_ndr",
		}

		mp.Track(context.Background(), []*mixpanel.Event{
			mp.NewEvent("Order Status Updated", userID, trackingObject),
		})

		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{userID},
			EventName:   "Order Status Updated",
			EventObject: trackingObject,
		})
	}(s.Mixpanel, request.UserID, orderid64, int(orderDetails.GetOrderValue()), s, request.Data.Message, request.Data.Email, source, orderInfo.Seller, orderStatuses)

	response = &dto.OrderFailedDeliveryResponse{
		Data: dto.OrderFailedDeliveryData{
			OrderID: orderID,
			Message: "Order has been marked failed delivery",
		},
	}
	return
}
