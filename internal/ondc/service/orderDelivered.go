package service

import (
	"context"
	"errors"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/external/whatsapp"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/logistics/couriers"
	orderS "kc/internal/ondc/service/orderStatus"
	"kc/internal/ondc/service/orderStatus/constants"
	"kc/internal/ondc/utils"
	"strconv"
	"strings"
	"time"

	"github.com/mixpanel/mixpanel-go"
)

func (s *Service) HandleDeliveredOrder(ctx context.Context, request dto.OrderDeliveredRequest) (response *dto.OrderDeliveredResponse, err error) {

	// getting source
	source := "APP"
	if request.Data.Email == "IVR" {
		source = "AUTOMATION"
	} else if request.Data.Email != "" {
		source = "D2R"
	} else if request.Data.Email == "SHIPWAY" {
		source = "AUTOMATION"
	}
	orderID := request.Data.OrderID
	if orderID == "" {
		err = errors.New("orderID cannot be empty")
		return
	}

	orderid, err := strconv.Atoi(orderID)
	if err != nil {
		err = errors.New("orderID is not defined")
		return
	}
	orderid64 := int64(orderid)
	orderDetails, err := GetOrderDetails(s.repository, orderID)
	if err != nil {
		return
	}

	orderInfo, err := GetOrderInfo(s.repository, orderid64)
	if err != nil {
		return
	}

	orderApiStatus, err := GetOrderApiStatus(s.repository, orderid64)
	if err != nil {
		return
	}

	orderNDRInfo, _ := s.GetNDROrder(int(orderid64))

	deliveredOrderStatus := "DELIVERED"
	if request.Data.OrderStatus != "" {
		deliveredOrderStatus = request.Data.OrderStatus
	}
	orderStatuses := orderS.MapOrderStatus(deliveredOrderStatus, request.Data.StatusType, orderS.OrderStatusResponse{})

	if orderStatuses.DisplayStatus == orderInfo.DisplayStatus {
		return
	}
	_, _, err = s.repository.Update(dao.KiranaBazarOrder{
		ID: &orderid64,
	}, dao.KiranaBazarOrder{
		OrderStatus:      &deliveredOrderStatus,
		UpdatedAt:        time.Now(),
		DeliveryStatus:   orderStatuses.ShipmentStatus,
		DisplayStatus:    orderStatuses.DisplayStatus,
		ProcessingStatus: orderStatuses.ProcessingStatus,
	})
	if err != nil {
		return
	}

	discountIds := orderDetails.GetDiscountIds()
	discountCodes := []string{}
	if len(discountIds) > 0 {
		for _, discountId := range discountIds {
			discountCode, err := s.Coupons.GetCouponByID(ctx, discountId)
			if err == nil {
				discountCodes = append(discountCodes, discountCode.Code)
			}
		}
	}

	eventObject := map[string]interface{}{
		"distinct_id":     request.UserID,
		"order_id":        orderid64,
		"cart_value":      int(orderDetails.GetCartValue()),
		"order_value":     int(orderDetails.GetOrderValue()),
		"seller":          orderInfo.Seller,
		"explaination":    request.Data.Explanation,
		"source":          source,
		"ordering_module": utils.MakeTitleCase(orderInfo.Seller),
		"discount_ids":    discountIds,
		"discount_codes":  discountCodes,
	}

	if orderID != "" {
		eventObject["feedback_webview_url"] = fmt.Sprintf("https://webapps.retailpulse.ai/post-delivery-flow/%s", orderID)

		sellerName, err := brands.GetNameMappingBySeller(orderDetails.Seller)

		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error getting seller name mapping for %s: %v Handle Confirmed Order", orderDetails.Seller, err))
		}

		err = whatsapp.SendGenericWhatsAppMessageWithDynamicLink("order_delivered", *orderDetails.ShippingAddress.Phone, []string{orderID, sellerName}, []string{}, "button", map[string]interface{}{
			"cta": map[string]interface{}{
				"name":     "WebViewOld",
				"nav_type": "Redirect to WebviewOld",
				"params": map[string]interface{}{
					"screenTitle": "",
					"showHeader":  false,
					"uri":         fmt.Sprintf("https://webapps.retailpulse.ai/post-delivery-flow/%s", orderID),
				},
			},
			"non_firebase_link": false,
		})
		if err != nil {
			fmt.Errorf("failed to send whatsapp message for order delivered: %v", err)
		}
	}

	if orderInfo.TrackingLink != nil {
		eventObject["tracking_link"] = *orderInfo.TrackingLink
	}
	if orderApiStatus.AWBNumber != nil {
		eventObject["awb_number"] = *orderApiStatus.AWBNumber
	}
	if orderApiStatus.Courier != nil {
		eventObject["courier_name"] = couriers.GetActualCourierName(orderApiStatus.Courier)
	}

	if request.Data.Email != "" {
		eventObject["email"] = request.Data.Email
	}
	if orderNDRInfo != nil {
		eventObject["email_NDR"] = orderNDRInfo.AssignedTo
	}

	if request.Data.DeliveredAt != 0 {
		eventObject["time"] = request.Data.DeliveredAt
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Delivered", request.UserID, eventObject,
			fmt.Sprintf("%s_%d", "order_delivered", orderid64)),
	})

	err = webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{request.UserID},
		EventName:   "Order Delivered",
		EventObject: eventObject,
	})
	if err != nil {
		fmt.Println("failed to send webengage event")
	}

	// this is go routine fired to call external api to update the widget
	go func(uid, orderid string, totalAmount float64, orderid64 int64) {
		s.ActivateUserLoyaltyRewards(context.Background(), dto.ActivateUserLoyaltyRewardsRequest{
			UserID: request.UserID,
			Data: dto.ActivateUserLoyaltyRewardsRequestData{
				OrderId: orderid64,
			},
		})
		// call this API again for zoff_foods or RSB for thailand scheme
		// reqObject := map[string]interface{}{
		// 	"user_id":  request.UserID,
		// 	"order_id": request.Data.OrderID,
		// 	"amount":   orderDetails.TotalAmount,
		// 	"status":   constants.DELIVERED,
		// }
		// utils.CallExternalAPIAsync(utils.PROGRESS_WIDGET_RESOLVER_API, "POST", reqObject, nil)
		go func(uid, orderid, seller, orderStatus string, orderDetails *dao.KiranaBazarOrderDetails) {
			s.UpdateProgressWidget(context.Background(), &dto.UpdateProgressWidgetRequest{
				UserID: uid,
				Data: dto.UpdateProgressWidgetData{
					OrderID:      orderid,
					Status:       orderStatus,
					Amount:       orderDetails.TotalAmount,
					OrderDetails: orderDetails,
					Seller:       seller,
				},
			})
		}(request.UserID, request.Data.OrderID, orderInfo.Seller, constants.DELIVERED, orderDetails)
	}(request.UserID, request.Data.OrderID, orderDetails.TotalAmount, orderid64)

	go func(mp *mixpanelRepo.Repository, userID string, orderID int64, orderValue int, s *Service, note, email string, source, seller string, orderStatuses orderS.OrderStatusResponse) {
		trackingObject := map[string]interface{}{
			"distinct_id":             userID,
			"order_id":                orderID,
			"order_value":             orderValue,
			"status":                  constants.DELIVERED,
			"notes":                   note,
			"ordering_module":         utils.MakeTitleCase(seller),
			"seller":                  seller,
			"email":                   email,
			"source":                  source,
			"shipment_status":         orderStatuses.ShipmentStatus,
			"processing_status":       orderStatuses.ProcessingStatus,
			"display_status":          orderStatuses.DisplayStatus,
			"previous_display_status": orderInfo.DisplayStatus,
			"event_trigger":           "order_delivered",
		}

		if request.Data.DeliveredAt != 0 {
			eventObject["time"] = request.Data.DeliveredAt
		}
		mp.Track(context.Background(), []*mixpanel.Event{
			mp.NewEvent("Order Status Updated", userID, trackingObject),
		})

		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{userID},
			EventName:   "Order Status Updated",
			EventObject: trackingObject,
		})

		s.AddDataForReconciliation(context.Background(), &dto.AddReconciliationRequest{
			OrderID: orderID,
			Data: []dto.StatusTimeStamp{
				dto.StatusTimeStamp{
					TimeStamp:   request.Data.DeliveredAt,
					OrderStatus: "order_delivered",
				},
			},
			Service: source,
		})
	}(s.Mixpanel, request.UserID, orderid64, int(orderDetails.GetOrderValue()), s, request.Data.Explanation, request.Data.Email, source, orderInfo.Seller, orderStatuses)

	go func(orderID string) {
		newStatus := utils.TICKET_STATUS_RESOLVED
		note := "Order has been marked delivered by " + request.Data.Source
		updatedBy := request.Data.Email
		if !(strings.Contains(request.Data.Explanation, "@kirana.club") || strings.Contains(request.Data.Explanation, "@retailpulse.ai")) {
			updatedBy = "<EMAIL>"
		}
		s.UpdateSupportTicketStatusByOrderId(context.Background(), orderID, newStatus, note, updatedBy, "order_delivered")
	}(request.Data.OrderID)

	response = &dto.OrderDeliveredResponse{
		Data: dto.OrderDeliveredData{
			OrderID: orderID,
			Message: "Order has been marked delivered",
		},
	}
	return

}
