// internal/ondc/service/call_service.go
package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"slices"
	"strings"
	"sync"
	"time"

	"kc/internal/ondc/azure"
	"kc/internal/ondc/config"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"

	"github.com/mixpanel/mixpanel-go"
	"gorm.io/gorm"
)

var agentEmailMap = map[string]string{
	"<EMAIL>":       "+919958201450",
	"<EMAIL>":     "+919509926748",
	"<EMAIL>":  "+919835865945",
	"<EMAIL>": "+919512916949",
	"<EMAIL>":    "+917014419358",
	"<EMAIL>":    "+919521205830",
	"<EMAIL>":     "+917420895270",
	"<EMAIL>":    "+918949913453",
}

// Cache configuration
// Cache configuration
const (
	CacheExpirationDuration = 10 * time.Minute
	CacheCleanupInterval    = 15 * time.Minute
)

// Call status information
type CallInfo struct {
	CallID  int    `json:"call_id"`
	CallSid string `json:"call_sid"`
	Status  string `json:"status"`
}

// Order cache entry with single call
type OrderCallCacheEntry struct {
	OrderID   int
	Call      *CallInfo // Changed from array to single call
	UpdatedAt time.Time
	IsExpired bool
}

// Check if cache entry is expired
func (e *OrderCallCacheEntry) CheckExpired() bool {
	e.IsExpired = time.Since(e.UpdatedAt) > CacheExpirationDuration
	return e.IsExpired
}

// In-memory cache for calls by order
type OrderCallCache struct {
	mu          sync.RWMutex
	orders      map[int]*OrderCallCacheEntry
	stopCleaner chan struct{}
}

var orderCallCache = &OrderCallCache{
	orders:      make(map[int]*OrderCallCacheEntry),
	stopCleaner: make(chan struct{}),
}

// Initialize cache with cleanup routine
func init() {
	go orderCallCache.cleanupExpired()
}

// Cache methods
func (c *OrderCallCache) Set(orderID int, callInfo *CallInfo) {
	c.mu.Lock()
	defer c.mu.Unlock()

	entry := &OrderCallCacheEntry{
		OrderID:   orderID,
		Call:      callInfo,
		UpdatedAt: time.Now(),
		IsExpired: false,
	}
	c.orders[orderID] = entry
}

func (c *OrderCallCache) Get(orderID int) (*OrderCallCacheEntry, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	entry, exists := c.orders[orderID]
	if !exists {
		return nil, false
	}

	// Check if entry is expired
	entry.CheckExpired()
	return entry, true
}

func (c *OrderCallCache) UpdateCallStatus(orderID int, callSid string, status string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	entry, exists := c.orders[orderID]
	if !exists {
		return
	}

	// Update the call's status if SID matches
	if entry.Call != nil && entry.Call.CallSid == callSid {
		entry.Call.Status = status
		entry.UpdatedAt = time.Now()
		entry.IsExpired = false
	}
}

func (c *OrderCallCache) Delete(orderID int) {
	c.mu.Lock()
	defer c.mu.Unlock()
	delete(c.orders, orderID)
}

// Clean up expired entries periodically
func (c *OrderCallCache) cleanupExpired() {
	ticker := time.NewTicker(CacheCleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.removeExpiredEntries()
		case <-c.stopCleaner:
			return
		}
	}
}

func (c *OrderCallCache) removeExpiredEntries() {
	c.mu.Lock()
	defer c.mu.Unlock()

	for orderID, entry := range c.orders {
		if entry.CheckExpired() {
			delete(c.orders, orderID)
		}
	}
}

// Stop the cleanup routine (for graceful shutdown)
func (c *OrderCallCache) Stop() {
	close(c.stopCleaner)
}

func (s *Service) InitiateCall(ctx context.Context, request *dto.InitiateCallRequest) (*dto.InitiateCallResponse, error) {
	// Check if there's an active call for this order
	if entry, exists := orderCallCache.Get(int(request.Data.OrderID)); exists {

		if !entry.IsExpired && entry.Call != nil {
			// Check if the existing call is in an active state
			if entry.Call.Status == dao.CallStatusInitiated ||
				entry.Call.Status == dao.CallStatusQueued ||
				entry.Call.Status == dao.CallStatusRinging ||
				entry.Call.Status == dao.CallStatusInProgress {
				return nil, fmt.Errorf("There is already an active call %s for Order %d", entry.Call.Status, request.Data.OrderID)
			}
		}
	}

	// Get order details
	orderDetails, err := GetOrderEssentials(s.repository, request.Data.OrderID)
	if err != nil {
		return nil, fmt.Errorf("failed to get order details: %w", err)
	}
	if orderDetails.CustomerPhone == nil {
		return nil, fmt.Errorf("customer phone number is missing for this order ID: %d", request.Data.OrderID)
	}

	// Virtual number
	virtualNumber := "+917314626966"

	// Determine from number
	fromNumber := ""
	if agentPhone, exists := agentEmailMap[request.Data.InitiatedBy]; exists {
		fromNumber = agentPhone
	} else {
		return nil, fmt.Errorf("agent phone number not found for email: %s", request.Data.InitiatedBy)
	}

	// Create custom field data
	customField := map[string]interface{}{
		"order_id":     request.Data.OrderID,
		"purpose":      request.Data.Purpose,
		"seller":       orderDetails.Seller,
		"user_id":      orderDetails.UserID,
		"order_value":  orderDetails.OrderValue,
		"initiated_by": request.Data.InitiatedBy,
		"timestamp":    time.Now().UnixMilli(),
	}

	customFieldJson, err := json.Marshal(customField)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal custom field data: %w", err)
	}

	// Prepare form data
	formData := url.Values{}
	formData.Set("From", fromNumber)
	formData.Set("To", fmt.Sprintf(`+91%s`, *orderDetails.CustomerPhone))
	formData.Set("CallerId", virtualNumber)
	formData.Set("CustomField", string(customFieldJson))
	formData.Set("Record", "true")
	formData.Set("StatusCallback", "https://ondc.retailpulse.ai/webhooks/exotel/status")
	formData.Set("StatusCallbackContentType", "application/json")

	// Add webhook events
	events := []string{"initiated", "ringing", "in-progress", "completed", "failed",
		"busy", "no-answer", "terminal", "answered", "canceled", "queued"}
	for i, event := range events {
		formData.Set(fmt.Sprintf("StatusCallbackEvents[%d]", i), event)
	}

	// Create request data to store
	requestData := map[string]interface{}{
		"order_id":       request.Data.OrderID,
		"from_number":    fromNumber,
		"to_number":      orderDetails.CustomerPhone,
		"virtual_number": virtualNumber,
		"purpose":        request.Data.Purpose,
		"initiated_by":   request.Data.InitiatedBy,
		"form_data":      formData.Encode(),
		"custom_field":   customField,
	}

	// Make Exotel API call
	callSid, exotelResp, err := s.callExotelAPI(formData, requestData, int(request.Data.OrderID))
	if err != nil {
		return nil, err
	}

	// Now create the call record after successful API call
	intOrderID := int(request.Data.OrderID)
	callRecord := &dao.KiranaBazarUserCall{
		OrderID:       &intOrderID,
		CallSid:       &callSid,
		FromNumber:    fromNumber,
		ToNumber:      *orderDetails.CustomerPhone,
		VirtualNumber: virtualNumber,
		InitiatedBy:   request.Data.InitiatedBy,
		Status:        exotelResp.Call.Status,
		Purpose:       request.Data.Purpose,
		CreatedAt:     time.Now().UnixMilli(),
		UpdatedAt:     time.Now().UnixMilli(),
	}

	// Create the call record
	if _, err := s.repository.Create(callRecord); err != nil {
		return nil, fmt.Errorf("failed to create call record: %w", err)
	}

	// Update cache with single call info
	callInfo := &CallInfo{
		CallID:  callRecord.ID,
		CallSid: callSid,
		Status:  exotelResp.Call.Status,
	}
	orderCallCache.Set(int(request.Data.OrderID), callInfo)

	// Fire the call initiated event in a goroutine
	go func() {
		err := s.trackCallInitiated(ctx, *orderDetails.UserID, callSid, int64(intOrderID), orderDetails, fromNumber, request.Data.InitiatedBy, request.Data.Purpose, exotelResp.Call.Status)
		if err != nil {
			fmt.Printf("Error tracking ticket resolved: %v\n", err)
		}
	}()

	return &dto.InitiateCallResponse{
		CallID:  int64(callRecord.ID),
		CallSID: callSid,
		Status:  exotelResp.Call.Status,
		Message: "Call initiated successfully",
	}, nil
}

func (s *Service) callExotelAPI(formData url.Values, requestData map[string]interface{}, orderID int) (string, *dto.ExotelV1Response, error) {
	// Create API log entry
	apiLog := &dao.KiranaBazarCallAPILog{
		OrderID: orderID,
		Status:  "initiated",
	}
	requestDataJson, err := json.Marshal(requestData)
	if err != nil {
		return "", nil, fmt.Errorf("failed to marshal request data: %w", err)
	}
	apiLog.Request = requestDataJson
	// Prepare Exotel V1 API URL
	accountID := "kirana10"
	exotelURL := fmt.Sprintf(`https://api.exotel.com/v1/Accounts/%s/Calls/connect.json`, accountID)

	// Create request
	req, err := http.NewRequest("POST", exotelURL, strings.NewReader(formData.Encode()))
	if err != nil {
		apiLog.Status = "failed"
		responseJson, err := json.Marshal((map[string]string{"error": err.Error()}))
		if err != nil {
			return "", nil, fmt.Errorf("failed to marshal request data: %w", err)
		}
		apiLog.Response = responseJson
		s.repository.Create(apiLog)
		return "", nil, err
	}
	// Set headers
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	auth := "NTgzYzYxZjY1NWJjMDQwMTMzZDI0NmFjOWRhMDQ4Yzc2ODY4YTZhNzczNjk3OGQwOmU4NzBjNWExNWNjZjZkMmY4MzBjMzlkMDUwZWY0OTViMGQzNzZiYWIwZDcwMDBjZQ=="
	req.Header.Add("Authorization", "Basic "+auth)

	// Make the API call
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		apiLog.Status = "failed"
		responseJson, err := json.Marshal((map[string]string{"error": err.Error()}))
		if err != nil {
			return "", nil, fmt.Errorf("failed to marshal request data: %w", err)
		}
		apiLog.Response = responseJson
		s.repository.Create(apiLog)
		return "", nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		apiLog.Status = "failed"
		responseJson, err := json.Marshal((map[string]string{"error": err.Error()}))
		if err != nil {
			return "", nil, fmt.Errorf("failed to marshal request data: %w", err)
		}
		apiLog.Response = responseJson
		s.repository.Create(apiLog)
		return "", nil, err
	}

	// Parse response
	var exotelResp dto.ExotelV1Response
	if err := json.Unmarshal(body, &exotelResp); err != nil {
		apiLog.Status = "failed"
		apiLog.Response = body
		s.repository.Create(apiLog)
		return "", nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Check for errors
	if exotelResp.RestException != nil {
		apiLog.Status = "failed"
		apiLog.Response = body
		s.repository.Create(apiLog)
		return "", nil, fmt.Errorf("exotel error: %s - %s",
			exotelResp.RestException.Code,
			exotelResp.RestException.Message)
	}

	// Success - store the log
	apiLog.CallSid = exotelResp.Call.Sid
	apiLog.Status = "success"
	apiLog.Response = body
	s.repository.Create(apiLog)
	return exotelResp.Call.Sid, &exotelResp, nil
}

// Update webhook handler to update based on SID
func (s *Service) GetOrderCallStatus(ctx context.Context, orderID int) (*CallInfo, error) {
	// Check cache first
	if entry, exists := orderCallCache.Get(orderID); exists {
		if !entry.IsExpired && entry.Call != nil {
			return entry.Call, nil
		}
		// Cache is expired, need to refresh from DB
		fmt.Printf("Cache expired for order %d, fetching from database\n", orderID)
	}

	// Get from database - get the most recent call
	var call dao.KiranaBazarUserCall
	if err := s.repository.Db.Where("order_id = ?", orderID).Order("created_at DESC").First(&call).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	// Convert to CallInfo and update cache
	callInfo := &CallInfo{
		CallID: call.ID,
		Status: call.Status,
	}
	if call.CallSid != nil {
		callInfo.CallSid = *call.CallSid
	}

	// Update cache with fresh data
	orderCallCache.Set(orderID, callInfo)

	return callInfo, nil
}

// Check if order has an active call
func (s *Service) HasActiveCall(ctx context.Context, orderID int) (bool, *CallInfo, error) {
	// First check cache
	if entry, exists := orderCallCache.Get(orderID); exists {
		if !entry.IsExpired && entry.Call != nil {
			// Check if call is in active state
			if entry.Call.Status == dao.CallStatusInitiated ||
				entry.Call.Status == dao.CallStatusQueued ||
				entry.Call.Status == dao.CallStatusRinging ||
				entry.Call.Status == dao.CallStatusInProgress {
				return true, entry.Call, nil
			}
		}
	}

	// If cache miss or expired, get from DB
	callInfo, err := s.GetOrderCallStatus(ctx, orderID)
	if err != nil {
		return false, nil, err
	}

	if callInfo != nil {
		if callInfo.Status == dao.CallStatusInitiated ||
			callInfo.Status == dao.CallStatusQueued ||
			callInfo.Status == dao.CallStatusRinging ||
			callInfo.Status == dao.CallStatusInProgress {
			return true, callInfo, nil
		}
	}

	return false, nil, nil
}

func (s *Service) ExotelStatusCallback(ctx context.Context, callback *dto.ExotelStatusCallback) (interface{}, error) {
	// Get call from database by SID
	var call dao.KiranaBazarUserCall
	if err := s.repository.Db.Where("call_sid = ?", callback.CallSid).First(&call).Error; err != nil {
		return nil, fmt.Errorf("call not found: %w", err)
	}

	// Update status
	call.Status = callback.Status
	call.UpdatedAt = time.Now().UnixMilli()

	// Update in database (use the DAO object directly for updates)
	if err := s.repository.Db.Save(&call).Error; err != nil {
		return nil, fmt.Errorf("failed to update call: %w", err)
	}

	// Store webhook data in API log
	var apiLog dao.KiranaBazarCallAPILog
	if err := s.repository.Db.Where("call_sid = ?", callback.CallSid).First(&apiLog).Error; err == nil {

		// Update existing log with webhook data including leg analysis
		webhookData := map[string]interface{}{
			"webhook_status":        callback.Status,
			"webhook_time":          time.Now().Unix(),
			"duration":              callback.DialCallDuration,
			"recording":             callback.RecordingUrl,
			"conversation_duration": callback.ConversationDuration,
			"callback_data":         callback,
		}
		responseJson, err := json.Marshal(webhookData)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request data: %w", err)
		}
		apiLog.Response = responseJson
		apiLog.Status = fmt.Sprintf("webhook_%s", callback.Status)

		// Update the API log
		if err := s.repository.Db.Save(&apiLog).Error; err != nil {
			fmt.Printf("Failed to update API log: %v\n", err)
		}
	}

	// Update cache
	if call.OrderID != nil {
		orderCallCache.UpdateCallStatus(*call.OrderID, callback.CallSid, callback.Status)
	}

	if slices.Contains([]string{"completed", "failed", "busy", "no-answer"}, callback.Status) {
		go func() {
			err := s.trackCallResponseRecordingUrl(ctx, &call, callback, call.InitiatedBy)
			if err != nil {
				fmt.Printf("Error tracking ticket resolved: %v\n", err)
			}
		}()
	}

	return map[string]string{"status": "success"}, nil
}

func (s *Service) GetCallDetails(ctx context.Context, callID int64) (*dto.CallDetailsResponse, error) {
	// Get call record
	// var call dao.KiranaBazarCall
	// if err := s.repository.FindByID(&call, callID); err != nil {
	// 	return nil, fmt.Errorf("call not found: %w", err)
	// }

	// // Check if we need to fetch latest status from Exotel
	// if call.Status == "in-progress" || call.Status == "initiated" {
	// 	// Fetch current status from Exotel
	// 	latestStatus, err := s.fetchCallStatusFromExotel(call.CallSid)
	// 	if err == nil && latestStatus.Status != call.Status {
	// 		// Update local status
	// 		call.Status = latestStatus.Status
	// 		s.repository.Save(&call)
	// 	}
	// }

	// // Get recent events
	// var events []dao.KiranaBazarCallEvent
	// eventsQuery := fmt.Sprintf(`
	// 	SELECT * FROM kiranabazar_call_events
	// 	WHERE call_sid = '%s'
	// 	ORDER BY created_at DESC
	// 	LIMIT 20
	// `, call.CallSid)

	// if _, err := s.repository.CustomQuery(&events, eventsQuery); err != nil {
	// 	return nil, err
	// }

	// // Convert events to response format
	// var eventResponses []dto.CallEvent
	// for _, event := range events {
	// 	eventResponses = append(eventResponses, dto.CallEvent{
	// 		ID:           event.ID,
	// 		EventType:    event.EventType,
	// 		Status:       event.Status,
	// 		Timestamp:    event.CreatedAt,
	// 		Duration:     event.Duration,
	// 		RecordingUrl: event.RecordingUrl,
	// 	})
	// }

	// // Get order details
	// orderDetails, _ := s.getOrderDetails(call.OrderID)

	// return &dto.CallDetailsResponse{
	// 	CallID:       call.ID,
	// 	CallSID:      call.CallSid,
	// 	Status:       call.Status,
	// 	Duration:     call.Duration,
	// 	RecordingUrl: call.RecordingUrl,
	// 	Events:       eventResponses,
	// 	CreatedAt:    call.CreatedAt,
	// 	UpdatedAt:    call.UpdatedAt,
	// 	AnsweredAt:   call.AnsweredAt,
	// 	CompletedAt:  call.CompletedAt,
	// 	MetaData: map[string]interface{}{
	// 		"order_id":      call.OrderID,
	// 		"purpose":       call.Purpose,
	// 		"initiated_by":  call.InitiatedBy,
	// 		"from_number":   call.FromNumber,
	// 		"to_number":     call.ToNumber,
	// 		"seller":        orderDetails.Seller,
	// 		"direction":     call.Direction,
	// 	},
	// }, nil
	return nil, nil
}

func (s *Service) fetchCallStatusFromExotel(callSid string) (*dto.ExotelCallDetails, error) {
	// url := fmt.Sprintf("https://api.exotel.com/v1/Accounts/kiranaclub1/Calls/%s.json", callSid)

	// req, err := http.NewRequest("GET", url, nil)
	// if err != nil {
	// 	return nil, err
	// }

	// req.SetBasicAuth(s.config.ExotelAPIKey, s.config.ExotelAPIToken)

	// resp, err := s.httpClient.Do(req)
	// if err != nil {
	// 	return nil, err
	// }
	// defer resp.Body.Close()

	// body, err := io.ReadAll(resp.Body)
	// if err != nil {
	// 	return nil, err
	// }

	// var exotelResp dto.ExotelCallDetailsResponse
	// if err := json.Unmarshal(body, &exotelResp); err != nil {
	// 	return nil, err
	// }

	// return &exotelResp.Call, nil
	return nil, nil
}

// Helper functions
func (s *Service) buildUpdateQuery(updates map[string]interface{}) string {
	var parts []string
	for key, value := range updates {
		switch v := value.(type) {
		case string:
			parts = append(parts, fmt.Sprintf("%s = '%s'", key, v))
		case int:
			parts = append(parts, fmt.Sprintf("%s = %d", key, v))
		case time.Time:
			parts = append(parts, fmt.Sprintf("%s = '%s'", key, v.Format("2006-01-02 15:04:05")))
		}
	}

	if len(parts) > 0 {
		return ", " + strings.Join(parts, ", ")
	}
	return ""
}

// GetCallsByOrderID returns all call records for a specific order
func (s *Service) GetCallHistoryByOrderID(ctx context.Context, orderID int) ([]dao.KiranaBazarUserCall, error) {
	var calls []dao.KiranaBazarUserCall

	// Query all calls for the given order ID, ordered by creation time (most recent first)
	if err := s.repository.Db.
		Where("order_id = ?", orderID).
		Order("created_at DESC").
		Find(&calls).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch calls for order %d: %w", orderID, err)
	}

	return calls, nil
}

// UploadMP3ToBlob uploads an MP3 file to Azure Blob Storage
func UploadMP3ToBlob(ctx context.Context, mp3Bytes []byte, blob string, container string) (string, error) {
	az := azure.AzureClient{
		Container: container,
		Blob:      blob,
		Client: &config.AzureConfig{
			AccountName: "d2rstorage2",
			AccountKey:  "****************************************************************************************", // Add your account key here
			URL:         "https://d2rstorage2.blob.core.windows.net/",
		},
	}

	blobUrl := fmt.Sprintf(`https://d2rstorage2.blob.core.windows.net/%s/%s`, container, blob)

	// Upload with audio/mpeg content type for MP3 files
	err := az.UploadFileToBlob(ctx, &mp3Bytes, "audio/mpeg")
	if err != nil {
		fmt.Println("failed to upload mp3, ", err)
		return "", err
	}

	return blobUrl, nil
}

// Helper function specifically for call recordings
func UploadCallRecordingToBlob(ctx context.Context, recordingBytes []byte, orderID int, callSid string) (string, error) {
	// Generate a unique blob name with timestamp
	timestamp := time.Now().Unix()
	blobName := fmt.Sprintf("call-recordings/order_%d/call_%s_%d.mp3", orderID, callSid, timestamp)

	// Use "call-recordings" as the container name
	container := "b2b"

	return UploadMP3ToBlob(ctx, recordingBytes, blobName, container)
}

// Updated processCallRecording method
func (s *Service) processCallRecording(ctx context.Context, call *dao.KiranaBazarUserCall, recordingUrl string) (string, error) {
	// Download recording from Exotel
	recordingData, err := s.downloadRecordingAsBytes(recordingUrl)
	if err != nil {
		return "", fmt.Errorf("failed to download recording: %w", err)
	}

	// Upload to Azure
	azureUrl, err := UploadCallRecordingToBlob(ctx, recordingData, *call.OrderID, *call.CallSid)
	if err != nil {
		return "", fmt.Errorf("failed to upload recording to Azure: %w", err)
	}

	// Update call record with Azure URL
	call.CallRecordingUrl = azureUrl
	call.UpdatedAt = time.Now().UnixMilli()

	query := fmt.Sprintf(`
	UPDATE kiranabazar_user_calls
	SET call_recording_url = '%s', updated_at = %d
	WHERE call_sid = '%s'
	`, azureUrl, call.UpdatedAt, *call.CallSid)
	_, err = s.repository.CustomQuery(nil, query)
	if err != nil {
		return "", fmt.Errorf("failed to update call record: %w", err)
	}

	return azureUrl, nil
}

// Download recording as bytes
func (s *Service) downloadRecordingAsBytes(url string) ([]byte, error) {
	// Create request with Exotel auth
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	// Add Basic Auth
	apiKey := "583c61f655bc040133d246ac9da048c76868a6a7736978d0"
	apiToken := "e870c5a15ccf6d2f830c39d050ef495b0d376bab0d7000ce"
	req.SetBasicAuth(apiKey, apiToken)

	// Make request
	client := &http.Client{
		Timeout: 5 * time.Minute, // Longer timeout for downloading recordings
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to download recording: status %d", resp.StatusCode)
	}

	// Read all bytes
	return io.ReadAll(resp.Body)
}

// Keep the tracking methods simple (no goroutines inside them)
func (s *Service) trackCallInitiated(ctx context.Context, userId string, callSid string, orderID int64, orderDetails *dao.GetOrderEssentials, fromNumber string, email string, purpose string, status string) error {
	if userId == "" {
		return fmt.Errorf("user ID is empty")
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Call Initiated", userId, map[string]interface{}{
			"distinct_id": orderDetails.UserID,
			"call_sid":    callSid,
			"order_id":    orderID,
			"seller":      orderDetails.Seller,
			"order_value": orderDetails.OrderValue,
			"call_from":   fromNumber,
			"email":       email,
			"purpose":     purpose,
			"status":      status,
			"user_id":     userId,
		}),
	})
	return nil
}

func (s *Service) trackCallResponseRecordingUrl(ctx context.Context, call *dao.KiranaBazarUserCall, callback *dto.ExotelStatusCallback, email string) error {
	var recordingUrl string
	orderDetails, err := GetOrderEssentials(s.repository, int64(*call.OrderID))
	if err != nil {
		return fmt.Errorf("failed to get order details: %w", err)
	}

	if callback.RecordingUrl != "" {
		recordingUrl, err = s.processCallRecording(ctx, call, callback.RecordingUrl)
		if err != nil {
			return fmt.Errorf("failed to download recording: %w", err)
		}
	}

	answeredBy, err := GetAnsweredBy(callback.CallSid)

	if err != nil {
		return fmt.Errorf("failed to get answered by: %w", err)
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Call Response Received", *orderDetails.UserID, map[string]interface{}{
			"distinct_id":        *orderDetails.UserID,
			"call_sid":           *call.CallSid,
			"order_id":           *call.OrderID,
			"seller":             orderDetails.Seller,
			"order_value":        orderDetails.OrderValue,
			"call_from":          call.FromNumber,
			"status":             callback.Status,
			"call_recording_url": recordingUrl,
			"call_duration":      safeParseCallDuration(callback),
			"email":              email,
			"purpose":            call.Purpose,
			"user_id":            *orderDetails.UserID,
			"leg_1_status":       safeParseLegStatus(callback.Legs, 0),
			"leg_1_duration":     safeParseLegCallDurationStatus(callback.Legs, 0),
			"leg_2_status":       safeParseLegStatus(callback.Legs, 1),
			"leg_2_duration":     safeParseLegCallDurationStatus(callback.Legs, 1),
			"answered_by":        answeredBy,
		}),
	})
	return nil
}

func safeParseCallDuration(callback *dto.ExotelStatusCallback) int {
	if callback.ConversationDuration != 0 {
		return callback.DialCallDuration
	}

	return 0
}

func safeParseLegStatus(callback []dto.ExotelCallLeg, index int) string {
	if len(callback) > 0 && callback[index].Status != "" {
		return callback[index].Status
	}

	return "leg_status_not_found"
}

func safeParseLegCallDurationStatus(callback []dto.ExotelCallLeg, index int) int {
	if len(callback) > 0 && callback[index].OnCallDuration != 0 {
		return callback[index].OnCallDuration
	}
	return 0
}

// Simple function to get call details from Exotel API
func GetExotelCallDetails(callSid string) (*dto.GetExotelCallResponse, error) {
	// Build URL
	url := fmt.Sprintf("https://api.exotel.com/v1/Accounts/%s/Calls/%s.json?details=true",
		"kirana10", callSid)

	// Create HTTP client with timeout
	client := &http.Client{Timeout: 30 * time.Second}

	// Create request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers (same as your curl command)
	req.Header.Set("Authorization", "Basic NTgzYzYxZjY1NWJjMDQwMTMzZDI0NmFjOWRhMDQ4Yzc2ODY4YTZhNzczNjk3OGQwOmU4NzBjNWExNWNjZjZkMmY4MzBjMzlkMDUwZWY0OTViMGQzNzZiYWIwZDcwMDBjZQ")
	req.Header.Set("Content-Type", "application/json")

	// Make request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status %d", resp.StatusCode)
	}

	// Parse JSON response
	var result dto.GetExotelCallResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &result, nil
}

// Even simpler - just get the AnsweredBy field
func GetAnsweredBy(callSid string) (string, error) {
	response, err := GetExotelCallDetails(callSid)
	if err != nil {
		return "", err
	}

	if response.Call.AnsweredBy == "" {
		return "unknown", nil
	}

	return response.Call.AnsweredBy, nil
}

func GetIVRCallDetails(callSid string) (*dto.CallDetailForSID, error) {

	url := fmt.Sprintf("https://api.exotel.com/v1/Accounts/kiranaclub1/Calls/%s.json", callSid)
	method := "GET"

	client := &http.Client{}
	req, err := http.NewRequest(method, url, nil)

	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	// hardcoded the token here 
	req.Header.Add("Authorization", "Basic ODEyZWZkOTNlYTdlZjNjYjRmMThkNzRiYzk3MmIyNjM3YzdjMjI0YjkwYWE4ZDYzOjA3MzUzNzM0ZmE2MGQyNTIzZDhmNTFhMTFjZTlhYjEwYzY2ZGIzMmUwZTFlMGE1ZQ==")
	req.Header.Add("accept", "application/json")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	var exotelResp dto.CallDetailForSID
	if err := json.Unmarshal(body, &exotelResp); err != nil {
		fmt.Println(err)
		return nil, err
	}

	return &exotelResp, nil
}
