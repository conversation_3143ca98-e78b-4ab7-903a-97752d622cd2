package service

import (
	"context"
	"fmt"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/utils"
	"strings"
)

func (s *Service) CreateBrandMeta(ctx context.Context, req *dto.UpdateBrandRequest) (*dto.Seller, error) {
	seller := expandToSellerStruct((*dto.CreateBrandRequest)(req))

	return seller, nil
}

func expandToSellerStruct(req *dto.CreateBrandRequest) *dto.Seller {
	brandName := toTitleCase(req.SellerCode)
	source := fmt.Sprintf("third_party_%s", req.SellerCode)
	screenName := fmt.Sprintf("BrandProfile_%s", toTitleCase(strings.Replace(req.SellerCode, "_", "", -1)))

	primaryColor := req.PrimaryColor
	if primaryColor == "" {
		primaryColor = "#D45339" // Default color
	}

	var sharedBannerArray []shared.BannerImageUrls

	if len(req.BannerImageUrls) > 0 {
		for _, url := range req.BannerImageUrls {

			if url.HasBulkOrderForm {
				sharedBannerArray = append(sharedBannerArray, shared.BannerImageUrls{
					MixpanelEventName: "Carousal in Brand Store",
					Type:              "",
					VideoUrl:          "",
					Url:               url.Url,
					Nav: &shared.Nav{
						Name:    "WebViewOld",
						NavType: "Redirect to WebviewOld",
						Params: map[string]interface{}{
							"uri":         url.BulkOrderFormUrl,
							"showHeader":  false,
							"screenTitle": "",
						},
					},
				})

			} else {
				sharedBannerArray = append(sharedBannerArray, shared.BannerImageUrls{
					MixpanelEventName: "Carousal in Brand Store",
					Type:              "",
					VideoUrl:          "",
					Url:               url.Url,
					Nav:               url.Nav,
				})
			}

		}
	}

	return &dto.Seller{
		PrimaryColor:    primaryColor,
		BannerImageUrls: sharedBannerArray,
		CarouselHeight:  nil, // Optional field
		Categories:      nil, // Empty by default, can be populated later
		CartScreenMeta: dto.CartScreen{
			Cta: dto.Cta{
				Icon:              "",
				Text:              "सारे आइटम देखें ",
				PrimaryColor:      utils.StrPtr("rgba(255, 255, 255, 1)"),
				SecondaryColor:    utils.StrPtr("rgba(107, 15, 237, 1)"),
				MixpanelEventName: "",
			},
			Config: map[string]interface{}{
				"header_gradient_colors": []string{
					"rgba(239, 228, 255, 1)",
					"rgba(255, 255, 255, 1)",
				},
				"header_text_color": "rgba(39, 1, 92, 1)",
				"btn_bg_color":      "rgba(206, 172, 255, 1)",
				"btn_text_color":    "rgba(44, 3, 102, 1)",
			},
			Rating:          "4.5",
			BannerImageUrls: nil,
		},
		Logo:       req.Logo,
		Name:       brandName,
		HeaderText: fmt.Sprintf("%s Store", brandName),
		Seller:     req.SellerCode,
		Screen:     screenName,
		BrandID:    fmt.Sprintf("brand_%s", req.SellerCode),
		Source:     source,
		Nav: &shared.Nav{
			Name: "OrderingModule",
			Params: map[string]interface{}{
				"params": map[string]string{
					"seller": req.SellerCode,
					"source": source,
				},
				"screen": "Products",
				"seller": req.SellerCode,
				"source": source,
			},
			NavType: "Redirect to Screen",
		},
		BrandCta: &dto.Cta{
			Nav: &shared.Nav{
				Name: "WebViewOld",
				Params: map[string]interface{}{
					"uri":         fmt.Sprintf("https://webapps.retailpulse.ai/brand?manufacturer=%s", strings.Replace(req.SellerCode, "_", "", -1)),
					"showHeader":  false,
					"screenTitle": "",
				},
				NavType: "Redirect to WebviewOld",
			},
			Icon:              "",
			Text:              "प्रोडक्ट मार्जिन देखें",
			MixpanelEventName: "",
		},
		HelpCentreCta: &dto.Cta{
			Nav: &shared.Nav{
				Name: "WebViewOld",
				Params: map[string]interface{}{
					"uri":         "https://webapps.retailpulse.ai/customer-support/home",
					"showHeader":  false,
					"screenTitle": "",
				},
				NavType: "Redirect to WebviewOld",
			},
			Icon:              "",
			Text:              "सहायता केंद्र",
			MixpanelEventName: "",
		},
		SideDrawer:             nil,
		KcFullFilled:           req.IsKcFullfilled,
		AddCashbackSourceId:    req.CashbackId,
		RedeemCashbackSourceId: req.RedeemId,
		BulkOrderForm:          req.BulkOrderForm,
	}
}
