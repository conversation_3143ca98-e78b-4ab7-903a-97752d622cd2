package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	processingstatus "kc/internal/ondc/service/orderStatus/processingStatus"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"
)

type AWBCourierMappingForShippingLabel struct {
	AWBNumber string
	Courier   string
}

type ShippingLabelData struct {
	ShippingAddress dao.UserAddress
	InvoiceNumber   string
	InvoiceDate     string
	CODAmount       float64
	Seller          string
	OrderValue      float64
	ShippingCharges float64
	Discount        float64
	Quantity        int
	RTOAddress      string
	COD             bool
	OrderID         int64
	DeadWeight      []float64
}

func (s *Service) GenerateShippingLabel(ctx context.Context, request *dto.GenerateShippingLabelRequest) (*dto.GenerateShippingLabelResponse, error) {
	orderID, err := strconv.ParseInt(request.Data.OrderID, 10, 64)
	if err != nil {
		return nil, err
	}
	orderInfo, err := GetOrderInfo(s.repository, orderID)
	if err != nil {
		return nil, err
	}
	orderDetails, err := GetOrderDetails(s.repository, request.Data.OrderID)
	if err != nil {
		return nil, err
	}
	orderPayments, err := GetOrderPayment(s.repository, request.Data.OrderID)
	if err != nil {
		return nil, err
	}

	orderStatus, err := GetOrderStatus(s.repository, orderID)
	if err != nil {
		fmt.Println("returning from here ")
		return nil, err
	}

	awbNumbers := orderStatus.GetAWBNumbers()

	acmfsl := []AWBCourierMappingForShippingLabel{}
	for _, awbNumber := range awbNumbers {
		acmfsl = append(acmfsl, AWBCourierMappingForShippingLabel{
			AWBNumber: awbNumber,
			Courier:   *orderStatus.Courier,
		})
	}

	sellerDetails, err := GetSellerInfo(s.repository, orderInfo.Seller)
	if err != nil {
		return nil, err
	}
	sellerShippingDetails := sellerDetails.GetShippingDetails()

	packageDetails, err := GetPackageDetails(s.repository, orderID)

	if err != nil {
		return nil, err
	}

	packageDimensions := packageDetails.GetPackageDetails()

	var weights []float64

	for _, item := range packageDimensions.Dimensions {
		weights = append(weights, item.Weight)
	}

	shippingLabelRequest, err := s.CreateShippingLabelRequest(ctx, acmfsl, ShippingLabelData{
		ShippingAddress: orderDetails.ShippingAddress,
		InvoiceNumber:   orderPayments.ExtInvoiceNumber,
		InvoiceDate:     time.Now().Format("2006-01-02"),
		CODAmount:       orderDetails.GetCodValue(),
		Seller:          orderInfo.Seller,
		OrderValue:      orderDetails.GetOrderValue(),
		ShippingCharges: orderDetails.GetShippingCharges(),
		Discount:        orderDetails.GetDiscountValue(),
		Quantity:        orderDetails.TotalItems,
		RTOAddress:      fmt.Sprintf("%s, %s", sellerShippingDetails.Address1, sellerShippingDetails.Address2),
		COD:             orderDetails.GetPaymentMode() == "COD",
		OrderID:         orderID,
		DeadWeight:      weights,
	})

	if err != nil {
		return nil, err
	}

	if len(shippingLabelRequest.Data) == 0 {
		return nil, fmt.Errorf("no awb numbers found for order %d", orderID)
	}
	shippingLabelResp, err := fetchShippingLabel(shippingLabelRequest)
	if err != nil {
		return nil, err
	}
	return &shippingLabelResp, nil
}

func getDeadWeight(index int, weights []float64) string {
	if index < 0 || index >= len(weights) {
		return "0.00" // or return an error indicator like "N/A"
	}
	return fmt.Sprintf("%.2f", weights[index])
}

func (s *Service) CreateShippingLabelRequest(ctx context.Context, awbCM []AWBCourierMappingForShippingLabel, sld ShippingLabelData) (dto.ShippingLabelRequestObject, error) {
	deliveryType := "Collect Cash on Delivery"
	paymentType := "COD"
	if !sld.COD {
		deliveryType = "[Do Not Collect Cash]"
		paymentType = "PREPAID"
	}
	requestObject := []dto.ShippingLabelRequestData{}
	for idx, mapping := range awbCM {
		requestObject = append(requestObject, dto.ShippingLabelRequestData{
			OrderFrom:    "Kirana Club",
			ProcessedBy:  mapping.Courier,
			AWBNumber:    mapping.AWBNumber,
			DeliveryType: deliveryType,
			OrderNumber:  fmt.Sprintf("KC_%06d", sld.OrderID),
			ShippingAddress: dto.ShippingAddress{
				Name:    *sld.ShippingAddress.Name,
				Address: *sld.ShippingAddress.Line,
				City:    *sld.ShippingAddress.District,
				State:   *sld.ShippingAddress.State,
				Pincode: *sld.ShippingAddress.PostalCode,
			},
			InvoiceDetails: dto.InvoiceDetails{
				OrderNumber:       fmt.Sprintf("KC_%06d", sld.OrderID),
				OrderDate:         sld.InvoiceDate,
				InvoiceNumber:     sld.InvoiceNumber,
				PaymentType:       paymentType,
				CollectCashAmount: sld.CODAmount,
				LabelGeneratedOn:  getISTTimestamp(),
				DeadWeight:        getDeadWeight(idx, sld.DeadWeight),
			},
			Items: []dto.ShippingLabelRequestItem{
				{
					ProductName:  fmt.Sprintf("%s-%d", sld.Seller, idx),
					SKU:          fmt.Sprintf("%s-%d", sld.Seller, idx),
					Quantity:     sld.Quantity,
					TotalInclGst: sld.OrderValue / float64(sld.Quantity),
				},
			},
			Charges: dto.Charges{
				ShippingCharges: sld.ShippingCharges,
				Discount:        sld.Discount,
				NetTotal:        sld.OrderValue,
			},
			ReturnAddress: dto.ReturnAddress{
				ShippedBy: sld.RTOAddress,
			},
			Note: "THIS IS AN AUTO-GENERATED LABEL AND DOES NOT NEED SIGNATURE",
		})
	}
	return dto.ShippingLabelRequestObject{
		Data: requestObject,
	}, nil
}

func getISTTimestamp() string {
	ist, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		// Fallback to UTC+5:30 if timezone loading fails
		ist = time.FixedZone("IST", 5*60*60+30*60)
	}

	return time.Now().In(ist).Format("2006-01-02 15:04:05")
}

func fetchShippingLabel(request dto.ShippingLabelRequestObject) (dto.GenerateShippingLabelResponse, error) {
	fmt.Println("calling shipping label external api")
	const (
		baseURL      = "https://asia-south1-op-d2r.cloudfunctions.net"
		endpoint     = "/shippingLabelGen"
		timeout      = 15 * time.Second
		maxRetries   = 3
		retryBackoff = 500 * time.Millisecond
	)

	var (
		resp       *http.Response
		respBody   []byte
		retryCount = 0
		lastErrMsg string
	)

	for retryCount <= maxRetries {
		payloadBytes, err := json.Marshal(request)
		if err != nil {
			return dto.GenerateShippingLabelResponse{}, fmt.Errorf("failed to marshal request: %w", err)
		}

		// Create request
		client := &http.Client{Timeout: timeout}
		req, err := http.NewRequest(http.MethodPost, baseURL+endpoint, bytes.NewReader(payloadBytes))
		if err != nil {
			return dto.GenerateShippingLabelResponse{}, fmt.Errorf("failed to create request: %w", err)
		}

		// Set headers
		req.Header.Set("Content-Type", "application/json")

		// Execute request
		resp, err = client.Do(req)

		if err == nil && resp.StatusCode == http.StatusOK {
			// Success - break out of retry loop
			defer resp.Body.Close()

			// Read response
			respBody, err = io.ReadAll(resp.Body)
			if err != nil {
				lastErrMsg = fmt.Sprintf("failed to read response body: %v", err)
				retryCount++
				time.Sleep(retryBackoff * time.Duration(retryCount))
				continue
			}

			respp := dto.GenerateShippingLabelResponse{}
			err = json.Unmarshal(respBody, &respp)
			if err != nil {
				return dto.GenerateShippingLabelResponse{}, fmt.Errorf("failed to unmarshal response: %w", err)
			}

			// Log response for debugging
			log.Printf("Shipping label generation response: %s", string(respBody))

			return respp, nil
		}

		// Handle error case
		if resp != nil {
			respBody, _ = io.ReadAll(resp.Body)
			resp.Body.Close()
			lastErrMsg = fmt.Sprintf("unexpected status code: %d, body: %s", resp.StatusCode, string(respBody))
		} else {
			lastErrMsg = fmt.Sprintf("failed to execute request: %v", err)
		}

		// Retry logic
		retryCount++
		if retryCount <= maxRetries {
			log.Printf("Retrying shipping label request (attempt %d of %d): %s", retryCount, maxRetries, lastErrMsg)
			time.Sleep(retryBackoff * time.Duration(retryCount))
		}
	}

	// Extract order number for slack notification (assuming it's in the first data item)
	orderNumber := "Unknown"
	if len(request.Data) > 0 {
		orderNumber = request.Data[0].OrderNumber
	}

	slack.SendSlackMessage(fmt.Sprintf("%s, Shipping label generation failed after %d retries, last error: %s", orderNumber, maxRetries, lastErrMsg))
	// All retries failed
	return dto.GenerateShippingLabelResponse{}, fmt.Errorf("after %d retries, last error: %s", maxRetries, lastErrMsg)
}

func (s *Service) GetShipmentCreatedLabels(ctx context.Context, req *dto.GetOrdersRequest) (*string, error) {
	if len(req.Status) > 0 && req.Status[0] != processingstatus.SHIPMENT_CREATED {
		return nil, fmt.Errorf("status should be SHIPMENT_CREATED")
	}
	req.Limit = 1000000
	req.Offset = 0
	// Preprocess the request for downloading the orders
	allOrders, err := s.GetOrders(ctx, req)
	if err != nil {
		return nil, err
	}
	allOrderIds := []int64{}
	for _, order := range allOrders.Data {
		oid, err := strconv.ParseInt(order.OrderID, 10, 64)
		if err != nil {
			return nil, err
		}
		allOrderIds = append(allOrderIds, oid)
	}
	query := fmt.Sprintf(`select printing_label from kiranabazar_order_details kop where order_id in (%s)`, strings.Trim(strings.Join(strings.Fields(fmt.Sprint(allOrderIds)), ","), "[]"))
	type OrderDetails struct {
		PrintingLabel string `json:"printing_label"`
	}
	orderDetails := []OrderDetails{}
	_, err = s.repository.CustomQuery(&orderDetails, query)
	if err != nil {
		return nil, err
	}
	urls := []string{}
	for _, order := range orderDetails {
		if order.PrintingLabel == "" {
			continue
		}
		urls = append(urls, order.PrintingLabel)
	}

	zipResponse, err := s.ConvertUrlsToZip(ctx, urls)
	if err != nil {
		return nil, err
	}
	return &zipResponse.Result.DownloadUrl, nil
}
