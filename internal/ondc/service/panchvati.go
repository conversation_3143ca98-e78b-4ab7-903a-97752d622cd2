package service

import (
	"context"
	"kc/internal/ondc/external/panchvati"
	"kc/internal/ondc/models/dto"
	"strconv"
)

func (s *Service) CheckPanchvatiServiceAbility(ctx context.Context, request *panchvati.CourierServiceAblityAPIRequest) (*dto.AppserviceAbilityAPIResponse, error) {
	tat, availability := panchvati.CheckServiceAbility(request.DeliveryPostCode)
	return &dto.AppserviceAbilityAPIResponse{
		Data: dto.AppServiceAbilityAPIData{
			Servicable:          availability,
			Message:             "",
			MinimumDeliveryDays: strconv.Itoa(tat),
		},
	}, nil
}
