package inventory

import (
	"context"
	"fmt"
	"kc/internal/ondc/service/inventory/models/dao"
	"strconv"
	"strings"
	"time"
)

func (s *InventoryService) GetSkusSalesData(numOfDays int) (interface{}, error) {
	ctx := context.Background()
	query := fmt.Sprintf(`
		SELECT 
			product_id,
			summary_date,
			added_quantity,
			deducted_quantity,
			closing_inventory
		FROM kiranabazar_daily_inventory_summary 
		WHERE summary_date >= DATE_SUB(CURDATE(), INTERVAL %d DAY)
			AND summary_date < CURDATE()
		ORDER BY product_id, summary_date`, numOfDays)

	data := []dao.KiranaBazarDailyInventorySummary{}
	_, err := s.repo.CustomQuery(&data, query)
	if err != nil {
		inventoryLogger.Error(ctx, "failed to get sales data for last %d days: %v", numOfDays, err)
		return nil, fmt.Errorf("failed to get sales data: %w", err)
	}
	inventoryLogger.Info(ctx, "fetched sales data for last %d days: %d records", numOfDays, len(data))

	productData := make(map[string][]dao.KiranaBazarDailyInventorySummary)
	for _, item := range data {
		productData[item.ProductID] = append(productData[item.ProductID], item)
	}

	var results []dao.KiranaBazarProductsInventoryAnalysisHistory
	analysisDate := time.Now().Format("2006-01-02") // Today's date without time

	query = `
	    INSERT INTO kiranabazar_product_inventory_analysis_history
		(product_id, analysis_type, analysis_date, analysis_period_days, quantity_sold, throughput_per_day, actual_sales_days)
	    VALUES 
	`

	updateClause := `
		ON DUPLICATE KEY UPDATE
		analysis_period_days = VALUES(analysis_period_days),
		quantity_sold = VALUES(quantity_sold),
		throughput_per_day = VALUES(throughput_per_day),
		actual_sales_days = VALUES(actual_sales_days)
	`

	values := make([]string, 0)
	args := make([]interface{}, 0)

	for productID, dailyData := range productData {
		// Calculate metrics
		totalQuantitySold := 0
		actualSalesDays := 0
		int64ProductId, err := strconv.ParseInt(productID, 10, 64)
		if err != nil {
			inventoryLogger.Error(ctx, "invalid product ID %s: %v", productID, err)
			continue
		}

		for _, day := range dailyData {
			totalQuantitySold += day.DeductedQuantity
			if day.DeductedQuantity > 0 {
				actualSalesDays++
			}
		}

		// Calculate throughput per day
		var throughputPerDay float64
		if actualSalesDays > 0 {
			throughputPerDay = float64(totalQuantitySold) / float64(numOfDays)
		}

		// Create analysis result
		result := dao.KiranaBazarProductsInventoryAnalysisHistory{
			ProductID:          int64ProductId,
			AnalysisType:       fmt.Sprintf("LAST_%d_DAYS_ANALYSIS", numOfDays),
			AnalysisDate:       time.Now(),
			AnalysisPeriodDays: numOfDays,
			QuantitySold:       totalQuantitySold,
			ThroughputPerDay:   throughputPerDay,
			ActualSalesDays:    actualSalesDays,
		}
		results = append(results, result)
		values = append(values, "(?, '?', '?', ?, ?, ?, ?)")
		args = append(args, int64ProductId, fmt.Sprintf("LAST_%d_DAYS_ANALYSIS", numOfDays), analysisDate, numOfDays, totalQuantitySold, throughputPerDay, actualSalesDays)
	}

	if len(values) == 0 {
		inventoryLogger.Info(ctx, "no sales data found for last %d days", numOfDays)
		return nil, nil
	}

	finalQuery := query + strings.Join(values, ", ") + " " + updateClause
	// Execute the batch insert with ON DUPLICATE KEY UPDATE
	executableQuery := s.buildQueryWithArgs(finalQuery, args)

	_, err = s.repo.CustomQuery(nil, executableQuery)
	if err != nil {
		inventoryLogger.Error(ctx, "failed to save sales analysis data for last %d days: %v", numOfDays, err)
		return nil, fmt.Errorf("failed to save sales analysis data: %w", err)
	}

	inventoryLogger.Info(ctx, "saved sales analysis data for last %d days: %d records", numOfDays, len(results))
	return results, nil
}
