package inventory

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/shared"
	inventoryDao "kc/internal/ondc/service/inventory/models/dao"
	"kc/internal/ondc/service/inventory/models/dto"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

// UpdateProductsInventory is the main entry point for inventory updates
// Handles both ADD and DEDUCT operations with proper transaction management
func (s *InventoryService) UpdateProductsInventory(ctx context.Context, cartItems []shared.SellerItems, inventoryOperation string) ([]inventoryDao.KiranaBazarProductsInventory, []inventoryDao.KiranaBazarProductsInventory, error) {
	if len(cartItems) == 0 {
		return []inventoryDao.KiranaBazarProductsInventory{}, []inventoryDao.KiranaBazarProductsInventory{}, nil
	}

	// Validate operation type
	if !s.isValidInventoryOperation(inventoryOperation) {
		return nil, nil, fmt.Errorf("invalid inventory operation: %s", inventoryOperation)
	}

	inventoryLogger.Info(ctx, "starting inventory update for %d products with operation: %s", len(cartItems), inventoryOperation)

	// Prepare product data with validation (sequential processing)
	cartProductData, err := s.prepareProductData(ctx, cartItems)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to prepare product data: %w", err)
	}

	// Get current inventory state (without locking for read operations)
	currentInventory, err := s.GetCurrentInventory(ctx, cartProductData.ProductIDs)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get current inventory: %w", err)
	}

	// Calculate updates and determine state changes
	updateRequests, result := s.calculateInventoryUpdates(ctx, currentInventory, cartProductData, inventoryOperation)

	if len(updateRequests) == 0 {
		inventoryLogger.Warn(ctx, "no valid products to update")
		return result.OOSProducts, result.InstockProducts, nil
	}

	// Execute updates asynchronously
	go func() {
		updateCtx := context.Background() // Use background context for async operation
		if err := s.executeInventoryUpdates(updateCtx, updateRequests); err != nil {
			inventoryLogger.Error(updateCtx, "async inventory update failed: %v", err)
			slack.SendSlackMessage(fmt.Sprintf("Inventory update failed: %v", err))
		}
	}()

	inventoryLogger.Info(ctx, "inventory update initiated for %d products, %d will be OOS, %d will be instock",
		len(updateRequests), len(result.OOSProducts), len(result.InstockProducts))

	return result.OOSProducts, result.InstockProducts, nil
}

// prepareProductData validates and processes input product data sequentially
func (s *InventoryService) prepareProductData(ctx context.Context, cartItems []shared.SellerItems) (*dto.PreparedProductData, error) {
	productIDs := make([]string, 0, len(cartItems))
	quantityMap := make(map[string]dto.ProductQuantity)

	for _, cartProduct := range cartItems {
		// Validate product ID
		productID, err := s.validateProductID(cartProduct.ID)
		if err != nil {
			inventoryLogger.Error(ctx, "invalid product ID %s: %v", cartProduct.ID, err)
			continue
		}

		// Parse product meta
		productMeta, err := s.parseProductMeta(cartProduct.Meta)
		if err != nil {
			inventoryLogger.Error(ctx, "failed to parse meta for product %s: %v", cartProduct.ID, err)
			continue
		}

		productIDs = append(productIDs, cartProduct.ID)
		quantityMap[cartProduct.ID] = dto.ProductQuantity{
			ProductID: productID,
			Quantity:  int(cartProduct.Quantity),
			PackSize:  productMeta.PackSize,
			CaseSize:  productMeta.CaseSize,
		}
	}

	if len(productIDs) == 0 {
		return nil, fmt.Errorf("no valid products found after validation")
	}

	return &dto.PreparedProductData{
		ProductIDs:  productIDs,
		QuantityMap: quantityMap,
	}, nil
}

// calculateInventoryUpdates determines what updates are needed and state changes
func (s *InventoryService) calculateInventoryUpdates(
	ctx context.Context,
	currentInventory []inventoryDao.KiranaBazarProductsInventory,
	cartProductData *dto.PreparedProductData,
	operation string,
) ([]dto.ProductQuantity, *dto.InventoryUpdateResult) {

	result := &dto.InventoryUpdateResult{
		OOSProducts:     make([]inventoryDao.KiranaBazarProductsInventory, 0),
		InstockProducts: make([]inventoryDao.KiranaBazarProductsInventory, 0),
	}

	updateRequests := make([]dto.ProductQuantity, 0, len(currentInventory))
	quantityMap := cartProductData.QuantityMap
	inventoryDataMap := make(map[string]*inventoryDao.KiranaBazarProductsInventory)
	for _, inventory := range currentInventory {
		inventoryDataMap[fmt.Sprint(inventory.ID)] = &inventory
	}

	for productId, productQuantity := range quantityMap {
		productKey := productId

		currentQty := 0
		inventory, exists := inventoryDataMap[productKey]
		if !exists {
			inventoryLogger.Warn(ctx, "product %s not found in inventory", productKey)
		}
		if inventory != nil {
			currentQty = inventory.DisplayQuantity
		}

		// Calculate pieces count (quantity * pack_size)
		operationQuantity := productQuantity.Quantity * productQuantity.PackSize
		newQuantity := currentQty

		switch operation {
		case INVENTORY_OPERATION_TYPES.ADD_INVENORY:
			newQuantity += operationQuantity
			// Check if product should be marked instock
			if currentQty <= 0 && newQuantity > 0 && inventory != nil {
				result.InstockProducts = append(result.InstockProducts, *inventory)
			}

		case INVENTORY_OPERATION_TYPES.DEDUCT_INVENTORY:
			newQuantity -= operationQuantity
			if newQuantity < 0 {
				newQuantity = 0
			}
			// Check if product should be marked OOS
			if newQuantity <= 0 && inventory != nil {
				result.OOSProducts = append(result.OOSProducts, *inventory)
			}

		default:
			inventoryLogger.Warn(ctx, "unknown operation %s for product %s", operation, productKey)
			continue
		}

		updateRequests = append(updateRequests, dto.ProductQuantity{
			ProductID:              productQuantity.ProductID,
			CurrentDisplayQuantity: currentQty,
			Quantity:               operationQuantity,
			PackSize:               productQuantity.PackSize,
			CaseSize:               productQuantity.CaseSize,
			InventoryOperation:     operation,
		})
	}

	return updateRequests, result
}

// executeInventoryUpdates performs the actual database updates with proper transaction management
func (s *InventoryService) executeInventoryUpdates(ctx context.Context, updateRequests []dto.ProductQuantity) error {
	if len(updateRequests) == 0 {
		return nil
	}

	inventoryLogger.Info(ctx, "executing inventory updates for %d products", len(updateRequests))

	// Begin transaction with timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	tx, err := s.repo.BeginTx(ctxWithTimeout)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Execute inventory count updates
	if err := s.batchUpdateInventoryCount(ctxWithTimeout, tx, updateRequests); err != nil {
		return fmt.Errorf("failed to update inventory counts: %w", err)
	}

	// Execute daily summary updates
	if err := s.batchUpdateDailySummary(ctxWithTimeout, tx, updateRequests); err != nil {
		return fmt.Errorf("failed to update daily summary: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	inventoryLogger.Info(ctx, "successfully completed inventory updates for %d products", len(updateRequests))
	return nil
}

// batchUpdateInventoryCount updates inventory quantities using efficient batch operations
func (s *InventoryService) batchUpdateInventoryCount(ctx context.Context, tx *gorm.DB, updateRequests []dto.ProductQuantity) error {
	if len(updateRequests) == 0 {
		return nil
	}

	// Build efficient batch update query using CASE statements
	query := `UPDATE kiranabazar_products_inventory SET display_quantity = CASE id `

	var args []interface{}
	var productIDs []interface{}

	// Build CASE statement for display_quantity
	for _, req := range updateRequests {
		switch req.InventoryOperation {
		case INVENTORY_OPERATION_TYPES.ADD_INVENORY:
			query += "WHEN ? THEN COALESCE(display_quantity, 0) + ? "
		case INVENTORY_OPERATION_TYPES.DEDUCT_INVENTORY:
			query += "WHEN ? THEN GREATEST(0, COALESCE(display_quantity, 0) - ?) "
		default:
			continue
		}
		args = append(args, req.ProductID, req.Quantity)
		productIDs = append(productIDs, req.ProductID)
	}

	query += "ELSE display_quantity END, updated_at = NOW() "

	// Build WHERE clause
	placeholders := make([]string, len(productIDs))
	for i := range placeholders {
		placeholders[i] = "?"
	}
	whereClause := fmt.Sprintf("WHERE id IN (%s)", strings.Join(placeholders, ","))

	finalQuery := query + whereClause
	allArgs := append(args, productIDs...)

	// Use buildQueryWithArgs for consistent query building
	executableQuery := s.buildQueryWithArgs(finalQuery, allArgs)

	result := tx.Exec(executableQuery)
	if result.Error != nil {
		return fmt.Errorf("batch inventory update failed: %w", result.Error)
	}

	inventoryLogger.Info(ctx, "batch inventory update affected %d rows", result.RowsAffected)
	return nil
}

// batchUpdateDailySummary updates daily inventory summary with proper MySQL upsert
func (s *InventoryService) batchUpdateDailySummary(ctx context.Context, tx *gorm.DB, updateRequests []dto.ProductQuantity) error {
	if len(updateRequests) == 0 {
		return nil
	}

	currentDate := time.Now().Format("2006-01-02")

	// Build batch upsert query
	query := `
        INSERT INTO kiranabazar_daily_inventory_summary 
        (summary_date, product_id, added_quantity, deducted_quantity, closing_inventory, created_at, updated_at)
        VALUES `

	updateClause := `
        ON DUPLICATE KEY UPDATE
            added_quantity = added_quantity + VALUES(added_quantity),
            deducted_quantity = deducted_quantity + VALUES(deducted_quantity),
            closing_inventory = VALUES(closing_inventory),
            updated_at = NOW()`

	var values []string
	var args []interface{}

	for _, req := range updateRequests {
		var addedQty, deductedQty int
		newClosingInventory := req.CurrentDisplayQuantity

		switch req.InventoryOperation {
		case INVENTORY_OPERATION_TYPES.ADD_INVENORY:
			addedQty = req.Quantity
			newClosingInventory += req.Quantity
		case INVENTORY_OPERATION_TYPES.DEDUCT_INVENTORY:
			deductedQty = req.Quantity
			newClosingInventory -= req.Quantity
			if newClosingInventory < 0 {
				newClosingInventory = 0
			}
		default:
			continue
		}

		values = append(values, "('?', ?, ?, ?, ?, NOW(), NOW())")
		args = append(args, currentDate, req.ProductID, addedQty, deductedQty, newClosingInventory)
	}

	if len(values) == 0 {
		inventoryLogger.Warn(ctx, "no valid daily summary updates to perform")
		return nil
	}

	finalQuery := query + strings.Join(values, ", ") + updateClause
	executableQuery := s.buildQueryWithArgs(finalQuery, args)

	result := tx.Exec(executableQuery)
	if result.Error != nil {
		return fmt.Errorf("daily summary update failed: %w", result.Error)
	}

	inventoryLogger.Info(ctx, "daily summary update affected %d rows", result.RowsAffected)
	return nil
}

// Helper functions for better maintainability
func (s *InventoryService) isValidInventoryOperation(operation string) bool {
	return operation == INVENTORY_OPERATION_TYPES.ADD_INVENORY ||
		operation == INVENTORY_OPERATION_TYPES.DEDUCT_INVENTORY
}

func (s *InventoryService) parseProductMeta(metaBytes []byte) (*shared.KiranaBazarProductMeta, error) {
	var meta shared.KiranaBazarProductMeta
	if err := json.Unmarshal(metaBytes, &meta); err != nil {
		return nil, fmt.Errorf("failed to unmarshal product meta: %w", err)
	}
	return &meta, nil
}

func (s *InventoryService) validateProductID(productIDStr string) (int64, error) {
	productID, err := strconv.ParseInt(productIDStr, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("invalid product ID format: %w", err)
	}
	if productID <= 0 {
		return 0, fmt.Errorf("product ID must be positive")
	}
	return productID, nil
}
