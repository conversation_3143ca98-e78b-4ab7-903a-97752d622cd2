package inventory

import (
	"context"
	"errors"
	"fmt"
	"kc/internal/ondc/service/inventory/models/dao"
	"kc/internal/ondc/service/inventory/models/dto"
	"kc/internal/ondc/service/products"
	"strconv"
	"time"
)

func (s *InventoryService) GetProductInventoryByID(ctx context.Context, productId interface{}) (*dao.KiranaBazarProductsInventory, bool) {
	// Parse the productId to uint
	productIdUint, err := parseID(productId)
	if err != nil {
		inventoryLogger.Error(ctx, "invalid product ID %v: %v", productId, err)
		return nil, false
	}

	inventory := &dao.KiranaBazarProductsInventory{}
	query := fmt.Sprintf(`SELECT * FROM kiranabazar_products_inventory WHERE id = %d`, productIdUint)
	_, err = s.repo.CustomQuery(inventory, query)
	if err != nil {
		inventoryLogger.Error(ctx, "failed to find inventory for product ID %d: %v", productIdUint, err)
		return nil, false
	}
	return inventory, true
}

// Mark Product OOS in inventory
func (s *InventoryService) MarkProductOOS(ctx context.Context, productId interface{}, seller, updatedBy string) error {
	currentInventory, exists := s.GetProductInventoryByID(ctx, productId) // Ensure product exists
	if !exists {
		errMsg := fmt.Sprintf("product ID %v does not exist in inventory", productId)
		inventoryLogger.Error(ctx, errMsg)
		return errors.New(errMsg)
	}
	// Check if product is already OOS
	if currentInventory.DisplayQuantity == 0 {
		inventoryLogger.Info(ctx, "product ID %d is already marked as OOS", productId)
		return nil // No action needed if already OOS
	}

	productIdUint, err := parseID(productId)
	if err != nil {
		inventoryLogger.Error(ctx, "invalid product ID %v: %v", productId, err)
		return fmt.Errorf("invalid product ID %v: %w", productId, err)
	}
	inventoryLogger.Info(ctx, "marking product ID %d as OOS", productIdUint)
	// Begin transaction
	tx, err := s.repo.BeginTx(ctx)
	if err != nil {
		inventoryLogger.Error(ctx, "failed to begin transaction for marking product OOS: %v", err)
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	// Update inventory to mark as OOS
	updateQuery := `UPDATE kiranabazar_products_inventory
		SET display_quantity = 0, updated_at = NOW(), updated_by = '?'
		WHERE id = ?`

	updateArgs := []interface{}{updatedBy, productIdUint}
	executableUpdateQuery := s.buildQueryWithArgs(updateQuery, updateArgs)
	result := tx.Exec(executableUpdateQuery)
	if result.Error != nil {
		inventoryLogger.Error(ctx, "failed to update inventory for product ID %d: %v", productIdUint, result.Error)
		return fmt.Errorf("failed to update inventory: %w", result.Error)
	}

	// Insert inventory update record
	insertQuery := `INSERT INTO kiranabazar_seller_inventory_updates
		(product_id, ` + "`type`" + `, quantity, inventory_before, inventory_after, seller, email, comment, updated_at)
		VALUES (?, '?', 0, ?, 0, '?', '?', 'Marked as OOS', NOW())`
	insertArgs := []interface{}{
		productIdUint, "OOS", currentInventory.DisplayQuantity, seller, updatedBy,
	}
	executableInsertQuery := s.buildQueryWithArgs(insertQuery, insertArgs)
	insertResult := tx.Exec(executableInsertQuery)
	if insertResult.Error != nil {
		inventoryLogger.Error(ctx, "failed to create inventory update entry for product ID %d: %v", productIdUint, insertResult.Error)
		return fmt.Errorf("failed to create inventory update record: %w", insertResult.Error)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		inventoryLogger.Error(ctx, "failed to commit marking product OOS: %v", err)
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	inventoryLogger.Info(ctx, "successfully marked product ID %d as OOS", productIdUint)
	return nil
}

// AddProductsInventory adds inventory for new stock/rto-delivered products with structured request/response
func (s *InventoryService) AddProductsInventory(ctx context.Context, request *dto.AddInventoryRequest) (*dto.AddInventoryResponse, error) {
	startTime := time.Now()

	// Validate request
	if err := request.Validate(); err != nil {
		return &dto.AddInventoryResponse{
			Success:        false,
			Error:          fmt.Sprintf("validation failed: %v", err),
			ProcessingTime: time.Since(startTime).String(),
		}, err
	}

	inventoryLogger.Info(ctx, "processing inventory addition request for product ID %d with %d %s",
		request.ProductID, request.Quantity, request.Unit)

	// Check if the product exists in the inventory
	inventory, exists := s.GetProductInventoryByID(ctx, request.ProductID)
	if !exists {
		errMsg := fmt.Sprintf("product ID %d does not exist in inventory", request.ProductID)
		inventoryLogger.Error(ctx, errMsg)
		return &dto.AddInventoryResponse{
			Success:        false,
			Error:          errMsg,
			ProcessingTime: time.Since(startTime).String(),
		}, errors.New(errMsg)
	}

	// Get product meta for case size and pack size
	productData, exists := products.GetProductByID(request.ProductID)
	if !exists {
		errMsg := fmt.Sprintf("failed to get product data for product ID %d", request.ProductID)
		inventoryLogger.Error(ctx, errMsg)
		return &dto.AddInventoryResponse{
			Success:        false,
			Error:          errMsg,
			ProcessingTime: time.Since(startTime).String(),
		}, errors.New(errMsg)
	}

	// Calculate pieces count based on case size and pack size
	caseSize := productData.MetaProperties.CaseSize
	packSize := productData.MetaProperties.PackSize
	piecesCount := getPiecesCount(request.Quantity, caseSize, packSize, request.Unit)
	updatedInventoryQuantity := inventory.DisplayQuantity + piecesCount

	if request.AdditionType == "INVENTORY_OVERRIDE" {
		updatedInventoryQuantity = piecesCount // For override, set directly to pieces count
	}

	inventoryLogger.Info(ctx, "calculated pieces count for product ID %d: %d (%s: %d, case_size: %d, pack_size: %d)",
		request.ProductID, piecesCount, request.Unit, request.Quantity, caseSize, packSize)

	// Execute the update
	updateResult, err := s.executeInventoryAddition(ctx, request, inventory, productData, piecesCount, updatedInventoryQuantity)
	if err != nil {
		return &dto.AddInventoryResponse{
			Success:        false,
			Error:          err.Error(),
			ProcessingTime: time.Since(startTime).String(),
		}, err
	}

	// Determine if product should be marked as instock
	shouldMarkInstock := (inventory.DisplayQuantity == 0 || (productData.IsOOS != nil && *productData.IsOOS)) && updatedInventoryQuantity > 0

	response := &dto.AddInventoryResponse{
		Success:           true,
		ShouldMarkInstock: shouldMarkInstock,
		InventoryUpdate:   updateResult,
		ProcessingTime:    time.Since(startTime).String(),
	}

	inventoryLogger.Info(ctx, "successfully processed inventory addition for product ID %d. New quantity: %d, Instock status change: %v",
		request.ProductID, updatedInventoryQuantity, shouldMarkInstock)

	return response, nil
}

// executeInventoryAddition performs the actual database operations
func (s *InventoryService) executeInventoryAddition(ctx context.Context, request *dto.AddInventoryRequest,
	inventory *dao.KiranaBazarProductsInventory, productData *products.Product,
	piecesCount, updatedInventoryQuantity int) (*dto.InventoryUpdateDetails, error) {

	// Begin transaction
	tx, err := s.repo.BeginTx(ctx)
	if err != nil {
		inventoryLogger.Error(ctx, "failed to begin transaction for updating inventory: %v", err)
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Use INSERT ... ON DUPLICATE KEY UPDATE to handle both new and existing inventory records
	upsertQuery := `
	 INSERT INTO kiranabazar_products_inventory 
	 (id, display_quantity, updated_at, updated_by, seller, created_at) 
	 VALUES (?, ?, NOW(), '?', '?',  NOW())
	 ON DUPLICATE KEY UPDATE 
		 display_quantity = ?,
		 updated_at = NOW(),
		 updated_by = '?'`

	upsertArgs := []interface{}{
		request.ProductID,        // id for INSERT
		updatedInventoryQuantity, // display_quantity for INSERT
		request.UpdatedBy,        // updated_by for INSERT
		request.Seller,           // seller for INSERT
		updatedInventoryQuantity, // display_quantity for UPDATE
		request.UpdatedBy,        // updated_by for UPDATE
	}

	executableUpdateQuery := s.buildQueryWithArgs(upsertQuery, upsertArgs)

	result := tx.Exec(executableUpdateQuery)
	if result.Error != nil {
		inventoryLogger.Error(ctx, "failed to update inventory for product ID %d: %v", request.ProductID, result.Error)
		return nil, fmt.Errorf("failed to update inventory: %w", result.Error)
	}

	// Insert inventory update record
	var recordID int64
	insertQuery := `INSERT INTO kiranabazar_seller_inventory_updates 
        (product_id,` + "`type`" + ` , quantity, inventory_before, inventory_after, seller, email, comment, updated_at)
        VALUES (?, '?', ?, ?, ?, '?', '?', '?', NOW())`

	insertArgs := []interface{}{
		request.ProductID, request.AdditionType, piecesCount, inventory.DisplayQuantity,
		updatedInventoryQuantity, request.Seller, request.UpdatedBy, request.Comment,
	}
	executableInsertQuery := s.buildQueryWithArgs(insertQuery, insertArgs)

	insertResult := tx.Exec(executableInsertQuery)
	if insertResult.Error != nil {
		inventoryLogger.Error(ctx, "failed to create inventory update entry for product ID %d: %v", request.ProductID, insertResult.Error)
		return nil, fmt.Errorf("failed to create inventory update record: %w", insertResult.Error)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		inventoryLogger.Error(ctx, "failed to commit inventory addition: %v", err)
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Build response details
	updateDetails := &dto.InventoryUpdateDetails{
		ProductID:               request.ProductID,
		Quantity:                request.Quantity,
		Unit:                    request.Unit,
		PiecesAdded:             piecesCount,
		InventoryBefore:         inventory.DisplayQuantity,
		InventoryAfter:          updatedInventoryQuantity,
		CaseSize:                productData.MetaProperties.CaseSize,
		PackSize:                productData.MetaProperties.PackSize,
		AdditionType:            request.AdditionType,
		Seller:                  request.Seller,
		UpdatedBy:               request.UpdatedBy,
		Comment:                 request.Comment,
		UpdatedAt:               time.Now(),
		InventoryUpdateRecordID: recordID,
	}

	return updateDetails, nil
}

// BatchAddProductsInventory handles multiple inventory additions in a single operation
func (s *InventoryService) BatchAddProductsInventory(ctx context.Context, request *dto.BatchAddInventoryRequest) (*dto.BatchAddInventoryResponse, error) {
	startTime := time.Now()

	if len(request.Items) == 0 {
		return &dto.BatchAddInventoryResponse{
			Success:        false,
			ProcessingTime: time.Since(startTime).String(),
			Errors:         []string{"no items provided for batch processing"},
		}, fmt.Errorf("no items provided")
	}

	batchID := request.BatchID
	if batchID == "" {
		batchID = fmt.Sprintf("batch_%d", time.Now().Unix())
	}

	inventoryLogger.Info(ctx, "processing batch inventory addition with %d items, batch ID: %s", len(request.Items), batchID)

	results := make([]dto.AddInventoryResponse, 0, len(request.Items))
	var processedCount, failedCount int
	var errors []string

	for i, item := range request.Items {
		// Override UpdatedBy if provided at batch level
		if request.UpdatedBy != "" {
			item.UpdatedBy = request.UpdatedBy
		}

		result, err := s.AddProductsInventory(ctx, &item)
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("item %d (product %d): %v", i+1, item.ProductID, err))
		} else {
			processedCount++
		}

		results = append(results, *result)
	}

	response := &dto.BatchAddInventoryResponse{
		Success:        failedCount == 0,
		ProcessedCount: processedCount,
		FailedCount:    failedCount,
		Results:        results,
		BatchID:        batchID,
		ProcessingTime: time.Since(startTime).String(),
		Errors:         errors,
	}

	inventoryLogger.Info(ctx, "completed batch inventory addition: %d processed, %d failed, batch ID: %s",
		processedCount, failedCount, batchID)

	return response, nil
}

func parseID(id interface{}) (uint, error) {
	switch v := id.(type) {
	case string:
		parsed, err := strconv.ParseUint(v, 10, 32)
		if err != nil {
			return 0, fmt.Errorf("invalid ID format: %v", err)
		}
		return uint(parsed), nil
	case int:
		if v < 0 {
			return 0, fmt.Errorf("negative ID not allowed: %d", v)
		}
		return uint(v), nil
	case int64:
		if v < 0 {
			return 0, fmt.Errorf("negative ID not allowed: %d", v)
		}
		return uint(v), nil
	case uint:
		return v, nil
	case uint64:
		if v > uint64(^uint(0)) {
			return 0, fmt.Errorf("ID exceeds maximum uint value: %d", v)
		}
		return uint(v), nil
	default:
		return 0, fmt.Errorf("invalid ID type: %T", id)
	}
}

func getPiecesCount(quantity, caseSize, packSize int, unit string) int {
	switch unit {
	case dto.INVENTORY_OPERATION_UNITS.CASES:
		return quantity * caseSize * packSize
	case dto.INVENTORY_OPERATION_UNITS.PACKS:
		return quantity * packSize
	case dto.INVENTORY_OPERATION_UNITS.PIECES:
		return quantity * 1
	default:
		return 0
	}
}
