package dao

import "time"

type KiranaBazarProductsInventory struct {
	ID                uint64    `json:"id"`
	DisplayQuantity   int       `json:"display_quantity"`
	LowStockThreshold int       `json:"low_stock_threshold"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	UpdatedBy         string    `json:"updated_by"`
	Seller            string    `json:"seller"`
}

func (KiranaBazarProductsInventory) TableName() string {
	return "kiranabazar_products_inventory"
}

type KiranaBazarDailyInventorySummary struct {
	ID               uint64    `json:"id"`
	SummaryDate      time.Time `json:"summary_date"`      // Date for which the summary is created
	ProductID        string    `json:"product_id"`        // Product ID
	AddedQuantity    int       `json:"added_quantity"`    // Quantity added on this date
	DeductedQuantity int       `json:"deducted_quantity"` // Quantity deducted on this date
	ClosingInventory int       `json:"closing_inventory"` // Closing inventory after additions and deductions
	CreatedAt        time.Time `json:"created_at"`        // Timestamp when the record was created
	UpdatedAt        time.Time `json:"updated_at"`        // Timestamp when the record was last updated
}

func (KiranaBazarDailyInventorySummary) TableName() string {
	return "kiranabazar_daily_inventory_summary"
}

type KiranaBazarSellerInventoryUpdates struct {
	ID              uint64    `json:"id"`
	ProductID       int64     `json:"product_id"`       // Product ID
	Type            string    `json:"type"`             // Type of update (NEW_STOCK, RTO_DELIVERED, INVENTORY_OVERRIDE)
	Quantity        int       `json:"quantity"`         // Quantity updated
	InventoryBefore int       `json:"inventory_before"` // Inventory before update
	InventoryAfter  int       `json:"inventory_after"`  // Inventory after update
	Seller          string    `json:"seller"`           // Seller ID
	Email           string    `json:"email"`            // Seller email
	Comment         string    `json:"comment"`          // Additional comments
	UpdatedAt       time.Time `json:"updated_at"`       // Timestamp of the update
}

func (KiranaBazarSellerInventoryUpdates) TableName() string {
	return "kiranabazar_seller_inventory_updates"
}

type KiranaBazarProductsInventoryAnalysisHistory struct {
	ID                 uint64    `json:"id"`
	ProductID          int64     `json:"product_id"`           // Product ID
	AnalysisType       string    `json:"analysis_type"`        // Type of analysis (e.g., LAST_7_DAYS, LAST_10_DAYS)
	AnalysisDate       time.Time `json:"analysis_date"`        // Date of analysis
	AnalysisPeriodDays int       `json:"analysis_period_days"` // Number of days for analysis
	QuantitySold       int       `json:"quantity_sold"`        // Total quantity sold in the analysis period
	ThroughputPerDay   float64   `json:"throughput_per_day"`   // Average throughput per day
	ActualSalesDays    int       `json:"actual_sales_days"`    // Number of days with actual sales
	CreatedAt          time.Time `json:"created_at"`           // Timestamp when the record was created
	UpdatedAt          time.Time `json:"updated_at"`           // Timestamp when the record was last updated
}

func (KiranaBazarProductsInventoryAnalysisHistory) TableName() string {
	return "kiranabazar_product_inventory_analysis_history"
}
