package dto

import (
	"fmt"
	"kc/internal/ondc/service/inventory/models/dao"
	"time"
)

type ProductQuantity struct {
	ProductID              int64  `json:"product_id"`
	CurrentDisplayQuantity int    `json:"current_display_quantity"` // Current display quantity in inventory
	Quantity               int    `json:"quantity"`
	CaseSize               int    `json:"case_size"`           // Case size for the product
	PackSize               int    `json:"pack_size"`           // Pack size for the product
	InventoryOperation     string `json:"inventory_operation"` // Operation type (ADD_INVENTORY, DEDUCT_INVENTORY)
}

// InventoryUpdateResult contains the result of inventory operations
type InventoryUpdateResult struct {
	OOSProducts     []dao.KiranaBazarProductsInventory
	InstockProducts []dao.KiranaBazarProductsInventory
	UpdatedCount    int64
	Errors          []error
}

// PreparedProductData holds validated and processed product information
type PreparedProductData struct {
	ProductIDs  []string
	QuantityMap map[string]ProductQuantity
}

// AddInventoryRequest represents the request structure for adding inventory
type AddInventoryRequest struct {
	ProductID    int64  `json:"product_id"`
	Quantity     int    `json:"quantity"`
	Unit         string `json:"unit"` // e.g., "CASES", "PACKS",  "PIECES"
	AdditionType string `json:"addition_type"`
	Seller       string `json:"seller"`
	UpdatedBy    string `json:"updated_by"`
	Comment      string `json:"comment"`
}

// Validate performs basic validation on the request
func (r *AddInventoryRequest) Validate() error {
	if r.ProductID <= 0 {
		return fmt.Errorf("product ID must be greater than 0")
	}
	if r.Quantity <= 0 {
		return fmt.Errorf("carton count must be greater than 0")
	}
	if r.AdditionType == "" {
		return fmt.Errorf("addition type is required")
	}
	if r.Seller == "" {
		return fmt.Errorf("seller is required")
	}
	if r.UpdatedBy == "" {
		return fmt.Errorf("updated by is required")
	}
	if r.Unit == "" {
		return fmt.Errorf("unit is required")
	}
	if r.Unit != INVENTORY_OPERATION_UNITS.CASES &&
		r.Unit != INVENTORY_OPERATION_UNITS.PACKS &&
		r.Unit != INVENTORY_OPERATION_UNITS.PIECES {
		return fmt.Errorf("unit must be one of: %s, %s, %s", INVENTORY_OPERATION_UNITS.CASES, INVENTORY_OPERATION_UNITS.PACKS, INVENTORY_OPERATION_UNITS.PIECES)
	}
	return nil
}

// AddInventoryResponse represents the response structure for adding inventory
type AddInventoryResponse struct {
	Success           bool                    `json:"success"`
	ShouldMarkInstock bool                    `json:"should_mark_instock"`
	InventoryUpdate   *InventoryUpdateDetails `json:"inventory_update"`
	Error             string                  `json:"error,omitempty"`
	ProcessingTime    string                  `json:"processing_time"`
}

// InventoryUpdateDetails contains detailed information about the inventory update
type InventoryUpdateDetails struct {
	ProductID               int64     `json:"product_id"`
	Quantity                int       `json:"quantity"`
	Unit                    string    `json:"unit"` // e.g., "CASES", "PACKS", "PIECES"
	PiecesAdded             int       `json:"pieces_added"`
	InventoryBefore         int       `json:"inventory_before"`
	InventoryAfter          int       `json:"inventory_after"`
	CaseSize                int       `json:"case_size"`
	PackSize                int       `json:"pack_size"`
	AdditionType            string    `json:"addition_type"`
	Seller                  string    `json:"seller"`
	UpdatedBy               string    `json:"updated_by"`
	Comment                 string    `json:"comment"`
	UpdatedAt               time.Time `json:"updated_at"`
	InventoryUpdateRecordID int64     `json:"inventory_update_record_id,omitempty"`
}

// BatchAddInventoryRequest for handling multiple products at once
type BatchAddInventoryRequest struct {
	Items     []AddInventoryRequest `json:"items" validate:"required,min=1,max=100"`
	BatchID   string                `json:"batch_id,omitempty"`
	UpdatedBy string                `json:"updated_by" validate:"required,email"`
}

// BatchAddInventoryResponse for batch operations
type BatchAddInventoryResponse struct {
	Success        bool                   `json:"success"`
	ProcessedCount int                    `json:"processed_count"`
	FailedCount    int                    `json:"failed_count"`
	Results        []AddInventoryResponse `json:"results"`
	BatchID        string                 `json:"batch_id"`
	ProcessingTime string                 `json:"processing_time"`
	Errors         []string               `json:"errors,omitempty"`
}

var INVENTORY_OPERATION_UNITS = struct {
	CASES  string
	PIECES string
	PACKS  string
}{
	CASES:  "CASES",
	PIECES: "PIECES",
	PACKS:  "PACKS",
}