package inventory

import (
	"context"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/infrastructure/logging"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/inventory/models/dao"
	"kc/internal/ondc/service/inventory/models/dto"
	"strings"

	"gorm.io/gorm"
)

var inventoryLogger = logging.New("inventory")

// InventoryService handles inventory operations
type InventoryService struct {
	repo                *sqlRepo.Repository
	mixpanel            *mixpanelRepo.Repository
	lowProductThreshold int
}

// NewInventoryService creates a new inventory service
func NewInventoryService(repo *sqlRepo.Repository, mixpanel *mixpanelRepo.Repository) *InventoryService {
	return &InventoryService{
		repo:                repo,
		mixpanel:            mixpanel,
		lowProductThreshold: 10,
	}
}

// GetCurrentInventory fetches current inventory for given product IDs in single query
func (s *InventoryService) GetCurrentInventory(ctx context.Context, productIDs []string) ([]dao.KiranaBazarProductsInventory, error) {
	if len(productIDs) == 0 {
		return []dao.KiranaBazarProductsInventory{}, nil
	}

	// Build placeholders for IN clause
	placeholders := make([]string, len(productIDs))
	for i := range placeholders {
		placeholders[i] = "?"
	}

	query := fmt.Sprintf(`SELECT *
		FROM kiranabazar_products_inventory 
		WHERE id IN (%s)`, strings.Join(placeholders, ","))

	// Convert productIDs to interface{} for query
	args := make([]interface{}, len(productIDs))
	for i, id := range productIDs {
		args[i] = id
	}

	// Build final query with values
	finalQuery := s.buildQueryWithArgs(query, args)

	var inventoryData []dao.KiranaBazarProductsInventory
	_, err := s.repo.CustomQuery(&inventoryData, finalQuery)
	if err != nil {
		inventoryLogger.Error(ctx, "failed to get current inventory: %v", err)
		return nil, fmt.Errorf("failed to get current inventory: %w", err)
	}

	inventoryLogger.Info(ctx, "fetched current inventory for %d products", len(inventoryData))
	return inventoryData, nil
}


// updateInventoryBatch updates inventory for multiple products in single query
func (s *InventoryService) updateInventoryBatch(ctx context.Context, productQuantities []dto.ProductQuantity) {
	if len(productQuantities) == 0 {
		return
	}

	inventoryLogger.Info(ctx, "starting batch inventory update for %d products", len(productQuantities))

	// Begin transaction
	tx, err := s.repo.BeginTx(ctx)
	if err != nil {
		inventoryLogger.Error(ctx, "failed to begin transaction: %v", err)
		return
	}
	defer tx.Rollback()

	// Build single update query using CASE statements
	err = s.executeBatchUpdate(ctx, tx, productQuantities)
	if err != nil {
		inventoryLogger.Error(ctx, "failed to execute batch update: %v", err)
		slack.SendSlackMessage(fmt.Sprintf("failed to execute batch update: %v", err))
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		inventoryLogger.Error(ctx, "failed to commit batch update: %v", err)
		slack.SendSlackMessage(fmt.Sprintf("failed to commit batch update: %v", err))
		return
	}

	inventoryLogger.Info(ctx, "successfully completed batch inventory update for %d products", len(productQuantities))
}

// executeBatchUpdate performs single SQL update for all products
func (s *InventoryService) executeBatchUpdate(ctx context.Context, tx *gorm.DB, productQuantities []dto.ProductQuantity) error {
	// Build single query with CASE statements
	query := `UPDATE kiranabazar_products_inventory 
		SET 
			inventory_quantity = CASE id `

	var args []interface{}

	// Add CASE conditions for inventory updates
	for _, pq := range productQuantities {
		query += "WHEN ? THEN GREATEST(0, COALESCE(inventory_quantity, 0) - ?) "
		args = append(args, pq.ProductID, pq.Quantity)
	}

	query += "END, updated_at = NOW() WHERE id IN ("

	// Add WHERE clause
	for i, pq := range productQuantities {
		if i > 0 {
			query += ", "
		}
		query += "?"
		args = append(args, pq.ProductID)
	}
	query += ")"

	// Execute batch update
	result := tx.Exec(query, args...)
	if result.Error != nil {
		return fmt.Errorf("batch update failed: %w", result.Error)
	}

	inventoryLogger.Info(ctx, "batch update affected %d rows", result.RowsAffected)
	return nil
}

// buildQueryWithArgs replaces placeholders with actual values
func (s *InventoryService) buildQueryWithArgs(query string, args []interface{}) string {
	result := query
	for _, arg := range args {
		result = strings.Replace(result, "?", fmt.Sprintf("%v", arg), 1)
	}
	return result
}

// GetProductInventory gets current inventory for a single product (helper method)
func (s *InventoryService) GetProductInventory(ctx context.Context, productID string) (inventoryInfo *dao.KiranaBazarProductsInventory, err error) {
	inventoryData, err := s.GetCurrentInventory(ctx, []string{productID})
	if err != nil {
		return nil, err
	}

	if len(inventoryData) == 0 {
		return nil, fmt.Errorf("product %s not found in inventory", productID)
	}

	return &inventoryData[0], nil
}
