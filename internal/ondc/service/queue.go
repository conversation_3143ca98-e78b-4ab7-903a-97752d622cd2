package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/queue"
	queueModals "kc/internal/ondc/queue/models"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"

	"gorm.io/gorm"
)

func (s *Service) UpdateQueueTaskStatus(repo *sqlRepo.Repository, queueID string, status string) error {
	_, _, err := repo.Update(&dao.TimedFunctionsQueueData{
		QueueId: queueID,
	}, &dao.TimedFunctionsQueueData{
		Status: status,
	})
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) PrefillQueueData(repo *sqlRepo.Repository, mp *mixpanelRepo.Repository) {
	timedFunctionsQueue := []dao.TimedFunctionsQueueData{}

	_, err := repo.Find(map[string]interface{}{
		"status": "INITIATED",
	}, &timedFunctionsQueue)
	if err != nil && err != HandleDbErr(gorm.ErrRecordNotFound) {
		fmt.Println("Error fetching data from TimedFunctionsQueueData table:", err)
	}

	for _, timedFunctionData := range timedFunctionsQueue {
		// handle adding elements to queue
		var data interface{}
		err := json.Unmarshal(timedFunctionData.Data, &data)
		if err != nil {
			continue
		}
		var meta interface{}
		json.Unmarshal(timedFunctionData.Data, &meta)

		var triggerFunc func() error
		if timedFunctionData.TriggerFunction == queue.QUEUE_TRIGGER_FUNCTION_TYPES.PAYMENT_WA {
			triggerFunc = func() error {
				s.SendRetryPaymentWAMessage(repo, mp, timedFunctionData.QueueId, data, nil)
				return nil
			}
		} else if timedFunctionData.TriggerFunction == queue.QUEUE_TRIGGER_FUNCTION_TYPES.CANCEL_PAYMENT_FAIL {
			triggerFunc = func() error {
				s.CancelUnpaidOrders(repo, mp, timedFunctionData.QueueId, data, nil)
				return nil
			}
		} else if timedFunctionData.TriggerFunction == queue.QUEUE_TRIGGER_FUNCTION_TYPES.AUTO_CANCEL_NONCONFIRMED_ORDER {
			triggerFunc = func() error {
				s.createConditionalAutoCancelQueueFunc(repo, mp, data)
				return nil
			}
		} else {
			continue
		}

		queueInsertParams := queueModals.QueueInsertParams{
			Data:             data,
			ShouldAdjustTime: false,
			TimeAdjustConfig: nil,
			TriggerAt:        *timedFunctionData.TriggerAt,
			TriggerFunction:  timedFunctionData.TriggerFunction,
			TriggerFunc:      triggerFunc,
			QueueID:          &timedFunctionData.QueueId,
			CreateInDb:       false,
		}

		queue.AddDataToQueue(context.Background(), repo, mp, queueInsertParams)
	}
	fmt.Println("added data to all the queues")

}
