package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/infrastructure/logging"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/service/cart"
	ordervalue "kc/internal/ondc/service/orderBill/orderValue"
	userdetails "kc/internal/ondc/service/userDetails"
	"kc/internal/ondc/utils"
	"time"

	"github.com/google/uuid"
)

var kbcLogger = logging.GetLogrusLogger("kiranabazar_cart")

var INTERNAL_USERS = []string{
	"wMn4a578ChXPCC34Vl2Qn7KZFHV2",
	"LFgu0CMG4bZXxpjW5oHlX4zEtFf2",
}

func (s *Service) GetKiranaBazarCart(ctx context.Context, req *dto.AppGetKiranaBazarCartRequest) (response *dto.AppGetKiranaBazarCartResponse, err error) {
	cart, err := cart.Get(ctx, req.UserID, req.Data.Seller)
	byt, err := json.Marshal(cart.Cart)
	if err != nil {
		paymentLogger.Error(ctx, "Error marshalling cart", "userID", req.UserID, "seller", req.Data.Seller, "error", err)
		return
	}
	cartProducts := []shared.SellerItems{}
	json.Unmarshal(byt, &cartProducts)
	if len(cartProducts) == 0 {
		paymentLogger.Info(ctx, "Cart is empty", "userID", req.UserID, "seller", req.Data.Seller)
		response = &dto.AppGetKiranaBazarCartResponse{
			Data: dto.AppGetKiranaBazarCartData{
				Products: make([]shared.SellerItems, 0),
			},
		}
		return
	}
	paymentLogger.Info(ctx, "Cart fetched successfully", "userID", req.UserID, "seller", req.Data.Seller)
	response = &dto.AppGetKiranaBazarCartResponse{
		Data: dto.AppGetKiranaBazarCartData{
			Products: cartProducts,
			Seller:   req.Data.Seller,
		},
	}
	return
}

// UpsertKiranaBazarCart syncs the cart with FE
func (s *Service) UpsertKiranaBazarCartV2(ctx context.Context, req *dto.AppUpsertKiranaBazarCartRequest) (interface{}, error) {
	paymentCtx := ctx
	paymentCtx = logging.WithUserID(paymentCtx, req.UserID)
	paymentCtx = logging.WithTraceID(paymentCtx, string(uuid.New().String()))

	paymentLogger.Info(paymentCtx, "UpsertKiranaBazarCartV2 called", "userID", req.UserID, "seller", req.Data.Seller)
	// handling for undefined seller
	if req.Data.Seller == "undefined" {
		paymentLogger.Warn(paymentCtx, "Undefined seller encountered", "userID", req.UserID)
		if utils.UNDEFINED_SELLER_FIX_USERS[req.UserID] {
			duration := 200 * 24 * time.Hour
			s.IncrementWidgetViewCount(ctx, req.UserID, "1236", &duration)
			paymentLogger.Info(paymentCtx, "Incremented widget view count for undefined seller", "userID", req.UserID)
		}
		return &dto.AppUpsertKiranaBazarResponse{
			Data: dto.AppGetKiranaBazarCartDataV2{
				Products:       make(map[string]shared.SellerItems),
				Seller:         req.Data.Seller,
				OfferMessage:   dto.OfferMessage{},
				OrderInfoModal: nil,
				BillPricing:    nil,
			},
		}, nil
	}
	if req.UserID == "" {
		paymentLogger.Error(paymentCtx, "UserID missing in request")
		return nil, errors.New("FAILED")
	} else if req.Data.Seller == "" {
		paymentLogger.Error(paymentCtx, "Seller missing in request", "userID", req.UserID)
		return nil, errors.New("FAILED")
	}
	var MIN_ORDER_VALUE float64 = userdetails.GetUserSellerMinOrderValue(req.UserID, req.Data.Seller, false, "upsert_kirana_bazar_cart_V2")
	paymentLogger.Info(paymentCtx, "Fetched MIN_ORDER_VALUE", "value", MIN_ORDER_VALUE)
	if MIN_ORDER_VALUE < 0 {
		paymentLogger.Error(paymentCtx, "Failed to get min order value", "userID", req.UserID, "seller", req.Data.Seller)
		return nil, errors.New("failed to get min order value")
	}
	userDetailsChannel := userdetails.AsyncFetchUserDetails(req.UserID, []string{
		userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS,
	}, 1*time.Second)

	appVersion := req.Meta.AppVersion
	var SELLER = req.Data.Seller
	context, err := json.Marshal(req.Meta.Context)
	if err != nil {
		paymentLogger.Error(paymentCtx, "Failed to marshal context", "error", err)
		return nil, err
	}

	activationCohortEligibilityChannel := make(chan *dto.ActivationCohortUserData, 1)
	activationCohortEligibilityChannel <- nil

	if SELLER == utils.KIRANA_CLUB {
		sellerLevelCount, err := ordervalue.GetSellerLevelOrderCount(req.UserID)
		if err != nil {
			paymentLogger.Error(paymentCtx, "Failed to get seller level order count", "error", err)
			return nil, err
		}

		placedOrderCount := sellerLevelCount[fmt.Sprintf("%s::placed", SELLER)]
		confirmedOrderCount := sellerLevelCount[fmt.Sprintf("%s::confirmed", SELLER)]
		paymentLogger.Info(paymentCtx, "KIRANA_CLUB order counts", "placed", placedOrderCount, "confirmed", confirmedOrderCount)

		if placedOrderCount > 0 || confirmedOrderCount > 0 {
			paymentLogger.Info(paymentCtx, "User has placed/confirmed orders for KIRANA_CLUB, sending empty cart", "userID", req.UserID)
			// send empty cart
			emptyCart := make(map[string]shared.SellerItems)
			response := &dto.AppUpsertKiranaBazarResponse{
				Data: dto.AppGetKiranaBazarCartDataV2{
					Products:       emptyCart,
					Seller:         req.Data.Seller,
					OfferMessage:   dto.OfferMessage{},
					OrderInfoModal: nil,
					BillPricing:    nil,
				},
			}
			return response, nil
		}
	}

	cartObject, err := cart.GetCartObject(req.UserID, req.Data.Seller, true, time.Now(), context, req.Data.Products)
	if err != nil {
		paymentLogger.Error(paymentCtx, "Failed to get cart object", "error", err)
		return nil, err
	}

	checkForMaxProductQuantityCap := true
	// if SELLER == utils.LOTS || SELLER == utils.KIRANA_CLUB || SELLER == utils.RSB_SUPER_STOCKIST {
	// 	checkForMaxProductQuantityCap = true
	// }
	// validate cart to check for expired, wrong seller products
	cartObject, err = cartObject.ValidateCart(cart.CartValidationCoinditions{IncludeOOSProducts: false, IncludeVariants: true, CheckMaxProductQuantityCap: checkForMaxProductQuantityCap}, appVersion)
	if err != nil {
		paymentLogger.Error(paymentCtx, "Cart validation failed", "error", err)
		return nil, err
	}

	// updating cart to redis cache and persistent storage
	err = cart.Update(ctx, req.UserID, req.Data.Seller, cartObject)
	if err != nil {
		paymentLogger.Error(paymentCtx, "Failed to update cart", "error", err)
		return nil, err
	}

	totalProductPricing, err := cartObject.CartValue()
	if err != nil {
		paymentLogger.Error(paymentCtx, "Failed to get cart value", "error", err)
		return nil, err
	}

	totalPricingWithoutDiscount := totalProductPricing
	userDetails := <-userDetailsChannel

	var shippingAddress *dao.UserAddress = nil
	if SELLER == utils.CHUK_DE {
		addressResp, err := s.GetUserAddress(ctx, &dto.GetUserAddressRequest{
			UserID:     req.UserID,
			AppVersion: req.Meta.AppVersion,
			Data: dto.AppServiceAbilityAPIRequestData{
				Seller: req.Data.Seller,
				Source: req.Data.Source,
			},
		})
		if err != nil {
			paymentLogger.Error(paymentCtx, "Failed to get user address", "error", err)
			return nil, err
		}
		if len(addressResp.Address) > 0 {
			userShippingAddress := s.createAddressDAO(addressResp.Address[0])
			shippingAddress = &userShippingAddress
		}
	}

	offerMessage := dto.OfferMessage{}
	// eligibleOffer := GetOfferMessage(SELLER, totalProductPricing, MIN_ORDER_VALUE)
	eligibleOffer, err := s.GetNextOfferCouponData(ctx, SELLER, req.UserID, MIN_ORDER_VALUE, userDetails, appVersion)
	if err != nil {
		paymentLogger.Error(paymentCtx, "Failed to get next offer coupon data", "error", err)
		return nil, err
	}
	if eligibleOffer != nil {
		offerMessage = *eligibleOffer
	}

	_, _, _, _, _, _, updatedTotalProductPricing, _, totalPricingWithoutDiscount, _, lastAppliedCoupon, eligibleCharges, promoItems, _, err :=
		s.ProcessCart(ctx, cartObject, activationCohortEligibilityChannel, dto.GetBillDetailsRequest{
			Data: dto.GetBillDetailsData{
				Seller: SELLER,
				Source: req.Data.Source,
			},
			UserID: req.UserID,
			Meta:   req.Meta,
		}, MIN_ORDER_VALUE, userDetails, shippingAddress, false)
	if err != nil {
		if err.Error() == exceptions.OosErrorMessage {
			paymentLogger.Warn(paymentCtx, "OOS error in ProcessCart, sending empty cart", "userID", req.UserID)
			return &dto.AppUpsertKiranaBazarResponse{
				Data: dto.AppGetKiranaBazarCartDataV2{
					Products:       make(map[string]shared.SellerItems),
					Seller:         req.Data.Seller,
					OfferMessage:   dto.OfferMessage{},
					OrderInfoModal: nil,
					BillPricing:    nil,
				},
			}, nil
		}
		paymentLogger.Error(paymentCtx, "ProcessCart failed", "error", err)
		return nil, err
	}

	if eligibleCharges != nil {
		offerMessage = *eligibleCharges
	}

	var currentOffer *dto.OrderInfoModal = nil
	// currOffer := GetCurrentOffer(lastAppliedCoupon)

	// userOverallConfirmedOrderCount, err := ordervalue.GetUserLevelConfirmedOrderCount(req.UserID)
	// if err != nil {
	// 	return nil, err
	// }

	currOffer := GetCurrentOffer(lastAppliedCoupon)
	if currOffer != nil {
		if offerMessage.DiscountAmount != nil {
			currOffer.NextDiscountThreshold = *offerMessage.DiscountAmount
		}
		currentOffer = currOffer
	}

	totalProductPricing = updatedTotalProductPricing
	// handling Backend Discounts
	// tpm, disc, err := s.GetBackendDiscount(totalPricingWithoutDiscount, totalProductPricing, SELLER, req.UserID, nil)
	// if err == nil {

	// 	for _, tpmDiscount := range tpm {
	// 		discountAmount += tpmDiscount.TotalValue
	// 	}
	// 	totalProductPricing -= disc
	// }

	var billPricing *dto.BillPricing = nil

	if currentOffer != nil && currentOffer.Discount != nil {
		billPricingData := dto.BillPricing{}
		discountString := fmt.Sprintf("%.2f%%", *currentOffer.Discount)
		billPricingData.Discount = currentOffer.Discount
		billPricingData.DiscountString = &discountString
		billPricingData.TotalPricing = totalProductPricing
		billPricingData.TotalPricingString = fmt.Sprintf("₹%s", utils.FormatIndianNumber(int(totalProductPricing)))
		billPricingData.TotalPricingWithoutDiscount = totalPricingWithoutDiscount
		billPricingData.TotalPricingWithoutDiscountString = fmt.Sprintf("₹%s", utils.FormatIndianNumber(int(totalPricingWithoutDiscount)))
		billPricing = &billPricingData
	}

	if eligibleCharges != nil {
		billPricingData := dto.BillPricing{}
		billPricingData.TotalPricingString = fmt.Sprintf("₹%s", utils.FormatIndianNumber(int(totalProductPricing)))
		billPricingData.TotalPricing = totalProductPricing
		billPricingData.TotalPricingWithoutDiscount = totalPricingWithoutDiscount
		billPricingData.TotalPricingWithoutDiscountString = fmt.Sprintf("₹%s", utils.FormatIndianNumber(int(totalPricingWithoutDiscount)))
		billPricingData.Discount = eligibleCharges.Discount
		billPricingData.DiscountString = utils.StrPtr(fmt.Sprintf("%.2f%%", *eligibleCharges.Discount))
		billPricing = &billPricingData
	}

	productsResponse := make(map[string]shared.SellerItems)
	allProducts, err := cartObject.GetProducts()
	if err != nil {
		paymentLogger.Error(paymentCtx, "Failed to get products from cart object", "error", err)
		return nil, err
	}

	allProducts = append(allProducts, promoItems...)
	for _, product := range allProducts {
		productsResponse[product.ID] = product
	}

	response := &dto.AppUpsertKiranaBazarResponse{
		Data: dto.AppGetKiranaBazarCartDataV2{
			Products:       productsResponse,
			Seller:         req.Data.Seller,
			OfferMessage:   offerMessage,
			OrderInfoModal: currentOffer,
			BillPricing:    billPricing,
			// AppliedCoupons: appliedCoupons,
		},
	}
	return response, err
}

// UpsertKiranaBazarCart syncs the cart with FE
func (s *Service) UpsertKiranaBazarCart(ctx context.Context, req *dto.AppUpsertKiranaBazarCartRequest) (interface{}, error) {
	if req.UserID == "" {
		return nil, errors.New("FAILED")
	} else if req.Data.Seller == "" {
		return nil, errors.New("FAILED")
	}
	var MIN_ORDER_VALUE float64 = userdetails.GetUserSellerMinOrderValue(req.UserID, req.Data.Seller, false, "upsert_kirana_bazar_cart")
	if MIN_ORDER_VALUE < 0 {
		return nil, errors.New("failed to get min order value")
	}
	appVersion := req.Meta.AppVersion
	var SELLER = req.Data.Seller

	context, err := json.Marshal(req.Meta.Context)
	if err != nil {
		return nil, err
	}
	cartObject, err := cart.GetCartObject(req.UserID, req.Data.Seller, true, time.Now(), context, req.Data.Products)
	if err != nil {
		return nil, err
	}

	checkForMaxProductQuantityCap := true
	// if SELLER == utils.LOTS || SELLER == utils.KIRANA_CLUB {
	// 	checkForMaxProductQuantityCap = true
	// }
	// validate cart to check for expired, wrong seller products
	cartObject, err = cartObject.ValidateCart(cart.CartValidationCoinditions{IncludeOOSProducts: false, IncludeVariants: false, CheckMaxProductQuantityCap: checkForMaxProductQuantityCap}, appVersion)
	if err != nil {
		return nil, err
	}

	// updating cart to redis cache and persistent storage
	err = cart.Update(ctx, req.UserID, req.Data.Seller, cartObject)
	if err != nil {
		return nil, err
	}
	totalProductPricing, err := cartObject.CartValue()
	if err != nil {
		return nil, err
	}

	offerMessage := dto.OfferMessage{}
	eligibleOffer := GetOfferMessage(SELLER, totalProductPricing, MIN_ORDER_VALUE, nil)
	if eligibleOffer != nil {
		offerMessage = *eligibleOffer
	}

	products, _ := cartObject.GetProducts()
	response := &dto.AppUpsertKiranaBazarResponse{
		Data: dto.AppGetKiranaBazarCartData{
			Products:     products,
			Seller:       req.Data.Seller,
			OfferMessage: offerMessage,
		},
	}
	return response, err
}

func contains(s []string, e string) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

func ondcSelectAPI(s *Service, ctx context.Context, req *dto.AppUpsertKiranaBazarCartRequest) (*dto.AppSelectResponse, error) {
	if req.Meta.Context.TransactionID == nil {
		return nil, nil
	}
	selectContext, err := getContext(&req.Meta.Context, SELECT, getMessageID())
	if err != nil {
		return nil, err
	}
	// create request object for select request
	var providerID string
	var providerTTL string
	var providerLocation []string

	for _, product := range req.Data.Products {
		providerID = product.ProviderID
		providerTTL = product.ProviderTTL
		providerLocation = product.LocationIds
	}
	requestObject := dto.AppSelectRequest{
		Data: dto.AppSelectRequestData{
			Provider: dto.AppSelectProvider{
				ProviderID:       providerID,
				ProviderTTL:      providerTTL,
				ProviderLocation: providerLocation,
			},
			Items: req.Data.Products,
		},
		Meta: dto.Meta{
			TransactionID: *req.Meta.Context.TransactionID,
			Context:       *selectContext,
		},
		UserID: req.UserID,
	}
	resp, err := s.Select(ctx, &requestObject)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
