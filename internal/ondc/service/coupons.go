package service

import (
	"context"
	"encoding/json"
	brands2 "kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/coupons"
	"net/http"
	"time"
)

func (s *Service) GetCouponNameMap(ctx context.Context) (map[string]*coupons.Coupon, error) {
	return s.Coupons.GetCouponNameMap(ctx), nil
}

func (s *Service) GetCouponByCode(ctx context.Context, code string, fetchActive bool) (*coupons.Coupon, error) {
	return s.Coupons.GetCouponByCode(ctx, code, fetchActive)
}

func (s *Service) GetCouponByName(ctx context.Context, name string, fetchActive bool) (*coupons.Coupon, error) {
	return s.Coupons.GetCouponByName(ctx, name, fetchActive)
}

// GetAllBrandIds returns a hardcoded list of all brand IDs (to be replaced with DB query later)
func (s *Service) GetAllBrandIds(ctx context.Context) ([]string, error) {
	brands := brands2.GetAllBrands()
	brands = append(brands, "ALL")
	return brands, nil
}

// GetAllCohortIds returns a hardcoded list of all cohort IDs (to be replaced with DB query later)
func (s *Service) GetAllCohortIds(ctx context.Context) ([]string, error) {
	cohorts := []string{"ALL", "NEW_USER", "OLD_USER", "NEW_SELLER_USER", "OLD_SELLER_USER", "USER_LEVEL_COUPON"}

	// Fetch additional cohorts from API
	type apiCohort struct {
		Message string `json:"message"`
		Error   bool   `json:"error"`
		Code    int    `json:"code"`
		Result  struct {
			Data []struct {
				Name  string `json:"name"`
				Start int64  `json:"start"`
				End   int64  `json:"end"`
			} `json:"data"`
		} `json:"results"`
	}

	req, err := http.NewRequestWithContext(ctx, "POST", "https://kc.retailpulse.ai/api/cohort/details", nil)
	if err == nil {
		req.Header.Set("User-Agent", "Go-http-client")
		client := &http.Client{Timeout: 5 * time.Second}
		resp, err := client.Do(req)
		if err == nil && resp.StatusCode == 200 {
			defer resp.Body.Close()
			var apiCohorts apiCohort
			if err := json.NewDecoder(resp.Body).Decode(&apiCohorts); err == nil {
				now := time.Now().UnixMilli()
				for _, c := range apiCohorts.Result.Data {
					if c.Start <= now && c.End >= now {
						cohorts = append(cohorts, c.Name)
					}
				}
			}
		}
	}

	return cohorts, nil
}
