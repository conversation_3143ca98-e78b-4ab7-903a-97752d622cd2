package service

import (
	"context"
	"errors"
	"fmt"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/logistics/couriers"
	"kc/internal/ondc/utils"
	"strconv"
	"time"

	"github.com/mixpanel/mixpanel-go"
)

func (s *Service) HandleDispatchedOrder(ctx context.Context, request dto.DispatchedOrderRequest) error {
	orderID := request.Data.OrderID
	if orderID == "" {
		err := errors.New("orderID cannot be empty")
		return err
	}

	orderIDInt, err := strconv.ParseInt(orderID, 10, 64)
	if err != nil {
		err := errors.New("orderID is not defined")
		return err
	}
	orderInfo, err := GetOrderInfo(s.repository, orderIDInt)
	if err != nil {
		return err
	}
	orderDetails, err := GetOrderDetails(s.repository, fmt.Sprintf("%d", orderIDInt))
	if err != nil {
		return err
	}

	wayBillDetails, err := s.AWBMaster.GetPrimaryWaybillForOrderID(context.Background(), []uint64{uint64(orderIDInt)})
	if err != nil {
		return err
	}

	if len(wayBillDetails) == 0 || wayBillDetails[0].AWBNumber == nil {
		err = errors.New("no waybill found for order")
		return err
	}

	awb := wayBillDetails[0]

	epochMillis := request.Data.DispatchedTime
	if epochMillis == 0 {
		epochMillis = *awb.DispatchedAt
	}

	response, err := s.AddDataForReconciliation(context.Background(), &dto.AddReconciliationRequest{
		OrderID: orderIDInt,
		Data: []dto.StatusTimeStamp{
			{
				TimeStamp:   epochMillis,
				OrderStatus: "order_dispatched",
			},
		},
		Service: "INTERNAL",
		OMS:     orderInfo.Seller,
	})

	metricsUpdated := response.UpdatedMetrics
	var expectedDeliveryDate time.Time
	var promisedDeliveryDate time.Time

	if awb.EDD != nil {
		// int64 to local time
		expectedDeliveryDate = time.UnixMilli(*awb.EDD).Local()
	}

	if awb.PDD != nil {
		promisedDeliveryDate = time.UnixMilli(*awb.PDD).Local()
	}

	if err == nil && includes(metricsUpdated, "order_dispatched") {
		eventObject := map[string]any{
			"distinct_id":     *orderInfo.UserID,
			"awb_number":      *awb.AWBNumber,
			"courier_name":    couriers.GetActualCourierName(awb.Courier),
			"tracking_link":   "",
			"order_id":        *orderInfo.ID,
			"cart_value":      int(orderDetails.GetCartValue()),
			"order_value":     int(orderDetails.GetOrderValue()),
			"seller":          orderInfo.Seller,
			"ordering_module": utils.MakeTitleCase(orderInfo.Seller),
			"time":            epochMillis / 1000,
			"updated_by":      request.Data.UpdatedBy,
			"$insert_id":      fmt.Sprintf("%s_%d", "order_dispatched", *orderInfo.ID),
		}

		pdd, displayDate := getPromiseDeliveryDate(promisedDeliveryDate, awb.DispatchedAt)

		eventObject["promised_delivery_date"] = pdd
		eventObject["displayed_promised_delivery_date"] = displayDate

		if !expectedDeliveryDate.IsZero() {
			eventObject["expected_delivery_date"] = expectedDeliveryDate.Format("2006-01-02 15:04:05")
		}

		if request.Data.DispatchedTime != 0 {
			eventObject["time"] = request.Data.DispatchedTime
		}

		s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
			s.Mixpanel.NewEvent("Order Dispatched", *orderInfo.UserID, eventObject, fmt.Sprintf("%s_%d", "order_dispatched", *orderInfo.ID)),
		})

		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{*orderInfo.UserID},
			EventName:   "Order Dispatched",
			EventObject: eventObject,
		})
	}
	return nil
}
