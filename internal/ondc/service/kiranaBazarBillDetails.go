package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/cache"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/external/loyalty"
	"slices"

	// "kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/cart"
	"kc/internal/ondc/service/charges"
	"kc/internal/ondc/service/coupons"
	"kc/internal/ondc/service/imageGenerator/config"
	"kc/internal/ondc/service/imageGenerator/generator"
	"kc/internal/ondc/service/offers"
	ordervalue "kc/internal/ondc/service/orderBill/orderValue"
	"kc/internal/ondc/service/products"
	userdetails "kc/internal/ondc/service/userDetails"
	"kc/internal/ondc/utils"
	"math"
	"strconv"
	"time"
)

const (
	FIRST_ORDER_DISCOUNT          = 200
	MIN_ORDER_FOR_10_PACKET_HALDI = 200000000
	MIN_ORDER_FOR_20_PACKET_HALDI = 200000000
	MIN_ORDER_FOR_30_PACKET_HALDI = 200000000
	// MIN_ORDER_FOR_3_PERCENT  = 1500
	MIN_ORDER_FOR_KNIFE_SET  = 2000
	MIN_ORDER_FOR_FREE_BAG   = 1500
	MIN_ORDER_FOR_5_PERCENT  = 10000000
	MIN_ORDER_FOR_7_PERCENT  = 17000
	MIN_ORDER_FOR_8_PERCENT  = 28000
	MIN_ORDER_FOR_10_PERCENT = 100000
	MIN_ORDER_VALUE_PEN_SET  = 600

	// MIN_ORDER_FOR_2_PERCENT_GODESI = 5000
	// MIN_ORDER_FOR_3_PERCENT_GODESI = 10000
	// MIN_ORDER_FOR_5_PERCENT_GODESI = 15000
	MIN_ORDER_FOR_3_PERCENT_GO_DESI          = 200000000
	MIN_ORDER_FOR_5_PERCENT_AND_MOBILE_STAND = 200000000
	MIN_ORDER_FOR_7_PERCENT_GO_DESI          = 200000000

	MIN_ORDER_FOR_10_IMLI_POPS = 200000000
	MIN_ORDER_FOR_30_IMLI_POPS = 200000000
	MIN_ORDER_FOR_60_IMLI_POPS = 200000000

	MIN_ORDER_FOR_1CHAI_PATTI_APSARA     = 200000000
	MIN_ORDER_FOR_2CHAI_PATTI_APSARA     = 200000000
	MIN_ORDER_FOR_4CHAI_PATTI_APSARA     = 200000000
	MIN_ORDER_FOR_1CHAI_PATTI_282_APSARA = 2000
	MIN_ORDER_FOR_3CHAI_PATTI_282_APSARA = 5000

	MIN_ORDER_FOR_5_PERCENT_SOOTHE = 200000000

	MIN_ORDER_FOR_3_PERCENT_MANGALAM = 200000000
	MIN_ORDER_FOR_4_PERCENT_MANGALAM = 200000000

	KC_OFFER_DELIVER_CHARGE = 0

	MIN_ORDER_FOR_1_PERCENT_MILDEN          = 20000000
	MIN_ORDER_FOR_1_AND_HALF_PERCENT_MILDEN = 50000000
	MIN_ORDER_FOR_3_PERCENT_MILDEN          = 10000000

	MIN_ORDER_FOR_2_FREE_CANDY_PACKETS_MILDEN = 200000000
	MIN_ORDER_FOR_3_FREE_CANDY_PACKETS_MILDEN = 200000000
	MIN_ORDER_FOR_5_FREE_CANDY_PACKETS_MILDEN = 200000000

	MIN_ORDER_FOR_FREE_DELIVERY_CHUK_DE = 1200

	MIN_ORDER_FOR_5_FREE_HAIR_COLOUR_RSB = 200000000
	MIN_ORDER_FOR_10_FREE_HAIR_COLOR_RSB = 200000000
	MIN_ORDER_FOR_30_FREE_HAIR_COLOR_RSB = 200000000

	MIN_ORDER_FOR_3_PERCENT_EXTRA_MARGIN_RSB = 200000000

	MIN_ORDER_FOR_5_PERCENT_EXTRA_MARGIN_RSB = 200000000
	MIN_ORDER_FOR_7_PERCENT_EXTRA_MARGIN_RSB = 200000000

	MIN_ORDER_FOR_100_PERCENT_EXTRA_MARGIN_KC_LR = 0

	MIN_ORDER_FOR_10_FREE_PACKETS_APSARA_RSB_APSARA = 200000000
	MIN_ORDER_FOR_20_FREE_PACKETS_APSARA_RSB_APSARA = 200000000
	MIN_ORDER_FOR_40_FREE_PACKETS_APSARA_RSB_APSARA = 200000000

	MIN_ORDER_FOR_RSB_FREE_BAG_OFFER = 1001
)

func handleONDCInit(s *Service, ctx context.Context, request dto.GetBillDetailsRequest, repo *sqlRepo.Repository) (interface{}, error) {
	cartData, err := cart.Get(ctx, request.UserID, request.Data.Seller)
	if err != nil {
		return nil, err
	}

	onSelectData := cartData.OnSelect
	context := cartData.Context
	appInitContext := &dto.Context{}
	appSelectResponseData := dto.AppSelectResponse{}
	err = json.Unmarshal(onSelectData, &appSelectResponseData)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(context, appInitContext)
	if err != nil {
		return nil, err
	}

	providerLocations := []string{}
	for _, loc := range appSelectResponseData.Cart.Meta.Provider.Locations {
		providerLocations = append(providerLocations, loc.ID)
	}
	if appSelectResponseData.Cart.Meta.Provider.TTL == nil {
		ttl := PT30S
		appSelectResponseData.Cart.Meta.Provider.TTL = &ttl
	}
	appInitContext, err = getContext(appInitContext, INIT, getMessageID())
	if err != nil {
		return nil, err
	}
	initRequest := &dto.AppInitReq{
		Meta: dto.Meta{
			Context: *appInitContext,
		},
		UserID: request.UserID,
		Data: dto.AppInitRequestData{
			AddressID: request.Data.AddressID,
			Items:     appSelectResponseData.Cart.Meta.Items,
			Provider: dto.AppInitProvider{
				ProviderID:       appSelectResponseData.Cart.Meta.Provider.ID,
				ProviderTTL:      *appSelectResponseData.Cart.Meta.Provider.TTL,
				ProviderLocation: providerLocations,
			},
		},
	}
	initResponse, err := s.Init(ctx, initRequest)
	if err != nil {
		return nil, err
	}

	byt, err := json.Marshal(initResponse)
	if err != nil {
		return nil, err
	}

	cartData.OnInit = byt

	// updating cart to sql and redis at the same time
	err = cart.Update(ctx, request.UserID, request.Data.Seller, cartData)
	if err != nil {
		return nil, err
	}
	appinitresp := dto.AppInitResp{}
	err = json.Unmarshal(byt, &appinitresp)
	if err != nil {
		return nil, err
	}

	productPricing := []shared.ProductPrice{}
	totalPricing := []shared.TotalPricing{
		{
			Key:   "Total",
			Value: appinitresp.Meta.Quote.Price.Value,
		},
	}

	for _, j := range appinitresp.Meta.Quote.Breakup {
		offeredValue := "0"
		if j.Item != nil {
			offeredValue = j.Item.Price.Value
		}
		productPricing = append(productPricing, shared.ProductPrice{
			ID:           j.Item.ID,
			OfferedValue: offeredValue,
			Quantity:     j.ONDCOrgItemQuantity.Count,
			TotalValue:   j.Price.Value,
			ProductName:  j.Title,
			PackSize:     0,
		})
	}

	response := dto.GetBillDetailsResponse{
		Data: dto.BillDetails{
			EnableOrdering:  true,
			ProductsPricing: productPricing,
			TotalPricing:    totalPricing,
			Seller:          request.Data.Seller,
			Coupon:          request.Data.CouponID,
		},
	}

	return response, nil
}

func (s *Service) getUserCashbackBalanceFromService(ctx context.Context, userID string, seller string, meta dto.Meta, cashbackBalanceChannel chan *float64) {
	if seller == utils.LOTS {
		cashbackBalanceChannel <- nil
		return
	}
	resp, err := s.GetUserCashbackBalance(ctx, dto.GetUserCashbackBalanceRequest{
		UserID: userID,
		Meta:   meta,
	})
	if err != nil {
		cashbackBalanceChannel <- nil
		return
	}
	cashbackBalanceChannel <- resp.Data.Cashback
}

func (s *Service) calculateUserLoyaltyRewards(userID string, appVersion string, totalProductPricing float64, seller string, applyCache bool) (*loyalty.LoyaltyRewardsResponse, error) {
	// Create cache key based on user ID and order amount
	cacheKey := fmt.Sprintf("user_loyalty_rewards:%s:%.2f", userID, totalProductPricing)

	localCacheExpiry := 10 * time.Minute
	if applyCache {
		localCacheExpiry = 0
	}
	// Try to get from cache first
	cacheResult, err := cache.GetInstance().GetCachedData(
		context.Background(),
		cacheKey,
		func() (interface{}, error) {
			orderCount, err := ordervalue.GetUserLevelOrderCount(userID)
			if err != nil {
				fmt.Println("error in getting user order count", err)
			}
			isFirstTimeUser := false
			if orderCount == 0 {
				isFirstTimeUser = true
			}
			requestObject := map[string]interface{}{
				"data": map[string]interface{}{
					"uid":             userID,
					"appVersion":      appVersion,
					"deviceId":        "a1d4g6hh",
					"orderAmount":     totalProductPricing,
					"isFirstTimeUser": isFirstTimeUser,
				},
			}
			resp, err := loyalty.CallLoyaltyAPI("CALCULATE_LOYALTY_REWARDS", requestObject, nil)
			if err != nil {
				return nil, err
			}
			orderLoyaltyReward := loyalty.LoyaltyRewardsResponse{}
			err = json.Unmarshal(resp, &orderLoyaltyReward)
			if err != nil {
				return nil, err
			}
			return &orderLoyaltyReward, nil
		},
		localCacheExpiry, // Local cache for 10 minutes
		0,                // not caching in redis
	)

	if err != nil {
		return nil, err
	}

	orderLoyaltyReward, ok := cacheResult.Data.(*loyalty.LoyaltyRewardsResponse)
	if !ok {
		return nil, fmt.Errorf("invalid cache data type")
	}

	return orderLoyaltyReward, nil
}

func (s *Service) GetBillDetailsV3(ctx context.Context, request dto.GetBillDetailsRequest) (interface{}, error) {
	var err error

	if request.Data.Seller != "" && !includes(brands.GetAllBrands(), request.Data.Seller) {
		return nil, fmt.Errorf("invalid seller")
	}

	appVersion := request.Meta.AppVersion

	userDetailsChannel := userdetails.AsyncFetchUserDetails(request.UserID, []string{
		userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS,
	}, 1*time.Second)

	addressResp, err := s.GetUserAddress(ctx, &dto.GetUserAddressRequest{
		UserID:     request.UserID,
		AppVersion: request.Meta.AppVersion,
		Data: dto.AppServiceAbilityAPIRequestData{
			Seller: request.Data.Seller,
			Source: request.Data.Source,
		},
	})
	if err != nil {
		return nil, err
	}
	var shippingAddress *dao.UserAddress = nil
	if len(addressResp.Address) > 0 {
		userShippingAddress := s.createAddressDAO(addressResp.Address[0])
		shippingAddress = &userShippingAddress
	}

	activationCohortEligibilityChannel := make(chan *dto.ActivationCohortUserData, 1)
	userCashbackBalanceChannel := make(chan *float64, 1)

	if request.Data.Seller == utils.ZOFF_FOODS || true {
		go getActivationCohortUserData(ctx, request.UserID, utils.ZOFF_FOODS, s.GcpRedis, activationCohortEligibilityChannel)
	} else {
		// insert nil in channel to avoid deadlock
		activationCohortEligibilityChannel <- nil
	}
	go s.getUserCashbackBalanceFromService(ctx, request.UserID, request.Data.Seller, request.Meta, userCashbackBalanceChannel)

	var updating bool = false
	kiranaBazarCart, err := cart.Get(ctx, request.UserID, request.Data.Seller)
	if err != nil {
		return nil, err
	}

	// this is for handling the cart from FE in case of cart comes from FE
	if request.Data.Cart != nil {
		allProds := []shared.SellerItems{}
		bt, _ := json.Marshal(request.Data.Cart)
		json.Unmarshal(bt, &allProds)
		kiranaBazarCart, err = cart.GetCartObject(request.UserID, request.Data.Seller, true, time.Now(), nil, allProds)
		if err != nil {
			return nil, err
		}
		updating = true
	}

	var MIN_ORDER_VALUE float64 = userdetails.GetUserSellerMinOrderValue(request.UserID, request.Data.Seller, updating, "bill_details_v2")
	if slices.Contains(ordervalue.MIN_ORDER_VALUE_EXCEPTION_EMAILS, request.Email) {
		MIN_ORDER_VALUE = 0
	}

	sellerItems, _ := kiranaBazarCart.Cart.Get()
	if len(sellerItems) == 0 {
		return dto.GetBillDetailsResponse{
			Data: dto.BillDetails{
				ProductsPricing: []shared.ProductPrice{},
				TotalPricing:    []shared.TotalPricing{},
				WaysToPay:       []dto.WaysToPayDataV1{},
				PaymentSection:  dto.PaymentSection{},
				OfferMessage:    nil,
			},
		}, nil
	}

	userDetails := <-userDetailsChannel
	// Process cart and calculate discounts
	productsPricing, discountPricing, totalPricingMap, appliedCoupons, discountAmount, discountReason, totalProductPricing, _, totalPricingWithoutDiscount, _, _, _, _, _, err :=
		s.ProcessCart(ctx, kiranaBazarCart, activationCohortEligibilityChannel, request, MIN_ORDER_VALUE, userDetails, shippingAddress, false)
	if err != nil {
		return nil, err
	}

	userCashbackBalance := <-userCashbackBalanceChannel
	userCashbackOfferComponent, _, totalProductPricing, requestedCashback, err :=
		s.processCashbackApplication(userCashbackBalance, request.Data.RequestedCashback, totalProductPricing, &discountPricing, &discountReason, &discountAmount, userDetails, request.Data.Seller, request.Data.CashbackApplied)
	if err != nil {
		// Log error but continue
		fmt.Println("Error processing cashback:", err)
	}

	style := map[string]interface{}{
		"fontWeight": 700,
	}
	totalPricingMap = append(totalPricingMap, shared.TotalPricing{
		Key:        "टोटल",
		Value:      fmt.Sprintf("₹%.2f", totalProductPricing),
		Styles:     &style,
		TotalValue: totalProductPricing,
	})

	// Generate offer message if applicable
	var offerMessage *dto.OfferMessage = nil
	if discountAmount > 0 {
		intDiscountAmount := int(discountAmount)
		offerMessage = &dto.OfferMessage{
			Text:           &discountReason,
			DiscountAmount: &intDiscountAmount,
		}
	}

	var orderLoyaltyReward *dto.LoyaltyRewardsComponent
	// do no call this API for zoff_foods and call with amount excluding zoff_foods manufacturer products for rsb
	loyaltyTotalAmount := totalProductPricing
	if request.Data.Seller == utils.RSB_SUPER_STOCKIST {
		zoffCartValue := utils.GetManufacturerCartValue(productsPricing, []string{utils.ZOFF_FOODS})
		totalAmountExcludingZoff := math.Max(0, totalProductPricing-zoffCartValue)
		loyaltyTotalAmount = totalAmountExcludingZoff
	}
	var orderLoyaltyRewardResponse *loyalty.LoyaltyRewardsResponse
	if request.Data.Seller != utils.ZOFF_FOODS {
		orderLoyaltyRewardResponse, err = s.calculateUserLoyaltyRewards(request.UserID, request.Meta.AppVersion, loyaltyTotalAmount, request.Data.Seller, false)
		if err != nil {
			// slack.SendSlackMessage(fmt.Sprintf("Error in calculating user loyalty rewards %s, cashback %f, total value %f: %v", request.UserID, request.Data.RequestedCashback, totalProductPricing, err))
		}
	}
	if orderLoyaltyRewardResponse != nil && (orderLoyaltyRewardResponse.Cashback > 0 || orderLoyaltyRewardResponse.Coins > 0) {
		orderLoyaltyReward = generateLoyaltyRewardsComponent(*orderLoyaltyRewardResponse)
	}

	var disclaimerMessage string = "“आगे बढ़ें” पर क्लिक करने से कोई पैसा नहीं कटेगा"

	paymentSection, defaultPaymentMethod, paymentSectionHeader, err := s.GetPaymentMethods(request.UserID, request.Data.Seller, totalProductPricing, appVersion)
	if err != nil {
		return nil, err
	}
	//if request.Data.PaymentMethod != nil {
	//	defaultPaymentMethod = *request.Data.PaymentMethod
	//}

	response := dto.GetBillDetailsResponse{
		Data: dto.BillDetails{
			ProductsPricing: productsPricing,
			TotalPricing:    totalPricingMap,
			DiscountPricing: discountPricing,
			//RewardCoins:         &rewardCoins,
			PaymentSection:       paymentSection,
			DefaultPaymentMethod: defaultPaymentMethod,
			PaymentSectionHeader: paymentSectionHeader,
			OfferMessage:         offerMessage,
			CashbackComponent:    userCashbackOfferComponent,
			AutoApplyCashback:    true,
			OrderLoyaltyRewards:  orderLoyaltyReward,
			RequestedCashback:    requestedCashback,
			Coupon:               request.Data.CouponID,
			Disclaimer:           disclaimerMessage,
			//PaymentBanner:        &utils.PaymentBannerURLForBillDetails,
			CTAText:        "आगे बढ़ें",
			AppliedCoupons: appliedCoupons,
		},
	}

	if totalPricingWithoutDiscount <= MIN_ORDER_VALUE {
		// response.Data.EnableOrdering = false
		response.Data.EnableOrdering = true
		response.Data.Message = fmt.Sprintf("ऑर्डर देने के लिए न्यूनतम कार्ट मूल्य %d से अधिक होना चाहिए", int(MIN_ORDER_VALUE))
	} else {
		response.Data.EnableOrdering = true
	}
	response.Data.Seller = request.Data.Seller
	return response, nil
}

func (s *Service) GetBillDetailsV2(ctx context.Context, request dto.GetBillDetailsRequest) (interface{}, error) {
	return s.GetBillDetailsV3(ctx, request)
	var err error

	if request.Data.Seller != "" && !includes(brands.GetAllBrands(), request.Data.Seller) {
		return handleONDCInit(s, ctx, request, s.repository)
	}
	activationCohortEligibilityChannel := make(chan *dto.ActivationCohortUserData, 1)
	if request.Data.Seller == utils.ZOFF_FOODS || true {
		go getActivationCohortUserData(ctx, request.UserID, utils.ZOFF_FOODS, s.GcpRedis, activationCohortEligibilityChannel)
	} else {
		// insert nil in channel to avoid deadlock
		activationCohortEligibilityChannel <- nil
	}

	userCashbackBalanceChannel := make(chan *float64, 1)
	go s.getUserCashbackBalanceFromService(ctx, request.UserID, request.Data.Seller, request.Meta, userCashbackBalanceChannel)

	couponID := request.Data.CouponID
	couponIDInt, err := strconv.Atoi(couponID)
	coupon := dto.Coupon{}
	if err == nil {
		couponResponse, err := s.GetCoupons(ctx, dto.GetCouponsRequest{
			UserID: request.UserID,
			Data: dto.GetCouponData{
				CouponID: couponIDInt,
				Seller:   request.Data.Seller,
			},
		})
		if err != nil {
			return nil, err
		}
		coupon = couponResponse.Data.Coupons[0]
	}

	var updating bool = false
	kiranaBazarCart, err := cart.Get(ctx, request.UserID, request.Data.Seller)
	if err != nil {
		return nil, err
	}

	// this is for handling the cart from FE in case of cart comes from FE
	if request.Data.Cart != nil {
		allProds := []shared.SellerItems{}
		bt, _ := json.Marshal(request.Data.Cart)
		json.Unmarshal(bt, &allProds)
		kiranaBazarCart, err = cart.GetCartObject(request.UserID, request.Data.Seller, true, time.Now(), nil, allProds)
		if err != nil {
			return nil, err
		}
		updating = true
	}

	var MIN_ORDER_VALUE float64 = userdetails.GetUserSellerMinOrderValue(request.UserID, request.Data.Seller, updating, "bill_details_v2")
	if MIN_ORDER_VALUE < 0 {
		return nil, errors.New("failed to get min order value")
	}

	sellerItems, _ := kiranaBazarCart.Cart.Get()

	productIds := make([]string, 0)
	for _, item := range sellerItems {
		productIds = append(productIds, item.ID)
	}

	if len(productIds) == 0 {
		return dto.GetBillDetailsResponse{
			Data: dto.BillDetails{
				ProductsPricing: []shared.ProductPrice{},
				TotalPricing:    []shared.TotalPricing{},
				WaysToPay:       []dto.WaysToPayDataV1{},
				PaymentSection:  dto.PaymentSection{},
				OfferMessage:    nil,
			},
		}, nil
	}

	inStockProductIds, err := s.CheckProductsOutOfStock(productIds)
	if err != nil {
		return nil, err
	}

	productsPricing := []shared.ProductPrice{}
	discountPricing := []shared.DiscountPricing{}
	totalProductPricing := 0.0
	totalRewardCoins := 0

	for _, item := range sellerItems {
		if err != nil {
			fmt.Println("err ", err)
		}
		if !includes(inStockProductIds, item.ID) {
			continue
		}
		meta := shared.KiranaBazarProductMeta{}
		err = json.Unmarshal(item.Meta, &meta)
		if err != nil {
			return nil, err
		}
		productsPricing = append(productsPricing, shared.ProductPrice{
			ID:           item.ID,
			OfferedValue: fmt.Sprintf("%.2f", meta.WholesaleRate),
			Quantity:     int(item.Quantity),
			TotalValue:   fmt.Sprintf("%.2f", meta.WholesaleRate*float64(item.Quantity)*float64(meta.PackSize)),
			ProductName:  meta.HindiName,
			PackSize:     meta.PackSize,
			Size:         meta.Quantity,
			Key:          fmt.Sprintf("%s %s (%d पैकेट्स) x %d", meta.HindiName, meta.Quantity, meta.PackSize, item.Quantity),
			Value:        fmt.Sprintf("₹%.2f", meta.WholesaleRate*float64(item.Quantity)*float64(meta.PackSize)),
			Manufacturer: item.Manufacturer,
		})
		totalProductPricing += meta.WholesaleRate * float64(item.Quantity) * float64(meta.PackSize)
		if meta.RewardCoins != nil {
			totalRewardCoins += *meta.RewardCoins
		}
	}

	totalPricingWithoutDiscount := totalProductPricing

	totalPricingMap := []shared.TotalPricing{}
	discountAmount := 0.0
	discountReason := ""
	var offerMessage *dto.OfferMessage = nil
	var disclaimerMessage string = "“आगे बढ़ें” पर क्लिक करने से कोई पैसा नहीं कटेगा"

	activationCohortUserData := <-activationCohortEligibilityChannel

	if request.Data.Seller == utils.KIRANA_CLUB {
		styles := map[string]interface{}{
			"color":      "#009E7F",
			"fontWeight": 700,
		}

		totalPricingMap = append(totalPricingMap, shared.TotalPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      fmt.Sprintf("₹%d", KC_OFFER_DELIVER_CHARGE),
			TotalValue: KC_OFFER_DELIVER_CHARGE,
			Styles:     &styles,
			Name:       "Service Charge",
			Type:       offers.OFFER_TYPES.CHARGE,
		})

		discountPricing = append(discountPricing, shared.DiscountPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      fmt.Sprintf("₹%d", KC_OFFER_DELIVER_CHARGE),
			TotalValue: KC_OFFER_DELIVER_CHARGE,
			Styles:     &styles,
			Name:       "Service Charge",
			Type:       offers.OFFER_TYPES.CHARGE,
		})
		totalProductPricing += KC_OFFER_DELIVER_CHARGE
		totalPricingWithoutDiscount += KC_OFFER_DELIVER_CHARGE
	}

	// handling Backend Discounts
	tpm, disc, err := s.GetBackendDiscount(totalPricingWithoutDiscount, totalProductPricing, request.Data.Seller, request.UserID, activationCohortUserData)
	if err == nil {
		for _, tpmDiscount := range tpm {
			discValue := tpmDiscount.TotalValue
			value := tpmDiscount.Value
			if discValue > 0 {
				discValue = discValue * -1
			}
			totalPricingMap = append(totalPricingMap, shared.TotalPricing{
				Key:        tpmDiscount.Key,
				Value:      value,
				TotalValue: math.Floor(discValue*100) / 100, // TotalValue is negative here
				Styles:     tpmDiscount.Styles,
			})
			discountPricing = append(discountPricing, shared.DiscountPricing{
				Key:        tpmDiscount.Key,
				Value:      value,
				TotalValue: math.Floor(discValue*100) / 100, // TotalValue is negative here
				Styles:     tpmDiscount.Styles,
				IsInternal: tpmDiscount.IsInternal,
			})

			discountReason = discountReason + fmt.Sprintf("%s %s | ", tpmDiscount.Key, tpmDiscount.Value)
			discountAmount += (-1 * discValue)
		}
		totalProductPricing -= disc
	}

	// handling coupon discounts
	if coupon.Valid && coupon.MinimumAmount <= totalProductPricing {
		discountInt, err := strconv.Atoi(coupon.Discount)
		if err != nil {
			fmt.Println("err ", err)
		}
		discount := float64(discountInt)
		if coupon.PercentageDiscount > 0.0 {
			discount = totalProductPricing * coupon.PercentageDiscount * 0.01
		}

		styles := map[string]interface{}{
			"color":      "#009E7F",
			"fontWeight": 700,
		}

		totalPricingMap = append(totalPricingMap, shared.TotalPricing{
			Key:        coupon.Description,
			Value:      "-₹" + fmt.Sprintf("%0.2f", discount),
			TotalValue: -1 * discount,
			Styles:     &styles,
		})

		discountPricing = append(discountPricing, shared.DiscountPricing{
			Key:        coupon.Description,
			Value:      "-₹" + fmt.Sprintf("%0.2f", discount),
			TotalValue: -1 * discount,
			Styles:     &styles,
			IsInternal: coupon.IsInternal,
		})

		totalProductPricing -= float64(discount)
		discountReason = discountReason + fmt.Sprintf("%s %0.2f | ", coupon.Code, discount)
		discountAmount += discount
	}

	if totalProductPricing >= MIN_ORDER_VALUE && request.Data.Seller != utils.KIRANA_CLUB {
		styles := map[string]interface{}{
			"color":      "#009E7F",
			"fontWeight": 700,
		}

		freeDeliveryString := fmt.Sprintf("₹̶%s फ्री डिलीवरी", utils.GenerateStrikethrough(fmt.Sprintf("%d", 100)))
		totalPricingMap = append(totalPricingMap, shared.TotalPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      freeDeliveryString,
			TotalValue: 0,
			Styles:     &styles,
		})

		discountPricing = append(discountPricing, shared.DiscountPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      freeDeliveryString,
			TotalValue: 0,
			Styles:     &styles,
			IsInternal: true,
		})
	}

	totalRewardCoins = 100
	rewardCoins := dto.RewardCoinsData{}
	err = json.Unmarshal([]byte(utils.RewardsCoinsData), &rewardCoins)
	if err != nil {
		return nil, err
	}
	rewardCoins.Amount = totalRewardCoins
	rewardCoins.Text = fmt.Sprintf("%d किराना कॉइन्स", totalRewardCoins)

	waysToPayData := make([]dto.WaysToPayDataV1, 0)
	err = json.Unmarshal([]byte(utils.WaysToPayData), &waysToPayData)
	if err != nil {
		return nil, err
	}

	paymentSection, _, _, err := s.GetPaymentMethods(request.UserID, request.Data.Seller, totalProductPricing, "")
	if err != nil {
		return nil, err
	}

	var userCashbackOfferComponent *dto.CashbackComponent
	userCashbackBalance := <-userCashbackBalanceChannel
	if userCashbackBalance != nil && *userCashbackBalance > 0 {
		var maxApplicableCashback = math.Min(math.Floor(totalProductPricing*.1), *userCashbackBalance)
		userCashbackBalance = &maxApplicableCashback

		userCashbackOfferComponent = generateLoyaltyCashbackComponent(*userCashbackBalance)
		if request.Data.RequestedCashback > 0 {
			styles := map[string]interface{}{
				"color":      "#009E7F",
				"fontWeight": 700,
			}
			if request.Data.RequestedCashback > *userCashbackBalance {
				request.Data.RequestedCashback = *userCashbackBalance
			}

			if request.Data.RequestedCashback <= totalProductPricing {
				discountPricing = append(discountPricing, shared.DiscountPricing{
					Key:        "कैशबैक",
					Value:      fmt.Sprintf("₹%0.2f", request.Data.RequestedCashback),
					TotalValue: -1 * float64(request.Data.RequestedCashback),
					Styles:     &styles,
					Type:       offers.OFFER_TYPES.CASHBACK,
				})

				discountAmount += float64(request.Data.RequestedCashback)
				discountReason = discountReason + fmt.Sprintf("कैशबैक %0.2f | ", float64(request.Data.RequestedCashback))
				totalProductPricing -= request.Data.RequestedCashback
			} else if request.Data.RequestedCashback > totalProductPricing {
				discountPricing = append(discountPricing, shared.DiscountPricing{
					Key:        "कैशबैक",
					Value:      fmt.Sprintf("₹%0.2f", totalProductPricing),
					TotalValue: -1 * totalProductPricing,
					Styles:     &styles,
					Type:       offers.OFFER_TYPES.CASHBACK,
				})

				discountAmount += totalProductPricing
				discountReason = discountReason + fmt.Sprintf("कैशबैक %0.2f | ", totalProductPricing)
				totalProductPricing = 0
			}
		}
	}
	style := map[string]interface{}{
		"fontWeight": 700,
	}
	totalPricingMap = append(totalPricingMap, shared.TotalPricing{
		Key:        "टोटल",
		Value:      fmt.Sprintf("₹%.2f", totalProductPricing),
		Styles:     &style,
		TotalValue: totalProductPricing,
	})

	if discountAmount > 0 {
		intDiscountAmount := int(discountAmount)
		offerMessage = &dto.OfferMessage{
			Text:           &discountReason,
			DiscountAmount: &intDiscountAmount,
		}
	}

	var orderLoyaltyReward *dto.LoyaltyRewardsComponent
	orderLoyaltyRewardResponse, err := s.calculateUserLoyaltyRewards(request.UserID, request.Meta.AppVersion, totalProductPricing, request.Data.Seller, false)
	if err != nil {
		// slack.SendSlackMessage(fmt.Sprintf("Error in calculating user loyalty rewards %s, cashback %f, total value %f: %v", request.UserID, request.Data.RequestedCashback, totalProductPricing, err))
	}
	if orderLoyaltyRewardResponse != nil && (orderLoyaltyRewardResponse.Cashback > 0 || orderLoyaltyRewardResponse.Coins > 0) {
		orderLoyaltyReward = generateLoyaltyRewardsComponent(*orderLoyaltyRewardResponse)
	}

	response := dto.GetBillDetailsResponse{
		Data: dto.BillDetails{
			ProductsPricing:     productsPricing,
			TotalPricing:        totalPricingMap,
			DiscountPricing:     discountPricing,
			RewardCoins:         &rewardCoins,
			WaysToPay:           waysToPayData,
			PaymentSection:      paymentSection,
			OfferMessage:        offerMessage,
			CashbackComponent:   userCashbackOfferComponent,
			OrderLoyaltyRewards: orderLoyaltyReward,
			RequestedCashback:   request.Data.RequestedCashback,
			Coupon:              request.Data.CouponID,
			Disclaimer:          disclaimerMessage,
			PaymentBanner:       &utils.PaymentBannerURLForBillDetails,
		},
	}

	if totalPricingWithoutDiscount <= MIN_ORDER_VALUE {
		response.Data.EnableOrdering = false
		response.Data.Message = fmt.Sprintf("ऑर्डर देने के लिए न्यूनतम कार्ट मूल्य %d से अधिक होना चाहिए", int(MIN_ORDER_VALUE))
	} else {
		response.Data.EnableOrdering = true
	}
	response.Data.Seller = request.Data.Seller
	return response, nil
}

func (s *Service) GetBillDetails(ctx context.Context, request dto.GetBillDetailsRequest) (interface{}, error) {
	var err error

	var MIN_ORDER_VALUE float64 = userdetails.GetUserSellerMinOrderValue(request.UserID, request.Data.Seller, false, "bill_details")
	if MIN_ORDER_VALUE < 0 {
		return nil, errors.New("failed to get min order value")
	}

	if request.Data.Seller != "" && !includes(brands.GetAllBrands(), request.Data.Seller) {
		return handleONDCInit(s, ctx, request, s.repository)
	}

	activationCohortEligibilityChannel := make(chan *dto.ActivationCohortUserData, 1)
	if request.Data.Seller == utils.ZOFF_FOODS {
		go getActivationCohortUserData(ctx, request.UserID, utils.ZOFF_FOODS, s.GcpRedis, activationCohortEligibilityChannel)
	} else {
		// insert nil in channel to avoid deadlock
		activationCohortEligibilityChannel <- nil
	}

	couponID := request.Data.CouponID
	couponIDInt, err := strconv.Atoi(couponID)
	coupon := dto.Coupon{}
	if err == nil {
		couponResponse, err := s.GetCoupons(ctx, dto.GetCouponsRequest{
			UserID: request.UserID,
			Data: dto.GetCouponData{
				CouponID: couponIDInt,
				Seller:   request.Data.Seller,
			},
		})
		if err != nil {
			return nil, err
		}
		coupon = couponResponse.Data.Coupons[0]
	}
	kiranaBazarCart, err := cart.Get(ctx, request.UserID, request.Data.Seller)
	if err != nil {
		return nil, err
	}

	sellerItems, _ := kiranaBazarCart.Cart.Get()

	productsPricing := []shared.ProductPrice{}
	discountPricing := []shared.DiscountPricing{}
	totalProductPricing := 0.0

	for _, item := range sellerItems {
		if err != nil {
			fmt.Println("err ", err)
		}
		meta := shared.KiranaBazarProductMeta{}
		err = json.Unmarshal(item.Meta, &meta)
		if err != nil {
			return nil, err
		}
		productsPricing = append(productsPricing, shared.ProductPrice{
			ID:           item.ID,
			OfferedValue: fmt.Sprintf("%.2f", meta.WholesaleRate),
			Quantity:     int(item.Quantity),
			TotalValue:   fmt.Sprintf("%.2f", meta.WholesaleRate*float64(item.Quantity)*float64(meta.PackSize)),
			ProductName:  meta.HindiName,
			PackSize:     meta.PackSize,
			Manufacturer: item.Manufacturer,
		})
		totalProductPricing += meta.WholesaleRate * float64(item.Quantity) * float64(meta.PackSize)
	}

	totalPricingWithoutDiscount := totalProductPricing

	totalPricingMap := []shared.TotalPricing{}

	activationCohortUserData := <-activationCohortEligibilityChannel

	// handling Backend Discounts
	tpm, disc, err := s.GetBackendDiscount(totalPricingWithoutDiscount, totalProductPricing, request.Data.Seller, request.UserID, activationCohortUserData)
	if err == nil {
		for _, tpmDiscount := range tpm {
			discValue := tpmDiscount.TotalValue
			value := tpmDiscount.Value
			totalPricingMap = append(totalPricingMap, shared.TotalPricing{
				Key:        tpmDiscount.Key,
				Value:      value,
				TotalValue: math.Floor(discValue*100) / 100,
				Styles:     tpmDiscount.Styles,
			})

			discountPricing = append(discountPricing, shared.DiscountPricing{
				Key:        tpmDiscount.Key,
				Value:      value,
				TotalValue: math.Floor(discValue*100) / 100,
				Styles:     tpmDiscount.Styles,
				IsInternal: tpmDiscount.IsInternal,
			})
		}

		totalProductPricing -= disc
	}

	// handling coupon discounts
	if coupon.Valid && coupon.MinimumAmount <= totalProductPricing {
		discountInt, err := strconv.Atoi(coupon.Discount)
		if err != nil {
			fmt.Println("err ", err)
		}
		discount := float64(discountInt)
		if coupon.PercentageDiscount > 0.0 {
			discount = totalProductPricing * coupon.PercentageDiscount * 0.01
		}

		totalPricingMap = append(totalPricingMap, shared.TotalPricing{
			Key:        coupon.Description,
			Value:      "-₹" + fmt.Sprintf("%.2f", discount),
			TotalValue: math.Floor(-1*discount*100) / 100,
		})

		discountPricing = append(discountPricing, shared.DiscountPricing{
			Key:        coupon.Description,
			Value:      "-₹" + fmt.Sprintf("%.2f", discount),
			TotalValue: math.Floor(-1*discount*100) / 100,
			IsInternal: coupon.IsInternal,
		})

		totalProductPricing -= float64(discount)
	}

	if totalProductPricing > MIN_ORDER_VALUE {
		styles := map[string]interface{}{
			"color":      "#009E7F",
			"fontWeight": 700,
		}

		freeDeliveryString := fmt.Sprintf("₹̶%s फ्री डिलीवरी", utils.GenerateStrikethrough(fmt.Sprintf("%d", 100)))
		discountPricing = append(discountPricing, shared.DiscountPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      freeDeliveryString,
			TotalValue: 0,
			Styles:     &styles,
			IsInternal: true,
		})
	}

	totalPricingMap = append(totalPricingMap, shared.TotalPricing{
		Key:        "टोटल",
		Value:      fmt.Sprintf("%.2f", totalProductPricing),
		TotalValue: totalProductPricing,
	})

	response := dto.GetBillDetailsResponse{
		Data: dto.BillDetails{
			ProductsPricing:   productsPricing,
			TotalPricing:      totalPricingMap,
			DiscountPricing:   discountPricing,
			RequestedCashback: request.Data.RequestedCashback,
		},
	}
	if totalPricingWithoutDiscount <= MIN_ORDER_VALUE {
		response.Data.EnableOrdering = false
		response.Data.Message = fmt.Sprintf("ऑर्डर देने के लिए न्यूनतम कार्ट मूल्य %d से अधिक होना चाहिए", int(MIN_ORDER_VALUE))
	} else {
		response.Data.EnableOrdering = true
	}
	response.Data.Seller = request.Data.Seller
	return response, nil
}

func (s *Service) CreateOrderInvoice(ctx context.Context, request dto.CreateInvoiceRequest) (response dto.CreateInvoiceResponse, err error) {
	orderID, err := strconv.ParseInt(request.OrderID, 10, 64)
	if err != nil {
		return
	}
	orderInfo := dao.KiranaBazarOrder{}
	orderDetail := dao.KiranaBazarOrderDetail{}
	orderDetails := dao.KiranaBazarOrderDetails{}
	paymentDetails := dao.KiranaBazarOrderPayment{}
	_, err = s.repository.Find(map[string]interface{}{
		"id": orderID,
	}, &orderInfo)
	if err != nil {
		return
	}
	_, err = s.repository.Find(map[string]interface{}{
		"order_id": orderID,
	}, &orderDetail)
	if err != nil {
		return
	}

	_, err = s.repository.Find(map[string]interface{}{
		"order_id": orderID,
	}, &paymentDetails)
	if err != nil {
		return
	}

	byt, err := json.Marshal(orderDetail.OrderDetails)
	if err != nil {
		return
	}
	err = json.Unmarshal(byt, &orderDetails)
	if err != nil {
		return
	}

	cfg := config.DefaultConfig()
	gen, err := generator.NewImageGenerator(cfg)

	url, err := gen.GenerateOrderImage(orderDetails, paymentDetails, fmt.Sprintf("KC_%06d", orderID), orderInfo.Seller)
	if err != nil {
		return
	}
	response.Data.InvoiceUrl = url
	response.Data.OrderID = request.OrderID

	go func(orderid int64, url string, repo *sqlRepo.Repository) {
		repo.CustomQuery(nil, fmt.Sprintf(`update kiranabazar_order_payments set kc_invoice = "%s" where order_id = %d`, url, orderid))
	}(orderID, url, s.repository)
	return
}

func (s *Service) BillDifference(ctx context.Context, request dto.BillDifferenceRequest) (interface{}, error) {

	orderID := request.Data.OrderID
	orderDetails, err := GetOrderDetails(s.repository, orderID)
	if err != nil {
		return nil, err
	}

	res, err := s.GetBillDetailsV2(ctx, dto.GetBillDetailsRequest{
		Data:   request.Data.GetBillDetailsData,
		UserID: request.UserID,
		Meta:   request.Meta,
	})

	billv2 := res.(dto.GetBillDetailsResponse)
	bye, _ := json.Marshal(billv2.Data)
	changredBills := dto.BillDetails{}
	json.Unmarshal(bye, &changredBills)

	dtoBillDetailsOld := orderDetails.BillBreakUp
	currentBils := dto.BillDetails{
		ProductsPricing: dtoBillDetailsOld.ProductsPricing,
		TotalPricing:    dtoBillDetailsOld.TotalPricing,
		DiscountPricing: dtoBillDetailsOld.DiscountPricing,
		EnableOrdering:  dtoBillDetailsOld.EnableOrdering,
		Message:         dtoBillDetailsOld.Message,
		Coupon:          dtoBillDetailsOld.Coupon,
	}

	diff, err := CompareBills(currentBils, changredBills)
	if err != nil {
		return "", err
	}
	return diff, nil
}

func (s *Service) addPromotionalItems(promoItems []shared.SellerItems) []shared.ProductPrice {
	var productsPricing []shared.ProductPrice

	for _, item := range promoItems {
		meta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(item.Meta, &meta)
		if err != nil {
			fmt.Println("Error unmarshalling promo item meta:", err)
			continue
		}

		productsPricing = append(productsPricing, shared.ProductPrice{
			ID:           item.ID,
			OfferedValue: fmt.Sprintf("%.2f", meta.WholesaleRate),
			Quantity:     int(item.Quantity),
			TotalValue:   fmt.Sprintf("%.2f", meta.WholesaleRate*float64(item.Quantity)*float64(meta.PackSize)),
			ProductName:  meta.HindiName,
			PackSize:     meta.PackSize,
			Size:         meta.Quantity,
			Key:          fmt.Sprintf("%s %s (%d पैकेट्स) x %d", meta.HindiName, meta.Quantity, meta.PackSize, item.Quantity),
			Value:        fmt.Sprintf("₹%.2f", meta.WholesaleRate*float64(item.Quantity)*float64(meta.PackSize)),
			Manufacturer: item.Manufacturer,
		})
	}

	return productsPricing
}

func (s *Service) ProcessCart(ctx context.Context, kiranaBazarCart *cart.KiranaBazarCart,
	activationCohortEligibilityChannel chan *dto.ActivationCohortUserData,
	request dto.GetBillDetailsRequest, MIN_ORDER_VALUE float64, userDetails userdetails.AsyncResult, shippingAddress *dao.UserAddress, processVirtualSku bool) ([]shared.ProductPrice, []shared.DiscountPricing,
	[]shared.TotalPricing, []shared.AppliedCoupon, float64, string, float64, int, float64, *dto.ActivationCohortUserData, *coupons.Coupon, *dto.OfferMessage, []shared.SellerItems, []shared.SellerItems, error) {
	var (
		productsPricing             []shared.ProductPrice
		discountPricing             []shared.DiscountPricing
		totalPricingMap             []shared.TotalPricing
		appliedCoupons              []shared.AppliedCoupon
		promoItems                  []shared.SellerItems
		virtualSkus                 []shared.SellerItems
		discountAmount              float64 = 0.0
		discountReason              string  = ""
		totalProductPricing         float64 = 0.0
		totalRewardCoins            int     = 0
		totalPricingWithoutDiscount float64 = 0.0
		err                         error
		activationCohortUserData    *dto.ActivationCohortUserData
		lastAppliedCoupon           *coupons.Coupon
	)

	appVersion := request.Meta.AppVersion

	// Process products from cart
	productsPricing, virtualSkus, totalProductPricing, totalRewardCoins, err = s.processCartProducts(kiranaBazarCart, appVersion, processVirtualSku)
	if err != nil {
		return productsPricing, discountPricing, totalPricingMap, appliedCoupons, discountAmount, discountReason, totalProductPricing, totalRewardCoins, totalProductPricing, activationCohortUserData, nil, nil, promoItems, virtualSkus, err
	}
	totalPricingWithoutDiscount = totalProductPricing
	discountPricing, totalPricingMap, appliedCoupons, discountAmount, discountReason, totalProductPricing, activationCohortUserData, lastAppliedCoupon, promoItems, err = s.processDiscounts(
		ctx,
		activationCohortEligibilityChannel,
		request,
		totalPricingWithoutDiscount,
		totalProductPricing,
		MIN_ORDER_VALUE,
		&userDetails,
	)
	if err != nil {
		// Log the error but continue - discounts aren't critical
		fmt.Println("Error processing discounts:", err)
	}

	// Add promoItems in CART product pricing
	promoProductsPricing := s.addPromotionalItems(promoItems)
	productsPricing = append(productsPricing, promoProductsPricing...)

	chargesDiscountPricing, chargesTotalPricingMap, _, totalProductPricing, totalPricingWithoutDiscount, chargeMessageData, err := s.processTaxesCharges(
		ctx,
		request,
		totalPricingWithoutDiscount,
		totalProductPricing,
		MIN_ORDER_VALUE,
		&userDetails,
		shippingAddress,
	)
	if err == nil {
		discountPricing = append(discountPricing, chargesDiscountPricing...)
		totalPricingMap = append(totalPricingMap, chargesTotalPricingMap...)
	} else {
		// Log the error but continue - discounts aren't critical
		fmt.Println("Error processing discounts:", err)
	}

	var eligibleCharges *dto.OfferMessage = nil
	if chargeMessageData != nil && chargeMessageData.DiffAmount > 0 {
		formatMap := map[string]interface{}{}
		formatMap["amount"] = chargeMessageData.DiffAmount

		formattedChargeMessage := dto.NewMessageBuilder().
			WithFormatValues(formatMap).
			WithText(chargeMessageData.Message).
			WithHighlightedTexts(chargeMessageData.HighlightedTexts).
			WithIconType(chargeMessageData.IconType).
			WithMinOrderValue(int(MIN_ORDER_VALUE)).
			WithDiscountAmount(int(chargeMessageData.MaxCartValue)).
			WithDiscount(0.0).
			Build()

		eligibleCharges = formattedChargeMessage
	}

	return productsPricing, discountPricing, totalPricingMap, appliedCoupons, discountAmount, discountReason, totalProductPricing,
		totalRewardCoins, totalPricingWithoutDiscount, activationCohortUserData, lastAppliedCoupon, eligibleCharges, promoItems, virtualSkus, nil
}

func (s *Service) processCartProducts(kiranaBazarCart *cart.KiranaBazarCart, appVersion string, processVirtualSku bool) ([]shared.ProductPrice, []shared.SellerItems, float64, int, error) {
	var productsPricing []shared.ProductPrice
	totalProductPricing := 0.0
	totalRewardCoins := 0

	// Get cart items and filter valid products
	sellerItems, _ := kiranaBazarCart.Cart.Get()
	inStockProducts, _, _, virtualSkus := kiranaBazarCart.FilterAndUpdateProducts(sellerItems, cart.CartValidationCoinditions{
		IncludeVariants: true,
	}, appVersion, processVirtualSku)

	if len(inStockProducts) == 0 {
		return productsPricing, virtualSkus, 0.0, 0, errors.New(exceptions.OosErrorMessage)
		// return productsPricing, 0.0, 0, nil
	}

	kiranaBazarCart.Cart.Set(inStockProducts)

	// Process each product
	for _, item := range inStockProducts {
		meta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(item.Meta, &meta)
		if err != nil {
			return nil, virtualSkus, 0.0, 0, err
		}

		productsPricing = append(productsPricing, shared.ProductPrice{
			ID:           item.ID,
			OfferedValue: fmt.Sprintf("%.2f", meta.WholesaleRate),
			Quantity:     int(item.Quantity),
			TotalValue:   fmt.Sprintf("%.2f", kiranaBazarCart.GetProductPricing(item, &meta)),
			ProductName:  meta.HindiName,
			PackSize:     meta.PackSize,
			Size:         meta.Quantity,
			Key:          fmt.Sprintf("%s %s (%d पैकेट्स) x %d", meta.HindiName, meta.Quantity, meta.PackSize, item.Quantity),
			Value:        fmt.Sprintf("₹%.2f", kiranaBazarCart.GetProductPricing(item, &meta)),
			Manufacturer: item.Manufacturer,
		})

		totalProductPricing += kiranaBazarCart.GetProductPricing(item, &meta)
		if meta.RewardCoins != nil {
			totalRewardCoins += *meta.RewardCoins
		}
	}
	return productsPricing, virtualSkus, totalProductPricing, totalRewardCoins, nil
}

// Process all discounts - backend, coupon, delivery
func (s *Service) processDiscounts(ctx context.Context, activationCohortEligibilityChannel chan *dto.ActivationCohortUserData,
	request dto.GetBillDetailsRequest, totalPricingWithoutDiscount float64, totalProductPricing float64, MIN_ORDER_VALUE float64, userDetails *userdetails.AsyncResult,
) ([]shared.DiscountPricing, []shared.TotalPricing, []shared.AppliedCoupon, float64, string, float64, *dto.ActivationCohortUserData, *coupons.Coupon, []shared.SellerItems, error) {
	var discountPricing []shared.DiscountPricing
	var totalPricingMap []shared.TotalPricing
	var appliedCoupons []shared.AppliedCoupon
	var promoItems []shared.SellerItems
	discountAmount := 0.0
	discountReason := ""

	// Get activation cohort data
	activationCohortUserData := <-activationCohortEligibilityChannel

	// Process backend discounts
	backendDiscountPricing, backendTotalPricing, appliedBackendCoupons, backendDiscountAmount, backendDiscountReason, updatedTotalProductPricing, lastAppliedCoupon, promoItemsCart, err := s.processBackendDiscounts(
		ctx,
		dto.ProcessBackendDiscountsData{
			Seller:                   request.Data.Seller,
			UserID:                   request.UserID,
			CouponID:                 request.Data.CouponID,
			AppVersion:               request.Meta.AppVersion,
			ActivationCohortUserData: activationCohortUserData,
			TotalProductPricing:      totalProductPricing,
		},
		userDetails,
	)

	if err == nil {
		// Append results
		discountPricing = append(discountPricing, backendDiscountPricing...)
		totalPricingMap = append(totalPricingMap, backendTotalPricing...)
		discountAmount += backendDiscountAmount
		discountReason += backendDiscountReason
		totalProductPricing = updatedTotalProductPricing
		promoItems = promoItemsCart
	}

	// Process coupon discounts
	couponDiscountPricing, couponTotalPricing, couponAppliedAppCoupons, couponDiscountAmount, couponDiscountReason, updatedTotalProductPricing, err := s.processCouponDiscount(
		ctx,
		request,
		totalProductPricing,
		userDetails,
	)
	if err == nil {
		// Append results
		discountPricing = append(discountPricing, couponDiscountPricing...)
		totalPricingMap = append(totalPricingMap, couponTotalPricing...)
		discountAmount += couponDiscountAmount
		discountReason += couponDiscountReason
		totalProductPricing = updatedTotalProductPricing
	}

	appliedCoupons = append(appliedCoupons, appliedBackendCoupons...)
	appliedCoupons = append(appliedCoupons, couponAppliedAppCoupons...)

	return discountPricing, totalPricingMap, appliedCoupons, discountAmount, discountReason, totalProductPricing, activationCohortUserData, lastAppliedCoupon, promoItems, nil
}

func (s *Service) processTaxesCharges(ctx context.Context, request dto.GetBillDetailsRequest,
	totalPricingWithoutDiscount float64, totalProductPricing float64, MIN_ORDER_VALUE float64, userDetails *userdetails.AsyncResult, shippingAddress *dao.UserAddress) ([]shared.DiscountPricing,
	[]shared.TotalPricing, float64, float64, float64, *charges.ChargeMessage, error) {
	var discountPricing []shared.DiscountPricing
	var totalPricingMap []shared.TotalPricing
	taxesChargesAmount := 0.0

	// Process backend discounts
	chargesDiscountPricing, chargesTotalPricing, chargesAmount,
		updatedTotalProductPricing, updatedTotalPricingWithoutDiscount, chargeMessage, err := s.processCharges(
		ctx,
		request,
		totalPricingWithoutDiscount,
		totalProductPricing,
		userDetails,
		shippingAddress,
	)

	if err == nil {
		discountPricing = append(discountPricing, chargesDiscountPricing...)
		totalPricingMap = append(totalPricingMap, chargesTotalPricing...)
		taxesChargesAmount += chargesAmount
		totalProductPricing = updatedTotalProductPricing
		totalPricingWithoutDiscount = updatedTotalPricingWithoutDiscount
	}

	if totalProductPricing >= MIN_ORDER_VALUE {
		deliveryDiscountPricing, deliveryTotalPricing := s.processDeliveryDiscount(request.Data.Seller)
		discountPricing = append(discountPricing, deliveryDiscountPricing...)
		totalPricingMap = append(totalPricingMap, deliveryTotalPricing...)
	}

	return discountPricing, totalPricingMap, taxesChargesAmount, totalProductPricing, totalPricingWithoutDiscount, chargeMessage, nil
}

// Process backend discounts
func (s *Service) processBackendDiscounts(ctx context.Context, processBackendDiscountsData dto.ProcessBackendDiscountsData, userDetails *userdetails.AsyncResult) ([]shared.DiscountPricing,
	[]shared.TotalPricing, []shared.AppliedCoupon, float64, string, float64, *coupons.Coupon, []shared.SellerItems, error) {
	discountPricing := []shared.DiscountPricing{}
	totalPricingMap := []shared.TotalPricing{}
	appliedBackendCoupons := make([]shared.AppliedCoupon, 0)
	discountAmount := 0.0
	discountReason := ""
	totalProductPricing := processBackendDiscountsData.TotalProductPricing
	promoItems := make([]shared.SellerItems, 0)

	// Prepare activation coupon if available
	var activationCoupon int64
	var err error
	if processBackendDiscountsData.ActivationCohortUserData != nil && processBackendDiscountsData.ActivationCohortUserData.Eligible {
		activationCoupon, err = strconv.ParseInt(processBackendDiscountsData.ActivationCohortUserData.CouponId, 10, 64)
		if err != nil {
			activationCoupon = 0
		}
	}

	// Calculate backend discounts
	backendCoupons, err := s.Coupons.CalculateBackendDiscount(
		ctx,
		processBackendDiscountsData.Seller,
		processBackendDiscountsData.UserID,
		&activationCoupon,
		&processBackendDiscountsData.CouponID,
		userDetails,
	)

	if err != nil {
		return discountPricing, totalPricingMap, appliedBackendCoupons, discountAmount, discountReason, totalProductPricing, nil, nil, err
	}

	// Process each backend coupon
	style := map[string]interface{}{
		"color":      "#009E7F",
		"fontWeight": 700,
	}

	var lastAppliedCoupon *coupons.Coupon = nil

	for _, tpmCoupon := range backendCoupons {
		if tpmCoupon == nil || !tpmCoupon.IsValid {
			continue
		}

		// Format discount value
		discValue := tpmCoupon.DiscountAmount
		var value string
		if discValue > 0 {
			value = fmt.Sprintf("-₹%.2f", discValue)
			discValue = discValue * -1
		} else {
			value = fmt.Sprintf("₹%.2f", discValue)
		}

		var description string = tpmCoupon.Coupon.Name
		roundedDiscValue := math.Floor(discValue*100) / 100

		// Add to pricing mapsd
		totalPricingMap = append(totalPricingMap, shared.TotalPricing{
			Key:        description,
			Value:      value,
			TotalValue: roundedDiscValue,
			Styles:     &style,
			Name:       tpmCoupon.Coupon.Key,
			ID:         tpmCoupon.Coupon.ID,
		})

		discountPricing = append(discountPricing, shared.DiscountPricing{
			Key:        description,
			Value:      value,
			TotalValue: roundedDiscValue,
			Styles:     &style,
			IsInternal: tpmCoupon.Coupon.IsInternal,
			Name:       tpmCoupon.Coupon.Key,
			ID:         tpmCoupon.Coupon.ID,
		})

		// Update discount reason and amounts
		discountReason = discountReason + fmt.Sprintf("%s %s | ", description, value)
		discountAmount += (-1 * discValue)
		totalProductPricing -= (-1 * discValue)
		lastAppliedCoupon = tpmCoupon.Coupon

		appliedCouponObject := getAppliedCouponObject(
			tpmCoupon.Coupon,
			processBackendDiscountsData.Seller,
			false, // don't show backend coupons code
		)
		if appliedCouponObject != nil {
			appliedBackendCoupons = append(appliedBackendCoupons, *appliedCouponObject)
		}

		promoItem := getAppliedPromoItemCartObject(tpmCoupon.Coupon, processBackendDiscountsData.Seller, processBackendDiscountsData.AppVersion)
		if promoItem != nil {
			promoItems = append(promoItems, *promoItem)
		}
	}

	return discountPricing, totalPricingMap, appliedBackendCoupons, discountAmount, discountReason, totalProductPricing, lastAppliedCoupon, promoItems, nil
}

// Process coupon discount
func (s *Service) processCouponDiscount(ctx context.Context, request dto.GetBillDetailsRequest, totalProductPricing float64, userDetails *userdetails.AsyncResult) ([]shared.DiscountPricing,
	[]shared.TotalPricing, []shared.AppliedCoupon, float64, string, float64, error) {
	var discountPricing []shared.DiscountPricing
	var totalPricingMap []shared.TotalPricing
	appliedAppCoupons := make([]shared.AppliedCoupon, 0)
	discountAmount := 0.0
	discountReason := ""

	// Validate coupon
	couponsData, err := s.Coupons.ValidateCoupon(ctx, request.Data.CouponID, request.Data.Seller, request.UserID, userDetails, true)

	if err != nil || !couponsData.IsValid {
		return discountPricing, totalPricingMap, appliedAppCoupons, discountAmount, discountReason, totalProductPricing, nil
	}

	// Calculate discount
	discount := couponsData.DiscountAmount
	styles := map[string]interface{}{
		"color":      "#009E7F",
		"fontWeight": 700,
	}

	var description string = couponsData.Coupon.Name
	// Add to pricing maps
	totalPricingMap = append(totalPricingMap, shared.TotalPricing{
		Key:        description,
		Value:      "-₹" + fmt.Sprintf("%0.2f", discount),
		TotalValue: -1 * discount,
		Styles:     &styles,
		Name:       couponsData.Coupon.Key,
		ID:         couponsData.Coupon.ID,
	})

	discountPricing = append(discountPricing, shared.DiscountPricing{
		Key:        description,
		Value:      "-₹" + fmt.Sprintf("%0.2f", discount),
		TotalValue: -1 * discount,
		Styles:     &styles,
		IsInternal: couponsData.Coupon.IsInternal,
		Name:       couponsData.Coupon.Key,
		ID:         couponsData.Coupon.ID,
	})

	appliedCouponObject := getAppliedCouponObject(
		couponsData.Coupon,
		request.Data.Seller,
		true, // show coupon code in app
	)

	if appliedCouponObject != nil {
		appliedAppCoupons = append(appliedAppCoupons, *appliedCouponObject)
	}

	// Update total pricing and discount info
	totalProductPricing -= float64(discount)
	discountReason = discountReason + fmt.Sprintf("%s %0.2f | ", couponsData.Coupon.Code, discount)
	discountAmount += discount

	return discountPricing, totalPricingMap, appliedAppCoupons, discountAmount, discountReason, totalProductPricing, nil
}

func (s *Service) processCharges(ctx context.Context, request dto.GetBillDetailsRequest, totalPricingWithoutDiscount float64,
	totalProductPricing float64, userDetails *userdetails.AsyncResult, shippingAddress *dao.UserAddress) ([]shared.DiscountPricing, []shared.TotalPricing, float64, float64, float64, *charges.ChargeMessage, error) {
	var discountPricing = make([]shared.DiscountPricing, 0)
	var totalPricingMap = make([]shared.TotalPricing, 0)
	var totalChanges float64 = 0.0
	var chargeMessage *charges.ChargeMessage = nil
	styles := map[string]interface{}{
		"color":      "#009E7F",
		"fontWeight": 700,
	}

	applicabelCharges := make([]charges.AppliedCharge, 0)

	applicabelCharges = charges.GetApplicableCharges(request.UserID, request.Data.Seller, userDetails, shippingAddress, totalPricingWithoutDiscount)

	if applicabelCharges == nil {
		freeDeliveryString := fmt.Sprintf("₹̶%s फ्री डिलीवरी", utils.GenerateStrikethrough(fmt.Sprintf("%d", 100)))

		totalPricingMap = append(totalPricingMap, shared.TotalPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      freeDeliveryString,
			TotalValue: 0,
			Styles:     &styles,
			Name:       "Delivery Charge",
		})

		discountPricing = append(discountPricing, shared.DiscountPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      freeDeliveryString,
			TotalValue: 0,
			Styles:     &styles,
			IsInternal: true,
			Name:       "Delivery Charge",
		})
		return discountPricing, totalPricingMap, totalChanges, totalProductPricing, totalPricingWithoutDiscount, chargeMessage, nil
	}

	for _, chrg := range applicabelCharges {
		chrgAmount := chrg.Amount
		chrgDisplayName := chrg.DisplayName
		totalPricingMap = append(totalPricingMap, shared.TotalPricing{
			Key:        chrgDisplayName,
			Value:      fmt.Sprintf("₹%0.2f", chrgAmount),
			TotalValue: chrgAmount,
			Styles:     &styles,
			Name:       "Service Charge",
			Type:       offers.OFFER_TYPES.CHARGE,
		})

		discountPricing = append(discountPricing, shared.DiscountPricing{
			Key:        chrgDisplayName,
			Value:      fmt.Sprintf("₹%0.2f", chrgAmount),
			TotalValue: chrgAmount,
			Styles:     &styles,
			Name:       "Service Charge",
			Type:       offers.OFFER_TYPES.CHARGE,
		})
		totalProductPricing += chrgAmount
		totalPricingWithoutDiscount += chrgAmount
		totalChanges += chrgAmount
		if chrg.ChargeMessage != nil {
			chargeMessage = chrg.ChargeMessage
		}
	}
	return discountPricing, totalPricingMap, totalChanges, totalProductPricing, totalPricingWithoutDiscount, chargeMessage, nil
}

// Process delivery discount
func (s *Service) processDeliveryDiscount(seller string) ([]shared.DiscountPricing, []shared.TotalPricing) {
	var discountPricing = make([]shared.DiscountPricing, 0)
	var totalPricing = make([]shared.TotalPricing, 0)

	// if seller != utils.KIRANA_CLUB {
	// 	styles := map[string]interface{}{
	// 		"color":      "#009E7F",
	// 		"fontWeight": 700,
	// 	}

	// 	freeDeliveryString := fmt.Sprintf("₹̶%s फ्री डिलीवरी", utils.GenerateStrikethrough(fmt.Sprintf("%d", 100)))

	// 	totalPricing = append(totalPricing, shared.TotalPricing{
	// 		Key:        "डिलीवरी चार्ज",
	// 		Value:      freeDeliveryString,
	// 		TotalValue: 0,
	// 		Styles:     &styles,
	// 		Name:       "Delivery Charge",
	// 	})

	// 	discountPricing = append(discountPricing, shared.DiscountPricing{
	// 		Key:        "डिलीवरी चार्ज",
	// 		Value:      freeDeliveryString,
	// 		TotalValue: 0,
	// 		Styles:     &styles,
	// 		IsInternal: true,
	// 		Name:       "Delivery Charge",
	// 	})
	// }
	return discountPricing, totalPricing
}

// Helper function to process cashback application
func (s *Service) processCashbackApplication(userCashbackBalance *float64, requestedCashback float64, totalProductPricing float64,
	discountPricing *[]shared.DiscountPricing, discountReason *string, discountAmount *float64,
	userDetails userdetails.AsyncResult, seller string, cashbackApplied *bool) (*dto.CashbackComponent,
	float64, float64, float64, error) {
	var userCashbackOfferComponent *dto.CashbackComponent
	var cashbackDiscounting shared.DiscountPricing
	var appliedCashback float64 = 0.0

	if userCashbackBalance == nil || *userCashbackBalance <= 0 || seller == utils.KIRANA_CLUB {
		return nil, appliedCashback, totalProductPricing, requestedCashback, nil
	}

	userCohortNames := make([]string, 0)
	if userDetails.Data != nil {
		if userDetails.Data.UserDynamicDetails != nil {
			userCohortNames = userDetails.Data.UserDynamicDetails.UserCohortNames
		}
	}

	cashbackFactor := 0.1
	if userCohortNames != nil && includes(userCohortNames, "26April200CashbackCohort") {
		cashbackFactor = 0.05
	}

	var maxApplicableCashback = math.Min(math.Floor(totalProductPricing*cashbackFactor), *userCashbackBalance)

	if cashbackApplied == nil {
		requestedCashback = maxApplicableCashback
	}

	userCashbackBalance = &maxApplicableCashback
	// Generate cashback component
	userCashbackOfferComponent = generateLoyaltyCashbackComponent(*userCashbackBalance)

	// Apply cashback if requested
	if requestedCashback <= 0 {
		return userCashbackOfferComponent, appliedCashback, totalProductPricing, requestedCashback, nil
	}

	styles := map[string]interface{}{
		"color":      "#009E7F",
		"fontWeight": 700,
	}

	// Adjust requested cashback if exceeds available balance
	if requestedCashback > *userCashbackBalance {
		requestedCashback = *userCashbackBalance
	}

	// Apply cashback based on available amount
	if requestedCashback <= totalProductPricing {
		cashbackDiscounting = shared.DiscountPricing{
			Key:        "कैशबैक",
			Value:      fmt.Sprintf("₹%0.2f", requestedCashback),
			TotalValue: -1 * float64(requestedCashback),
			Styles:     &styles,
			IsInternal: true,
			Type:       offers.OFFER_TYPES.CASHBACK,
			Name:       offers.OFFER_TYPES.CASHBACK,
		}

		*discountReason = *discountReason + fmt.Sprintf("कैशबैक %0.2f | ", float64(requestedCashback))
		*discountAmount += float64(requestedCashback)
		totalProductPricing -= requestedCashback
		appliedCashback = requestedCashback
	} else {
		cashbackDiscounting = shared.DiscountPricing{
			Key:        "कैशबैक",
			Value:      fmt.Sprintf("₹%0.2f", totalProductPricing),
			TotalValue: -1 * totalProductPricing,
			Styles:     &styles,
			IsInternal: true,
			Type:       offers.OFFER_TYPES.CASHBACK,
			Name:       offers.OFFER_TYPES.CASHBACK,
		}

		*discountReason = *discountReason + fmt.Sprintf("कैशबैक %0.2f | ", totalProductPricing)
		*discountAmount += totalProductPricing
		appliedCashback = totalProductPricing
		totalProductPricing = 0
	}

	*discountPricing = append(*discountPricing, cashbackDiscounting)

	return userCashbackOfferComponent, appliedCashback, totalProductPricing, requestedCashback, nil
}

func getAppliedCouponObject(coupon *coupons.Coupon, seller string, showCode bool) *shared.AppliedCoupon {
	brandImageUrl := ""
	brandMetaData, exists := brands.GetBrandMetaBySeller(seller)
	if exists {
		brandImageUrl = brandMetaData.Logo
	}
	codeText := "Coupon Applied!"
	if showCode {
		codeText = coupon.Code
	}
	return &shared.AppliedCoupon{
		Code:        codeText,
		Description: coupon.Description.String,
		// Text:        "Coupon Applied!",
		ID:       fmt.Sprint(coupon.ID),
		ImageUrl: brandImageUrl,
	}
}

func getAppliedPromoItemCartObject(coupon *coupons.Coupon, seller string, appVersion string) *shared.SellerItems {
	if coupon == nil {
		return nil
	}

	if coupon.DiscountType != coupons.DiscountTypeFreebie {
		return nil
	}

	if len(coupon.Rules) == 0 {
		return nil
	}

	// get product id from rules
	var productId *string
	var productQuantity int
	var productRuleType string
	for _, rule := range coupon.Rules {
		if rule.RuleType == "freebie" {
			ruleValue, err := rule.GetCouponRules()
			if err != nil {
				fmt.Println("Error getting coupon rules:", err)
				continue
			}
			if ruleValue != nil && ruleValue.ProductId != nil && *ruleValue.ProductId != "" {
				productId = ruleValue.ProductId
				productRuleType = rule.RuleType
				productQuantity = 1
				if ruleValue.Quantity != nil {
					productQuantity = *ruleValue.Quantity
				}
				break
			}
		}
	}

	if productId == nil {
		return nil
	}
	productInfo, exists := products.GetProductByID(*productId)
	if !exists {
		fmt.Println("Product not found for ID:", *productId)
		return nil
	}
	productType := productInfo.ProductType
	if productType != string(products.ProductTypePosm) {
		return nil
	}

	productCartObject := productInfo.ToSellerItems(products.ToSellerItemsCondition{
		IncludeVariant: true,
	}, appVersion, nil)
	//TODO: To use conditional pricing we need to pass userContext

	productCartObject.ProductType = &productRuleType
	productCartObject.Quantity = int32(productQuantity)

	return &productCartObject
}
