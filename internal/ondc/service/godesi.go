package service

import (
	"context"
	"kc/internal/ondc/external/godesi"
	"kc/internal/ondc/models/dto"
	"strconv"
)

func (s *Service) CheckGodesiServiceAbility(ctx context.Context, request *godesi.CourierServiceAblityAPIRequest) (*dto.AppserviceAbilityAPIResponse, error) {
	tat, availability := godesi.CheckServiceAbility(request.DeliveryPostCode)
	return &dto.AppserviceAbilityAPIResponse{
		Data: dto.AppServiceAbilityAPIData{
			Servicable:          availability,
			Message:             "",
			MinimumDeliveryDays: strconv.Itoa(tat),
		},
	}, nil
}
