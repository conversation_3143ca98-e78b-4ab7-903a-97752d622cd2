package service

import (
	"context"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/logistics/couriers"
	orderstatus "kc/internal/ondc/service/orderStatus"
	"kc/internal/ondc/utils"
	"strconv"
	"time"

	"github.com/mixpanel/mixpanel-go"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
)

// HandleShipmentCreatedOrder processes a shipment created event for an order
// Updates order status, tracking details, and sends related analytics events
func (s *Service) HandleShipmentCreatedOrder(ctx context.Context, req dto.ShipmentCreatedRequest) (any, error) {
	logger := logrus.WithContext(ctx).WithField("function", "HandleShipmentCreatedOrder")

	// Parse order ID
	orderID, err := strconv.ParseInt(req.Data.OrderID, 10, 64)
	if err != nil {
		err = errors.Wrap(err, "failed to parse order ID")
		logger.WithError(err).WithField("order_id", req.Data.OrderID).Error("Invalid order ID format")
		slack.SendSlackMessage(fmt.Sprintf("Error parsing order ID: %v", err))
		return nil, err
	}
	logger = logger.WithField("order_id", orderID)

	// Fetch order information
	orderInfo, err := GetOrderInfo(s.repository, orderID)
	if err != nil {
		err = errors.Wrap(err, "failed to fetch order info")
		logger.WithError(err).Error("Failed to retrieve order information")
		slack.SendSlackMessage(fmt.Sprintf("Error fetching order info: %v", err))
		return nil, err
	}

	// Check if order status is already updated (idempotency check)
	if orderInfo.OrderStatus != nil && *orderInfo.OrderStatus == req.Data.OrderStatus {
		logger.Info("Order status already updated, skipping processing")
		return nil, nil
	}

	// Fetch order details
	orderDetails, err := GetOrderDetails(s.repository, fmt.Sprintf("%d", orderID))
	if err != nil {
		err = errors.Wrap(err, "failed to fetch order details")
		logger.WithError(err).Error("Failed to retrieve order details")
		slack.SendSlackMessage(fmt.Sprintf("Error fetching order details: %v", err))
		return nil, err
	}

	// Process updates concurrently
	s.processOrderUpdates(ctx, orderID, req)

	// Process reconciliation and analytics
	freshShipmentCreated, err := s.processReconciliation(ctx, orderID, orderInfo, req)
	if err != nil {
		logger.WithError(err).Warn("Reconciliation processing encountered errors")
		// Continue execution as this should not block the main flow
	}

	// Process OMS and shipment analytics events
	if freshShipmentCreated {
		s.processAnalyticsEvents(ctx, orderID, orderInfo, orderDetails, req)
	}

	// Send order status update notification
	s.sendOrderStatusUpdateEvent(ctx, orderID, orderInfo, orderDetails, req)

	return nil, nil
}

// processOrderUpdates updates tracking details and order status in the database
func (s *Service) processOrderUpdates(ctx context.Context, orderID int64, req dto.ShipmentCreatedRequest) {
	logger := logrus.WithContext(ctx).WithField("order_id", orderID)

	// Update tracking details if available
	if req.Data.AWBNumber != "" && req.Data.Courier != "" {
		go func() {
			err := UpdateOrderTrackingDetails(s.repository, orderID, req.Data.Courier, req.Data.AWBNumber, req.Data.UpdatedBy)
			if err != nil {
				logger.WithError(err).Error("Failed to update order tracking details")
				slack.SendSlackMessage(fmt.Sprintf("Error updating order tracking details: %v", err))
			}
		}()
	}

	// Update order status
	go func() {
		err := UpdateOrderStatus(s.repository, orderID, req.Data.OrderStatus, req.Data.StatusType)
		if err != nil {
			logger.WithError(err).Error("Failed to update order status")
			slack.SendSlackMessage(fmt.Sprintf("Error updating order status: %v", err))
		}
	}()
}

// processReconciliation adds shipment creation timestamp to reconciliation data
// Returns whether this is a fresh shipment creation event
func (s *Service) processReconciliation(ctx context.Context, orderID int64, orderInfo dao.KiranaBazarOrder, req dto.ShipmentCreatedRequest) (bool, error) {
	logger := logrus.WithContext(ctx).WithField("order_id", orderID)

	// Determine shipment creation timestamp
	shipmentCreationTime := time.Now().UnixMilli()
	if req.Data.ShipmentCreatedAt != 0 {
		shipmentCreationTime = req.Data.ShipmentCreatedAt
	}

	// Add reconciliation data
	reconReq := &dto.AddReconciliationRequest{
		OrderID: orderID,
		Data: []dto.StatusTimeStamp{
			{
				TimeStamp:   shipmentCreationTime,
				OrderStatus: "order_shipment_created",
			},
		},
		Service: "INTERNAL",
		OMS:     orderInfo.Seller,
	}

	reconResp, err := s.AddDataForReconciliation(ctx, reconReq)
	if err != nil {
		logger.WithError(errors.Wrap(err, "failed to add reconciliation data")).Warn("Reconciliation update failed")
		slack.SendSlackMessage(fmt.Sprintf("Error in add data for reconciliation: %v", err))
		return false, err
	}

	// Check if this is the first time order_shipment_created entry in recon
	return includes(reconResp.UpdatedMetrics, "order_shipment_created"), nil
}

// processAnalyticsEvents sends analytics events for OMS order push and shipment creation
func (s *Service) processAnalyticsEvents(ctx context.Context, orderID int64, orderInfo dao.KiranaBazarOrder, orderDetails *dao.KiranaBazarOrderDetails, req dto.ShipmentCreatedRequest) {
	logger := logrus.WithContext(ctx).WithField("order_id", orderID)

	// Send OMS order event if applicable
	if req.Data.OMS != "" {
		s.sendOmsOrderEvent(ctx, orderID, orderInfo, orderDetails, req)
	}

	// Send shipment created event
	s.sendShipmentCreatedEvent(ctx, orderID, orderInfo, orderDetails, req)

	// Process order financials
	go func() {
		err := s.ProcessOrderFinancials(ctx, fmt.Sprint(*orderInfo.ID), req.Data.KCShip)
		if err != nil {
			logger.WithError(errors.Wrap(err, "failed to process order financials")).Error("Financial processing failed")
			slack.SendSlackMessage(fmt.Sprintf("Error in processing order financials: %v", err))
		}
	}()
}

// sendOmsOrderEvent sends the "Order Sent to Vendor OMS" event
func (s *Service) sendOmsOrderEvent(ctx context.Context, orderID int64, orderInfo dao.KiranaBazarOrder, orderDetails *dao.KiranaBazarOrderDetails, req dto.ShipmentCreatedRequest) {
	if orderInfo.UserID == nil {
		logrus.WithContext(ctx).WithField("order_id", orderID).Warn("Missing user ID, skipping OMS order event")
		return
	}

	eventData := map[string]any{
		"distinct_id":     *orderInfo.UserID,
		"order_id":        orderID,
		"order_value":     int(orderDetails.GetOrderValue()),
		"oms_order_id":    req.Data.OMSOrderID,
		"oms_invoice_id":  req.Data.InvoiceID,
		"OMS":             req.Data.OMS,
		"seller":          orderInfo.Seller,
		"ordering_module": utils.MakeTitleCase(orderInfo.Seller),
		"updated_by":      req.Data.UpdatedBy,
		"courier_name":    couriers.GetActualCourierName(&req.Data.Courier),
		"awb":             req.Data.AWBNumber,
		"invoice":         req.Data.InvoiceURL,
		"shipping_labels": []string{req.Data.ShippingLabel},
	}

	event := s.Mixpanel.NewEvent("Order Sent to Vendor OMS", *orderInfo.UserID, eventData)
	err := s.Mixpanel.Track(ctx, []*mixpanel.Event{event})
	if err != nil {
		logrus.WithContext(ctx).WithError(err).WithField("event", "Order Sent to Vendor OMS").
			WithField("order_id", orderID).Error("Failed to send analytics event")
	}
}

// sendShipmentCreatedEvent sends the "Shipment Created" event
func (s *Service) sendShipmentCreatedEvent(ctx context.Context, orderID int64, orderInfo dao.KiranaBazarOrder, orderDetails *dao.KiranaBazarOrderDetails, req dto.ShipmentCreatedRequest) {
	if orderInfo.UserID == nil {
		logrus.WithContext(ctx).WithField("order_id", orderID).Warn("Missing user ID, skipping shipment created event")
		return
	}

	courierName := req.Data.Courier
	cr, err := couriers.GetActualCourier(&courierName)
	if err == nil {
		courierName = cr.CourierName
	}

	istLocation, _ := time.LoadLocation("Asia/Kolkata")
	eventProperties := map[string]any{
		"distinct_id":       *orderInfo.UserID,
		"order_id":          orderID,
		"seller":            orderInfo.Seller,
		"order_value":       int(orderDetails.GetOrderValue()),
		"invoice_number":    req.Data.InvoiceID,
		"invoice_amount":    req.Data.InvoiceAmount,
		"CGST":              req.Data.CSGT,
		"SGST":              req.Data.SGST,
		"IGST":              req.Data.IGST,
		"buyer_GST":         req.Data.BuyerGST,
		"no_of_skus":        req.Data.NoOfSkus,
		"AWB":               req.Data.AWBNumber,
		"courier_requested": req.Data.CourierRequested,
		"courier_assigned":  req.Data.CourierAssigned,
		"courier_name":      courierName,
		// convert invoice created at to IST here from epoch to time.Time dont use any external function
		"invoice_time":    time.UnixMilli(req.Data.InvoiceCreatedAt).In(istLocation),
		"oms_order_id":    req.Data.OMSOrderID,
		"OMS":             req.Data.OMS,
		"updated_by":      req.Data.UpdatedBy,
		"invoice":         req.Data.InvoiceURL,
		"discount":        req.Data.Discount,
		"shipping_labels": []string{req.Data.ShippingLabel},
		"item_qty":        req.Data.ItemQuantity,
	}

	// Handle package dimensions
	s.addPackageDimensionsToEvent(eventProperties, req)

	// Add custom timestamp if available
	if req.Data.ShipmentCreatedAt != 0 {
		eventProperties["time"] = req.Data.ShipmentCreatedAt
	}

	event := s.Mixpanel.NewEvent("Shipment Created", *orderInfo.UserID, eventProperties)
	err = s.Mixpanel.Track(ctx, []*mixpanel.Event{event})
	if err != nil {
		logrus.WithContext(ctx).WithError(err).WithField("event", "Shipment Created").
			WithField("order_id", orderID).Error("Failed to send analytics event")
	}
}

// addPackageDimensionsToEvent adds package dimension data to the event properties
func (s *Service) addPackageDimensionsToEvent(eventProperties map[string]any, req dto.ShipmentCreatedRequest) {
	if len(req.Data.PackageDetails.Dimensions) > 1 {
		eventProperties["package_dimensions"] = "mps"
		eventProperties["package_weight"] = "mps"
	} else if len(req.Data.PackageDetails.Dimensions) == 1 {
		dimension := req.Data.PackageDetails.Dimensions[0]
		eventProperties["package_dimensions"] = []float64{dimension.Length, dimension.Breadth, dimension.Height}
		eventProperties["package_weight"] = dimension.Weight
		eventProperties["volumetric_weight"] = req.Data.PackageDetails.GetVolumetricWeight()
	}
}

// sendOrderStatusUpdateEvent sends the "Order Status Updated" event
func (s *Service) sendOrderStatusUpdateEvent(ctx context.Context, orderID int64, orderInfo dao.KiranaBazarOrder, orderDetails *dao.KiranaBazarOrderDetails, req dto.ShipmentCreatedRequest) {
	if orderInfo.UserID == nil {
		logrus.WithContext(ctx).WithField("order_id", orderID).Warn("Missing user ID, skipping order status update event")
		return
	}

	// Map order status to standardized format
	orderStatuses := orderstatus.MapOrderStatus(req.Data.OrderStatus, req.Data.StatusType, orderstatus.OrderStatusResponse{})

	eventObject := map[string]interface{}{
		"distinct_id":             *orderInfo.UserID,
		"order_id":                *orderInfo.ID,
		"order_value":             int(orderDetails.GetOrderValue()),
		"status":                  req.Data.OrderStatus,
		"ordering_module":         utils.MakeTitleCase(orderInfo.Seller),
		"seller":                  orderInfo.Seller,
		"email":                   req.Data.UpdatedBy,
		"source":                  req.Data.Source,
		"shipment_status":         orderStatuses.ShipmentStatus,
		"processing_status":       orderStatuses.ProcessingStatus,
		"display_status":          orderStatuses.DisplayStatus,
		"previous_display_status": orderInfo.DisplayStatus,
		"event_trigger":           "order_status_updated",
	}

	if req.Data.ShipmentCreatedAt != 0 {
		eventObject["time"] = req.Data.ShipmentCreatedAt
	}

	// Send to Mixpanel
	event := s.Mixpanel.NewEvent("Order Status Updated", *orderInfo.UserID, eventObject)
	err := s.Mixpanel.Track(ctx, []*mixpanel.Event{event})
	if err != nil {
		logrus.WithContext(ctx).WithError(err).WithField("event", "Order Status Updated").
			WithField("order_id", orderID).Error("Failed to send Mixpanel event")
	}

	// Send to WebEngage
	webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{*orderInfo.UserID},
		EventName:   "Order Status Updated",
		EventObject: eventObject,
	})
}
