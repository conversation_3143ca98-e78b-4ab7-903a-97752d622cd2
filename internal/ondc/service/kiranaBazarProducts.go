package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/cache"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/redis"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/brands"
	ordervalue "kc/internal/ondc/service/orderBill/orderValue"
	"kc/internal/ondc/service/products"
	productsService "kc/internal/ondc/service/products"
	userdetails "kc/internal/ondc/service/userDetails"
	"kc/internal/ondc/utils"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/Masterminds/semver"
	"github.com/mixpanel/mixpanel-go"
)

var REVIEW_TEST_WIDGET = map[string]interface{}{
	"id":                589,
	"MixPanelEventName": "Update Michis Playstore Banner",
	"expiry_time":       int64(1738743360000),
	"type":              48,
	"updatedBy":         "<EMAIL>",
	"visibility":        1,
	"visible_from":      int64(1735892181530),
	"widget_info": map[string]interface{}{
		"widget_name": "Products Rating Bottomsheet",
	},
	"header": map[string]interface{}{
		"primary_text":   "सभी प्रॉडक्ट्स का फीडबैक दें",
		"secondary_text": "अपना फीडबैक दीजिये",
	},
	"rating_meta": map[string]interface{}{
		"options": []interface{}{
			map[string]interface{}{
				"option_id": 1,
				"icon":      "🤩",
				"label":     "बिक रहा हैं",
				"value":     1,
			},
			map[string]interface{}{
				"option_id": 2,
				"icon":      "🙂",
				"label":     "धीरें बिक रहा हैं ",
				"value":     3,
			},
			map[string]interface{}{
				"option_id": 3,
				"icon":      "☹️",
				"label":     "नहीं बिक रहा हैं",
				"value":     5,
			},
		},
		"placeholder":  "और जानकारी दें...",
		"success_text": "फ़ीडबैक दर्ज किया गया ✅",
	},
	"cta": map[string]interface{}{
		"text": "जमा करें",
	},
	"items": []interface{}{
		map[string]interface{}{
			"seller":       "hugs",
			"product_id":   1,
			"image_url":    "https://d2rstorage2.blob.core.windows.net/widget/June/16/01c5d6eb-5771-4107-bdaa-42d9748eeae9/1750067940894.webp",
			"product_name": "हल्दी पाउडर",
			"description":  "Zoff का हल्दी पाउडर (15 ग्राम) आपने 1 जून को ख़रीदा था।",
		},
		map[string]interface{}{
			"seller":       "hugs",
			"product_id":   2,
			"image_url":    "https://d2rstorage2.blob.core.windows.net/widget/June/16/01c5d6eb-5771-4107-bdaa-42d9748eeae9/1750067940894.webp",
			"product_name": "हल्दी पाउडर",
			"description":  "Zoff का हल्दी पाउडर (15 ग्राम) आपने 1 जून को ख़रीदा था।",
		},
		map[string]interface{}{
			"seller":       "hugs",
			"product_id":   3,
			"image_url":    "https://d2rstorage2.blob.core.windows.net/widget/June/16/01c5d6eb-5771-4107-bdaa-42d9748eeae9/1750067940894.webp",
			"product_name": "हल्दी पाउडर",
			"description":  "Zoff का हल्दी पाउडर (15 ग्राम) आपने 1 जून को ख़रीदा था।",
		},
	},
}

// select * from kiranabazar_products join kiranabazar_categories on kiranabazar_products.category_id = kiranabazar_categories.id where kiranabazar_categories.source = 'third_party_zoff_foods'category_id = %!d(string=Powder)

// Add these cache keys
var (
	productCacheKey = func(source, categoryID, excludedProducts string, limit, offset int) string {
		return fmt.Sprintf("products:%s:%s:%s:%d:%d", source, categoryID, excludedProducts, limit, offset)
	}
	serviceabilityCacheKey = func(postalCode, seller string) string {
		return fmt.Sprintf("serviceability:%s:%s", postalCode, seller)
	}
)

func filterProductsByCohort(responseProducts []shared.SellerItems, userCohorts []string) []shared.SellerItems {
	// Convert userCohorts to a set for fast lookup
	userCohortSet := make(map[string]bool)
	for _, cohort := range userCohorts {
		userCohortSet[cohort] = true
	}

	filtered := []shared.SellerItems{}

	for _, product := range responseProducts {
		allowedCohorts, exists := PRODUCTS_COHORT_MAP[product.ID]

		if !exists {
			// Product not restricted by cohort, keep it
			filtered = append(filtered, product)
			continue
		}

		// Check if user belongs to any allowed cohort
		allowed := false
		for _, cohort := range allowedCohorts {
			if userCohortSet[cohort] {
				allowed = true
				break
			}
		}

		if allowed {
			filtered = append(filtered, product)
		}
	}
	return filtered
}

func getProductsResponse(userId string, items []interface{}, maxPerGroup int, globalWidgets []interface{}, subType string) interface{} {
	var result []interface{}
	// if userId == "hUWFEnLsOyOO6eFlfZv6zQFSkqp2" {
	// 	result = append(result, REVIEW_TEST_WIDGET)
	// }

	itr := 0
	for i := 0; i < len(items); i += maxPerGroup {

		// if i == 2 && len(globalWidgets) > 0 {
		// 	result = append(result, globalWidgets...)
		// }

		end := i + maxPerGroup
		if end > len(items) {
			end = len(items)
		}
		chunk := items[i:end]

		widthPercentage := 48
		optimisedWidthPercentage := 35
		if len(chunk) > 2 {
			widthPercentage = 25
		}

		if len(chunk) > 4 {
			widthPercentage = 17
		}
		group := dto.WidgetType43{
			Type:                     43,
			ID:                       12345,
			UpdatedBy:                "backend",
			Visibility:               1,
			VisibleFrom:              time.Now().Unix(),
			MixPanelEventName:        "ProductList Widget",
			ExpiryTime:               time.Now().Add(24 * time.Hour).Unix(),
			WidthPercentage:          widthPercentage,
			OptimisedWidthPercentage: optimisedWidthPercentage,
			Data:                     chunk,
			SubType:                  subType,
		}
		group.WidgetInfo.WidgetName = "ProductList Products Screen Widget"
		result = append(result, group)
		if itr == 0 && len(globalWidgets) > 0 {
			result = append(result, globalWidgets...)
		}
		itr++
	}

	return result
}

func (s *Service) CheckProductsOutOfStock(productIds []string) ([]string, error) {
	products := []dao.KiranaBazarProduct{}
	query := fmt.Sprintf(`select * from kiranabazar_products where id in (%s) and is_oos != true and is_active = true`, strings.Join(productIds, ","))
	_, err := s.repository.CustomQuery(&products, query)
	if err != nil {
		return nil, err
	}
	inStockProductIds := make([]string, 0)
	for _, product := range products {
		inStockProductIds = append(inStockProductIds, strconv.Itoa(int(product.ID)))
	}
	return inStockProductIds, nil
}

func GetTopBadge(seller string, text *string, styles *dao.TopBadgeStyles) *shared.Badge {
	if text == nil {
		return nil
	}
	color := "#6f3b00"
	bgColor := []string{"#ffdbb2", "#ffdbb2"}

	if styles != nil {
		if styles.Color != "" {
			color = styles.Color
		}
		if len(styles.BgColor) > 0 {
			bgColor = styles.BgColor
		}
	}

	return &shared.Badge{
		Text:    *text,
		Color:   color,
		BgColor: bgColor,
	}
}

// GetMarginPercentageString extracts the margin percentage from the input string.
// If not found, it formats the fallback float value to 2 decimal places with a '%' sign.
func GetMarginPercentageString(input string, fallback float64) string {
	parts := strings.Split(input, "मार्जिन:")
	if len(parts) > 1 {
		result := strings.TrimSpace(parts[1])
		if result != "" {
			return result
		}
	}
	// Fallback to formatting float value
	return fmt.Sprintf("%.1f%%", fallback)
}

func (s *Service) GetProductsFromDB(request *dto.AppSearchRequest, excludedProducts []string, screenTag string) ([]*productsService.Product, error) {
	query, cacheKey := s.BuildProductsQuery(request, excludedProducts, screenTag)

	cacheResult, err := cache.GetInstance().GetCachedData(
		context.Background(),
		cacheKey,
		func() (interface{}, error) {
			var productsData []dao.KiranaBazarProduct
			_, err := s.repository.CustomQuery(&productsData, query)
			if err != nil {
				return nil, err
			}
			return productsData, nil
		},
		10*time.Minute,
		0,
	)
	if err != nil {
		return nil, err
	}

	productsCode, ok := cacheResult.Data.([]dao.KiranaBazarProduct)
	if !ok {
		return nil, fmt.Errorf("invalid cache data type for products: %T", cacheResult.Data)
	}

	//fmt.Printf("productsData %+v\n", productsCode[0])

	productsData := []*productsService.Product{}
	for _, product := range productsCode {
		data := productsService.GetProductByCode(product.Code)
		if data == nil {
			errMsg := fmt.Sprintf("product not found for code: %s", product.Code)
			fmt.Println(errMsg)
			continue
		}
		productsData = append(productsData, data)
	}
	return productsData, nil
}

func (s *Service) GetProductsV2(ctx context.Context, request *dto.AppSearchRequest) (response *dto.AppSearchResponses, err error) {
	floatingVideoWidget := []map[string]interface{}{}
	appVersion := request.Meta.AppVersion
	userAppVersion, _ := semver.NewVersion(appVersion)
	productsScreenChangeAppVersion, _ := semver.NewVersion("6.5.2")
	screenTag := "screen:products"

	if request.Data.ScreenTag != nil {
		screenTag = *request.Data.ScreenTag
	}

	widgetViewCountChannel := make(chan map[string]int)
	s.GetAllWidgetCountsAsync(ctx, request.UserID, widgetViewCountChannel)

	response = new(dto.AppSearchResponses)
	response.Data.Sellers = []*dto.SearchResponseSellers{}
	response.Data.FloatingData = floatingVideoWidget
	response.Data.Items = make([]interface{}, 0)

	if request.Data.Offset != nil && *request.Data.Offset == 0 && request.Data.CategoryID == "" {
		go s.setBottomSheetData(request)
	}

	userDetailsChannel := userdetails.AsyncFetchUserDetails(request.UserID, []string{userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS, userdetails.USER_DETAILS_TYPES.USER_GEOGRAPHY}, 1*time.Minute)
	userDetails := <-userDetailsChannel

	// Handle UNDEFINED seller case -- In an issue where seller was set as UNDEFINED in app redux store
	if request.Data.Seller == utils.UNDEFINED {
		userCohortNames := []string{}
		var userGeography *userdetails.UserGeoData
		if userDetails.Data != nil && userDetails.Data.UserDynamicDetails != nil {
			userCohortNames = userDetails.Data.UserDynamicDetails.UserCohortNames
		}
		if userDetails.Data != nil {
			userGeography = userDetails.Data.UserGeography
		}
		userCohortNames = append(userCohortNames, userdetails.GetUserDerivedCohorts(request.UserID, &[]string{request.Data.Seller}, userGeography)...)
		return s.HandleUndefinedSeller(request, userCohortNames)
	}

	// Check serviceability for specific sellers
	if request.Data.Seller != "" {
		if err := s.CheckServiceability(ctx, request, screenTag); err != nil {
			return nil, err
		}
	}

	defaultLimit := 6
	defaultOffset := 0
	if request.Data.Limit == nil || request.Data.Offset == nil {
		request.Data.Offset = &defaultOffset
	}

	if !request.Data.Internal {
		request.Data.Limit = &defaultLimit
	}

	// IMPORTANT: Adjust limit to account for top products
	if request.Data.TopProductIds != nil && len(request.Data.TopProductIds) > 0 {
		request.Data.Limit = utils.IntPtr((*request.Data.Limit) - len(request.Data.TopProductIds))
	}

	// Handle Michis seller case - if not on payment app and seller is Michis, then ask user to update to payment app
	if request.Data.Seller == utils.MICHIS {
		if response, err = s.HandleMichisSeller(request, floatingVideoWidget); err != nil {
			return nil, err
		} else if response != nil {
			return response, nil
		}
	}

	// Handle KIRANA_CLUB seller case
	if request.Data.Seller == utils.KIRANA_CLUB {
		if response, err = s.HandleKiranaClubSeller(request, floatingVideoWidget); err != nil {
			return nil, err
		} else if response != nil {
			return response, nil
		}
	}

	if request.Data.Seller == "" && request.Data.CategoryID == "" && len(request.Data.TopProductIds) == 0 {
		return response, errors.New("seller or category not defined")
	}

	var userCohortNames []string
	var userGeography *userdetails.UserGeoData
	if userDetails.Data != nil && userDetails.Data.UserDynamicDetails != nil {
		userCohortNames = userDetails.Data.UserDynamicDetails.UserCohortNames
	}
	if userDetails.Data != nil {
		if userDetails.Data.UserGeography != nil {
			userGeography = userDetails.Data.UserGeography
		}
	}
	userCohortNames = append(userCohortNames, userdetails.GetUserDerivedCohorts(request.UserID, &[]string{request.Data.Seller}, userGeography)...)
	userPricingContext := productsService.PricingContext{
		UserID:     &request.UserID,
		UserCohort: &userCohortNames,
		Quantity:   0,
	}

	// CRITICAL FIX: Properly handle excluded products for both top products and regular products
	excludedProducts := make([]string, 0)
	responseProducts := make([]shared.SellerItems, 0)
	topProducts := request.Data.TopProductIds

	if topProducts != nil && len(topProducts) > 0 {
		// Add top products to excluded list to prevent duplicates in regular query
		excludedProducts = append(excludedProducts, topProducts...)

		topProductsBaseData := productsService.GetProductsByIDs(utils.StringSliceToUintSlice(topProducts))
		topProductsSizeVariantMap := s.ProcessAndMakeVariantMap(topProductsBaseData, request.Data.CategoryID)

		// if offset not provided or offset is 0, prepending topProductsMap to the main query map
		// adding check for only lots category to handle pagination to avoid duplicate products in search
		if request.Data.Offset == nil || (request.Data.Offset != nil && *request.Data.Offset == 0) {
			shouldFetchInActiveProduct := false
			if request.Data.Conditions != nil && request.Data.Conditions.FetchInactiveProducts {
				shouldFetchInActiveProduct = true
			}

			topProductsData, err := GetProductsFromMap(topProductsSizeVariantMap, request.Data.Exclusive, appVersion,
				request.Data.CategoryID, shouldFetchInActiveProduct, &userPricingContext)
			if err != nil {
				return nil, err
			}

			topProductsOrderSizeVariantCode := make([]int64, 0)
			for _, topProductId := range topProducts {
				for _, topProduct := range topProductsData {
					if topProductId == topProduct.ID {
						topProductsOrderSizeVariantCode = append(topProductsOrderSizeVariantCode, *topProduct.SizeVariantCode)
					} else {
						for _, sizeVariant := range topProduct.SizeVariants {
							if topProductId == sizeVariant.ID {
								topProductsOrderSizeVariantCode = append(topProductsOrderSizeVariantCode, *sizeVariant.SizeVariantCode)
							}
						}
					}
				}
			}

			sort.Slice(topProductsData, func(i, j int) bool {
				return TopProductsIndexOf(topProductsOrderSizeVariantCode, topProductsData[i]) < TopProductsIndexOf(topProductsOrderSizeVariantCode, topProductsData[j])
			})

			// add top products to the response first
			responseProducts = append(responseProducts, topProductsData...)
		}
	}

	if request.Data.Exclusive != nil && *request.Data.Exclusive {
		// If the request is for exclusive products, return only the top products
		response = &dto.AppSearchResponses{
			Meta: dto.Meta{
				Limit:  *request.Data.Limit,
				Offset: *request.Data.Offset + *request.Data.Limit,
			},
			Data: dto.AppSearchData{
				Items:        responseProducts,
				Sellers:      make([]*dto.SearchResponseSellers, 0),
				FloatingData: floatingVideoWidget,
			},
		}
		return response, nil
	}

	// Build query using ProductService - this will exclude the top products
	productsData, err := s.GetProductsFromDB(request, excludedProducts, screenTag)
	if err != nil {
		return nil, err
	}

	productsSizeVariantMap := s.ProcessAndMakeVariantMap(productsData, request.Data.CategoryID)

	if len(productsData) > 0 {
		response = &dto.AppSearchResponses{
			Meta: dto.Meta{
				Limit:  *request.Data.Limit,
				Offset: *request.Data.Offset + *request.Data.Limit,
			},
			Data: dto.AppSearchData{
				Items:        make([]shared.SellerItems, 0),
				Sellers:      make([]*dto.SearchResponseSellers, 0),
				FloatingData: floatingVideoWidget,
			},
		}
	}

	// Build the final response array
	sizeVariantProducts, err := GetProductsFromMap(productsSizeVariantMap, request.Data.Exclusive, appVersion,
		request.Data.CategoryID, false, &userPricingContext)
	if err != nil {
		return nil, err
	}
	// change logic here for oos products and sorting
	sizeVariantProducts = s.ArrangeAndSortProducts(sizeVariantProducts, request.Data.CategoryID)

	responseProducts = append(responseProducts, sizeVariantProducts...)

	var bsNavObject *shared.Nav
	zerothOffset := 0
	var globalWidgets []interface{}
	items := []interface{}{}
	seenProducts := make(map[string]bool)

	for _, item := range responseProducts {
		// Skip duplicates based on ID
		if seenProducts[item.ID] {
			continue
		}
		seenProducts[item.ID] = true
		showRecommnded := true
		item.ShowRecommendedProducts = &showRecommnded
		items = append(items, item)
	}

	if request.Data.Seller == utils.KIRANA_CLUB && len(items) > 0 {
		items = append(items, KC_COMBO_OFFER_PRODUCTS_BANNER)
	}

	widgetViewsCount := <-widgetViewCountChannel
	if request.Data.Seller == utils.APSARA_TEA && request.Data.Offset != nil && *request.Data.Offset == zerothOffset && getWidgetViewCountFromId("2975", widgetViewsCount) < 2 {
		floatingVideoWidget = append(floatingVideoWidget, APSARA_FLOATING_VIDEO_WIDGET...)
		s.IncrementWidgetViewCount(ctx, request.UserID, "2975", nil)
	}

	if request.Data.Seller != utils.KIRANA_CLUB && (userAppVersion != nil) && (userAppVersion.GreaterThan(productsScreenChangeAppVersion)) {
		v3ProductsResponse := getProductsResponse(request.UserID, items, 2, globalWidgets, "get_products")
		if request.Data.Offset != nil && *request.Data.Offset == zerothOffset && request.Data.Seller == utils.SOMNATH {
			if slice, ok := v3ProductsResponse.([]interface{}); ok {
				if len(slice) > 0 {
					v3ProductsResponse = append(slice[:1], append([]interface{}{SOMNATH_EXTRA_MARGIN_BANNER}, slice[1:]...)...)
				} else {
					v3ProductsResponse = append(slice, SOMNATH_EXTRA_MARGIN_BANNER)
				}
			}
		}
		return &dto.AppSearchResponses{
			Meta: dto.Meta{
				Limit:  *request.Data.Limit,
				Offset: *request.Data.Offset + *request.Data.Limit,
			},
			Data: dto.AppSearchData{
				Items:        v3ProductsResponse,
				Sellers:      make([]*dto.SearchResponseSellers, 0),
				FloatingData: floatingVideoWidget,
			},
			NavObj: bsNavObject,
		}, err
	}

	// if len(globalWidgets) > 0 && len(items) > 2 {
	// 	items = append(append(items[:2], globalWidgets...), items[5:]...)
	// }

	newItems := make([]interface{}, 0)
	for i, item := range items {
		if i == 2 {
			newItems = append(newItems, globalWidgets...)
		}
		newItems = append(newItems, item)
	}

	if request.Data.Offset != nil && *request.Data.Offset == zerothOffset && request.Data.Seller == utils.SOMNATH {
		if len(newItems) > 0 {
			newItems = append(newItems[:1], append([]interface{}{SOMNATH_EXTRA_MARGIN_BANNER_OLD}, newItems[1:]...)...)
		} else {
			newItems = append(newItems, SOMNATH_EXTRA_MARGIN_BANNER)
		}
	}

	response = &dto.AppSearchResponses{
		Meta: dto.Meta{
			Limit:  *request.Data.Limit,
			Offset: *request.Data.Offset + *request.Data.Limit,
		},
		Data: dto.AppSearchData{
			Items:        newItems,
			Sellers:      make([]*dto.SearchResponseSellers, 0),
			FloatingData: floatingVideoWidget,
		},
		NavObj: bsNavObject,
	}

	return response, err
}

var usersSeen = map[string]bool{}

func (s *Service) setBottomSheetData(request *dto.AppSearchRequest) {
	// requested the data that means user is on the app
	// now check for the order placed ever data
	countChan := make(chan int, 1)
	GetUserOrderCount(request.UserID, s.repository, countChan)
	numberOfOrders := <-countChan
	if numberOfOrders == 0 {
		redis.Client.Set(fmt.Sprintf("survey_bottom_sheet:%s", request.UserID), request.Data.Seller, 36000)
	}

}

func (s *Service) GetProducts(ctx context.Context, request *dto.AppSearchRequest) (response *dto.AppSearchResponses, err error) {
	// below condition is for the 1st request hit
	if request.Data.Offset != nil && *request.Data.Offset == 0 && request.Data.CategoryID == "" {
		go s.setBottomSheetData(request)
	}

	userDetailsChannel := userdetails.AsyncFetchUserDetails(request.UserID, []string{userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS, userdetails.USER_DETAILS_TYPES.USER_GEOGRAPHY}, 1*time.Minute)

	type kiranaBazarProductsAndCategories struct {
		dao.KiranaBazarProduct
		EntityID int64 `json:"entity_id"`
	}
	if request.Data.PostalCode != "" {
		serviceablityResponse := &dto.AppserviceAbilityAPIResponse{}
		serviceablityResponse, err = s.CheckServiceAbility(ctx, &dto.AppServiceAblityAPIRequest{
			DeliveryPostCode: request.Data.PostalCode,
			COD:              "y",
			UserID:           request.UserID,
			Data: dto.AppServiceAbilityAPIRequestData{
				Seller: request.Data.Seller,
			},
		})
		if err != nil {
			return nil, err
		}
		eventObject := map[string]interface{}{
			"distinct_id":     request.UserID,
			"ordering_module": utils.MakeTitleCase(request.Data.Seller),
			"seller":          request.Data.Seller,
			"source":          "GET_PRODUCTS",
			"latitude":        request.Data.Latitude,
			"longitude":       request.Data.Longitude,
			"platform":        request.Meta.Platform,
		}
		var intPincode int
		intPincode, err = strconv.Atoi(request.Data.PostalCode)
		eventObject["pincode"] = intPincode

		if !serviceablityResponse.Data.Servicable {
			err = errors.New("not able to serve to the provided location")
			s.Mixpanel.Track(ctx, []*mixpanel.Event{
				s.Mixpanel.NewEvent("Non Servicable Location", request.UserID, eventObject),
			})
			webengage.SendWebengageEvents(&webengage.WebengageEvents{
				UserIds:     []string{request.UserID},
				EventName:   "Non Servicable Location",
				EventObject: eventObject,
			})
			return
		}

	} else if request.Data.Latitude != 0 {
		var locationData *dto.GeoLocationResponse
		eventObject := map[string]interface{}{
			"distinct_id":     request.UserID,
			"ordering_module": utils.MakeTitleCase(request.Data.Seller),
			"seller":          request.Data.Seller,
			"source":          "GET_PRODUCTS",
			"latitude":        request.Data.Latitude,
			"longitude":       request.Data.Longitude,
			"platform":        request.Meta.Platform,
		}
		locationData, err = utils.GetLocationDetailsFromLatLong(request.Data.Latitude, request.Data.Longitude)
		if err != nil {
			err = errors.New("failed to fetch location data from latitude and longitude")
			return
		}
		pincode := locationData.Data.Pincode
		pincode = "110001"
		var intPincode int
		intPincode, err = strconv.Atoi(pincode)
		eventObject["pincode"] = intPincode
		if err != nil {
			err = errors.New("not able to get pincode from the location data")
			s.Mixpanel.Track(ctx, []*mixpanel.Event{
				s.Mixpanel.NewEvent("Non Servicable Location", request.UserID, eventObject),
			})
			webengage.SendWebengageEvents(&webengage.WebengageEvents{
				UserIds:     []string{request.UserID},
				EventName:   "Non Servicable Location",
				EventObject: eventObject,
			})
			return
		}
		serviceablityResponse := &dto.AppserviceAbilityAPIResponse{}
		serviceablityResponse, err = s.CheckServiceAbility(ctx, &dto.AppServiceAblityAPIRequest{
			DeliveryPostCode: pincode,
			COD:              "y",
			UserID:           request.UserID,
			Data: dto.AppServiceAbilityAPIRequestData{
				Seller: request.Data.Seller,
			},
		})
		if err != nil {
			return nil, err
		}
		if !serviceablityResponse.Data.Servicable {
			err = errors.New("not able to serve to the provided location")
			s.Mixpanel.Track(ctx, []*mixpanel.Event{
				s.Mixpanel.NewEvent("Non Servicable Location", request.UserID, eventObject),
			})
			webengage.SendWebengageEvents(&webengage.WebengageEvents{
				UserIds:     []string{request.UserID},
				EventName:   "Non Servicable Location",
				EventObject: eventObject,
			})
			return
		}
	}

	products := []kiranaBazarProductsAndCategories{}
	if request.Data.Source == nil {
		return response, errors.New("source undefined")
	}
	if request.Data.Seller == utils.KIRANA_CLUB {
		return &dto.AppSearchResponses{
			Meta: dto.Meta{
				Limit:  *request.Data.Limit,
				Offset: *request.Data.Offset + *request.Data.Limit,
			},
			Data: dto.AppSearchData{
				Items:   make([]shared.SellerItems, 0),
				Sellers: make([]*dto.SearchResponseSellers, 0),
			},
		}, nil
	}

	excludedProducts := getAllOfferSkus()
	defaultLimit := 10
	defaultOffset := 0

	queryBuilder := sqlRepo.NewQueryBuilder(&sqlRepo.QueryBuilderOptions{
		TableName:      "kiranabazar_products kp",
		DefaultColumns: []string{"kp.*"},
		DefaultLimit:   &defaultLimit,
		DefaultOffset:  &defaultOffset,
	})

	seller, exists := brands.GetSellerBySource(*request.Data.Source)
	if !exists {
		return nil, fmt.Errorf("invalid source: %s", *request.Data.Source)
	}
	joins := []string{}
	columns := []string{}
	whereAnd := []string{
		"kp.seller = '?'",
		"kp.is_oos != ? ",
		"kp.is_active = ? ",
		"kp.id NOT IN (?)",
	}
	args := []interface{}{
		seller,
		true,
		true,
		strings.Join(excludedProducts, ","),
	}

	if request.Data.CategoryID != "" {
		joins = append(joins, `
		JOIN 
		kiranabazar_entities_mapping kcm
	ON kcm.`+"`type`"+` = 'product' and kcm.tag = 'screen:products' and kcm.target_id = kp.id
		`)
		columns = append(columns, "kcm.entity_id as entity_id")
		whereAnd = append(whereAnd, "kcm.entity_id = ?", "kcm.is_active = ?")
		args = append(args, request.Data.CategoryID, true)
	}

	queryBuilder.
		WithJoins(joins).
		WithColumns(columns).
		WhereAnd(whereAnd, args).
		WithOrderBy([]string{"kp.`rank`", "kp.popularity_value desc"}).
		WithPagination(request.Data.Limit, request.Data.Offset)

	query := queryBuilder.ToSQL()

	s.repository.CustomQuery(&products, query)

	userDetails := <-userDetailsChannel
	userCohortNames := []string{}
	var userGeography *userdetails.UserGeoData
	if userDetails.Data != nil && userDetails.Data.UserDynamicDetails != nil {
		userCohortNames = userDetails.Data.UserDynamicDetails.UserCohortNames
	}
	if userDetails.Data != nil {
		if userDetails.Data.UserGeography != nil {
			userGeography = userDetails.Data.UserGeography
		}
	}
	userCohortNames = append(userCohortNames, userdetails.GetUserDerivedCohorts(request.UserID, &[]string{request.Data.Seller}, userGeography)...)
	if len(products) > 0 {
		response = &dto.AppSearchResponses{
			Meta: dto.Meta{
				Limit:  *request.Data.Limit,
				Offset: *request.Data.Offset + *request.Data.Limit,
			},
			Data: dto.AppSearchData{
				Items:   make([]shared.SellerItems, 0),
				Sellers: make([]*dto.SearchResponseSellers, 0),
			},
		}
	}
	if len(products) == 0 {
		return response, nil
	}
	items := make([]shared.SellerItems, 0)
	for _, product := range products {
		productImageUrls := []string{}
		productMediaUrls := []dao.KiranaBazarProductMediaUrl{}
		json.Unmarshal([]byte(product.MediaUrls), &productMediaUrls)
		if len(productMediaUrls) > 0 {
			for _, mediaUrl := range productMediaUrls {
				if mediaUrl.Url != "" && mediaUrl.VideoUrl == nil {
					// productImageUrls = append(productImageUrls, mediaUrl.Url)
				}
			}
		}

		byt, _ := json.Marshal(product.ImageUrls)
		json.Unmarshal(byt, &productImageUrls)
		if items == nil {
			items = make([]shared.SellerItems, 0)
		}
		categoryId := fmt.Sprintf("%d", product.CategoryID)
		if product.EntityID != 0 {
			categoryId = fmt.Sprintf("%d", product.EntityID)
		}

		productSource, exists := brands.GetSourceBySeller(product.Seller)
		if !exists {
			slack.SendSlackMessage(fmt.Sprintf("source not found for seller: %s get products ", product.Seller))
			// Skip this product if source doesn't exist
			continue
		}
		items = append(items, shared.SellerItems{
			SellerItemsData: shared.SellerItemsData{
				Type:        33,
				ProviderID:  productSource,
				Seller:      product.Seller,
				Name:        product.Name,
				ImageUrls:   productImageUrls,
				CategoryIds: []string{categoryId},
				ID:          fmt.Sprintf("%d", product.ID),
				Price: shared.Price{
					Currency:       "rs",
					Value:          "10",
					MinimumValue:   "10",
					EstimatedValue: "10",
					ComputedValue:  "10",
					ListedValue:    "10",
					OfferedValue:   "10",
					MaximumValue:   "10",
				},
				Quantity:     0,
				Meta:         product.Meta,
				Manufacturer: product.Manufacturer,
			},
		})
	}

	responseProducts := filterProductsByCohort(items, userCohortNames)
	if len(responseProducts) > 0 {
		response.Data.Items = responseProducts
	}
	return response, err
}

func (s *Service) GetProductDetails(ctx context.Context, request *dto.GetProductsRequest) (response *dto.AppGetProductDetailsResponse, err error) {
	productID := request.Data.ProductID
	categoryID := request.Data.CategoryID
	appVersion := request.Meta.AppVersion
	userAppVersion, _ := semver.NewVersion(appVersion)
	productsScreenChangeAppVersion, _ := semver.NewVersion("6.5.2")
	if (userAppVersion != nil) && (userAppVersion.GreaterThan(productsScreenChangeAppVersion)) {
		// variantTopBadgeText = utils.StrPtr("बेस्ट सेलर")
	}

	if productID == nil || categoryID == nil || request.Data.Source == nil {
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, nil, "product_id, category_id and source is required", http.StatusBadRequest)
		return
	}

	productRawData, exist := productsService.GetProductByID(*productID)
	if !exist {
		fmt.Println(fmt.Errorf("product not found for id: %s", *productID))
		return nil, fmt.Errorf("product not found for id: %s", *productID)
	}

	userDetailsChannel := userdetails.AsyncFetchUserDetails(request.UserID, []string{userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS, userdetails.USER_DETAILS_TYPES.USER_GEOGRAPHY}, 1*time.Minute)
	userDetails := <-userDetailsChannel

	userCohortNames := []string{}
	var userGeography *userdetails.UserGeoData
	if userDetails.Data != nil {
		if userDetails.Data.UserDynamicDetails != nil {
			userCohortNames = userDetails.Data.UserDynamicDetails.UserCohortNames
		}
		if userDetails.Data.UserGeography != nil {
			userGeography = userDetails.Data.UserGeography
		}
		userCohortNames = append(userCohortNames, userdetails.GetUserDerivedCohorts(request.UserID, &[]string{productRawData.Seller}, userGeography)...)
	}

	productSellerItemData := productRawData.ToSellerItems(productsService.ToSellerItemsCondition{
		IncludeVariant: true,
	}, appVersion, &productsService.PricingContext{
		UserID:     &request.UserID,
		Quantity:   0,
		UserCohort: &userCohortNames,
	})

	productSellerItemData.Type = 0
	productSellerItemData.CategoryIds = []string{*categoryID}

	for i, _ := range productSellerItemData.SizeVariants {
		productSellerItemData.SizeVariants[i].Type = 0
		productSellerItemData.SizeVariants[i].ParentItemID = productSellerItemData.ID
		productSellerItemData.SizeVariants[i].CategoryIds = []string{*categoryID}
	}

	currentTime := time.Now()
	newTime := currentTime.AddDate(1, 1, 0) // defualt 13 months
	epochMillis := currentTime.UnixMilli()

	if productRawData.MetaProperties.ExpiresIn != nil {
		newEpochMillis := epochMillis + *productRawData.MetaProperties.ExpiresIn
		newTime = time.UnixMilli(newEpochMillis)
	}
	formattedTime := newTime.Format("Jan 2006")
	productRawData.MetaProperties.ExpiryDateString = &formattedTime
	productRawData.MetaProperties.MarkupMarginKey = "मार्जिन:"
	productRawData.MetaProperties.MarkupMarginValue = GetMarginPercentageString(productRawData.MetaProperties.MarkupMarginString, productRawData.MetaProperties.MarkupMargin)
	productRawData.MetaProperties.MRPStringValue = utils.FormatINR(productRawData.MetaProperties.MRPNumber)
	marshalledMeta, err := json.Marshal(productRawData.MetaProperties)

	productSellerItemData.SellerItemsData.Meta = marshalledMeta

	response = &dto.AppGetProductDetailsResponse{
		Data: dto.ProductDetails{
			Product: productSellerItemData,
		},
	}

	// Handle reward products
	if *request.Data.Source == "kiranaclub_rewards" {
		return handleRewardProduct(response)
	}

	return response, nil
}

// getSellerFromSource gets the seller from the source
func (s *Service) getSellerFromSource(source *string) (string, error) {
	seller, exists := brands.GetSellerBySource(*source)
	if !exists {
		return "", fmt.Errorf("invalid source: %s", *source)
	}
	return seller, nil
}

func handleRewardProduct(response *dto.AppGetProductDetailsResponse) (*dto.AppGetProductDetailsResponse, error) {
	// Set default badge for out-of-stock status
	response.Data.Product.BottomBadge = &shared.Badge{
		Text:    "",
		Color:   "#FFFFFF",
		BgColor: []string{"#FFFFFF", "#FFFFFF"},
	}

	response.Data.Product.IsOos = &shared.Badge{
		Text:    "",
		Color:   "#FFFFFF",
		BgColor: []string{"#FFFFFF", "#FFFFFF"},
	}

	// Clear ratings
	response.Data.Product.RatingsCount = nil
	response.Data.Product.Rating = nil

	// Parse original meta
	var originalMeta shared.KiranaBazarProductMeta
	if err := json.Unmarshal(response.Data.Product.Meta, &originalMeta); err != nil {
		return nil, fmt.Errorf("failed to unmarshal product meta: %w", err)
	}

	// Create new meta with selected fields
	newMeta := shared.KiranaBazarProductMeta{
		Description: originalMeta.Description,
		HindiName:   originalMeta.HindiName,
		Quantity:    "नई", // "New" in Hindi
	}

	// Marshal new meta
	metaBytes, err := json.Marshal(newMeta)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal new meta: %w", err)
	}
	response.Data.Product.Meta = metaBytes

	return response, nil
}

// HandleUndefinedSeller handles the case when seller is UNDEFINED (app redux store issue)
func (s *Service) HandleUndefinedSeller(request *dto.AppSearchRequest, userCohortNames []string) (*dto.AppSearchResponses, error) {
	offset := 0
	if request.Data.Offset != nil {
		offset = *request.Data.Offset
	}

	if request.Data.Limit == nil {
		defaultLimit := 10
		request.Data.Limit = &defaultLimit
	}

	var widgetsArray []interface{}
	if offset == 0 {
		var widgetData string
		if includes(userCohortNames, RSB_LOTS_SKU_COHORT) {
			widgetData = UNDEFINED_RSB_PRODUCTS_WIDGETS
		} else {
			widgetData = UNDEFINED_PRODUCTS_WIDGETS
		}

		if err := json.Unmarshal([]byte(widgetData), &widgetsArray); err != nil {
			return nil, err
		}
	}

	return &dto.AppSearchResponses{
		Meta: dto.Meta{Limit: *request.Data.Limit, Offset: offset},
		Data: dto.AppSearchData{Items: widgetsArray, Sellers: []*dto.SearchResponseSellers{}},
	}, nil
}

// CheckServiceability validates if the seller can serve the user's location
func (s *Service) CheckServiceability(ctx context.Context, request *dto.AppSearchRequest, screenTag string) error {
	if request.Data.PostalCode == "" && request.Data.Latitude == 0 {
		return nil // No location data provided
	}

	var pincode string
	if request.Data.PostalCode != "" {
		pincode = request.Data.PostalCode
	} else {
		locationData, err := utils.GetLocationDetailsFromLatLong(request.Data.Latitude, request.Data.Longitude)
		if err != nil {
			return errors.New("failed to fetch location data from latitude and longitude")
		}
		pincode = locationData.Data.Pincode
	}

	// Check serviceability with caching
	cacheKey := serviceabilityCacheKey(pincode, request.Data.Seller)
	cacheResult, err := cache.GetInstance().GetCachedData(ctx, cacheKey, func() (interface{}, error) {
		return s.CheckServiceAbility(ctx, &dto.AppServiceAblityAPIRequest{
			DeliveryPostCode: pincode,
			COD:              "y",
			UserID:           request.UserID,
			Data:             dto.AppServiceAbilityAPIRequestData{Seller: request.Data.Seller},
		})
	}, 60*time.Second, 0)

	if err != nil {
		return err
	}

	serviceabilityResponse, ok := cacheResult.Data.(*dto.AppserviceAbilityAPIResponse)
	if !ok {
		return fmt.Errorf("invalid cache data type")
	}

	if !serviceabilityResponse.Data.Servicable {
		eventObject := map[string]interface{}{
			"distinct_id":     request.UserID,
			"ordering_module": utils.MakeTitleCase(request.Data.Seller),
			"seller":          request.Data.Seller,
			"category_id":     request.Data.CategoryID,
			"screen_tag":      screenTag,
			"source":          "GET_PRODUCTS",
			"latitude":        request.Data.Latitude,
			"longitude":       request.Data.Longitude,
			"pincode":         pincode,
		}

		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds: []string{request.UserID}, EventName: "Non Servicable Location", EventObject: eventObject,
		})
		return errors.New("not able to serve to the provided location")
	}

	return nil
}

// HandleMichisSeller handles MICHIS seller specific logic (app version check)
func (s *Service) HandleMichisSeller(request *dto.AppSearchRequest, floatingVideoWidget []map[string]interface{}) (*dto.AppSearchResponses, error) {
	requestAppVersion := strings.TrimSpace(request.Meta.AppVersion)
	userAppVersion, err := semver.NewVersion(requestAppVersion)
	if err != nil {
		return nil, err
	}

	paymentsApkVersion, err := semver.NewVersion("6.5.0")
	if err != nil {
		return nil, err
	}

	if userAppVersion.LessThan(paymentsApkVersion) && request.Data.Offset != nil && *request.Data.Offset == 0 {
		return &dto.AppSearchResponses{
			Meta: dto.Meta{Limit: *request.Data.Limit, Offset: *request.Data.Offset},
			Data: dto.AppSearchData{
				Items:        []interface{}{UPDATE_TO_PAYMENTS_APK_BANNER_WIDGET},
				Sellers:      []*dto.SearchResponseSellers{},
				FloatingData: floatingVideoWidget,
			},
		}, nil
	}

	return nil, nil
}

// HandleKiranaClubSeller handles KIRANA_CLUB seller specific logic (order count check)
func (s *Service) HandleKiranaClubSeller(request *dto.AppSearchRequest, floatingVideoWidget []map[string]interface{}) (*dto.AppSearchResponses, error) {
	sellerLevelCount, err := ordervalue.GetSellerLevelOrderCount(request.UserID)
	if err != nil {
		return nil, err
	}

	placedOrderCount := sellerLevelCount[fmt.Sprintf("%s::placed", utils.KIRANA_CLUB)]
	confirmedOrderCount := sellerLevelCount[fmt.Sprintf("%s::confirmed", utils.KIRANA_CLUB)]

	if placedOrderCount == 0 && confirmedOrderCount == 0 {
		return nil, nil
	}

	if request.Data.Exclusive != nil && *request.Data.Exclusive {
		return &dto.AppSearchResponses{
			Meta: dto.Meta{Limit: *request.Data.Limit, Offset: *request.Data.Offset + *request.Data.Limit},
			Data: dto.AppSearchData{
				Items:        []shared.SellerItems{},
				Sellers:      []*dto.SearchResponseSellers{},
				FloatingData: floatingVideoWidget,
			},
		}, nil
	}

	var widgetsArray []interface{}
	if *request.Data.Offset < *request.Data.Limit {
		widgetsArray = []interface{}{KC_COMBO_OFFER_ORDER_PLACED_BANNER}
	}

	return &dto.AppSearchResponses{
		Meta: dto.Meta{Limit: *request.Data.Limit, Offset: 0},
		Data: dto.AppSearchData{
			Items:        widgetsArray,
			Sellers:      []*dto.SearchResponseSellers{},
			FloatingData: floatingVideoWidget,
		},
	}, nil
}

// BuildProductsQuery constructs the SQL query for fetching products based on request parameters
func (s *Service) BuildProductsQuery(request *dto.AppSearchRequest, excludedProducts []string, screenTag string) (string, string) {
	// Build subquery for filtering default products
	queryBuilder := sqlRepo.NewQueryBuilder(&sqlRepo.QueryBuilderOptions{
		TableName: "kiranabazar_products kp",
		DefaultColumns: []string{
			"kp.*",
		},
		DefaultLimit:  request.Data.Limit,
		DefaultOffset: request.Data.Offset,
	})

	queryWhereAnd := []string{
		"kp.is_active = ?",
		"kp.is_default = ?",
		"(kp.product_type = '?' OR kp.product_type = '?')",
	}
	queryArgs := []interface{}{
		true,
		true,
		products.ProductTypeProduct,
		products.ProductTypeVirtual,
	}

	if len(excludedProducts) > 0 {
		placeholders := strings.Repeat("?,", len(excludedProducts))
		placeholders = strings.TrimRight(placeholders, ",")
		queryWhereAnd = append(queryWhereAnd, fmt.Sprintf("kp.id NOT IN (%s)", placeholders))
		for _, id := range excludedProducts {
			queryArgs = append(queryArgs, id)
		}
	}

	if request.Data.Seller != "" {
		queryWhereAnd = append(queryWhereAnd, "kp.seller = '?'")
		queryArgs = append(queryArgs, request.Data.Seller)
	}

	subQueryJoins := make([]string, 0)
	subQueryOrderBy := []string{
		"kp.is_oos ASC",
	}

	if request.Data.CategoryID != "" {
		subQueryJoins = append(subQueryJoins, fmt.Sprintf(`
		JOIN 
				kiranabazar_entities_mapping kcm
			ON kcm.`+"`type`"+` = 'product' and kcm.tag = '%s' and kcm.target_id = kp.id
		`, screenTag))

		queryWhereAnd = append(queryWhereAnd, "kcm.entity_id = ?")
		queryArgs = append(queryArgs, request.Data.CategoryID)

		queryWhereAnd = append(queryWhereAnd, "kcm.is_active = ?")
		queryArgs = append(queryArgs, true)
	} else if request.Data.CategoryID == "" {
		subQueryOrderBy = append(subQueryOrderBy, "kp.popularity_value DESC")
	}
	subQueryOrderBy = append(subQueryOrderBy, "kp.rank ASC")
	subQueryOrderBy = append(subQueryOrderBy, "kp.is_oos ASC")

	// Check cache for products
	cacheKey := ""
	category := request.Data.CategoryID
	if category == "" {
		category = "no_category"
	}
	excludedProductsString := strings.Join(excludedProducts, ",")
	if request.Data.Seller != "" {
		cacheKey = productCacheKey(request.Data.Seller, category, excludedProductsString, *request.Data.Limit, *request.Data.Offset)
	} else {
		cacheKey = productCacheKey("no_seller", category, excludedProductsString, *request.Data.Limit, *request.Data.Offset)
	}

	return queryBuilder.
		WhereAnd(queryWhereAnd, queryArgs).
		WithJoins(subQueryJoins).
		WithOrderBy(subQueryOrderBy).
		WithPagination(request.Data.Limit, request.Data.Offset).ToSQL(), cacheKey
}

// ProcessAndArrageProdcuts processes and arranges products into size variant map
func (s *Service) ProcessAndMakeVariantMap(products []*productsService.Product, category string) map[int][]productsService.Product {
	// Create a map of size_variant_code to product arrays
	sizeVariantMap := make(map[int][]productsService.Product)

	for _, product := range products {
		if product.SizeVariantCode != nil {
			sizeVariants := make([]productsService.Product, len(product.SizeVariants))
			for i, v := range product.SizeVariants {
				sizeVariants[i] = *v
			}
			sizeVariantMap[*product.SizeVariantCode] = sizeVariants
		}
	}

	// make category variants as default to show them on top
	for key, productResp := range sizeVariantMap {
		isDefault := false
		sort.Slice(sizeVariantMap[key], func(i, j int) bool {
			if category != "" {
				if sizeVariantMap[key][i].Rank != nil && sizeVariantMap[key][j].Rank != nil &&
					*sizeVariantMap[key][i].Rank < *sizeVariantMap[key][j].Rank &&
					fmt.Sprintf("%d", sizeVariantMap[key][i].CategoryID) == category {
					return true
				}
				return false
			}
			if sizeVariantMap[key][i].Rank != nil && sizeVariantMap[key][j].Rank != nil {
				return *sizeVariantMap[key][i].Rank < *sizeVariantMap[key][j].Rank
			}
			return false
		})

		for _, j := range productResp {
			if category != "" {
				if j.IsDefault != nil && *j.IsDefault && fmt.Sprintf("%d", j.CategoryID) == category {
					isDefault = true
				}
			} else if j.IsDefault != nil && *j.IsDefault {
				isDefault = true
			}
		}

		if !isDefault && len(sizeVariantMap[key]) > 0 {
			for k := range sizeVariantMap[key] {
				val := false
				sizeVariantMap[key][k].IsDefault = &val
			}
			val := true
			sizeVariantMap[key][0].IsDefault = &val
		}
	}

	// Sort the arrays in the map to ensure the default product is first,
	// followed by products in decreasing order of rank
	for key, productArray := range sizeVariantMap {
		sort.Slice(productArray, func(i, j int) bool {
			isDefaultI := productArray[i].IsDefault != nil && *productArray[i].IsDefault
			isDefaultJ := productArray[j].IsDefault != nil && *productArray[j].IsDefault

			if isDefaultI && !isDefaultJ {
				return true // Default product comes first
			}
			if !isDefaultI && isDefaultJ {
				return false
			}

			isOosI := productArray[i].IsOOS != nil && *productArray[i].IsOOS
			isOosJ := productArray[j].IsOOS != nil && *productArray[j].IsOOS

			// push oos products in the end
			if isOosI && !isOosJ {
				return false // i is OOS, j is not OOS, so j comes first
			}
			if !isOosI && isOosJ {
				return true // i is not OOS, j is OOS, so i comes first
			}
			// For non-default products, sort by rank in ascending order
			if productArray[i].Rank != nil && productArray[j].Rank != nil {
				return *productArray[i].Rank < *productArray[j].Rank
			}
			return false
		})
		sizeVariantMap[key] = productArray
	}

	return sizeVariantMap
}

// ArrangeAndSortProducts sorts products and their size variants, pushing OOS products to the end
func (s *Service) ArrangeAndSortProducts(products []shared.SellerItems, categoryID string) []shared.SellerItems {
	sort.Slice(products, func(i, j int) bool {
		if len(products[i].SizeVariants) > 1 {
			sort.Slice(products[i].SizeVariants, func(ii, jj int) bool {
				// push oos product in the end
				// using bottom badge as proxy for oos as it is not fixed whether oos will be boolean or object
				if (products[i].SizeVariants[ii].BottomBadge != nil) && (products[i].SizeVariants[jj].BottomBadge == nil) {
					return false
				}
				if (products[i].SizeVariants[ii].BottomBadge == nil) && (products[i].SizeVariants[jj].BottomBadge != nil) {
					return true
				}
				if products[i].SizeVariants[ii].Rank < products[i].SizeVariants[jj].Rank {
					return true
				} else {
					return false
				}
			})
		}
		if categoryID == "" {
			if products[i].PopularityValue > products[j].PopularityValue {
				return true
			}
			return false
		} else {
			if products[i].Rank < products[j].Rank {
				return true
			}
			return false
		}
	})

	// sort ooos products in the end of
	sort.Slice(products, func(i, j int) bool {
		if utils.IsReallyNil(products[i].IsOos) && !utils.IsReallyNil(products[j].IsOos) {
			return true
		} else if !utils.IsReallyNil(products[i].IsOos) && utils.IsReallyNil(products[j].IsOos) {
			return false
		}
		return products[i].PopularityValue > products[j].PopularityValue
	})

	return products
}

func GetProductsFromMap(sizeVariantMap map[int][]productsService.Product, excluside *bool, appVersion string, categoryId string,
	showInActive bool, userContext *productsService.PricingContext) ([]shared.SellerItems, error) {
	userAppVersion, _ := semver.NewVersion(appVersion)
	productsScreenChangeAppVersion, _ := semver.NewVersion("6.5.2")

	responseProducts := make([]shared.SellerItems, 0)

	for key, productArray := range sizeVariantMap {
		if len(productArray) == 0 {
			continue
		}

		filteredProducts := make([]productsService.Product, 0, len(productArray))

		for _, product := range productArray {
			// Keep active products or all products if showInActive is true
			if showInActive || product.IsActive == nil || *product.IsActive {
				filteredProducts = append(filteredProducts, product)
			}
		}

		// Update the map with filtered products
		sizeVariantMap[key] = filteredProducts
	}

	for _, productArray := range sizeVariantMap {
		if len(productArray) == 0 {
			continue
		}

		parentProduct := productArray[0]

		// Build size variants
		sizeVariants, err := buildSizeVariants(productArray, parentProduct,
			excluside, userAppVersion, productsScreenChangeAppVersion, userContext)
		if err != nil {
			return nil, err
		}

		if len(sizeVariants) <= 1 {
			sizeVariants = nil
		}

		// Build parent product
		parentSellerItem, err := buildSellerItem(parentProduct,
			excluside, userAppVersion, productsScreenChangeAppVersion, &productArray, userContext)
		if err != nil {
			return nil, err
		}

		responseProducts = append(responseProducts, shared.SellerItems{
			SellerItemsData: parentSellerItem,
			SizeVariants:    sizeVariants,
		})
	}
	return responseProducts, nil
}

// buildSizeVariants creates size variants array
func buildSizeVariants(productArray []productsService.Product, parentProduct productsService.Product, excluside *bool, userAppVersion,
	productsScreenChangeAppVersion *semver.Version, userContext *productsService.PricingContext) ([]shared.SellerItemsData, error) {
	sizeVariants := []shared.SellerItemsData{}

	for _, variant := range productArray {
		sellerItem, err := buildSellerItem(variant, excluside, userAppVersion, productsScreenChangeAppVersion, nil, userContext)
		if err != nil {
			return nil, err
		}
		sellerItem.ParentItemID = fmt.Sprintf("%d", parentProduct.ID)
		sizeVariants = append(sizeVariants, sellerItem)
	}

	return sizeVariants, nil
}

// buildSellerItem creates a SellerItemsData struct for a product
func buildSellerItem(product productsService.Product, excluside *bool, userAppVersion,
	productsScreenChangeAppVersion *semver.Version, productArray *[]productsService.Product, userContext *productsService.PricingContext) (shared.SellerItemsData, error) {
	// Get category ID
	categoryId := fmt.Sprint(product.CategoryID)
	//if product.EntityId != 0 {
	//	categoryId = fmt.Sprint(product.EntityId)
	//}

	// Build navigation
	nav, err := buildNavigation(product, excluside)
	if err != nil {
		return shared.SellerItemsData{}, err
	}

	userAppVersionString := ""
	if userAppVersion != nil {
		userAppVersionString = userAppVersion.String()
	}

	sellerItem := product.SizeVariantsSellerItems(userAppVersionString, userContext, productArray)
	sellerItem.Nav = nav
	sellerItem.CategoryIds = []string{categoryId}
	sellerItem.Type = 0

	// Set parent-specific fields
	if productArray != nil && len(*productArray) > 0 {
		sellerItem.Type = 33
		// sellerItem.ProductType is nil by default (commented out in original)
	} else {
		sellerItem.Type = 0
	}

	return sellerItem, nil
}

// buildNavigation creates navigation object if needed
func buildNavigation(product productsService.Product, excluside *bool) (*shared.Nav, error) {
	if excluside == nil || !*excluside {
		return nil, nil
	}

	source, exists := brands.GetSourceBySeller(product.Seller)
	if !exists {
		slack.SendSlackMessage(fmt.Sprintf("source not found for seller: %s", product.Seller))
		return nil, fmt.Errorf("source not found for seller: %s", product.Seller)
	}

	return &shared.Nav{
		Name:    "OrderingModule",
		NavType: "Redirect to Screen",
		Params: map[string]interface{}{
			"params": map[string]interface{}{
				"seller":     product.Seller,
				"source":     source,
				"product_id": fmt.Sprintf("%d", product.ID),
			},
			"screen": "Products",
			"seller": product.Seller,
			"source": source,
		},
	}, nil
}
