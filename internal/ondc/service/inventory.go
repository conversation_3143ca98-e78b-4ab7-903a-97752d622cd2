package service

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"kc/internal/ondc/models/dto"
	inventoryDao "kc/internal/ondc/service/inventory/models/dao"
	inventoryDto "kc/internal/ondc/service/inventory/models/dto"
	"time"

	"github.com/gin-gonic/gin"
)

func (s *Service) AddProductInventory(ctx context.Context, request *dto.AddInventoryRequest) (*dto.AddInventoryResponse, error) {

	addProductInventoryReq := inventoryDto.AddInventoryRequest{
		ProductID:    request.Data.ProductID,
		Quantity:     request.Data.Quantity,
		Unit:         request.Data.Unit,
		AdditionType: request.Data.AdditionType,
		Seller:       request.Data.Seller,
		UpdatedBy:    request.UpdatedBy,
		Comment:      request.Data.Comment,
	}
	response, err := s.Inventory.AddProductsInventory(ctx, &addProductInventoryReq)
	if err != nil {
		return nil, err
	}

	if response == nil || (response != nil && !response.Success) {
		errMsg := "failed to add product inventory"
		if response != nil && response.Error != "" {
			errMsg = fmt.Sprintf("%s: %s", errMsg, response.Error)
		}
		return nil, errors.New("failed to add product inventory: " + errMsg)
	}

	if response.ShouldMarkInstock && response.InventoryUpdate != nil {
		product := make([]inventoryDao.KiranaBazarProductsInventory, 0)
		product = append(product, inventoryDao.KiranaBazarProductsInventory{
			ID:              uint64(request.Data.ProductID),
			DisplayQuantity: response.InventoryUpdate.InventoryAfter,
			UpdatedAt:       time.Now(),
			UpdatedBy:       request.UpdatedBy,
			Seller:          request.Data.Seller,
		})
		s.UpdateProductsStockStatus(ctx, product, false, request.UpdatedBy)
	}

	return &dto.AddInventoryResponse{
		Success: response.Success,
		Error:   response.Error,
		Data:    response.InventoryUpdate,
	}, nil
}

func (s *Service) GetInvenotryProductsForSeller(ctx *gin.Context, request *dto.ExportInventoryProductsRequest) ([]dto.ExportInventoryProductsData, error) {
	sellers := request.Data.Filters.Sellers
	if len(sellers) == 0 {
		return nil, errors.New("no sellers provided in the request")
	}

	sellersString := ""
	for i, seller := range sellers {
		if i > 0 {
			sellersString += ", "
		}
		sellersString += fmt.Sprintf("'%s'", seller)
	}

	query := fmt.Sprintf(`
	SELECT 
		kp.id, 
		kp.code, 
		kp.name, 
		JSON_UNQUOTE(JSON_EXTRACT(kp.meta, '$.quantity')) AS quantity,   
		JSON_UNQUOTE(JSON_EXTRACT(kp.meta, '$.case_size')) AS case_size,
		JSON_UNQUOTE(JSON_EXTRACT(kp.meta, '$.pack_size')) AS pack_size,
		JSON_UNQUOTE(JSON_EXTRACT(kp.meta, '$.wholesale_rate')) AS wholesale_rate,
		JSON_UNQUOTE(JSON_EXTRACT(kp.meta, '$.mrp_number')) AS mrp_number,
		kp.is_oos AS oos_status,
		kpi.display_quantity AS inventory_quantity,
		kpiah30.throughput_per_day,
		kpiah30.quantity_sold AS quantity_sold30d, 
		kpiah7.quantity_sold AS quantity_sold7d
	FROM 
		kiranaclubdb.kiranabazar_products kp
	LEFT JOIN 
		kiranabazar_products_inventory kpi 
		ON kp.id = kpi.id 
	LEFT JOIN 
		kiranabazar_product_inventory_analysis_history kpiah30
		ON kp.id = kpiah30.product_id 
		AND kpiah30.analysis_type = "LAST_30_DAYS_ANALYSIS"
		AND kpiah30.analysis_date = CURDATE()
	LEFT JOIN 
		kiranabazar_product_inventory_analysis_history kpiah7 
		ON kp.id = kpiah7.product_id 
		AND kpiah7.analysis_type = "LAST_7_DAYS_ANALYSIS"
		AND kpiah7.analysis_date = CURDATE()
	WHERE 
		kp.seller IN (%s) and kp.is_active = true
	ORDER BY 
		kp.id;
	`, sellersString)

	productsInventoryData := []dto.ExportInventoryProductsData{}
	_, err := s.repository.CustomQuery(&productsInventoryData, query)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch inventory products for sellers: %w", err)
	}
	return productsInventoryData, nil
}

func (s *Service) ExportInventoryProducts(ctx *gin.Context, request *dto.ExportInventoryProductsRequest) error {
	records, err := s.GetInvenotryProductsForSeller(ctx, request)
	if err != nil {
		logger.Error(ctx, "failed to get inventory products for sellers: %v", err)
		return fmt.Errorf("failed to get inventory products for sellers: %w", err)
	}

	ctx.Header("Content-Disposition", `attachment; filename="OrderLevelExport.csv"`)
	ctx.Header("Content-Type", "text/csv")

	writer := csv.NewWriter(ctx.Writer)
	header := []string{
		"Product ID",
		"SKU Code",
		"Product Name",
		"Quantity",
		"Case Size",
		"Pack Size",
		"OOS Status",
		"Inventory Quantity",
		"PTR / Piece",
		"MRP",
		"Pieces Sold (Last 30 Days)",
		"Pieces Sold (Last 7 Days)",
	}

	err = writer.Write(header)
	if err != nil {
		logger.Error(ctx, "failed to write CSV header: %v", err)
		return fmt.Errorf("failed to write CSV header: %w", err)
	}

	for _, record := range records {
		row := []string{
			fmt.Sprintf("%d", record.ID),
			record.Code,
			record.Name,
			record.Quantity,
			fmt.Sprintf("%d", record.CaseSize),
			fmt.Sprintf("%d", record.PackSize),
			fmt.Sprintf("%t", record.OosStatus),
			fmt.Sprintf("%d", record.InventoryQuantity),
			fmt.Sprintf("%.2f", record.WholesaleRate),
			fmt.Sprintf("%.2f", record.MrpNumber),
			fmt.Sprintf("%d", record.QuantitySold30d),
			fmt.Sprintf("%d", record.QuantitySold7d),
		}

		err = writer.Write(row)
		if err != nil {
			logger.Error(ctx, "failed to write CSV row: %v", err)
			return fmt.Errorf("failed to write CSV row: %w", err)
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		logger.Error(ctx, "error flushing CSV writer: %v", err)
		return fmt.Errorf("error flushing CSV writer: %w", err)
	}
	return nil
}
