package service

import (
	"context"
	"fmt"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/utils"

	"github.com/mixpanel/mixpanel-go"
)

type resolveScreenName struct {
	cohortName                     []string
	userOverallConfirmedOrderCount int
	UserId                         string
	MixPanelRepo                   *mixpanelRepo.Repository
}

func resolveScreenNameAccordingToHierarchy(r *resolveScreenName) (string, error) {

	type HandlerContext struct {
		cohortNames  []string
		orderCount   int
		UserId       string
		mixPanelRepo *mixpanelRepo.Repository
	}

	ctx := HandlerContext{
		cohortNames:  r.cohortName,
		orderCount:   r.userOverallConfirmedOrderCount,
		UserId:       r.UserId,
		mixPanelRepo: r.MixPanelRepo,
	}

	handlers := []func(HandlerContext) *string{
		func(ctx HandlerContext) *string {
			return handleRsbHeirarchy(&ctx.cohortNames, ctx.orderCount, ctx.UserId, ctx.mixPanelRepo)
		},
		func(ctx HandlerContext) *string {
			return handleZeroOrderPlaced(ctx.orderCount)
		},
	}
	for _, handler := range handlers {
		if screenName := handler(ctx); screenName != nil {
			return *screenName, nil
		}
	}
	return "Seller", nil
}

func getAscii(userId string) int {
	firstChar := userId[0]
	asciiValue := int(firstChar)
	result := asciiValue % 2

	return result
}

func setSellerCohortProperty(mixpanelRepo *mixpanelRepo.Repository, userID string, cohortName string) {
	mixpanelRepo.PeopleSet(context.Background(), []*mixpanel.PeopleProperties{
		{
			DistinctID: userID,
			Properties: map[string]any{
				"seller_cohort": fmt.Sprintf("%s", cohortName),
			},
		},
	},
	)
}

func handleRsbZeroOrderPlacedSubCondition(userID string, mixPanelRepo *mixpanelRepo.Repository) string {
	// switch getAscii(userID) {
	// case 0:
	// 	go setSellerCohortProperty(mixPanelRepo, userID, "Seller_RSB_New_To_Ordering")
	// 	return "Seller_RSB_New_To_Ordering"
	// case 1:
	// 	go setSellerCohortProperty(mixPanelRepo, userID, "Seller_RSB_New_To_Ordering_B")
	// 	return "Seller_RSB_New_To_Ordering_B"
	// }
	return "Seller_RSB_New_To_Ordering"
}

func handleRsbOrderPlacedSubCondition(userID string, mixPanelRepo *mixpanelRepo.Repository) string {
	// switch getAscii(userID) {
	// case 0:
	// 	go setSellerCohortProperty(mixPanelRepo, userID, "Seller_RSB")
	// 	return "Seller_RSB"
	// case 1:
	// 	go setSellerCohortProperty(mixPanelRepo, userID, "Seller_RSB_B")
	// 	return "Seller_RSB_B"
	// }
	return "Seller_RSB"
}

// Update handler functions to have consistent signatures
func handleRsbHeirarchy(userCohortNames *[]string, userOverallConfirmedOrderCount int, userId string, mixPanelRepo *mixpanelRepo.Repository) *string {
	if userCohortNames == nil {
		return nil
	}
	if includes(*userCohortNames, utils.RSB_STOCKIST_USER_COHORT) {
		var screenName string
		if userOverallConfirmedOrderCount == 0 {
			screenName = handleRsbZeroOrderPlacedSubCondition(userId, mixPanelRepo)
			return &screenName
		}
		screenName = handleRsbOrderPlacedSubCondition(userId, mixPanelRepo)
		return &screenName
	}
	return nil
}
func handleZeroOrderPlaced(userOverallConfirmedOrderCount int) *string {
	if userOverallConfirmedOrderCount == 0 {
		var screenName string
		screenName = "Seller_New_To_Ordering"
		return &screenName
	}
	return nil
}
