package service

import (
	"context"
	"fmt"
	"kc/internal/ondc/models/dto"
)

func (s *Service) OnSelect(ctx context.Context, req *dto.OnSelectRequest) error {
	key := fmt.Sprintf("%s%s", *req.Context.TransactionID, *req.Context.MessageID)
	fmt.Println("setting in select", key)
	provider := req.Message.Order.Provider.ID
	//locations := req.Message.Order.Provider.Locations
	fulfillments := map[string]dto.ItemFulfilment{}
	for _, d := range req.Message.Order.Fulfillments {
		if _, ok := fulfillments[d.ID]; !ok {
			f := dto.ItemFulfilment{
				ID:       d.ID,
				Name:     d.ONDCOrgProviderName,
				Category: d.ONDCOrgCategory,
				TAT:      d.ONDCOrgTAT,
				Tracking: d.Tracking,
			}
			fulfillments[d.ID] = f
		}
	}

	items := map[string]dto.CartItem{}

	for _, d := range req.Message.Order.Quote.Breakup {
		data := dto.CartItem{}
		if d.ONDCOrgTitleType != nil && *d.ONDCOrgTitleType == "item" { // It means that this particular entry is an item
			fmt.Println("d.I", d.Item.Price.Value)

			data.ItemID = d.ONDCOrgItemID
			data.Quantity = int32(d.ONDCOrgItemQuantity.Count)
			data.Price = getFloatFromString(d.Price.Value)
			data.PerItemPrice = getFloatFromString(d.Item.Price.Value)

			if d.ONDCOrgTitleType != nil && *d.ONDCOrgTitleType == "delivery" {
				data.DeliveryCharge = getFloatFromString(d.Item.Price.Value)
			}

			if d.ONDCOrgTitleType != nil && *d.ONDCOrgTitleType == "tax" {
				data.Tax = getFloatFromString(d.Item.Price.Value)
			}
			if d.ONDCOrgTitleType != nil && *d.ONDCOrgTitleType == "tax" {
				data.Tax = getFloatFromString(d.Item.Price.Value)
			}
			items[d.ONDCOrgItemID] = data
		}
	}

	for _, d := range req.Message.Order.Quote.Breakup {
		if d.ONDCOrgTitleType != nil && *d.ONDCOrgTitleType == "tax" { // It means that this particular entry is an item
			item := items[d.ONDCOrgItemID]
			item.Tax = getFloatFromString(d.Price.Value)
			items[d.ONDCOrgItemID] = item
		}
	}

	meta := dto.Meta{
		Provider:    req.Message.Order.Provider,
		Items:       req.Message.Order.Items,
		Tags:        req.Message.Order.Tags,
		Payment:     req.Message.Order.Payment,
		Fulfillment: req.Message.Order.Fulfillments,
		BppID:       req.Context.BppID,
		BppUrl:      req.Context.BppURI,
	}
	cart := dto.Cart{
		ProviderID: provider,
		Quote: dto.Quote{
			TotalPrice: getFloatFromString(req.Message.Order.Quote.Price.Value),
			Breakup:    items,
		},
		Meta: meta,
	}

	resp := dto.AppSelectResponse{Cart: cart}
	resp.Cart.BppID = req.Context.BppID
	resp.Cart.BppUrl = req.Context.BppURI
	if req.Error != nil && req.Error.Code != nil {
		resp.Error = dto.AppResponseError{
			Code:        req.Error.Code,
			Message:     &req.Error.Message,
			Description: &req.Error.Path,
			Type:        &req.Error.Type,
		}
	}

	_, err := s.Cache.Create(ctx, key, resp)
	if err != nil {
		return err
	}
	return nil

}
