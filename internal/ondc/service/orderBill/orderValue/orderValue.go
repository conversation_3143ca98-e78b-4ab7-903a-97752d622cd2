package ordervalue

import (
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/brands"
	"strings"
	"sync"
	"time"
)

var minOrderService *sellerMinOrderImpl

type SellerMinOrderService interface {
	GetMinOrderValue(sellerCode string, orderNumber int) (float64, error)
	GetMinOrderValueData(sellerCode string, orderNumber int) (*MinOrderRule, error)
}

type MinOrderRule struct {
	OrderNumber    int
	MinAmount      float64
	BannerImageUrl string `json:"banner_image_url"`
	PositionIndex  int    `json:"position_index"`
	Cohort         string `json:"cohort"`
}

type SellerOrderCount map[string]int

type userOrderCacheEntry struct {
	counts    SellerOrderCount
	timestamp time.Time
}

type sellerMinOrderImpl struct {
	db                  *sqlRepo.Repository
	cache               map[string][]MinOrderRule
	userOrderCountCache map[string]userOrderCacheEntry
	cacheMutex          sync.RWMutex
	lastRefresh         time.Time
}

func NewSellerMinOrderService(db *sqlRepo.Repository) {
	s := &sellerMinOrderImpl{
		db:                  db,
		cache:               make(map[string][]MinOrderRule),
		userOrderCountCache: make(map[string]userOrderCacheEntry),
	}

	// Initial load
	s.refreshCache()

	// Start background refresh
	go s.backgroundRefresh()

	minOrderService = s
}

func (s *sellerMinOrderImpl) refreshCache() error {
	// Fetch all rules from database
	kmors := []dao.KiranabazarMinOrdervalueRules{}
	_, err := s.db.CustomQuery(&kmors, `select * from kiranabazar_min_ordervalue_rules where is_active = true;`)
	if err != nil {
		return err
	}

	newCache := make(map[string][]MinOrderRule)

	for _, row := range kmors {
		meta := dao.KiranabazarMinOrdervalueRulesMetaData{}
		if row.Meta != nil {
			err := json.Unmarshal(row.Meta, &meta)
			if err != nil {
				return err
			}
		}
		rule := MinOrderRule{
			OrderNumber: row.OrderNumber,
			MinAmount:   row.MinAmount,
			Cohort:      row.Cohort,
		}
		rule.PositionIndex = 0
		if meta.BannerImageUrl != nil {
			rule.BannerImageUrl = *meta.BannerImageUrl
		}
		if meta.PositionIndex != nil && *meta.PositionIndex > 0 {
			rule.PositionIndex = *meta.PositionIndex
		}
		newCache[row.Seller] = append(newCache[row.Seller], rule)
	}

	// Update cache atomically
	s.cacheMutex.Lock()
	s.cache = newCache
	s.lastRefresh = time.Now()
	s.cacheMutex.Unlock()

	return nil
}

func (s *sellerMinOrderImpl) backgroundRefresh() {
	ticker := time.NewTicker(1 * time.Minute)
	for range ticker.C {
		// Refresh min order rules cache
		err := s.refreshCache()
		if err != nil {
			fmt.Printf("Error refreshing cache: %v\n", err)
		}

		// Cleanup expired user order count entries
		s.cacheMutex.Lock()
		for userID, entry := range s.userOrderCountCache {
			if time.Since(entry.timestamp) > time.Minute {
				delete(s.userOrderCountCache, userID)
			}
		}
		s.cacheMutex.Unlock()
	}
}

func GetMinOrderValue(sellerCode string, orderNumber int, userCohortNames []string) (float64, error) {
	s := minOrderService
	s.cacheMutex.RLock()
	rules, exists := s.cache[sellerCode]
	s.cacheMutex.RUnlock()

	if !exists {
		return 0, fmt.Errorf("no rules found for seller %s", sellerCode)
	}

	userCohortSet := make(map[string]bool)
	for _, cohort := range userCohortNames {
		userCohortSet[cohort] = true
	}

	// 1. Look for cohort-specific rule for the order number (excluding "ALL")
	for _, rule := range rules {
		if rule.OrderNumber == orderNumber && userCohortSet[rule.Cohort] {
			return rule.MinAmount, nil
		}
	}

	// 2. Look for "ALL" cohort rule for the order number
	for _, rule := range rules {
		if rule.OrderNumber == orderNumber && rule.Cohort == "ALL" {
			return rule.MinAmount, nil
		}
	}

	// 3. Look for cohort-specific default rule (orderNumber == 0, excluding "ALL")
	for _, rule := range rules {
		if rule.OrderNumber == 0 && userCohortSet[rule.Cohort] {
			return rule.MinAmount, nil
		}
	}

	// 4. Look for "ALL" cohort default rule (orderNumber == 0)
	for _, rule := range rules {
		if rule.OrderNumber == 0 && rule.Cohort == "ALL" {
			return rule.MinAmount, nil
		}
	}

	return 0, fmt.Errorf("no minimum order rule found for seller %s", sellerCode)
}

func GetMinOrderValueData(sellerCode string, orderNumber int, userCohortNames []string) (*MinOrderRule, error) {
	s := minOrderService
	s.cacheMutex.RLock()
	rules, exists := s.cache[sellerCode]
	s.cacheMutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("no rules found for seller %s", sellerCode)
	}

	userCohortSet := make(map[string]bool)
	for _, cohort := range userCohortNames {
		userCohortSet[cohort] = true
	}

	// 1. Look for cohort-specific rule for the order number (excluding "ALL")
	for _, rule := range rules {
		if rule.OrderNumber == orderNumber && userCohortSet[rule.Cohort] {
			return &rule, nil
		}
	}

	// 2. Look for "ALL" cohort rule for the order number
	for _, rule := range rules {
		if rule.OrderNumber == orderNumber && rule.Cohort == "ALL" {
			return &rule, nil
		}
	}

	// 3. Look for cohort-specific default rule (orderNumber == 0, excluding "ALL")
	for _, rule := range rules {
		if rule.OrderNumber == 0 && userCohortSet[rule.Cohort] {
			return &rule, nil
		}
	}

	// 4. Look for "ALL" cohort default rule (orderNumber == 0)
	for _, rule := range rules {
		if rule.OrderNumber == 0 && rule.Cohort == "ALL" {
			return &rule, nil
		}
	}

	return nil, fmt.Errorf("no minimum order rule found for seller %s", sellerCode)
}

func UpdateOrderCount(userID string, seller string) error {
	// TODO: @sanket Implementation pending, implement when needed
	return nil
}

func GetSellerLevelOrderCount(userID string) (SellerOrderCount, error) {
	s := minOrderService
	s.cacheMutex.RLock()
	cacheEntry, exists := s.userOrderCountCache[userID]
	s.cacheMutex.RUnlock()

	// Check if cache exists and is still valid (less than 1 minute old)
	if exists && time.Since(cacheEntry.timestamp) < time.Minute {
		return cacheEntry.counts, nil
	}

	// If cache doesn't exist or is expired, fetch fresh data
	type sellerLevelOrderCount struct {
		Seller              string `json:"seller"`
		OrderCount          int    `json:"order_count"`
		ConfirmedOrderCount int    `json:"confirmed_order_count"`
		PlacedOrderCount    int    `json:"placed_order_count"`
	}
	sloc := []sellerLevelOrderCount{}

	_, err := s.db.CustomQuery(&sloc, fmt.Sprintf(`SELECT 
    seller,
		COUNT(*) AS order_count,
		COUNT(CASE 
			WHEN display_status IN (
				'DELIVERED',
				'RETURNED',
				'SHIPMENT_CREATED',
				'IN_TRANSIT',
				'CONFIRMED',
				'OTHERS',
				'NDR'
			) THEN 1 
			ELSE NULL 
		END) AS confirmed_order_count,
		COUNT(CASE 
			WHEN display_status IN (
				'PLACED'
			) THEN 1 
			ELSE NULL 
		END) AS placed_order_count
	FROM 
		kiranabazar_orders ko
	WHERE 
		user_id = '%s'
	GROUP BY 
    seller;`, userID))
	if err != nil {
		return nil, err
	}

	// Create new cache entry
	socs := make(SellerOrderCount)
	for _, s := range sloc {
		socs[s.Seller] = s.OrderCount
		socs[fmt.Sprintf("%s::confirmed", s.Seller)] = s.ConfirmedOrderCount
		socs[fmt.Sprintf("%s::placed", s.Seller)] = s.PlacedOrderCount
	}

	// Update cache with new timestamp
	newCacheEntry := userOrderCacheEntry{
		counts:    socs,
		timestamp: time.Now(),
	}

	s.cacheMutex.Lock()
	s.userOrderCountCache[userID] = newCacheEntry
	s.cacheMutex.Unlock()

	return socs, nil
}

// Updated GetSellerLevelOrderCount function with June filter
func GetSellerLevelOrderCountForJune(userID string) (SellerOrderCount, error) {
	s := minOrderService

	// Create cache key specific to June data
	cacheKey := fmt.Sprintf("%s_june_2025", userID)

	s.cacheMutex.RLock()
	cacheEntry, exists := s.userOrderCountCache[cacheKey]
	s.cacheMutex.RUnlock()

	// Check if cache exists and is still valid (less than 1 minute old)
	if exists && time.Since(cacheEntry.timestamp) < time.Minute {
		return cacheEntry.counts, nil
	}

	// If cache doesn't exist or is expired, fetch fresh data
	type sellerLevelOrderCount struct {
		Seller              string `json:"seller"`
		OrderCount          int    `json:"order_count"`
		ConfirmedOrderCount int    `json:"confirmed_order_count"`
		PlacedOrderCount    int    `json:"placed_order_count"`
	}

	sloc := []sellerLevelOrderCount{}

	_, err := s.db.CustomQuery(&sloc, fmt.Sprintf(`
	select
		count(*) as confirmed_order_count,
		oms as seller
	from
		kc_bazar_reconciliation kbr
	join kiranabazar_orders ko on
		ko.id = kbr.order_id
	where
		kbr.order_confirmed >= 1748736000000
		and kbr.order_confirmed <= 1751328000000
		and user_id = '%s'
	group by
		oms;`, userID))

	if err != nil {
		return nil, err
	}

	// Create new cache entry
	socs := make(SellerOrderCount)
	for _, s := range sloc {
		socs[s.Seller] = s.OrderCount
		socs[fmt.Sprintf("%s::confirmed", s.Seller)] = s.ConfirmedOrderCount
		socs[fmt.Sprintf("%s::placed", s.Seller)] = s.PlacedOrderCount
	}

	// Update cache with new timestamp using June-specific key
	newCacheEntry := userOrderCacheEntry{
		counts:    socs,
		timestamp: time.Now(),
	}

	s.cacheMutex.Lock()
	s.userOrderCountCache[cacheKey] = newCacheEntry
	s.cacheMutex.Unlock()

	return socs, nil
}

func GetUserLevelOrderCount(userID string) (int, error) {
	sellerCounts, err := GetSellerLevelOrderCount(userID)
	if err != nil {
		return 0, err
	}

	// Sum all the orders across all sellers
	totalCount := 0

	for key, count := range sellerCounts {
		if _, exists := brands.GetBrandMetaBySeller(key); exists {
			totalCount += count
		}
	}

	return totalCount, nil
}

func GetUserLevelConfirmedOrderCount(userID string) (int, error) {
	sellerCounts, err := GetSellerLevelOrderCount(userID)
	if err != nil {
		return 0, err
	}

	// Sum all the confirmed orders across all sellers
	totalCount := 0
	for key, count := range sellerCounts {
		if strings.Contains(key, "::confirmed") {
			totalCount += count
		}
	}

	return totalCount, nil
}

func GetUserSellerLevelConfirmedOrderCount(userID string, seller string) (int, error) {
	sellerCounts, err := GetSellerLevelOrderCount(userID)
	if err != nil {
		return 0, err
	}

	return sellerCounts[fmt.Sprintf("%s::confirmed", seller)], nil
}

func GetSellerLevelConfirmedOrderCount(userId string) (SellerOrderCount, error) {
	sellerCounts, err := GetSellerLevelOrderCount(userId)
	if err != nil {
		return nil, err
	}

	confirmedCounts := make(SellerOrderCount)
	for key, value := range sellerCounts {
		if strings.Contains(key, "::confirmed") {
			// remove ::confirmed from key
			key = strings.TrimSuffix(key, "::confirmed")
			if key != "" {
				confirmedCounts[key] = value
			}
		}
	}

	return confirmedCounts, nil
}

func GetUserSellerLevelPlacedOrderCount(userID string) (int, SellerOrderCount, error) {
	sellerCounts, err := GetSellerLevelOrderCount(userID)
	if err != nil {
		return 0, SellerOrderCount{}, err
	}

	count := 0
	for key, value := range sellerCounts {
		if strings.Contains(key, "::placed") {
			count += value
		}
	}
	return count, sellerCounts, nil
}
