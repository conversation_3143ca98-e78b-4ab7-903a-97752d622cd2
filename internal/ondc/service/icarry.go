package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/icarry"
	"kc/internal/ondc/models/dto"
	"time"
)

func icarryResponseGenerator(sericeAbilityData icarry.CourierServiceAblityAPIResponse) dto.AppserviceAbilityAPIResponse {
	response := dto.AppserviceAbilityAPIResponse{
		Data: dto.AppServiceAbilityAPIData{
			Servicable: false,
			Message:    "",
		},
	}

	if sericeAbilityData.Success == 0 || len(sericeAbilityData.Msg) == 0 {
		response.Data.Servicable = false
		response.Data.Message = "Service not available"
		return response
	}

	codAvailable := false
	for _, msg := range sericeAbilityData.Msg {
		if msg.COD == "Y" {
			codAvailable = true
			break
		}
	}

	if !codAvailable {
		response.Data.Servicable = false
		response.Data.Message = "COD not available"
		return response
	}

	response.Data.Servicable = true
	response.Data.Message = "Service available"
	response.Data.MinimumDeliveryDays = "6"

	return response
}

func fetchIcarryPincodeServiceabilityFromApi(pincode string) (icarry.CourierServiceAblityAPIResponse, error) {
	body := map[string]interface{}{
		"pincode": pincode,
	}
	serviceAbilityResponse, err := icarry.CallIcarryAPI("SERVICE_ABILITY", body, nil)
	if err != nil {
		fmt.Println("Error in icarry service ability API call: ", err)
		return icarry.CourierServiceAblityAPIResponse{}, err
	}

	serviceAbilityApiResponse := icarry.CourierServiceAblityAPIResponse{}
	err = json.Unmarshal(serviceAbilityResponse, &serviceAbilityApiResponse)
	if err != nil {
		return icarry.CourierServiceAblityAPIResponse{}, err
	}

	return serviceAbilityApiResponse, nil
}

func (s *Service) CheckIcarryServiceAbility(ctx context.Context, request *icarry.CourierServiceAblityAPIRequest) (dto.AppserviceAbilityAPIResponse, error) {
	response := dto.AppserviceAbilityAPIResponse{
		Data: dto.AppServiceAbilityAPIData{
			Servicable: false,
			Message:    "",
		},
	}
	value, exists := icarry.ICARRY_SERVICEABLE_PINCODES_MAP[request.Pincode]
	if exists {
		apiResponseChan := make(chan *icarry.CourierServiceAblityAPIResponse, 1)

		go func(p string) {
			apiResponse, err := fetchIcarryPincodeServiceabilityFromApi(p)
			if err != nil {
				fmt.Println("Error in API call: ", err)
				apiResponseChan <- nil
				return
			}
			apiResponseChan <- &apiResponse
		}(request.Pincode)

		select {
		case apiResponse := <-apiResponseChan:
			if apiResponse != nil {
				icarry.ICARRY_SERVICEABLE_PINCODES_MAP[request.Pincode] = *apiResponse
				return icarryResponseGenerator(*apiResponse), nil
			}
			return icarryResponseGenerator(value), nil
		case <-time.After(icarry.ICARRY_MAX_RESPONSE_WAIT_TIME):
			go func(p string, apiResponseChan chan *icarry.CourierServiceAblityAPIResponse) {
				if apiResponse := <-apiResponseChan; apiResponse != nil {
					icarry.ICARRY_SERVICEABLE_PINCODES_MAP[p] = *apiResponse
				}
			}(request.Pincode, apiResponseChan)
			return icarryResponseGenerator(value), nil
		}
	}

	serviceAbilityApiResponse, err := fetchIcarryPincodeServiceabilityFromApi(request.Pincode)
	if err != nil {
		return response, err
	}

	icarry.ICARRY_SERVICEABLE_PINCODES_MAP[request.Pincode] = serviceAbilityApiResponse

	return icarryResponseGenerator(serviceAbilityApiResponse), nil
}
