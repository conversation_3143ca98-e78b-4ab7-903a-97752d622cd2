package service

import (
	"context"
	"errors"
	"fmt"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/service/logistics/couriers"
	orderreason "kc/internal/ondc/service/orderReason"
	orderS "kc/internal/ondc/service/orderStatus"
	"kc/internal/ondc/service/orderStatus/constants"
	"kc/internal/ondc/utils"
	"strconv"
	"time"

	"github.com/mixpanel/mixpanel-go"
)

func (s *Service) HandleReturnedOrder(ctx context.Context, request dto.OrderReturnedRequest) (response *dto.OrderReturnedResponse, err error) {
	// getting source
	source := "APP"
	if request.Data.Email == "IVR" {
		source = "AUTOMATION"
	} else if request.Data.Email != "" {
		source = "D2R"
	}
	orderID := request.Data.OrderID
	if orderID == "" {
		err = errors.New("orderID cannot be empty")
		return
	}

	orderid, err := strconv.Atoi(orderID)
	if err != nil {
		err = errors.New("orderID is not defined")
		return
	}
	orderid64 := int64(orderid)
	orderDetails, err := GetOrderDetails(s.repository, orderID)
	if err != nil {
		return
	}

	orderInfo, err := GetOrderInfo(s.repository, orderid64)
	if err != nil {
		err = errors.New("order with given orderid doest not exists")
		return
	}
	orderApiStatus, err := GetOrderApiStatus(s.repository, orderid64)
	if err != nil {
		return
	}

	orderNDRInfo, _ := s.GetNDROrder(int(orderid64))

	returnedOrderStatus := "RETURNED"
	if request.Data.Status != "" {
		returnedOrderStatus = request.Data.Status
	}
	orderStatuses := orderS.MapOrderStatus(returnedOrderStatus, request.Data.StatusType, orderS.OrderStatusResponse{})

	_, _, err = s.repository.Update(dao.KiranaBazarOrder{
		ID: &orderid64,
	}, dao.KiranaBazarOrder{
		OrderStatus:      &returnedOrderStatus,
		UpdatedAt:        time.Now(),
		DeliveryStatus:   orderStatuses.ShipmentStatus,
		DisplayStatus:    orderStatuses.DisplayStatus,
		ProcessingStatus: orderStatuses.ProcessingStatus,
	})
	if err != nil {
		return
	}

	if orderStatuses.DisplayStatus == orderInfo.DisplayStatus {
		return
	}

	returnReasonMap := orderreason.GetOrderReturnCodeFromReason(request.Data.Reason)

	eventObject := map[string]interface{}{
		"distinct_id":        request.UserID,
		"order_id":           orderid64,
		"cart_value":         int(orderDetails.GetCartValue()),
		"order_value":        int(orderDetails.GetOrderValue()),
		"return_reason":      returnReasonMap.ReasonString,
		"return_reason_code": returnReasonMap.ReasonCode,
		"seller":             orderInfo.Seller,
		"explaination":       request.Data.Explanation,
		"source":             source,
		"ordering_module":    utils.MakeTitleCase(orderInfo.Seller),
	}

	if orderInfo.TrackingLink != nil {
		eventObject["tracking_link"] = *orderInfo.TrackingLink
	}
	if orderApiStatus.AWBNumber != nil {
		eventObject["awb_number"] = *orderApiStatus.AWBNumber
	}
	if orderApiStatus.Courier != nil {
		eventObject["courier_name"] = couriers.GetActualCourierName(orderApiStatus.Courier)
	}
	if request.Data.Email != "" {
		eventObject["email"] = request.Data.Email
	}
	if orderNDRInfo != nil {
		eventObject["email_NDR"] = orderNDRInfo.AssignedTo
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Returned", request.UserID, eventObject,
			fmt.Sprintf("%s_%d", "order_returned", orderid64)),
	})

	err = webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{request.UserID},
		EventName:   "Order Returned",
		EventObject: eventObject,
	})
	if err != nil {
		fmt.Println("failed to send webengage event")
	}

	// this is go routine fired to call external api to update the widget
	go func(uid, orderid, seller, orderStatus string, totalAmount float64, orderid64 int64) {
		// reqObject := map[string]interface{}{
		// 	"user_id":  request.UserID,
		// 	"order_id": request.Data.OrderID,
		// 	"amount":   orderDetails.TotalAmount,
		// 	"status":   constants.RETURNED,
		// }
		// // call this API again for zoff_foods or RSB for thailand scheme
		// utils.CallExternalAPIAsync(utils.PROGRESS_WIDGET_RESOLVER_API, "POST", reqObject, nil)

		s.UpdateProgressWidget(context.Background(), &dto.UpdateProgressWidgetRequest{
			UserID: uid,
			Data: dto.UpdateProgressWidgetData{
				OrderID:      orderid,
				Status:       orderStatus,
				Amount:       orderDetails.TotalAmount,
				OrderDetails: orderDetails,
				Seller:       seller,
			},
		})
	}(request.UserID, request.Data.OrderID, orderInfo.Seller, constants.RETURNED, orderDetails.TotalAmount, orderid64)

	go func(mp *mixpanelRepo.Repository, userID string, orderID int64, orderValue int, s *Service, note, email string, source, seller string, orderStatuses orderS.OrderStatusResponse) {
		trackingObject := map[string]interface{}{
			"distinct_id":             userID,
			"order_id":                orderID,
			"order_value":             orderValue,
			"status":                  constants.RETURNED,
			"notes":                   note,
			"ordering_module":         utils.MakeTitleCase(seller),
			"seller":                  seller,
			"email":                   email,
			"source":                  source,
			"shipment_status":         orderStatuses.ShipmentStatus,
			"processing_status":       orderStatuses.ProcessingStatus,
			"display_status":          orderStatuses.DisplayStatus,
			"previous_display_status": orderInfo.DisplayStatus,
			"event_trigger":           "order_returned",
		}
		mp.Track(context.Background(), []*mixpanel.Event{
			mp.NewEvent("Order Status Updated", userID, trackingObject),
		})

		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{userID},
			EventName:   "Order Status Updated",
			EventObject: trackingObject,
		})

		s.AddDataForReconciliation(context.Background(), &dto.AddReconciliationRequest{
			OrderID: orderID,
			Data: []dto.StatusTimeStamp{
				dto.StatusTimeStamp{
					TimeStamp:   time.Now().UnixMilli(),
					OrderStatus: "order_returned",
				},
			},
			Service: source,
		})
	}(s.Mixpanel, request.UserID, orderid64, int(orderDetails.GetOrderValue()), s, request.Data.Explanation, request.Data.Email, source, orderInfo.Seller, orderStatuses)

	response = &dto.OrderReturnedResponse{
		Data: dto.OrderReturnedData{
			OrderID: orderID,
			Message: "Order has been marked returned",
		},
	}
	return

}
