package service

import (
	"context"
	"kc/internal/ondc/models/dto"
	ordervalue "kc/internal/ondc/service/orderBill/orderValue"
	"strings"
)

func (s *Service) GetUserSellerLevelOrderStats(ctx context.Context, req *dto.SellerLevelOrderStats) (*dto.GetUserPlacedOrderCountResponse, error) {

	// var showWidget bool
	// widgetViewCountChannel := make(chan map[string]int)
	// s.GetAllWidgetCountsAsync(ctx, req.UserId, widgetViewCountChannel)

	// orderPlaced, sellerCount, err := ordervalue.GetUserSellerLevelPlacedOrderCount(req.UserId)

	// if err != nil {
	// 	return nil, err
	// }

	// widgetViewsCount := <-widgetViewCountChannel

	// if orderPlaced == 0 && getWidgetViewCountFromId("3784", widgetViewsCount) < 2 {
	// 	showWidget = true
	// } else {
	// 	showWidget = false
	// }

	return &dto.GetUserPlacedOrderCountResponse{OrdersCount: map[string]int{}, ShowWidget: false}, nil
}

func (s *Service) ResolveWidgetAccordingToOrderCount(ctx context.Context, userId string) (*dto.ResolveWidgetAccordingToOrderCountResponse, error) {

	// orderPlaced, _, err := ordervalue.GetUserSellerLevelPlacedOrderCount(userId)
	// if err != nil {
	// 	return nil, err
	// }

	// widgets := map[string]interface{}{}

	// if orderPlaced > 0 {
	// 	//s.IncrementWidgetViewCount(ctx, userId, "3784", &duration)
	// 	widgets = RSB_DHANYAWWAD_WIDGET
	// 	return &dto.ResolveWidgetAccordingToOrderCountResponse{Result: widgets}, nil
	// }

	// //s.IncrementWidgetViewCount(ctx, userId, "3784", &duration)

	// widgets = RSB_WHATSAPP_WIDGET

	return &dto.ResolveWidgetAccordingToOrderCountResponse{Result: map[string]any{}}, nil
}

func (s *Service) GetUserSellerLevelOrderStatsFork(ctx context.Context, req *dto.SellerLevelOrderStats) (*dto.SellerWiseConfirmedOrderCount, error) {

	count, err := ordervalue.GetSellerLevelOrderCountForJune(req.UserId)

	if err != nil {
		return nil, err
	}

	confirmedOrders := transformToSellerWiseConfirmedOrders(count)

	return &dto.SellerWiseConfirmedOrderCount{Orders: confirmedOrders}, nil
}

func transformToSellerWiseConfirmedOrders(count map[string]int) []dto.SellerWiseConfirmedOrderCountInfo {
	sellerMap := make(map[string]*dto.SellerWiseConfirmedOrderCountInfo)

	// Process each key-value pair from the map
	for key, value := range count {
		if strings.Contains(key, "::") {
			parts := strings.Split(key, "::")
			if len(parts) == 2 {
				seller := parts[0]
				status := parts[1]

				// Initialize seller entry if it doesn't exist
				if _, exists := sellerMap[seller]; !exists {
					sellerMap[seller] = &dto.SellerWiseConfirmedOrderCountInfo{
						Seller:    seller,
						Confirmed: 0,
						Placed:    0,
					}
				}

				// Set the appropriate field based on the status
				switch status {
				case "confirmed":
					sellerMap[seller].Confirmed = value
				case "placed":
					sellerMap[seller].Placed = value
				}
			}
		}
	}

	// Convert map to slice, filtering out sellers with both confirmed and placed as 0
	var result []dto.SellerWiseConfirmedOrderCountInfo
	for _, sellerData := range sellerMap {
		// Only include if either confirmed or placed is greater than 0
		if sellerData.Confirmed > 0 || sellerData.Placed > 0 {
			result = append(result, *sellerData)
		}
	}

	return result
}
