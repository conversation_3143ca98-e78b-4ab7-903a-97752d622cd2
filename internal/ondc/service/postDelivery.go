package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/exotel/ivr"
	"kc/internal/ondc/external/whatsapp"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	postdelivery "kc/internal/ondc/service/postDelivery"
	"slices"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/mixpanel/mixpanel-go"
	"gorm.io/datatypes"
)

type PostDeliveryRating struct {
	CourierName  string         `gorm:"courier"`
	Seller       string         `gorm:"seller"`
	OrderDetails datatypes.JSON `gorm:"order_details"`
	UserId       string         `gorm:"user_id"`
}

func (s *Service) SubmitDeliveryRating(ctx *gin.Context, req *dto.SubmitDeliveryRatingRequest) (interface{}, error) {

	if postdelivery.Contains(req.OrderId) {
		return nil, fmt.Errorf("rating already provided")
	}

	if req.Rating < 1 || req.Rating > 5 {
		return nil, fmt.Errorf("rating should be between 1 and 5")
	}

	postDeliveryRating := new(PostDeliveryRating)

	query := fmt.Sprintf(`
		select
		ko.user_id,
		kod.order_details,
		ko.seller,
		kos.courier as courier
		from
			kiranaclubdb.kiranabazar_order_details kod
		join kiranaclubdb.kiranabazar_orders ko on
			kod.order_id = ko.id
		join kiranaclubdb.kiranabazar_order_status kos on
			kos.id = kod.order_id
		where kod.order_id = %d
	`, req.OrderId)

	_, err := s.repository.CustomQuery(postDeliveryRating, query)

	if err != nil {
		return nil, err
	}

	orderDetails := dao.KiranaBazarOrderDetails{}
	err = json.Unmarshal(postDeliveryRating.OrderDetails, &orderDetails)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling order details")
	}

	eventObject := map[string]interface{}{
		"order_id":     req.OrderId,
		"rating":       req.Rating,
		"order_value":  orderDetails.GetOrderValue(),
		"courier_name": postDeliveryRating.CourierName,
		"seller":       postDeliveryRating.Seller,
		"user_id":      postDeliveryRating.UserId,
	}

	if slices.Contains([]int{1, 2}, req.Rating) {
		go s.triggerWhatsAppAndIVRforDeliveryRating(postDeliveryRating.UserId, int64(req.OrderId), postDeliveryRating.Seller, orderDetails)
	}

	if req.Reason != "" {
		eventObject["reason"] = req.Reason
	}

	s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
		s.Mixpanel.NewEvent("Feedback on Delivered Order", postDeliveryRating.UserId, eventObject),
	})

	webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{postDeliveryRating.UserId},
		EventName:   "Feedback on Delivered Order",
		EventObject: eventObject,
	})

	postdelivery.Add(req.OrderId)
	return "success", nil
}

func (s *Service) GetDeliveryRatingStatus(ctx *gin.Context, req *dto.GetDeliveryRatingRequest) (interface{}, error) {
	hasRating := postdelivery.Contains(req.OrderId)

	return dto.GetDeliveryRatingResponse{
		OrderId:        req.OrderId,
		RatingProvided: hasRating,
	}, nil
}

func (s *Service) triggerWhatsAppAndIVRforDeliveryRating(userId string, orderId int64, seller string, orderDetails dao.KiranaBazarOrderDetails) error {

	shippingAddress := orderDetails.ShippingAddress

	if (shippingAddress.Phone != nil && *shippingAddress.Phone == "") || shippingAddress.Phone == nil {
		return fmt.Errorf("phone number not available")
	}

	err := whatsapp.SendWhatsAppForTemplate(*shippingAddress.Phone, "help_0207")
	if err != nil {
		return err
	}
	s.addDataToIVRQueue(orderId, seller, 0, 0, 1, userId, *shippingAddress.Phone, 1011440, time.Now().Add(time.Second*10), ivr.IVR_REQUEST_TYPES.ORDER_BAD_DELIVERY)
	return nil
}
