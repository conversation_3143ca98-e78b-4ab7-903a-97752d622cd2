package service

import (
	"context"
	"errors"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/external/whatsapp"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/queue"
	queueModals "kc/internal/ondc/queue/models"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	"kc/internal/ondc/utils"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/mixpanel/mixpanel-go"
)

func (s *Service) createAutoCancelQueueData(orderId int64) map[string]interface{} {
	mp := make(map[string]interface{})
	queueId := uuid.NewString() + "KC"
	mp["queue_id"] = queueId
	mp["order_id"] = strconv.FormatInt(orderId, 10)
	return mp
}

func (s *Service) createConditionalAutoCancelQueueFunc(sqlRepo *sqlRepo.Repository, mixpanelRepo *mixpanelRepo.Repository, queueData interface{}) error {

	dataMap, ok := queueData.(map[string]interface{})
	if !ok {
		return fmt.Errorf("queueData is not a map[string]interface{}")
	}

	orderId, exist := dataMap["order_id"].(string)
	if !exist || orderId == "" {
		return fmt.Errorf("order_id key does not exist in data")
	}

	queueId, exist := dataMap["queue_id"].(string)
	if !exist || queueId == "" {
		return fmt.Errorf("queue_id key does not exist in data")
	}

	orderIdInt64, err := strconv.ParseInt(orderId, 10, 64)
	if err != nil {
		return fmt.Errorf("failed to parse order_id %s to int64: %w", orderId, err)
	}

	orderInfo, err := GetOrderInfo(sqlRepo, orderIdInt64)

	if err != nil {
		return fmt.Errorf("failed to get order info for order_id %s: %w", orderId, err)
	}

	orderDetails, err := GetOrderDetails(sqlRepo, orderId)

	if err != nil {
		return fmt.Errorf("failed to get order details for order_id %s: %w", orderId, err)
	}

	if includes([]string{displaystatus.PENDING_CONFIRMATION, displaystatus.PLACED}, orderInfo.DisplayStatus) {
		err := s.autoCancelOrder(orderInfo.UserID, orderId)

		if err != nil {
			return fmt.Errorf("failed to cancel order %s: %w", orderId, err)
		}

		mixpanelRepo.Track(context.Background(), []*mixpanel.Event{
			mixpanelRepo.NewEvent("Order Auto Cancelled", *orderInfo.UserID, map[string]interface{}{
				"user_id":         *orderInfo.UserID,
				"order_id":        orderId,
				"seller":          orderInfo.Seller,
				"order_value":     orderDetails.GetOrderValueV2(),
				"order_status":    orderInfo.DisplayStatus,
				"order_placed_at": orderInfo.CreatedAt,
			}),
		})

		s.UpdateQueueTaskStatus(sqlRepo, queueId, "COMPLETED")
		return nil
	}

	s.UpdateQueueTaskStatus(sqlRepo, queueId, "IGNORED")

	return nil
}

func (s *Service) createQueueModalDataforNonConfirmedOrder(orderId *int64) *queueModals.QueueInsertParams {

	if orderId == nil {
		slack.SendSlackMessage("Order ID is nil in createQueueModalDataforNonConfirmedOrder")
		return nil
	}

	queueData := s.createAutoCancelQueueData(*orderId)

	callBackFunc := func() error {
		s.createConditionalAutoCancelQueueFunc(s.repository, s.Mixpanel, queueData)
		return nil
	}

	queueModalData := &queueModals.QueueInsertParams{
		Data:             queueData,
		ShouldAdjustTime: false,
		TimeAdjustConfig: nil,
		TriggerAt:        time.Now().Add(time.Duration(5 * 24 * time.Hour)),
		TriggerFunction:  queue.QUEUE_TRIGGER_FUNCTION_TYPES.AUTO_CANCEL_NONCONFIRMED_ORDER,
		TriggerFunc:      callBackFunc,
		QueueID:          utils.StrPtr(queueData["queue_id"].(string)),
		CreateInDb:       true,
	}

	return queueModalData
}

func (s *Service) autoCancelOrder(userId *string, orderId string) error {
	// Input validation
	if userId == nil || *userId == "" {
		return errors.New("user ID cannot be nil or empty")
	}
	if orderId == "" {
		return errors.New("order ID cannot be empty")
	}

	_, err := s.CancelKiranaBazarOrder(context.Background(), dto.AppCancelKiranaBazarOrderRequest{
		UserID: *userId,
		Data: dto.AppCancelKiranaBazarOrderData{
			OrderID: orderId,
			Message: "Order auto cancelled due to non-confirmation",
			Reason:  "Order auto cancelled due to non-confirmation",
			Email:   "AUTO-CANCELLED",
			Source:  "AUTO_CANCEL",
		},
	})

	return err
}

func sendWhatsAppForNonConfirmedOrders(orderInfo dao.KiranaBazarOrder, orderDetails *dao.KiranaBazarOrderDetails) error {
	// Validate input parameters
	if orderDetails == nil {
		return fmt.Errorf("orderDetails cannot be nil")
	}

	if orderDetails.ShippingAddress.Phone == nil {
		return fmt.Errorf("phone number cannot be nil")
	}

	if orderInfo.ID == nil {
		return fmt.Errorf("order ID cannot be nil")
	}

	// Validate phone number format (basic validation)
	phoneNumber := strings.TrimSpace(*orderDetails.ShippingAddress.Phone)
	if phoneNumber == "" {
		return fmt.Errorf("phone number cannot be empty")
	}

	// Validate seller information
	if strings.TrimSpace(orderInfo.Seller) == "" {
		return fmt.Errorf("seller cannot be empty")
	}

	// Safely convert order ID to string
	orderIdInStr := strconv.FormatInt(*orderInfo.ID, 10)
	if orderIdInStr == "" {
		return fmt.Errorf("failed to convert order ID to string")
	}

	// Get order value safely
	orderValue := orderDetails.GetOrderValue()
	if orderValue < 0 {
		return fmt.Errorf("order value cannot be negative")
	}

	// Build dynamic link configurations
	dynamicLinkConfigs := []whatsapp.DynamicLinkConfig{
		{
			EmbeddingType: "button",
			ButtonIndex:   "0",
			Data: map[string]interface{}{
				"cta": map[string]interface{}{
					"name":     "WebViewOld",
					"nav_type": "Redirect to WebviewOld",
					"params": map[string]interface{}{
						"screenTitle": "",
						"showHeader":  false,
						"uri":         fmt.Sprintf("https://kcbazar.retailpulse.ai/orderDetails?id=%s&seller=%s", orderIdInStr, orderInfo.Seller),
					},
				},
				"non_firebase_link": false,
			},
		},
		{
			EmbeddingType: "button",
			ButtonIndex:   "1",
			Data: map[string]interface{}{
				"cta": map[string]interface{}{
					"name":     "WebViewOld",
					"nav_type": "Redirect to WebviewOld",
					"params": map[string]interface{}{
						"screenTitle": "",
						"showHeader":  false,
						"uri":         fmt.Sprintf("https://kcbazar.retailpulse.ai/orderDetails?id=%s&seller=%s", orderIdInStr, orderInfo.Seller),
					},
				},
				"non_firebase_link": false,
			},
		},
	}

	// Prepare message body with safe formatting
	messageBody := []string{fmt.Sprintf("(ID - %s: ₹%.2f)", orderIdInStr, math.Ceil(orderValue))}

	// Prepare header (empty string array, but safe)
	messageHeader := []string{""}

	// Send WhatsApp message
	err := whatsapp.SendGenericWhatsAppMessageWithDynamicLinkFRFR(
		"order_0807",
		phoneNumber,
		messageBody,
		messageHeader,
		dynamicLinkConfigs,
	)

	if err != nil {
		// Log the error with more context
		return fmt.Errorf("failed to send whatsapp message for order %s to phone %s: %w",
			orderIdInStr, phoneNumber, err)
	}

	return nil
}
