package service

import (
    "context"
    "encoding/json"
    "fmt"
    "kc/internal/ondc/models/dto"
    userdetails "kc/internal/ondc/service/userDetails"
    "kc/internal/ondc/utils"
    "sync"
    "time"
)


// GetScreenWidgetsAsync asynchronously fetches screen widgets and returns the result via channel
// It provides proper error handling, timeout management, and graceful cancellation
func (s *Service) GetScreenWidgetsAsync(ctx context.Context, request *dto.WidgetsRequest, resultChannel chan<- dto.WidgetsResult) {
    // Ensure channel is always written to, even on early returns
    defer func() {
        if r := recover(); r != nil {
            resultChannel <- dto.WidgetsResult{
                Widgets: nil,
                Error:   fmt.Errorf("panic in GetScreenWidgetsAsync: %v", r),
            }
        }
    }()

    // Validate request
    if err := request.Validate(); err != nil {
        resultChannel <- dto.WidgetsResult{
            Widgets: nil,
            Error:   fmt.<PERSON><PERSON><PERSON>("validation failed: %w", err),
        }
        return
    }

    // Create a context with timeout for the async operation
    timeoutCtx, cancel := context.WithTimeout(ctx, 2*time.Second)
    defer cancel()

    // Use goroutine to make it truly async
    go func() {
        defer close(resultChannel)
        
        // Call the synchronous version with timeout context
        widgets, err := s.GetScreenWidgets(timeoutCtx, request)
        
        // Handle context cancellation
        select {
        case <-timeoutCtx.Done():
            resultChannel <- dto.WidgetsResult{
                Widgets: nil,
                Error:   fmt.Errorf("operation cancelled or timed out: %w", timeoutCtx.Err()),
            }
        default:
            if err != nil {
                resultChannel <- dto.WidgetsResult{
                    Widgets: nil,
                    Error:   fmt.Errorf("failed to fetch widgets: %w", err),
                }
            } else {
                // Type assertion to ensure we have the right type
                if widgetSlice, ok := widgets.([]dto.Widget); ok {
                    resultChannel <- dto.WidgetsResult{
                        Widgets: widgetSlice,
                        Error:   nil,
                    }
                } else {
                    resultChannel <- dto.WidgetsResult{
                        Widgets: nil,
                        Error:   fmt.Errorf("unexpected widget type returned"),
                    }
                }
            }
        }
    }()
}

// GetScreenWidgets is an improved version of GetScreenWidgets with better error handling and context support
func (s *Service) GetScreenWidgets(ctx context.Context, request *dto.WidgetsRequest) (interface{}, error) {
	// Extract parameters from request
	screenName := request.ScreenName
	userId := request.UserID
	appVersion := request.AppVersion
    // Create structured logging helper
    logCtx := map[string]interface{}{
        "screen_name": screenName,
        "user_id":     userId,
        "app_version": appVersion,
    }

    // Validate inputs
    if screenName == "" || userId == "" {
        return nil, fmt.Errorf("screen_name and user_id are required parameters")
    }

    // Start async user details fetch with timeout
    userDetailsChannel := userdetails.AsyncFetchUserDetails(
        userId, 
        []string{userdetails.USER_DETAILS_TYPES.USER_GEOGRAPHY}, 
        1*time.Second, // Increased timeout for better reliability
    )

    // Fetch screen details with error context
    screenDetails, err := s.GetScreenDetails(ctx, screenName, userId)
    if err != nil {
        return nil, fmt.Errorf("failed to get screen details for screen '%s': %w", screenName, err)
    }
    if screenDetails == nil {
        return nil, fmt.Errorf("screen details not found for screen: %s", screenName)
    }

    // Wait for user details with timeout handling
    var userDetails userdetails.AsyncResult
    select {
    case userDetails = <-userDetailsChannel:
        if userDetails.Data == nil {
            return nil, fmt.Errorf("user details not found for user: %s", userId)
        }
    case <-ctx.Done():
        return nil, fmt.Errorf("context cancelled while fetching user details: %w", ctx.Err())
    case <-time.After(6 * time.Second): // Slightly longer than user details timeout
        return nil, fmt.Errorf("timeout while fetching user details for user: %s", userId)
    }

    // Build request object with validation
    reqObject := dto.GetScreenDetailsRequest{
        Uid:             userId,
        WidgetIndexes:   screenDetails.Widgets,
        State:           userDetails.Data.UserGeography.GeoState,
        District:        userDetails.Data.UserGeography.GeoDistrict,
        GeoCodedCluster: userDetails.Data.UserGeography.GeoCodedCluster,
        AppVersion:      appVersion,
    }

    // Add request validation
    if err := s.validateScreenDetailsRequest(&reqObject); err != nil {
        return nil, fmt.Errorf("invalid screen details request: %w", err)
    }

    // Make API call with retries
    apiResponse, statusCode, err := utils.CallExternalAPI(utils.WIDGET_RESOLVER_API, "POST", reqObject, nil)
    if err != nil {
        return nil, fmt.Errorf("error in fetching screen details: %w", err)
    }
    if statusCode == nil || *statusCode != 200 {
        return nil, fmt.Errorf("widget resolver API returned status %v: %s", statusCode, string(apiResponse))
    }

    // Parse API response
    apiResponseData := dto.GetScreenDetailsResponse{}
    if err := json.Unmarshal(apiResponse, &apiResponseData); err != nil {
        return nil, fmt.Errorf("error in unmarshalling screen details response: %w", err)
    }

    // Filter and validate widgets
    filteredWidgets := s.filterAndValidateWidgets(apiResponseData.Result)
    
    // Log success with metrics
    s.logWidgetFetchSuccess(logCtx, len(filteredWidgets))
    
    return filteredWidgets, nil
}

// validateScreenDetailsRequest validates the screen details request
func (s *Service) validateScreenDetailsRequest(req *dto.GetScreenDetailsRequest) error {
    if req.Uid == "" {
        return fmt.Errorf("user ID is required")
    }
    if len(req.WidgetIndexes) == 0 {
        return fmt.Errorf("widget indexes are required")
    }
    return nil
}

// filterAndValidateWidgets filters out nil widgets and validates the remaining ones
func (s *Service) filterAndValidateWidgets(widgets []dto.Widget) []dto.Widget {
    filteredWidgets := make([]dto.Widget, 0, len(widgets))
    
    for _, widget := range widgets {
        if widget != nil && s.isValidWidget(widget) {
            filteredWidgets = append(filteredWidgets, widget)
        }
    }
    
    return filteredWidgets
}

// isValidWidget validates individual widget data
func (s *Service) isValidWidget(widget dto.Widget) bool {
    // Add widget validation logic here
    // This is a placeholder - implement based on your Widget interface
    return widget != nil
}

// logWidgetFetchSuccess logs successful widget fetching with metrics
func (s *Service) logWidgetFetchSuccess(logCtx map[string]interface{}, widgetCount int) {
    // Implement your logging here
    // Example: log.InfoWithFields("Successfully fetched widgets", map[string]interface{}{
    //     "screen_name": logCtx["screen_name"],
    //     "user_id": logCtx["user_id"],
    //     "widget_count": widgetCount,
    // })
}

// BatchGetScreenWidgets fetches widgets for multiple screens concurrently
func (s *Service) BatchGetScreenWidgets(ctx context.Context, requests []dto.WidgetsRequest) map[string]dto.WidgetsResult {
    results := make(map[string]dto.WidgetsResult)
    resultsMutex := sync.RWMutex{}
    
    // Use worker pool pattern for controlled concurrency
    const maxWorkers = 10
    semaphore := make(chan struct{}, maxWorkers)
    
    var wg sync.WaitGroup
    
    for _, request := range requests {
        wg.Add(1)
        go func(req dto.WidgetsRequest) {
            defer wg.Done()
            
            // Acquire semaphore
            semaphore <- struct{}{}
            defer func() { <-semaphore }()
            
            // Create result channel
            resultChannel := make(chan dto.WidgetsResult, 1)
            
            // Fetch widgets asynchronously
            s.GetScreenWidgetsAsync(ctx, &req, resultChannel)
            
            // Wait for result
            result := <-resultChannel
            
            // Store result safely
            resultsMutex.Lock()
            results[fmt.Sprintf("%s_%s", req.ScreenName, req.UserID)] = result
            resultsMutex.Unlock()
        }(request)
    }
    
    wg.Wait()
    return results
}