package service

import (
	"bytes"
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/orderStatus/constants"
	"kc/internal/ondc/utils"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
)

func GetOrderFromWaybill(repo *sqlRepo.Repository, wayBill string) (*dao.KiranaBazarOrderStatus, error) {
	conditions := map[string]interface{}{
		"awb_number": wayBill,
	}
	data := &dao.KiranaBazarOrderStatus{}
	_, err := repo.Find(conditions, data)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (s *Service) SubmitBulkData(ctx context.Context, request *dto.UpdateBulkDataRequest) (*dto.SubmitBulkDataResponse, error) {
	asyncCtx := context.Background()
	go func() {
		err := s.UpdateBulkData(asyncCtx, request)
		if err != nil {
			// Log the error since we can't return it
			log.Printf("Async UpdateBulkData failed: %v", err)
			return
		}
		// Log success if needed
	}()
	return &dto.SubmitBulkDataResponse{Message: "Data Submitted Successfully."}, nil

}

func (s *Service) UpdateBulkData(ctx context.Context, request *dto.UpdateBulkDataRequest) error {
	// Create a new CSV reader from the string content
	reader := csv.NewReader(strings.NewReader(request.Data.Data))
	var csvResponse dto.UpdateBulkDataResponse
	// Read the header row
	header, err := reader.Read()
	if err != nil {
		errorString := fmt.Sprintf("error reading header: %v", err)
		return fmt.Errorf(errorString)
	}

	// Create a map to find column indices
	headerMap := make(map[string]int)
	for i, h := range header {
		headerMap[h] = i
	}

	// Verify required columns exist
	for _, col := range utils.NDRRequiredColumns {
		if _, exists := headerMap[col]; !exists {
			errorString := fmt.Sprintf("required column '%s' not found in CSV", col)
			return fmt.Errorf(errorString)
		}
	}

	// Store all rows and processing results
	var responseData []dto.ProcessedCSV
	// Process each row (first iteration)
	for {
		row, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Printf("Error reading row: %v", err)
			continue
		}

		rowMap := make(map[string]string)
		for i, value := range row {
			if i < len(header) {
				rowMap[header[i]] = value
			}
		}
		result := dto.ProcessedCSV{
			OriginalData: row,
			Success:      false,
		}

		var response *dto.ProcessedCSV
		if request.Data.Type == "NDR" {
			response, err = s.ProcessNDR(ctx, rowMap, &request.Data.UpdatedBy, &request.Data.Seller)
		} else if request.Data.Type == "AWB" {
		}
		if err != nil {
			csvResponse.Data.Failed += 1
		} else {
			csvResponse.Data.Success += 1
		}
		result.Error = response.Error
		result.Response = response.Response
		result.Success = response.Success
		responseData = append(responseData, result)
		csvResponse.Data.TotalRecords += 1
	}

	logID := uuid.New().String()
	requestURl, responseURl, err := s.CreateAndUploadCSVs(ctx, header, responseData, logID)
	if err != nil {
		errorString := fmt.Sprintf("error creating and uploading CSVs: %v", err)
		return fmt.Errorf(errorString)
	}

	csvResponse.Data.ResponseCsvUrl = *responseURl
	csvResponse.Data.RequestCsvUrl = *requestURl
	jsonBytes, err := json.Marshal(csvResponse)
	if err != nil {
		errorString := fmt.Sprintf("error marshalling response: %v", err)
		return fmt.Errorf(errorString)
	}
	successStatus := "SUCCESS"
	_, err = s.repository.Create(dao.KiranaBazarBulkActionLogs{
		ID:        &logID,
		UpdatedAt: &request.Data.UpdatedAt,
		UpdatedBy: &request.UpdatedBy,
		Seller:    &request.Data.Seller,
		Type:      &request.Data.Type,
		Meta:      jsonBytes,
		Status:    &successStatus,
	})
	if err != nil {
		errorString := fmt.Sprintf("error creating bulk action log: %v", err)
		return fmt.Errorf(errorString)
	}

	return nil
}

func (s *Service) ProcessNDR(ctx context.Context, rowMap map[string]string, updatedBy *string, seller *string) (*dto.ProcessedCSV, error) {
	// Extract values from the row
	waybill := strings.TrimSpace(rowMap["Waybill"])
	remarks := rowMap["Remarks"]
	var result dto.ProcessedCSV

	// Parse attempt count
	attemptCount, err := strconv.Atoi(strings.TrimSpace(rowMap["Attempt count"]))
	if err != nil {
		result.Error = fmt.Sprintf("Warning: Invalid attempt count for Waybill %s: %v", waybill, err)
		return &result, fmt.Errorf(result.Error)
	}
	kos, err := GetOrderFromWaybill(s.repository, waybill)
	if err != nil {
		result.Error = fmt.Sprintf("Not Able to find order for Waybill %s: %v", waybill, err)
		return &result, fmt.Errorf(result.Error)
	}
	ko, err := GetOrderInfo(s.repository, *kos.ID)
	if err != nil {
		result.Error = fmt.Sprintf("Not Able to find order for Waybill in kiranabazar_orders %s: %v", waybill, err)
		return &result, fmt.Errorf(result.Error)
	}
	if ko.Seller != *seller {
		result.Error = fmt.Sprintf("Seller mismatch for Waybill %s: %v", waybill, err)
		return &result, fmt.Errorf(result.Error)
	}

	if ko.DisplayStatus == constants.DELIVERED {
		result.Error = fmt.Sprintf("Order already delivered for Waybill %s", waybill)
		return &result, fmt.Errorf(result.Error)
	}

	// Construct the request
	orderStatus := constants.NDR + fmt.Sprintf(`%d`, attemptCount)
	updateB2BRequest := &dto.UpdateB2BOrderStatusRequest{
		UpdatedBy: *updatedBy,
		Data: dto.UpdateB2BOrderStatusData{
			OrderID:        fmt.Sprintf(`%d`, *kos.ID),
			UpdatedBy:      *updatedBy,
			OrderStatus:    orderStatus,
			UpdatedAt:      time.Now().UnixMilli(),
			OrderingModule: ko.Seller,
			Source:         "CSV",
			OrderMeta: dto.OrderMeta{
				Note:           remarks,
				NDRAttempCount: attemptCount,
			},
		},
	}
	response, err := s.UpdateB2BOrderStatus(ctx, updateB2BRequest)
	if err != nil {
		result.Error = fmt.Sprintf("Error updating order status for Waybill %s: %v", waybill, err)
		return &result, fmt.Errorf(result.Error)
	}
	result.Success = true
	result.Response = fmt.Sprintf("%+v", response) // Convert response to string
	return &result, nil
}

func (s *Service) CreateAndUploadCSVs(
	ctx context.Context,
	originalHeaders []string,
	responseData []dto.ProcessedCSV,
	logID string) (*string, *string, error) {

	var responseBuf bytes.Buffer
	responseWriter := csv.NewWriter(&responseBuf)
	var requestBuf bytes.Buffer
	requestWriter := csv.NewWriter(&requestBuf)

	responseHeaders := append(append([]string{}, originalHeaders...), "Response", "Error", "Success")

	if err := requestWriter.Write(originalHeaders); err != nil {
		return nil, nil, fmt.Errorf("error writing request header: %w", err)
	}

	if err := responseWriter.Write(responseHeaders); err != nil {
		return nil, nil, fmt.Errorf("error writing response header: %w", err)
	}

	for _, row := range responseData {
		if err := requestWriter.Write(row.OriginalData); err != nil {
			return nil, nil, fmt.Errorf("error writing request row: %w", err)
		}

		responseRow := append(append([]string{}, row.OriginalData...),
			row.Response,
			row.Error,
			strconv.FormatBool(row.Success))

		if err := responseWriter.Write(responseRow); err != nil {
			return nil, nil, fmt.Errorf("error writing response row: %w", err)
		}
	}
	responseWriter.Flush()
	if err := responseWriter.Error(); err != nil {
		return nil, nil, fmt.Errorf("error flushing response writer: %w", err)
	}

	requestWriter.Flush()
	if err := requestWriter.Error(); err != nil {
		return nil, nil, fmt.Errorf("error flushing request writer: %w", err)
	}

	basePath := "bulk_actions/" + time.Now().Format("20060102") + "/" + logID
	requestCsvPath := basePath + "_request.csv"
	responseCsvPath := basePath + "_response.csv"

	var wg sync.WaitGroup
	wg.Add(2)

	errorCh := make(chan error, 2)

	go func() {
		defer wg.Done()
		_, err := utils.UploadCSVToBlob(ctx, requestBuf.Bytes(), requestCsvPath, "text/csv", "b2b")
		if err != nil {
			errorCh <- fmt.Errorf("error uploading request CSV: %w", err)
		}
	}()

	go func() {
		defer wg.Done()
		_, err := utils.UploadCSVToBlob(ctx, responseBuf.Bytes(), responseCsvPath, "text/csv", "b2b")
		if err != nil {
			errorCh <- fmt.Errorf("error uploading response CSV: %w", err)
		}
	}()

	wg.Wait()
	close(errorCh)

	for err := range errorCh {
		return nil, nil, err
	}
	requestURl := "https://d2rstorage2.blob.core.windows.net/b2b/" + requestCsvPath
	responseURl := "https://d2rstorage2.blob.core.windows.net/b2b/" + responseCsvPath
	return &requestURl, &responseURl, nil
}

func (s *Service) GetBulkActionLogs(ctx context.Context, request *dto.GetBulkActionLogsRequest) (*dto.GetBulkActionLogsResponse, error) {
	conditions := map[string]interface{}{}
	if request.Data.UpdatedBy != nil {
		conditions["updated_by"] = request.Data.UpdatedBy
	}

	bulkActionQuery := fmt.Sprintln("SELECT * FROM kiranabazar_bulk_action_logs kbal ")
	if request.Data.UpdatedBy != nil {
		bulkActionQuery += fmt.Sprintf("WHERE kbal.updated_by = '%s' and", *request.Data.UpdatedBy)
	}
	bulkActionQuery = strings.TrimSuffix(bulkActionQuery, "and")
	bulkActionQuery += fmt.Sprintf(" ORDER BY kbal.updated_at DESC LIMIT %d OFFSET %d", *request.Data.Limit, *request.Data.Offset)
	var data []dao.KiranaBazarBulkActionLogs
	_, err := s.repository.CustomQuery(&data, bulkActionQuery)
	if err != nil {
		return nil, err
	}

	type TotalResponse struct {
		TotalRecords int `json:"total_records"`
	}
	var totalResponse TotalResponse
	totalResponseQuery := fmt.Sprintln("SELECT COUNT(*) as total_records FROM kiranabazar_bulk_action_logs")
	if request.Data.UpdatedBy != nil {
		totalResponseQuery += fmt.Sprintf("WHERE updated_by = '%s'", *request.Data.UpdatedBy)
	}
	_, err = s.repository.CustomQuery(&totalResponse, totalResponseQuery)
	if err != nil {
		return nil, err
	}

	var response dto.GetBulkActionLogsResponse
	for _, log := range data {
		var meta dto.UpdateBulkDataResponse
		err := json.Unmarshal(log.Meta, &meta)
		if err != nil {
			return nil, err
		}
		message := "Data processed successfully."
		if meta.Error != nil {
			message = *meta.Error
		}
		response.Data = append(response.Data, dto.GetBulkActionLogsResponseData{
			ID:             *log.ID,
			UpdatedAt:      *log.UpdatedAt,
			UpdatedBy:      *log.UpdatedBy,
			Seller:         *log.Seller,
			Type:           *log.Type,
			TotalRecords:   meta.Data.TotalRecords,
			Success:        meta.Data.Success,
			Failed:         meta.Data.Failed,
			ResponseCsvUrl: meta.Data.ResponseCsvUrl,
			RequestCsvUrl:  meta.Data.RequestCsvUrl,
			Status:         log.Status,
			Message:        &message,
		})
	}
	response.Total = totalResponse.TotalRecords
	return &response, nil
}
