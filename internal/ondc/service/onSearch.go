package service

import (
	"context"
	"fmt"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"strconv"
)

func (s *Service) OnSearch(ctx context.Context, req *dto.OnSearchRequest) error {
	keyIdentifier := fmt.Sprintf("%s%s", *req.Context.TransactionID, *req.Context.MessageID)
	validVersion := "2.0.2"
	if req.Context.CoreVersion != nil && *req.Context.CoreVersion != validVersion {
		return fmt.Errorf("not valid version")
	}

	sellers := make([]*dto.SearchResponseSellers, 0)
	//reqInBytes, _ := json.Marshal(*req)
	//logger.Info(ctx, string(reqInBytes))
	for _, d := range req.Message.Catalog.BppProviders {
		// ImageURL := d.Descriptor.Images[0].Value
		ImageURL := d.Descriptor.Images[0].(map[string]interface{})["url"].(string)
		baseSellerDetails := &dto.SearchResponseSellers{
			ProviderID:  d.ID,
			Name:        d.Descriptor.Name,
			Type:        "grocery",
			ImageURL:    ImageURL,
			BppID:       req.Context.BppID,
			BppURL:      req.Context.BppURI,
			ProviderTTL: d.TTL,
			//RawResp:  string(reqInBytes),
		}
		items := make([]shared.SellerItems, 0)
		for _, item := range d.Items {
			imageUrls := item.Descriptor.Images[0].(map[string]interface{})["url"].(string)
			parsedQty := 0
			switch item.Quantity.Available.Count.(type) {
			case string:
				qty := item.Quantity.Available.Count.(string)
				parsedQty, _ = strconv.Atoi(qty)
			case int:
				qty := item.Quantity.Available.Count.(int)
				parsedQty = qty
			case float64:
				qty := item.Quantity.Available.Count.(float64)
				parsedQty = int(qty)
			}
			var rating *float64 = nil
			if item.Rating != 0 {
				ratingValue := float64(item.Rating)
				rating = &ratingValue
			}
			singleItem := shared.SellerItems{
				SellerItemsData: shared.SellerItemsData{
					Name:          item.Descriptor.Name,
					LocationIds:   item.LocationIds,
					CategoryIds:   item.CategoryIDs,
					ID:            item.ID,
					ParentItemID:  item.ParentItemID,
					Price:         *item.Price,
					Rating:        rating,
					FulfilmentIds: item.FulfillmentIds,
					Description:   &item.Descriptor.LongDesc,
					ImageUrls:     []string{imageUrls},
					Quantity:      int32(parsedQty),
				},
			}
			items = append(items, singleItem)
		}

		baseSellerDetails.Items = items

		for _, data := range d.Locations {
			baseSellerDetails.Address = fmt.Sprintf("%s, %s", data.Address, data.City.Name)
			baseSellerDetails.LocationID = data.ID
			sellers = append(sellers, baseSellerDetails)
		}

	}

	searchResponses := &dto.AppSearchResponses{
		Data: dto.AppSearchData{
			Sellers: sellers,
		},
		Meta: dto.Meta{
			Context: *req.Context,
		},
	}
	if req.Error != nil && req.Error.Code != nil {
		searchResponses.Error = dto.AppResponseError{
			Code:        req.Error.Code,
			Message:     &req.Error.Message,
			Description: &req.Error.Path,
			Type:        &req.Error.Type,
		}
	}

	err := s.Cache.Push(ctx, keyIdentifier, searchResponses)
	if err != nil {
		return err
	}
	return nil
}
