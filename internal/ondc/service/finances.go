package service

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/helper"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/service/kcFinances"
	kcFinancesDao "kc/internal/ondc/service/kcFinances/dao"
	"kc/internal/ondc/service/kcFinances/finance"
	"kc/internal/ondc/service/logistics/couriers"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	shipmentstatus "kc/internal/ondc/service/orderStatus/shipmentStatus"
	"kc/internal/ondc/service/products"
	"kc/internal/ondc/utils"
	"math"
	"os"
	"path/filepath"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
)

// Constants for payment types and providers
const (
	PaymentTypeCOD           = "COD"
	PaymentTypeOnline        = "ONLINE"
	PaymentGatewayRazorpay   = "Razorpay"
	DefaultShippingStatus    = displaystatus.IN_TRANSIT
	LogTagFinancials         = "order_financials"
	ErrMsgSellerInvoiceData  = "seller invoicing data is not available"
	ErrMsgSellerShippingData = "seller shipping data is not available"
	ErrMsgProductNotFound    = "product not found"
)

// PaymentMode represents different payment modes supported by the platform
type PaymentMode string

// PaymentMode constants
const (
	PaymentModeUPI     PaymentMode = "UPI"
	PaymentModeWallet  PaymentMode = "WALLET"
	PaymentModeNetbank PaymentMode = "NETBANK"
	PaymentModeCard    PaymentMode = "CARD"
	PaymentModeCOD     PaymentMode = "COD"
)

// OrderData encapsulates all the data related to an order
type OrderData struct {
	OrderInfo           dao.KiranaBazarOrder
	OrderDetails        *dao.KiranaBazarOrderDetails
	OrderPayment        dao.KiranaBazarOrderPayment
	OrderReconciliation dao.KiranaBazarReconciliation
	SellerInfo          dao.KiranaBazarSeller
	PackageDetails      dao.KiranabazarOrderPackageDetails
	OrderStatus         dao.KiranaBazarOrderStatus
}

// ProcessOrderFinancials handles the financial calculations and processing for an order
// It computes discounts, taxes, commissions and creates financial records for the order
func (s *Service) ProcessOrderFinancials(ctx context.Context, orderIdentifier string, kcShip bool) error {
	logger := logrus.WithContext(ctx).WithField("func", "ProcessOrderFinancials").WithField("orderIdentifier", orderIdentifier)
	logger.Info("Processing order financials")

	// Parse and validate order ID
	orderID, err := strconv.ParseInt(orderIdentifier, 10, 64)
	if err != nil {
		return errors.Wrap(err, "failed to parse order identifier")
	}

	// Retrieve all required order information
	orderData, err := s.fetchOrderData(ctx, orderID)
	if err != nil {
		return errors.Wrap(err, "failed to fetch order data")
	}

	// Extract shipping details and determine GST type (IGST or CGST+SGST)
	shippingDetails := orderData.SellerInfo.GetShippingDetails()
	isIGST := !strings.EqualFold(strings.ToUpper(shippingDetails.State), strings.ToUpper(*(orderData.OrderDetails.ShippingAddress.State)))

	// Process order discounts
	orderDiscounts, allDiscounts, err := s.calculateOrderDiscounts(ctx, orderData.OrderInfo, orderData.OrderDetails, orderData.OrderPayment, orderData.OrderReconciliation)
	if err != nil {
		return errors.Wrap(err, "failed to calculate order discounts")
	}

	// Process order item financials including taxes
	orderItemFinancials, taxFinances, err := s.CalculateOrderItemFinancials(ctx, orderData.OrderInfo, orderData.OrderDetails, orderData.OrderPayment, orderData.OrderReconciliation, isIGST, orderData.SellerInfo)
	if err != nil {
		return errors.Wrap(err, "failed to calculate order item financials")
	}

	// Calculate order-level financials
	orderFinancials, err := s.calculateOrderFinancials(ctx, orderData.OrderInfo, orderData.OrderDetails, orderData.OrderPayment, orderData.OrderReconciliation, orderData.SellerInfo, allDiscounts, kcShip, taxFinances, orderData.OrderStatus)
	if err != nil {
		return errors.Wrap(err, "failed to calculate order financials")
	}

	// Write order financials to CSV -- commented this to not to write to csv
	// orders := []kcFinancesDao.OrderFinancials{orderFinancials}
	// err = WriteOrderFinancialsToCSV("mayData.csv", orders)
	// if err != nil {
	// 	return errors.Wrap(err, "failed to write order financials to CSV")
	// }

	// Process free items, if any
	freeItems, err := s.calculateFreeItems(ctx, orderData.OrderInfo, orderData.OrderDetails, orderData.OrderPayment, orderData.OrderReconciliation)
	if err != nil {
		return errors.Wrap(err, "failed to calculate free items")
	}

	// Process shipment financials
	shipmentFinancials, err := s.calculateShipmentFinancials(ctx, orderData.OrderInfo, orderData.OrderDetails, orderData.OrderPayment, orderData.OrderReconciliation, orderData.OrderStatus, orderData.PackageDetails.GetPackageDetails(), shippingDetails)
	if err != nil {
		return errors.Wrap(err, "failed to calculate shipment financials")
	}

	// Create the financial request object and process it
	orderFinancialsRequest := &finance.OrderFinancialsRequest{
		OrderFinancials:     &orderFinancials,
		OrderItemFinancials: orderItemFinancials,
		OrderDiscounts:      orderDiscounts,
		FreeItems:           freeItems,
		ShipmentFinancials:  shipmentFinancials,
	}

	logger.Info("Submitting order financials for processing")
	err = s.FinancialService.ProcessOrderFinancials(ctx, orderFinancialsRequest)
	if err != nil {
		return errors.Wrap(err, "failed to process order financials")
	}

	logger.Info("Successfully processed order financials")
	return nil
}

func (s *Service) ProcessCreditNote(ctx context.Context, request *dto.CreditNoteRequest) (*kcFinancesDao.CreditNote, error) {
	logger := logrus.WithContext(ctx).WithField("func", "ProcessCreditNote").WithField("orderID", request.CreditNote.OrderID)
	logger.Info("Processing credit note")

	creditNoteType := request.CreditNote.CreditNoteType

	switch creditNoteType {
	case kcFinances.CREDIT_NOTE_TYPE.FULL_RETURN:
		return s.processReturnCreditNote(ctx, request)
	case kcFinances.CREDIT_NOTE_TYPE.MISSING_ITEMS:
		return s.processPartialItemsCreditNote(ctx, request, kcFinances.CREDIT_NOTE_TYPE.MISSING_ITEMS)
	case kcFinances.CREDIT_NOTE_TYPE.DAMAGED_ITEMS:
		return s.processPartialItemsCreditNote(ctx, request, kcFinances.CREDIT_NOTE_TYPE.DAMAGED_ITEMS)
	case kcFinances.CREDIT_NOTE_TYPE.CUSTOM:
		return s.processCustomCreditNote(ctx, request, kcFinances.CREDIT_NOTE_TYPE.CUSTOM)
	default:
		return nil, fmt.Errorf("unsupported credit note type: %s", creditNoteType)
	}
}

// processReturnCreditNote is the function to process full return credit note
func (s *Service) processReturnCreditNote(ctx context.Context, request *dto.CreditNoteRequest) (*kcFinancesDao.CreditNote, error) {
	logger := logrus.WithContext(ctx).WithField("func", "processReturnCreditNote").WithField("orderID", request.CreditNote.OrderID)
	logger.Info("Processing return credit note")

	orderID, err := strconv.ParseInt(request.CreditNote.OrderID, 10, 64)
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse order ID")
	}

	// Get the order items
	orderItems, err := s.FinancialService.GetOrderItemFinancialsByOrderID(ctx, uint(orderID))
	if err != nil {
		return nil, errors.Wrap(err, "failed to get order items")
	}
	for _, item := range orderItems {
		err = s.FinancialService.CreateReturn(ctx, &kcFinancesDao.ReturnsRefunds{
			OrderID:                    uint(orderID),
			OrderItemID:                uint(item.OrderItemID),
			Quantity:                   item.Quantity,
			ReturnType:                 "FULL_RETURN",
			ReturnAmount:               item.ItemTotal,
			RefundStatus:               "PROCESSED",
			SellerConfirmationReceived: false,
			ReturnInitiatedDate:        time.Now(),
			SellerConfirmationDate:     time.Now(),
			ReturnConfirmedDate:        time.Now(),
			RefundProcessedDate:        time.Now(),
			PaymentRefundID:            request.CreditNote.PaymentID,
		})
		if err != nil {
			return nil, errors.Wrap(err, "failed to create return")
		}

	}

	// Create the credit note
	creditNote, err := s.FinancialService.CreateCreditNoteForReturn(ctx, uint(orderID), "Full Return")
	if err != nil {
		return nil, errors.Wrap(err, "failed to create credit note")
	}

	return creditNote, nil
}

func (s *Service) processPartialItemsCreditNote(ctx context.Context, request *dto.CreditNoteRequest, creditNoteType string) (*kcFinancesDao.CreditNote, error) {
	logger := logrus.WithContext(ctx).WithField("func", "processMissingItemsCreditNote").WithField("orderID", request.CreditNote.OrderID)
	logger.Info("Processing missing items credit note")

	orderID, err := strconv.ParseInt(request.CreditNote.OrderID, 10, 64)
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse order ID")
	}

	orderItems, err := s.FinancialService.GetOrderItemFinancialsByOrderID(ctx, uint(orderID))
	if err != nil {
		return nil, errors.Wrap(err, "failed to get order items")
	}

	missingItemsSkus := []string{}
	missingItemsQuantity := make(map[string]int)
	for _, item := range request.CreditNoteItems {
		missingItemsSkus = append(missingItemsSkus, item.SKUID)

		if item.Quantity == 0 {
			missingItemsQuantity[item.SKUID] = 0
		} else {
			missingItemsQuantity[item.SKUID] = item.Quantity
		}
	}

	for _, item := range orderItems {
		if slices.Contains(missingItemsSkus, item.SKUID) {
			skuQuantity := item.Quantity
			if missingItemsQuantity[item.SKUID] != 0 {
				skuQuantity = missingItemsQuantity[item.SKUID]
			}
			err = s.FinancialService.CreateReturn(ctx, &kcFinancesDao.ReturnsRefunds{
				OrderID:                    uint(orderID),
				OrderItemID:                uint(item.OrderItemID),
				Quantity:                   skuQuantity,
				ReturnType:                 creditNoteType,
				ReturnAmount:               (item.ItemTotal / float64(item.Quantity)) * float64(skuQuantity),
				RefundStatus:               "PROCESSED",
				SellerConfirmationReceived: false,
				ReturnInitiatedDate:        time.Now(),
				ReturnConfirmedDate:        time.Now(),
				SellerConfirmationDate:     time.Now(),
				RefundProcessedDate:        time.Now(),
				PaymentRefundID:            request.CreditNote.PaymentID,
			})

			if err != nil {
				return nil, errors.Wrap(err, "failed to create return")
			}
		}
	}

	creditNote, err := s.FinancialService.CreateCreditNoteForReturn(ctx, uint(orderID), "Missing Items")
	if err != nil {
		return nil, errors.Wrap(err, "failed to create credit note")
	}

	return creditNote, nil
}

func (s *Service) processCustomCreditNote(ctx context.Context, request *dto.CreditNoteRequest, creditNoteType string) (*kcFinancesDao.CreditNote, error) {
	logger := logrus.WithContext(ctx).WithField("func", "processCustomCreditNote").WithField("orderID", request.CreditNote.OrderID)
	logger.Info("Processing custom credit note")

	orderID, err := strconv.ParseInt(request.CreditNote.OrderID, 10, 64)
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse order ID")
	}

	financialValue, err := s.FinancialService.GetOrderFinancialsByOrderID(ctx, uint(orderID))
	if err != nil {
		return nil, errors.Wrap(err, "failed to get order financials")
	}

	creditNumber, err := s.FinancialService.GetCreditNoteNumber(uint64(orderID), financialValue.InvoiceNumber)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get credit note number")
	}

	// Create the credit note
	creditNote, err := s.FinancialService.CreateCreditNote(ctx, &kcFinancesDao.CreditNote{
		CreditNoteNumber:     creditNumber,
		CreditNoteDate:       time.Now(),
		RelatedInvoiceNumber: financialValue.InvoiceNumber,
		RelatedInvoiceDate:   financialValue.InvoiceDate,
		SellerID:             uint(financialValue.SupplierID),
		BuyerID:              financialValue.RetailerID,
		OrderID:              uint(orderID),
		CreditNoteReason:     "Custom Credit Note",
		CreditNoteType:       creditNoteType,

		TotalValueBeforeTax:  request.CreditNote.Amount,
		CGSTAmount:           0,
		SGSTAmount:           0,
		IGSTAmount:           0,
		TotalCreditNoteValue: request.CreditNote.Amount,

		AdjustmentStatus: "PENDING",
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}, nil)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create credit note")
	}
	return creditNote, nil
}

// fetchOrderData retrieves all necessary data for processing order financials
func (s *Service) fetchOrderData(ctx context.Context, orderID int64) (*OrderData, error) {
	orderIDStr := strconv.FormatInt(orderID, 10)
	logger := logrus.WithContext(ctx).WithField("orderID", orderID)

	// Initialize the result structure
	result := &OrderData{}

	// Get order info
	orderInfo, err := GetOrderInfo(s.repository, orderID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get order info")
	}
	result.OrderInfo = orderInfo

	// Get order payment
	orderPayment, err := GetOrderPayment(s.repository, orderIDStr)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get order payment")
	}
	result.OrderPayment = orderPayment

	// Get order details
	orderDetails, err := GetOrderDetails(s.repository, orderIDStr)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get order details")
	}
	result.OrderDetails = orderDetails

	// Get order reconciliation
	orderReconciliation, err := GetOrderReconciliation(s.repository, orderID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get order reconciliation")
	}
	result.OrderReconciliation = orderReconciliation

	// Get seller info
	sellerInfo, err := GetSellerInfo(s.repository, orderInfo.Seller)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get seller info")
	}
	result.SellerInfo = sellerInfo

	// Get package details (not critical if this fails)
	packageDetails, err := GetPackageDetails(s.repository, orderID)
	if err != nil {
		logger.WithError(err).Warn("failed to get package details, proceeding with empty details")
		packageDetails = dao.KiranabazarOrderPackageDetails{}
	}
	result.PackageDetails = packageDetails

	// Get order status
	orderStatus, err := GetOrderStatus(s.repository, orderID)
	if err != nil {
		// return nil, errors.Wrap(err, "failed to get order status")
	}
	result.OrderStatus = orderStatus

	return result, nil
}

// calculateOrderFinancials computes the financial summary for the entire order
func (s *Service) calculateOrderFinancials(ctx context.Context, orderInfo dao.KiranaBazarOrder, orderDetails *dao.KiranaBazarOrderDetails, orderPayment dao.KiranaBazarOrderPayment, orderReconciliation dao.KiranaBazarReconciliation, sellerInfo dao.KiranaBazarSeller, allDiscounts kcFinances.AllDiscounts, kcShip bool, taxFinances kcFinances.Tax, orderStatus dao.KiranaBazarOrderStatus) (kcFinancesDao.OrderFinancials, error) {
	logger := logrus.WithContext(ctx).WithField("func", "calculateOrderFinancials").WithField("orderID", *orderInfo.ID)

	// Validate seller data
	sellerInvoicingData := sellerInfo.GetInvoicingDetails()
	if sellerInvoicingData.DisplayName == "" {
		logger.Error(ErrMsgSellerInvoiceData)
		return kcFinancesDao.OrderFinancials{}, errors.New(ErrMsgSellerInvoiceData)
	}

	sellerShippingData := sellerInfo.GetShippingDetails()
	if sellerShippingData.City == "" {
		logger.Error(ErrMsgSellerShippingData)
		return kcFinancesDao.OrderFinancials{}, errors.New(ErrMsgSellerShippingData)
	}

	// Process advance payment
	advancePayment := 0.0
	if orderPayment.GetAdvancePaid() != nil {
		advancePayment = *orderPayment.GetAdvancePaid()
	}

	billingGST := ""
	if orderDetails.BillingAddress != nil {
		billingGST = orderDetails.BillingAddress.GST
	}
	billingState := ""
	if orderDetails.BillingAddress != nil {
		billingState = *orderDetails.BillingAddress.State
	} else {
		billingState = *orderDetails.ShippingAddress.State
	}

	invoiceDate := time.UnixMilli(orderReconciliation.OrderPlaced)
	if orderReconciliation.OrderShipmentCreated != nil {
		invoiceDate = time.UnixMilli(*orderReconciliation.OrderShipmentCreated)
	}

	thirdPL := ""
	if orderStatus.Courier != nil {
		thirdPL = *orderStatus.Courier
	}

	confirmedTime := time.Time{}
	if orderReconciliation.OrderConfirmed != nil {
		confirmedTime = time.UnixMilli(*orderReconciliation.OrderConfirmed)
	}

	saasFees := getSaasFees(orderInfo)

	// Create order financials record
	financials := kcFinancesDao.OrderFinancials{
		OrderID:        uint64(*orderInfo.ID),
		InvoiceNumber:  orderPayment.ExtInvoiceNumber,
		InvoiceDate:    invoiceDate,
		GSTINBuyer:     billingGST,
		GSTINSeller:    sellerInvoicingData.GSTNumber,
		PANSeller:      sellerInvoicingData.PANNumber,
		PlaceOfSupply:  sellerShippingData.State,
		PlaceOfDemand:  billingState,
		RetailerID:     *orderInfo.UserID,
		SupplierID:     uint(sellerInfo.ID),
		OrderConfirmed: confirmedTime,

		// All discounts
		SellerDiscount:   allDiscounts.SellerDiscount,
		PlatformDiscount: allDiscounts.PlatformDiscount,
		PlatformCashback: allDiscounts.PlatformCashback,
		PaymentCashback:  allDiscounts.PaymentCashback,
		TotalDiscount:    allDiscounts.TotalDiscount,
		MarkdownDiscount: allDiscounts.MarkdownDiscount,

		// KCShip: kcShip,

		// Tax
		CartValue:      taxFinances.CartValue,
		TaxableValue:   taxFinances.TaxableValue,
		CGSTTotal:      taxFinances.CGSTTotal,
		SGSTTotal:      taxFinances.SGSTTotal,
		IGSTTotal:      taxFinances.IGSTTotal,
		CESSTotal:      taxFinances.CESSTotal,
		TotalGSTAmount: taxFinances.TotalGSTAmount,
		OrderTotal:     taxFinances.OrderTotal,
		InvoiceValue:   taxFinances.InvoiceValue,
		OrderValue:     taxFinances.OrderValue,

		// Commission and charges (will be populated later via CSV upload)
		// CommissionRate:            0,
		// CommissionAmount:          0,
		SaaSCharge:    &saasFees,
		TDSRate:       0.01,
		TDSAmount:     taxFinances.TaxableValue * 0.001,
		TCSApplicable: false,
		TCSRate:       1,
		TCSAmount:     taxFinances.TaxableValue * 0.01,
		ThirdPL:       thirdPL,
		// ThirdPLChargedWeight:      0,
		// ThirdPLCharges:            0,
		PaymentType:           determinePaymentType(orderPayment),
		PaymentGateway:        determinePaymentGateway(orderPayment),
		PaymentGatewayCharges: calculatePaymentGatewayCharges(orderPayment),
		PaymentGatewayGST:     calculatePaymentGatewayGST(orderPayment),
		// PaymentTotalCharges:       calculatePaymentGatewayCharges(orderPayment) + calculatePaymentGatewayGST(orderPayment),
		AdvanceCollected: advancePayment,
		// CashCollectedByThirdPL:    0,
		// CODCharges:                0,
		// NetPayableToSupplier:      0,
		// FinalSettlementToSupplier: 0,
		ShippingStatus:     determineOrderStatusForFinances(orderInfo),
		ShippingStatusDate: time.Now(),
		Remarks:            "",
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
		Seller:             sellerInfo.Name,
	}

	// Add timestamp for order dispatch if available
	if orderReconciliation.OrderDispatched != nil && *orderReconciliation.OrderDispatched != 0 {
		dispatchedTime := time.UnixMilli(*orderReconciliation.OrderDispatched)
		financials.OrderDispatched = &dispatchedTime
	}

	// Add timestamp for order delivery if available
	if orderReconciliation.OrderDelivered != nil && *orderReconciliation.OrderDelivered != 0 {
		deliveredTime := time.UnixMilli(*orderReconciliation.OrderDelivered)
		financials.OrderDelivered = &deliveredTime
	}

	logger.WithField("financials", financials).Debug("Order financials calculated")
	return financials, nil
}

// getSaasFees returns the saas fees for the order
func getSaasFees(orderInfo dao.KiranaBazarOrder) float64 {
	if orderInfo.Seller == utils.SOOTHE {
		return 1.0
	}
	return 2.0
}

// determineOrderStatusForFinances maps the order status to financial status
func determineOrderStatusForFinances(orderInfo dao.KiranaBazarOrder) string {
	if orderInfo.DisplayStatus == displaystatus.DELIVERED {
		return displaystatus.DELIVERED
	} else if orderInfo.DeliveryStatus == shipmentstatus.RTO_DELIVERED {
		return shipmentstatus.RTO_DELIVERED
	}
	return DefaultShippingStatus
}

// determinePaymentType identifies the payment type based on order payment details
func determinePaymentType(orderPayment dao.KiranaBazarOrderPayment) string {

	// Default to COD if unable to determine
	if orderPayment.Status != nil && slices.Contains([]string{"FULLY_PAID", "PARTIALLY_PAID"}, *orderPayment.Status) {
		return PaymentTypeOnline
	}
	return PaymentTypeCOD
}

// determinePaymentGateway identifies the payment gateway used for the order
func determinePaymentGateway(orderPayment dao.KiranaBazarOrderPayment) string {
	// Extract payment gateway from payment details if available
	// if orderPayment.GetPaymentGateway() != "" {
	// 	return orderPayment.GetPaymentGateway()
	// }

	// Default to Razorpay if not specified
	return PaymentGatewayRazorpay
}

// calculatePaymentGatewayCharges computes charges applied by payment gateway
func calculatePaymentGatewayCharges(orderPayment dao.KiranaBazarOrderPayment) *float64 {
	// Extract payment gateway charges if available
	// if orderPayment.GetPaymentGatewayCharges() != nil {
	// 	return *orderPayment.GetPaymentGatewayCharges()
	// }
	return nil
}

// calculatePaymentGatewayGST computes GST on payment gateway charges
func calculatePaymentGatewayGST(orderPayment dao.KiranaBazarOrderPayment) *float64 {
	// Extract payment gateway GST if available
	// if orderPayment.GetPaymentGatewayGST() != nil {
	// 	return *orderPayment.GetPaymentGatewayGST()
	// }
	return nil
}

// CalculateOrderItemFinancials calculates financial details for each item in the order
func (s *Service) CalculateOrderItemFinancials(ctx context.Context, orderInfo dao.KiranaBazarOrder, orderDetails *dao.KiranaBazarOrderDetails, orderPayment dao.KiranaBazarOrderPayment, orderReconciliation dao.KiranaBazarReconciliation, isIGST bool, sellerInfo dao.KiranaBazarSeller) ([]*kcFinancesDao.OrderItemFinancials, kcFinances.Tax, error) {

	logger := logrus.WithContext(ctx).WithField("func", "CalculateOrderItemFinancials").WithField("orderID", *orderInfo.ID)

	sellerShippingData := sellerInfo.GetShippingDetails()
	sellerInvoicingData := sellerInfo.GetInvoicingDetails()

	// Create invoice request to get tax information
	invoiceResponse, _, err := s.CreateInvoiceRequest(orderInfo, orderDetails, orderPayment, sellerShippingData, "", sellerInvoicingData, "", "", false)

	if err != nil {
		errMsg := fmt.Sprintf("error creating invoice request: %v", err)
		slack.SendSlackMessage(errMsg)
		logger.WithError(err).Error("Failed to create invoice request")
		return nil, kcFinances.Tax{}, errors.Wrap(err, "failed to create invoice request")
	}

	// Create a map of invoice items for easy lookup
	invoiceItemData := make(map[string]dto.ItemInfo, len(invoiceResponse.Data.Items))
	for _, item := range invoiceResponse.Data.Items {
		invoiceItemData[item.SKU] = item
	}

	// Initialize tax counters
	var (
		totalDiscount       = 0.0
		totalTax            = 0.0
		totalValueBeforeTax = 0.0
		cgstTotal           = 0.0
		sgstTotal           = 0.0
		igstTotal           = 0.0
		cessTotal           = 0.0
	)

	orderItemFinancials := make([]*kcFinancesDao.OrderItemFinancials, 0, len(orderDetails.GetOrderCart()))

	// Process each item in the cart
	for orderItemID, item := range orderDetails.GetOrderCart() {
		// Extract product metadata
		meta := shared.KiranaBazarProductMeta{}
		if err := json.Unmarshal(item.Meta, &meta); err != nil {
			logger.WithError(err).WithField("itemID", item.ID).Error("Failed to unmarshal product meta")
			return nil, kcFinances.Tax{}, errors.Wrapf(err, "failed to unmarshal product meta for item %s", item.ID)
		}

		// Parse product ID and fetch product details
		productID, err := strconv.ParseInt(item.ID, 10, 64)
		if err != nil {
			logger.WithError(err).WithField("itemID", item.ID).Error("Failed to parse product ID")
			return nil, kcFinances.Tax{}, errors.Wrapf(err, "failed to parse product ID for item %s", item.ID)
		}

		product, exists := products.GetProductByID(productID)
		if !exists {
			logger.WithField("productID", productID).Error("Product not found")
			return nil, kcFinances.Tax{}, errors.New(ErrMsgProductNotFound)
		}

		invoiceItem, exists := invoiceItemData[product.Code]
		if !exists {
			logger.WithField("productCode", product.Code).Error("Invoice item not found")
			return nil, kcFinances.Tax{}, errors.Errorf("invoice item not found for product code %s", product.Code)
		}

		// Get HSN code
		hsnCode := determineHSNCode(product)

		// Determine wholesale rate
		brandWholeSaleRate := determineBrandWholesaleRate(meta)

		// Calculate GST amounts
		gstRate, _, cgstRate, cgstAmount, sgstRate, sgstAmount, igstRate, igstAmount := calculateGSTValues(product.MetaProperties, invoiceItem.TaxAmount, isIGST)

		// CESS is currently not applicable
		cessRate := 0.0
		cessAmount := 0.0

		// Update tax totals
		totalDiscount += invoiceItem.ItemTotalDiscount
		totalTax += cgstAmount + sgstAmount + igstAmount + cessAmount
		totalValueBeforeTax += invoiceItem.TaxableValue
		cgstTotal += cgstAmount
		sgstTotal += sgstAmount
		igstTotal += igstAmount
		cessTotal += cessAmount

		// Create order item financial record
		orderItemFinancials = append(orderItemFinancials, &kcFinancesDao.OrderItemFinancials{
			OrderID:     uint64(*orderInfo.ID),
			OrderItemID: uint64(orderItemID),
			ProductID:   uint64(productID),
			SKUID:       product.Code,
			HSNCode:     hsnCode,

			// Pricing
			KCUnitPrice:       brandWholeSaleRate,
			KCSellingPrice:    meta.WholesaleRate,
			Quantity:          int(item.Quantity),
			TotalKCPrice:      brandWholeSaleRate * float64(item.Quantity) * float64(meta.PackSize),
			TotalSellingPrice: meta.WholesaleRate * float64(item.Quantity) * float64(meta.PackSize),
			TotalDiscount:     invoiceItem.ItemTotalDiscount,
			PlatformDiscount:  invoiceItem.ItemPlatformDiscount,
			SellerDiscount:    invoiceItem.ItemSellerDiscount,
			PaymentDiscount:   invoiceItem.ItemPaymentDiscount,
			PlatformCashback:  invoiceItem.ItemPlatformCashback,
			MarkdownDiscount:  invoiceItem.ItemMarkdownDiscount,

			// Tax
			GSTRate:      gstRate,
			CGSTRate:     cgstRate,
			SGSTRate:     sgstRate,
			IGSTRate:     igstRate,
			CESSRate:     cessRate,
			CGSTAmount:   cgstAmount,
			SGSTAmount:   sgstAmount,
			IGSTAmount:   igstAmount,
			CESSAmount:   cessAmount,
			TotalTax:     cgstAmount + sgstAmount + igstAmount + cessAmount,
			ItemTotal:    invoiceItem.TotalItemValue,
			TaxableValue: invoiceItem.TaxableValue,

			IsFreeItem:     false, // Free items not implemented yet
			FreeItemReason: "",    // Free item reason not implemented yet
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		})
	}

	// Create tax finance summary
	taxFinances := kcFinances.Tax{
		CartValue:      orderDetails.GetCartValue(),
		TaxableValue:   totalValueBeforeTax,
		CGSTTotal:      cgstTotal,
		SGSTTotal:      sgstTotal,
		IGSTTotal:      igstTotal,
		CESSTotal:      cessTotal,
		TotalGSTAmount: totalTax,
		OrderTotal:     invoiceResponse.Data.TaxSummary.InvoiceValue, // deprecated, use invoice value
		InvoiceValue:   invoiceResponse.Data.TaxSummary.InvoiceValue,
		OrderValue:     invoiceResponse.Data.TaxSummary.OrderValue,
	}

	logger.WithField("taxFinancesTotal", taxFinances.OrderTotal).Debug("Order item financials calculated")
	return orderItemFinancials, taxFinances, nil
}

// determineHSNCode gets the appropriate HSN code from product or meta
func determineHSNCode(product *products.Product) string {
	// Fall back to product properties
	if product.MetaProperties.HSNCode != nil {
		return *product.MetaProperties.HSNCode
	}

	return ""
}

// determineBrandWholesaleRate gets the wholesale rate from metadata
func determineBrandWholesaleRate(meta shared.KiranaBazarProductMeta) float64 {
	if meta.BrandWholesaleRate != nil {
		return *meta.BrandWholesaleRate
	}
	return meta.WholesaleRate
}

// calculateGSTValues computes GST values based on tax rate and IGST flag
func calculateGSTValues(meta shared.KiranaBazarProductMeta, taxAmount float64, isIGST bool) (gstRate, gstAmount, cgstRate, cgstAmount, sgstRate, sgstAmount, igstRate, igstAmount float64) {

	// Get GST rate and amount from meta
	gstRate = 0.0
	gstAmount = 0.0
	if meta.Tax != nil {
		gstRate = *meta.Tax
		gstAmount = taxAmount
	}

	// Split GST based on whether IGST applies
	if isIGST {
		igstRate = gstRate
		igstAmount = gstAmount
	} else {
		cgstRate = gstRate / 2
		cgstAmount = gstAmount / 2
		sgstRate = gstRate / 2
		sgstAmount = gstAmount / 2
	}

	return gstRate, gstAmount, cgstRate, cgstAmount, sgstRate, sgstAmount, igstRate, igstAmount
}

// calculateOrderDiscounts calculates all discounts applied to the order
func (s *Service) calculateOrderDiscounts(ctx context.Context, orderInfo dao.KiranaBazarOrder, orderDetails *dao.KiranaBazarOrderDetails, orderPayment dao.KiranaBazarOrderPayment, orderReconciliation dao.KiranaBazarReconciliation) ([]*kcFinancesDao.OrderDiscount, kcFinances.AllDiscounts, error) {
	orderDiscounts := make([]*kcFinancesDao.OrderDiscount, 0, len(orderDetails.BillBreakUp.DiscountPricing))

	// Process each discount in the order
	for _, discount := range orderDetails.BillBreakUp.DiscountPricing {
		// Skip zero-value discounts
		if math.Abs(discount.TotalValue) == 0 || discount.Type == "charge" || discount.Type == "PrePaid Amount" {
			continue
		}

		// Determine discount provider and categorize discount
		provider := helper.DetermineDiscountProvider(discount, orderDetails.Seller)

		// Create order discount record
		orderDiscounts = append(orderDiscounts, &kcFinancesDao.OrderDiscount{
			OrderID:             uint64(*orderInfo.ID),
			DiscountType:        discount.Type,
			DiscountProvider:    provider,
			DiscountCode:        discount.Name,
			IsPercentage:        false,
			DiscountPercentage:  0,
			DiscountAmount:      math.Abs(discount.TotalValue),
			DiscountDescription: discount.Key,
			OfferType:           "NA",
			IsPreTax:            true,
			OrderItemID:         0,
		})
	}

	// Create discount summary
	allDiscounts := kcFinances.AllDiscounts{
		TotalDiscount:    orderDetails.GetTotalDiscountValue(),
		PlatformDiscount: orderDetails.GetPlatformDiscountValue(),
		SellerDiscount:   orderDetails.GetSellerDiscountValue(),
		PlatformCashback: orderDetails.GetPlatformCashbackValue(),
		PaymentCashback:  orderDetails.GetPaymentDiscountValue(),
		MarkdownDiscount: orderDetails.GetMarkdownDiscountValue(),
	}

	return orderDiscounts, allDiscounts, nil
}

// calculateFreeItems processes any free items included in the order
func (s *Service) calculateFreeItems(ctx context.Context, orderInfo dao.KiranaBazarOrder, orderDetails *dao.KiranaBazarOrderDetails, orderPayment dao.KiranaBazarOrderPayment, orderReconciliation dao.KiranaBazarReconciliation) ([]*kcFinancesDao.FreeItem, error) {

	logger := logrus.WithContext(ctx).WithField("func", "calculateFreeItems").WithField("orderID", *orderInfo.ID)

	// Currently, no free items are being processed
	// This is a placeholder for future implementation
	logger.Debug("No free items found in order")
	return []*kcFinancesDao.FreeItem{}, nil
}

// calculateShipmentFinancials processes shipping details and costs
func (s *Service) calculateShipmentFinancials(ctx context.Context, orderInfo dao.KiranaBazarOrder, orderDetails *dao.KiranaBazarOrderDetails, orderPayment dao.KiranaBazarOrderPayment, orderReconciliation dao.KiranaBazarReconciliation, orderStatus dao.KiranaBazarOrderStatus, packageDetails shared.PackageDetails, shippingDetails dao.KiranaBazarSellerShippingDetails) ([]*kcFinancesDao.ShipmentFinancials, error) {

	logger := logrus.WithContext(ctx).WithField("func", "calculateShipmentFinancials").WithField("orderID", *orderInfo.ID)

	// Extract package dimensions
	sellerDeadWeight := 0.0
	sellerLengthCm := 0.0
	sellerBreadthCm := 0.0
	sellerHeightCm := 0.0
	sellerVolumetricWeight := 0.0

	// If package dimensions are available, use them
	if len(packageDetails.Dimensions) > 0 {
		sellerDeadWeight = packageDetails.Dimensions[0].Weight
		sellerLengthCm = packageDetails.Dimensions[0].Length
		sellerBreadthCm = packageDetails.Dimensions[0].Breadth
		sellerHeightCm = packageDetails.Dimensions[0].Height
		sellerVolumetricWeight = packageDetails.GetVolumetricWeight()
		logger.WithFields(logrus.Fields{
			"deadWeight":       sellerDeadWeight,
			"volumetricWeight": sellerVolumetricWeight,
		}).Debug("Package dimensions found")
	} else {
		logger.Debug("No package dimensions found")
	}

	// Validate AWB number
	awbNumber := ""
	if orderStatus.AWBNumber != nil {
		awbNumber = *orderStatus.AWBNumber
	} else {
		logger.Warn("Missing AWB number in order status")
	}

	// Validate courier partner
	courierPartner := ""
	if orderStatus.Courier != nil {
		courierPartner = *orderStatus.Courier
	} else {
		logger.Warn("Missing courier partner in order status")
	}
	billingZoneDefault := "NA"
	actualCourier, err := couriers.GetActualCourier(&courierPartner)
	if err != nil {
		logger.WithError(err).Error("Failed to get actual courier")
		actualCourier.CourierName = courierPartner
	}

	billingZone, err := s.FinancialService.GetShipmentBillingZone(ctx, shippingDetails.Pincode, *orderDetails.ShippingAddress.PostalCode, actualCourier.CourierName)
	if err != nil {
		logger.WithError(err).Error("Failed to get shipment billing zone")
		billingZone = &billingZoneDefault
	}

	// Create shipment financials record
	shipmentFinancials := []*kcFinancesDao.ShipmentFinancials{
		{
			OrderID:                uint(*orderInfo.ID),
			WaybillNumber:          awbNumber,
			CourierPartner:         courierPartner,
			CourierPartnerGSTIN:    "", // To be populated from CSV
			LogisticsZone:          *billingZone,
			SellerDeadWeight:       sellerDeadWeight,
			SellerLengthCm:         sellerLengthCm,
			SellerBreadthCm:        sellerBreadthCm,
			SellerHeightCm:         sellerHeightCm,
			SellerVolumetricWeight: sellerVolumetricWeight,
			RTOCharges:             0.00,
			RTOReason:              "",
			CreatedAt:              time.Now(),
			UpdatedAt:              time.Now(),
			Origin:                 shippingDetails.Pincode,
			Destination:            *orderDetails.ShippingAddress.PostalCode,
		},
	}

	logger.Debug("Shipment financials calculated")
	return shipmentFinancials, nil
}

// Helper function to handle pointer to float64
func floatPtrToString(f *float64) string {
	if f == nil {
		return ""
	}
	return strconv.FormatFloat(*f, 'f', 2, 64)
}

// Helper function to handle pointer to bool
func boolPtrToString(b *bool) string {
	if b == nil {
		return ""
	}
	return strconv.FormatBool(*b)
}

// Helper function to handle pointer to time.Time
func timePtrToString(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

// Global mutex map for file-level locking
var (
	fileMutexes = make(map[string]*sync.Mutex)
	mapMutex    = sync.RWMutex{}
)

// getFileMutex returns a mutex for the given filename
func getFileMutex(filename string) *sync.Mutex {
	// Use absolute path to avoid issues with relative paths
	absPath, err := filepath.Abs(filename)
	if err != nil {
		absPath = filename // fallback to original if abs path fails
	}

	mapMutex.RLock()
	mu, exists := fileMutexes[absPath]
	mapMutex.RUnlock()

	if !exists {
		mapMutex.Lock()
		// Double-check pattern
		if mu, exists = fileMutexes[absPath]; !exists {
			mu = &sync.Mutex{}
			fileMutexes[absPath] = mu
		}
		mapMutex.Unlock()
	}

	return mu
}

// WriteOrderFinancialsToCSV writes a slice of OrderFinancials to a CSV file
// This function is safe for concurrent use from multiple goroutines
func WriteOrderFinancialsToCSV(filename string, orders []kcFinancesDao.OrderFinancials) error {
	// Get file-specific mutex to prevent concurrent writes to the same file
	fileMutex := getFileMutex(filename)
	fileMutex.Lock()
	defer fileMutex.Unlock()

	// Check if file exists to determine if we need to write header
	fileExists := false
	if _, err := os.Stat(filename); err == nil {
		fileExists = true
	}

	// Open file in append mode if it exists, create if it doesn't
	var file *os.File
	var err error
	if fileExists {
		file, err = os.OpenFile(filename, os.O_WRONLY|os.O_APPEND, 0644)
	} else {
		file, err = os.Create(filename)
	}

	if err != nil {
		return fmt.Errorf("failed to open/create file: %w", err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Write CSV header only if file is new
	if !fileExists {
		header := []string{
			"ID", "OrderID", "InvoiceNumber", "InvoiceDate", "GSTINBuyer", "GSTINSeller",
			"PANSeller", "PlaceOfSupply", "PlaceOfDemand", "RetailerID", "SupplierID",
			"CartValue", "TaxableValue", "CGSTTotal", "SGSTTotal", "IGSTTotal", "CESSTotal",
			"TotalGSTAmount", "OrderTotal", "Seller", "SellerDiscount", "PlatformDiscount",
			"PlatformCashback", "PaymentCashback", "TotalDiscount", "CommissionRate",
			"CommissionAmount", "SaaSCharge", "TDSRate", "TDSAmount", "KCShip",
			"TCSApplicable", "TCSRate", "TCSAmount", "ThirdPL", "ThirdPLChargedWeight",
			"ThirdPLCharges", "PaymentType", "PaymentGateway", "PaymentGatewayCharges",
			"PaymentGatewayGST", "PaymentTotalCharges", "AdvanceCollected",
			"CashCollectedByThirdPL", "CODCharges", "CODRemittanceDate",
			"NetPayableToSupplier", "FinalSettlementToSupplier", "FinalSettlementDate",
			"ShippingStatus", "ShippingStatusDate", "OrderConfirmed", "OrderDispatched",
			"OrderDelivered", "Remarks", "CreatedAt", "UpdatedAt",
		}

		if err := writer.Write(header); err != nil {
			return fmt.Errorf("failed to write header: %w", err)
		}
	}

	// Write data rows
	for _, order := range orders {
		record := []string{
			strconv.FormatUint(order.ID, 10),
			strconv.FormatUint(order.OrderID, 10),
			order.InvoiceNumber,
			order.InvoiceDate.Format("2006-01-02 15:04:05"),
			order.GSTINBuyer,
			order.GSTINSeller,
			order.PANSeller,
			order.PlaceOfSupply,
			order.PlaceOfDemand,
			order.RetailerID,
			strconv.FormatUint(uint64(order.SupplierID), 10),
			strconv.FormatFloat(order.CartValue, 'f', 2, 64),
			strconv.FormatFloat(order.TaxableValue, 'f', 2, 64),
			strconv.FormatFloat(order.CGSTTotal, 'f', 2, 64),
			strconv.FormatFloat(order.SGSTTotal, 'f', 2, 64),
			strconv.FormatFloat(order.IGSTTotal, 'f', 2, 64),
			strconv.FormatFloat(order.CESSTotal, 'f', 2, 64),
			strconv.FormatFloat(order.TotalGSTAmount, 'f', 2, 64),
			strconv.FormatFloat(order.OrderTotal, 'f', 2, 64),
			order.Seller,
			strconv.FormatFloat(order.SellerDiscount, 'f', 2, 64),
			strconv.FormatFloat(order.PlatformDiscount, 'f', 2, 64),
			strconv.FormatFloat(order.PlatformCashback, 'f', 2, 64),
			strconv.FormatFloat(order.PaymentCashback, 'f', 2, 64),
			strconv.FormatFloat(order.TotalDiscount, 'f', 2, 64),
			floatPtrToString(order.CommissionRate),
			floatPtrToString(order.CommissionAmount),
			floatPtrToString(order.SaaSCharge),
			strconv.FormatFloat(order.TDSRate, 'f', 2, 64),
			strconv.FormatFloat(order.TDSAmount, 'f', 2, 64),
			boolPtrToString(order.KCShip),
			strconv.FormatBool(order.TCSApplicable),
			strconv.FormatFloat(order.TCSRate, 'f', 2, 64),
			strconv.FormatFloat(order.TCSAmount, 'f', 2, 64),
			order.ThirdPL,
			floatPtrToString(order.ThirdPLChargedWeight),
			floatPtrToString(order.ThirdPLCharges),
			order.PaymentType,
			order.PaymentGateway,
			floatPtrToString(order.PaymentGatewayCharges),
			floatPtrToString(order.PaymentGatewayGST),
			floatPtrToString(order.PaymentTotalCharges),
			strconv.FormatFloat(order.AdvanceCollected, 'f', 2, 64),
			floatPtrToString(order.CashCollectedByThirdPL),
			floatPtrToString(order.CODCharges),
			timePtrToString(order.CODRemittanceDate),
			floatPtrToString(order.NetPayableToSupplier),
			floatPtrToString(order.FinalSettlementToSupplier),
			timePtrToString(order.FinalSettlementDate),
			order.ShippingStatus,
			order.ShippingStatusDate.Format("2006-01-02 15:04:05"),
			order.OrderConfirmed.Format("2006-01-02 15:04:05"),
			timePtrToString(order.OrderDispatched),
			timePtrToString(order.OrderDelivered),
			order.Remarks,
			order.CreatedAt.Format("2006-01-02 15:04:05"),
			order.UpdatedAt.Format("2006-01-02 15:04:05"),
		}

		if err := writer.Write(record); err != nil {
			return fmt.Errorf("failed to write record: %w", err)
		}
	}

	return nil
}
