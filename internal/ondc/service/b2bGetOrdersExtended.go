package service

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	processingstatus "kc/internal/ondc/service/orderStatus/processingStatus"
	"log"
	"slices"
	"strings"
	"sync"
	"time"

	"kc/internal/ondc/utils"

	"gorm.io/datatypes"
)

var (
	RECO_DAYS_IN = map[string]int{
		"PLACED":          5,
		"CONFIRMED":       5,
		"IN_TRANSIT":      10,
		"CONFIRMED_72HRS": 3,
	}

	TAG_TYPES = map[string]string{
		"IVR":     "IVR",
		"ORDER":   "ORDER",
		"NOT_SET": "NOT_SET",
	}

	RECO_STATUS_IN = []string{"PLACED", "CONFIRMED", "IN_TRANSIT", "NDR", "OTHERS"}
	RECO_STATUS_IN_EXTENDED = []string{"PLACED", "CONFIRMED_72HRS", "IN_TRANSIT", "NDR", "OTHERS"}

	ORDER_TAG_MAP = map[string]string{
		"1":  "FIRST TIME",
		"2":  "POWER USER",
		"3":  "BULK",
		"4":  "SPAM",
		"5":  "MANUAL CALL",
		"7":  "PARTIAL PAID",
		"8":  "FULLY PAID",
		"9":  "PAYMENT PENDING",
		"10": "ADVANCE REFUNDED",
		"11": "REFUNDED",
		"12": "COD Available",
		"13": "RSB COHORT USER",
	}
)

type OrderTagMapping struct {
	OrderID string `db:"order_id"`
	TagIDs  string `db:"tag_ids"`
}

type OrderStats struct {
	GenStatus string `db:"gen_status"`
	Count     int    `db:"count"`
}

type GetOrdersExtendedResponse struct {
	Stats map[string]int        `json:"stats"`
	Data  []ExtendedOrderDetail `json:"data"`
}

type ExtendedOrderDetail struct {
	ID                        int                       `json:"id"`
	User                      UserDetail                `json:"user"`
	AddressName               string                    `json:"address_name"`
	AddressCity               string                    `json:"address_city"`
	AddressLine               string                    `json:"address_line"`
	PostalCode                string                    `json:"postal_code"`
	State                     string                    `json:"state"`
	GST                       string                    `json:"gst"`
	AddressID                 float64                   `json:"address_id"`
	StoreName                 string                    `json:"store_name"`
	HouseNumber               string                    `json:"house_number"`
	Neighbourhood             string                    `json:"neighbourhood"`
	Village                   string                    `json:"village"`
	Landmark                  string                    `json:"landmark"`
	AddressTag                string                    `json:"address_tag"`
	AddressLine1              string                    `json:"address_line1"`
	AddressLine2              string                    `json:"address_line2"`
	Phone                     string                    `json:"phone"`
	Total                     float64                   `json:"total"`
	OrderID                   string                    `json:"order_id"`
	OrderDate                 string                    `json:"order_date"`
	OrderStatus               string                    `json:"order_status"`
	PaymentStatus             string                    `json:"payment_status"`
	DeliveryStatus            string                    `json:"delivery_status"`
	DisplayStatus             string                    `json:"display_status"`
	ProcessingStatus          string                    `json:"processing_status"`
	Note                      string                    `json:"note"`
	CallStatus                string                    `json:"call_status"`
	UpdatedBy                 string                    `json:"updated_by"`
	UpdatedAt                 string                    `json:"updated_at"`
	CancelReason              string                    `json:"cancel_reason"`
	Seller                    string                    `json:"seller"`
	PaidAmount                float64                   `json:"paid_amount"`
	PaidAmountProof           []string                  `json:"paid_amount_proof"`
	AdvanceTaken              bool                      `json:"advance_taken"`
	TrackingLink              string                    `json:"tracking_link"`
	Courier                   string                    `json:"courier,omitempty"`
	AWBNumber                 string                    `json:"awb_number,omitempty"`
	OrderPlaced               int64                     `json:"order_placed,omitempty"`
	OrderConfirmed            int64                     `json:"order_confirmed,omitempty"`
	OrderShipmentCreated      int64                     `json:"order_shipment_created,omitempty"`
	OrderDelivered            int64                     `json:"order_delivered,omitempty"`
	OrderCancelled            int64                     `json:"order_cancelled,omitempty"`
	OrderReturned             int64                     `json:"order_returned,omitempty"`
	IVRStatus                 string                    `json:"ivr_status"`
	IVRMeta                   string                    `json:"ivr_meta"`
	ReturnedReason            string                    `json:"returned_reason"`
	OrderTags                 []string                  `json:"order_tags"`
	TotalPlacedOrderAmount    float64                   `json:"total_placed_order_amount"`
	TotalConfirmedOrderAmount float64                   `json:"total_confirmed_order_amount"`
	TotalDeliveredOrderAmount float64                   `json:"total_delivered_order_amount"`
	TotalReturnedOrderAmount  float64                   `json:"total_returned_order_amount"`
	TotalCancelledOrderAmount float64                   `json:"total_cancelled_order_amount"`
	TotalOrderPlaced          int                       `json:"total_order_placed"`
	TotalOrderConfirmed       int                       `json:"total_order_confirmed"`
	TotalOrderDelivered       int                       `json:"total_order_delivered"`
	TotalOrderReturned        int                       `json:"total_order_returned"`
	TotalOrderCancelled       int                       `json:"total_order_cancelled"`
	TicketStatus              string                    `json:"ticket_status,omitempty"`
	TotalCallsCount           int                       `json:"total_calls_count,omitempty"`
	Calls                     []dao.KiranaBazarUserCall `json:"calls,omitempty"`
	AllowedActions            []string                  `json:"allowed_actions,omitempty"`
}

type UserDetail struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	ShopName     string `json:"shop_name,omitempty"`
	District     string `json:"district,omitempty"`
	Cluster      string `json:"cluster,omitempty"`
	State        string `json:"state,omitempty"`
	Level        string `json:"level,omitempty"`
	Phone        string `json:"phone,omitempty"`
	ProfileImage string `json:"profile_image,omitempty"`
	CreatedAt    string `json:"created_at,omitempty"`
}

type ExtendedOrderQueryResponse struct {
	ID                        int            `db:"id"`
	IVRStatus                 sql.NullString `db:"ivr_status"`
	TotalPlacedOrderAmount    float64        `db:"total_placed_order_amount"`
	TotalConfirmedOrderAmount float64        `db:"total_confirmed_order_amount"`
	TotalDeliveredOrderAmount float64        `db:"total_delivered_order_amount"`
	TotalReturnedOrderAmount  float64        `db:"total_returned_order_amount"`
	TotalCancelledOrderAmount float64        `db:"total_cancelled_order_amount"`
	TotalOrderPlaced          int            `db:"total_order_placed"`
	TotalOrderConfirmed       int            `db:"total_order_confirmed"`
	TotalOrderDelivered       int            `db:"total_order_delivered"`
	TotalOrderReturned        int            `db:"total_order_returned"`
	TotalOrderCancelled       int            `db:"total_order_cancelled"`
	IVRMeta                   datatypes.JSON `db:"ivr_meta"`
	CreatedAt                 time.Time      `db:"created_at"`
	PaidAmount                float64        `db:"paid_amount"`
	PaymentStatus             string         `db:"payment_status"`
	TotalAmount               float64        `db:"total_amount"`
	PaymentMeta               datatypes.JSON `db:"payment_meta"`
	Seller                    string         `db:"seller"`
	UserID                    string         `db:"user_id"`
	OrderStatus               string         `db:"order_status"`
	TrackingLink              string         `db:"tracking_link"`
	ReturnedReason            string         `db:"returned_reason"`
	DeliveryStatus            string         `db:"delivery_status"`
	DisplayStatus             string         `db:"display_status"`
	ProcessingStatus          string         `db:"processing_status"`
	ShippingAddress           string         `db:"shipping_address"`
	OrderID                   string         `db:"order_id"`
	CallStatus                string         `db:"call_status"`
	Note                      string         `db:"note"`
	UpdatedAt                 int64          `db:"updated_at"`
	UpdatedBy                 string         `db:"updated_by"`
	CancelReason              string         `db:"cancel_reason"`
	OrderConfirmed            int64          `db:"order_confirmed"`
	OrderPlaced               int64          `db:"order_placed"`
	OrderShipmentCreated      int64          `db:"order_shipment_created"`
	OrderDelivered            int64          `db:"order_delivered"`
	OrderCancelled            int64          `db:"order_cancelled"`
	OrderReturned             int64          `db:"order_returned"`
	Courier                   string         `db:"courier"`
	AWBNumber                 string         `db:"awb_number"`
	UserName                  string         `db:"user_name"`
	UserStoreName             string         `db:"user_store_name"`
	UserPhone                 string         `db:"user_phone"`
	UserLevel                 string         `db:"user_level"`
	TicketStatus              string         `db:"ticket_status"`
}

// filterTags filters tags by type
func filterTags(tags []dto.TagFilter, tagType string) []string {
	if len(tags) == 0 {
		return []string{}
	}
	var result []string
	for _, tag := range tags {
		if tag.Type == tagType {
			result = append(result, tag.Key)
		}
	}
	return result
}

// getOrdersConcatenatedTags fetches concatenated tags for orders
func (s *Service) getOrdersConcatenatedTags(ctx context.Context, orderIDs []string) (map[string][]string, error) {
	if len(orderIDs) == 0 {
		return make(map[string][]string), nil
	}

	// Build the IN clause with order IDs
	orderIDsStr := strings.Join(orderIDs, ", ")
	query := fmt.Sprintf(`
		SELECT 
			otm.order_id,  
			GROUP_CONCAT(DISTINCT otm.tag_id ORDER BY otm.tag_id SEPARATOR ', ') AS tag_ids  
		FROM kiranabazar_order_tag_mapping otm  
		WHERE otm.order_id IN (%s) and otm.is_active = 1
		GROUP BY otm.order_id
	`, orderIDsStr)

	var orderTagMappings []OrderTagMapping
	_, err := s.repository.CustomQuery(&orderTagMappings, query)
	if err != nil {
		return make(map[string][]string), nil
	}

	orderTagsMap := make(map[string][]string)
	for _, mapping := range orderTagMappings {
		if mapping.TagIDs != "" {
			tagIDs := strings.Split(mapping.TagIDs, ", ")
			var tagNames []string
			for _, tagID := range tagIDs {
				if tagName, exists := ORDER_TAG_MAP[tagID]; exists {
					tagNames = append(tagNames, tagName)
				}
			}
			orderTagsMap[mapping.OrderID] = tagNames
		} else {
			orderTagsMap[mapping.OrderID] = []string{}
		}
	}
	return orderTagsMap, nil
}

// getCombinedConditionsExtended creates SQL conditions for reco status with extended support
func getCombinedConditionsExtended(recoStatus []string, queryType string) string {
	if queryType == "count" {
		if slices.Contains(recoStatus, "CONFIRMED_72HRS") {
			recoStatus = RECO_STATUS_IN_EXTENDED
		} else {
			recoStatus = RECO_STATUS_IN
		}
	}

	var conditions []string
	for _, status := range recoStatus {
		recoDays := RECO_DAYS_IN[status]
		if status == "CONFIRMED_72HRS" {
			status = "CONFIRMED"
		}
		if recoDays == 0 {
			recoDays = 5
		}
		timestampXDaysAgo := time.Now().AddDate(0, 0, -recoDays).Truncate(24 * time.Hour).UnixMilli()

		switch status {
		case "PLACED":
			conditions = append(conditions, fmt.Sprintf(`
				(kbr.order_placed <= %d AND 
				kbr.order_confirmed IS NULL AND 
				kbr.order_shipment_created IS NULL AND 
				kbr.order_delivered IS NULL AND 
				kbr.order_cancelled IS NULL AND 
				kbr.order_returned IS NULL AND
				ko.display_status = 'PLACED')`, timestampXDaysAgo))
		case "CONFIRMED":
			conditions = append(conditions, fmt.Sprintf(`
				(kbr.order_confirmed <= %d AND 
				kbr.order_shipment_created IS NULL AND 
				kbr.order_delivered IS NULL AND 
				kbr.order_cancelled IS NULL AND 
				kbr.order_returned IS NULL AND
				ko.display_status = 'CONFIRMED')`, timestampXDaysAgo))
		case "IN_TRANSIT":
			conditions = append(conditions, fmt.Sprintf(`
				(kbr.order_shipment_created <= %d AND 
				kbr.order_delivered IS NULL AND 
				kbr.order_cancelled IS NULL AND 
				kbr.order_returned IS NULL AND
				ko.display_status = 'IN_TRANSIT')`, timestampXDaysAgo))
		case "NDR":
			conditions = append(conditions, "ko.display_status = 'NDR'")
		case "OTHERS":
			conditions = append(conditions, "ko.display_status = 'OTHERS'")
		}
	}

	return strings.Join(conditions, " OR ")
}

func (s *Service) getOrdersCallsInfo(ctx context.Context, orderIds []string) (map[string][]dao.KiranaBazarUserCall, error) {
	if len(orderIds) == 0 {
		return make(map[string][]dao.KiranaBazarUserCall), nil
	}

	queryOrderIds := make([]string, 0)
	for _, orderId := range orderIds {
		queryOrderIds = append(queryOrderIds, fmt.Sprintf(`"%s"`, orderId))
	}

	data := make([]dao.KiranaBazarUserCall, 0)
	query := fmt.Sprintf(`select * from kiranabazar_user_calls kuc where order_id in (%s) order by order_id desc, updated_at desc`, strings.Join(queryOrderIds, ", "))
	_, err := s.repository.CustomQuery(&data, query)
	if err != nil {
		log.Printf("Error fetching order calls info: %v", err)
		return nil, err
	}
	orderCallsMap := make(map[string][]dao.KiranaBazarUserCall)
	for _, call := range data {
		if call.OrderID == nil || *call.OrderID == 0 {
			continue
		}
		orderId := fmt.Sprintf("%d", *call.OrderID)
		orderCallsMap[orderId] = append(orderCallsMap[orderId], call)
	}
	return orderCallsMap, nil
}

func (s *Service) GetCallingOrders(ctx context.Context, req *dto.GetOrdersExtendedRequest) (*GetOrdersExtendedResponse, error) {
	// Set default values
	if req.From == 0 {
		req.From = 12205754
	}
	if req.To == 0 {
		req.To = 3167965754000
	}
	if req.Limit == 0 {
		req.Limit = 50
	}

	// Set default sellers and status - FIXED: Set all sellers instead of just zoff_foods
	if len(req.Seller) == 0 {
		req.Seller = []string{"zoff_foods"}
	}

	// FIXED: Set correct status values with proper quotes
	req.Status = []string{"\"PLACED\"", "\"PENDING_CONFIRMATION\""}

	// Filter tags by type
	req.Tag = append(req.Tag, dto.TagFilter{
		Key:  "IVR FAILED",
		Type: "IVR",
	}, dto.TagFilter{
		Key:  "4",
		Type: "ORDER",
	}, dto.TagFilter{
		Key:  "5",
		Type: "ORDER",
	}, dto.TagFilter{
		Key:  "9",
		Type: "ORDER",
	})

	ivrTags := filterTags(req.Tag, TAG_TYPES["IVR"])
	orderTags := filterTags(req.Tag, TAG_TYPES["ORDER"])
	// notSetTags := filterTags(req.Tag, TAG_TYPES["NOT_SET"])

	nonIvrUserIds := []string{}
	for userId := range utils.NON_IVR_USERS {
		if userId != "" {
			nonIvrUserIds = append(nonIvrUserIds, fmt.Sprintf(`"%s"`, userId))
		}
	}

	// Convert timestamps to dates
	loc, _ := time.LoadLocation("Asia/Kolkata")
	fromTime := time.UnixMilli(req.From).In(loc)
	toTime := time.UnixMilli(req.To).In(loc)
	fromDate := time.Date(fromTime.Year(), fromTime.Month(), fromTime.Day(), 0, 0, 0, 0, loc).Format("2006-01-02 15:04:05")
	toDate := time.Date(toTime.Year(), toTime.Month(), toTime.Day(), 23, 54, 59, 0, loc).Format("2006-01-02 15:04:05")

	// Build common join query
	commonJoinQuery := `SELECT
        DISTINCT ko.id,
        kois.ivr_status,
        COALESCE(uos.total_placed_order_amount, 0) as total_placed_order_amount,
        COALESCE(uos.total_confirmed_order_amount, 0) as total_confirmed_order_amount,
        COALESCE(uos.total_delivered_order_amount, 0) as total_delivered_order_amount,
        COALESCE(uos.total_returned_order_amount, 0) as total_returned_order_amount,
        COALESCE(uos.total_cancelled_order_amount, 0) as total_cancelled_order_amount,
        COALESCE(uos.order_placed, 0) as total_order_placed,
        COALESCE(uos.confirmed_order, 0) as total_order_confirmed,
        COALESCE(uos.order_delivered, 0) as total_order_delivered,
        COALESCE(uos.returned_order, 0) as total_order_returned,
        COALESCE(uos.cancelled_order, 0) as total_order_cancelled,
        kois.ivr_meta,
        ko.created_at,
        kop.paid_amount,
        kop.status AS payment_status,
        kop.amount AS total_amount,
        kop.payment_meta,
        ko.seller,
        ko.user_id,
        ko.order_status,
        ko.tracking_link,
        kcs.returned_reason,
        ko.delivery_status,
        ko.display_status,
        ko.processing_status,
        JSON_EXTRACT(kod.order_details, '$.shipping_address') AS shipping_address,
        kod.order_id,
        kcs.call_status,
        kcs.note,
        kcs.updated_at,
        kcs.updated_by,
        kcs.cancel_reason,
        kbr.order_confirmed,
        u.name as user_name,
        u.store_name as user_store_name,
        u.phone as user_phone,
        up.user_level,
        ct.status as ticket_status,
		kos.courier,
		kos.awb_number
        `

	commonJoinQuery += ` FROM kiranabazar_orders ko
        JOIN kiranabazar_order_details kod ON ko.id = kod.order_id
        LEFT JOIN kiranabazar_call_status kcs ON ko.id = kcs.order_id
        LEFT JOIN kiranabazar_order_payments kop ON ko.id = kop.order_id
        LEFT JOIN kiranabazar_orders_ivr_status kois ON ko.id = kois.order_id
        LEFT JOIN user_order_stats uos ON ko.user_id = uos.user_id
        LEFT JOIN kc_bazar_reconciliation kbr ON ko.id = kbr.order_id
        LEFT JOIN kiranabazar_payment_gateway_orders kpgo ON ko.id = kpgo.kc_order_id
        LEFT JOIN users u ON ko.user_id = u.user_id
        LEFT JOIN users_profile up ON ko.user_id = up.user_id
        left join cs_tickets ct ON ko.id = ct.order_id
		LEFT JOIN kiranabazar_order_status kos ON ko.id = kos.id
        `

	// FIXED: Always add the LEFT JOIN for order tag mapping since we need it for the conditions
	commonJoinQuery += ` LEFT JOIN kiranabazar_order_tag_mapping otm ON otm.order_id = ko.id`

	commonJoinQuery += ` WHERE 1=1`

	var query string
	if req.UserID != "" {
		query = fmt.Sprintf(`%s AND (ko.user_id = '%s' OR kod.customer_phone = '%s' OR kod.order_id = '%s')`,
			commonJoinQuery, req.UserID, req.UserID, req.UserID)
	} else {
		// FIXED: Main WHERE condition with proper date range
		query = fmt.Sprintf(`%s AND ko.created_at BETWEEN '%s' AND '%s'`, commonJoinQuery, fromDate, toDate)
	}

	// FIXED: Add seller filter
	if len(req.Seller) > 0 {
		sellerList := "'" + strings.Join(req.Seller, "','") + "'"
		query += fmt.Sprintf(` AND ko.seller IN (%s)`, sellerList)
	}

	// FIXED: Create the main OR condition grouping all calling criteria
	var callingConditions []string

	// Condition 1: NON_IVR_USERS
	if len(nonIvrUserIds) > 0 {
		callingConditions = append(callingConditions, fmt.Sprintf(`ko.user_id IN (%s)`, strings.Join(nonIvrUserIds, ", ")))
	}

	// Condition 2: High value orders with no payment
	callingConditions = append(callingConditions, fmt.Sprintf(`(kop.amount >= %f and (kop.paid_amount = 0 or kop.paid_amount is null))`, utils.MAX_ORDER_FOR_AUTOMATED_IVR))

	// Condition 3: IVR failed orders
	if len(ivrTags) > 0 {
		ivrTagsList := "'" + strings.Join(ivrTags, "','") + "'"
		callingConditions = append(callingConditions, fmt.Sprintf(`kois.ivr_status IN (%s)`, ivrTagsList))
	}

	// Condition 4: Specific order tags (SPAM=4, MANUAL CALL=5)
	if len(orderTags) > 0 {
		callingConditions = append(callingConditions, fmt.Sprintf(`(otm.tag_id IN (%s) AND otm.is_active = 1)`, strings.Join(orderTags, ", ")))
	}

	// Add the grouped OR conditions
	if len(callingConditions) > 0 {
		query += fmt.Sprintf(` AND (%s)`, strings.Join(callingConditions, " OR "))
	}

	// Add order amount filter if specified
	if req.OrderAmount.Max > 0 && req.OrderAmount.Min <= req.OrderAmount.Max {
		query += fmt.Sprintf(` AND kop.amount BETWEEN %f AND %f`, req.OrderAmount.Min, req.OrderAmount.Max)
	}

	// FIXED: Add display status filter
	query += fmt.Sprintf(` AND ko.display_status IN (%s)`, strings.Join(req.Status, ", "))

	// Add payment gateway status filter
	query += ` AND (kpgo.gateway_status != 'initiated' OR kpgo.gateway_status IS NULL OR (kpgo.gateway_status = 'initiated' AND kpgo.created_at <= (CONVERT_TZ(NOW(), @@session.time_zone, '+05:30') - INTERVAL 1 HOUR)))`

	query += ` ORDER BY ko.created_at DESC`

	if !req.ExportCSV {
		query += fmt.Sprintf(` LIMIT %d OFFSET %d`, req.Limit, req.Offset)
	}

	// FIXED: Build count query with same conditions
	countQuery := `SELECT
        CASE
            WHEN ko.processing_status in ('EXCEPTION') then ko.processing_status
            WHEN ko.display_status IN ('CANCELLED', 'DELIVERED', 'PLACED', 'RETURNED', 'PENDING_CONFIRMATION', 'IN_TRANSIT', 'CONFIRMED', 'SHIPMENT_CREATED', 'NDR')
            THEN ko.display_status
            ELSE 'OTHER'
        END AS gen_status,
        COUNT(DISTINCT ko.id) AS count
    FROM kiranabazar_orders ko
    JOIN kiranabazar_order_details kod ON ko.id = kod.order_id
    LEFT JOIN kiranabazar_call_status kcs ON ko.id = kcs.order_id
    LEFT JOIN kiranabazar_order_payments kop ON ko.id = kop.order_id
    LEFT JOIN kiranabazar_orders_ivr_status kois ON ko.id = kois.order_id
    LEFT JOIN kiranabazar_payment_gateway_orders kpgo ON ko.id = kpgo.kc_order_id
    LEFT JOIN kiranabazar_order_tag_mapping otm ON otm.order_id = ko.id`

	if req.UserID != "" {
		countQuery += fmt.Sprintf(` WHERE (ko.user_id = '%s' OR kod.customer_phone = '%s' OR kod.order_id = '%s')`,
			req.UserID, req.UserID, req.UserID)
	} else {
		countQuery += fmt.Sprintf(` WHERE ko.created_at BETWEEN '%s' AND '%s'`, fromDate, toDate)
	}

	// Add seller filter to count query
	if len(req.Seller) > 0 {
		sellerList := "'" + strings.Join(req.Seller, "','") + "'"
		countQuery += fmt.Sprintf(` AND ko.seller IN (%s)`, sellerList)
	}

	// Add the same grouped OR conditions to count query
	if len(callingConditions) > 0 {
		countQuery += fmt.Sprintf(` AND (%s)`, strings.Join(callingConditions, " OR "))
	}

	// Add order amount filter to count query
	if req.OrderAmount.Max > 0 && req.OrderAmount.Min <= req.OrderAmount.Max {
		countQuery += fmt.Sprintf(` AND kop.amount BETWEEN %f AND %f`, req.OrderAmount.Min, req.OrderAmount.Max)
	}

	// Add display status filter to count query
	countQuery += fmt.Sprintf(` AND ko.display_status IN (%s)`, strings.Join(req.Status, ", "))

	// Add payment gateway status filter to count query
	countQuery += ` AND (kpgo.gateway_status != 'initiated' OR kpgo.gateway_status IS NULL OR (kpgo.gateway_status = 'initiated' AND kpgo.created_at <= (CONVERT_TZ(NOW(), @@session.time_zone, '+05:30') - INTERVAL 1 HOUR)))`

	countQuery += `
    GROUP BY
        CASE
            WHEN ko.processing_status in ('EXCEPTION') THEN ko.processing_status
            WHEN ko.display_status IN ('CANCELLED', 'DELIVERED', 'PLACED', 'RETURNED', 'PENDING_CONFIRMATION', 'IN_TRANSIT', 'CONFIRMED', 'SHIPMENT_CREATED', 'NDR')
            THEN ko.display_status
            ELSE 'OTHER'
        END
    ORDER BY
        CASE
            WHEN gen_status = 'OTHER' THEN 1
            ELSE 0
        END,
        gen_status`

	// Rest of the function remains the same...
	// Execute queries
	var wg sync.WaitGroup
	var orders []ExtendedOrderQueryResponse
	var counts []OrderStats
	var getOrdersErr, getCountsErr error

	wg.Add(2)

	go func() {
		defer wg.Done()
		_, getOrdersErr = s.repository.CustomQuery(&orders, query)
	}()

	go func() {
		defer wg.Done()
		_, getCountsErr = s.repository.CustomQuery(&counts, countQuery)
	}()

	wg.Wait()

	if getOrdersErr != nil {
		return nil, fmt.Errorf("failed to get order details: %w", getOrdersErr)
	}
	if getCountsErr != nil {
		return nil, fmt.Errorf("failed to get order counts: %w", getCountsErr)
	}

	// Process stats
	stats := make(map[string]int)
	for _, cc := range counts {
		stats[cc.GenStatus] = cc.Count
	}

	// Get order IDs for tag mapping
	var orderIDs []string
	for _, order := range orders {
		orderIDs = append(orderIDs, fmt.Sprintf(`%d`, order.ID))
	}

	// Get concatenated tags for orders
	orderTagsMap, err := s.getOrdersConcatenatedTags(ctx, orderIDs)
	if err != nil {
		log.Printf("Failed to get order tags: %v", err)
		orderTagsMap = make(map[string][]string)
	}

	ordersCallsMapping, err := s.getOrdersCallsInfo(ctx, orderIDs)
	if err != nil {
		log.Printf("Failed to get order calls info: %v", err)
		ordersCallsMapping = make(map[string][]dao.KiranaBazarUserCall)
	}

	// Process order data - same as before...
	var orderDetails []ExtendedOrderDetail
	for _, order := range orders {
		// ... existing order processing logic remains the same ...
		// Parse shipping address
		var shippingAddress map[string]interface{}
		if err := json.Unmarshal([]byte(order.ShippingAddress), &shippingAddress); err != nil {
			log.Printf("Failed to parse shipping address for order %s: %v", order.ID, err)
			continue
		}

		// Parse payment meta
		var paidAmountProof []string
		var advanceTaken bool
		if order.PaymentMeta != nil {
			var paymentMeta map[string]interface{}
			if err := json.Unmarshal([]byte(order.PaymentMeta), &paymentMeta); err == nil {
				if proofData, ok := paymentMeta["paid_amount_proof"].([]interface{}); ok {
					for _, proof := range proofData {
						if proofStr, ok := proof.(string); ok {
							paidAmountProof = append(paidAmountProof, proofStr)
						}
					}
				}
				if taken, ok := paymentMeta["advance_taken"].(bool); ok {
					advanceTaken = taken
				}
			}
		}

		// Get order tags
		orderTags := orderTagsMap[fmt.Sprintf(`%d`, order.ID)]
		if orderTags == nil {
			orderTags = []string{}
		}

		// Helper function to safely get string from interface{}
		safeString := func(val interface{}) string {
			if val != nil {
				if str, ok := val.(string); ok {
					return str
				}
			}
			return ""
		}

		// Helper function to safely get float64 from interface{}
		safeFloat64 := func(val interface{}) float64 {
			if val != nil {
				if f, ok := val.(float64); ok {
					return f
				}
			}
			return 0
		}

		allowedActions := s.GetOrderActions(ctx, order.DisplayStatus, order.Seller, order.Courier, req.Email)

		orderDetail := ExtendedOrderDetail{
			ID: order.ID,
			User: UserDetail{
				ID:       order.UserID,
				Name:     order.UserName,
				ShopName: order.UserStoreName,
				Level:    order.UserLevel,
				Phone:    order.UserPhone,
			},
			AddressName:               safeString(shippingAddress["name"]),
			AddressCity:               safeString(shippingAddress["district"]),
			AddressLine:               safeString(shippingAddress["line"]),
			PostalCode:                safeString(shippingAddress["postal_code"]),
			State:                     safeString(shippingAddress["state"]),
			GST:                       safeString(shippingAddress["gst"]),
			AddressID:                 safeFloat64(shippingAddress["id"]),
			StoreName:                 safeString(shippingAddress["store_name"]),
			HouseNumber:               safeString(shippingAddress["house_number"]),
			Neighbourhood:             safeString(shippingAddress["neighbourhood"]),
			Village:                   safeString(shippingAddress["village"]),
			Landmark:                  safeString(shippingAddress["landmark"]),
			AddressTag:                safeString(shippingAddress["tag"]),
			AddressLine1:              safeString(shippingAddress["line1"]),
			AddressLine2:              safeString(shippingAddress["line2"]),
			Phone:                     safeString(shippingAddress["phone"]),
			Total:                     order.TotalAmount,
			OrderID:                   order.OrderID,
			OrderDate:                 order.CreatedAt.Format("2006-01-02T15:04:05.000Z"),
			OrderStatus:               order.OrderStatus,
			PaymentStatus:             order.PaymentStatus,
			DeliveryStatus:            order.DeliveryStatus,
			DisplayStatus:             order.DisplayStatus,
			ProcessingStatus:          order.ProcessingStatus,
			Note:                      order.Note,
			CallStatus:                order.CallStatus,
			UpdatedBy:                 order.UpdatedBy,
			UpdatedAt:                 formatTimestamp(order.UpdatedAt),
			CancelReason:              order.CancelReason,
			Seller:                    order.Seller,
			PaidAmount:                order.PaidAmount,
			PaidAmountProof:           paidAmountProof,
			AdvanceTaken:              advanceTaken,
			TrackingLink:              order.TrackingLink,
			OrderPlaced:               order.OrderPlaced,
			OrderConfirmed:            order.OrderConfirmed,
			OrderShipmentCreated:      order.OrderShipmentCreated,
			OrderDelivered:            order.OrderDelivered,
			OrderCancelled:            order.OrderCancelled,
			OrderReturned:             order.OrderReturned,
			IVRStatus:                 order.IVRStatus.String,
			IVRMeta:                   string(order.IVRMeta),
			ReturnedReason:            order.ReturnedReason,
			OrderTags:                 orderTags,
			TotalPlacedOrderAmount:    order.TotalPlacedOrderAmount,
			TotalConfirmedOrderAmount: order.TotalConfirmedOrderAmount,
			TotalDeliveredOrderAmount: order.TotalDeliveredOrderAmount,
			TotalReturnedOrderAmount:  order.TotalReturnedOrderAmount,
			TotalCancelledOrderAmount: order.TotalCancelledOrderAmount,
			TotalOrderPlaced:          order.TotalOrderPlaced,
			TotalOrderConfirmed:       order.TotalOrderConfirmed,
			TotalOrderDelivered:       order.TotalOrderDelivered,
			TotalOrderReturned:        order.TotalOrderReturned,
			TotalOrderCancelled:       order.TotalOrderCancelled,
			TicketStatus:              order.TicketStatus,
			TotalCallsCount:           len(ordersCallsMapping[fmt.Sprint(order.ID)]),
			Calls:                     ordersCallsMapping[fmt.Sprint(order.ID)],
			AllowedActions:            allowedActions,
		}

		if req.ExportCSV {
			orderDetail.Courier = order.Courier
			orderDetail.AWBNumber = order.AWBNumber
		}

		orderDetails = append(orderDetails, orderDetail)
	}

	return &GetOrdersExtendedResponse{
		Stats: stats,
		Data:  formatOrders(orderDetails),
	}, nil
}

func (s *Service) GetOrdersExtended(ctx context.Context, req *dto.GetOrdersExtendedRequest) (*GetOrdersExtendedResponse, error) {
	// Set default values
	if req.From == 0 {
		req.From = 12205754
	}
	if req.To == 0 {
		req.To = 3167965754000
	}
	if req.Limit == 0 {
		req.Limit = 10
	}
	if req.RecoDays == 0 {
		req.RecoDays = 5
	}

	// Handle reco status
	var recoStatus []string
	if req.RecoStatus == "ALL" {
		recoStatus = RECO_STATUS_IN
	} else if req.RecoStatus != "" {
		recoStatus = append(recoStatus, req.RecoStatus)
	}
	// Set default sellers and status
	if len(req.Seller) == 0 {
		req.Seller = []string{"zoff_foods"}
	}

	if len(req.Status) == 1 && req.Status[0] == processingstatus.EXCEPTION {
		req.Status = []string{processingstatus.SHIPMENT_CREATED}
		req.ProcessingStatus = []string{processingstatus.EXCEPTION}
	}
	// Filter tags by type
	ivrTags := filterTags(req.Tag, TAG_TYPES["IVR"])
	orderTags := filterTags(req.Tag, TAG_TYPES["ORDER"])
	notSetTags := filterTags(req.Tag, TAG_TYPES["NOT_SET"])

	// Convert timestamps to dates
	loc, _ := time.LoadLocation("Asia/Kolkata")
	fromTime := time.UnixMilli(req.From).In(loc)
	toTime := time.UnixMilli(req.To).In(loc)
	fromDate := time.Date(fromTime.Year(), fromTime.Month(), fromTime.Day(), 0, 0, 0, 0, loc).Format("2006-01-02 15:04:05")
	toDate := time.Date(toTime.Year(), toTime.Month(), toTime.Day(), 23, 54, 59, 0, loc).Format("2006-01-02 15:04:05")

	// Build common join query
	commonJoinQuery := `SELECT 
		DISTINCT ko.id,
		kois.ivr_status, 
		COALESCE(uos.total_placed_order_amount, 0) as total_placed_order_amount,
		COALESCE(uos.total_confirmed_order_amount, 0) as total_confirmed_order_amount,
		COALESCE(uos.total_delivered_order_amount, 0) as total_delivered_order_amount,
		COALESCE(uos.total_returned_order_amount, 0) as total_returned_order_amount,
		COALESCE(uos.total_cancelled_order_amount, 0) as total_cancelled_order_amount,
		COALESCE(uos.order_placed, 0) as total_order_placed,
		COALESCE(uos.confirmed_order, 0) as total_order_confirmed,
		COALESCE(uos.order_delivered, 0) as total_order_delivered,
		COALESCE(uos.returned_order, 0) as total_order_returned,
		COALESCE(uos.cancelled_order, 0) as total_order_cancelled,
		kois.ivr_meta, 
		ko.created_at, 
		kop.paid_amount, 
		kop.status AS payment_status,
		kop.amount AS total_amount,
		kop.payment_meta, 
		ko.seller, 
		ko.user_id, 
		ko.order_status, 
		ko.tracking_link,
		kcs.returned_reason, 
		ko.delivery_status, 
		ko.display_status, 
		ko.processing_status, 
		JSON_EXTRACT(kod.order_details, '$.shipping_address') AS shipping_address, 
		kod.order_id, 
		kcs.call_status,
		kcs.note,
		kcs.updated_at,
		kcs.updated_by,
		kcs.cancel_reason,
		kbr.order_confirmed,
		u.name as user_name,
		u.store_name as user_store_name,
		u.phone as user_phone,
		up.user_level,
		ct.status as ticket_status,
		kos.courier,
		kos.awb_number
		`

	if len(recoStatus) > 0 {
		commonJoinQuery += `, kbr.order_placed, 
			kbr.order_shipment_created, 
			kbr.order_delivered, 
			kbr.order_cancelled, 
			kbr.order_returned`
	}

	commonJoinQuery += ` FROM kiranabazar_orders ko 
		JOIN kiranabazar_order_details kod ON ko.id = kod.order_id 
		LEFT JOIN kiranabazar_call_status kcs ON ko.id = kcs.order_id 
		LEFT JOIN kiranabazar_order_payments kop ON ko.id = kop.order_id 
		LEFT JOIN kiranabazar_orders_ivr_status kois ON ko.id = kois.order_id
		LEFT JOIN user_order_stats uos ON ko.user_id = uos.user_id
		LEFT JOIN kc_bazar_reconciliation kbr ON ko.id = kbr.order_id
		LEFT JOIN kiranabazar_payment_gateway_orders kpgo ON ko.id = kpgo.kc_order_id
		LEFT JOIN users u ON ko.user_id = u.user_id
		LEFT JOIN users_profile up ON ko.user_id = up.user_id
		left join cs_tickets ct ON ko.id = ct.order_id
		LEFT JOIN kiranabazar_order_status kos ON ko.id = kos.id
		`

	if len(orderTags) > 0 || len(notSetTags) > 0 {
		commonJoinQuery += ` LEFT JOIN kiranabazar_order_tag_mapping otm ON otm.order_id = ko.id`
	}

	commonJoinQuery += ` WHERE 1=1`

	var query string
	if req.UserID != "" {
		query = fmt.Sprintf(`%s AND (ko.user_id = '%s' OR kod.customer_phone = '%s' OR kod.order_id = '%s')`,
			commonJoinQuery, req.UserID, req.UserID, req.UserID)
	} else if len(recoStatus) > 0 {
		combinedConditions := getCombinedConditionsExtended(recoStatus, "data")
		if combinedConditions != "" {
			query = fmt.Sprintf(`%s AND (%s)`, commonJoinQuery, combinedConditions)
		}
	} else {
		query = fmt.Sprintf(`%s AND ko.created_at BETWEEN '%s' AND '%s'`, commonJoinQuery, fromDate, toDate)
	}

	// Add additional filters
	if len(recoStatus) == 0 && len(req.Status) > 0 && !contains(req.Status, "OTHER") {
		statusList := "'" + strings.Join(req.Status, "','") + "'"
		query += fmt.Sprintf(` AND ko.display_status IN (%s)`, statusList)
	}
	if len(recoStatus) == 0 && len(req.ProcessingStatus) > 0 && !contains(req.ProcessingStatus, "OTHER") {
		processingStatusList := "'" + strings.Join(req.ProcessingStatus, "','") + "'"
		query += fmt.Sprintf(` AND ko.processing_status IN (%s)`, processingStatusList)
	}

	if len(req.Seller) > 0 {
		sellerList := "'" + strings.Join(req.Seller, "','") + "'"
		query += fmt.Sprintf(` AND ko.seller IN (%s)`, sellerList)
	}

	if len(ivrTags) > 0 {
		ivrTagsList := "'" + strings.Join(ivrTags, "','") + "'"
		query += fmt.Sprintf(` AND kois.ivr_status IN (%s)`, ivrTagsList)
	}

	if len(orderTags) > 0 {
		query += fmt.Sprintf(` AND otm.tag_id IN (%s) AND otm.is_active = 1`, strings.Join(orderTags, ", "))
	}

	if len(notSetTags) > 0 {
		query += ` AND otm.tag_id IS NULL AND kois.ivr_status IS NULL`
	}

	if req.OrderAmount.Max > 0 && req.OrderAmount.Min <= req.OrderAmount.Max {
		query += fmt.Sprintf(` AND kop.amount BETWEEN %f AND %f`, req.OrderAmount.Min, req.OrderAmount.Max)
	}

	query += ` AND (kpgo.gateway_status != 'initiated' OR kpgo.gateway_status IS NULL OR (kpgo.gateway_status = 'initiated' AND kpgo.created_at <= (CONVERT_TZ(NOW(), @@session.time_zone, '+05:30') - INTERVAL 1 HOUR)))`

	// Add ordering
	if len(recoStatus) > 0 {
		query += ` ORDER BY ko.created_at`
	} else {
		query += ` ORDER BY ko.created_at DESC`
	}

	if !req.ExportCSV {
		query += fmt.Sprintf(` LIMIT %d OFFSET %d`, req.Limit, req.Offset)
	}

	// Build count query
	countQuery := `SELECT 
		CASE 
			WHEN ko.processing_status in ('EXCEPTION') then ko.processing_status
			WHEN ko.display_status IN ('CANCELLED', 'DELIVERED', 'PLACED', 'RETURNED', 'PENDING_CONFIRMATION', 'IN_TRANSIT', 'CONFIRMED', 'SHIPMENT_CREATED', 'NDR') 
			THEN ko.display_status
			ELSE 'OTHER'
		END AS gen_status,
		COUNT(DISTINCT ko.id) AS count
	FROM kiranabazar_orders ko
	JOIN kiranabazar_order_details kod ON ko.id = kod.order_id
	LEFT JOIN kiranabazar_orders_ivr_status kois ON ko.id = kois.order_id
	LEFT JOIN kiranabazar_payment_gateway_orders kpgo ON ko.id = kpgo.kc_order_id`

	if len(orderTags) > 0 || len(notSetTags) > 0 {
		countQuery += ` LEFT JOIN kiranabazar_order_tag_mapping otm ON otm.order_id = ko.id`
	}

	countQuery += ` LEFT JOIN kiranabazar_call_status kcs ON ko.id = kcs.order_id`

	if req.OrderAmount.Max > 0 && req.OrderAmount.Min <= req.OrderAmount.Max {
		countQuery += ` LEFT JOIN kiranabazar_order_payments kop ON ko.id = kop.order_id`
	}

	if req.UserID != "" {
		countQuery += fmt.Sprintf(` WHERE (ko.user_id = '%s' OR kod.customer_phone = '%s' OR kod.order_id = '%s')`,
			req.UserID, req.UserID, req.UserID)
	} else if len(recoStatus) > 0 {
		countQuery += ` JOIN kc_bazar_reconciliation kbr ON ko.id = kbr.order_id WHERE 1=1`
		combinedConditions := getCombinedConditionsExtended(recoStatus, "count")
		if combinedConditions != "" {
			countQuery += fmt.Sprintf(` AND (%s)`, combinedConditions)
		}
	} else {
		countQuery += fmt.Sprintf(` WHERE ko.created_at BETWEEN '%s' AND '%s'`, fromDate, toDate)
	}

	// Add filters to count query
	if len(req.Seller) > 0 {
		sellerList := "'" + strings.Join(req.Seller, "','") + "'"
		countQuery += fmt.Sprintf(` AND ko.seller IN (%s)`, sellerList)
	}

	if len(ivrTags) > 0 {
		ivrTagsList := "'" + strings.Join(ivrTags, "','") + "'"
		countQuery += fmt.Sprintf(` AND kois.ivr_status IN (%s)`, ivrTagsList)
	}

	if req.OrderAmount.Max > 0 && req.OrderAmount.Min <= req.OrderAmount.Max {
		countQuery += fmt.Sprintf(` AND kop.amount BETWEEN %f AND %f`, req.OrderAmount.Min, req.OrderAmount.Max)
	}

	if len(orderTags) > 0 {
		countQuery += fmt.Sprintf(` AND otm.tag_id IN (%s)`, strings.Join(orderTags, ", "))
	}

	if len(notSetTags) > 0 {
		countQuery += ` AND otm.tag_id IS NULL AND kois.ivr_status IS NULL`
	}

	countQuery += ` AND (kpgo.gateway_status != 'initiated' OR kpgo.gateway_status IS NULL OR (kpgo.gateway_status = 'initiated' AND kpgo.created_at <= (CONVERT_TZ(NOW(), @@session.time_zone, '+05:30') - INTERVAL 1 HOUR)))`

	countQuery += `
	GROUP BY 
		CASE 
			WHEN ko.processing_status in ('EXCEPTION') THEN ko.processing_status
			WHEN ko.display_status IN ('CANCELLED', 'DELIVERED', 'PLACED', 'RETURNED', 'PENDING_CONFIRMATION', 'IN_TRANSIT', 'CONFIRMED', 'SHIPMENT_CREATED', 'NDR') 
			THEN ko.display_status
			ELSE 'OTHER'
		END
	ORDER BY 
		CASE 
			WHEN gen_status = 'OTHER' THEN 1
			ELSE 0
		END,
		gen_status`

	// Execute queries
	var wg sync.WaitGroup
	var orders []ExtendedOrderQueryResponse
	var counts []OrderStats
	var getOrdersErr, getCountsErr error

	wg.Add(2)

	go func() {
		defer wg.Done()
		_, getOrdersErr = s.repository.CustomQuery(&orders, query)
	}()

	go func() {
		defer wg.Done()
		_, getCountsErr = s.repository.CustomQuery(&counts, countQuery)
	}()

	wg.Wait()

	if getOrdersErr != nil {
		return nil, fmt.Errorf("failed to get order details: %w", getOrdersErr)
	}
	if getCountsErr != nil {
		return nil, fmt.Errorf("failed to get order counts: %w", getCountsErr)
	}

	// Process stats
	stats := make(map[string]int)
	for _, cc := range counts {
		if len(recoStatus) > 0 {
			if contains(RECO_STATUS_IN, cc.GenStatus) {
				stats[cc.GenStatus] = cc.Count
			} else if cc.GenStatus == "PENDING_CONFIRMATION" {
				stats["PENDING_CONFIRMATION"] = cc.Count
			} else {
				stats[cc.GenStatus] = 0
			}
		} else {
			stats[cc.GenStatus] = cc.Count
		}
	}

	if len(recoStatus) > 0 {
		placedCount := stats["PLACED"]
		pendingCount := stats["PENDING_CONFIRMATION"]
		stats["PLACED"] = placedCount + pendingCount
		delete(stats, "PENDING_CONFIRMATION")
	}

	// Get order IDs for tag mapping
	var orderIDs []string
	for _, order := range orders {
		orderIDs = append(orderIDs, fmt.Sprintf(`%d`, order.ID))
	}

	// Get concatenated tags for orders
	orderTagsMap, err := s.getOrdersConcatenatedTags(ctx, orderIDs)
	if err != nil {
		log.Printf("Failed to get order tags: %v", err)
		orderTagsMap = make(map[string][]string)
	}

	// Process order data
	var orderDetails []ExtendedOrderDetail
	for _, order := range orders {
		// Parse shipping address
		var shippingAddress map[string]interface{}
		if err := json.Unmarshal([]byte(order.ShippingAddress), &shippingAddress); err != nil {
			log.Printf("Failed to parse shipping address for order %s: %v", order.ID, err)
			continue
		}

		// Parse payment meta
		var paidAmountProof []string
		var advanceTaken bool
		if order.PaymentMeta != nil {
			var paymentMeta map[string]interface{}
			if err := json.Unmarshal([]byte(order.PaymentMeta), &paymentMeta); err == nil {
				if proofData, ok := paymentMeta["paid_amount_proof"].([]interface{}); ok {
					for _, proof := range proofData {
						if proofStr, ok := proof.(string); ok {
							paidAmountProof = append(paidAmountProof, proofStr)
						}
					}
				}
				if taken, ok := paymentMeta["advance_taken"].(bool); ok {
					advanceTaken = taken
				}
			}
		}

		// Get order tags
		orderTags := orderTagsMap[fmt.Sprintf(`%d`, order.ID)]
		if orderTags == nil {
			orderTags = []string{}
		}

		// Helper function to safely get string from interface{}
		safeString := func(val interface{}) string {
			if val != nil {
				if str, ok := val.(string); ok {
					return str
				}
			}
			return ""
		}

		// Helper function to safely get float64 from interface{}
		safeFloat64 := func(val interface{}) float64 {
			if val != nil {
				if f, ok := val.(float64); ok {
					return f
				}
			}
			return 0
		}

		allowedActions := s.GetOrderActions(ctx, order.DisplayStatus, order.Seller, order.Courier, req.Email)

		orderDetail := ExtendedOrderDetail{
			ID: order.ID,
			User: UserDetail{
				ID:       order.UserID,
				Name:     order.UserName,
				ShopName: order.UserStoreName,
				Level:    order.UserLevel,
				Phone:    order.UserPhone,
				// Note: You'll need to implement getUserDataFromUserID equivalent
				// For now, leaving these fields empty as they require additional service calls
			},
			AddressName:               safeString(shippingAddress["name"]),
			AddressCity:               safeString(shippingAddress["district"]),
			AddressLine:               safeString(shippingAddress["line"]),
			PostalCode:                safeString(shippingAddress["postal_code"]),
			State:                     safeString(shippingAddress["state"]),
			GST:                       safeString(shippingAddress["gst"]),
			AddressID:                 safeFloat64(shippingAddress["id"]),
			StoreName:                 safeString(shippingAddress["store_name"]),
			HouseNumber:               safeString(shippingAddress["house_number"]),
			Neighbourhood:             safeString(shippingAddress["neighbourhood"]),
			Village:                   safeString(shippingAddress["village"]),
			Landmark:                  safeString(shippingAddress["landmark"]),
			AddressTag:                safeString(shippingAddress["tag"]),
			AddressLine1:              safeString(shippingAddress["line1"]),
			AddressLine2:              safeString(shippingAddress["line2"]),
			Phone:                     safeString(shippingAddress["phone"]),
			Total:                     order.TotalAmount,
			OrderID:                   order.OrderID,
			OrderDate:                 order.CreatedAt.Format("2006-01-02T15:04:05.000Z"),
			OrderStatus:               order.OrderStatus,
			PaymentStatus:             order.PaymentStatus,
			DeliveryStatus:            order.DeliveryStatus,
			DisplayStatus:             order.DisplayStatus,
			ProcessingStatus:          order.ProcessingStatus,
			Note:                      order.Note,
			CallStatus:                order.CallStatus,
			UpdatedBy:                 order.UpdatedBy,
			UpdatedAt:                 formatTimestamp(order.UpdatedAt), // Convert to IST (UTC+5:30)
			CancelReason:              order.CancelReason,
			Seller:                    order.Seller,
			PaidAmount:                order.PaidAmount,
			PaidAmountProof:           paidAmountProof,
			AdvanceTaken:              advanceTaken,
			TrackingLink:              order.TrackingLink,
			OrderPlaced:               order.OrderPlaced,
			OrderConfirmed:            order.OrderConfirmed,
			OrderShipmentCreated:      order.OrderShipmentCreated,
			OrderDelivered:            order.OrderDelivered,
			OrderCancelled:            order.OrderCancelled,
			OrderReturned:             order.OrderReturned,
			IVRStatus:                 order.IVRStatus.String,
			IVRMeta:                   string(order.IVRMeta),
			ReturnedReason:            order.ReturnedReason,
			OrderTags:                 orderTags,
			TotalPlacedOrderAmount:    order.TotalPlacedOrderAmount,
			TotalConfirmedOrderAmount: order.TotalConfirmedOrderAmount,
			TotalDeliveredOrderAmount: order.TotalDeliveredOrderAmount,
			TotalReturnedOrderAmount:  order.TotalReturnedOrderAmount,
			TotalCancelledOrderAmount: order.TotalCancelledOrderAmount,
			TotalOrderPlaced:          order.TotalOrderPlaced,
			TotalOrderConfirmed:       order.TotalOrderConfirmed,
			TotalOrderDelivered:       order.TotalOrderDelivered,
			TotalOrderReturned:        order.TotalOrderReturned,
			TotalOrderCancelled:       order.TotalOrderCancelled,
			TicketStatus:              order.TicketStatus,
			AllowedActions:            allowedActions,
		}

		if req.ExportCSV {
			orderDetail.Courier = order.Courier
			orderDetail.AWBNumber = order.AWBNumber
		}

		orderDetails = append(orderDetails, orderDetail)
	}

	return &GetOrdersExtendedResponse{
		Stats: stats,
		Data:  formatOrders(orderDetails),
	}, nil
}

// final order formating
func formatOrders(orders []ExtendedOrderDetail) []ExtendedOrderDetail {
	formatedOrders := []ExtendedOrderDetail{}
	for _, order := range orders {
		if order.ProcessingStatus == processingstatus.EXCEPTION {
			order.DisplayStatus = order.ProcessingStatus
		}
		formatedOrders = append(formatedOrders, order)
	}
	return formatedOrders
}

// Helper function to format timestamp
func formatTimestamp(ts int64) string {
	if ts == 0 {
		return ""
	}
	t := time.UnixMilli(ts)
	return t.Format("2006-01-02T15:04:05.000Z")
}

// Helper function to format created_at string to ISO format
func formatCreatedAt(createdAt string) string {
	if createdAt == "" {
		return ""
	}
	// Parse the MySQL datetime format and convert to ISO format
	t, err := time.Parse("2006-01-02 15:04:05", createdAt)
	if err != nil {
		// If parsing fails, return as is
		return createdAt
	}
	return t.Format("2006-01-02T15:04:05.000Z")
}

func (s *Service) GetOrderActions(ctx context.Context, orderStatus, seller, courier, userEmail string) []string {
	actions := []string{}
	if canConfirmOrder(orderStatus) {
		actions = append(actions, "confirm_order")
	}
	if canCancelOrder(orderStatus, seller, courier, userEmail) {
		actions = append(actions, "cancel_order")
	}
	if canEditOrder(orderStatus) {
		actions = append(actions, "edit_order")
	}

	return actions
}

// canConfirmOrder checks if order can be confirmed
func canConfirmOrder(orderStatus string) bool {
	return orderStatus == displaystatus.PLACED || orderStatus == displaystatus.PENDING_CONFIRMATION
}

// canCancelOrder checks if order can be cancelled based on complex conditions
func canCancelOrder(orderStatus, seller, courier, userEmail string) bool {
	switch orderStatus {
	case displaystatus.PLACED, displaystatus.PENDING_CONFIRMATION:
		// Anyone can cancel if status is placed or pending confirmation
		return true

	case displaystatus.CONFIRMED:
		if userEmail == "" {
			return false
		}
		return canCancelConfirmedOrder(seller, userEmail)

	case displaystatus.SHIPMENT_CREATED:
		if userEmail == "" {
			return false
		}
		return canCancelShipmentCreatedOrder(seller, courier)

	default:
		// Can't cancel for any other status
		return false
	}
}

// canCancelConfirmedOrder handles cancellation logic for confirmed orders
func canCancelConfirmedOrder(seller, userEmail string) bool {

	// Check if seller is in restricted list
	isRestrictedSeller := slices.Contains(utils.RestrictedSellers, seller)

	if isRestrictedSeller {
		// For restricted sellers, only authorized users can cancel
		return slices.Contains(utils.OrderCancelAuthorizedUsers, userEmail)
	}

	// For non-restricted sellers, anyone can cancel
	return true
}

// canCancelShipmentCreatedOrder handles cancellation logic for shipment_created orders
func canCancelShipmentCreatedOrder(seller, courier string) bool {
	// Can't cancel if seller is nutraj or mangalam
	if seller == "nutraj" || seller == "mangalam" {
		return false
	}

	// For other sellers, can cancel only if courier is delhivery
	return strings.Contains(strings.ToLower(courier), "delhivery")
}

// canEditOrder checks if order can be edited
func canEditOrder(orderStatus string) bool {
	return !slices.Contains(utils.RestrictedOrderEditStatuses, orderStatus)
}
