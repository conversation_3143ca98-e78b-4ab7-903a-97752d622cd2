package service

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/repositories/sqlRepo"
	processingstatus "kc/internal/ondc/service/orderStatus/processingStatus"
	"kc/internal/ondc/service/products"
	"kc/internal/ondc/utils"
	"log"
	"math"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/divan/num2words"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type InvoiceGenerationResponse struct {
	Result Result `json:"result"`
}

type GetInvoiceAndOrderStatusFields struct {
	ExtInvoice       string `json:"ext_invoice" gorm:"column:ext_invoice"`
	ExtInvoiceNumber string `json:"ext_invoice_number" gorm:"column:ext_invoice_number"`
	Courier          string `json:"courier" gorm:"column:courier"`
	AwbNumber        string `json:"awb_number" gorm:"column:awb_number"`
}

type OrderDetailPrintLabel struct {
	Printinglabel string `json:"printing_label" gorm:"column:printing_label"`
}

// Package with invoice generation functionality

// GetOrderIDFromInvoice extracts the order ID from an invoice ID
func GetOrderIDFromInvoice(invoiceID string) (string, error) {
	// Split by hyphen and get the middle part
	parts := strings.Split(invoiceID, "-")
	if len(parts) != 3 || parts[0] != "INV" {
		return "", errors.New("invalid invoice ID format")
	}
	return parts[1], nil
}

// calculateTaxSplit calculates taxable value and tax amount from a tax-inclusive price
// using standardized two-decimal precision for GST compliance
func calculateTaxSplit(priceAfterDiscount float64, taxRate float64) (taxableValue float64, taxAmount float64) {
	if taxRate == 0 {
		return utils.RoundToTwoDecimals(priceAfterDiscount), 0.0
	}

	// Calculate using the standard formula: taxable = price / (1 + taxRate/100)
	taxable := priceAfterDiscount / (1 + taxRate/100)
	taxableRounded := utils.RoundToTwoDecimals(taxable)

	// Calculate tax amount from taxable value with standard GST precision
	taxAmount = utils.RoundToTwoDecimals(taxableRounded * taxRate / 100)

	return taxableRounded, taxAmount
}

// allocateDiscount distributes a total discount amount across multiple items proportionally
// with two-decimal precision for consistent financial calculations
func allocateDiscount(itemPrices []float64, totalDiscount float64) []float64 {
	totalPrice := 0.0
	for _, price := range itemPrices {
		totalPrice += price
	}

	// Handle edge case
	if totalPrice == 0 {
		return make([]float64, len(itemPrices))
	}

	allocatedDiscounts := make([]float64, len(itemPrices))
	allocatedTotal := 0.0

	// Allocate proportionally for all but the last item
	for i := 0; i < len(itemPrices)-1; i++ {
		proportion := itemPrices[i] / totalPrice
		itemTotalDiscount := utils.RoundToTwoDecimals(totalDiscount * proportion)
		allocatedDiscounts[i] = itemTotalDiscount
		allocatedTotal += itemTotalDiscount
	}

	// Assign the remaining discount to the last item to avoid rounding errors
	allocatedDiscounts[len(itemPrices)-1] = utils.RoundToTwoDecimals(totalDiscount - allocatedTotal)

	return allocatedDiscounts
}

func (s *Service) CreateExtInvoice(ctx context.Context, request dto.CreateExtInvoiceRequest) (dto.CreateExtInvoiceResponse, error) {
	// Parse order ID
	orderID, err := strconv.ParseInt(request.OrderID, 10, 64)
	if err != nil {
		return dto.CreateExtInvoiceResponse{}, err
	}

	// Fetch order information from repository
	orderInfo, err := GetOrderInfo(s.repository, orderID)
	if err != nil {
		return dto.CreateExtInvoiceResponse{}, err
	}

	orderDetails, err := GetOrderDetails(s.repository, request.OrderID)
	if err != nil {
		return dto.CreateExtInvoiceResponse{}, err
	}

	orderPayment, err := GetOrderPayment(s.repository, request.OrderID)
	if err != nil {
		return dto.CreateExtInvoiceResponse{}, err
	}

	seller, err := GetSellerInfo(s.repository, orderInfo.Seller)
	if err != nil {
		return dto.CreateExtInvoiceResponse{}, err
	}

	vendorShippingDetails := seller.GetShippingDetails()
	vendorInvoiceDetails := seller.GetInvoicingDetails()

	invoiceID := orderPayment.ExtInvoiceNumber

	if invoiceID == "" {
		// Generate unique invoice ID, TODO: take the sellercode from vendor info, but as of not using the seller code fisrt 3 characters
		invoiceID, err = s.CreateNewInvoiceNumber(context.Background(), strings.ToUpper(vendorInvoiceDetails.VendorCode[:3]))
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error in creating invoice number %v", err))
			return dto.CreateExtInvoiceResponse{}, err
		}
	}

	// Determine if IGST applies (interstate transaction)
	IGST := !strings.EqualFold(strings.ToUpper(vendorShippingDetails.State), strings.ToUpper(*orderDetails.ShippingAddress.State))

	invoiceRequest, itemQuantity, err := s.CreateInvoiceRequest(orderInfo, orderDetails, orderPayment, vendorShippingDetails, invoiceID, vendorInvoiceDetails, request.Courier, request.AWB, IGST)
	if err != nil {
		return dto.CreateExtInvoiceResponse{}, err
	}

	// Generate the invoice PDF
	invoiceURL, err := fetchInvoice(invoiceRequest)
	if err != nil {
		return dto.CreateExtInvoiceResponse{}, err
	}

	CGSTValue := 0.0
	SGSTValue := 0.0
	IGSTValue := 0.0

	if !IGST {
		CGSTValue = utils.RoundToTwoDecimals(invoiceRequest.Data.TaxSummary.TotalItemTax / 2.0)
		SGSTValue = utils.RoundToTwoDecimals(CGSTValue)
	} else {
		IGSTValue = invoiceRequest.Data.TaxSummary.TotalItemTax
	}
	return dto.CreateExtInvoiceResponse{
		InvoiceURL:    invoiceURL,
		InvoiceID:     invoiceID,
		InvoiceAmount: math.Round(invoiceRequest.Data.TaxSummary.TotalValue),
		CGST:          CGSTValue,
		SGST:          SGSTValue,
		IGST:          IGSTValue,
		BuyerGST:      invoiceRequest.Data.BilledTo.GSTIN,
		NoOfSkus:      len(invoiceRequest.Data.Items),
		ItemQuantity:  itemQuantity,
		Discount:      invoiceRequest.Data.TaxSummary.TotalDiscount,
	}, nil
}

func (s *Service) CreateInvoiceRequest(orderInfo dao.KiranaBazarOrder, orderDetails *dao.KiranaBazarOrderDetails, orderPayment dao.KiranaBazarOrderPayment, vendorShippingDetails dao.KiranaBazarSellerShippingDetails, invoiceID string, vendorInvoiceDetails dao.KiranaBazarSellerInvoiceDetails, Courier string, AWB string, IGST bool) (dto.InvoiceRequestObject, int, error) {

	// Handle payment calculations
	paidAmount := 0.0
	if orderPayment.PaidAmount != nil && *orderPayment.PaidAmount > 0 {
		paidAmount = *orderPayment.PaidAmount
	}

	orderValue := utils.RoundToTwoDecimals(orderDetails.GetOrderValue())

	// this is full discount bifurcation
	platformDiscount := utils.RoundToTwoDecimals(orderDetails.GetPlatformDiscountValue())
	paymentDiscount := utils.RoundToTwoDecimals(orderDetails.GetPaymentDiscountValue())
	platformCashback := utils.RoundToTwoDecimals(orderDetails.GetPlatformCashbackValue())
	sellerDiscount := utils.RoundToTwoDecimals(orderDetails.GetSellerDiscountValue())
	markdownDiscount := utils.RoundToTwoDecimals(orderDetails.GetMarkdownDiscountValue())
	totalDiscount := utils.RoundToTwoDecimals(platformDiscount + paymentDiscount + platformCashback + sellerDiscount)

	collectableAmount := orderValue
	if paidAmount > 0 && orderValue > 0 {
		collectableAmount = utils.RoundToTwoDecimals(orderValue - paidAmount)
	}
	if collectableAmount < 1.0 {
		collectableAmount = 0.0
	}

	// Prepare data for discount allocation
	itemPrices := make([]float64, 0, len(orderDetails.Cart))
	for _, item := range orderDetails.Cart {
		if item.Quantity == 0 {
			continue
		}
		var meta shared.KiranaBazarProductMeta
		if err := json.Unmarshal(item.Meta, &meta); err != nil {
			return dto.InvoiceRequestObject{}, 0, err
		}
		invoiceWholesale := meta.WholesaleRate
		if meta.BrandWholesaleRate != nil {
			invoiceWholesale = *meta.BrandWholesaleRate
		}
		if orderInfo.Seller == "apsara_tea" {
			invoiceWholesale = meta.WholesaleRate * float64(meta.PackSize)
		}

		invoicePackSize := meta.PackSize
		if orderInfo.Seller == "apsara_tea" {
			invoicePackSize = 1
		}
		quantity := float64(int(item.Quantity) * invoicePackSize)
		price := invoiceWholesale * quantity
		itemPrices = append(itemPrices, price)
	}

	// Allocate discounts proportionally with two-decimal precision
	allocatedPlatformDiscount := allocateDiscount(itemPrices, platformDiscount)
	allocatedPaymentDiscount := allocateDiscount(itemPrices, paymentDiscount)
	allocatedPlatformCashback := allocateDiscount(itemPrices, platformCashback)
	allocatedSellerDiscount := allocateDiscount(itemPrices, sellerDiscount)

	totalTaxable := 0.0
	totalTax := 0.0
	totalInvoiceValue := 0.0
	items := []dto.ItemInfo{}
	itemQuantity := 0
	// Process each item in the cart
	for idx, item := range orderDetails.Cart {
		if item.Quantity == 0 {
			continue
		}
		var meta shared.KiranaBazarProductMeta
		if err := json.Unmarshal(item.Meta, &meta); err != nil {
			return dto.InvoiceRequestObject{}, 0, err
		}

		product, ok := products.GetProductByID(item.ID)
		if !ok {
			return dto.InvoiceRequestObject{}, 0, errors.New("product not found")
		}

		// Get tax rate and HSN code
		tax := 0.0
		if product.MetaProperties.Tax != nil {
			tax = *product.MetaProperties.Tax
		}

		hsnCode := ""
		if product.MetaProperties.HSNCode != nil {
			hsnCode = *product.MetaProperties.HSNCode
		}
		invoiceWholesale := meta.WholesaleRate
		if meta.BrandWholesaleRate != nil {
			invoiceWholesale = *meta.BrandWholesaleRate
		}
		if orderInfo.Seller == "apsara_tea" {
			invoiceWholesale = invoiceWholesale * float64(meta.PackSize)
		}

		invoicePackSize := meta.PackSize
		if orderInfo.Seller == "apsara_tea" {
			invoicePackSize = 1
		}

		// Calculate item quantities and values with two-decimal precision
		quantity := float64(int(item.Quantity) * invoicePackSize)
		price := utils.RoundToTwoDecimals(invoiceWholesale * quantity)

		// Calculate price after discount with two-decimal precision
		priceAfterSellerDiscount := utils.RoundToTwoDecimals(price - allocatedSellerDiscount[idx])

		// Calculate tax components with two-decimal precision
		taxableValue, taxAmount := calculateTaxSplit(priceAfterSellerDiscount, tax)

		// Calculate the total with two-decimal precision to ensure it equals taxable + tax
		itemTotal := utils.RoundToTwoDecimals(taxableValue + taxAmount)

		itemMarkdownDiscount := orderDetails.GetItemLevelMarkdownDiscountValue(item.ID)

		// Add item to invoice items list
		items = append(items, dto.ItemInfo{
			Description:                     item.Name,
			SKU:                             product.Code,
			HSNCode:                         hsnCode,
			ItemPrice:                       utils.RoundToTwoDecimals(invoiceWholesale),
			Quantity:                        int(quantity),
			TotalItemValue:                  utils.RoundToTwoDecimals(priceAfterSellerDiscount - (allocatedPlatformDiscount[idx] + allocatedPaymentDiscount[idx] + itemMarkdownDiscount)),
			TotalItemValueWithoutTax:        utils.RoundToTwoDecimals(price),
			ItemTotalDiscount:               utils.RoundToTwoDecimals(allocatedPlatformDiscount[idx] + allocatedSellerDiscount[idx] + allocatedPaymentDiscount[idx] + allocatedPlatformCashback[idx] + itemMarkdownDiscount),
			ItemCummulativePlatformDiscount: utils.RoundToTwoDecimals(allocatedPlatformDiscount[idx] + allocatedPaymentDiscount[idx] + itemMarkdownDiscount),
			ItemPlatformDiscount:            utils.RoundToTwoDecimals(allocatedPlatformDiscount[idx]),
			ItemSellerDiscount:              utils.RoundToTwoDecimals(allocatedSellerDiscount[idx]),
			ItemPaymentDiscount:             utils.RoundToTwoDecimals(allocatedPaymentDiscount[idx]),
			ItemPlatformCashback:            utils.RoundToTwoDecimals(allocatedPlatformCashback[idx]),
			ItemMarkdownDiscount:            itemMarkdownDiscount,
			TaxableValue:                    taxableValue,
			TaxRate:                         fmt.Sprintf("%.1f%%", tax),
			TaxAmount:                       taxAmount,
			CGST:                            fmt.Sprintf("%.1f%%", tax/2),
			SGST:                            fmt.Sprintf("%.1f%%", tax/2),
		})

		// Accumulate totals for summary with two-decimal precision
		totalTaxable += taxableValue
		totalTax += taxAmount
		totalInvoiceValue += itemTotal
		itemQuantity += int(quantity)
	}

	// Round the total values to ensure consistency
	totalTaxable = utils.RoundToTwoDecimals(totalTaxable)
	totalTax = utils.RoundToTwoDecimals(totalTax)

	// Calculate final invoice value as the sum of item totals
	// This ensures mathematical consistency
	finalInvoiceValue := utils.RoundToTwoDecimals(totalTaxable + totalTax)

	// For amount in words, round to the nearest integer
	payableAmount := totalTaxable + totalTax - (platformDiscount + paymentDiscount + markdownDiscount)
	amountInWordsValue := int(math.Round(payableAmount))

	shippingAddress := orderDetails.ShippingAddress
	billingAddress := orderDetails.BillingAddress
	if billingAddress == nil {
		billingAddress = &shippingAddress
	}

	igstAmount := 0.0
	cgstAmount := 0.0
	sgstAmount := 0.0
	if IGST {
		igstAmount = totalTax
	}

	if !IGST {
		cgstAmount = totalTax / 2
		sgstAmount = totalTax / 2
	}

	// Create invoice request object
	invoiceRequest := dto.InvoiceRequestObject{
		Data: dto.InvoiceData{
			IGST: IGST,
			Invoice: dto.InvoiceInfo{
				Number:            invoiceID,
				Date:              time.Now().Format("2006-01-02"),
				OrderNumber:       fmt.Sprintf("KC_%06d", *orderInfo.ID),
				PaymentMode:       "Cash",
				CollectableAmount: collectableAmount,
				Carrier:           Courier,
				AWBNumber:         AWB,
				GeneratedAt:       time.Now().Format("2006-01-02 15:04:05"),
			},
			Seller: dto.SellerInfo{
				LegalName:        vendorInvoiceDetails.LegalName,
				Name:             vendorInvoiceDetails.DisplayName,
				Email:            vendorInvoiceDetails.Email,
				Phone:            vendorInvoiceDetails.Phone,
				CIN:              vendorInvoiceDetails.CINNumber,
				RegisteredOffice: vendorInvoiceDetails.RegisteredAddress,
				PAN:              vendorInvoiceDetails.PANNumber,
				GSTIN:            vendorInvoiceDetails.GSTNumber,
				State:            vendorInvoiceDetails.State,
				StateCode:        stateCodeMap[strings.ToUpper(vendorInvoiceDetails.State)],
			},
			ShippedFrom: dto.SellerInfo{
				Name:             vendorShippingDetails.VendorName,
				RegisteredOffice: vendorShippingDetails.Address1,
				State:            vendorShippingDetails.State,
				GSTIN:            vendorInvoiceDetails.GSTNumber,
				StateCode:        stateCodeMap[strings.ToUpper(vendorShippingDetails.State)],
			},
			BilledTo: dto.PartyInfo{
				Name:          *billingAddress.Name,
				Address:       *billingAddress.Line,
				State:         *billingAddress.State,
				StateCode:     stateCodeMap[strings.ToUpper(*billingAddress.State)],
				PlaceOfSupply: *billingAddress.State,
				GSTIN:         billingAddress.GST,
			},
			ShippedTo: dto.ShippingInfo{
				Name:          *shippingAddress.Name,
				Address:       *shippingAddress.Line,
				State:         *shippingAddress.State,
				StateCode:     stateCodeMap[strings.ToUpper(*shippingAddress.State)],
				PlaceOfSupply: *shippingAddress.State,
			},
			Items: items,
			TaxSummary: dto.TaxSummary{
				TotalItemTaxableValue:            totalTaxable,
				TotalItemTax:                     totalTax,
				TotalValue:                       payableAmount,
				InvoiceValue:                     math.Round(finalInvoiceValue),
				TotalPlatformCummulativeDiscount: utils.RoundToTwoDecimals(platformDiscount + paymentDiscount + markdownDiscount),
				AmountInWords:                    num2words.ConvertAnd(amountInWordsValue),
				PayableAmount:                    payableAmount,
				IGST:                             utils.RoundToTwoDecimals(igstAmount),
				CGST:                             utils.RoundToTwoDecimals(cgstAmount),
				SGST:                             utils.RoundToTwoDecimals(sgstAmount),

				//
				CashbackUsed:  platformCashback,
				AdvancePaid:   paidAmount,
				PaymentDue:    utils.RoundToTwoDecimals(math.Max(0, payableAmount-paidAmount-platformCashback)),
				TotalDiscount: totalDiscount,
				OrderValue:    orderValue,
			},
		},
	}
	return invoiceRequest, itemQuantity, nil
}

var stateCodeMap = map[string]string{
	"JAMMU AND KASHMIR":                    "01",
	"HIMACHAL PRADESH":                     "02",
	"PUNJAB":                               "03",
	"CHANDIGARH":                           "04",
	"UTTARAKHAND":                          "05",
	"HARYANA":                              "06",
	"DELHI":                                "07",
	"RAJASTHAN":                            "08",
	"UTTAR PRADESH":                        "09",
	"BIHAR":                                "10",
	"SIKKIM":                               "11",
	"ARUNACHAL PRADESH":                    "12",
	"NAGALAND":                             "13",
	"MANIPUR":                              "14",
	"MIZORAM":                              "15",
	"TRIPURA":                              "16",
	"MEGHALAYA":                            "17",
	"ASSAM":                                "18",
	"WEST BENGAL":                          "19",
	"JHARKHAND":                            "20",
	"ODISHA":                               "21",
	"CHHATTISGARH":                         "22",
	"MADHYA PRADESH":                       "23",
	"GUJARAT":                              "24",
	"DADRA & NAGAR HAVELI AND DAMAN & DIU": "26",
	"MAHARASHTRA":                          "27",
	"KARNATAKA":                            "29",
	"GOA":                                  "30",
	"LAKSHADWEEP":                          "31",
	"KERALA":                               "32",
	"TAMIL NADU":                           "33",
	"PUDUCHERRY":                           "34",
	"ANDAMAN AND NICOBAR ISLANDS":          "35",
	"TELANGANA":                            "36",
	"ANDHRA PRADESH":                       "37",
	"LADAKH":                               "38",
	"OTHER TERRITORY":                      "97",
	"CENTRE JURISDICTION":                  "99",
}

func fetchInvoice(request dto.InvoiceRequestObject) (string, error) {
	fmt.Println("calling external api")
	const (
		baseURL      = "https://asia-south1-op-d2r.cloudfunctions.net"
		endpoint     = "/invoice-gen"
		timeout      = 15 * time.Second
		maxRetries   = 3
		retryBackoff = 500 * time.Millisecond
	)

	var (
		resp       *http.Response
		respBody   []byte
		retryCount = 0
		lastErrMsg string
	)

	for retryCount <= maxRetries {
		payloadBytes, err := json.Marshal(request)
		if err != nil {
			return "", fmt.Errorf("failed to marshal request: %w", err)
		}

		// Create request
		client := &http.Client{Timeout: timeout}
		req, err := http.NewRequest(http.MethodPost, baseURL+endpoint, bytes.NewReader(payloadBytes))
		if err != nil {
			return "", fmt.Errorf("failed to create request: %w", err)
		}

		// Set headers
		req.Header.Set("Content-Type", "application/json")

		// Execute request
		resp, err = client.Do(req)

		if err == nil && resp.StatusCode == http.StatusOK {
			// Success - break out of retry loop
			defer resp.Body.Close()

			// Read response
			respBody, err = io.ReadAll(resp.Body)
			if err != nil {
				lastErrMsg = fmt.Sprintf("failed to read response body: %v", err)
				retryCount++
				time.Sleep(retryBackoff * time.Duration(retryCount))
				continue
			}

			respp := InvoiceGenerationResponse{}
			err = json.Unmarshal(respBody, &respp)
			if err != nil {
				return "", fmt.Errorf("failed to unmarshal response: %w", err)
			}

			// Log response for debugging
			log.Printf("PDF generation response: %s", string(respBody))

			return respp.Result.URL, nil
		}

		// Handle error case
		if resp != nil {
			respBody, _ = io.ReadAll(resp.Body)
			resp.Body.Close()
			lastErrMsg = fmt.Sprintf("unexpected status code: %d, body: %s", resp.StatusCode, string(respBody))
		} else {
			lastErrMsg = fmt.Sprintf("failed to execute request: %v", err)
		}

		// Retry logic
		retryCount++
		if retryCount <= maxRetries {
			log.Printf("Retrying invoice request (attempt %d of %d): %s", retryCount, maxRetries, lastErrMsg)
			time.Sleep(retryBackoff * time.Duration(retryCount))
		}
	}

	slack.SendSlackMessage(fmt.Sprintf("%s, Invoice generation failed after %d retries, last error: %s", request.Data.Invoice.OrderNumber, maxRetries, lastErrMsg))
	// All retries failed
	return "", fmt.Errorf("after %d retries, last error: %s", maxRetries, lastErrMsg)
}

// Invoice number format:
// KC[3-char Seller Code][Financial Year YYFY][7-digit Annual Sequential Number]
// Example: KCABC25260000001

var (
	ErrInvalidSellerCode     = errors.New("seller code must be exactly 3 characters")
	ErrInvalidFinancialYear  = errors.New("financial year must be exactly 4 digits in YYFY format")
	ErrInvalidSequenceNumber = errors.New("sequence number must be a positive integer up to 7 digits")
	ErrInvalidInvoiceNumber  = errors.New("invalid invoice number format")

	financialYearRegex = regexp.MustCompile(`^\d{4}$`)
	invoiceNumberRegex = regexp.MustCompile(`^KC[A-Z0-9]{3}\d{4}\d{7}$`)
)

// GenerateInvoiceNumber formats an invoice number using the provided components
func generateInvoiceNumber(sellerCode string, financialYear string, sequenceNumber int) (string, error) {
	// Validate seller code
	if len(sellerCode) != 3 {
		return "", ErrInvalidSellerCode
	}

	// Validate financial year
	if !financialYearRegex.MatchString(financialYear) {
		return "", ErrInvalidFinancialYear
	}

	// Validate sequence number
	if sequenceNumber < 1 || sequenceNumber > 9999999 {
		return "", ErrInvalidSequenceNumber
	}

	// Format the sequence number with leading zeros
	formattedSequence := fmt.Sprintf("%07d", sequenceNumber)

	// Construct the invoice number
	return fmt.Sprintf("KC%s%s%s", sellerCode, financialYear, formattedSequence), nil
}

// GetCurrentFinancialYear returns the current financial year in YYFY format
func GetCurrentFinancialYear() string {
	now := time.Now()
	currentMonth := int(now.Month())
	currentYear := now.Year()

	// Determine financial year start based on current month
	financialYearStart := currentYear
	financialYearStartMonth := 4
	if financialYearStartMonth < 1 || financialYearStartMonth > 12 {
		financialYearStartMonth = 4 // Default to April (common financial year start)
	}

	if currentMonth < financialYearStartMonth {
		financialYearStart = currentYear - 1
	}

	// Calculate the financial year end
	financialYearEnd := financialYearStart + 1

	// Format as YYFY (e.g., "2526" for financial year 2025-2026)
	return fmt.Sprintf("%02d%02d", financialYearStart%100, financialYearEnd%100)
}

// CreateNewInvoiceNumber generates a new invoice number for the given seller
func (s *Service) CreateNewInvoiceNumber(ctx context.Context, sellerCode string) (string, error) {
	// Get current financial year
	financialYear := GetCurrentFinancialYear()

	// Get next sequence number (with transaction for thread safety)
	sequenceNumber, err := getNextSequenceNumber(ctx, sellerCode, financialYear, s.repository)
	if err != nil {
		return "", fmt.Errorf("failed to get next sequence number: %w", err)
	}

	// Generate the invoice number
	return generateInvoiceNumber(sellerCode, financialYear, sequenceNumber)
}

// getNextSequenceNumber retrieves and increments the sequence counter in a transaction
func getNextSequenceNumber(ctx context.Context, sellerCode string, financialYear string, repo *sqlRepo.Repository) (int, error) {
	var nextSequence int

	// Use a transaction with pessimistic locking
	err := repo.Db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var counter dao.InvoiceCounter

		// Try to get existing counter with locking
		result := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
			Where("seller_code = ? AND financial_year = ?", sellerCode, financialYear).
			First(&counter)

		if result.Error != nil {
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				// Create new counter if it doesn't exist
				counter = dao.InvoiceCounter{
					SellerCode:         sellerCode,
					FinancialYear:      financialYear,
					LastSequenceNumber: 1,
				}
				if err := tx.Create(&counter).Error; err != nil {
					return err
				}
				nextSequence = 1
				return nil
			}
			return result.Error
		}

		// Increment existing counter
		nextSequence = counter.LastSequenceNumber + 1
		counter.LastSequenceNumber = nextSequence

		return tx.Save(&counter).Error
	})

	if err != nil {
		return 0, err
	}

	return nextSequence, nil
}

// ParseInvoiceNumber extracts components from a formatted invoice number
func ParseInvoiceNumber(invoiceNumber string) (map[string]string, error) {
	// Validate the invoice number format
	if !invoiceNumberRegex.MatchString(invoiceNumber) {
		return nil, ErrInvalidInvoiceNumber
	}

	sequenceNumber, err := strconv.Atoi(invoiceNumber[9:])
	if err != nil {
		return nil, err
	}

	return map[string]string{
		"prefix":         invoiceNumber[0:2],
		"sellerCode":     invoiceNumber[2:5],
		"financialYear":  invoiceNumber[5:9],
		"sequenceNumber": strconv.Itoa(sequenceNumber),
	}, nil
}

// ValidateInvoiceNumber checks if an invoice number is correctly formatted
func ValidateInvoiceNumber(invoiceNumber string) bool {
	return invoiceNumberRegex.MatchString(invoiceNumber)
}

func (s *Service) GetShipmentCreatedInvoices(ctx context.Context, req *dto.GetOrdersRequest) (*string, error) {
	if len(req.Status) > 0 && req.Status[0] != processingstatus.SHIPMENT_CREATED {
		return nil, fmt.Errorf("status should be SHIPMENT_CREATED")
	}
	req.Limit = 1000000
	req.Offset = 0
	// Preprocess the request for downloading the orders
	allOrders, err := s.GetOrders(ctx, req)
	if err != nil {
		return nil, err
	}
	allOrderIds := []int64{}
	for _, order := range allOrders.Data {
		oid, err := strconv.ParseInt(order.OrderID, 10, 64)
		if err != nil {
			return nil, err
		}
		allOrderIds = append(allOrderIds, oid)
	}
	query := fmt.Sprintf(`select ext_invoice from kiranabazar_order_payments kop where order_id in (%s)`, strings.Trim(strings.Join(strings.Fields(fmt.Sprint(allOrderIds)), ","), "[]"))
	type Payment struct {
		ExtInvoice string `json:"ext_invoice"`
	}
	payments := []Payment{}
	_, err = s.repository.CustomQuery(&payments, query)
	if err != nil {
		return nil, err
	}
	urls := []string{}
	for _, payment := range payments {
		if payment.ExtInvoice == "" {
			continue
		}
		urls = append(urls, payment.ExtInvoice)
	}

	zipResponse, err := s.ConvertUrlsToZip(ctx, urls)
	if err != nil {
		return nil, err
	}
	return &zipResponse.Result.DownloadUrl, nil
}

func (s *Service) CreateAndSaveExtInvoice(ctx context.Context, req *dto.GetOrderIdRequest) (*dto.B2BInvoiceGenWrapper, error) {

	if req.OrderID == "" {
		return nil, fmt.Errorf("order ID is required")
	}

	orderID, err := strconv.ParseInt(req.OrderID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid order ID format: %w", err)
	}

	query := fmt.Sprintf(`
	select
		kop.ext_invoice,
		kop.ext_invoice_number,
		kos.courier ,
		kos.awb_number
	from
		kiranaclubdb.kiranabazar_order_payments kop
	join kiranaclubdb.kiranabazar_order_status kos on
		kop.order_id = kos.id
	where  kop.order_id = %d`, orderID)

	var getInvoiceAndOrderStatus GetInvoiceAndOrderStatusFields

	_, err = s.repository.CustomQuery(&getInvoiceAndOrderStatus, query)

	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("error fetching invoice data: %w", err)
		}
		return nil, fmt.Errorf("invoice details not found for order ID: %d", orderID)
	}

	if getInvoiceAndOrderStatus.ExtInvoice != "" && getInvoiceAndOrderStatus.ExtInvoiceNumber != "" {
		return &dto.B2BInvoiceGenWrapper{
			InvoiceUrl:    getInvoiceAndOrderStatus.ExtInvoice,
			InvoiceNumber: getInvoiceAndOrderStatus.ExtInvoiceNumber,
		}, nil
	}

	if getInvoiceAndOrderStatus.Courier == "" && getInvoiceAndOrderStatus.AwbNumber == "" {
		return nil, fmt.Errorf("courier and AWB Number is required to generate invoice")
	}

	// Create the invoice request
	createExtInvoiceData, err := s.CreateExtInvoice(ctx, dto.CreateExtInvoiceRequest{
		OrderID: req.OrderID,
		Courier: getInvoiceAndOrderStatus.Courier,
		AWB:     getInvoiceAndOrderStatus.AwbNumber,
	})

	if err != nil {
		return nil, fmt.Errorf("error creating external invoice")
	}

	_, _, err = s.repository.Update(&dao.KiranaBazarOrderPayment{OrderID: &orderID}, &dao.KiranaBazarOrderPayment{ExtInvoice: createExtInvoiceData.InvoiceURL, ExtInvoiceNumber: createExtInvoiceData.InvoiceID})

	if err != nil {
		return nil, fmt.Errorf("error updating invoice details. Please try again later")
	}

	return &dto.B2BInvoiceGenWrapper{InvoiceUrl: createExtInvoiceData.InvoiceURL, InvoiceNumber: createExtInvoiceData.InvoiceID, OrderId: req.OrderID}, nil
}

func (s *Service) CreateAndSavePrintingLabel(ctx context.Context, req *dto.GetOrderIdRequest) (*dto.B2BShippingLabelGenWrapper, error) {

	if req.OrderID == "" {
		return nil, fmt.Errorf("order ID is required")
	}
	orderID, err := strconv.ParseInt(req.OrderID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid order ID format: %w", err)
	}

	orderDetail := OrderDetailPrintLabel{}
	_, err = s.repository.CustomQuery(&orderDetail, fmt.Sprintf("SELECT printing_label FROM kiranaclubdb.kiranabazar_order_details WHERE order_id = %d", orderID))

	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("error fetching order details")
		}
		return nil, fmt.Errorf("order details not found for order ID: %d", orderID)
	}

	if orderDetail.Printinglabel != "" {
		return &dto.B2BShippingLabelGenWrapper{OrderId: req.OrderID, PrintingLabel: orderDetail.Printinglabel}, nil
	}

	shippingLabelData, err := s.GenerateShippingLabel(ctx, &dto.GenerateShippingLabelRequest{
		Data: dto.GenerateShippingLabelRequestData{
			OrderID: req.OrderID,
		}},
	)

	if err != nil {
		return nil, fmt.Errorf("error generating shipping label %v", err)
	}
	if shippingLabelData == nil || shippingLabelData.Result.URL == "" {
		return nil, fmt.Errorf("shipping label generation failed or returned empty label")
	}

	_, _, err = s.repository.Update(&dao.KiranaBazarOrderDetail{OrderID: &orderID}, &dao.KiranaBazarOrderDetail{PrintingLabel: shippingLabelData.Result.URL})

	if err != nil {
		return nil, fmt.Errorf("error updating printing label details. Please try again later")
	}

	return &dto.B2BShippingLabelGenWrapper{OrderId: req.OrderID, PrintingLabel: shippingLabelData.Result.URL}, nil
}
