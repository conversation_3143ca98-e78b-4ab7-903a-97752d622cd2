package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/cart"
	productsService "kc/internal/ondc/service/products"
	userdetails "kc/internal/ondc/service/userDetails"
	"kc/internal/ondc/utils"
	"strings"
	"time"
)

func (s *Service) GetSuggestedProductsV2(ctx context.Context, request dto.GetSuggestedProductsRequest) (*dto.GetSuggestedProductsResponse, error) {
	if request.Data.Source == "" {
		request.Data.Source = "third_party_zoff_foods"
	}
	var err error
	appVersion := request.Meta.AppVersion

	kiranaBazarCart, err := cart.Get(ctx, request.UserID, request.Data.Seller)
	if err != nil {
		return nil, err
	}

	sellerItems, _ := kiranaBazarCart.Cart.Get()

	limit := 10
	if request.Meta.Limit != 0 {
		limit = request.Meta.Limit
	}

	userDetailsChannel := userdetails.AsyncFetchUserDetails(request.UserID, []string{userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS}, 1*time.Minute)

	widgets := []interface{}{}
	err = json.Unmarshal([]byte(utils.SUGGESTED_WIDGETS), &widgets)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal suggested widgets: %w", err)
	}

	sellerData, ok := brands.GetBrandMetaBySeller(request.Data.Seller)
	if ok {
		productsScreenNav := sellerData.Nav
		if len(widgets) > 0 {
			widget0Data, ok := widgets[0].(map[string]interface{})
			if ok {
				widget0Data["CTA"] = dto.Cta{
					Nav: productsScreenNav,
				}
			}
			// update widget[0]
			widgets[0] = widget0Data
		}
	}

	inCartItems := []string{}
	for _, item := range sellerItems {
		if err != nil {
			fmt.Println("err ", err)
		}
		inCartItems = append(inCartItems, item.ID)
	}

	userDetails := <-userDetailsChannel
	userCohortNames := []string{}
	var userGeography *userdetails.UserGeoData
	if userDetails.Data != nil && userDetails.Data.UserDynamicDetails != nil {
		userCohortNames = userDetails.Data.UserDynamicDetails.UserCohortNames
	}
	if userDetails.Data != nil {
		if userDetails.Data.UserGeography != nil {
			userGeography = userDetails.Data.UserGeography
		}
	}
	userCohortNames = append(userCohortNames, userdetails.GetUserDerivedCohorts(request.UserID, &[]string{request.Data.Seller}, userGeography)...)

	userPricingContext := productsService.PricingContext{
		UserID:     &request.UserID,
		Quantity:   0,
		UserCohort: &userCohortNames,
	}

	excludedProducts := request.Data.ExcludedProducts
	excludedProducts = append(excludedProducts, inCartItems...)
	var suggestedProducts []shared.SellerItems
	var products []dao.KiranaBazarProduct
	var productsData []*productsService.Product

	if len(inCartItems) == 0 {
		inCartItems = append(inCartItems, "1000000")
	}

	query := fmt.Sprintf(`
			SELECT 
				kp.*
			FROM 
				kiranabazar_products kp
			WHERE 
				kp.is_active = true
				AND kp.is_default = true
			    AND kp.product_type = "product"
				AND kp.id NOT IN (%s)
				AND kp.seller = "%s"
				AND kp.is_oos = false
				ORDER BY kp.popularity_value DESC, kp.rank 
				LIMIT %d`, strings.Join(excludedProducts, ","), request.Data.Seller, limit)

	s.repository.CustomQuery(&products, query)

	for _, product := range products {
		data, exist := productsService.GetProductByID(product.ID)
		if data == nil || exist == false {
			errMsg := fmt.Sprintf("product not found for code: %s", product.ID, err)
			fmt.Println(errMsg)
			continue
		}
		productsData = append(productsData, data)
	}

	productsSizeVariantMap := s.ProcessAndMakeVariantMap(productsData, "")
	suggestedProducts, err = GetProductsFromMap(productsSizeVariantMap, &request.Data.Exclusive, appVersion, "",
		false, &userPricingContext)
	if err != nil {
		return nil, err
	}

	suggestedProducts = filterProductsByCohort(suggestedProducts, userCohortNames)

	for i, _ := range suggestedProducts {
		// suggestedProducts[i].SizeVariants = nil
		suggestedProducts[i].Nav = nil
	}

	items := make([]interface{}, 0)
	cnt := 0
	for _, product := range suggestedProducts {
		items = append(items, product)
		cnt++
		if cnt >= 20 {
			break
		}
	}
	// type43Widgets := getProductsResponse(items, 20, []interface{}{}, "similar_products")
	type43Widgets := getProductsResponse(request.UserID, items, 20, []interface{}{}, request.Data.SubType)
	if request.Data.Exclusive {
		return &dto.GetSuggestedProductsResponse{
			Data: dto.SuggestedProductsData{
				SuggestedProducts: suggestedProducts,
				Widgets:           type43Widgets,
			},
		}, nil
	}

	type43WidgetsData, ok := type43Widgets.([]interface{})
	if ok {
		widgets = append(widgets, type43WidgetsData...)
	}

	// widgets2 := []interface{}{}
	// err = json.Unmarshal([]byte(utils.SUGGESTED_WIDGETS_2), &widgets2)
	// if err != nil {
	// 	return nil, fmt.Errorf("failed to unmarshal suggested widgets 2: %w", err)
	// }

	// widgets = append(widgets, widgets2...)

	response := dto.GetSuggestedProductsResponse{
		Data: dto.SuggestedProductsData{
			SuggestedProducts: suggestedProducts,
			Widgets:           widgets,
		},
	}
	return &response, nil
}

func (s *Service) GetSuggestedProducts(ctx context.Context, request dto.GetSuggestedProductsRequest) (interface{}, error) {
	if request.Data.Source == "" {
		request.Data.Source = "third_party_zoff_foods"
	}
	var err error

	userDetailsChannel := userdetails.AsyncFetchUserDetails(request.UserID, []string{userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS}, 1*time.Minute)
	kiranaBazarCart, err := cart.Get(ctx, request.UserID, request.Data.Seller)
	if err != nil {
		return nil, err
	}

	sellerItems, _ := kiranaBazarCart.Cart.Get()

	inCartItems := []string{}

	for _, item := range sellerItems {
		if err != nil {
			fmt.Println("err ", err)
		}
		inCartItems = append(inCartItems, item.ID)
	}

	// suggested products for the order placing
	suggestedProducts := func(inCartItems []string) []shared.SellerItems {
		var suggestedProducts []shared.SellerItems
		type kiranaBazarProductsAndCategories struct {
			dao.KiranaBazarProduct
			Source string `json:"source"`
		}
		if len(inCartItems) == 0 {
			inCartItems = append(inCartItems, "1000000")
		}

		excludedProducts := getAllOfferSkus()
		inCartItems = append(inCartItems, excludedProducts...)
		products := []kiranaBazarProductsAndCategories{}

		query := fmt.Sprintf(`
			select
				kiranabazar_products.*,
			from
				kiranabazar_products
			where
				kiranabazar_products.is_oos != true
				and kiranabazar_products.is_active = true
				and kiranabazar_products.id not in (%s)
				and kiranabazar_products.seller = "%s"
			order by `+"`rank`"+` , popularity_value desc
			limit 6`, strings.Join(inCartItems, ","), request.Data.Seller)

		s.repository.CustomQuery(&products, query)
		for _, product := range products {
			productImageUrls := []string{}
			byt, _ := json.Marshal(product.ImageUrls)
			json.Unmarshal(byt, &productImageUrls)

			source, exists := brands.GetSourceBySeller(product.Seller)

			if !exists {
				slack.SendSlackMessage("GetSuggestedProducts: No source found for seller : " + product.Seller)
				return nil
			}

			suggestedProducts = append(suggestedProducts, shared.SellerItems{
				SellerItemsData: shared.SellerItemsData{
					Type:        33,
					ProviderID:  source,
					Seller:      product.Seller,
					Name:        product.Name,
					ImageUrls:   productImageUrls,
					CategoryIds: []string{fmt.Sprintf("%d", product.CategoryID)},
					ID:          fmt.Sprintf("%d", product.ID),
					Price: shared.Price{
						Currency:       "rs",
						Value:          "10",
						MinimumValue:   "10",
						EstimatedValue: "10",
						ComputedValue:  "10",
						ListedValue:    "10",
						OfferedValue:   "10",
						MaximumValue:   "10",
					},
					Quantity:     0,
					Meta:         product.Meta,
					Manufacturer: product.Manufacturer,
				},
			})
		}
		return suggestedProducts
	}(inCartItems)

	userDetails := <-userDetailsChannel
	userCohortNames := []string{}
	if userDetails.Data != nil && userDetails.Data.UserDynamicDetails != nil {
		userCohortNames = userDetails.Data.UserDynamicDetails.UserCohortNames
	}

	filteredSuggestedProducts := filterProductsByCohort(suggestedProducts, userCohortNames)

	response := dto.GetSuggestedProductsResponse{
		Data: dto.SuggestedProductsData{
			SuggestedProducts: filteredSuggestedProducts,
		},
	}
	return response, nil
}
