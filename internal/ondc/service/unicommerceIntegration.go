package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/external/unicommerce"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/utils"
	"math"
	"time"

	"github.com/mixpanel/mixpanel-go"
)

func getShippingMethodCode() string {
	return "STD"
}

var MNAGALAM_NON_CLUBBED_SKUS = []string{"FGCMPOTH85", "FGCAM0053", "FGCAM0058", "FGCAM0062"}
var NUTRAJ_NON_CLUBBED_SKUS = []string{"8906019770267", "8906019770762", "8906019770793", "8906019771066", "8906019771097", "8906019771271", "8906019773008", "8906019776092", "8906019776917", "8906019778997", "8906019779505", "8906019779796", "8906019779826", "Nut-026", "Nut-025", "8906019779826-1"}

func saveUnicommerceRequestData(orderID int, requestByte []byte, response []byte, OMS string, repository *sqlRepo.Repository) error {
	compressedRequest := requestByte
	compressedResponse := response
	var err error
	if requestByte != nil {
		compressedRequest, err = utils.CompressJSON(requestByte)
		if err != nil {
			return err
		}
	}

	if response != nil {
		compressedResponse, err = utils.CompressJSON(response)
		if err != nil {
			return err
		}
	}

	unicommerceRequest := dao.KiranaBazarUnicommerceOrder{
		OrderID:       orderID,
		OrderRequest:  compressedRequest,
		OrderResponse: compressedResponse,
		OMS:           OMS,
		CreatedAt:     time.Now(),
	}
	_, err = repository.Save(&unicommerceRequest)
	return err
}

func getUnicommerceRequestOrderItems(oms string, orderID *int64, orderDetails dao.KiranaBazarOrderDetails, repository *sqlRepo.Repository) ([]unicommerce.UnicommerceSaleOrderItem, error) {
	orderItems := []unicommerce.UnicommerceSaleOrderItem{}
	items := orderDetails.Cart

	for _, j := range items {
		meta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(j.Meta, &meta)
		if err != nil {
			return orderItems, err
		}
		skuCode := getItemSkuCodeLabel(j.ID, repository)
		orderIdString := fmt.Sprintf("KC_%06d", *orderID)
		if NUTRAJ_UNICOMMERCE_DUPLICATE_ORDER[int(*orderID)] {
			orderIdString = fmt.Sprintf("KC_%06d-1", *orderID)
		}
		if UNICOMMERCE_DUPLICATE_ORDER[int(*orderID)] {
			orderIdString = fmt.Sprintf("KC_%06d_1", *orderID)
		}
		if includes(MNAGALAM_NON_CLUBBED_SKUS, skuCode) || includes(NUTRAJ_NON_CLUBBED_SKUS, skuCode) {
			for i := 0; i < int(j.Quantity*int32(meta.PackSize)); i++ {
				item := unicommerce.UnicommerceSaleOrderItem{
					ItemSku:            skuCode,
					ShippingMethodCode: getShippingMethodCode(),
					Code:               fmt.Sprintf("%s-%s-%d", orderIdString, j.ID, i),
					PacketNumber:       1,
					GiftWrap:           false,
					FacilityCode:       unicommerce.GetSellerUnicommerceFacility(oms),
					TotalPrice:         fmt.Sprintf("%f", meta.WholesaleRate),
					SellingPrice:       fmt.Sprintf("%f", meta.WholesaleRate),
					PrepaidAmount:      "0",
					Discount:           "0",
					ShippingCharges:    "0",
				}
				orderItems = append(orderItems, item)
			}
		} else {
			for i := 0; i < int(j.Quantity); i++ {
				item := unicommerce.UnicommerceSaleOrderItem{
					ItemSku:            skuCode,
					ShippingMethodCode: getShippingMethodCode(),
					Code:               fmt.Sprintf("%s-%s-%d", orderIdString, j.ID, i),
					PacketNumber:       1,
					GiftWrap:           false,
					FacilityCode:       unicommerce.GetSellerUnicommerceFacility(oms),
					TotalPrice:         fmt.Sprintf("%f", meta.WholesaleRate*float64(meta.PackSize)),
					SellingPrice:       fmt.Sprintf("%f", meta.WholesaleRate*float64(meta.PackSize)),
					PrepaidAmount:      "0",
					Discount:           "0",
					ShippingCharges:    "0",
				}
				orderItems = append(orderItems, item)
			}
		}
	}
	return orderItems, nil
}

func (s *Service) CreateUnicommerceSaleOrder(ctx context.Context, request *dto.AppUnicommerceCreateSaleOrderRequest) (*unicommerce.UnicommerceCreateSaleOrderResponse, error) {
	orderID := request.Data.OrderID
	if orderID == 0 {
		return nil, errors.New("order id is required")
	}
	orderDetails := dao.KiranaBazarOrderDetail{}
	_, err := s.repository.Find(map[string]interface{}{
		"order_id": orderID,
	}, &orderDetails)
	if err != nil {
		return nil, err
	}

	order := dao.KiranaBazarOrder{}
	_, err = s.repository.Find(map[string]interface{}{
		"id": orderID,
	}, &order)
	if err != nil {
		return nil, err
	}

	if order.Seller == "" {
		order.Seller = "mangalam"
	}

	unicommerceResponse := unicommerce.UnicommerceCreateSaleOrderResponse{}
	OMS := order.Seller
	if order.Seller == utils.PANCHVATI || order.Seller == utils.HUGS || order.Seller == utils.MOTHERS_KITCHEN || order.Seller == utils.APSARA_TEA || order.Seller == utils.ZOFF_FOODS || order.Seller == utils.GO_DESI {
		return &unicommerceResponse, nil
	}

	orderDetail := dao.KiranaBazarOrderDetails{}
	json.Unmarshal(orderDetails.OrderDetails, &orderDetail)

	items, err := getUnicommerceRequestOrderItems(OMS, orderDetails.OrderID, orderDetail, s.repository)
	if err != nil {
		return nil, err
	}

	orderid64 := int64(orderID)
	orderPayment := dao.KiranaBazarOrderPayment{}
	_, err = s.repository.Find(map[string]interface{}{
		"order_id": orderid64,
	}, &orderPayment)

	if err != nil {
		return nil, err
	}

	paidAmount := 0.0
	if orderPayment.PaidAmount != nil && *orderPayment.PaidAmount > 0 {
		paidAmount = *orderPayment.PaidAmount
	}

	// handling discount
	totalDiscount := orderDetail.GetDiscountValue()
	orderValue := orderDetail.GetOrderValue()
	isCod := true
	roundedOrderValue := math.Round(orderValue*100) / 100
	remainingAmount := roundedOrderValue - paidAmount
	if remainingAmount >= 0.0 && remainingAmount < 1.0 {
		isCod = false
	}

	//handling freebie discounts
	allCouponsKeyMap := getAllCouponsDesMap()
	for i, j := range orderDetail.BillBreakUp.DiscountPricing {
		value, exists := allCouponsKeyMap[j.Key]
		if orderDetail.BillBreakUp.DiscountPricing[i].Name != "" {
			orderDetail.BillBreakUp.DiscountPricing[i].Key = orderDetail.BillBreakUp.DiscountPricing[i].Name
		}
		if exists {
			// if value.Type == "FreeBie" {
			// 	items = append(items, dto.UnicommerceSaleOrderItem{
			// 		OrderItemID: fmt.Sprintf("KC_%06d-%s", orderID, value.Freebie.ProductID),
			// 		Sku:         getItemSkuCodeLabel(value.Freebie.ProductID, s.repository),
			// 		ProductName: getItemSkuNameLabel(value.Freebie.ProductID, s.repository),
			// 		Quantity:    value.Freebie.Quantity,
			// 		Price:       value.Freebie.Value,
			// 	})
			// }
			// here update the key in the discount pricing to english name
			if orderDetail.BillBreakUp.DiscountPricing[i].Name == "" {
				orderDetail.BillBreakUp.DiscountPricing[i].Key = value.Name
			}
		}
	}

	var gstNumber *string = nil
	if orderDetail.ShippingAddress.GST != "" {
		gstNum := orderDetail.ShippingAddress.GST
		gstNumber = &gstNum
	}

	if totalDiscount < 0 {
		totalDiscount = totalDiscount * -1.0
	}

	var collectableAmount *float64 = &orderValue
	remarks1 := ""
	remarks2 := ""
	if paidAmount > 0 && orderValue > 0 {
		collAmnt := orderValue - paidAmount
		collectableAmount = &collAmnt
		remarks1 = fmt.Sprintf(`Advance Amount: %.2f`, paidAmount)
	}
	if *collectableAmount < 1.0 {
		zeroValue := 0.0
		collectableAmount = &zeroValue
	}

	// paymentMethod := 2
	// if *collectableAmount == 0.0 {
	// 	paymentMethod = 5
	// }

	now := time.Now()
	formattedTime := now.Format("2006-01-02T15:04:05.000-0700")

	remarks2 = generateDiscountString(orderDetail.BillBreakUp.DiscountPricing)
	customFieldValues := []unicommerce.CustomField{
		{
			Name:  "Marketplace",
			Value: "Kirana Club",
		},
	}
	if orderDetail.BillingAddress == nil {
		orderDetail.BillingAddress = &orderDetail.ShippingAddress
	}
	omsChannel := unicommerce.GetSellerUnicommerChannel(OMS)

	orderIdString := fmt.Sprintf("KC_%06d", orderID)
	if NUTRAJ_UNICOMMERCE_DUPLICATE_ORDER[orderID] {
		orderIdString = fmt.Sprintf("KC_%06d-1", orderID)
	}
	if UNICOMMERCE_DUPLICATE_ORDER[int(orderID)] {
		orderIdString = fmt.Sprintf("KC_%06d_1", orderID)
	}
	unicommerceOrderRequest := unicommerce.UnicommerceCreateSaleOrderRequest{
		SaleOrder: unicommerce.SaleOrder{
			Code:                 orderIdString,
			DisplayOrderCode:     orderIdString,
			DisplayOrderDateTime: formattedTime,
			CustomerCode:         request.UserID,
			Channel:              omsChannel,
			CustomerGSTIN:        gstNumber,
			CashOnDelivery:       isCod,
			PaymentInstrument:    "CASH",
			AdditionalInfo:       fmt.Sprintf("%s | %s", remarks1, remarks2),
			ThirdPartyShipping:   false,
			Addresses:            getUnicommerceAddress(orderDetail.ShippingAddress, orderDetail.BillingAddress),
			BillingAddress: unicommerce.UnicommerceAddressReference{
				ReferenceID: fmt.Sprintf("%d", *orderDetail.BillingAddress.ID),
			},
			ShippingAddress: unicommerce.UnicommerceAddressReference{
				ReferenceID: fmt.Sprintf("%d", *orderDetail.ShippingAddress.ID),
			},
			SaleOrderItems:             items,
			CustomFieldValues:          customFieldValues,
			CurrencyCode:               "INR",
			TaxExempted:                false,
			CformProvided:              false,
			VerificationRequired:       false,
			TotalDiscount:              totalDiscount,
			TotalShippingCharges:       0,
			TotalCashOnDeliveryCharges: 0,
			TotalPrepaidAmount:         paidAmount,
			NotificationMobile:         *orderDetail.ShippingAddress.Phone,
		},
	}

	byt, err := json.Marshal(unicommerceOrderRequest)
	if err != nil {
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Order Rejected Unicommerce API", request.UserID, map[string]interface{}{
				"order_id":            orderID,
				"order_value":         orderValue,
				"unicommerce_request": unicommerceOrderRequest,
				"error":               err.Error(),
				"seller":              OMS,
				"ordering_module":     utils.MakeTitleCase(OMS),
			}),
		})

		combinedError := fmt.Sprintf("error marshalling unicommerce order request: %v", err)
		dbErr := saveUnicommerceRequestData(orderID, byt, nil, OMS, s.repository)
		if dbErr != nil {
			combinedError = fmt.Sprintf("%s, %v", combinedError, dbErr)
		}
		return nil, errors.New(combinedError)
	}

	unicommerceResponseByte, err, statusCode := unicommerce.CallUnicommerceAPI(OMS, "CREATE_SALE_ORDER", unicommerceOrderRequest, nil)
	if err != nil {
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Order Rejected Unicommerce API", request.UserID, map[string]interface{}{
				"order_id":            orderID,
				"order_value":         orderValue,
				"unicommerce_request": unicommerceOrderRequest,
				"error":               err.Error(),
				"seller":              OMS,
				"ordering_module":     utils.MakeTitleCase(OMS),
			}),
		})

		dbErr := saveUnicommerceRequestData(orderID, byt, nil, OMS, s.repository)
		if dbErr != nil {
			return nil, dbErr
		}
		return nil, err
	}

	json.Unmarshal(unicommerceResponseByte, &unicommerceResponse)
	kbuco := dao.KiranaBazarUnicommerceOrder{
		OrderID:       orderID,
		OrderResponse: unicommerceResponseByte,
		OrderRequest:  byt,
		CreatedAt:     time.Now(),
		OMS:           OMS,
	}
	_, err = s.repository.Save(&kbuco)
	if err != nil {
		return nil, err
	}

	if statusCode == nil || (statusCode != nil && *statusCode != 200) || !unicommerceResponse.Successful {
		ucresp := unicommerce.UnicommerceCreateSaleOrderResponse{}
		err = json.Unmarshal(kbuco.OrderResponse, &ucresp)
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Order Rejected Unicommerce API", request.UserID, map[string]interface{}{
				"order_id":        orderID,
				"order_value":     orderValue,
				"reason":          ucresp.Message,
				"seller":          order.Seller,
				"ordering_module": utils.MakeTitleCase(order.Seller),
			}),
		})
		slackMessage := fmt.Sprintf("%d Order Rejected Unicommerce API: %d, %s", statusCode, orderID, ucresp.Message)
		slack.SendSlackMessage(slackMessage)
		return nil, err
	}
	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Sent to Vendor OMS", request.UserID, map[string]interface{}{
			"order_id":        orderID,
			"order_value":     orderValue,
			"message":         unicommerceResponse.Message,
			"OMS":             "Unicommerce",
			"seller":          order.Seller,
			"ordering_module": utils.MakeTitleCase(order.Seller),
		}),
	})

	s.AddDataForReconciliation(ctx, &dto.AddReconciliationRequest{
		OrderID: int64(orderID),
		Data: []dto.StatusTimeStamp{
			{
				TimeStamp:   time.Now().UnixMilli(),
				OrderStatus: "order_confirmed",
			},
		},
		Service: "INTERNAL",
		OMS:     order.Seller,
	})

	return &unicommerceResponse, nil
}

/*
getAddress returns the address of user that is passed to unicommerce api, if shipping and billing addrs are same pass any one if not then pass both
the addrs
*/
func getUnicommerceAddress(ShippingAddress dao.UserAddress, BillingAddress *dao.UserAddress) []unicommerce.UnicommerceAddress {

	getStringValue := func(ptr *string) string {
		if ptr != nil {
			return *ptr
		}
		return ""
	}

	getUint64Value := func(ptr *uint64) uint64 {
		if ptr != nil {
			return *ptr
		}
		return 0
	}

	createUnicommerceAddress := func(addr dao.UserAddress) unicommerce.UnicommerceAddress {
		line1 := ""
		line2 := ""
		if addr.Line1 != "" && addr.Line1 != ", " {
			line1 = addr.Line1
		} else {
			line1 = getStringValue(addr.Line)
		}
		if addr.Line2 != "" && addr.Line2 != ", " {
			line2 = addr.Line2
		} else {
			line2 = getStringValue(addr.Line)
		}
		if addr.AlternatePhone != nil {
			line2 = fmt.Sprintf("%s, %s", line2, *addr.AlternatePhone)
		}

		return unicommerce.UnicommerceAddress{
			ID:           fmt.Sprintf("%d", getUint64Value(addr.ID)),
			Name:         getStringValue(addr.Name),
			AddressLine1: line1,
			AddressLine2: line2,
			City:         getStringValue(addr.District),
			State:        getStringValue(addr.State),
			Country:      "India",
			Pincode:      getStringValue(addr.PostalCode),
			Phone:        getStringValue(addr.Phone),
		}
	}

	addressesAreSame := func(addr1, addr2 dao.UserAddress) bool {
		if addr1.ID == nil || addr2.ID == nil {
			return true
		}
		return *addr1.ID == *addr2.ID
	}

	// Start with shipping address
	addresses := []unicommerce.UnicommerceAddress{
		createUnicommerceAddress(ShippingAddress),
	}

	// Add billing address if it exists and is different from shipping address
	if BillingAddress != nil && !addressesAreSame(ShippingAddress, *BillingAddress) {
		addresses = append(addresses, createUnicommerceAddress(*BillingAddress))
	}

	return addresses
}

func (s *Service) GetUnicommerceOrderDetails(ctx context.Context, orderID int) (*unicommerce.UnicommerceOrderDetailsResponse, error) {
	// Helper function to save data in KiranaBazarOrderStatus table
	orderInfo, err := GetOrderInfo(s.repository, int64(orderID))
	if err != nil {
		return nil, err
	}
	createdAt := time.Now()
	saveKiranaBazarOrderStatus := func(orderID int64, status []byte, errMsg *string) error {
		responseData := unicommerce.UnicommerceOrderDetailsResponse{}
		var awbNumber string
		var courier string
		if status != nil {
			json.Unmarshal(status, &responseData)
			if responseData.Successful && len(responseData.Errors) == 0 && len(responseData.SaleOrderDTO.ShippingPackages) > 0 {
				if responseData.SaleOrderDTO.ShippingPackages[0].TrackingNumber != "" {
					awbNumber = responseData.SaleOrderDTO.ShippingPackages[0].TrackingNumber
					courier = responseData.SaleOrderDTO.ShippingPackages[0].ShippingCourier
				}
			}
		}

		orderStatus := &dao.KiranaBazarOrderStatus{
			ID:        &orderID,
			Status:    status,
			Error:     errMsg,
			CreatedAt: &createdAt,
			UpdatedAt: time.Now(),
		}
		if awbNumber != "" {
			orderStatus.AWBNumber = &awbNumber
			orderStatus.AWBNumbers = []byte(fmt.Sprintf(`["%s"]`, awbNumber))
		}
		if courier != "" {
			orderStatus.Courier = &courier
		}
		_, err := s.repository.Save(orderStatus)
		return err
	}

	orderIdString := fmt.Sprintf("KC_%06d", orderID)
	if NUTRAJ_UNICOMMERCE_DUPLICATE_ORDER[orderID] {
		orderIdString = fmt.Sprintf("KC_%06d-1", orderID)
	}
	if UNICOMMERCE_DUPLICATE_ORDER[int(orderID)] {
		orderIdString = fmt.Sprintf("KC_%06d_1", orderID)
	}
	requestObject := map[string]interface{}{
		"code": orderIdString,
	}
	apiResponse, err, statusCode := unicommerce.CallUnicommerceAPI(orderInfo.Seller, "GET_ORDER_DETAILS", requestObject, nil)
	if err != nil {
		errMsg := err.Error()
		err := saveKiranaBazarOrderStatus(int64(orderID), nil, &errMsg)
		if err != nil {
			return nil, err
		}
		return nil, err
	}

	if statusCode == nil || (statusCode != nil && *statusCode != 200) {
		var errMsg string
		if statusCode == nil {
			errMsg = "status code error, nil"
		} else {
			errMsg = fmt.Sprintf("status code error with status %d", *statusCode)
		}
		err := saveKiranaBazarOrderStatus(int64(orderID), nil, &errMsg)
		if err != nil {
			return nil, err
		}
		return nil, errors.New(errMsg)
	}

	err = saveKiranaBazarOrderStatus(int64(orderID), apiResponse, nil)
	if err != nil {
		return nil, err
	}

	response := &unicommerce.UnicommerceOrderDetailsResponse{}
	err = json.Unmarshal(apiResponse, response)
	if err != nil {
		return nil, err
	}
	return response, nil
}
