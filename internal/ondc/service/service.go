package service

import (
	"kc/internal/ondc/cache"
	"kc/internal/ondc/external/zoho"
	"kc/internal/ondc/infrastructure/keyClient"
	"kc/internal/ondc/infrastructure/payments"
	"kc/internal/ondc/redis"
	"kc/internal/ondc/repositories/cacheRepo"
	"kc/internal/ondc/repositories/elasticRepo"
	"kc/internal/ondc/repositories/firebaseRepo"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	lspAllocation "kc/internal/ondc/service/LSPAllocation"
	"kc/internal/ondc/service/auth"
	awbService "kc/internal/ondc/service/awbMaster/service"
	"kc/internal/ondc/service/coupons"
	"kc/internal/ondc/service/inventory"
	"kc/internal/ondc/service/kcFinances/finance"
	"kc/internal/ondc/syncify"
	"net/http"

	tplgo "github.com/Kirana-Club/3pl-go"
	"github.com/Kirana-Club/oms-go"
)

type MyService interface {
}

type Service struct {
	FirebaseRepository   *firebaseRepo.FirebaseRepo
	repository           *sqlRepo.Repository
	KeyClient            keyClient.KeyClient
	httpClient           *http.Client
	Cache                *cacheRepo.Repository
	syncifyClient        syncify.Syncify
	Mixpanel             *mixpanelRepo.Repository
	ZohoClient           *zoho.Client
	GcpRedis             *redis.GcpRedis
	AzureRedis           *cacheRepo.Repository
	Payments             *payments.Service
	Coupons              *coupons.Repository
	AWBMaster            awbService.AWBMasterService
	ElasticSearchClient  *elasticRepo.ElasticSearchClient
	OmsService           *oms.Service
	TPLService           *tplgo.Service
	FinancialService     *finance.Service
	LSPAllocationService *lspAllocation.LSPAllocation
	Inventory            *inventory.InventoryService
	ReadOnlyRepository   *sqlRepo.Repository
	GenericCache         *cache.Cache
	AuthService          *auth.Service
}

func NewService(repository *sqlRepo.Repository, fbRepo *firebaseRepo.FirebaseRepo, client keyClient.KeyClient,
	redisAzure *cacheRepo.Repository, syncifyClient syncify.Syncify, mixpanel *mixpanelRepo.Repository,
	zohoClient *zoho.Client, redisGcp *redis.GcpRedis, paymentService *payments.Service, couponsService *coupons.Repository,
	awbMasterService awbService.AWBMasterService, ElasticSearchClient *elasticRepo.ElasticSearchClient,
	OmsService *oms.Service, TPLService *tplgo.Service, financialService *finance.Service,
	lspAllocationService *lspAllocation.LSPAllocation, inventoryService *inventory.InventoryService,
	readOnlyRepo *sqlRepo.Repository, genericCache *cache.Cache, authService *auth.Service) *Service {
	return &Service{
		repository:           repository,
		KeyClient:            client,
		httpClient:           &http.Client{},
		syncifyClient:        syncifyClient,
		Mixpanel:             mixpanel,
		ZohoClient:           zohoClient,
		FirebaseRepository:   fbRepo,
		GcpRedis:             redisGcp,
		AzureRedis:           redisAzure,
		Payments:             paymentService,
		Coupons:              couponsService,
		AWBMaster:            awbMasterService,
		ElasticSearchClient:  ElasticSearchClient,
		OmsService:           OmsService,
		TPLService:           TPLService,
		FinancialService:     financialService,
		LSPAllocationService: lspAllocationService,
		Inventory:            inventoryService,
		ReadOnlyRepository:   readOnlyRepo,
		GenericCache:         genericCache,
		AuthService:          authService,
	}
}

func (s *Service) PerformBusinessLogic() (string, error) {
	data := s.repository.GetDummyData()
	// Perform business logic using the retrieved data
	result := "Business logic result: " + data

	return result, nil
}
