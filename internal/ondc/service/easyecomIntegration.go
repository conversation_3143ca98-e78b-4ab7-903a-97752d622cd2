package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/external/easyecom"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/offers"
	"kc/internal/ondc/utils"
	"strconv"
	"strings"
	"time"

	"github.com/mixpanel/mixpanel-go"
)

// getItemSkuCode returns the sku code for the product.
func getItemSkuCode(productID string, repository *sqlRepo.Repository) string {
	//  TODO: @sanket please use cache here
	kcbazarProduct := dao.KiranaBazarProduct{}
	repository.Find(map[string]interface{}{
		"id": productID,
	}, &kcbazarProduct)
	return kcbazarProduct.Code
}

func generateDiscountString(discounts []shared.DiscountPricing) string {
	var filtered []string
	for _, discount := range discounts {
		if !discount.IsInternal && discount.Type != offers.OFFER_TYPES.PREPAID_AMOUNT {
			discountName := ""
			if discount.Name != "" {
				discountName = discount.Name
			} else {
				discountName = discount.Key
			}
			filtered = append(filtered, fmt.Sprintf("%s: %.2f", discountName, discount.TotalValue))
		}
	}
	if len(filtered) == 0 {
		return ""
	}
	return fmt.Sprintf("Offers: (%s)", strings.Join(filtered, " | "))
}

func getItemSkuCodeLabel(productID string, repository *sqlRepo.Repository) string {
	//  TODO: @sanket please use cache here
	kcbazarProduct := dao.KiranaBazarProduct{}
	repository.Find(map[string]interface{}{
		"id": productID,
	}, &kcbazarProduct)
	return kcbazarProduct.CodeLabel
}

func getItemSkuNameLabel(productID string, repository *sqlRepo.Repository) string {
	//  TODO: @sanket please use cache here
	kcbazarProduct := dao.KiranaBazarProduct{}
	repository.Find(map[string]interface{}{
		"id": productID,
	}, &kcbazarProduct)
	return kcbazarProduct.NameLabel
}

// returning the proper items format needed for easyecom api
func getOrderItems(orderID *int64, orderDetails dao.KiranaBazarOrderDetails, respository *sqlRepo.Repository) ([]dto.EasyEcomItem, error) {
	easyEcomItems := []dto.EasyEcomItem{}
	items := orderDetails.Cart

	for _, j := range items {
		meta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(j.Meta, &meta)
		if err != nil {
			return easyEcomItems, err
		}
		easyEcomItems = append(easyEcomItems, dto.EasyEcomItem{
			OrderItemID: fmt.Sprintf("KC_%06d-%s", *orderID, j.ID),
			Sku:         getItemSkuCodeLabel(j.ID, respository),
			ProductName: getItemSkuNameLabel(j.ID, respository),
			Quantity:    int(j.Quantity),
			Price:       meta.WholesaleRate * float64(meta.PackSize),
		})
	}

	// returning easyEcomItems
	return easyEcomItems, nil
}

// saving the easyecom request and response
func saveEasyEcomRequestData(orderID int, requestByte []byte, response []byte, OMS string, repository *sqlRepo.Repository) error {
	kbeeo := dao.KiranaBazarEasyEcomOrder{
		OrderID:       orderID,
		OrderResponse: response,
		CreatedAt:     time.Now(),
		OrderRequest:  requestByte,
		OMS:           OMS,
	}
	_, err := repository.Create(&kbeeo)
	if err != nil {
		return err
	}
	return nil
}

// CreateEasyEcomOrder is a logic layer for create easyecom order
func (s *Service) CreateEasyEcomOrder(ctx context.Context, request *dto.AppEasyEcomCreateOrderRequest) (*dto.EasyEcomCreateOrderResponse, error) {
	orderID := request.Data.OrderID
	if orderID == 0 {
		err := errors.New("invalid order id")
		return nil, err
	}
	orderDetails := dao.KiranaBazarOrderDetail{}
	_, err := s.repository.Find(map[string]interface{}{
		"order_id": orderID,
	}, &orderDetails)
	if err != nil {
		return nil, err
	}

	order := dao.KiranaBazarOrder{}
	_, err = s.repository.Find(map[string]interface{}{
		"id": orderID,
	}, &order)
	if err != nil {
		return nil, err
	}

	if order.Seller == "" {
		order.Seller = "zoff_foods"
	}

	easyEcomResponse := dto.EasyEcomCreateOrderResponse{}
	OMS := order.Seller

	orderDetail := dao.KiranaBazarOrderDetails{}

	json.Unmarshal(orderDetails.OrderDetails, &orderDetail)

	// getOrderItems sends the proper object for items
	items, err := getOrderItems(orderDetails.OrderID, orderDetail, s.repository)
	if err != nil {
		return nil, err
	}

	orderid64 := int64(orderID)
	orderPayment := dao.KiranaBazarOrderPayment{}
	_, err = s.repository.Find(map[string]interface{}{
		"order_id": orderid64,
	}, &orderPayment)

	if err != nil {
		return nil, err
	}

	paidAmount := 0.0
	if orderPayment.PaidAmount != nil && *orderPayment.PaidAmount > 0 {
		paidAmount = *orderPayment.PaidAmount
	}

	// handling discount
	totalDiscount := orderDetail.GetDiscountValue()
	orderValue := orderDetail.GetOrderValue()

	//handling freebie discounts
	allCouponsKeyMap := getAllCouponsDesMap()
	for i, j := range orderDetail.BillBreakUp.DiscountPricing {
		value, exists := allCouponsKeyMap[j.Key]
		// here update the key in the discount pricing to english name
		if orderDetail.BillBreakUp.DiscountPricing[i].Name != "" {
			orderDetail.BillBreakUp.DiscountPricing[i].Key = orderDetail.BillBreakUp.DiscountPricing[i].Name
		}
		if exists {
			// if value.Type == "FreeBie" {
			// 	items = append(items, dto.EasyEcomItem{
			// 		OrderItemID: fmt.Sprintf("KC_%06d-%s", orderID, value.Freebie.ProductID),
			// 		Sku:         getItemSkuCodeLabel(value.Freebie.ProductID, s.repository),
			// 		ProductName: getItemSkuNameLabel(value.Freebie.ProductID, s.repository),
			// 		Quantity:    value.Freebie.Quantity,
			// 		Price:       value.Freebie.Value,
			// 	})
			// }
			if orderDetail.BillBreakUp.DiscountPricing[i].Name == "" {
				orderDetail.BillBreakUp.DiscountPricing[i].Key = value.Name
			}
		}
	}

	line1 := ""
	line2 := ""
	var gstNumber *string = nil
	if orderDetail.ShippingAddress.Line1 != "" && orderDetail.ShippingAddress.Line1 != ", " {
		line1 = orderDetail.ShippingAddress.Line1
	} else {
		line1 = *orderDetail.ShippingAddress.Line
	}
	if orderDetail.ShippingAddress.Line2 != "" && orderDetail.ShippingAddress.Line2 != ", " {
		line2 = orderDetail.ShippingAddress.Line2
	} else {
		line2 = *orderDetail.ShippingAddress.Line
	}

	if orderDetail.ShippingAddress.AlternatePhone != nil {
		line2 = fmt.Sprintf("%s, %s", line2, *orderDetail.ShippingAddress.AlternatePhone)
	}

	if orderDetail.ShippingAddress.GST != "" {
		gstNum := orderDetail.ShippingAddress.GST
		gstNumber = &gstNum
	}

	if totalDiscount < 0 {
		totalDiscount = totalDiscount * -1.0
	}

	var collectableAmount *float64 = &orderValue
	remarks1 := ""
	remarks2 := ""
	if paidAmount > 0 && orderValue > 0 {
		collAmnt := orderValue - paidAmount
		collectableAmount = &collAmnt
		remarks1 = fmt.Sprintf(`Advance Amount: %.2f`, paidAmount)
	}
	if *collectableAmount < 1.0 {
		zeroValue := 0.0
		collectableAmount = &zeroValue
	}

	paymentMethod := 2
	if *collectableAmount == 0.0 {
		paymentMethod = 5
	}

	if orderDetail.BillingAddress == nil {
		orderDetail.BillingAddress = &orderDetail.ShippingAddress
	}
	remarks2 = generateDiscountString(orderDetail.BillBreakUp.DiscountPricing)
	billingPhoneNumber := *orderDetail.BillingAddress.Phone
	shippingPhoneNumber := *orderDetail.ShippingAddress.Phone

	// reversing 99999
	// if order.Seller == utils.ZOFF_FOODS {
	// 	billingPhoneNumber = "9999999999"  //  not sending phone number on easyecom for zoff_foods
	// 	shippingPhoneNumber = "9999999999" //  not sending phone number on easyecom for zoff_foods
	// }
	easyEcomPlaceOrderRequest := dto.EasyEcomCreateOrderRequest{
		OrderType:         easyecom.GetEasyEcomOrderType(),
		MarketplaceID:     easyecom.GetMarketPlaceID(OMS),
		OrderNumber:       fmt.Sprintf("KC_%06d", orderID),
		OrderDate:         time.Now().Format("2006-01-02 15:04:05"),
		ExpDeliveryDate:   time.Now().AddDate(0, 0, 3).Format("2006-01-02 15:04:05"),
		Remarks1:          remarks1,
		Remarks2:          remarks2,
		ShippingCost:      0,
		Discount:          totalDiscount,
		WalletDiscount:    0,
		PromoCodeDiscount: 0,
		PrepaidDiscount:   0,
		PaymentMode:       paymentMethod,
		PaymentGateway:    "",
		ShippingMethod:    1,
		IsMarketShipped:   0,
		Items:             items,
		CollectableAmount: collectableAmount,
		GstNumber:         gstNumber,
		Notes:             []dto.CustomField{},
		Customer: []dto.EasyEcomCustomer{
			{
				GSTNumber: gstNumber,
				Billing: dto.EasyEcomAddress{
					Name:         *orderDetail.BillingAddress.Name,
					AddressLine1: line1,
					AddressLine2: line2,
					PostalCode:   *orderDetail.BillingAddress.PostalCode,
					City:         *orderDetail.BillingAddress.District,
					State:        *orderDetail.BillingAddress.State,
					Country:      "India",
					Contact:      billingPhoneNumber,
					Email:        "",
					Latitude:     fmt.Sprint(orderDetail.BillingAddress.Latitude),
					Longitude:    fmt.Sprint(orderDetail.BillingAddress.Longitude),
				},
				Shipping: dto.EasyEcomAddress{
					Name:         *orderDetail.ShippingAddress.Name,
					AddressLine1: line1,
					AddressLine2: line2,
					PostalCode:   *orderDetail.ShippingAddress.PostalCode,
					City:         *orderDetail.ShippingAddress.District,
					State:        *orderDetail.ShippingAddress.State,
					Country:      "India",
					Contact:      shippingPhoneNumber,
					Email:        "",
					Latitude:     fmt.Sprint(orderDetail.ShippingAddress.Latitude),
					Longitude:    fmt.Sprint(orderDetail.ShippingAddress.Longitude),
				},
			},
		},
	}

	byt, err := json.Marshal(easyEcomPlaceOrderRequest)
	if err != nil {
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Order Rejected EasyEcom API", request.UserID, map[string]interface{}{
				"order_id":          orderID,
				"order_value":       orderValue,
				"easy_ecom_request": easyEcomPlaceOrderRequest,
				"error":             err,
				"seller":            order.Seller,
				"ordering_module":   utils.MakeTitleCase(order.Seller),
			}),
		})

		combinedError := fmt.Sprintf("error in marshalling easyecom request, %v ", err)
		dbErr := saveEasyEcomRequestData(orderID, byt, nil, OMS, s.repository)
		if dbErr != nil {
			combinedError = fmt.Sprintf("%s, %v", combinedError, dbErr)
		}
		return nil, errors.New(combinedError)
	}
	//fmt.Println("easyEcomPlaceOrderRequest", easyEcomPlaceOrderRequest)

	easyEcomAPIResponse, err, statusCode, retryCount := easyecom.CallEasyEcomAPI(OMS, "CREATE_ORDER", easyEcomPlaceOrderRequest, nil, 0)
	if err != nil || statusCode == nil || *statusCode != 200 {
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Order Rejected EasyEcom API", request.UserID, map[string]interface{}{
				"order_id":          orderID,
				"order_value":       orderValue,
				"easy_ecom_request": easyEcomPlaceOrderRequest,
				"error":             err,
				"status_code":       statusCode,
				"retry_count":       retryCount,
				"seller":            order.Seller,
				"ordering_module":   utils.MakeTitleCase(order.Seller),
			}),
		})

		combinedError := fmt.Sprintf("error in easyecom api after %d retry, %v, %v ", retryCount, err, statusCode)
		dbErr := saveEasyEcomRequestData(orderID, byt, easyEcomAPIResponse, OMS, s.repository)
		if dbErr != nil {
			combinedError = fmt.Sprintf("%s, %v", combinedError, dbErr)
		}
		return nil, errors.New(combinedError)
	}

	json.Unmarshal(easyEcomAPIResponse, &easyEcomResponse)

	kbeeo := dao.KiranaBazarEasyEcomOrder{
		OrderID:       orderID,
		OrderResponse: easyEcomAPIResponse,
		OrderRequest:  byt,
		CreatedAt:     time.Now(),
		OMS:           OMS,
	}
	_, err = s.repository.Create(&kbeeo)
	if err != nil {
		return nil, err
	}

	if statusCode == nil || *statusCode != 200 {
		eeresp := dto.EasyEcomCreateOrderResponse{}
		err = json.Unmarshal(kbeeo.OrderResponse, &eeresp)
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Order Rejected EasyEcom API", request.UserID, map[string]interface{}{
				"order_id":        orderID,
				"order_value":     orderValue,
				"reason":          eeresp.Message,
				"seller":          order.Seller,
				"ordering_module": utils.MakeTitleCase(order.Seller),
			}),
		})
		return nil, err
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Sent to Vendor OMS", request.UserID, map[string]interface{}{
			"order_id":        orderID,
			"order_value":     orderValue,
			"oms_order_id":    easyEcomResponse.Data.OrderID,
			"oms_invoice_id":  easyEcomResponse.Data.InvoiceID,
			"sub_order_id":    easyEcomResponse.Data.SuborderID,
			"OMS":             "EasyEcom",
			"seller":          order.Seller,
			"ordering_module": utils.MakeTitleCase(order.Seller),
		}),
	})

	s.AddDataForReconciliation(ctx, &dto.AddReconciliationRequest{
		OrderID: int64(orderID),
		Data: []dto.StatusTimeStamp{
			{
				TimeStamp:   time.Now().UnixMilli(),
				OrderStatus: "order_confirmed",
			},
		},
		Service: "INTERNAL",
		OMS:     order.Seller,
	})

	return &easyEcomResponse, nil

}

func (s *Service) EasyEcomWebhook(request string) {
	_, err := s.repository.Create(&dao.EasyEcomWebhookRequest{
		Request: []byte(request),
	})
	if err != nil {
		fmt.Println("err in easyecom webhook", err)
	}
}

func getInvoiceIDAndOMSFromOrderID(orderID int, repository *sqlRepo.Repository) (int, string, error) {
	// returning invoice id
	kkwco := dao.KiranaBazarEasyEcomOrder{}
	_, err := repository.Find(map[string]interface{}{
		"order_id": orderID,
	}, &kkwco)
	if err != nil {
		return 0, "", err
	}

	response := dto.EasyEcomCreateOrderResponse{}
	err = json.Unmarshal(kkwco.OrderResponse, &response)
	if err != nil {
		return 0, "", err
	}
	invoiceId, err := strconv.Atoi(response.Data.InvoiceID)
	if err != nil {
		return 0, "", err
	}
	return invoiceId, kkwco.OMS, nil
}

func (s *Service) GetEasyEcomOrderDetails(ctx context.Context, orderID int) (*dto.EasyEcomGetOrderDetailsResponse, error) {
	// Helper function to save data in KiranaBazarOrderStatus table
	createdAt := time.Now()
	saveKiranaBazarOrderStatus := func(orderID int64, status []byte, errMsg *string) error {
		responseData := dto.EasyEcomGetOrderDetailsResponse{}
		var awbNumber string
		var courier string
		if status != nil {
			json.Unmarshal(status, &responseData)
			if len(responseData.Data) > 0 {
				awbNumber = responseData.Data[0].AWBNumber
				courier = responseData.Data[0].Courier
			}
		}

		orderStatus := &dao.KiranaBazarOrderStatus{
			ID:         &orderID,
			Status:     status,
			Error:      errMsg,
			CreatedAt:  &createdAt,
			UpdatedAt:  time.Now(),
			AWBNumber:  &awbNumber,
			AWBNumbers: []byte(fmt.Sprintf(`["%s"]`, awbNumber)),
			Courier:    &courier,
		}
		_, err := s.repository.Save(orderStatus)
		return err
	}

	invoiceID, OMS, err := getInvoiceIDAndOMSFromOrderID(orderID, s.repository)
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"invoice_id": invoiceID,
	}
	apiResponse, err, statusCode, retryCount := easyecom.CallEasyEcomAPI(OMS, "GET_ORDER_DETAILS", nil, params, 0)
	int64OrderID := int64(orderID)

	if err != nil {
		errMsg := fmt.Sprintf("API call error: %v", err)
		saveErr := saveKiranaBazarOrderStatus(int64OrderID, nil, &errMsg)
		if saveErr != nil {
			return nil, saveErr
		}
		return nil, err
	}

	if statusCode == nil || *statusCode != 200 {
		var errMsg string
		if statusCode == nil {
			errMsg = fmt.Sprintf("status code error with %d retry, nil", retryCount)
		} else {
			errMsg = fmt.Sprintf("status code error with %d retry, %d", retryCount, *statusCode)
		}
		saveErr := saveKiranaBazarOrderStatus(int64OrderID, apiResponse, &errMsg)
		if saveErr != nil {
			return nil, saveErr
		}
		return nil, errors.New(errMsg)
	}

	saveErr := saveKiranaBazarOrderStatus(int64OrderID, apiResponse, nil)
	if saveErr != nil {
		return nil, saveErr
	}
	response := &dto.EasyEcomGetOrderDetailsResponse{}
	err = json.Unmarshal(apiResponse, response)
	if err != nil {
		return nil, err
	}
	return response, nil
}
