package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/sqlRepo"
	"time"

	"github.com/google/uuid"
)

func (s *Service) Confirm(ctx context.Context, req *dto.AppConfirmRequest) (*dto.AppConfirmResponse, error) {
	byt, _ := json.Marshal(req.Meta.Context)
	fmt.Println("byt = ", string(byt));
	msgId := getMessageID()
	identifier := fmt.Sprintf("%s%s", *req.Meta.Context.TransactionID, msgId)

	confirmRequestContext, err := getContext(&req.Meta.Context, CONFIRM, msgId)
	if err != nil {
		return nil, err
	}

	orderId := uuid.NewString()
	orderTime := time.Now()
	confirmMessage, err := getConfirmMessage(req, s.repository, orderId, orderTime)
	if err != nil {
		return nil, err
	}

	confirmRequest := dto.ConfirmRequest{
		Context: confirmRequestContext,
		Message: confirmMessage,
	}

	return s.handleONDCConfirmRequest(ctx, confirmRequest, identifier, *req.Meta.Context.TransactionID)

}

func (s *Service) handleONDCConfirmRequest(ctx context.Context, confirmRequest dto.ConfirmRequest, identifier, transactionID string) (*dto.AppConfirmResponse, error) {
	adjustedReqJSON, err := json.Marshal(confirmRequest)
	if err != nil {
		logger.Error(ctx, "Marshal adjusted request failed: %v", err)
		return nil, err
	}
	fmt.Println("CONFIRM request is", string(adjustedReqJSON))

	resp, err := s.syncingONDCRequest(ctx, adjustedReqJSON, identifier, CONFIRM)
	if err != nil {
		return nil, err
	}

	redisResp, ok := resp.(string)
	if !ok {
		logger.Error(ctx, "not able to typecast the redis resp")
		return nil, fmt.Errorf("not able to typecast the redis resp")
	}
	appConfirmResp := &dto.AppConfirmResponse{}
	err = json.Unmarshal([]byte(redisResp), appConfirmResp)
	if err != nil {
		logger.Error(ctx, "not able to unmarshal the redis resp, err is %s", err.Error())
		return nil, err
	}
	appConfirmResp.MessageID = confirmRequest.Context.MessageID
	return appConfirmResp, nil
}

func getConfirmMessage(req *dto.AppConfirmRequest, repo *sqlRepo.Repository, orderId string, orderTime time.Time) (*dto.ConfirmMessage, error) {
	address, _, err := getUserAddress(req.UserID, true, req.Data.AddressID, repo)
	if err != nil {
		return nil, err
	}

	return &dto.ConfirmMessage{
		Order: &dto.Order{
			Fulfillments: []dto.Fulfillment{
				{
					ID:   "1",
					Type: "Delivery",
					Stops: []dto.Stops{
						{
							Type: "end",
							Location: dto.Location{
								Gps:      fmt.Sprintf("%f,%f", address.Latitude, address.Longitude),
								AreaCode: *address.PostalCode,
								City: &dto.City{
									Name: *address.District,
								},
								State:   &dto.City{Name: *address.State},
								Country: &dto.Country{Code: "IND"},
								Address: fmt.Sprintf("%s, %s", *address.Name, *address.Line),
							},
							Contact: &dto.Contact{Phone: *address.Phone},
						},
					},
				},
			},
			Billing: &dto.Billing{
				Name:    *address.Name,
				Address: *address.Line,
				State: dto.City{
					Name: *address.State,
				},
				City: dto.City{
					Name: *address.District,
				},
				Phone:     *address.Phone,
				TaxNumber: address.GST,
			},
			ID:       orderId,
			State:    "Created",
			Provider: req.Data.Provider,
			Items:    req.Data.Items,
			Payment: []dto.Payment{
				{
					Type:                           "ON-FULFILLMENT",
					CollectedBy:                    "BPP",
					ONDCOrgSettlementBasis:         req.Data.Payment[0].ONDCOrgSettlementBasis,
					ONDCOrgSettlementWindow:        req.Data.Payment[0].ONDCOrgSettlementWindow,
					ONDCOrgWithholdingAmount:       req.Data.Payment[0].ONDCOrgWithholdingAmount,
					ONDCOrgBuyerAppFinderFeeType:   "percent",
					ONDCOrgBuyerAppFinderFeeAmount: "0.0",
					Status:                         "NOT-PAID",
					Params: &dto.PaymentParams{
						Currency: "INR",
						Amount:   10.0,
					},
					ONDCOrgSettlementDetails: []dto.PaymentSettlementDetails{{
						SettlementCounterparty:  "buyer-app",
						SettlementPhase:         "sale-amount",
						SettlementType:          "neft",
						SettlementBankAccountNo: "",
						SettlementIFSCCode:      "",
						BeneficiaryName:         "",
						BankName:                "",
					}},
				},
			},
			Quote:     req.Data.Quote,
			CreatedAt: &orderTime,
			UpdatedAt: &orderTime,
			Tags:      req.Data.Tags,
		},
	}, nil
}

func (s *Service) OnConfirm(ctx context.Context, req *dto.OnConfirmRequest) error {
	keyIdentifier := fmt.Sprintf("%s%s", *req.Context.TransactionID, *req.Context.MessageID)
	shipmentStart := dto.Stops{}

	for _, d := range req.Message.Order.Fulfillments[0].Stops {
		if d.Type == "start" {
			shipmentStart = d
		}
	}
	appConfirmResp := &dto.AppConfirmResponse{
		PaymentStatus: req.Message.Order.Payment[0].Status,
		ShipmentStart: shipmentStart,
		OrderState:    req.Message.Order.State,
		Order:         *req.Message.Order,
	}
	if req.Error != nil && req.Error.Code != nil {
		appConfirmResp.Error = dto.AppResponseError{
			Code:        req.Error.Code,
			Message:     &req.Error.Message,
			Description: &req.Error.Path,
			Type:        &req.Error.Type,
		}
	}

	marshalledResponse, _ := json.Marshal(appConfirmResp)
	fmt.Println(string(marshalledResponse))
	// responseAsArray := make([]*dto.AppConfirmResponse, 0)
	// responseAsArray = append(responseAsArray, appConfirmResp)
	_, err := s.Cache.Create(ctx, keyIdentifier, appConfirmResp)
	if err != nil {
		return err
	}
	return nil
}
