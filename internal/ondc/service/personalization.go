package service

import (
	"context"
	"fmt"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/cart"
	"kc/internal/ondc/service/products"
	"math/rand"
	"sort"
	"time"
)

func (s *Service) GetCartProducts(ctx context.Context, userID string, brand *string) ([]dto.CartProduct, error) {

	var brandsToProcess []string
	if brand != nil && *brand != "" {
		brandsToProcess = []string{*brand}
	} else {
		brandsToProcess = brands.GetAllBrands()
	}
	var productsData []dto.CartProduct
	for _, b := range brandsToProcess {
		cart, err := cart.Get(ctx, userID, b)
		if err != nil {
			return nil, fmt.Errorf("error getting cart from repository: %w", err)
		}
		cartProducts, err := cart.GetProducts()
		for _, p := range cartProducts {
			productData, exists := products.GetProductByID(p.ID)
			if !exists {
				continue
			}
			if productData.IsOOS != nil && *productData.IsOOS {
				continue
			}
			name := productData.MetaProperties.HindiName
			mrpNumber := productData.MetaProperties.MRPNumber
			markupMargin := productData.MetaProperties.MarkupMargin
			quantity := productData.MetaProperties.Quantity
			wholesaleRate := productData.MetaProperties.WholesaleRate

			productsData = append(productsData, dto.CartProduct{
				ID:              p.ID,
				Name:            name,
				MRPNumber:       mrpNumber,
				MarkupMargin:    markupMargin,
				Quantity:        quantity,
				WholesaleRate:   wholesaleRate,
				ImageURL:        p.ImageUrls[0],
				PopularityValue: p.PopularityValue,
				Seller:          p.Seller,
				ProductType:     p.ProductType,
			})
		}
	}
	sort.Slice(productsData, func(i, j int) bool {
		return productsData[i].PopularityValue > productsData[j].PopularityValue
	})
	if len(productsData) > 10 {
		productsData = productsData[:10]
	}
	rand.Seed(time.Now().UnixNano())
	rand.Shuffle(len(productsData), func(i, j int) {
		productsData[i], productsData[j] = productsData[j], productsData[i]
	})
	if len(productsData) > 6 {
		productsData = productsData[:6]
	}
	return productsData, nil
}
