package processingstatus

import (
	"kc/internal/ondc/service/orderStatus/constants"
)

var (
	PLACED               = constants.PLACED
	PENDING_CONFIRMATION = constants.PENDING_CONFIRMATION
	CONFIRMED            = constants.CONFIRMED
	PUSHED_TO_OMS        = constants.PUSHED_TO_OMS
	SHIPMENT_CREATED     = constants.SHIPMENT_CREATED
	SHIPMENT_MANIFESTED  = constants.SHIPMENT_MANIFESTED
	CANCELLED            = constants.CANCELLED
	ARCHIVED             = constants.ARCHIVED
	EXCEPTION            = constants.EXCEPTION
	// this needs human intervention
	OTHERS = constants.OTHERS
)
