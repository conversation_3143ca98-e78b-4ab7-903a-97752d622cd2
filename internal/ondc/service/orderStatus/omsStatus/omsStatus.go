package omsstatus

import (
	"kc/internal/ondc/service/orderStatus/constants"
)

var (
	SUCCESS_CREATED        = constants.SUCCESS_CREATED
	FAILED_NON_SERVICEABLE = constants.FAILED_NON_SERVICEABLE
	FAILED_OTHERS          = constants.FAILED_OTHERS
	FAILED_WEIGHT_ISSUE    = constants.FAILED_WEIGHT_ISSUE
	FAILED_API_5XX         = constants.FAILED_API_5XX
	FAILED_API_4XX         = constants.FAILED_API_4XX
)

func GetOMSStatusDescription(status string) string {
	switch status {
	case SUCCESS_CREATED:
		return "Order pushed to OMS successfully"
	case FAILED_NON_SERVICEABLE:
		return "Order not pushed to OMS due to non-serviceable location"
	case FAILED_OTHERS:
		return "Order not pushed to OMS due to other reasons"
	case FAILED_WEIGHT_ISSUE:
		return "Order not pushed to OMS due to weight issue"
	case FAILED_API_5XX:
		return "Order not pushed to OMS due to 5xx error"
	case FAILED_API_4XX:
		return "Order not pushed to OMS due to 4xx error"
	default:
		return "Order not pushed to OMS due to other reasons"
	}
}
