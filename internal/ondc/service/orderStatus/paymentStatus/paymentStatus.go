package paymentstatus

import (
	"kc/internal/ondc/service/orderStatus/constants"
)

var PAYMENT_METHOD = map[string]string{
	constants.COD:     constants.COD,
	constants.PREPAID: constants.PREPAID,
}

var (
	FULLY_PAID         = constants.FULLY_PAID
	PARTIALLY_PAID     = constants.PARTIALLY_PAID
	UNPAID             = constants.UNPAID
	PARTIALLY_REFUNDED = constants.PARTIALLY_REFUNDED
	REFUNDED           = constants.REFUNDED
	PREPAID            = constants.PREPAID
	FAILED             = constants.FAILED
)
