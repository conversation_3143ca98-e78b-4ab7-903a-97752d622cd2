package shipmentstatus

import (
	"kc/internal/ondc/service/orderStatus/constants"
)

var (
	IN_TRANSIT                       = constants.IN_TRANSIT                       // order in transit
	OUT_FOR_DELIVERY                 = constants.OUT_FOR_DELIVERY                 // order out for delivery
	DELIVERED                        = constants.DELIVERED                        // order delivered
	DELAYED                          = constants.DELAYED                          // order delayed
	FDA                              = constants.FDA                              // failed delivery attempt
	RETURNED                         = constants.RETURNED                         // order returned
	FDA_PAYMENT_NOT_READY            = constants.FDA_PAYMENT_NOT_READY            // payment not ready
	FDA_ADDRESS_ISSUE                = constants.FDA_ADDRESS_ISSUE                // address issue
	FDA_CUSTOMER_REFUSED             = constants.FDA_CUSTOMER_REFUSED             // customer refused
	FDA_UNREACHABLE                  = constants.FDA_UNREACHABLE                  // customer unreachable
	OTHERS                           = constants.OTHERS                           // others
	OUT_OF_DELIVERY_AREA             = constants.OUT_OF_DELIVERY_AREA             // out of delivery area
	FDA_CUSTOMER_UNAVAILABLE         = constants.FDA_CUSTOMER_UNAVAILABLE         // customer unavailable
	FDA_CUSTOMER_WANTS_OPEN_DELIVERY = constants.FDA_CUSTOMER_WANTS_OPEN_DELIVERY // customer wants open delivery
	LOST                             = constants.LOST                             // order lost
	RTO_RECEIVED                     = constants.RTO_RECEIVED                     // rto received at warehouse
	RTO_IN_TRANSIT                   = constants.RTO_IN_TRANSIT                   // rto in transit
	RTO_DELIVERED                    = constants.RTO_DELIVERED                    // rto delivered
	CANCELLED                        = constants.CANCELLED                        // order cancelled
)

// array to check FDA status
var ALL_FDA_STATUS = []string{
	FDA,
	FDA_PAYMENT_NOT_READY,
	FDA_ADDRESS_ISSUE,
	FDA_CUSTOMER_REFUSED,
	FDA_UNREACHABLE,
	FDA_CUSTOMER_UNAVAILABLE,
	FDA_CUSTOMER_WANTS_OPEN_DELIVERY,
}

func MapShipmentStatus(reason string) string {
	switch reason {
	case "Consignee refused to accept/order cancelled", "Consignee opened the package and refused to accept":
		return FDA_CUSTOMER_REFUSED
	case "Consignee Unavailable", "Delivery Rescheduled by Customer":
		return FDA_CUSTOMER_UNAVAILABLE
	case "Recipient wants open delivery":
		return FDA_CUSTOMER_WANTS_OPEN_DELIVERY
	case "Incomplete address & contact details", "Recipient wants delivery at a different address", "Bad/Incomplete Address":
		return FDA_ADDRESS_ISSUE
	case "Not attempted", "Code verified cancellation":
		return FDA
	case "Payment Mode / Amt Dispute":
		return FDA_PAYMENT_NOT_READY
	default:
		return FDA
	}
}
