package shipdelightstatus

import "kc/internal/ondc/service/orderStatus/constants"

var (
	STATUS_59  = constants.STATUS_59
	STATUS_99  = constants.STATUS_99
	STATUS_100 = constants.STATUS_100 // PICKUP DONE
	STATUS_900 = constants.STATUS_900 // PICKUP CANCELLED

	// In-Transit Status Codes
	STATUS_102 = constants.STATUS_102 // IN-TRANSIT
	STATUS_105 = constants.STATUS_105 // IN-TRANSIT
	STATUS_200 = constants.STATUS_200 // IN-TRANSIT
	STATUS_201 = constants.STATUS_201 // IN-TRANSIT
	STATUS_204 = constants.STATUS_204 // IN-TRANSIT
	STATUS_211 = constants.STATUS_211 // IN-TRANSIT
	STATUS_301 = constants.STATUS_301 // IN-TRANSIT
	STATUS_312 = constants.STATUS_312 // IN-TRANSIT

	// Out for Delivery Status Codes
	STATUS_305 = constants.STATUS_305 // OUT FOR DELIVERY

	// Delivery Status Codes
	STATUS_400 = constants.STATUS_400 // DELIVERED
	STATUS_401 = constants.STATUS_401 // RTO DELIVERED

	// Undelivered Status Codes
	STATUS_500 = constants.STATUS_500 // UNDELIVERED
	STATUS_501 = constants.STATUS_501 // UNDELIVERED
	STATUS_502 = constants.STATUS_502 // UNDELIVERED
	STATUS_503 = constants.STATUS_503 // UNDELIVERED
	STATUS_504 = constants.STATUS_504 // UNDELIVERED
	STATUS_505 = constants.STATUS_505 // UNDELIVERED
	STATUS_506 = constants.STATUS_506 // UNDELIVERED
	STATUS_507 = constants.STATUS_507 // UNDELIVERED
	STATUS_508 = constants.STATUS_508 // UNDELIVERED
	STATUS_509 = constants.STATUS_509 // UNDELIVERED
	STATUS_510 = constants.STATUS_510 // UNDELIVERED
	STATUS_511 = constants.STATUS_511 // UNDELIVERED
	STATUS_513 = constants.STATUS_513 // UNDELIVERED
	STATUS_514 = constants.STATUS_514 // UNDELIVERED
	STATUS_515 = constants.STATUS_515 // UNDELIVERED
	STATUS_516 = constants.STATUS_516 // UNDELIVERED
	STATUS_517 = constants.STATUS_517 // UNDELIVERED
	STATUS_518 = constants.STATUS_518 // UNDELIVERED
	STATUS_519 = constants.STATUS_519 // UNDELIVERED
	STATUS_520 = constants.STATUS_520 // UNDELIVERED
	STATUS_521 = constants.STATUS_521 // UNDELIVERED
	STATUS_522 = constants.STATUS_522 // UNDELIVERED
	STATUS_523 = constants.STATUS_523 // UNDELIVERED
	STATUS_524 = constants.STATUS_524 // UNDELIVERED
	STATUS_526 = constants.STATUS_526 // UNDELIVERED

	// Return to Origin (RTO) Status Codes
	STATUS_600 = constants.STATUS_600 // RTO INITIATED
	STATUS_601 = constants.STATUS_601 // RTO IN-TRANSIT
	STATUS_615 = constants.STATUS_615 // RTO UNDELIVERED
	STATUS_616 = constants.STATUS_616 // RTO UNDELIVERED
	STATUS_617 = constants.STATUS_617 // RTO UNDELIVERED
	STATUS_618 = constants.STATUS_618 // RTO UNDELIVERED
	STATUS_620 = constants.STATUS_620 // RTO OUT FOR DELIVERY
	STATUS_621 = constants.STATUS_621 // RTO UNDELIVERED
	STATUS_636 = constants.STATUS_636 // RTO UNDELIVERED
	STATUS_641 = constants.STATUS_641 // RTO UNDELIVERED
	STATUS_642 = constants.STATUS_642 // RTO UNDELIVERED
	STATUS_643 = constants.STATUS_643 // RTO UNDELIVERED
	STATUS_644 = constants.STATUS_644 // RTO UNDELIVERED

	// Exception Status Codes
	STATUS_901  = constants.STATUS_901  // LOST
	STATUS_902  = constants.STATUS_902  // SHIPMENT DAMAGE
	STATUS_951  = constants.STATUS_951  // REATTEMPT
	STATUS_1001 = constants.STATUS_1001 // UNATTEMPTED

	// Client-Specific Status Codes
	STATUS_I002 = constants.STATUS_I002 // CL-REATTEMPT
	STATUS_I003 = constants.STATUS_I003 // CL-RTO-INITIATED
	STATUS_I005 = constants.STATUS_I005 // CL-SELFCOLLECT

	// return status
	STATUS_R001  = constants.STATUS_R001
	STATUS_R002  = constants.STATUS_R002
	STATUS_R003  = constants.STATUS_R003
	STATUS_R004  = constants.STATUS_R004
	STATUS_R011  = constants.STATUS_R011
	STATUS_R085  = constants.STATUS_R085
	STATUS_R086  = constants.STATUS_R086
	STATUS_R087  = constants.STATUS_R087
	STATUS_R088  = constants.STATUS_R088
	STATUS_R100  = constants.STATUS_R100
	STATUS_R1000 = constants.STATUS_R1000
	STATUS_R101  = constants.STATUS_R101
	STATUS_R102  = constants.STATUS_R102
	STATUS_R103  = constants.STATUS_R103
	STATUS_R104  = constants.STATUS_R104
	STATUS_R106  = constants.STATUS_R106
	STATUS_R107  = constants.STATUS_R107
	STATUS_R109  = constants.STATUS_R109
	STATUS_R1100 = constants.STATUS_R1100
	STATUS_R1101 = constants.STATUS_R1101
	STATUS_R1102 = constants.STATUS_R1102
	STATUS_R1103 = constants.STATUS_R1103
	STATUS_R111  = constants.STATUS_R111
	STATUS_R112  = constants.STATUS_R112
	STATUS_R113  = constants.STATUS_R113
	STATUS_R114  = constants.STATUS_R114
	STATUS_R117  = constants.STATUS_R117
	STATUS_R118  = constants.STATUS_R118
	STATUS_R119  = constants.STATUS_R119
	STATUS_R120  = constants.STATUS_R120
	STATUS_R1200 = constants.STATUS_R1200
	STATUS_R1201 = constants.STATUS_R1201
	STATUS_R1202 = constants.STATUS_R1202
	STATUS_R1203 = constants.STATUS_R1203
	STATUS_R1204 = constants.STATUS_R1204
	STATUS_R1205 = constants.STATUS_R1205
	STATUS_R1206 = constants.STATUS_R1206
	STATUS_R1207 = constants.STATUS_R1207
	STATUS_R1208 = constants.STATUS_R1208
	STATUS_R121  = constants.STATUS_R121
	STATUS_R122  = constants.STATUS_R122
	STATUS_R123  = constants.STATUS_R123
	STATUS_R124  = constants.STATUS_R124
	STATUS_R125  = constants.STATUS_R125
	STATUS_R126  = constants.STATUS_R126
	STATUS_R127  = constants.STATUS_R127
	STATUS_R128  = constants.STATUS_R128
	STATUS_R129  = constants.STATUS_R129
	STATUS_R130  = constants.STATUS_R130
	STATUS_R131  = constants.STATUS_R131
	STATUS_R200  = constants.STATUS_R200
	STATUS_R201  = constants.STATUS_R201
	STATUS_R202  = constants.STATUS_R202
	STATUS_R203  = constants.STATUS_R203
	STATUS_R204  = constants.STATUS_R204
	STATUS_R205  = constants.STATUS_R205
	STATUS_R206  = constants.STATUS_R206
	STATUS_R300  = constants.STATUS_R300
	STATUS_R400  = constants.STATUS_R400
	STATUS_R600  = constants.STATUS_R600
	STATUS_R85   = constants.STATUS_R85
)

const (
	StatusForward        = "Forward"
	StatusRTO            = "RTO"
	StatusNDR            = "NDR"
	StatusReturnExchange = "Return-Exchange"
)

// all the statustype for shipdelight
var StatusTypes = []string{StatusForward, StatusRTO, StatusNDR, StatusReturnExchange}

func GetStatusType(statusCode string) string {
	// Map status codes to their types
	statusTypeMap := map[string]string{
		// Forward status codes
		STATUS_59:   "Forward",
		STATUS_99:   "Forward",
		STATUS_100:  "Forward",
		STATUS_102:  "Forward",
		STATUS_105:  "Forward",
		STATUS_200:  "Forward",
		STATUS_201:  "Forward",
		STATUS_204:  "Forward",
		STATUS_211:  "Forward",
		STATUS_301:  "Forward",
		STATUS_305:  "Forward",
		STATUS_312:  "Forward",
		STATUS_400:  "Forward",
		STATUS_500:  "Forward",
		STATUS_501:  "Forward",
		STATUS_502:  "Forward",
		STATUS_503:  "Forward",
		STATUS_504:  "Forward",
		STATUS_505:  "Forward",
		STATUS_506:  "Forward",
		STATUS_507:  "Forward",
		STATUS_508:  "Forward",
		STATUS_509:  "Forward",
		STATUS_510:  "Forward",
		STATUS_511:  "Forward",
		STATUS_513:  "Forward",
		STATUS_514:  "Forward",
		STATUS_515:  "Forward",
		STATUS_516:  "Forward",
		STATUS_517:  "Forward",
		STATUS_518:  "Forward",
		STATUS_519:  "Forward",
		STATUS_520:  "Forward",
		STATUS_521:  "Forward",
		STATUS_522:  "Forward",
		STATUS_523:  "Forward",
		STATUS_524:  "Forward",
		STATUS_526:  "Forward",
		STATUS_900:  "Forward",
		STATUS_901:  "Forward",
		STATUS_902:  "Forward",
		STATUS_1001: "Forward",

		// RTO status codes
		STATUS_401: "RTO",
		STATUS_600: "RTO",
		STATUS_601: "RTO",
		STATUS_615: "RTO",
		STATUS_616: "RTO",
		STATUS_617: "RTO",
		STATUS_618: "RTO",
		STATUS_620: "RTO",
		STATUS_621: "RTO",
		STATUS_636: "RTO",
		STATUS_641: "RTO",
		STATUS_642: "RTO",
		STATUS_643: "RTO",
		STATUS_644: "RTO",

		// NDR status codes
		STATUS_951:  "NDR",
		STATUS_I002: "NDR",
		STATUS_I003: "NDR",
		STATUS_I005: "NDR",

		// Return-Exchange status codes
		STATUS_R001:  "Return-Exchange",
		STATUS_R002:  "Return-Exchange",
		STATUS_R003:  "Return-Exchange",
		STATUS_R004:  "Return-Exchange",
		STATUS_R011:  "Return-Exchange",
		STATUS_R085:  "Return-Exchange",
		STATUS_R086:  "Return-Exchange",
		STATUS_R087:  "Return-Exchange",
		STATUS_R088:  "Return-Exchange",
		STATUS_R100:  "Return-Exchange",
		STATUS_R1000: "Return-Exchange",
		STATUS_R101:  "Return-Exchange",
		STATUS_R102:  "Return-Exchange",
		STATUS_R103:  "Return-Exchange",
		STATUS_R104:  "Return-Exchange",
		STATUS_R106:  "Return-Exchange",
		STATUS_R107:  "Return-Exchange",
		STATUS_R109:  "Return-Exchange",
		STATUS_R1100: "Return-Exchange",
		STATUS_R1101: "Return-Exchange",
		STATUS_R1102: "Return-Exchange",
		STATUS_R1103: "Return-Exchange",
		STATUS_R111:  "Return-Exchange",
		STATUS_R112:  "Return-Exchange",
		STATUS_R113:  "Return-Exchange",
		STATUS_R114:  "Return-Exchange",
		STATUS_R117:  "Return-Exchange",
		STATUS_R118:  "Return-Exchange",
		STATUS_R119:  "Return-Exchange",
		STATUS_R120:  "Return-Exchange",
		STATUS_R1200: "Return-Exchange",
		STATUS_R1201: "Return-Exchange",
		STATUS_R1202: "Return-Exchange",
		STATUS_R1203: "Return-Exchange",
		STATUS_R1204: "Return-Exchange",
		STATUS_R1205: "Return-Exchange",
		STATUS_R1206: "Return-Exchange",
		STATUS_R1207: "Return-Exchange",
		STATUS_R1208: "Return-Exchange",
		STATUS_R121:  "Return-Exchange",
		STATUS_R122:  "Return-Exchange",
		STATUS_R123:  "Return-Exchange",
		STATUS_R124:  "Return-Exchange",
		STATUS_R125:  "Return-Exchange",
		STATUS_R126:  "Return-Exchange",
		STATUS_R127:  "Return-Exchange",
		STATUS_R128:  "Return-Exchange",
		STATUS_R129:  "Return-Exchange",
		STATUS_R130:  "Return-Exchange",
		STATUS_R131:  "Return-Exchange",
		STATUS_R200:  "Return-Exchange",
		STATUS_R201:  "Return-Exchange",
		STATUS_R202:  "Return-Exchange",
		STATUS_R203:  "Return-Exchange",
		STATUS_R204:  "Return-Exchange",
		STATUS_R205:  "Return-Exchange",
		STATUS_R206:  "Return-Exchange",
		STATUS_R300:  "Return-Exchange",
		STATUS_R400:  "Return-Exchange",
		STATUS_R600:  "Return-Exchange",
		STATUS_R85:   "Return-Exchange",
	}

	// Return the status type if it exists in the map, otherwise return unknown
	if statusType, exists := statusTypeMap[statusCode]; exists {
		return statusType
	}

	return "Unknown"
}

var ValidStatusCodes = []string{
	STATUS_59,
	STATUS_99,
	STATUS_100,
	STATUS_102,
	STATUS_105,

	// Success (2xx)
	STATUS_200,
	STATUS_201,
	STATUS_204,
	STATUS_211,

	// Redirection (3xx)
	STATUS_301,
	STATUS_305,
	STATUS_312,

	// Client Error (4xx)
	STATUS_400,
	STATUS_401,

	// Server Error (5xx)
	STATUS_500,
	STATUS_501,
	STATUS_502,
	STATUS_503,

	STATUS_504,
	STATUS_505,
	STATUS_506,
	STATUS_507,

	STATUS_508,
	STATUS_509,
	STATUS_510,
	STATUS_511,

	STATUS_513,
	STATUS_514,
	STATUS_515,
	STATUS_516,

	STATUS_517,
	STATUS_518,
	STATUS_519,
	STATUS_520,

	STATUS_521,
	STATUS_522,
	STATUS_523,
	STATUS_524,

	STATUS_526,
	// Custom 6xx codes
	STATUS_600,
	STATUS_601,
	STATUS_615,
	STATUS_616,

	STATUS_617,
	STATUS_618,
	STATUS_620,
	STATUS_621,

	STATUS_636,
	STATUS_641,
	STATUS_642,
	STATUS_643,

	STATUS_644,
	// Custom 9xx codes
	STATUS_900,
	STATUS_901,
	STATUS_902,
	STATUS_951,
	// Special codes (1xxx and I00x)
	STATUS_1001,
	STATUS_I002,
	STATUS_I003,
	STATUS_I005,
}
