package delhiverystatus

import "kc/internal/ondc/service/orderStatus/constants"

// Delhivery Status Variables referencing constants
var (
	XUCI      = constants.XUCI      // <PERSON><PERSON><PERSON> uploaded
	XDDD3FD   = constants.XDDD3FD   // Out for delivery
	EOD11     = constants.EOD11     // Consignee Unavailable
	DLYDC109  = constants.DLYDC109  // Doubtful Order
	DLYMR118  = constants.DLYMR118  // Misrouted
	FMPUR101  = constants.FMPUR101  // Pickup scheduled
	XILL1F    = constants.XILL1F    // Bag Received at Facility
	XDWS      = constants.XDWS      // System weight captured
	XDLL2F    = constants.XDLL2F    // Bag Added To Trip
	ST114     = constants.ST114     // Call placed to consignee
	XUNEX     = constants.XUNEX     // Unexpected scan
	XAWD      = constants.XAWD      // Added to Dispatch
	XDBL1F    = constants.XDBL1F    // Added to Bag
	XILL2F    = constants.XILL2F    // Trip Arrived
	XSC       = constants.XSC       // Consignee to collect from branch
	EOD38     = constants.EOD38     // Delivered to consignee
	PL105     = constants.PL105     // Paid through link
	XIBD3F    = constants.XIBD3F    // Shipment Received at Facility
	DLYMPS101 = constants.DLYMPS101 // Shipment on hold due to delay of other associated MPS packages
	FMOFP101  = constants.FMOFP101  // Out for Pickup
	XOLL2F    = constants.XOLL2F    // Bag Removed
	DTUP219   = constants.DTUP219   // Pincode updated by Addfix
	DLYLH105  = constants.DLYLH105  // Vehicle delayed - Controllable
	EOD6      = constants.EOD6      // Consignee refused to accept/order cancelled
	ST105     = constants.ST105     // Reattempt - As per NDR instructions
	ST110     = constants.ST110     // Unlock
	DTUP207   = constants.DTUP207   // Re-routed
	DTUP205   = constants.DTUP205   // Package details changed by Delhivery
	DTUP210   = constants.DTUP210   // Seller cancelled the order
	EOD3      = constants.EOD3      // Delivery Rescheduled by Customer 2025-03-27 00:00:00.000
	DLYRG135  = constants.DLYRG135  // Short Shipment
	EOD37     = constants.EOD37     // Delivered at Mailroom/Security
	ST6W      = constants.ST6W      // Whatsapp verified cancellation
	DLYSU100  = constants.DLYSU100  // NTD Updated
	RT109     = constants.RT109     // Returned Due To Poor Packaging
	RDPD20    = constants.RDPD20    // Reject - Missing client label or invoice
	ST115     = constants.ST115     // Agent remark verified
	EOD104    = constants.EOD104    // Entry restricted area
	ST116     = constants.ST116     // Agent remark incorrect
	DTUP213   = constants.DTUP213   // Scan Bulk Update
	EOD111    = constants.EOD111    // Consignee opened the package and refused to accept
	EOD43     = constants.EOD43     // Consignee will collect from branch
	EOD15     = constants.EOD15     // Recipient wants delivery at a different address
	EOD74     = constants.EOD74     // Incomplete address & contact details
	ST108     = constants.ST108     // Maximum attempts reached
	XPPOM     = constants.XPPOM     // Shipment picked up
	XPNP      = constants.XPNP      // Shipment not received from client
	EOD86     = constants.EOD86     // Not attempted
	FMEOD152  = constants.FMEOD152  // Shipment not ready for pickup
	XPIOM     = constants.XPIOM     // Shipment Recieved at Origin Center
	DTUP231   = constants.DTUP231   // Center changed by system
	EOD40     = constants.EOD40     // Payment Mode / Amt Dispute
	RT108     = constants.RT108     // No client instructions to Reattempt
	DLYLH126  = constants.DLYLH126  // On hold. Destination unable to receive
	DOFF128   = constants.DOFF128   // Transit Delay
	DLYDG119  = constants.DLYDG119  // Shipment damaged
	CL106     = constants.CL106     // Damaged shipment to be attempted
	ST120     = constants.ST120     // Maximum attempts reached for self collect
	DTUP235   = constants.DTUP235   // Returned. Ageing limit crossed
	DTUP204   = constants.DTUP204   // Status Changed
	RDAC      = constants.RDAC      // RETURN Accepted
	RT110     = constants.RT110     // RTO due to poor packaging
	ST107     = constants.ST107     // Reattempt as per Client's instruction
	DLYB2B101 = constants.DLYB2B101 // Delivery rescheduled based on input from consignee
	EOD6O     = constants.EOD6O     // Code verified cancellation
	DLYDC102  = constants.DLYDC102  // Natural Disaster
	EOD69     = constants.EOD69     // Recipient wants open delivery
	ST102     = constants.ST102     // Bad/Incomplete Address
	CSCSL     = constants.CSCSL     // Received At Delhivery Hub
	EOD105    = constants.EOD105    // Shipment seized by consignee
	DLYLH151  = constants.DLYLH151  // Vehicle Accident
	XRWD      = constants.XRWD      // Removed From Dispatch
	DTUP203   = constants.DTUP203   // Package details changed by shipper
	DLYLH152  = constants.DLYLH152  // Delayed. No Entry
	DLYRG125  = constants.DLYRG125  // Delayed. Local disturbance/strike
	RT101     = constants.RT101     // Returned as per Client Instructions
	EOD83     = constants.EOD83     // Damaged/Used Product
	CS104     = constants.CS104     // Outbound against permanent connection [custody scan]
	CS101     = constants.CS101     // Inbound against permanent connection [custody scan]
	DLYHD007  = constants.DLYHD007  // Held for consolidation
	DTUP212   = constants.DTUP212   // MPS Shipment: Partial Delivery
	DTUP209   = constants.DTUP209   // Return Center Changed
	LFIN      = constants.LFIN      // On hold. Finance embargo
	DLYDC107  = constants.DLYDC107  // Office/Institute closed
	CL105     = constants.CL105     // Parcel status reverted
	LT100     = constants.LT100     // Shipment LOST
	XPROM     = constants.XPROM     // Shipment Picked Up at Origin Center
	SXIN      = constants.SXIN      // Scanned Through X-Ray
	XNSZ      = constants.XNSZ      // Non-serviceable location
	DLYDC101  = constants.DLYDC101  // Delayed due to weather conditions
	EOD36     = constants.EOD36     // Delivered to other as instructed by consignee
)
