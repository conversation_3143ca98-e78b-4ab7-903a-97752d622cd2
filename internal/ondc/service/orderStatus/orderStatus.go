package orderstatus

import (
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/orderStatus/constants"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	ekartstatus "kc/internal/ondc/service/orderStatus/ekart"
	omsstatus "kc/internal/ondc/service/orderStatus/omsStatus"
	processingstatus "kc/internal/ondc/service/orderStatus/processingStatus"
	shipdelightstatus "kc/internal/ondc/service/orderStatus/shipdelightStatus"
	shipmentstatus "kc/internal/ondc/service/orderStatus/shipmentStatus"
	"slices"
	"strings"
)

var SqlRespository *sqlRepo.Repository

type OrderStatusResponse struct {
	DisplayStatus    string `json:"display_status"`
	ShipmentStatus   string `json:"shipment_status"`
	ProcessingStatus string `json:"processing_status"`
	PaymentStatus    string `json:"payment_status"`
}

func ifNotEmpty(value, defaultValue string) string {
	if value != "" {
		return value
	}
	return defaultValue
}

func GetOrderStatusFromOrderID(orderID int64) (*OrderStatusResponse, error) {
	kcOrder := dao.KiranaBazarOrder{}
	_, err := SqlRespository.Find(map[string]interface{}{
		"id": orderID,
	}, &kcOrder)
	if err != nil {
		return nil, err
	}
	orderStatus := MapOrderStatus(*kcOrder.OrderStatus, "", OrderStatusResponse{})
	return &OrderStatusResponse{
		DisplayStatus:    orderStatus.DisplayStatus,
		ShipmentStatus:   orderStatus.ShipmentStatus,
		ProcessingStatus: orderStatus.ProcessingStatus,
	}, nil
}

func GetOrderStatusFromDB(orderID int64) (*OrderStatusResponse, error) {
	kcOrder := dao.KiranaBazarOrder{}
	_, err := SqlRespository.Find(map[string]interface{}{
		"id": orderID,
	}, &kcOrder)
	if err != nil {
		return nil, err
	}
	return &OrderStatusResponse{
		DisplayStatus:    kcOrder.DisplayStatus,
		ShipmentStatus:   kcOrder.DeliveryStatus,
		ProcessingStatus: kcOrder.ProcessingStatus,
		PaymentStatus:    kcOrder.PaymentStatus,
	}, nil
}

func updateShipdelightOrderStatus(response *OrderStatusResponse, currentStatus string) OrderStatusResponse {

	switch currentStatus {
	// Pending/Initial States
	case constants.STATUS_99, constants.STATUS_900, constants.STATUS_59:
		{
			response.DisplayStatus = displaystatus.SHIPMENT_CREATED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		}

	// In Transit States
	case constants.STATUS_100, constants.STATUS_102, constants.STATUS_105, constants.STATUS_200,
		constants.STATUS_201, constants.STATUS_211, constants.STATUS_301, constants.STATUS_517, constants.STATUS_518:
		{
			response.DisplayStatus = displaystatus.IN_TRANSIT
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		}

	// Network Delay
	case constants.STATUS_504, constants.STATUS_204:
		{
			response.ShipmentStatus = shipmentstatus.DELAYED
			response.DisplayStatus = displaystatus.IN_TRANSIT
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		}

	// Out for Delivery
	case constants.STATUS_305:
		{
			response.DisplayStatus = displaystatus.IN_TRANSIT
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.OUT_FOR_DELIVERY
		}

	// Non-Delivery Report (NDR)
	case constants.STATUS_312, constants.STATUS_506, constants.STATUS_511,
		constants.STATUS_513, constants.STATUS_514, constants.STATUS_519,
		constants.STATUS_520, constants.STATUS_521, constants.STATUS_523,
		constants.STATUS_524, constants.STATUS_526, constants.STATUS_951,
		constants.STATUS_1001, constants.STATUS_I002, constants.STATUS_I003,
		constants.STATUS_I005:
		response.DisplayStatus = displaystatus.NDR
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.FDA

	// Special NDR Cases with specific FDA reasons
	case constants.STATUS_500:
		{
			response.DisplayStatus = displaystatus.NDR
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.FDA_CUSTOMER_REFUSED
		}

	case constants.STATUS_501, constants.STATUS_503, constants.STATUS_505:
		{
			response.DisplayStatus = displaystatus.NDR
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.FDA_ADDRESS_ISSUE
		}

	case constants.STATUS_502:
		{
			response.DisplayStatus = displaystatus.NDR
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.OUT_OF_DELIVERY_AREA
		}

	case constants.STATUS_507:
		{
			response.DisplayStatus = displaystatus.NDR
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.FDA_PAYMENT_NOT_READY
		}

	case constants.STATUS_508, constants.STATUS_509:
		{
			response.DisplayStatus = displaystatus.NDR
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.FDA_UNREACHABLE
		}

	case constants.STATUS_515, constants.STATUS_516:
		{
			response.DisplayStatus = displaystatus.NDR
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.FDA_CUSTOMER_UNAVAILABLE
		}

	case constants.STATUS_522:
		{
			response.DisplayStatus = displaystatus.NDR
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.FDA_CUSTOMER_WANTS_OPEN_DELIVERY
		}

	// Delivered
	case constants.STATUS_400:
		{
			response.DisplayStatus = displaystatus.DELIVERED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.DELIVERED
		}

	case constants.STATUS_401:
		{
			response.DisplayStatus = displaystatus.RETURNED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.RTO_DELIVERED
		}

	// Return States
	case constants.STATUS_600, constants.STATUS_601, constants.STATUS_615,
		constants.STATUS_616, constants.STATUS_617, constants.STATUS_618, constants.STATUS_620,
		constants.STATUS_621, constants.STATUS_636, constants.STATUS_641, constants.STATUS_642,
		constants.STATUS_643, constants.STATUS_644, constants.STATUS_902:
		{
			response.DisplayStatus = displaystatus.RETURNED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.RTO_IN_TRANSIT
		}

	// Lost Shipment
	case constants.STATUS_510, constants.STATUS_901:
		{
			response.DisplayStatus = displaystatus.RETURNED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.LOST
		}

	// Default case for any unhandled status codes
	default:
		{
			response.DisplayStatus = displaystatus.OTHERS
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.OTHERS
		}
	}

	return *response
}

func handleEkartOrderStatus(response *OrderStatusResponse, currentStatus string) OrderStatusResponse {
	switch strings.ToUpper(currentStatus) {
	case strings.ToUpper(ekartstatus.EKT_SHIPMENT_CREATED):
		{
			response.DisplayStatus = displaystatus.SHIPMENT_CREATED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		}
	case strings.ToUpper(ekartstatus.EKT_PICKUP_OUT_FOR_PICKUP):
		{
			response.DisplayStatus = displaystatus.SHIPMENT_CREATED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		}
	case strings.ToUpper(ekartstatus.EKT_PICKUP_REATTEMPT):
		{
			response.DisplayStatus = displaystatus.SHIPMENT_CREATED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		}
	case strings.ToUpper(ekartstatus.EKT_SHIPMENT_PICKUP_COMPLETE):
		{
			response.DisplayStatus = displaystatus.IN_TRANSIT
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		}
	case strings.ToUpper(ekartstatus.EKT_RECEIVED):
		{
			response.DisplayStatus = displaystatus.IN_TRANSIT
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		}
	case strings.ToUpper(ekartstatus.EKT_SHIPMENT_RTO_CONFIRMED):
		{
			response.DisplayStatus = displaystatus.RETURNED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.RTO_IN_TRANSIT
		}
	case strings.ToUpper(ekartstatus.EKT_PICKUP_CANCELLED):
		{
			response.DisplayStatus = displaystatus.CANCELLED
			response.ProcessingStatus = processingstatus.CANCELLED
			response.ShipmentStatus = shipmentstatus.CANCELLED
		}
	case strings.ToUpper(ekartstatus.EKT_RECEIVED_AT_DH):
		{
			response.DisplayStatus = displaystatus.IN_TRANSIT
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		}
	case strings.ToUpper(ekartstatus.EKT_SHIPMENT_OUT_FOR_DELIVERY):
		{
			response.DisplayStatus = displaystatus.IN_TRANSIT
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.OUT_FOR_DELIVERY
		}
	case strings.ToUpper(ekartstatus.EKT_SHIPMENT_DELIVERED):
		{
			response.DisplayStatus = displaystatus.DELIVERED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.DELIVERED
		}
	case strings.ToUpper(ekartstatus.EKT_SHIPMENT_RECEIVED):
		{
			response.DisplayStatus = displaystatus.IN_TRANSIT
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		}
	case strings.ToUpper(ekartstatus.EKT_SHIPMENT_SHIPPED):
		{
			response.DisplayStatus = displaystatus.IN_TRANSIT
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		}
	case strings.ToUpper(ekartstatus.EKT_EXPECTED):
		{
			response.DisplayStatus = displaystatus.IN_TRANSIT
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		}
	case strings.ToUpper(ekartstatus.EKT_SHIPMENT_UNDELIVERED_ATTEMPTED):
		{
			response.DisplayStatus = displaystatus.NDR
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.FDA
		}
	case strings.ToUpper(ekartstatus.EKT_RETURN_RECEIVED):
		{
			response.DisplayStatus = displaystatus.RETURNED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.RTO_DELIVERED
		}
	case strings.ToUpper(ekartstatus.EKT_RETURN_DELIVERED):
		{
			response.DisplayStatus = displaystatus.RETURNED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.RTO_DELIVERED
		}
	case strings.ToUpper(ekartstatus.EKT_RETURN_UNDELIVERED_ATTEMPTED):
		{
			response.DisplayStatus = displaystatus.RETURNED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.RTO_IN_TRANSIT
		}
	case strings.ToUpper(ekartstatus.EKT_SHIPMENT_RTO_CREATED):
		{
			response.DisplayStatus = displaystatus.RETURNED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.RTO_IN_TRANSIT
		}
	case strings.ToUpper(ekartstatus.EKT_SHIPMENT_RTO_IN_TRANSIT):
		{
			response.DisplayStatus = displaystatus.RETURNED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.RTO_IN_TRANSIT
		}
	case strings.ToUpper(ekartstatus.EKT_RETURN_EXPECTED):
		{
			response.DisplayStatus = displaystatus.RETURNED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.RTO_IN_TRANSIT
		}
	case strings.ToUpper(ekartstatus.EKT_RETURN_RECEIVED_AT_DH):
		{
			response.DisplayStatus = displaystatus.RETURNED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.RTO_IN_TRANSIT
		}
	case strings.ToUpper(ekartstatus.EKT_RETURN_OUT_FOR_DELIVERY):
		{
			response.DisplayStatus = displaystatus.RETURNED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.RTO_IN_TRANSIT
		}
	case strings.ToUpper(ekartstatus.EKT_SHIPMENT_RTO_COMPLETED):
		{
			response.DisplayStatus = displaystatus.RETURNED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.RTO_DELIVERED
		}
	case strings.ToUpper(ekartstatus.EKT_SHIPMENT_UNDELIVERED_UNATTEMPTED):
		{
			response.DisplayStatus = displaystatus.NDR
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.FDA_UNREACHABLE
		}
	case strings.ToUpper(ekartstatus.EKT_SHIPMENT_MISROUTED):
		{
			response.DisplayStatus = displaystatus.IN_TRANSIT
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		}
	case strings.ToUpper(ekartstatus.EKT_PICKUP_RESCHEDULED):
		{
			response.DisplayStatus = displaystatus.SHIPMENT_CREATED
			response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		}
	}

	return *response
}

func containsIgnoreCase(slice []string, item string) bool {
	for _, s := range slice {
		if strings.EqualFold(s, item) {
			return true
		}
	}
	return false
}

func MapOrderStatus(currentStatus string, statusType string, orderStatus OrderStatusResponse) OrderStatusResponse {
	response := OrderStatusResponse{
		DisplayStatus:    ifNotEmpty(orderStatus.DisplayStatus, displaystatus.PLACED),
		ProcessingStatus: ifNotEmpty(orderStatus.ProcessingStatus, processingstatus.PLACED),
		ShipmentStatus:   orderStatus.ShipmentStatus,
		PaymentStatus:    orderStatus.PaymentStatus,
	}

	// handling OMS errors
	if slices.Contains([]string{omsstatus.FAILED_NON_SERVICEABLE, omsstatus.FAILED_OTHERS, omsstatus.FAILED_WEIGHT_ISSUE, omsstatus.FAILED_API_4XX, omsstatus.FAILED_API_5XX}, currentStatus) {
		response.DisplayStatus = displaystatus.SHIPMENT_CREATED
		response.ProcessingStatus = processingstatus.EXCEPTION
		return response
	}

	// handling ekart if ValidEkartStatus has the currentStatus
	if containsIgnoreCase(ekartstatus.ValidEkartStatus, currentStatus) {
		return handleEkartOrderStatus(&response, currentStatus)
	}

	// handling shipdelight statusType
	if slices.Contains(shipdelightstatus.StatusTypes, statusType) {
		return updateShipdelightOrderStatus(&response, currentStatus)
	}

	// handling internal status of return received at seller end
	if currentStatus == shipmentstatus.RTO_RECEIVED {
		response.DisplayStatus = displaystatus.RETURNED
		response.ShipmentStatus = shipmentstatus.RTO_RECEIVED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		return response
	}

	// Handling Delivery Status
	if statusType == "RT" || ((statusType == "DL" && currentStatus == constants.RDAC) || statusType == "DL" && currentStatus == constants.RT110) {
		response.DisplayStatus = displaystatus.RETURNED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.RTO_IN_TRANSIT
		if currentStatus == constants.RDAC {
			response.ShipmentStatus = shipmentstatus.RTO_DELIVERED
		} else if currentStatus == constants.RT110 {
			response.ShipmentStatus = shipmentstatus.RTO_DELIVERED
		}
		return response
	} else if statusType == "DL" {
		response.DisplayStatus = displaystatus.DELIVERED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.DELIVERED
		return response
	} else if statusType == "LT" {
		response.DisplayStatus = displaystatus.RETURNED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.LOST
		return response
	} else if statusType == "UD" {
		switch currentStatus {
		// this is the NSL code for out for pickup, not ready for pickup....etc
		case constants.XPPOM, constants.ST102:
			{
				response.DisplayStatus = displaystatus.IN_TRANSIT
				response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
				response.ShipmentStatus = shipmentstatus.IN_TRANSIT
			}
		// these are manifested, pickup scheduled and not pickedup status
		case constants.XUCI, constants.FMOFP101, constants.FMEOD152, constants.FMPUR101, constants.DTUP219, constants.XPNP, constants.XNSZ:
			{
				response.DisplayStatus = displaystatus.SHIPMENT_CREATED
				response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
			}
		// this is the case for IN_TRANSIT
		case constants.XPROM, constants.DTUP212, constants.CSCSL, constants.XILL1F, constants.XDWS, constants.XDLL2F, constants.ST114, constants.XUNEX, constants.XAWD, constants.XDBL1F, constants.PL105, constants.DLYMR118, constants.XILL2F, constants.DLYMPS101, constants.XOLL2F, constants.XIBD3F, constants.ST110, constants.DTUP207, constants.DTUP205, constants.DTUP213, constants.XPIOM, constants.DTUP231, constants.DTUP235, constants.DTUP203, constants.CS104, constants.CS101, constants.DTUP209, constants.LFIN:
			{
				response.DisplayStatus = displaystatus.IN_TRANSIT
				response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
				response.ShipmentStatus = shipmentstatus.IN_TRANSIT
			}
		case constants.XDDD3FD:
			{

				response.DisplayStatus = displaystatus.IN_TRANSIT
				response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
				response.ShipmentStatus = shipmentstatus.OUT_FOR_DELIVERY
			}
		case constants.EOD11:
			{
				response.DisplayStatus = displaystatus.NDR
				response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
				response.ShipmentStatus = shipmentstatus.FDA_CUSTOMER_UNAVAILABLE
			}
		case constants.EOD6:
			{
				response.DisplayStatus = displaystatus.NDR
				response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
				response.ShipmentStatus = shipmentstatus.FDA_CUSTOMER_REFUSED
			}
		// default NDR
		case constants.EOD111, constants.DLYHD007, constants.EOD104, constants.EOD43, constants.EOD15, constants.EOD74, constants.EOD86, constants.EOD3, constants.EOD40, constants.DLYB2B101, constants.EOD105, constants.XRWD, constants.EOD83, constants.ST116, constants.ST115, constants.EOD6O, constants.XSC, constants.CL106, constants.DLYDC109, constants.ST108, constants.RT108, constants.ST6W, constants.RT109, constants.RDPD20, constants.ST107, constants.ST105, constants.CL105, constants.DLYSU100:
			{
				response.DisplayStatus = displaystatus.NDR
				response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
				response.ShipmentStatus = shipmentstatus.FDA
			}
		case constants.EOD69:
			{
				response.DisplayStatus = displaystatus.NDR
				response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
				response.ShipmentStatus = shipmentstatus.FDA_CUSTOMER_WANTS_OPEN_DELIVERY
			}
		case constants.DTUP210:
			{
				response.DisplayStatus = displaystatus.CANCELLED
				response.ProcessingStatus = processingstatus.CANCELLED
			}
		// delayed
		case constants.DLYRG135, constants.DLYDC101, constants.DLYDC107, constants.DLYLH105, constants.DLYLH126, constants.DOFF128, constants.DLYDC102, constants.DLYDG119, constants.DLYLH151, constants.DLYLH152, constants.DLYRG125:
			{
				response.DisplayStatus = displaystatus.IN_TRANSIT
				response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
				response.ShipmentStatus = shipmentstatus.DELAYED

			}
		// this is the default case for IN_TRANSIT
		default:
			{
				response.DisplayStatus = displaystatus.IN_TRANSIT
				response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
				response.ShipmentStatus = shipmentstatus.IN_TRANSIT
			}
		}
		// default intransit
		return response
	}

	if strings.Contains(currentStatus, "BAG") {
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		return response
	} else if strings.Contains(currentStatus, "EXPECTED") {
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		return response
	} else if strings.Contains(currentStatus, "INSCAN") {
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		return response
	} else if strings.Contains(currentStatus, "RECEIVED") {
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		return response
	} else if strings.Contains(currentStatus, "READY_FOR_PICKUP") {
		response.DisplayStatus = displaystatus.SHIPMENT_CREATED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		return response
	} else if strings.Contains(currentStatus, "PICKUPCANCELLED") {
		response.DisplayStatus = displaystatus.SHIPMENT_CREATED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		return response
	} else if strings.Contains(currentStatus, "OUTFORPICKUPEVENT") {
		response.DisplayStatus = displaystatus.SHIPMENT_CREATED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		return response
	} else if strings.Contains(currentStatus, "FIELD") {
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		return response
	} else if strings.Contains(currentStatus, "PICKUPRESCHEDULEDEVENT") {
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		return response
	} else if strings.Contains(currentStatus, "PENDING") {
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		return response
	} else if strings.Contains(currentStatus, "DUMMYSTATE - PICKUPHUB_MPHUB_FKRPR") {
		response.DisplayStatus = displaystatus.RETURNED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.RETURNED
		return response
	} else if strings.Contains(currentStatus, "DEPARTED FROM LOCATION") {
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		return response
	} else if strings.Contains(currentStatus, "SHIPMENT IS PICKED FROM SELLER") {
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		return response
	} else if strings.Contains(currentStatus, "SHIPMENTRTOCONFIRMED - CENTRALHUB_FKRPR") {
		response.DisplayStatus = displaystatus.RETURNED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.RETURNED
		return response
	} else if strings.Contains(currentStatus, "CNA") {
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT
		return response
	}

	switch currentStatus {
	case "INITIATED", "PLACED":
		response.DisplayStatus = displaystatus.PLACED
		response.ProcessingStatus = processingstatus.PLACED
	case "NOTCONFIRMED", "PENDING_CONFIRMATION":
		response.DisplayStatus = displaystatus.PENDING_CONFIRMATION
		response.ProcessingStatus = processingstatus.PENDING_CONFIRMATION
	case "CONFIRMED":
		response.DisplayStatus = displaystatus.CONFIRMED
		response.ProcessingStatus = processingstatus.CONFIRMED
	case "PUSHED_TO_OMS":
		response.DisplayStatus = displaystatus.PUSHED_TO_OMS
		response.ProcessingStatus = processingstatus.PUSHED_TO_OMS
	case "SHIPMENT CREATED", constants.SCH, constants.PKF:
		response.DisplayStatus = displaystatus.SHIPMENT_CREATED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
	case "SHIPMENT_CREATED":
		response.DisplayStatus = displaystatus.SHIPMENT_CREATED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
	case "ASSIGNED":
		response.DisplayStatus = displaystatus.SHIPMENT_CREATED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT
	case "OUT FOR PICKUP":
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT
	case "PICKUP SCHEDULED":
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT
	case "PICKUP EXCEPTION":
		response.DisplayStatus = displaystatus.RETURNED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.RETURNED
	case "IN TRANSIT", constants.INT, constants.PKP, constants.DNB:
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT
	case "IN_TRANSIT", constants.RAD:
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.IN_TRANSIT

	case "OUT FOR DELIVERY", constants.OOD:
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.OUT_FOR_DELIVERY
	case "DELIVERED", constants.DEL:
		response.DisplayStatus = displaystatus.DELIVERED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.DELIVERED
	case "NON_SERVICEABLE":
		response.DisplayStatus = displaystatus.RETURNED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.RETURNED
	case "UNDELIVERED":
		response.DisplayStatus = displaystatus.NDR
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.FDA
	case "UNCONTACTABLE":
		response.DisplayStatus = displaystatus.PENDING_CONFIRMATION
		response.ProcessingStatus = processingstatus.PENDING_CONFIRMATION
	case "RTO INITIATED", "RTO IN-TRANSIT", "RTO", "RETURNED", "SHIPMENT ERROR":
		response.DisplayStatus = displaystatus.RETURNED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.RETURNED
	case "SHIPMENT_LOST":
		response.DisplayStatus = displaystatus.RETURNED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.LOST
	case constants.RTD, "DELIVERED TO ORIGIN":
		response.DisplayStatus = displaystatus.RETURNED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.RTO_DELIVERED
	case "CANCELLED":
		response.DisplayStatus = displaystatus.CANCELLED
		response.ProcessingStatus = processingstatus.CANCELLED
	case "NON SERVICEABLE":
		response.DisplayStatus = displaystatus.CANCELLED
		response.ProcessingStatus = processingstatus.CANCELLED

	case constants.NDR1, constants.NDR2, constants.NDR3, constants.NDR4, constants.NDR5, constants.NDR6:
		response.DisplayStatus = displaystatus.NDR
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.FDA

	case "DELIVERY ATTEMPT", constants.TwoThree, constants.TwoFour, constants.TwoFive, constants.DRE, constants.NDR, constants.FDA:
		response.DisplayStatus = displaystatus.NDR
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.FDA
	case constants.CRTA, constants.CROV:
		response.DisplayStatus = displaystatus.NDR
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.FDA_CUSTOMER_REFUSED
	case constants.OFP:
		response.DisplayStatus = displaystatus.SHIPMENT_CREATED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
	case constants.LOST:
		response.DisplayStatus = displaystatus.RETURNED
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.LOST
	case constants.TwoTwo:
		response.DisplayStatus = displaystatus.NDR
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.FDA_ADDRESS_ISSUE

	case constants.PNR:
		response.DisplayStatus = displaystatus.NDR
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.FDA_PAYMENT_NOT_READY

	case constants.SMD:
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.DELAYED

	case constants.OUT_OF_DELIVERY_AREA, constants.ODA:
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.OUT_OF_DELIVERY_AREA

	case constants.UND, constants.CAN, constants.PCAN, constants.ONH, constants.NWI, constants.NFI, constants.OTH, constants.DEX:
		response.DisplayStatus = displaystatus.IN_TRANSIT
		response.ProcessingStatus = processingstatus.SHIPMENT_CREATED
		response.ShipmentStatus = shipmentstatus.OTHERS

	case constants.ARCHIVED:
		response.DisplayStatus = displaystatus.ARCHIVED

	// adding delhivery cases
	case constants.XUCI, constants.FMPUR101, constants.XILL1F, constants.XDWS, constants.XDLL2F, constants.ST114, constants.XDDD3FD, constants.EOD11, constants.XUNEX, constants.XAWD, constants.XDBL1F, constants.XILL2F, constants.DLYDC109, constants.XSC, constants.EOD38, constants.PL105, constants.DLYMR118, constants.XIBD3F, constants.DLYMPS101, constants.FMOFP101, constants.XOLL2F, constants.DTUP219, constants.DLYLH105, constants.EOD6, constants.ST105, constants.ST110, constants.DTUP207, constants.DTUP205, constants.DTUP210, constants.EOD3, constants.EOD37, constants.ST6W, constants.ST115, constants.EOD104, constants.ST116, constants.DTUP213, constants.EOD111, constants.EOD43, constants.EOD15, constants.EOD74, constants.ST108, constants.XPPOM, constants.XPNP, constants.EOD86, constants.FMEOD152, constants.XPIOM, constants.DTUP231, constants.EOD40, constants.RT108, constants.DLYLH126, constants.DOFF128, constants.DLYDG119, constants.CL106, constants.ST120, constants.DTUP235, constants.DTUP204, constants.RDAC, constants.ST107, constants.DLYB2B101, constants.EOD6O, constants.DLYDC102, constants.EOD69, constants.ST102, constants.CSCSL, constants.EOD105, constants.DLYLH151, constants.XRWD, constants.DTUP203, constants.DLYLH152, constants.DLYRG125, constants.RT101, constants.EOD83, constants.CS104, constants.CS101, constants.DLYHD007, constants.DTUP212, constants.DTUP209, constants.LFIN, constants.DLYDC107, constants.CL105, constants.LT100, constants.XPROM, constants.SXIN, constants.XNSZ, constants.DLYDC101, constants.EOD36:
		// these are delhivery cases have not handled the cases here, so sending the empty object
		return OrderStatusResponse{}

	// in case of default return others this needs human intervention
	default:
		response.DisplayStatus = displaystatus.OTHERS
		response.ProcessingStatus = processingstatus.OTHERS
		response.ShipmentStatus = shipmentstatus.OTHERS
	}
	return response
}

// this returns the status description
func GetStatusDescription(status string) *string {
	switch status {
	case omsstatus.SUCCESS_CREATED:
		description := "Order pushed to OMS successfully"
		return &description
	case omsstatus.FAILED_NON_SERVICEABLE:
		description := "Order not pushed to OMS due to non-serviceable location"
		return &description
	case omsstatus.FAILED_OTHERS:
		description := "Order not pushed to OMS due to other reasons"
		return &description
	case omsstatus.FAILED_WEIGHT_ISSUE:
		description := "Order not pushed to OMS due to weight issue"
		return &description
	case omsstatus.FAILED_API_5XX:
		description := "Order not pushed to OMS due to 5xx error"
		return &description
	case omsstatus.FAILED_API_4XX:
		description := "Order not pushed to OMS due to 4xx error"
		return &description
	default:
		return nil
	}
}
