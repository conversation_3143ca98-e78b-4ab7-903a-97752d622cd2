package constants

const (
	SHIPDELIGHT_STATUS  = "SHIPDELIGHT_STATUS"
	SHIPDELIGHT_WEBHOOK = "SHIPDELIGHT_WEBHOOK"
)

const (
	// paymentStatus
	FULLY_PAID         = "FULLY_PAID"
	PARTIALLY_PAID     = "PARTIALLY_PAID"
	UNPAID             = "UNPAID"
	COD                = "COD"
	PREPAID            = "PREPAID"
	REFUNDED           = "REFUNDED"
	PARTIALLY_REFUNDED = "PARTIALLY_REFUNDED"
	FAILED             = "FAILED"

	// processingStatus
	PLACED               = "PLACED"
	PENDING_CONFIRMATION = "PENDING_CONFIRMATION"
	CONFIRMED            = "CONFIRMED"
	PUSHED_TO_OMS        = "PUSHED_TO_OMS"
	SHIPMENT_CREATED     = "SHIPMENT_CREATED"
	SHIPMENT_MANIFESTED  = "SHIPMENT_MANIFESTED" //  this is the processing status after the shipment is created and manifest is downloaded
	CANCELLED            = "CANCELLED"
	ARCHIVED             = "ARCHIVED"
	EXCEPTION            = "EXCEPTION"

	// shippingStatus
	IN_TRANSIT                       = "IN_TRANSIT"
	OUT_FOR_DELIVERY                 = "OUT_FOR_DELIVERY"
	DELIVERED                        = "DELIVERED"
	DELAYED                          = "DELAYED"
	FDA                              = "FDA"
	FDA_ADDRESS_ISSUE                = "FDA_ADDRESS_ISSUE"
	FDA_CUSTOMER_REFUSED             = "FDA_CUSTOMER_REFUSED"
	FDA_UNREACHABLE                  = "FDA_UNREACHABLE"
	FDA_PAYMENT_NOT_READY            = "FDA_PAYMENT_NOT_READY"
	OTHERS                           = "OTHERS"
	OUT_OF_DELIVERY_AREA             = "OUT_OF_DELIVERY_AREA"
	FDA_CUSTOMER_UNAVAILABLE         = "FDA_CUSTOMER_UNAVAILABLE"
	FDA_CUSTOMER_WANTS_OPEN_DELIVERY = "FDA_CUSTOMER_WANTS_OPEN_DELIVERY"
	LOST                             = "LOST"
	RTO_RECEIVED                     = "RTO_RECEIVED"
	RTO_IN_TRANSIT                   = "RTO_IN_TRANSIT"
	RTO_DELIVERED                    = "RTO_DELIVERED"

	RETURNED = "RETURNED"

	// display status
	NDR  = "NDR"
	NDR1 = "NDR1"
	NDR2 = "NDR2"
	NDR3 = "NDR3"
	NDR4 = "NDR4"
	NDR5 = "NDR5"
	NDR6 = "NDR6"

	//shipwayStatus
	DEL      = "DEL"
	INT      = "INT"
	UND      = "UND"
	RTO      = "RTO"
	RTD      = "RTD"
	CAN      = "CAN"
	SCH      = "SCH"
	PKP      = "PKP"
	PKF      = "PKF"
	PCAN     = "PCAN"
	ONH      = "ONH"
	OOD      = "OOD"
	NWI      = "NWI"
	DNB      = "DNB"
	NFI      = "NFI"
	ODA      = "ODA"
	OTH      = "OTH"
	SMD      = "SMD"
	CRTA     = "CRTA"
	DEX      = "DEX"
	DRE      = "DRE"
	PNR      = "PNR"
	TwoTwo   = "22"
	TwoThree = "23"
	TwoFour  = "24"
	TwoFive  = "25"
	CROV     = "CROV"
	CNA      = "CNA"
	OFP      = "OFP"
	RAD      = "RAD"
	// LOST                     = "LOST"

	// Delhivery Status Codes
	XUCI      = "X-UCI"
	FMPUR101  = "FMPUR-101"
	XILL1F    = "X-ILL1F"
	XDWS      = "X-DWS"
	XDLL2F    = "X-DLL2F"
	ST114     = "ST-114"
	XDDD3FD   = "X-DDD3FD"
	EOD11     = "EOD-11"
	XUNEX     = "X-UNEX"
	XAWD      = "X-AWD"
	XDBL1F    = "X-DBL1F"
	XILL2F    = "X-ILL2F"
	DLYDC109  = "DLYDC-109"
	XSC       = "X-SC"
	EOD38     = "EOD-38"
	PL105     = "PL-105"
	DLYMR118  = "DLYMR-118"
	XIBD3F    = "X-IBD3F"
	DLYMPS101 = "DLYMPS-101"
	FMOFP101  = "FMOFP-101"
	XOLL2F    = "X-OLL2F"
	DTUP219   = "DTUP-219"
	DLYLH105  = "DLYLH-105"
	EOD6      = "EOD-6"
	ST105     = "ST-105"
	ST110     = "ST-110"
	DTUP207   = "DTUP-207"
	DTUP205   = "DTUP-205"
	DTUP210   = "DTUP-210"
	EOD3      = "EOD-3"
	DLYRG135  = "DLYRG-135"
	EOD37     = "EOD-37"
	ST6W      = "ST-6W"
	RT109     = "RT-109"
	RDPD20    = "RD-PD20"
	DLYSU100  = "DLYSU-100"
	ST115     = "ST-115"
	EOD104    = "EOD-104"
	ST116     = "ST-116"
	DTUP213   = "DTUP-213"
	EOD111    = "EOD-111"
	EOD43     = "EOD-43"
	EOD15     = "EOD-15"
	EOD74     = "EOD-74"
	ST108     = "ST-108"
	XPPOM     = "X-PPOM"
	XPNP      = "X-PNP"
	EOD86     = "EOD-86"
	FMEOD152  = "FMEOD-152"
	XPIOM     = "X-PIOM"
	DTUP231   = "DTUP-231"
	EOD40     = "EOD-40"
	RT108     = "RT-108"
	DLYLH126  = "DLYLH-126"
	DOFF128   = "DOFF-128"
	DLYDG119  = "DLYDG-119"
	CL106     = "CL-106"
	ST120     = "ST-120"
	DTUP235   = "DTUP-235"
	DTUP204   = "DTUP-204"
	RDAC      = "RD-AC"
	RT110     = "RT-110"
	ST107     = "ST-107"
	DLYB2B101 = "DLYB2B-101"
	EOD6O     = "EOD-6O"
	DLYDC102  = "DLYDC-102"
	EOD69     = "EOD-69"
	ST102     = "ST-102"
	CSCSL     = "CS-CSL"
	EOD105    = "EOD-105"
	DLYLH151  = "DLYLH-151"
	XRWD      = "X-RWD"
	DTUP203   = "DTUP-203"
	DLYLH152  = "DLYLH-152"
	DLYRG125  = "DLYRG-125"
	RT101     = "RT-101"
	EOD83     = "EOD-83"
	CS104     = "CS-104"
	CS101     = "CS-101"
	DLYHD007  = "DLYHD-007"
	DTUP212   = "DTUP-212"
	DTUP209   = "DTUP-209"
	LFIN      = "L-FIN"
	DLYDC107  = "DLYDC-107"
	CL105     = "CL-105"
	LT100     = "LT-100"
	XPROM     = "X-PROM"
	SXIN      = "S-XIN"
	XNSZ      = "X-NSZ"
	DLYDC101  = "DLYDC-101"
	EOD36     = "EOD-36"

	// OMS Status
	SUCCESS_CREATED        = "SUCCESS_CREATED"
	FAILED_NON_SERVICEABLE = "FAILED_NON_SERVICEABLE"
	FAILED_OTHERS          = "FAILED_OTHERS"
	FAILED_WEIGHT_ISSUE    = "FAILED_WEIGHT_ISSUE"
	FAILED_API_4XX         = "FAILED_API_4XX"
	FAILED_API_5XX         = "FAILED_API_5XX"
)

// shipdelight status constants
const (
	// Shipment Created
	STATUS_59 = "59"

	// Pickup Status Codes
	STATUS_99  = "99"  // PENDING PICKUP
	STATUS_100 = "100" // PICKUP DONE
	STATUS_900 = "900" // PICKUP CANCELLED

	// In-Transit Status Codes
	STATUS_102 = "102" // IN-TRANSIT - processing at origin hub
	STATUS_105 = "105" // IN-TRANSIT - ready to dispatch from origin
	STATUS_200 = "200" // IN-TRANSIT - left from origin
	STATUS_201 = "201" // IN-TRANSIT - in-transit
	STATUS_204 = "204" // IN-TRANSIT - network delay
	STATUS_211 = "211" // IN-TRANSIT - misroute
	STATUS_301 = "301" // IN-TRANSIT - arrival at destination branch
	STATUS_312 = "312" // IN-TRANSIT - Maximum Attempts done

	// Out for Delivery Status Codes
	STATUS_305 = "305" // OUT FOR DELIVERY - out for delivery

	// Delivery Status Codes
	STATUS_400 = "400" // DELIVERED - delivered
	STATUS_401 = "401" // RTO DELIVERED - rto delivered

	// Undelivered Status Codes
	STATUS_500 = "500" // UNDELIVERED - consignee refused
	STATUS_501 = "501" // UNDELIVERED - incomplete address
	STATUS_502 = "502" // UNDELIVERED - oda
	STATUS_503 = "503" // UNDELIVERED - consignee shifted
	STATUS_504 = "504" // UNDELIVERED - DAMAGED
	STATUS_505 = "505" // UNDELIVERED - no such consignee
	STATUS_506 = "506" // UNDELIVERED - future delivery
	STATUS_507 = "507" // UNDELIVERED - cod not ready
	STATUS_508 = "508" // UNDELIVERED - residence/office closed
	STATUS_509 = "509" // UNDELIVERED - out of station
	STATUS_510 = "510" // UNDELIVERED - shipment lost
	STATUS_511 = "511" // UNDELIVERED - Dangerous Goods
	STATUS_513 = "513" // UNDELIVERED - Self Collect
	STATUS_514 = "514" // UNDELIVERED - Held With Govt Authority
	STATUS_515 = "515" // UNDELIVERED - consignee not available
	STATUS_516 = "516" // UNDELIVERED - consignee not responding
	STATUS_517 = "517" // UNDELIVERED - misroute
	STATUS_518 = "518" // UNDELIVERED - on hold
	STATUS_519 = "519" // UNDELIVERED - restricted area
	STATUS_520 = "520" // UNDELIVERED - snatched by consignee
	STATUS_521 = "521" // UNDELIVERED - disturbance/natural disaster/strike/COVID
	STATUS_522 = "522" // UNDELIVERED - Open Delivery
	STATUS_523 = "523" // UNDELIVERED - Customer denied - OTP Delivery
	STATUS_524 = "524" // UNDELIVERED - Time Constraint / Dispute
	STATUS_526 = "526" // UNDELIVERED - OTP Not Received by Consignee

	// Return to Origin (RTO) Status Codes
	STATUS_600 = "600" // RTO INITIATED - rto initiated
	STATUS_601 = "601" // RTO IN-TRANSIT - rto intransit
	STATUS_615 = "615" // RTO UNDELIVERED - Vendor refused
	STATUS_616 = "616" // RTO UNDELIVERED - Vendor Office closed
	STATUS_617 = "617" // RTO UNDELIVERED - Vendor address changed
	STATUS_618 = "618" // RTO UNDELIVERED - Damaged
	STATUS_620 = "620" // RTO OUT FOR DELIVERY - rto out for delivery
	STATUS_621 = "621" // RTO UNDELIVERED - RTO Undelivered
	STATUS_636 = "636" // RTO UNDELIVERED - Packet Open
	STATUS_641 = "641" // RTO UNDELIVERED - RTO undelivered-Vendor not available
	STATUS_642 = "642" // RTO UNDELIVERED - RTO undelivered-Vendor Location Not serviceable
	STATUS_643 = "643" // RTO UNDELIVERED - RTO undelivered-Vendor address changed
	STATUS_644 = "644" // RTO UNDELIVERED - RTO undelivered- Data Incorrect

	// Exception Status Codes
	STATUS_901  = "901"  // LOST - SHIPMENT LOST
	STATUS_902  = "902"  // SHIPMENT DAMAGE - SHIPMENT DAMAGE
	STATUS_951  = "951"  // REATTEMPT - Reattempt
	STATUS_1001 = "1001" // UNATTEMPTED - Unattempted

	// Client-Specific Status Codes
	STATUS_I002 = "I002" // CL-REATTEMPT - Client Reattempt
	STATUS_I003 = "I003" // CL-RTO-INITIATED - cl rto initiated
	STATUS_I005 = "I005" // CL-SELFCOLLECT - Client Self Collect

	// Reverse Pickup Status Codes
	STATUS_R001  = "R001"  // REVERSE PICKUP PENDING - reverse pickup data shared
	STATUS_R002  = "R002"  // REVERSE PICKUP PENDING - reverse pickup token generated
	STATUS_R003  = "R003"  // REVERSE PICKUP PENDING - out for reverse pickup
	STATUS_R004  = "R004"  // REVERSE PICKUP DONE - Reverse Pickup Done
	STATUS_R011  = "R011"  // REVERSE PICKUP PENDING - Incomplete Address
	STATUS_R085  = "R085"  // REVERSE PICKUP PENDING - Reverse Initiated
	STATUS_R086  = "R086"  // REVERSE RECEIVED - Reverse Received
	STATUS_R087  = "R087"  // REVERSE PICKUP PENDING - Reverse Approved
	STATUS_R088  = "R088"  // REVERSE REJECTED - Reverse Rejected
	STATUS_R100  = "R100"  // REVERSE PICKUP DONE - Reverse pickup done
	STATUS_R1000 = "R1000" // REVERSE PICKUP DELIVERED - reverse- shipment delivered
	STATUS_R101  = "R101"  // REVERSE PICKUP PENDING - Reverse Pickup Rescheduled
	STATUS_R102  = "R102"  // REVERSE PICKUP FAILED - shipment not ready
	STATUS_R103  = "R103"  // REVERSE PICKUP FAILED - pickup cancelled by customer
	STATUS_R104  = "R104"  // REVERSE PICKUP FAILED - pickup location not served
	STATUS_R106  = "R106"  // REVERSE PICKUP FAILED - Customer not available
	STATUS_R107  = "R107"  // REVERSE PICKUP FAILED - pickup attempted late
	STATUS_R109  = "R109"  // REVERSE PICKUP FAILED - packaging not appropriate
	STATUS_R1100 = "R1100" // REVERSE PICKUP RTO INITIATED - reverse shipment- rto initiated
	STATUS_R1101 = "R1101" // REVERSE PICKUP RTO INTRANSIT - reverse shipment-rto intransit
	STATUS_R1102 = "R1102" // REVERSE PICKUP RTO OUT FOR DELIVERY - reverse shipment-rto out for delivery
	STATUS_R1103 = "R1103" // REVERSE PICKUP RTO DELIVERED - reverse shipment-rto delivered
	STATUS_R111  = "R111"  // REVERSE PICKUP FAILED - Different Product
	STATUS_R112  = "R112"  // REVERSE PICKUP FAILED - pickup done by other courier
	STATUS_R113  = "R113"  // REVERSE PICKUP FAILED - confirmation from shipper awaited
	STATUS_R114  = "R114"  // REVERSE PICKUP FAILED - customer wants the replacement/refund
	STATUS_R117  = "R117"  // REVERSE PICKUP FAILED - customer shifted
	STATUS_R118  = "R118"  // REVERSE PICKUP FAILED - maximum attempt done
	STATUS_R119  = "R119"  // REVERSE PICKUP FAILED - pickup refused by customer
	STATUS_R120  = "R120"  // REVERSE PICKUP FAILED - qc failed
	STATUS_R1200 = "R1200" // REVERSE PICKUP UNDELIVERED - vendor refused to accept
	STATUS_R1201 = "R1201" // REVERSE PICKUP UNDELIVERED - vendor shifted
	STATUS_R1202 = "R1202" // REVERSE PICKUP UNDELIVERED - Reverse shipment undelivered-Vendor Location Closed
	STATUS_R1203 = "R1203" // REVERSE PICKUP UNDELIVERED - Reverse shipment undelivered-Vendor not available
	STATUS_R1204 = "R1204" // REVERSE PICKUP UNDELIVERED - Reverse shipment undelivered-Vendor address changed
	STATUS_R1205 = "R1205" // REVERSE PICKUP UNDELIVERED - Reverse shipment undelivered-Vendor Location Not serviceable
	STATUS_R1206 = "R1206" // REVERSE PICKUP UNDELIVERED - Reverse shipment undelivered-Shipment Misroute
	STATUS_R1207 = "R1207" // REVERSE PICKUP UNDELIVERED - on hold
	STATUS_R1208 = "R1208" // REVERSE PICKUP UNDELIVERED - vendor not available
	STATUS_R121  = "R121"  // REVERSE PICKUP FAILED - pickup cancelled due to over size product
	STATUS_R122  = "R122"  // REVERSE PICKUP FAILED - On Hold
	STATUS_R123  = "R123"  // REVERSE PICKUP FAILED - Address unlocatable
	STATUS_R124  = "R124"  // REVERSE PICKUP FAILED - Partial Product
	STATUS_R125  = "R125"  // REVERSE PICKUP FAILED - Entry Restricted
	STATUS_R126  = "R126"  // REVERSE PICKUP FAILED - Color Mismatch
	STATUS_R127  = "R127"  // REVERSE PICKUP FAILED - Weight mismatch at pickup
	STATUS_R128  = "R128"  // REVERSE PICKUP FAILED - Failed KYC -OTP not available
	STATUS_R129  = "R129"  // REVERSE PICKUP FAILED - Pickup not attempted
	STATUS_R130  = "R130"  // REVERSE PICKUP FAILED - Brand Mismatch
	STATUS_R131  = "R131"  // REVERSE PICKUP FAILED - Size Mismatch
	STATUS_R200  = "R200"  // REVERSE PICKUP IN-TRANSIT - processing at origin hub
	STATUS_R201  = "R201"  // REVERSE PICKUP IN-TRANSIT - ready to dispatch from origin
	STATUS_R202  = "R202"  // REVERSE PICKUP IN-TRANSIT - left from origin
	STATUS_R203  = "R203"  // REVERSE PICKUP IN-TRANSIT - in-transit
	STATUS_R204  = "R204"  // REVERSE PICKUP IN-TRANSIT - network delay
	STATUS_R205  = "R205"  // REVERSE PICKUP IN-TRANSIT - misroute
	STATUS_R206  = "R206"  // REVERSE PICKUP IN-TRANSIT - arrival at destination
	STATUS_R300  = "R300"  // REVERSE PICKUP OUT FOR DELIVERY - reverse shipment-out for delivery
	STATUS_R400  = "R400"  // REVERSE PICKUP LOST - lost
	STATUS_R600  = "R600"  // REVERSE PICKUP DAMAGE - shipment damage
	STATUS_R85   = "R85"   // REVERSE PICKUP PENDING - Reverse ready for pickup
)

// Ekart Status Codes
const (
	EKT_SHIPMENT_CREATED                 = "shipment_created"                 // Initial shipment creation event
	EKT_PICKUP_OUT_FOR_PICKUP            = "pickup_out_for_pickup"            // Pickup agent is on the way to collect the shipment
	EKT_PICKUP_REATTEMPT                 = "pickup_reattempt"                 // Pickup attempt failed, will retry pickup
	EKT_SHIPMENT_PICKUP_COMPLETE         = "shipment_pickup_complete"         // Shipment has been successfully picked up from sender
	EKT_RECEIVED                         = "received"                         // Shipment received at logistics facility
	EKT_LPD_GENERATED                    = "lpd_generated"                    // Last Pickup Date (LPD) has been generated for the shipment
	EKT_SHIPMENT_RTO_CONFIRMED           = "shipment_rto_confirmed"           // Return to Origin (RTO) has been confirmed for the shipment
	EKT_PICKUP_CANCELLED                 = "pickup_cancelled"                 // Pickup request has been cancelled
	EKT_RECEIVED_AT_DH                   = "received_at_dh"                   // Shipment received at delivery hub
	EKT_SHIPMENT_OUT_FOR_DELIVERY        = "shipment_out_for_delivery"        // Shipment is out for delivery to recipient
	EKT_RETURN_RECEIVED                  = "return_received"                  // Return shipment has been received --  this is not received by the seller
	EKT_SHIPMENT_DELIVERED               = "shipment_delivered"               // Shipment has been successfully delivered to recipient
	EKT_SHIPMENT_RECEIVED                = "shipment_received"                // Shipment has been received (general status)
	EKT_SHIPMENT_SHIPPED                 = "shipment_shipped"                 // Shipment has been dispatched/shipped
	EKT_EXPECTED                         = "expected"                         // Shipment is expected to arrive
	EKT_SHIPMENT_UNDELIVERED_ATTEMPTED   = "shipment_undelivered_attempted"   // Delivery was attempted but unsuccessful -- NDR
	EKT_OUT_FOR_DELIVERY_UPDATE          = "out_for_delivery_update"          // Update on shipment that is out for delivery
	EKT_RETURN_OUT_FOR_DELIVERY          = "return_out_for_delivery"          // Return shipment is out for delivery
	EKT_DELIVERY_ATTEMPT_METADATA        = "delivery_attempt_metadata"        // Metadata about delivery attempt
	EKT_SHIPMENT_RTO_COMPLETED           = "shipment_rto_completed"           // Return to Origin (RTO) process has been completed
	EKT_SHIPMENT_UNDELIVERED_UNATTEMPTED = "shipment_undelivered_unattempted" // Shipment marked as undelivered without delivery attempt
	EKT_SHIPMENT_MISROUTED               = "shipment_misrouted"               // Shipment has been routed incorrectly
	EKT_PICKUP_RESCHEDULED               = "PickupRescheduledEvent"           // Pickup has been rescheduled to a different time
	EKT_SHIPMENT_RTO_CREATED             = "shipment_rto_created"             // Return to Origin (RTO) shipment has been created
	EKT_SHIPMENT_RTO_IN_TRANSIT          = "shipment_rto_in_transit"          // Return to Origin (RTO) shipment is in transit
	EKT_RETURN_EXPECTED                  = "return_expected"                  // Return shipment is expected
	EKT_RETURN_RECEIVED_AT_DH            = "return_received_at_dh"            // Return shipment received at delivery hub
	EKT_RETURN_DELIVERED                 = "return_delivered"                 // Return shipment has been delivered
	EKT_RETURN_UNDELIVERED_ATTEMPTED     = "return_undelivered_attempted"     // Return delivery was attempted but unsuccessful
)

// diff between EKT_RETURN_DELIVERED, EKT_SHIPMENT_RTO_COMPLETED
