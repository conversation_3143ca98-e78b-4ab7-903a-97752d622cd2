package ekartstatus

import "kc/internal/ondc/service/orderStatus/constants"

var (
	EKT_SHIPMENT_CREATED                 = constants.EKT_SHIPMENT_CREATED
	EKT_PICKUP_OUT_FOR_PICKUP            = constants.EKT_PICKUP_OUT_FOR_PICKUP
	EKT_PICKUP_REATTEMPT                 = constants.EKT_PICKUP_REATTEMPT
	EKT_SHIPMENT_PICKUP_COMPLETE         = constants.EKT_SHIPMENT_PICKUP_COMPLETE
	EKT_RECEIVED                         = constants.EKT_RECEIVED
	EKT_LPD_GENERATED                    = constants.EKT_LPD_GENERATED
	EKT_SHIPMENT_RTO_CONFIRMED           = constants.EKT_SHIPMENT_RTO_CONFIRMED
	EKT_PICKUP_CANCELLED                 = constants.EKT_PICKUP_CANCELLED
	EKT_RECEIVED_AT_DH                   = constants.EKT_RECEIVED_AT_DH
	EKT_SHIPMENT_OUT_FOR_DELIVERY        = constants.EKT_SHIPMENT_OUT_FOR_DELIVERY
	EKT_SHIPMENT_DELIVERED               = constants.EKT_SHIPMENT_DELIVERED
	EKT_SHIPMENT_RTO_CREATED             = constants.EKT_SHIPMENT_RTO_CREATED
	EKT_SHIPMENT_RTO_IN_TRANSIT          = constants.EKT_SHIPMENT_RTO_IN_TRANSIT
	EKT_RETURN_EXPECTED                  = constants.EKT_RETURN_EXPECTED
	EKT_RETURN_RECEIVED_AT_DH            = constants.EKT_RETURN_RECEIVED_AT_DH
	EKT_RETURN_OUT_FOR_DELIVERY          = constants.EKT_RETURN_OUT_FOR_DELIVERY
	EKT_SHIPMENT_RTO_COMPLETED           = constants.EKT_SHIPMENT_RTO_COMPLETED
	EKT_SHIPMENT_UNDELIVERED_UNATTEMPTED = constants.EKT_SHIPMENT_UNDELIVERED_UNATTEMPTED
	EKT_SHIPMENT_MISROUTED               = constants.EKT_SHIPMENT_MISROUTED
	EKT_PICKUP_RESCHEDULED               = constants.EKT_PICKUP_RESCHEDULED
	EKT_SHIPMENT_RECEIVED                = constants.EKT_SHIPMENT_RECEIVED
	EKT_SHIPMENT_SHIPPED                 = constants.EKT_SHIPMENT_SHIPPED
	EKT_EXPECTED                         = constants.EKT_EXPECTED
	EKT_SHIPMENT_UNDELIVERED_ATTEMPTED   = constants.EKT_SHIPMENT_UNDELIVERED_ATTEMPTED
	EKT_OUT_FOR_DELIVERY_UPDATE          = constants.EKT_OUT_FOR_DELIVERY_UPDATE
	EKT_DELIVERY_ATTEMPT_METADATA        = constants.EKT_DELIVERY_ATTEMPT_METADATA
	EKT_RETURN_RECEIVED                  = constants.EKT_RETURN_RECEIVED
	EKT_RETURN_DELIVERED                 = constants.EKT_RETURN_DELIVERED
	EKT_RETURN_UNDELIVERED_ATTEMPTED     = constants.EKT_RETURN_UNDELIVERED_ATTEMPTED
)



var ValidEkartStatus = []string{
	EKT_SHIPMENT_CREATED,
	EKT_PICKUP_OUT_FOR_PICKUP,
	EKT_PICKUP_REATTEMPT,
	EKT_SHIPMENT_PICKUP_COMPLETE,
	EKT_RECEIVED,
	EKT_SHIPMENT_RTO_CONFIRMED,
	EKT_PICKUP_CANCELLED,
	EKT_RECEIVED_AT_DH,
	EKT_SHIPMENT_OUT_FOR_DELIVERY,
	EKT_RETURN_RECEIVED,
	EKT_SHIPMENT_DELIVERED,
	EKT_SHIPMENT_RECEIVED,
	EKT_SHIPMENT_SHIPPED,
	EKT_SHIPMENT_UNDELIVERED_ATTEMPTED,
	EKT_RETURN_OUT_FOR_DELIVERY,
	EKT_SHIPMENT_RTO_COMPLETED,
	EKT_SHIPMENT_UNDELIVERED_UNATTEMPTED,
	EKT_SHIPMENT_MISROUTED,
	EKT_PICKUP_RESCHEDULED,
	EKT_SHIPMENT_RTO_CREATED,
	EKT_SHIPMENT_RTO_IN_TRANSIT,
	EKT_RETURN_EXPECTED,
	EKT_RETURN_RECEIVED_AT_DH,
	EKT_RETURN_DELIVERED,
	EKT_RETURN_UNDELIVERED_ATTEMPTED,
}
