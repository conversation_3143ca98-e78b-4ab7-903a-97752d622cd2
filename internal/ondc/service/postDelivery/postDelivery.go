package postdelivery

import (
	"sync"
)

// RatingCache stores information about submitted ratings
type RatingCache struct {
	mu    sync.RWMutex
	cache map[int]bool // key is just the orderID
}

// Global instance with proper initialization and protection
var (
	ratingCache   *RatingCache
	initCacheOnce sync.Once
)

// InitRatingCache initializes the singleton instance of RatingCache
// Using sync.Once ensures the cache is initialized exactly once
func InitRatingCache() {
	initCacheOnce.Do(func() {
		ratingCache = &RatingCache{
			cache: make(map[int]bool),
		}
	})
}

// Add adds a rating to the cache
func Add(orderID int) {
	if ratingCache == nil {
		InitRatingCache()
	}
	ratingCache.mu.Lock()
	defer ratingCache.mu.Unlock()
	ratingCache.cache[orderID] = true
}

// Contains checks if a rating exists in the cache
func Contains(orderID int) bool {
	if ratingCache == nil {
		return false
	}
	ratingCache.mu.RLock()
	defer ratingCache.mu.RUnlock()
	return ratingCache.cache[orderID]
}

// Clear empties the cache - useful for testing or resetting state
func Clear() {
	if ratingCache == nil {
		return
	}
	ratingCache.mu.Lock()
	defer ratingCache.mu.Unlock()
	// Create a new map rather than trying to clear the existing one
	ratingCache.cache = make(map[int]bool)
}

// Remove removes a rating from the cache
func Remove(orderID int) {
	if ratingCache == nil {
		return
	}
	ratingCache.mu.Lock()
	defer ratingCache.mu.Unlock()
	delete(ratingCache.cache, orderID)
}

// Size returns the current size of the cache
func Size() int {
	if ratingCache == nil {
		return 0
	}
	ratingCache.mu.RLock()
	defer ratingCache.mu.RUnlock()
	return len(ratingCache.cache)
}
