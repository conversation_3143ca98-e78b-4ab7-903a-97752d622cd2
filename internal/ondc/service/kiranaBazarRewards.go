package service

import (
	"context"
	"fmt"
	"kc/internal/ondc/models/dto"
)

func (s *Service) GetUserRewards(ctx context.Context, request *dto.GetKiranaBazarRewardsRequest) (*dto.GetKiranaBazarRewardsResponse, error) {
	const (
		defaultLimit = 20
		maxLimit     = 100
	)

	// Set default pagination if not provided
	if request.Meta.Limit == 0 {
		request.Meta.Limit = defaultLimit
		request.Meta.Offset = 0
	}

	// Validate pagination
	if request.Meta.Limit > maxLimit || request.Meta.Limit < 0 {
		return nil, fmt.Errorf("invalid limit: must be between 0 and %d", maxLimit)
	}
	if request.Meta.Offset < 0 {
		return nil, fmt.Errorf("invalid offset: must be non-negative")
	}

	rewards := []dto.KiranaBazarRewardsData{}
	baseQuery := `
        SELECT 
            kr.id,
            category_id,
            name,
            code,
            meta,
            is_active,
            is_oos,
            is_default,
            size_variant_code,
            image_urls,
            name_label,
            code_label,
            ratings_sum,
            ratings_count,
            user_id,
            product_id,
            address,
            awb,
            courier,
            kr.created_at,
            kr.updated_at,
            order_status,
            processing_status,
            shipment_status,
            display_status,
            is_archived,
            tracking_link,
			tentative_delivery_date
        FROM kiranaclubdb.kiranabazar_rewards kr
        JOIN kiranaclubdb.kiranabazar_products kp ON kr.product_id = kp.id`

	var query string
	if request.Data.RewardID != "" {
		query = fmt.Sprintf("%s WHERE kr.id = %s", baseQuery, request.Data.RewardID)
	} else if request.UserID != "" {
		query = fmt.Sprintf("%s WHERE kr.user_id = '%s' ORDER BY created_at DESC LIMIT %d OFFSET %d",
			baseQuery, request.UserID, request.Meta.Limit, request.Meta.Offset)
	} else {
		return nil, fmt.Errorf("either reward_id or user_id must be provided")
	}

	// Execute query
	_, err := s.repository.CustomQuery(&rewards, query)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch rewards: %w", err)
	}

	return &dto.GetKiranaBazarRewardsResponse{
		Data: rewards,
		Meta: request.Meta,
	}, nil
}
