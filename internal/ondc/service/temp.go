package service

import (
	"fmt"
	"kc/internal/ondc/utils"
)

type OfferSkuConfig struct {
	EligibleCohort string            `json:"eligible_cohort"`
	SkuMappings    map[string]string `json:"sku_mappings"` // original -> offer
}

// OfferSkuEligibilityResult contains all the information about user's offer SKU eligibility
type OfferSkuEligibilityResult struct {
	IsEligible           bool              `json:"is_eligible"`
	CacheKey             string            `json:"cache_key"`
	OfferSkuReplacements map[string]string `json:"offer_sku_replacements"`
	ExcludedSkus         []string          `json:"excluded_skus"`
	EligibilityReason    string            `json:"eligibility_reason"`
	HasOfferConfig       bool              `json:"has_offer_config"`
}

// Add this to your constants or configuration file
var OFFER_SKU_MAPPING = map[string]OfferSkuConfig{
	utils.ZOFF_FOODS: {
		EligibleCohort: "ALL_USERS",
		SkuMappings: map[string]string{
			"2319": "2428", // original_sku_id -> offer_sku_id
			"2344": "2432",
			"2328": "2441",
			"2320": "2429",
			"2345": "2433",
			"2321": "2430",
			"2338": "2431",
		},
	},
	utils.APSARA_TEA: {
		EligibleCohort: "ALL_USERS",
		SkuMappings: map[string]string{
			"223": "2435",
			"225": "2437",
			"224": "2436",
			"283": "2440",
			"282": "2439",
			"226": "2438",
		},
	},
	utils.RSB_SUPER_STOCKIST: {
		EligibleCohort: utils.RSB_STOCKIST_USER_COHORT,
		SkuMappings: map[string]string{
			"2021": "2392", // Keep existing Nisha Hair Color mapping
			"1042": "2442",
			"1108": "2443",
			"2268": "2822",
		},
	},
}

// GetOfferSkuEligibilityResult is the centralized function that contains all the logic
func GetOfferSkuEligibilityResult(seller string, userCohorts []string, userOverallConfirmedOrderCount int) *OfferSkuEligibilityResult {
	config, exists := OFFER_SKU_MAPPING[seller]
	if !exists {
		return &OfferSkuEligibilityResult{
			IsEligible:           false,
			CacheKey:             fmt.Sprintf("%s_default", seller),
			OfferSkuReplacements: make(map[string]string),
			ExcludedSkus:         []string{},
			EligibilityReason:    "No offer configuration found for seller",
			HasOfferConfig:       false,
		}
	}

	// Core eligibility logic - this is the single source of truth
	userInEligibleCohort := includes(userCohorts, config.EligibleCohort)
	userHasZeroOrders := userOverallConfirmedOrderCount == 0
	isEligible := userInEligibleCohort && userHasZeroOrders

	var eligibilityReason string
	var cacheKey string
	var offerSkuReplacements map[string]string
	var excludedSkus []string

	if isEligible {
		eligibilityReason = "User is eligible for offer SKUs"
		cacheKey = fmt.Sprintf("%s_offer", seller)
		offerSkuReplacements = config.SkuMappings
		excludedSkus = append(excludedSkus, getOfferSkusToExclude(config.SkuMappings)...)
	} else {
		// Determine specific reason for ineligibility
		if !userInEligibleCohort {
			eligibilityReason = fmt.Sprintf("User not in eligible cohort: %s (user cohorts: %v)",
				config.EligibleCohort, userCohorts)
		} else {
			eligibilityReason = fmt.Sprintf("User has %d confirmed orders (needs 0)", userOverallConfirmedOrderCount)
		}

		cacheKey = fmt.Sprintf("%s_regular", seller)
		offerSkuReplacements = make(map[string]string)
		excludedSkus = append(excludedSkus, getOriginalSkusToExclude(config.SkuMappings)...)
	}

	return &OfferSkuEligibilityResult{
		IsEligible:           isEligible,
		CacheKey:             cacheKey,
		OfferSkuReplacements: offerSkuReplacements,
		ExcludedSkus:         excludedSkus,
		EligibilityReason:    eligibilityReason,
		HasOfferConfig:       true,
	}
}

func getOriginalSkusToExclude(skuMappings map[string]string) []string {
	var excludeSkus []string
	for _, offerSku := range skuMappings {
		excludeSkus = append(excludeSkus, offerSku)
	}
	return excludeSkus
}

func getOfferSkusToExclude(skuMappings map[string]string) []string {
	var excludeSkus []string
	for originalSku := range skuMappings {
		excludeSkus = append(excludeSkus, originalSku)
	}
	return excludeSkus
}

func applyOfferSkuReplacements(productIds []string, replacements map[string]string) []string {
	if len(replacements) == 0 {
		return productIds
	}

	filteredIds := make([]string, 0, len(productIds))
	for _, productId := range productIds {
		if offerSku, shouldReplace := replacements[productId]; shouldReplace {
			filteredIds = append(filteredIds, offerSku)
		} else {
			filteredIds = append(filteredIds, productId)
		}
	}
	return filteredIds
}

func filterExcludedSkus(productIds []string, excludeSkus []string) []string {
	if len(excludeSkus) == 0 {
		return productIds
	}

	excludeSet := make(map[string]bool)
	for _, sku := range excludeSkus {
		excludeSet[sku] = true
	}

	filtered := make([]string, 0, len(productIds))
	for _, productId := range productIds {
		if !excludeSet[productId] {
			filtered = append(filtered, productId)
		}
	}
	return filtered
}

// checkOfferSkuEligibility checks if a specific product should be marked as OOS based on offer SKU rules
func checkOfferSkuEligibility(productID int64, seller string, userCohorts []string, userOverallConfirmedOrderCount int) bool {
	config, exists := OFFER_SKU_MAPPING[seller]
	if !exists {
		return false // No offer config for this seller
	}

	productIDStr := fmt.Sprintf("%d", productID)

	// Check if this product is an offer SKU
	var isOfferSku bool
	for _, offerSku := range config.SkuMappings {
		if offerSku == productIDStr {
			isOfferSku = true
			break
		}
	}

	if !isOfferSku {
		return false // Not an offer SKU, no need to mark OOS
	}

	// Check if user is NOT eligible for offer SKUs
	userInEligibleCohort := includes(userCohorts, config.EligibleCohort)
	userHasZeroOrders := userOverallConfirmedOrderCount == 0

	// If user is NOT in eligible cohort OR has orders, mark offer SKU as OOS
	return !userInEligibleCohort || !userHasZeroOrders
}

func getAllOfferSkus() []string {
	var allOfferSkus []string
	for _, config := range OFFER_SKU_MAPPING {
		for _, offerSku := range config.SkuMappings {
			allOfferSkus = append(allOfferSkus, offerSku)
		}
	}
	if len(allOfferSkus) == 0 {
		allOfferSkus = []string{"100000"}
	}
	return allOfferSkus
}
