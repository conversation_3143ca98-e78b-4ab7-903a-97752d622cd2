package service

import (
	"fmt"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"math"
	"sort"
	"strings"
)

// ProductDiff represents the difference in a product between two bills
type ProductDiff struct {
	ProductName    string  `json:"product_name"`
	Size           string  `json:"size"`
	OldQuantity    int     `json:"old_quantity"`
	NewQuantity    int     `json:"new_quantity"`
	OldTotalValue  float64 `json:"old_total_value"`
	NewTotalValue  float64 `json:"new_total_value"`
	DifferenceType string  `json:"difference_type"` // "ADDED", "REMOVED", "QUANTITY_CHANGE"
}

// BillDifference contains all the differences between two bills
type BillDifference struct {
	ProductDiffs      []ProductDiff `json:"product_difference"`
	DiscountChanges   []PricingDiff `json:"discount_difference"`
	OldTotalAmount    float64       `json:"old_total_amount"`
	NewTotalAmount    float64       `json:"new_total_amount"`
	TotalAmountChange float64       `json:"total_amount_change"`
}

// PricingDiff represents changes in pricing items (discounts, charges)
type PricingDiff struct {
	Key           string  `json:"key"`
	OldValue      float64 `json:"old_value"`
	NewValue      float64 `json:"new_value"`
	DifferenceAmt float64 `json:"difference_amount"`
}

// SortProductDiffs sorts ProductDiff array in the order: REMOVED, QUANTITY_CHANGE, ADDED
func SortProductDiffs(diffs []ProductDiff) []ProductDiff {
	// Create a copy to avoid modifying the original slice
	sortedDiffs := make([]ProductDiff, len(diffs))
	copy(sortedDiffs, diffs)

	// Define priority map for different types
	priorityMap := map[string]int{
		"REMOVED":         1,
		"QUANTITY_CHANGE": 2,
		"ADDED":           3,
	}

	// Sort by differenceType priority
	sort.SliceStable(sortedDiffs, func(i, j int) bool {
		// Get priorities (default to high number if type not found)
		priorityI := priorityMap[sortedDiffs[i].DifferenceType]
		priorityJ := priorityMap[sortedDiffs[j].DifferenceType]

		// Sort by priority first
		if priorityI != priorityJ {
			return priorityI < priorityJ
		}

		// If same priority, sort alphabetically by product name
		return sortedDiffs[i].ProductName < sortedDiffs[j].ProductName
	})

	return sortedDiffs
}

// CompareBills analyzes differences between two bill details
func CompareBills(oldBillJSON, newBillJSON dto.BillDetails) (*BillDifference, error) {
	// Create the difference result
	diff := &BillDifference{
		ProductDiffs: []ProductDiff{},
	}

	// Product mapping for easy lookup
	oldProducts := make(map[string]shared.ProductPrice)
	for _, product := range oldBillJSON.ProductsPricing {
		oldProducts[product.ProductName] = product
	}

	newProducts := make(map[string]shared.ProductPrice)
	for _, product := range newBillJSON.ProductsPricing {
		newProducts[product.ProductName] = product
	}

	// Find products that were in old bill but not in new (removed)
	for name, oldProduct := range oldProducts {
		if _, exists := newProducts[name]; !exists {
			oldTotalValue := extractFloat(oldProduct.TotalValue)
			diff.ProductDiffs = append(diff.ProductDiffs, ProductDiff{
				ProductName:    name,
				Size:           oldProduct.Size,
				OldQuantity:    oldProduct.Quantity,
				NewQuantity:    0,
				OldTotalValue:  oldTotalValue,
				NewTotalValue:  0,
				DifferenceType: "REMOVED",
			})
		}
	}

	// Find products that are in new bill but not in old (added) or changed
	for name, newProduct := range newProducts {
		newTotalValue := extractFloat(newProduct.TotalValue)

		if oldProduct, exists := oldProducts[name]; !exists {
			// Product was added
			diff.ProductDiffs = append(diff.ProductDiffs, ProductDiff{
				ProductName:    name,
				Size:           newProduct.Size,
				OldQuantity:    0,
				NewQuantity:    newProduct.Quantity,
				OldTotalValue:  0,
				NewTotalValue:  newTotalValue,
				DifferenceType: "ADDED",
			})
		} else {
			// Product exists in both - check for changes
			oldTotalValue := extractFloat(oldProduct.TotalValue)

			if oldProduct.Quantity != newProduct.Quantity ||
				math.Abs(oldTotalValue-newTotalValue) > 0.01 {
				diff.ProductDiffs = append(diff.ProductDiffs, ProductDiff{
					ProductName:    name,
					Size:           newProduct.Size,
					OldQuantity:    oldProduct.Quantity,
					NewQuantity:    newProduct.Quantity,
					OldTotalValue:  oldTotalValue,
					NewTotalValue:  newTotalValue,
					DifferenceType: "QUANTITY_CHANGE",
				})
			}
		}
	}

	// Extract total amounts
	var oldTotal, newTotal float64
	for _, pricing := range oldBillJSON.TotalPricing {
		if pricing.Key == "टोटल" {
			oldTotal = pricing.TotalValue
			break
		}
	}

	for _, pricing := range newBillJSON.TotalPricing {
		if pricing.Key == "टोटल" {
			newTotal = pricing.TotalValue
			break
		}
	}

	diff.OldTotalAmount = oldTotal
	diff.NewTotalAmount = newTotal
	diff.TotalAmountChange = newTotal - oldTotal

	// Check for discount changes
	oldDiscounts := make(map[string]float64)
	for _, discount := range oldBillJSON.DiscountPricing {
		if !discount.IsInternal {
			oldDiscounts[discount.Key] = discount.TotalValue
		}
	}

	newDiscounts := make(map[string]float64)
	for _, discount := range newBillJSON.DiscountPricing {
		if !discount.IsInternal {
			newDiscounts[discount.Key] = discount.TotalValue
		}
	}

	// Find all discount keys from both bills
	allDiscountKeys := make(map[string]bool)
	for k := range oldDiscounts {
		allDiscountKeys[k] = true
	}
	for k := range newDiscounts {
		allDiscountKeys[k] = true
	}

	// Compare discounts
	for key := range allDiscountKeys {
		oldValue := oldDiscounts[key]
		newValue := newDiscounts[key]

		if _, exists := oldDiscounts[key]; !exists {
			// New discount added
			diff.DiscountChanges = append(diff.DiscountChanges, PricingDiff{
				Key:           key,
				OldValue:      0,
				NewValue:      newValue,
				DifferenceAmt: newValue,
			})
		} else if _, exists := newDiscounts[key]; !exists {
			// Old discount removed
			diff.DiscountChanges = append(diff.DiscountChanges, PricingDiff{
				Key:           key,
				OldValue:      oldValue,
				NewValue:      0,
				DifferenceAmt: -oldValue,
			})
		} else if math.Abs(oldValue-newValue) > 0.01 {
			// Discount value changed
			diff.DiscountChanges = append(diff.DiscountChanges, PricingDiff{
				Key:           key,
				OldValue:      oldValue,
				NewValue:      newValue,
				DifferenceAmt: newValue - oldValue,
			})
		}
	}

	// Sort the product differences in the order: REMOVED, QUANTITY_CHANGE, ADDED
	diff.ProductDiffs = SortProductDiffs(diff.ProductDiffs)

	return diff, nil
}

// Helper function to extract float from price strings (e.g. "₹100.00")
func extractFloat(value string) float64 {
	// Handle empty strings
	if value == "" {
		return 0
	}

	// Remove currency symbol and any formatting
	value = strings.Replace(value, "₹", "", -1)
	value = strings.Replace(value, ",", "", -1)

	// Handle strikethrough text by removing all non-numeric characters
	// except for decimal point and negative sign
	cleanValue := ""
	for _, char := range value {
		if (char >= '0' && char <= '9') || char == '.' || char == '-' {
			cleanValue += string(char)
		}
	}

	// Take the first numeric part in case there's additional text
	var result float64
	_, err := fmt.Sscanf(cleanValue, "%f", &result)
	if err != nil {
		// If parsing fails, return 0
		return 0
	}

	return result
}
