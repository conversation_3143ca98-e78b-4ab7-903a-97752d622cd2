package service

import (
	"context"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	"kc/internal/ondc/utils"
	"strings"
	"sync"
	"time"

	"encoding/json"
)

// ProcessAutoReplyForOrderDelay handles automated replies for order delay tickets
func (s *Service) ProcessAutoReplyForOrderDelay(ctx context.Context, ticketID string, orderID int, userID string) (*dto.AutoReplyResult, error) {
	// Get ticket details to verify category and subcategory
	ticket := dao.CSTicket{}
	_, err := s.repository.Find(map[string]interface{}{
		"id": ticketID,
	}, &ticket)
	if err != nil {
		return nil, fmt.Errorf("error fetching ticket: %v", err)
	}

	// Check if ticket qualifies for auto reply
	if !s.shouldProcessAutoReply(ctx, &ticket) {
		return &dto.AutoReplyResult{
			ShouldSendAutoReply: false,
		}, nil
	}

	var wg sync.WaitGroup
	var orderDetails *dto.CsTicketOrderDetails
	var scanErr, orderErr error
	var currentCity *string

	wg.Add(2)

	go func() {
		defer wg.Done()
		orderDetails, _, orderErr = getKiranaBazarOrderDetails(s.repository, fmt.Sprintf("%d", orderID))
	}()

	go func() {
		defer wg.Done()
		_, _, currentCity, scanErr = s.GetOrderScans(ctx, fmt.Sprintf("%d", orderID))
	}()

	wg.Wait()

	if orderErr != nil {
		return nil, fmt.Errorf("failed to get order details: %w", orderErr)
	}
	if scanErr != nil {
		fmt.Printf("Error fetching scans: %v\n", scanErr)
	}

	// if order is NDR or attempt count is greater than 0, don't send auto reply
	if (orderDetails.Status != nil) && (*orderDetails.Status == displaystatus.NDR) || (orderDetails.AttemptCount != nil){
		return &dto.AutoReplyResult{
			ShouldSendAutoReply: false,
		}, nil
	}

	// Determine auto reply message
	currentCityValue := ""
	if currentCity != nil {
		currentCityValue = *currentCity
	}
	result := s.determineAutoReplyMessage(orderDetails, currentCityValue)

	if result.ShouldSendAutoReply {
		// Send the automated message
		err = s.sendAutoReplyMessage(ctx, ticket, result.Message)
		if err != nil {
			return nil, fmt.Errorf("error sending auto reply message: %v", err)
		}

		// Resolve ticket if required
		if result.ShouldResolveTicket {
			err = s.resolveTicketAutomatically(ctx, ticket, result.ResolutionNote)
			if err != nil {
				fmt.Printf("Error resolving ticket automatically: %v\n", err)
			}
		}
	}

	return result, nil
}

// CourierInfo holds courier and AWB information
type CourierInfo struct {
	Courier   string
	AWBNumber string
}

// shouldProcessAutoReply checks if the ticket qualifies for auto reply
func (s *Service) shouldProcessAutoReply(ctx context.Context, ticket *dao.CSTicket) bool {
	// Parse ticket meta to get issue details
	var ticketMeta []dto.AppSubmitTicketIssueDetails
	if err := json.Unmarshal(ticket.Meta, &ticketMeta); err != nil {
		fmt.Printf("Error unmarshaling ticket meta: %v\n", err)
		return false
	}

	if len(ticketMeta) == 0 {
		return false
	}

	issueDetails := ticketMeta[0]

	// Check if category is "orderStatus" (मेरा ऑर्डर कब आएगा?)
	if issueDetails.IssueCategory.ID != "orderStatus" {
		return false
	}

	// Check if subcategory is "delayInOrder" (आर्डर आने में देरी हो रही है)
	if issueDetails.IssueSubCategory.ID != "delayInOrder" {
		return false
	}

	return true
}

// determineAutoReplyMessage determines the appropriate auto reply message
func (s *Service) determineAutoReplyMessage(orderDetails *dto.CsTicketOrderDetails, currentLocation string) *dto.AutoReplyResult {
	loc, _ := time.LoadLocation("Asia/Kolkata")
	now := time.Now().In(loc)

	// Check if order is out for delivery and current time is before 7 PM and order status is not NDR
	if (orderDetails.OrderOfd != nil) && (orderDetails.Status != nil && *orderDetails.Status != displaystatus.NDR) {
		ofdTime := time.UnixMilli(*orderDetails.OrderOfd)
		currentHour := now.Hour()
		// Check if OFD was today and current time is before 7 PM
		if isSameDay(ofdTime, now) && currentHour < 19 {
			return &dto.AutoReplyResult{
				ShouldSendAutoReply: true,
				Message:             "आपका ऑर्डर आज डिलीवरी के लिए निकला है। यह आज डिलीवर किया जाएगा।",
				ShouldResolveTicket: true,
				ResolutionNote:      "Ticket resolved by automation because order was out for delivery at the time of ticket creation",
			}
		}
	}

	// Check if PDD is in next 2 days or order dispatched in last 4 days
	shouldSendDispatchedMessageFlag, resolveNote := s.shouldSendDispatchedMessage(orderDetails)
	if shouldSendDispatchedMessageFlag {
		var courier, awbNumber string
		if orderDetails.Courier != nil {
			courier = *orderDetails.Courier
		}
		if orderDetails.AwbNumber != nil {
			awbNumber = *orderDetails.AwbNumber
		}
		trackingLink := s.generateTrackingLink(courier, awbNumber)
		message := ""
		if orderDetails.PromisedDelivery != nil {
			// PDD available - format the date
			pddDate := utils.ConvertUTCToIST(*orderDetails.PromisedDelivery)

			location := currentLocation
			if location == "" {
				location = "वेयरहाउस"
			}

			message += fmt.Sprintf("आपका ऑर्डर %s से निकल चुका है और %s तक डिलीवरी हो जाएगी।", location, pddDate)
		} else {
			// PDD not available - use tracking link
			message += "आपका ऑर्डर इन-ट्रांज़िट है और जल्द ही डिलीवर हो जाएगा।"
		}
		if trackingLink != "" {
			message += fmt.Sprintf(" आप इसे नीचे दिए गए लिंक से ट्रैक कर सकते हैं:\n%s", trackingLink)
		}

		return &dto.AutoReplyResult{
			ShouldSendAutoReply: true,
			Message:             message,
			ShouldResolveTicket: true,
			ResolutionNote:      resolveNote,
		}
	}

	// Check if order placed in last 3 days and is confirmed
	shouldSendConfirmedMessageFlag, resolveNote := s.shouldSendConfirmedMessage(orderDetails)
	if shouldSendConfirmedMessageFlag {
		return &dto.AutoReplyResult{
			ShouldSendAutoReply: true,
			Message:             "आपका ऑर्डर तैयार किया जा रहा है, ये जल्द ही डिलीवर किया जाएगा।",
			ShouldResolveTicket: true,
			ResolutionNote:      resolveNote,
		}
	}

	return &dto.AutoReplyResult{
		ShouldSendAutoReply: false,
	}
}

// shouldSendDispatchedMessage checks if order qualifies for dispatched message
func (s *Service) shouldSendDispatchedMessage(orderDetails *dto.CsTicketOrderDetails) (bool, string) {
	// Check if PDD is in next 3 days
	now := time.Now()
	if orderDetails.PromisedDelivery != nil {
		pddTime := time.UnixMilli(*orderDetails.PromisedDelivery)
		daysDiff := getDaysDifference(now, pddTime)

		if daysDiff <= 3 && daysDiff >= 0 { // PDD is today or within next 3 days
			return true, "Ticket resolved by automation because PDD is in next 3 days"
		}
	}

	// Check if order dispatched in last 4 days
	if orderDetails.OrderDispatched != nil {
		dispatchTime := time.UnixMilli(*orderDetails.OrderDispatched)
		daysDiff := getDaysDifference(dispatchTime, now)

		if daysDiff <= 4 && daysDiff >= 0 { // Dispatched today or within last 4 days
			return true, "Ticket resolved by automation because order was dispatched in last 4 days"
		}
	}

	return false, ""
}

// shouldSendConfirmedMessage checks if order qualifies for confirmed message
func (s *Service) shouldSendConfirmedMessage(orderDetails *dto.CsTicketOrderDetails) (bool, string) {
	// Check if order placed in last 3 days and is confirmed
	now := time.Now()
	if orderDetails.OrderConfirmed != nil {
		placedTime := time.UnixMilli(*orderDetails.OrderConfirmed)
		daysDiff := getDaysDifference(placedTime, now)

		if daysDiff <= 3 && daysDiff >= 0 { // Placed today or within last 3 days
			return true, "Ticket resolved by automation because order was confirmed in last 3 days"
		}
	}

	return false, ""
}

// getDaysDifference calculates the difference in days between two dates (ignoring time)
func getDaysDifference(startTime, endTime time.Time) int {
	// Convert to dates only (ignore time)
	loc, _ := time.LoadLocation("Asia/Kolkata")
	startDate := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, loc)
	endDate := time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 0, 0, 0, 0, loc)

	// Calculate difference in days
	diff := endDate.Sub(startDate)
	days := int(diff.Hours() / 24)

	return days
}

// generateTrackingLink generates tracking link based on courier
func (s *Service) generateTrackingLink(courier, awbNumber string) string {
	if awbNumber == "" {
		return ""
	}

	switch strings.ToLower(courier) {
	case "delhivery":
		return fmt.Sprintf("https://www.delhivery.com/track-v2/package/%s", awbNumber)
	case "ekart":
		return fmt.Sprintf("https://www.ekartlogistics.com/ekartlogistics-web/shipmenttrack/%s", awbNumber)
	case "ekart large":
		return fmt.Sprintf("https://www.ekartlogistics.com/ekartlogistics-web/shipmenttrack/%s", awbNumber)
	default:
		return ""
	}
}

// sendAutoReplyMessage sends the automated reply message
func (s *Service) sendAutoReplyMessage(ctx context.Context, ticket dao.CSTicket, message string) error {
	// Get default agent for sending the message
	defaultAgent, err := s.GetAgentByEmail(ctx, "<EMAIL>")
	if err != nil {
		return fmt.Errorf("error fetching default agent: %v", err)
	}

	privateMessage := false
	_, err = s.SendMessage(ctx, &dto.SendMessageRequest{
		Data: dto.SendMessageRequestData{
			TicketID:       ticket.ID,
			ConversationID: ticket.ConversationID,
			Content:        message,
			SenderID:       defaultAgent.ID,
			SenderType:     utils.MESSAGE_SENDER_AGENT,
			Private:        &privateMessage,
			MessageType:    utils.MESSAGE_TYPE_AGENT_REPLY,
			Status:         "sent",
			Source:         "auto_reply_system",
		},
	})

	if err != nil {
		return fmt.Errorf("error sending auto reply message: %v", err)
	}

	return nil
}

// resolveTicketAutomatically resolves the ticket automatically
func (s *Service) resolveTicketAutomatically(ctx context.Context, ticket dao.CSTicket, resolutionNote string) error {
	_, err := s.PerformTicketAction(ctx, &dto.TicketActionRequest{
		Data: dto.TicketActionRequestData{
			TicketID:  ticket.ID,
			Action:    "change_status",
			ActionBy:  "<EMAIL>",
			NewStatus: utils.TICKET_STATUS_RESOLVED,
			ActionData: dto.ActionData{
				Note: resolutionNote,
			},
		},
	})

	if err != nil {
		return fmt.Errorf("error resolving ticket: %v", err)
	}

	return nil
}

// isSameDay checks if two times are on the same day
func isSameDay(t1, t2 time.Time) bool {
	y1, m1, d1 := t1.Date()
	y2, m2, d2 := t2.Date()
	return y1 == y2 && m1 == m2 && d1 == d2
}
