package service

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/service/products"
	"kc/internal/ondc/utils"
	"log"
	"net/http"
	"strconv"
	"time"
)

type Result struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	URL     string `json:"url"`
}

type PickListGenerationResponse struct {
	Result Result `json:"result"`
}

func (s *Service) CreatePickListForOrder(ctx context.Context, request dto.CreatePickListForOrderRequest) (*dto.GetPickListResponse, error) {
	orderID, err := strconv.ParseUint(request.Data.OrderID, 10, 64)
	if err != nil {
		return nil, err
	}

	orderInfo, err := GetOrderInfo(s.repository, int64(orderID))
	if err != nil {
		return nil, err
	}
	if orderInfo.ID == nil {
		return nil, errors.New("order not found")
	}

	orderDetails, err := GetOrderDetails(s.repository, request.Data.OrderID)
	if err != nil {
		return nil, err
	}

	fetchPickListPDFRequest := dto.PickListGenerationRequest{}
	orderItems := []dto.PickListGenerationRequestItems{}
	totalQty := 0
	for _, j := range orderDetails.Cart {
		meta := shared.KiranaBazarProductMeta{}
		err = json.Unmarshal(j.Meta, &meta)
		if err != nil {
			return nil, err
		}
		product, exists := products.GetProductByID(j.ID)
		if !exists {
			return nil, errors.New("product not found")
		}
		orderItem := dto.PickListGenerationRequestItems{
			ItemID:        j.ID,
			ItemName:      j.Name,
			ImageUrl:      j.ImageUrls[0],
			ItemHindiName: meta.HindiName,
			PackSize:      meta.PackSize,
			UnitPrice:     meta.WholesaleRate,
			Size:          meta.Quantity,
			Quantity:      int(j.Quantity),
		}

		if includes([]string{utils.PARIMAL, utils.RSB_SUPER_STOCKIST}, j.Seller) {
			orderItem.SKUCode = product.Code
		}

		// this is for combo sku so handling the description
		if orderInfo.Seller == utils.MICHIS || j.ID == "2566" || j.ID == "2565" {
			orderItem.Description = meta.Description
		}
		orderItems = append(orderItems, orderItem)
		totalQty += int(j.Quantity)
	}
	orderDetailss := dto.PickListGenerationRequestData{
		OrderID:     fmt.Sprintf("KC_%06d", orderID),
		TotalAmount: 1.1,
		Quantity:    totalQty,
		Items:       orderItems,
	}
	fetchPickListPDFRequest.Data = append(fetchPickListPDFRequest.Data, orderDetailss)
	url, err := fetchPickListPDF(fetchPickListPDFRequest)
	if err != nil {
		return nil, err
	}
	_, err = s.repository.CustomQuery(nil, fmt.Sprintf(`update kiranabazar_order_details set picklist = "%s" where order_id = %d`, url, orderID))
	if err != nil {
		fmt.Println("error updating picklist url in db", err)
		return nil, err
	}
	return &dto.GetPickListResponse{
		PDFUrl: url,
	}, err

}

func (s *Service) GetPickListPDF(ctx context.Context, request *dto.GetPickListPDFRequest) (*dto.GetPickListResponse, error) {
	parsedDate, _ := time.Parse("2006-01-02", request.Data.Date)
	epochStart := parsedDate.Unix() * 1000
	epochEnd := (parsedDate.AddDate(0, 0, 1).Unix() * 1000) - 1

	query := fmt.Sprintf(`SELECT
        ko.id,
        kod.order_details,
        kop.amount AS order_amount,
		kop.paid_amount,
        kbr.order_confirmed,
        ko.user_id,
		ko.seller
		FROM
			kiranabazar_orders ko
		JOIN
			kiranabazar_order_details kod ON ko.id = kod.order_id
		JOIN
			kiranabazar_order_payments kop ON ko.id = kop.order_id
		JOIN
			kiranaclubdb.kc_bazar_reconciliation kbr ON ko.id = kbr.order_id
		WHERE
        ko.seller = '%s'
        AND ko.display_status = 'CONFIRMED'
        AND kbr.order_confirmed BETWEEN %d AND %d`, request.Data.Seller, epochStart, epochEnd)

	orders := []dto.OrderExportData{}
	_, err := s.repository.CustomQuery(&orders, query)
	if err != nil {
		return nil, err
	}
	plgr := dto.PickListGenerationRequest{}

	for _, order := range orders {
		orderdls := dao.KiranaBazarOrderDetails{}
		err = json.Unmarshal([]byte(order.OrderDetails), &orderdls)
		if err != nil {
			return nil, err
		}
		totalQty := 0
		orderItems := []dto.PickListGenerationRequestItems{}
		for _, j := range orderdls.Cart {
			meta := shared.KiranaBazarProductMeta{}
			err = json.Unmarshal(j.Meta, &meta)
			if err != nil {
				return nil, err
			}
			product, exists := products.GetProductByID(j.ID)
			if !exists {
				return nil, errors.New("product not found")
			}
			orderItems = append(orderItems, dto.PickListGenerationRequestItems{
				ItemID:        j.ID,
				ItemName:      j.Name,
				ItemHindiName: meta.HindiName,
				PackSize:      meta.PackSize,
				ImageUrl:      j.ImageUrls[0],
				UnitPrice:     meta.WholesaleRate,
				SKUCode:       product.Code,
				Size:          meta.Quantity,
				Quantity:      int(j.Quantity),
			})

			// this is for combo sku so handling the description
			if order.Seller == utils.MICHIS || j.ID == "2566" || j.ID == "2565" {
				orderItems[len(orderItems)-1].Description = meta.Description
			}
			totalQty += int(j.Quantity)
		}
		orderDetails := dto.PickListGenerationRequestData{
			OrderID:     fmt.Sprint(order.ID),
			TotalAmount: order.OrderAmount,
			Quantity:    totalQty,
			Items:       orderItems,
		}
		plgr.Data = append(plgr.Data, orderDetails)
	}
	url, err := fetchPickListPDF(plgr)
	return &dto.GetPickListResponse{
		PDFUrl: url,
	}, err
}

func fetchPickListPDF(request dto.PickListGenerationRequest) (string, error) {
	fmt.Println("calling external api")
	const (
		baseURL      = "https://asia-south1-op-d2r.cloudfunctions.net"
		endpoint     = "/picklist-gen"
		timeout      = 30 * time.Second
		maxRetries   = 3
		retryBackoff = 500 * time.Millisecond
	)

	var (
		resp       *http.Response
		respBody   []byte
		retryCount = 0
		lastErrMsg string
	)

	for retryCount <= maxRetries {
		payloadBytes, err := json.Marshal(request)
		if err != nil {
			return "", fmt.Errorf("failed to marshal request: %w", err)
		}

		// Create request
		client := &http.Client{Timeout: timeout}
		req, err := http.NewRequest(http.MethodPost, baseURL+endpoint, bytes.NewReader(payloadBytes))
		if err != nil {
			return "", fmt.Errorf("failed to create request: %w", err)
		}

		// Set headers
		req.Header.Set("Content-Type", "application/json")

		// Execute request
		resp, err = client.Do(req)

		if err == nil && resp.StatusCode == http.StatusOK {
			// Success - break out of retry loop
			defer resp.Body.Close()

			// Read response
			respBody, err = io.ReadAll(resp.Body)
			if err != nil {
				lastErrMsg = fmt.Sprintf("failed to read response body: %v", err)
				retryCount++
				time.Sleep(retryBackoff * time.Duration(retryCount))
				continue
			}

			respp := PickListGenerationResponse{}
			err = json.Unmarshal(respBody, &respp)
			if err != nil {
				return "", fmt.Errorf("failed to unmarshal response: %w", err)
			}

			return respp.Result.Message, nil
		}

		// Handle error case
		if resp != nil {
			respBody, _ = io.ReadAll(resp.Body)
			resp.Body.Close()
			lastErrMsg = fmt.Sprintf("unexpected status code: %d, body: %s", resp.StatusCode, string(respBody))
		} else {
			lastErrMsg = fmt.Sprintf("failed to execute request: %v", err)
		}

		// Retry logic
		retryCount++
		if retryCount <= maxRetries {
			log.Printf("Retrying request (attempt %d of %d): %s", retryCount, maxRetries, lastErrMsg)
			time.Sleep(retryBackoff * time.Duration(retryCount))
		}
	}

	// All retries failed
	slack.SendSlackMessage(fmt.Sprintf("%s, Picklist generation failed after %d retries, last error: %s", request.Data[0].OrderID, maxRetries, lastErrMsg))
	return "", fmt.Errorf("after %d retries, last error: %s", maxRetries, lastErrMsg)
}
