package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	shipmentstatus "kc/internal/ondc/service/orderStatus/shipmentStatus"
	"kc/internal/ondc/utils"
	"log"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/mixpanel/mixpanel-go"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

func (s *Service) GetNDROrderV2(orderID int) (*dao.KiranaBazarNdrs, error) {
	conditions := map[string]interface{}{
		"order_id": orderID,
	}
	data := &dao.KiranaBazarNdrs{}
	_, err := s.repository.Find(conditions, data)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (s *Service) IsOrderKcFulfilled(orderID int) (bool, error) {
	query := fmt.Sprintf("select kam.awb_number from kiranabazar_awb_master kam where kam.order_id = %d and kam.is_primary = true and kam.account_name = 'kiranaclub';", orderID)
	type OrderData struct {
		AwbNumber string `json:"awb_number"`
	}
	data := []OrderData{}
	_, err := s.repository.CustomQuery(&data, query)
	if err != nil {
		return false, err
	}
	if len(data) == 0 {
		return false, nil
	}
	if data[0].AwbNumber == "" {
		return false, nil
	}
	return true, nil
}

func getFirstNonNil[T any](var1, var2, var3 *T) *T {
	if var1 != nil {
		return var1
	}
	if var2 != nil {
		return var2
	}
	if var3 != nil {
		return var3
	}
	return nil
}

func (s *Service) AddOrderAction(ctx context.Context, request *dto.AddOrderActionRequest) error {
	action := getFirstNonNil(request.CFAction, request.LMAction, nil)
	note := getFirstNonNil(request.CFNote, request.LMNote, request.OrderClosingNote)
	updatedAt := time.Now().UnixMilli()
	actionData := dao.OrderAction{
		UpdatedAt:   updatedAt,
		UpdatedBy:   request.UpdatedBy,
		MessageType: request.ActionType,
		Data: dao.OrderActionData{
			OrderID:        request.OrderID,
			OrderStatus:    request.OrderStatus,
			CFAssignedTo:   request.CFAssignedTo,
			LMAssignedTo:   request.LMAssignedTo,
			CallConnected:  request.CallConnected,
			Action:         action,
			NextActionAt:   request.NextActionAt,
			Note:           note,
			ReturnReason:   request.ReturnReason,
			CancelReason:   request.CancelReason,
			NDRAgentReason: request.NDRAgentReason,
			NDRUserReason:  request.NDRUserReason,
			OrderValue:     request.OrderValue,
			AttemptCount:   request.AttemptCount,
			Source:         request.Source,
		},
	}
	messageID, err := utils.GenerateShortBase64ID(actionData)
	if err != nil {
		return err
	}
	actionData.MessageID = messageID

	if request.ActionType == "add_ndr" {
		orderActionByte, err := json.Marshal([]dao.OrderAction{actionData})
		if err != nil {
			return err
		}
		orderActionLogs := dao.OrderActivityLogs{
			ID:        request.OrderActionID,
			UpdatedAt: updatedAt,
			UpdatedBy: request.UpdatedBy,
			Activity:  orderActionByte,
		}
		_, err = s.repository.Create(&orderActionLogs)
		if err != nil {
			return err
		}
	} else {
		orderActionByte, err := json.Marshal(actionData)
		if err != nil {
			return err
		}
		jsonStr := strings.Replace(string(orderActionByte), "'", "''", -1)
		orderActivityLogsQuery := fmt.Sprintf(`UPDATE kiranaclubdb.kiranabazar_order_activity_logs
              SET updated_at=%d, updated_by='%s', activity = JSON_ARRAY_APPEND(activity, '$', CAST('%s' AS JSON))
              WHERE id = '%s'`, updatedAt, request.UpdatedBy, jsonStr, request.OrderActionID)
		_, err = s.repository.CustomQuery(nil, orderActivityLogsQuery)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *Service) trackNdrUserFeedbackPn(ctx context.Context, orderID int64, userId string, orderValue float64, seller string) error {
	sentRedisKey := fmt.Sprintf("firstNdrPnSent_%d_%s", orderID, userId)
	submittedRedisKey := fmt.Sprintf("firstNdrPnTicketSubmitted_%d_%s", orderID, userId)
	istLocation, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		return err
	}
	todayDate := time.Now().In(istLocation).Format("2006-01-02")
	sentValue, err := s.GcpRedis.RedisClient.Get(ctx, sentRedisKey).Result()
	if err != nil {
		log.Println("Error getting value from Redis:", err)
	}
	submittedValue, err := s.GcpRedis.RedisClient.Get(ctx, submittedRedisKey).Result()
	if err != nil {
		log.Println("Error getting value from Redis:", err)
	}

	isSentSameDay := todayDate == sentValue
	isSubmitted := false
	if submittedValue != "" {
		isSubmitted = true
	}

	if !isSentSameDay && !isSubmitted {
		redirectionURL := fmt.Sprintf("https://webapps.retailpulse.ai/customer-support/guided-flow?order_id=%d&flow_id=fofdPlusOneFlow", orderID)
		caser := cases.Title(language.English)
		seller = caser.String(strings.ToLower(strings.ReplaceAll(seller, "_", " ")))
		eventObject := map[string]interface{}{
			"order_id":     fmt.Sprintf(`%d`, orderID),
			"order_status": "NDR",
			"order_value":  orderValue,
			"seller":       seller,
			"url":          redirectionURL,
		}
		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{userId},
			EventName:   "Order NDR User Feedback",
			EventObject: eventObject,
		})
		s.GcpRedis.RedisClient.Set(ctx, sentRedisKey, todayDate, time.Duration(45*24)*time.Hour)
	}
	return nil
}

func (s *Service) AddNDRV2(ctx context.Context, orderID string, orderStatus string, updatedBy string, ndrAgentReason string, source string, customerEscalated bool) (*string, error) {
	orderInt, err := strconv.Atoi(orderID)
	if err != nil {
		return nil, err
	}
	orderInfo, err := GetOrderEssentials(s.repository, int64(orderInt))
	if err != nil {
		return nil, err
	}
	if orderStatus == "" {
		orderStatus = *orderInfo.OrderStatus
	}

	defaultAttemptCount := 1
	defaultAttemptCountInt64 := int64(defaultAttemptCount)
	assignee := s.assignNDROrder(ctx, 1000, "V2")
	orderActionId := uuid.New().String()
	nowTimestamp := time.Now().UnixMilli()
	ndrOpenStatus := fmt.Sprintf(`%s`, utils.NDR_OPEN_STATUS)
	ndrData := dao.KiranaBazarNdrs{
		OrderID:           int64(orderInt),
		CFAssignedTo:      &assignee,
		LMAssignedTo:      &utils.B2B_NDR_ADMIN_HUNTER.Email,
		CFAction:          &utils.NDR_PENDING_STATUS,
		LMAction:          &utils.NDR_PENDING_STATUS,
		NDRAttemptCount:   &defaultAttemptCountInt64,
		NDRAgentReason:    &ndrAgentReason,
		OrderActionID:     &orderActionId,
		CreatedSource:     &source,
		Source:            &source,
		Status:            &ndrOpenStatus,
		CustomerEscalated: &customerEscalated,
		CreatedAt:         &nowTimestamp,
	}
	_, err = s.repository.Create(&ndrData)
	if err != nil {
		return nil, err
	}

	err = s.AddOrderAction(ctx, &dto.AddOrderActionRequest{
		OrderActionID:  orderActionId,
		ActionType:     "add_ndr",
		OrderID:        orderInt,
		OrderStatus:    orderStatus,
		UpdatedAt:      nowTimestamp,
		UpdatedBy:      updatedBy,
		CFAssignedTo:   *ndrData.CFAssignedTo,
		LMAssignedTo:   *ndrData.LMAssignedTo,
		NDRAgentReason: &ndrAgentReason,
		OrderValue:     orderInfo.OrderValue,
		AttemptCount:   &defaultAttemptCount,
		Source:         &source,
	})
	if err != nil {
		return nil, err
	}
	properties := map[string]interface{}{
		"distinct_id":   *orderInfo.UserID,
		"order_id":      orderID,
		"seller":        orderInfo.Seller,
		"email":         updatedBy,
		"assigned_to":   ndrData.CFAssignedTo,
		"order_value":   *orderInfo.OrderValue,
		"attempt_count": 1,
	}
	addIfNotEmpty := func(key string, value *string) {
		if (value != nil) && (*value != "") {
			properties[key] = value
		}
	}
	addIfNotEmpty("courier", orderInfo.Courier)
	addIfNotEmpty("ndr_agent_reason", &ndrAgentReason)

	s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order NDR - Action Allocated", *orderInfo.UserID, properties,
			fmt.Sprintf("%s_%s", "Order NDR - Action Allocated", orderID)),
	})
	go func() {
		err := s.trackNdrUserFeedbackPn(ctx, int64(orderInt), *orderInfo.UserID, *orderInfo.OrderValue, *orderInfo.Seller)
		if err != nil {
			fmt.Printf("Error sending ndr pn: %v\n", err)
		}
	}()

	message := "NDR Added Succesfully"
	return &message, nil
}

func calculateOrderPriority(order dto.NDROrderDetails) string {
	// Extract values for priority calculation
	isInbound := (order.Source == "Customer Escalation") // Based on the code logic
	isReattempt := order.ReAttempt
	isHighValue := (order.OrderValue > 5000)
	isMidValue := (order.OrderValue > 3000)

	// Determine if order is paid (assuming we need to check payment details from order_details or other fields)
	// This would need to be adjusted based on actual payment type field availability
	isPaid := false // You'll need to extract this from order details or add payment_type to query
	if (order.PaymentStatus == "PARTIALLY_PAID") || (order.PaymentStatus == "FULLY_PAID") {
		isPaid = true
	}

	// Extract past deliveries from user order stats
	pastDeliveries := order.TotalOrderDelivered
	if pastDeliveries == nil {
		zeroDeliveries := int64(0)
		pastDeliveries = &zeroDeliveries // Default to 0 if not available
	}
	pastDeliveriesInt := int(*pastDeliveries)

	// P0 Priority
	if isInbound && isReattempt && (isHighValue || isPaid || pastDeliveriesInt >= 5 || pastDeliveriesInt == 0) {
		return "P0"
	}

	// P1 Priority
	if isReattempt && (isMidValue || (pastDeliveriesInt >= 2 && pastDeliveriesInt <= 5) || isInbound) {
		return "P1"
	}

	// P2 Priority (default)
	return "P2"
}

func (s *Service) GetNDROrdersV2(ctx context.Context, request *dto.GetNDROrdersRequestV2) (*dto.GetNDROrdersResponseV2, error) {
	locationTime, _ := time.LoadLocation("Asia/Kolkata")
	getOrderQuery := `select 
							kno.order_id,
							ko.created_at as order_date,
							ko.user_id,
							ko.seller,
							ko.display_status as display_status,
							kno.created_at,
							u.name as customer_name, 
							u.phone as customer_phone,
							kam.nsl_code,
							kam.courier,
							kam.awb_number,
							kno.cf_updated_by,
							kno.lm_updated_by,
							kno.cf_assigned_to,
							kno.lm_assigned_to,
							kno.cf_action,
							kno.lm_action,
							kno.cf_ndr_user_reason,
							kop.amount as order_value,
							kop.status as payment_status,
							ko.order_status,
							kno.cf_call_connected,
							kno.cf_next_attempt_at as cf_next_attempt_at,
							kno.cf_note,
							kno.lm_note, 
							kno.lm_updated_at,
							kno.cf_updated_at,
							kno.source,
							kno.ndr_attempt_count,
							kno.ndr_agent_reason,
							kno.status,
							kno.created_source,
							kno.customer_escalated,
							ko.seller,
							kod.order_details,
							kno.order_action_id,
							uos.total_placed_order_amount as total_placed_order_amount,
							uos.total_confirmed_order_amount as total_confirmed_order_amount,
							uos.total_delivered_order_amount as total_delivered_order_amount,
							uos.total_returned_order_amount as total_returned_order_amount,
							uos.total_cancelled_order_amount as total_cancelled_order_amount,
							uos.order_placed as total_order_placed,
							uos.confirmed_order as total_order_confirmed,
							uos.order_delivered as total_order_delivered,
							uos.returned_order as total_order_returned,
							uos.cancelled_order as total_order_cancelled
						from kiranabazar_ndrs kno 
							left join kiranabazar_orders ko on kno.order_id = ko.id
							left join kiranabazar_order_payments kop on kno.order_id = kop.order_id
							left join kiranabazar_order_details kod on kno.order_id = kod.order_id
							left join kiranabazar_awb_master kam on kno.order_id = kam.order_id
							left join users u on ko.user_id = u.user_id
							left join user_order_stats uos ON ko.user_id = uos.user_id
						where 1=1`
	var getCountQuery string
	if request.Data.NDRStage == "CF" {
		getCountQuery = ` select
								SUM(CASE WHEN cf_action = 'PENDING' AND (cf_call_connected IS NULL OR cf_call_connected = '') THEN 1 ELSE 0 END) AS PENDING,
								SUM(CASE WHEN cf_action = 'PENDING' AND cf_call_connected = 'NO' THEN 1 ELSE 0 END) AS CALL_NOT_CONNECTED
						`
	} else {
		getCountQuery = fmt.Sprintf(` select
								SUM(CASE WHEN lm_action = 'PENDING' AND cf_action != 'PENDING' and kno.status != 'closed' THEN 1 ELSE 0 END) AS PENDING,
								SUM(CASE WHEN lm_action NOT IN ('PENDING', 'CLOSED') and kno.status != 'closed' THEN 1 ELSE 0 END) AS ACTION_TAKEN,
								SUM(CASE WHEN (lm_action = 'CLOSED' or kno.status = 'closed') AND kno.lm_updated_at >= %d THEN 1 ELSE 0 END) AS CLOSED
						`, time.Now().AddDate(0, 0, -7).UnixMilli())

	}
	getCountQuery += ` from kiranabazar_ndrs kno
							left join kiranabazar_orders ko on kno.order_id = ko.id 
							left join kiranabazar_awb_master kam on kno.order_id = kam.order_id where 1=1
					`

	if request.Data.Action == "PENDING" {
		if request.Data.NDRStage == "CF" {
			getOrderQuery += fmt.Sprintf(` and kno.cf_action = '%s' and kno.cf_call_connected is null`, request.Data.Action)
		} else {
			getOrderQuery += fmt.Sprintf(` and kno.lm_action = '%s' and kno.cf_action != '%s' and kno.status != 'closed'`, request.Data.Action, request.Data.Action)
		}
	}

	if request.Data.Action == "CALL_NOT_CONNECTED" {
		getOrderQuery += fmt.Sprintf(` and kno.cf_action = 'PENDING' and kno.cf_call_connected = 'NO'`)
	}

	if request.Data.Action == "ACTION_TAKEN" {
		getOrderQuery += fmt.Sprintf(` and kno.lm_action not in ('PENDING', 'CLOSED') and kno.status != 'closed'`)
	}

	if request.Data.Action == "CLOSED" {
		getOrderQuery += fmt.Sprintf(` and (kno.lm_action = 'CLOSED' or kno.status = 'closed') and kno.lm_updated_at >= %d`, time.Now().AddDate(0, 0, -7).UnixMilli())
	}

	if request.Data.CFAction != nil && len(*request.Data.CFAction) > 0 {
		getOrderQuery += fmt.Sprintf(` and kno.cf_action in ('%s')`, strings.Join(*request.Data.CFAction, "','"))
		getCountQuery += fmt.Sprintf(` and kno.cf_action in ('%s')`, strings.Join(*request.Data.CFAction, "','"))
	}
	if request.Data.LMAction != nil && len(*request.Data.LMAction) > 0 {
		getOrderQuery += fmt.Sprintf(` and kno.lm_action in ('%s')`, strings.Join(*request.Data.LMAction, "','"))
		getCountQuery += fmt.Sprintf(` and kno.lm_action in ('%s')`, strings.Join(*request.Data.LMAction, "','"))
	}

	if len(request.Data.Seller) > 0 {
		getOrderQuery += fmt.Sprintf(` and ko.seller in ('%s')`, strings.Join(request.Data.Seller, "','"))
		getCountQuery += fmt.Sprintf(` and ko.seller in ('%s')`, strings.Join(request.Data.Seller, "','"))
	}

	if len(request.Data.AssignedTo) > 0 {
		if request.Data.NDRStage == "CF" {
			getOrderQuery += fmt.Sprintf(` and kno.cf_assigned_to in ('%s')`, strings.Join(request.Data.AssignedTo, "','"))
			getCountQuery += fmt.Sprintf(` and kno.cf_assigned_to in ('%s')`, strings.Join(request.Data.AssignedTo, "','"))
		} else {
			getOrderQuery += fmt.Sprintf(` and kno.lm_assigned_to in ('%s')`, strings.Join(request.Data.AssignedTo, "','"))
			getCountQuery += fmt.Sprintf(` and kno.lm_assigned_to in ('%s')`, strings.Join(request.Data.AssignedTo, "','"))
		}
	}

	if request.Data.CallConnected != nil {
		getOrderQuery += fmt.Sprintf(` and kno.cf_call_connected = '%s'`, *request.Data.CallConnected)
	}

	getOrderQuery += ` and ko.display_status not in ('DELIVERED') and (kno.lm_action not in ('CLOSED') or kno.status != 'closed') and kam.nsl_code not in ('RD-AC') and kam.is_primary = true and kam.account_name = 'kiranaclub' and ko.is_archived = false and kno.ndr_agent_reason not in ('Code verified cancellation', 'Whatsapp verified cancellation')`
	getCountQuery += ` and ko.display_status not in ('DELIVERED') and (kno.lm_action not in ('CLOSED') or kno.status != 'closed') and kam.nsl_code not in ('RD-AC') and kam.is_primary = true and kam.account_name = 'kiranaclub' and ko.is_archived = false and kno.ndr_agent_reason not in ('Code verified cancellation', 'Whatsapp verified cancellation')`

	if request.Data.NDRStage == "LM" {
		getOrderQuery += fmt.Sprintf(` order by kop.amount desc`)
	} else {
		getOrderQuery += fmt.Sprintf(` order by ko.created_at desc`)
	}
	if request.Data.Limit != 0 {
		getOrderQuery += fmt.Sprintf(` limit %d offset %d;`, request.Data.Limit, request.Data.Offset)
	}

	ndrQueryResponse := []dto.NDROrderDetails{}
	_, err := s.repository.CustomQuery(&ndrQueryResponse, getOrderQuery)
	if err != nil {
		return nil, err
	}

	ndrCountResponse := map[string]interface{}{}
	_, err = s.repository.CustomQuery(&ndrCountResponse, getCountQuery)
	if err != nil {
		return nil, err
	}

	var ndrData []dto.NDROrderDetails
	for _, order := range ndrQueryResponse {
		var orderDetailsMap map[string]interface{}
		if err := json.Unmarshal(*order.OrderDetails, &orderDetailsMap); err != nil {
			log.Println(err)
			continue
		}

		alternatePhone := ""
		val, ok := orderDetailsMap["alternate_phone"].(string)
		if ok {
			alternatePhone = val
		}
		actionStatus := "Not Called Yet"
		if order.CFCallConnected != "" {
			if order.CFCallConnected == "YES" {
				actionStatus = "Call Connected"
			} else if order.CFCallConnected == "NO" {
				actionStatus = "Call Not Connected"
			}
		}
		ndrSource := "Outbound Call"
		if slices.Contains([]string{utils.TICKET_FLOW_FOFD, utils.TICKET_FLOW_APP_STANDARD}, order.Source) || order.CustomerEscalated {
			ndrSource = "Customer Escalation"
		}
		order.Source = ndrSource
		order.ActionStatus = actionStatus
		shippingAddress := orderDetailsMap["shipping_address"].(map[string]interface{})
		order.AddressName = shippingAddress["name"].(string)
		order.AddressCity = shippingAddress["district"].(string)
		order.AddressLine = shippingAddress["line"].(string)
		order.PostalCode = shippingAddress["postal_code"].(string)
		order.State = shippingAddress["state"].(string)
		order.GST = shippingAddress["gst"].(string)
		order.AddressID = shippingAddress["id"].(float64)
		order.StoreName = shippingAddress["store_name"].(string)
		order.HouseNumber = shippingAddress["house_number"].(string)
		order.Neighbourhood = shippingAddress["neighbourhood"].(string)
		order.Village = shippingAddress["village"].(string)
		order.Landmark = shippingAddress["landmark"].(string)
		order.AddressTag = shippingAddress["tag"].(string)
		order.AddressLine1 = shippingAddress["line1"].(string)
		order.AddressLine2 = shippingAddress["line2"].(string)
		order.Phone = shippingAddress["phone"].(string)
		order.AlternatePhone = alternatePhone
		order.OrderDetails = &[]byte{}
		if order.CFNextAttemptAt != 0 {
			order.NextAttemptAt = time.UnixMilli(order.CFNextAttemptAt).In(locationTime).Format("2006-01-02 15:04:05")
		}
		order.ReAttempt = false
		if slices.Contains([]string{"REATTEMPT", "REATTEMPT_CUSTOM"}, order.CFAction) {
			order.ReAttempt = true
		}

		if order.PaymentStatus == "UNPAID" {
			order.PaymentStatus = "COD"
		}

		order.Priority = calculateOrderPriority(order)
		ndrData = append(ndrData, order)
	}
	return &dto.GetNDROrdersResponseV2{
		Stats: ndrCountResponse,
		Data:  ndrData,
	}, nil
}

func (s *Service) UpdateNDROrderV2(ctx context.Context, request *dto.UpdateNDROrderRequestV2) (*dto.UpdateNDROrderResponse, error) {
	orderInt, err := strconv.Atoi(request.Data.OrderID)
	if err != nil {
		fmt.Println("Invalid order ID:", orderInt)
		return nil, err
	}
	orderInfo, err := GetOrderEssentials(s.repository, int64(orderInt))
	if err != nil {
		return nil, err
	}
	orderNDRInfo, err := s.GetNDROrderV2(orderInt)
	if err != nil {
		return nil, err
	}

	if (orderNDRInfo.LMAction != nil) && (*orderNDRInfo.LMAction == "CLOSED") {
		return nil, fmt.Errorf("order is already closed")
	}

	nskey := "cf"
	if request.Data.NDRStage == "LM" {
		nskey = "lm"
	} else if request.Data.NDRStage == "3PL" && request.Data.Source != nil && *request.Data.Source == "DELHIVERY" {
		nskey = "3pl"
	}
	if nskey == "3pl" {
		updateQuery := fmt.Sprintf(`UPDATE kiranabazar_ndrs set lm_action="%s", ndr_agent_reason="%s", ndr_attempt_count=%d WHERE order_id=%d`, utils.NDR_PENDING_STATUS, strings.ReplaceAll(*request.Data.NDRAgentReason, "'", "\\'"), *request.Data.AttemptCount, orderInt)

		_, err = s.repository.CustomQuery(nil, updateQuery)
		if err != nil {
			fmt.Println("Error executing update query:", err)
			return nil, err
		}
	} else {
		whereClause := fmt.Sprintf("order_id = '%d'", orderInt)
		updateParts := []string{}

		addField := func(column string, value interface{}) {
			if str, ok := value.(string); ok && str != "" {
				updateParts = append(updateParts, fmt.Sprintf("%s = '%s'", column, str))
			} else if num, ok := value.(int64); ok {
				updateParts = append(updateParts, fmt.Sprintf("%s = %d", column, num))
			} else if flag, ok := value.(bool); ok {
				updateParts = append(updateParts, fmt.Sprintf("%s = %t", column, flag))
			} else if floatnum, ok := value.(float64); ok {
				updateParts = append(updateParts, fmt.Sprintf("%s = %f", column, floatnum))
			}
		}
		addField(fmt.Sprintf("%s_updated_by", nskey), request.Data.UpdatedBy)
		addField(fmt.Sprintf("%s_updated_at", nskey), time.Now().UnixMilli())
		if request.Data.CallConnected != nil && *request.Data.CallConnected == "YES" && request.Data.Action == nil {
			return nil, fmt.Errorf("action is required when call connected is YES")
		}
		if request.Data.CallConnected != nil {
			addField(fmt.Sprintf("%s_call_connected", nskey), *request.Data.CallConnected)
		}

		if (request.Data.Action != nil) && (*request.Data.Action != "CLOSED") {
			addField(fmt.Sprintf("%s_action", nskey), *request.Data.Action)
		}
		if (request.Data.Action != nil) && (*request.Data.Action == "CLOSED") {
			addField("status", utils.NDR_CLOSED_STATUS)
			addField("lm_order_closing_note", *request.Data.Note)
		}
		if request.Data.NextActionAt != nil {
			t, err := time.Parse("2006-01-02T15:04:05", *request.Data.NextActionAt)
			if err != nil {
				return nil, fmt.Errorf("invalid next action at format: %v", err)
			}
			addField(fmt.Sprintf("%s_next_attempt_at", nskey), t.UTC().UnixMilli())
		}
		if request.Data.Reason != nil {
			addField(fmt.Sprintf("%s_ndr_user_reason", nskey), *request.Data.Reason)
		}
		if request.Data.Note != nil {
			addField(fmt.Sprintf("%s_note", nskey), *request.Data.Note)
		}
		if request.Data.Source != nil {
			addField("source", *request.Data.Source)
		}
		if (request.Data.CustomerEscalated != nil) && *request.Data.CustomerEscalated {
			addField("customer_escalated", *request.Data.CustomerEscalated)
		}
		updateQuery := fmt.Sprintf("UPDATE kiranabazar_ndrs SET %s WHERE %s", strings.Join(updateParts, ", "), whereClause)
		_, err = s.repository.CustomQuery(nil, updateQuery)
		if err != nil {
			fmt.Println("Error executing update query:", err)
			return nil, err
		}
	}

	if nskey == "lm" && request.Data.Action != nil && *request.Data.Action == "CLOSED" {
		err = s.handleLMClosedAction(ctx, orderInt, *orderInfo.UserID, request.Data.UpdatedBy, *orderInfo.DisplayStatus)
		if err != nil {
			fmt.Printf("Error handling LM closed action for order %d: %v\n", orderInt, err)
		}
	}

	order_status := orderInfo.OrderStatus
	if request.Data.OrderStatus != "" {
		order_status = &request.Data.OrderStatus
	}
	attemptCount := request.Data.AttemptCount
	if attemptCount == nil {
		attemptCountInt64 := int(*orderNDRInfo.NDRAttemptCount)
		attemptCount = &attemptCountInt64
	}
	err = s.AddOrderAction(ctx, &dto.AddOrderActionRequest{
		OrderActionID:    *orderNDRInfo.OrderActionID,
		ActionType:       fmt.Sprintf("update_ndr_%s", nskey),
		OrderID:          orderInt,
		OrderStatus:      *order_status,
		UpdatedAt:        time.Now().UnixMilli(),
		UpdatedBy:        request.Data.UpdatedBy,
		CFAssignedTo:     *orderNDRInfo.CFAssignedTo,
		LMAssignedTo:     *orderNDRInfo.LMAssignedTo,
		CallConnected:    request.Data.CallConnected,
		CFAction:         request.Data.Action,
		LMAction:         request.Data.Action,
		CFNote:           request.Data.Note,
		LMNote:           request.Data.Note,
		OrderClosingNote: request.Data.Note,
		NDRAgentReason:   request.Data.NDRAgentReason,
		NDRUserReason:    request.Data.Reason,
		OrderValue:       orderInfo.OrderValue,
		AttemptCount:     attemptCount,
		Source:           request.Data.Source,
	})
	if err != nil {
		fmt.Println("Error adding order action:", err)
		return nil, err
	}
	properties := map[string]interface{}{
		"distinct_id":  *orderInfo.UserID,
		"order_id":     request.Data.OrderID,
		"seller":       orderInfo.Seller,
		"email":        request.Data.UpdatedBy,
		"order_value":  *orderInfo.OrderValue,
		"courier":      orderInfo.Courier,
		"order_status": order_status,
	}
	addIfNotEmpty := func(key string, value *string) {
		if (value != nil) && (*value != "") {
			properties[key] = value
		}
	}
	if nskey == "cf" {
		nextActionAt := request.Data.NextActionAt
		if nextActionAt != nil {
			dateTime := strings.Split(*nextActionAt, "T")
			if len(dateTime) > 0 {
				nextActionAt = &dateTime[0]
			}
		}
		addIfNotEmpty("call_connected", request.Data.CallConnected)
		addIfNotEmpty("next_action_at", nextActionAt)
		addIfNotEmpty("buyer_reason", request.Data.Reason)
		addIfNotEmpty("notes", request.Data.Note)
		addIfNotEmpty("assigned_to", orderNDRInfo.CFAssignedTo)
		addIfNotEmpty("action", request.Data.Action)
		addIfNotEmpty("3pl_reason", orderNDRInfo.NDRAgentReason)
		if request.Data.Source != nil && *request.Data.Source != "" {
			addIfNotEmpty("source", request.Data.Source)
		}
		if request.Data.CustomerEscalated != nil {
			properties["customer_escalated"] = *request.Data.CustomerEscalated
		}
		s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
			s.Mixpanel.NewEvent("Order NDR - CX Action Taken", *orderInfo.UserID, properties,
				fmt.Sprintf("%s_%s", "Order NDR - CX Action Taken", request.Data.OrderID)),
		})
	} else if nskey == "lm" {
		addIfNotEmpty("3pl_reason", orderNDRInfo.NDRAgentReason)
		addIfNotEmpty("buyer_reason", request.Data.Reason)
		addIfNotEmpty("action", request.Data.Action)
		addIfNotEmpty("assigned_to", orderNDRInfo.LMAssignedTo)
		addIfNotEmpty("notes", request.Data.Note)
		attemptCountStr := fmt.Sprintf(`%d`, *attemptCount)
		addIfNotEmpty("attempt_count", &attemptCountStr)
		s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
			s.Mixpanel.NewEvent("Order NDR - SC Action Taken", *orderInfo.UserID, properties,
				fmt.Sprintf("%s_%s", "Order NDR - SC Action Taken", request.Data.OrderID)),
		})
	}
	return nil, nil
}

// handleLMClosedAction processes refund and ticket resolution when LM action is CLOSED
func (s *Service) handleLMClosedAction(ctx context.Context, orderID int, userID string, updatedBy string, displayStatus string) error {
	orderID64 := int64(orderID)

	if displayStatus == displaystatus.RETURNED {
		// Process refund
		err := s.processRefundForClosedOrder(ctx, orderID64, userID)
		if err != nil {
			fmt.Printf("Error processing refund for order %d: %v\n", orderID, err)
		}

	}

	// Resolve associated ticket
	err := s.resolveTicketForOrder(ctx, orderID, updatedBy)
	if err != nil {
		fmt.Printf("Error resolving ticket for order %d: %v\n", orderID, err)
		return err
	}

	return nil
}

func (s *Service) processRefundForClosedOrder(ctx context.Context, orderID int64, userID string) error {
	// Refund advance payment
	_, err := s.RefundAdvancePayment(ctx, dto.RefundPaymentApiRequest{
		UserID:  userID,
		OrderID: orderID,
		Amount:  -1, // -1 indicates full refund
		Force:   utils.BoolPtr(true),
		Reason:  &shipmentstatus.RTO_DELIVERED, // Assuming this is the correct constant
	})
	if err != nil {
		fmt.Printf("Failed to refund payment for order %d: %v\n", orderID, err)
		return fmt.Errorf("failed to refund payment: %v", err)
	}

	// Revert user cashback
	_, err = s.RevertUserCashback(ctx, dto.RevertUserCashbackRequest{
		UserID: userID,
		Meta:   dto.Meta{}, // You might need to pass appropriate meta if required
		Data: dto.RevertUserUserCashbackData{
			OrderId: orderID,
		},
	})
	if err != nil {
		fmt.Printf("Failed to revert cashback for order %d: %v\n", orderID, err)
		return fmt.Errorf("failed to revert cashback: %v", err)
	}

	fmt.Printf("Successfully processed refund for order %d\n", orderID)
	return nil
}

func (s *Service) resolveTicketForOrder(ctx context.Context, orderID int, updatedBy string) error {
	// Find the ticket associated with this order
	ticketsArray, err := s.GetTicketByOrderID(ctx, fmt.Sprintf(`%d`, orderID))
	if err != nil {
		return fmt.Errorf("error finding ticket for order %d: %v", orderID, err)
	}
	var ticket dao.CSTicket
	if (ticketsArray != nil) && len(*ticketsArray) > 0 {
		ticket = (*ticketsArray)[0]
	}

	if ticket.ID == "" {
		fmt.Printf("No ticket found for order %d, skipping ticket resolution\n", orderID)
		return nil
	}

	// Check if ticket is already resolved or closed
	if ticket.Status != nil && (*ticket.Status == utils.TICKET_STATUS_RESOLVED || *ticket.Status == utils.TICKET_STATUS_CLOSED) {
		fmt.Printf("Ticket %s for order %d is already resolved/closed\n", ticket.ID, orderID)
		return nil
	}

	// Prepare ticket resolution request
	resolveNote := "Order closed by LM - automatically resolved"

	// Resolve the ticket
	_, err = s.PerformTicketAction(ctx, &dto.TicketActionRequest{
		Data: dto.TicketActionRequestData{
			TicketID:  ticket.ID,
			Action:    "change_status",
			ActionBy:  updatedBy,
			NewStatus: utils.TICKET_STATUS_RESOLVED,
			ActionData: dto.ActionData{
				Note: resolveNote,
			}},
	})
	if err != nil {
		return fmt.Errorf("error resolving ticket %s: %v", ticket.ID, err)
	}

	fmt.Printf("Successfully resolved ticket %s for order %d\n", ticket.ID, orderID)
	return nil
}

func (s *Service) EscalateTo3PL(ctx context.Context, request *dto.EscalateTo3PLRequest) (*string, error) {
	orderID, err := strconv.Atoi(request.Data.OrderID)
	if err != nil {
		return nil, fmt.Errorf("error converting order ID to int: %v", err)
	}

	orderNDRInfo, _ := s.GetNDROrderV2(orderID)
	if orderNDRInfo != nil && orderNDRInfo.CustomerEscalated != nil && *orderNDRInfo.CustomerEscalated {
		slack.SendSlackMessage(fmt.Sprintf("order is already escalated to 3PL %s", request.Data.OrderID))
		return nil, fmt.Errorf("order is already escalated to 3PL")
	}
	source := "b2bEscalateTo3PLButton"

	if orderNDRInfo == nil {
		_, err := s.AddNDRV2(ctx, request.Data.OrderID, "", request.Data.UpdatedBy, "", source, true)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error adding ndr for order in EscalateTo3PL %s: %v", request.Data.OrderID, err))
			return nil, fmt.Errorf("error adding NDR: %v", err)
		}
		orderNDRInfo, err = s.GetNDROrderV2(orderID)
		if err != nil {
			return nil, fmt.Errorf("error getting NDR order info: %v", err)
		}
	}

	callConnected := "NA"
	nextActionAt := time.Now().Add(24 * time.Hour).Format("2006-01-02T15:04:05")
	cfNdrUserReason := "Other"
	customerEscalated := true
	_, err = s.UpdateNDROrderV2(ctx, &dto.UpdateNDROrderRequestV2{
		Data: dto.UpdateNDROrderRequestV2Data{
			OrderID:           request.Data.OrderID,
			ActionID:          orderNDRInfo.OrderActionID,
			UpdatedBy:         request.Data.UpdatedBy,
			CallConnected:     &callConnected,
			Action:            &request.Data.Action,
			NextActionAt:      &nextActionAt,
			NDRStage:          "CF",
			Reason:            &cfNdrUserReason,
			Note:              request.Data.Note,
			Source:            &source,
			CustomerEscalated: &customerEscalated,
		},
	})
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("Error updating ndr for order in EscalateTo3PL %s: %v", request.Data.OrderID, err))
		return nil, fmt.Errorf("error updating NDR order: %v", err)
	}
	message := "Order added to 3PL NDR successfully"
	return &message, nil
}
