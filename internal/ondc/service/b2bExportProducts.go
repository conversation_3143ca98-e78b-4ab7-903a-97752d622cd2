package service

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"math"
	"strings"

	"github.com/gin-gonic/gin"
)

// getProductCatalogueData fetches and formats product catalogue data
func (s *Service) getProductCatalogueData(sellers []string) (*[]dto.ProductCatalogueRecord, error) {
	query := `
		SELECT
			kp.id,
			kp.name,
			kp.code,
			kp.seller,
			kp.size_variant_code,
			kp.rank,
			kp.media_urls,
			kp.meta,
			kp.image_urls,
			kp.is_active,
			kp.is_oos,
			kc.category,
			kpi.display_quantity
		FROM
			kiranaclubdb.kiranabazar_products kp
		JOIN
			kiranaclubdb.kiranabazar_categories kc
			ON kp.category_id = kc.id
		LEFT JOIN
			kiranaclubdb.kiranabazar_products_inventory kpi
			ON kpi.id = kp.id
		WHERE
			kc.domain = 'Retail'
	`

	if len(sellers) > 0 && sellers[0] != "all" {
		quotedSellers := make([]string, len(sellers))
		for i, seller := range sellers {
			quotedSellers[i] = fmt.Sprintf("'%s'", seller)
		}
		query += fmt.Sprintf(" AND kp.seller IN (%s)", strings.Join(quotedSellers, ","))
	}

	var rawProducts []dto.KiranaBazarProductCatalogue

	_, err := s.repository.CustomQuery(&rawProducts, query)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch products: %v", err)
	}

	// Create ID to Code mapping for base SKU code lookup
	idCodeMap := make(map[int64]string)
	for _, product := range rawProducts {
		idCodeMap[product.ID] = product.Code
	}

	var catalogueRecords []dto.ProductCatalogueRecord

	for _, rawProduct := range rawProducts {
		// Parse meta JSON
		var meta dto.KiranaBazarProductCatalogueMeta
		if err := json.Unmarshal([]byte(rawProduct.Meta), &meta); err != nil {
			fmt.Printf("Error unmarshaling meta for product %d: %v\n", rawProduct.ID, err)
			continue
		}

		// Skip products without MRP
		if meta.MRPNumber == nil {
			continue
		}

		// Determine wholesale rate (use brand_wholesale_rate if available, otherwise wholesale_rate)
		wholesaleRate := meta.WholesaleRate
		var offerWholesaleRate *float64
		if meta.BrandWholesaleRate != nil {
			wholesaleRate = *meta.BrandWholesaleRate
			offerWholesaleRate = &meta.WholesaleRate
		}

		// Calculate expiry months
		var expiryMonths *int32
		if meta.ExpiresIn != nil {
			months := int32(math.Round(float64(*meta.ExpiresIn) / (30.5 * 24 * 3600 * 1000)))
			expiryMonths = &months
		}

		// Parse image URLs
		var imageURLs []string
		var mediaUrls []dao.KiranaBazarProductMediaUrl
		if err := json.Unmarshal([]byte(rawProduct.MediaUrls), &mediaUrls); err != nil {
			fmt.Printf("Error unmarshaling image URLs for product %d: %v\n", rawProduct.ID, err)
			imageURLs = []string{}
		}
		for _, media := range mediaUrls {
			if media.Url != "" && media.VideoUrl == nil {
				imageURLs = append(imageURLs, media.Url)
			}
		}
		imageURLsString := strings.Join(imageURLs, ", ")

		// Get base SKU code
		baseSKUCode := idCodeMap[rawProduct.SizeVariantCode]

		// Determine unit
		var unit *string
		if rawProduct.DisplayQuantity != nil {
			unitValue := "PIECES"
			unit = &unitValue
		}

		record := dto.ProductCatalogueRecord{
			Seller:                  rawProduct.Seller,
			ProductID:               rawProduct.ID,
			SKUCode:                 rawProduct.Code,
			HSNCode:                 meta.HSNCode,
			SKUName:                 rawProduct.Name,
			HindiName:               meta.HindiName,
			HindiCategory:           rawProduct.Category,
			QuantityGrammage:        meta.Quantity,
			PackSize:                meta.PackSize,
			CaseSize:                meta.CaseSize,
			MRP:                     *meta.MRPNumber,
			WholesaleRate:           wholesaleRate,
			OfferWholesaleRate:      offerWholesaleRate,
			ExpiryMonths:            expiryMonths,
			TaxPercent:              meta.Tax,
			MaxCap:                  meta.MaxCap,
			Tag:                     meta.BadgeText,
			Rank:                    rawProduct.Rank,
			BaseSKUCode:             baseSKUCode,
			InventoryQuantity:       rawProduct.DisplayQuantity,
			Unit:                    unit,
			ImageURLsCommaSeparated: imageURLsString,
			IsActive:                rawProduct.IsActive,
			IsOOS:                   rawProduct.IsOOS,
		}

		catalogueRecords = append(catalogueRecords, record)
	}

	return &catalogueRecords, nil
}

// ExportProductCatalogueCSV exports product catalogue data as CSV
func (s *Service) ExportProductCatalogueCSV(ctx *gin.Context, req *dto.ProductCatalogueExportRequest) error {
	records, err := s.getProductCatalogueData(req.Data.Sellers)
	if err != nil {
		return err
	}

	ctx.Header("Content-Disposition", `attachment; filename="ProductCatalogue.csv"`)
	ctx.Header("Content-Type", "text/csv")

	writer := csv.NewWriter(ctx.Writer)
	header := []string{
		"Seller",
		"Product ID",
		"SKU Code",
		"HSN Code",
		"SKU Name",
		"Hindi Name",
		"Hindi Category",
		"Quantity (Grammage)",
		"Pack Size",
		"Case Size",
		"MRP",
		"Wholesale Rate",
		"Offer Wholesale Rate",
		"Expiry Months",
		"Tax %",
		"Max Cap",
		"Tag",
		"Rank",
		"Base SKU Code",
		"Inventory Quantity",
		"Unit (CASES/PACKS/PIECES)",
		"Image URLs (Comma Seperated)",
		"Is Active",
		"Is OOS",
	}

	err = writer.Write(header)
	if err != nil {
		return fmt.Errorf("error writing CSV header: %v", err)
	}

	if records != nil {
		for _, record := range *records {
			row := []string{
				record.Seller,
				fmt.Sprintf("%d", record.ProductID),
				record.SKUCode,
				stringPtrToString(record.HSNCode),
				record.SKUName,
				record.HindiName,
				record.HindiCategory,
				record.QuantityGrammage,
				fmt.Sprintf("%d", record.PackSize),
				int32PtrToString(record.CaseSize),
				fmt.Sprintf("%.2f", record.MRP),
				fmt.Sprintf("%.2f", record.WholesaleRate),
				float64PtrToString(record.OfferWholesaleRate),
				int32PtrToString(record.ExpiryMonths),
				float64PtrToString(record.TaxPercent),
				int32PtrToString(record.MaxCap),
				stringPtrToString(record.Tag),
				fmt.Sprintf("%d", record.Rank),
				record.BaseSKUCode,
				int32PtrToString(record.InventoryQuantity),
				stringPtrToString(record.Unit),
				record.ImageURLsCommaSeparated,
				fmt.Sprintf("%t", record.IsActive),
				fmt.Sprintf("%t", record.IsOOS),
			}

			if err := writer.Write(row); err != nil {
				fmt.Printf("Error writing record: %v\n", err)
				continue
			}
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return fmt.Errorf("error flushing CSV writer: %v", err)
	}

	return nil
}

// ExportProductCatalogue returns product catalogue data (for API response)
func (s *Service) ExportProductCatalogue(ctx *gin.Context, req *dto.ProductCatalogueExportRequest) (*[]dto.ProductCatalogueRecord, error) {
	return s.getProductCatalogueData(req.Data.Sellers)
}

// Helper functions for pointer to string conversion
func stringPtrToString(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

func int32PtrToString(ptr *int32) string {
	if ptr == nil {
		return ""
	}
	return fmt.Sprintf("%d", *ptr)
}

func float64PtrToString(ptr *float64) string {
	if ptr == nil {
		return ""
	}
	return fmt.Sprintf("%.2f", *ptr)
}
