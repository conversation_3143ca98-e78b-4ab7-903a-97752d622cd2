package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/loyalty"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/coupons"
	userdetails "kc/internal/ondc/service/userDetails"
	"kc/internal/ondc/utils"
	"math"
	"sort"
	"strconv"
	"time"

	"github.com/Masterminds/semver"
)

// Please keep description unique for each coupon and name in english
var ACTIVATION_COUPON_MAP = map[string]dto.Coupon{
	"104": {
		ID:                 104,
		Code:               "2_DARZAN_HALDI",
		Description:        "1 रुपये में 10 पैकेट हल्दी (25 ग्राम वाली)",
		Name:               "1 Rs 10 Packets Haldi (25g)",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"1 रुपये में 10 पैकेट हल्दी (25 ग्राम वाली)"},
		MinimumAmount:      1000,
		IsInternal:         true,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "258",
			Quantity:  1,
			Value:     1,
		},
	},
	"107": {
		ID:                 107,
		Code:               "2_DARZAN_HALDI_SPINNER",
		Description:        "1 रुपये में 2 दर्जन हल्दी (12 ग्राम वाली)|",
		Name:               "1 Rs 2 Dozen Haldi (12g)",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"1 रुपये में 2 दर्जन हल्दी (12 ग्राम वाली)|"},
		MinimumAmount:      1000,
		IsInternal:         true,
		Freebie: dto.Freebie{
			ProductID: "258",
			Quantity:  1,
			Value:     1,
		},
	},
	"108": {
		ID:                 108,
		Code:               "FREE_100_CASHBACK",
		Description:        "100 रुपये कैशबैक ऑफर",
		Name:               "100 Rs Cashback Offer",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"100 रुपये कैशबैक ऑफर"},
		MinimumAmount:      1000,
		IsInternal:         true,
	},
	"110": {
		ID:                 110,
		Code:               "100_DISCOUNT",
		Description:        "Extra 100₹ off",
		Name:               "Extra 100 Rs off",
		Discount:           "100",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"100₹ discount coupon"},
		MinimumAmount:      1000,
		IsInternal:         true,
	},
	"111": {
		ID:                 111,
		Code:               "20_PACKET_HALDI",
		Description:        "1 रुपये में 20 पैकेट हल्दी (25 ग्राम वाली)",
		Name:               "1 Rs 20 Packets Haldi (25g)",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"1 रुपये में 20 पैकेट हल्दी (25 ग्राम वाली)"},
		MinimumAmount:      1000,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "258",
			Quantity:  2,
			Value:     1,
		},
	},
	"112": {
		ID:                 112,
		Code:               "फ़रवरी धमाका स्कीम कूपन",
		Description:        "फ़रवरी धमाका स्कीम कूपन",
		Name:               "FEB SCHEME COUPON",
		Discount:           "500",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"फ़रवरी धमाका स्कीम कूपन"},
		MinimumAmount:      2000,
		IsInternal:         true,
	},
}

var GODESI_COUPONS = []dto.Coupon{}
var PANCHVATI_COUPONS = []dto.Coupon{}
var NUTRAJ_COUPONS = []dto.Coupon{}
var MICHIS_COUPONS = []dto.Coupon{}
var CRAVITOS_COUPONS = []dto.Coupon{}
var SOOTHE_COUPONS = []dto.Coupon{
	{
		ID:                 2055,
		Code:               "SOOTHE_SC_2PERCENT",
		Description:        "2% एक्स्ट्रा मार्जिन",
		Name:               "2% Extra Margin",
		Discount:           "-",
		PercentageDiscount: 2,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"2% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      10000,
	},
}
var MILDEN_COUPONS = []dto.Coupon{}
var CHUK_DE_COUPONS = []dto.Coupon{}

var KIRANA_CLUB_COUPONS = []dto.Coupon{}
var BOLAS_COUPONS = []dto.Coupon{}

var LOTS_COUPONS = []dto.Coupon{}
var HUGS_COUPONS = []dto.Coupon{}
var MANGALAM_COUPONS = []dto.Coupon{}
var APSARA_COUPONS = []dto.Coupon{}
var ZOFF_COUPONS = []dto.Coupon{
	{
		ID:                 8,
		Code:               "ZOFF_SC_2PERCENT",
		Description:        "2% एक्स्ट्रा मार्जिन",
		Name:               "2% Extra Margin",
		Discount:           "-",
		PercentageDiscount: 2,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"2% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      2000,
	},
	{
		ID:                 9,
		Code:               "ZOFF_SC_3PERCENT",
		Description:        "3% एक्स्ट्रा मार्जिन",
		Name:               "3% Extra Margin",
		Discount:           "-",
		PercentageDiscount: 3,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"3% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      3000,
	},
	{
		ID:                 10,
		Code:               "ZOFF_SC_5PERCENT",
		Description:        "5% एक्स्ट्रा मार्जिन",
		Name:               "5% Extra Margin",
		Discount:           "-",
		PercentageDiscount: 5,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"5% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      5000,
	},
	{
		ID:                 11,
		Code:               "ZOFF_50_SPECIAL",
		Description:        "Flat discount Rs. 50",
		Name:               "Flat discount Rs. 50",
		Discount:           "50",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"Flat discount Rs. 50"},
		MinimumAmount:      1500,
	},
	{
		ID:                 12,
		Code:               "ZOFF_SPECIAL_100",
		Description:        "Flat discount Rs. 100",
		Name:               "Flat discount Rs. 100",
		Discount:           "100",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"Flat discount Rs. 100"},
		MinimumAmount:      3000,
	},
	{
		ID:                 13,
		Code:               "ZOFF_NEW_150",
		Description:        "Flat discount Rs. 150",
		Name:               "Flat discount Rs. 150",
		Discount:           "150",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"Flat discount Rs. 150"},
		MinimumAmount:      5000,
	},
	{
		ID:                 2045,
		Code:               "MXZFOODS",
		Description:        "Extra ₹1000 off",
		Name:               "Extra Rs 1000 off",
		Discount:           "1000",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"Flat discount Rs. 1000"},
		MinimumAmount:      10000,
	},
	// {
	// 	ID:                 14,
	// 	Code:               "FREE10",
	// 	Description:        "फ्री 10 पैकेट हल्दी (25g)",
	// 	Name:               "Free 10 Packets Haldi (25g)",
	// 	Discount:           "-",
	// 	PercentageDiscount: 0,
	// 	ValidFrom:          "2024-01-01",
	// 	ValidTill:          "2025-12-31",
	// 	Valid:              true,
	// 	Terms:              []string{"फ्री 10 पैकेट हल्दी (25g)"},
	// 	MinimumAmount:      1500,
	// 	Type:               "FreeBie",
	// 	Freebie: dto.Freebie{
	// 		ProductID: "258",
	// 		Quantity:  1,
	// 		Value:     1,
	// 	},
	// },
	// {
	// 	ID:                 15,
	// 	Code:               "FREE20",
	// 	Description:        "फ्री 20 पैकेट हल्दी (25g)",
	// 	Name:               "Free 20 Packets Haldi (25g)",
	// 	Discount:           "-",
	// 	PercentageDiscount: 0,
	// 	ValidFrom:          "2024-01-01",
	// 	ValidTill:          "2025-12-31",
	// 	Valid:              true,
	// 	Terms:              []string{"फ्री 20 पैकेट हल्दी (25g)"},
	// 	MinimumAmount:      3000,
	// 	Type:               "FreeBie",
	// 	Freebie: dto.Freebie{
	// 		ProductID: "258",
	// 		Quantity:  2,
	// 		Value:     1,
	// 	},
	// },
	// {
	// 	ID:                 16,
	// 	Code:               "FREE30",
	// 	Description:        "फ्री 30 पैकेट हल्दी (25g)",
	// 	Name:               "Free 30 Packets Haldi (25g)",
	// 	Discount:           "-",
	// 	PercentageDiscount: 0,
	// 	ValidFrom:          "2024-01-01",
	// 	ValidTill:          "2025-12-31",
	// 	Valid:              true,
	// 	Terms:              []string{"फ्री 30 पैकेट हल्दी (25g)"},
	// 	MinimumAmount:      4000,
	// 	Type:               "FreeBie",
	// 	Freebie: dto.Freebie{
	// 		ProductID: "258",
	// 		Quantity:  3,
	// 		Value:     1,
	// 	},
	// },
}
var MOTHERS_KITCHEN_COUPONS = []dto.Coupon{}

var RSB_SUPER_STOCKIST_COUPONS = []dto.Coupon{}

var CANDYLAKE_COUPONS = []dto.Coupon{}

var SUGANDH_COUPONS = []dto.Coupon{}

var SOMNATH_COUPONS = []dto.Coupon{}

var ZOFF_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{
	{
		ID:                 108,
		Code:               "MANDATORY",
		Description:        "कैशबैक",
		Name:               "Cashback",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              false,
		Terms:              []string{"कैशबैक"},
		MinimumAmount:      10000000,
		Type:               "Cashback",
	},
	{
		ID:                 107,
		Code:               "MANDATORY",
		Description:        "10% एक्स्ट्रा मार्जिन",
		Name:               "10% Extra Margin",
		Discount:           "-",
		PercentageDiscount: 10,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"10% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      MIN_ORDER_FOR_10_PERCENT,
	},
	{
		ID:                 100,
		Code:               "MANDATORY",
		Description:        "8% एक्स्ट्रा मार्जिन",
		Name:               "8% Extra Margin",
		Discount:           "-",
		PercentageDiscount: 8,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"8% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      MIN_ORDER_FOR_8_PERCENT,
	},
	{
		ID:                 101,
		Code:               "MANDATORY",
		Description:        "7% एक्स्ट्रा मार्जिन",
		Name:               "7% Extra Margin",
		Discount:           "-",
		PercentageDiscount: 7,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"7% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      MIN_ORDER_FOR_7_PERCENT,
	},
	{
		ID:                 2058,
		Code:               "FREE30HALDIBE",
		Description:        "फ्री 30 पैकेट हल्दी",
		Name:               "Free 30 Packet Haldi",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"फ्री 30 पैकेट हल्दी"},
		MinimumAmount:      MIN_ORDER_FOR_30_PACKET_HALDI,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "258",
			Quantity:  3,
			Value:     1,
		},
	},
	{
		ID:                 2057,
		Code:               "FREE20HALDIBE",
		Description:        "फ्री 20 पैकेट हल्दी",
		Name:               "Free 20 Packet Haldi",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"फ्री 20 पैकेट हल्दी"},
		MinimumAmount:      MIN_ORDER_FOR_20_PACKET_HALDI,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "258",
			Quantity:  2,
			Value:     1,
		},
	},
	{
		ID:                 2056,
		Code:               "FREE10HALDIBE",
		Description:        "फ्री 10 पैकेट हल्दी",
		Name:               "Free 10 Packet Haldi",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"फ्री 10 पैकेट हल्दी"},
		MinimumAmount:      MIN_ORDER_FOR_10_PACKET_HALDI,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "258",
			Quantity:  1,
			Value:     1,
		},
	},
	{
		ID:                 102,
		Code:               "MANDATORY",
		Description:        "5% एक्स्ट्रा मार्जिन",
		Name:               "5% Extra Margin",
		Discount:           "-",
		PercentageDiscount: 5,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"5% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      MIN_ORDER_FOR_5_PERCENT,
	},
	{
		ID:                 107,
		Code:               "FREE10BE",
		Description:        "फ्री Zoff बैग",
		Name:               "Free Zoff Bag",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"फ्री Zoff बैग"},
		MinimumAmount:      MIN_ORDER_FOR_FREE_BAG,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "848",
			Quantity:  1,
			Value:     0,
		},
	},
	// {
	// 	ID:                 103,
	// 	Code:               "MANDATORY",
	// 	Description:        "3% एक्स्ट्रा मार्जिन",
	// 	Name:               "3% Extra Margin",
	// 	Discount:           "-",
	// 	PercentageDiscount: 3,
	// 	ValidFrom:          "2024-01-01",
	// 	ValidTill:          "2025-12-31",
	// 	Valid:              true,
	// 	Terms:              []string{"3% एक्स्ट्रा मार्जिन"},
	// 	MinimumAmount:      MIN_ORDER_FOR_3_PERCENT,
	// },
	// {
	// 	ID:                 104,
	// 	Code:               "FREE30BE",
	// 	Description:        "फ्री 30 पैकेट हल्दी (25gm)",
	// 	Name:               "Free 30 Packets Haldi (25gm)",
	// 	Discount:           "-",
	// 	PercentageDiscount: 0,
	// 	ValidFrom:          "2024-01-01",
	// 	ValidTill:          "2025-12-31",
	// 	Valid:              true,
	// 	Terms:              []string{"फ्री 30 पैकेट हल्दी (25gm)"},
	// 	MinimumAmount:      4000,
	// 	Type:               "FreeBie",
	// 	Freebie: dto.Freebie{
	// 		ProductID: "258",
	// 		Quantity:  3,
	// 		Value:     1,
	// 	},
	// },
	// {
	// 	ID:                 105,
	// 	Code:               "FREE20BE",
	// 	Description:        "फ्री 20 पैकेट हल्दी (25gm)",
	// 	Name:               "Free 20 Packets Haldi (25gm)",
	// 	Discount:           "-",
	// 	PercentageDiscount: 0,
	// 	ValidFrom:          "2024-01-01",
	// 	ValidTill:          "2025-12-31",
	// 	Valid:              true,
	// 	Terms:              []string{"फ्री 20 पैकेट हल्दी (25gm)"},
	// 	MinimumAmount:      3000,
	// 	Type:               "FreeBie",
	// 	Freebie: dto.Freebie{
	// 		ProductID: "258",
	// 		Quantity:  2,
	// 		Value:     1,
	// 	},
	// },
	// {
	// 	ID:                 106,
	// 	Code:               "FREE10BE",
	// 	Description:        "फ्री 10 पैकेट हल्दी (25gm)",
	// 	Name:               "Free 10 Packets Haldi (25gm)",
	// 	Discount:           "-",
	// 	PercentageDiscount: 0,
	// 	ValidFrom:          "2024-01-01",
	// 	ValidTill:          "2025-12-31",
	// 	Valid:              true,
	// 	Terms:              []string{"फ्री 10 पैकेट हल्दी (25gm)"},
	// 	MinimumAmount:      1500,
	// 	Type:               "FreeBie",
	// 	Freebie: dto.Freebie{
	// 		ProductID: "258",
	// 		Quantity:  1,
	// 		Value:     1,
	// 	},
	// },
}
var GODESI_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{
	{
		ID:                 204,
		Code:               "MANDATORY",
		Description:        "7% एक्स्ट्रा मार्जिन",
		Name:               "7% Extra margin",
		Discount:           "-",
		PercentageDiscount: 7,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"7% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      MIN_ORDER_FOR_7_PERCENT_GO_DESI,
	},
	{
		ID:                 205,
		Code:               "MANDATORY",
		Description:        "फ्री मोबाइल स्टैंड+5% एक्स्ट्रा मार्जिन",
		Name:               "Mobile Stand worth 50 free + 5% extra margin",
		Discount:           "-",
		PercentageDiscount: 5,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"फ्री मोबाइल स्टैंड+5% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      MIN_ORDER_FOR_5_PERCENT_AND_MOBILE_STAND,
	},
	{
		ID:                 206,
		Code:               "MANDATORY",
		Description:        "3% एक्स्ट्रा छूट",
		Name:               "3% extra margin",
		Discount:           "-",
		PercentageDiscount: 3,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"3% एक्स्ट्रा छूट"},
		MinimumAmount:      MIN_ORDER_FOR_3_PERCENT_GO_DESI,
	},
	{
		ID:                 2054,
		Code:               "60_PACKET_IMLI_POPS",
		Description:        "60 इमली पॉप फ्री",
		Name:               "60 Imli Pops Free",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"60 इमली पॉप फ्री"},
		MinimumAmount:      MIN_ORDER_FOR_60_IMLI_POPS,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "1026",
			Quantity:  6,
			Value:     0,
		},
	},
	{
		ID:                 2053,
		Code:               "30_PACKET_IMLI_POPS",
		Description:        "30 इमली पॉप फ्री",
		Name:               "30 Imli Pops Free",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"30 इमली पॉप फ्री"},
		MinimumAmount:      MIN_ORDER_FOR_30_IMLI_POPS,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "1026",
			Quantity:  3,
			Value:     0,
		},
	},
	{
		ID:                 2052,
		Code:               "10_PACKET_IMLI_POPS",
		Description:        "10 इमली पॉप फ्री",
		Name:               "10 Imli Pops Free",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"10 इमली पॉप फ्री"},
		MinimumAmount:      MIN_ORDER_FOR_10_IMLI_POPS,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "1026",
			Quantity:  1,
			Value:     0,
		},
	},
	// {
	// 	ID:                 201,
	// 	Code:               "MANDATORY",
	// 	Description:        "5% एक्स्ट्रा मार्जिन",
	// 	Name:               "5% Extra Margin",
	// 	Discount:           "-",
	// 	PercentageDiscount: 5,
	// 	ValidFrom:          "2024-01-01",
	// 	ValidTill:          "2025-12-31",
	// 	Valid:              true,
	// 	Terms:              []string{"5% एक्स्ट्रा मार्जिन"},
	// 	MinimumAmount:      15000,
	// },
	// {
	// 	ID:                 202,
	// 	Code:               "MANDATORY",
	// 	Description:        "3% एक्स्ट्रा मार्जिन",
	// 	Name:               "3% Extra Margin",
	// 	Discount:           "-",
	// 	PercentageDiscount: 3,
	// 	ValidFrom:          "2024-01-01",
	// 	ValidTill:          "2025-12-31",
	// 	Valid:              true,
	// 	Terms:              []string{"3% एक्स्ट्रा मार्जिन"},
	// 	MinimumAmount:      10000,
	// },
	// {
	// 	ID:                 203,
	// 	Code:               "MANDATORY",
	// 	Description:        "2% एक्स्ट्रा मार्जिन",
	// 	Name:               "2% Extra Margin",
	// 	Discount:           "-",
	// 	PercentageDiscount: 2,
	// 	ValidFrom:          "2024-01-01",
	// 	ValidTill:          "2025-12-31",
	// 	Valid:              true,
	// 	Terms:              []string{"2% एक्स्ट्रा मार्जिन"},
	// 	MinimumAmount:      5000,
	// },
	// {
	// 	ID:                 204,
	// 	Code:               "MANDATORY",
	// 	Description:        "7% एक्स्ट्रा मार्जिन",
	// 	Name:               "7% Extra Margin",
	// 	Discount:           "-",
	// 	PercentageDiscount: 7,
	// 	ValidFrom:          "2024-01-01",
	// 	ValidTill:          "2025-12-31",
	// 	Valid:              true,
	// 	Terms:              []string{"7% एक्स्ट्रा मार्जिन"},
	// 	MinimumAmount:      2500,
	// },
}
var PANCHVATI_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{}
var HUGS_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{}
var MANGALAM_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{
	{
		ID:                 2048,
		Code:               "MANDATORY",
		Description:        "4% एक्स्ट्रा मार्जिन",
		Name:               "4% Extra margin",
		Discount:           "-",
		PercentageDiscount: 4,
		ValidFrom:          "2024-01-10",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"4% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      MIN_ORDER_FOR_4_PERCENT_MANGALAM,
	},
	{
		ID:                 2047,
		Code:               "MANDATORY",
		Description:        "3% एक्स्ट्रा मार्जिन",
		Name:               "3% Extra margin",
		Discount:           "-",
		PercentageDiscount: 3,
		ValidFrom:          "2024-01-10",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"3% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      MIN_ORDER_FOR_3_PERCENT_MANGALAM,
	},
}
var NUTRAJ_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{}
var LOTS_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{}
var MOTHERS_KITCHEN_MANDATORY_DISCOUNT = []dto.Coupon{}
var MICHIS_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{}
var CRAVITOS_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{}
var SOOTHE_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{
	{
		ID:                 1001,
		Code:               "MANDATORY",
		Description:        "5% एक्स्ट्रा मार्जिन",
		Name:               "5% Extra margin",
		Discount:           "-",
		PercentageDiscount: 5,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"5% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      MIN_ORDER_FOR_5_PERCENT_SOOTHE,
	},
}

var CHUK_DE_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{
	{
		ID:                 2081,
		Code:               "FREE_KNIFESET_CHUKDE",
		Description:        "फ्री नाइफ सेट",
		Name:               "Free Knifeset Chukde",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"फ्री नाइफ सेट ₹2000+ के ऑर्डर पर"},
		MinimumAmount:      MIN_ORDER_FOR_KNIFE_SET,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "2457",
			Quantity:  1,
			Value:     0,
		},
	},
}

// start with higher
var MILDEN_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{
	{
		ID:                 2067,
		Code:               "FREE5ORANGE_MILDEN",
		Description:        "5 पैकेट ऑरेंज कैंडी फ्री",
		Name:               "Free 5 Packets Orange Candy",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"5 पैकेट ऑरेंज कैंडी फ्री"},
		MinimumAmount:      MIN_ORDER_FOR_5_FREE_CANDY_PACKETS_MILDEN,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "1173",
			Quantity:  5,
			Value:     0,
		},
	},
	{
		ID:                 2065,
		Code:               "FREE3ORANGE_MILDEN",
		Description:        "3 पैकेट ऑरेंज कैंडी फ्री",
		Name:               "Free 3 Packets Orange Candy",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"3 पैकेट ऑरेंज कैंडी फ्री"},
		MinimumAmount:      MIN_ORDER_FOR_3_FREE_CANDY_PACKETS_MILDEN,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "1173",
			Quantity:  3,
			Value:     0,
		},
	},
	{
		ID:                 2066,
		Code:               "FREE2ORANGE_MILDEN",
		Description:        "2 पैकेट ऑरेंज कैंडी फ्री",
		Name:               "Free 2 Packets Orange Candy",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"2 पैकेट ऑरेंज कैंडी फ्री"},
		MinimumAmount:      MIN_ORDER_FOR_2_FREE_CANDY_PACKETS_MILDEN,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "1173",
			Quantity:  2,
			Value:     0,
		},
	},
}

var KIRANA_CLUB_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{
	{
		ID:                 2061,
		Code:               "MANDATORY",
		Description:        "किराना क्लब सैंपल बॉक्स ऑफर",
		Name:               "Kirana Club Combo Offer",
		Discount:           "450",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-10",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"किराना क्लब सैंपल बॉक्स ऑफर"},
		MinimumAmount:      1,
	},
}

var BOLAS_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{}

var APSARA_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{
	{
		ID:                 2051,
		Code:               "40_PACKET_CHAI_PATTI",
		Description:        "फ्री 40 पैकेट इलाइची चाय (12 ग्राम वाली)",
		Name:               "40 Packet Elaichi Tea Free",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"फ्री 40 पैकेट इलाइची चाय (12 ग्राम वाली)"},
		MinimumAmount:      MIN_ORDER_FOR_4CHAI_PATTI_APSARA,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "862",
			Quantity:  4,
			Value:     0,
		},
	},
	{
		ID:                 2050,
		Code:               "20_PACKET_CHAI_PATTI",
		Description:        "फ्री 20 पैकेट इलाइची चाय (12 ग्राम वाली)",
		Name:               "20 Packet Elaichi Tea Free",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"फ्री 20 पैकेट इलाइची चाय (12 ग्राम वाली)"},
		MinimumAmount:      MIN_ORDER_FOR_2CHAI_PATTI_APSARA,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "862",
			Quantity:  2,
			Value:     0,
		},
	},
	{
		ID:                 2049,
		Code:               "10_PACKET_CHAI_PATTI",
		Description:        "फ्री 10 पैकेट इलाइची चाय (12 ग्राम वाली)",
		Name:               "10 Packet Elaichi Tea Free",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"फ्री 10 पैकेट इलाइची चाय (12 ग्राम वाली)"},
		MinimumAmount:      MIN_ORDER_FOR_1CHAI_PATTI_APSARA,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "862",
			Quantity:  1,
			Value:     0,
		},
	},
	{
		ID:                 2082,
		Code:               "FREE_200_TEA_APSARA",
		Name:               "Free 200 Tea Apsara",
		Description:        "₹200 की चाय फ्री",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"₹200 की चाय फ्री"},
		MinimumAmount:      MIN_ORDER_FOR_1CHAI_PATTI_282_APSARA,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "2456",
			Quantity:  1,
			Value:     0,
		},
	},
	{
		ID:                 2083,
		Code:               "FREE_600_TEA_APSARA",
		Name:               "Free 600 Tea Apsara",
		Description:        "₹600 की चाय फ्री",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"₹600 की चाय फ्री"},
		MinimumAmount:      MIN_ORDER_FOR_3CHAI_PATTI_282_APSARA,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "2456",
			Quantity:  3,
			Value:     0,
		},
	},
}

var RSB_SUPER_STOCKIST_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{
	{
		ID:                 2073,
		Code:               "RSB_SS_SC_7PER",
		Description:        "7% एक्स्ट्रा मार्जिन|",
		Name:               "7% extra Margin",
		Discount:           "-",
		PercentageDiscount: 7,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"7% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      MIN_ORDER_FOR_7_PERCENT_EXTRA_MARGIN_RSB,
	},
	{
		ID:                 2072,
		Code:               "RSB_SS_SC_5PER",
		Description:        "5% एक्स्ट्रा मार्जिन|",
		Name:               "5% extra Margin",
		Discount:           "-",
		PercentageDiscount: 5,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"5% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      MIN_ORDER_FOR_5_PERCENT_EXTRA_MARGIN_RSB,
	},
	{
		ID:                 2071,
		Code:               "RSB_SS_SC_3PER",
		Description:        "3% एक्स्ट्रा मार्जिन|",
		Name:               "3% extra Margin",
		Discount:           "-",
		PercentageDiscount: 3,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"3% एक्स्ट्रा मार्जिन"},
		MinimumAmount:      MIN_ORDER_FOR_3_PERCENT_EXTRA_MARGIN_RSB,
	},
	{
		ID:                 2080,
		Code:               "FREE_BAG_RSB",
		Description:        "फ्री बैग ऑफर",
		Name:               "Free Bag Offer",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"फ्री बैग ऑफर"},
		MinimumAmount:      MIN_ORDER_FOR_RSB_FREE_BAG_OFFER,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "2271",
			Quantity:  1,
			Value:     0,
		},
	},
	{
		ID:                 2079,
		Code:               "40_PACKET_CHAI_PATTI_RSB",
		Description:        "फ्री 40 पैकेट इलाइची चाय (12 ग्राम वाली)|",
		Name:               "40 Packet Apsara Chai Free",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"फ्री 40 पैकेट इलाइची चाय (12 ग्राम वाली)|"},
		MinimumAmount:      MIN_ORDER_FOR_40_FREE_PACKETS_APSARA_RSB_APSARA,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "2264",
			Quantity:  4,
			Value:     0,
		},
	},
	{
		ID:                 2078,
		Code:               "20_PACKET_CHAI_PATTI_RSB",
		Description:        "फ्री 20 पैकेट इलाइची चाय (12 ग्राम वाली)|",
		Name:               "20 Packet Apsara Chai Free",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"फ्री 20 पैकेट इलाइची चाय (12 ग्राम वाली)|"},
		MinimumAmount:      MIN_ORDER_FOR_20_FREE_PACKETS_APSARA_RSB_APSARA,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "2264",
			Quantity:  2,
			Value:     0,
		},
	},
	{
		ID:                 2077,
		Code:               "10_PACKET_CHAI_PATTI_RSB",
		Description:        "फ्री 10 पैकेट इलाइची चाय (12 ग्राम वाली)|",
		Name:               "10 Packet Apsara Chai Free",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"फ्री 10 पैकेट इलाइची चाय (12 ग्राम वाली)|"},
		MinimumAmount:      MIN_ORDER_FOR_10_FREE_PACKETS_APSARA_RSB_APSARA,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "2264",
			Quantity:  1,
			Value:     0,
		},
	},
	{
		ID:                 2070,
		Code:               "FREE30MEHNDIBE",
		Description:        "30 पैकेट निशा हेयर कलर फ्री",
		Name:               "30 Packet Hair Color Free",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"30 पैकेट निशा हेयर कलर फ्री"},
		MinimumAmount:      MIN_ORDER_FOR_30_FREE_HAIR_COLOR_RSB,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "2202",
			Quantity:  6,
			Value:     0,
		},
	},
	{
		ID:                 2068,
		Code:               "FREE10MEHNDIBE",
		Description:        "10 पैकेट निशा हेयर कलर फ्री",
		Name:               "10 Packet Hair Color Free",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"10 पैकेट निशा हेयर कलर फ्री"},
		MinimumAmount:      MIN_ORDER_FOR_10_FREE_HAIR_COLOR_RSB,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "2202",
			Quantity:  2,
			Value:     0,
		},
	},
	{
		ID:                 2069,
		Code:               "FREE5MEHNDIBE",
		Description:        "5 पैकेट निशा हेयर कलर फ्री",
		Name:               "5 Packet Hair Color Free",
		Discount:           "-",
		PercentageDiscount: 0,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"5 पैकेट निशा हेयर कलर फ्री"},
		MinimumAmount:      MIN_ORDER_FOR_5_FREE_HAIR_COLOUR_RSB,
		Type:               "FreeBie",
		Freebie: dto.Freebie{
			ProductID: "2202",
			Quantity:  1,
			Value:     0,
		},
	},
}

var KIRANA_CLUB_LOYALTY_REWARDS_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{
	{
		ID:                 2074,
		Code:               "KC_LR_100PER",
		Description:        "किराना क्लब लॉयल्टी आर्डर",
		Name:               "Kirana Club Loyalty Order",
		Discount:           "-",
		PercentageDiscount: 100,
		ValidFrom:          "2024-01-01",
		ValidTill:          "2025-12-31",
		Valid:              true,
		Terms:              []string{"किराना क्लब लॉयल्टी आर्डर"},
		MinimumAmount:      MIN_ORDER_FOR_100_PERCENT_EXTRA_MARGIN_KC_LR,
	},
}

var CANDYLAKE_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{}
var SUGANDH_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{}
var SOMNATH_BACKEND_MANDATORY_DISCOUNT = []dto.Coupon{}

var ALL_COUPONS = []dto.Coupon{}
var ALL_COUPONS_DES_MAP = map[string]dto.Coupon{}

func getAllCoupons() []dto.Coupon {
	if len(ALL_COUPONS) > 0 {
		return ALL_COUPONS
	}
	ALL_COUPONS = append(ALL_COUPONS, GODESI_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, PANCHVATI_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, HUGS_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, ZOFF_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, MOTHERS_KITCHEN_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, APSARA_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, MANGALAM_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, NUTRAJ_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, LOTS_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, MILDEN_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, MICHIS_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, CRAVITOS_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, SOOTHE_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, RSB_SUPER_STOCKIST_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, BOLAS_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, CHUK_DE_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, CANDYLAKE_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, SUGANDH_COUPONS...)
	ALL_COUPONS = append(ALL_COUPONS, SOMNATH_COUPONS...)

	ALL_COUPONS = append(ALL_COUPONS, ZOFF_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, GODESI_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, PANCHVATI_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, HUGS_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, MOTHERS_KITCHEN_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, APSARA_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, MANGALAM_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, NUTRAJ_BACKEND_MANDATORY_DISCOUNT...)
	// ALL_COUPONS = append(ALL_COUPONS, LOTS_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, MICHIS_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, CRAVITOS_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, SOOTHE_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, MILDEN_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, RSB_SUPER_STOCKIST_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, KIRANA_CLUB_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, BOLAS_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, CHUK_DE_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, KIRANA_CLUB_LOYALTY_REWARDS_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, CANDYLAKE_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, SUGANDH_BACKEND_MANDATORY_DISCOUNT...)
	ALL_COUPONS = append(ALL_COUPONS, SOMNATH_BACKEND_MANDATORY_DISCOUNT...)

	for _, coupon := range ACTIVATION_COUPON_MAP {
		ALL_COUPONS = append(ALL_COUPONS, coupon)
	}

	return ALL_COUPONS
}

func getAllCouponsDesMap() map[string]dto.Coupon {
	if len(ALL_COUPONS_DES_MAP) > 0 {
		return ALL_COUPONS_DES_MAP
	}
	ALL_COUPONS_DES_MAP = make(map[string]dto.Coupon)
	allCoupons := ALL_COUPONS
	if len(allCoupons) == 0 {
		allCoupons = getAllCoupons()
	}
	for _, j := range allCoupons {
		ALL_COUPONS_DES_MAP[j.Description] = j
	}
	return ALL_COUPONS_DES_MAP
}

func (s *Service) GetAllAvailableCoupons(ctx context.Context) (*dto.GelAllCouponsResponse, error) {
	allCoupons := make([]*coupons.Coupon, 0)
	for _, sd := range brands.GetBrandsMeta() {
		seller := sd.Seller
		coupons, err := s.Coupons.GetCouponsByBrand(ctx, seller, false)
		if err != nil {
			logger.Error(ctx, "Error fetching coupons for seller %s: %v", seller, err)
			continue
		}
		if len(coupons) == 0 {
			logger.Info(ctx, "No coupons found for seller %s", seller)
			continue
		}
		allCoupons = append(allCoupons, coupons...)
	}

	allCouponsMap := make(map[string]*coupons.Coupon)
	for _, coupon := range allCoupons {
		if coupon == nil {
			continue
		}

		for _, b := range coupon.Brands {
			allCouponsMap[fmt.Sprintf("%s:%s", coupon.Name, b)] = coupon
		}

		if len(coupon.Brands) == 0 {
			allCouponsMap[coupon.Name] = coupon
		}
	}

	return &dto.GelAllCouponsResponse{
		Data: dto.GetAllCouponsResponseData{
			Coupons:       allCoupons,
			AllCouponsMap: allCouponsMap,
		},
	}, nil

}

func (s *Service) CheckCoupon(ctx context.Context, request dto.CheckCouponRequest) (response dto.CheckCouponResponse, err error) {
	resp, err := s.CheckCouponV2(ctx, dto.GetApplicableCouponsRequest{
		UserID: request.UserID,
		Meta:   request.Meta,
		Data: dto.GetBillDetailsData{
			CouponID: request.Data.CouponCode,
			Seller:   request.Data.Seller,
		},
	})
	if err != nil || len(resp.Data.Coupons) == 0 {
		logger.Error(ctx, "Error checking coupon: %v", err)
		return dto.CheckCouponResponse{
			Meta: request.Meta,
			Data: dto.GetCouponsData{
				Coupons:   []dto.Coupon{},
				Count:     0,
				ErrorText: "कूपन लागू नहीं हो सका",
			},
		}, nil
	}

	return dto.CheckCouponResponse{
		Meta: request.Meta,
		Data: dto.GetCouponsData{
			Coupons: []dto.Coupon{
				{
					Code:        resp.Data.Coupons[0].Code,
					Description: resp.Data.Coupons[0].Description,
					Name:        resp.Data.Coupons[0].Name,
					Discount:    fmt.Sprintf("%f", resp.Data.Coupons[0].Discount),
				},
			},
			Count: resp.Data.Count,
		},
	}, nil
}

func (s *Service) CheckCouponV2(ctx context.Context, request dto.GetApplicableCouponsRequest) (response dto.CheckCouponResponseV2, err error) {
	userDetailsChannel := userdetails.AsyncFetchUserDetails(request.UserID, []string{userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS}, 1*time.Second)
	userDetails := <-userDetailsChannel
	resp, err := s.Coupons.ValidateCoupon(ctx, request.Data.CouponID, request.Data.Seller, request.UserID, &userDetails, true)
	if err != nil || resp.IsValid == false {
		return dto.CheckCouponResponseV2{
			Meta: request.Meta,
			Data: dto.GetCouponsDataV2{
				Coupons:   []dto.ValidCoupon{},
				Count:     0,
				ErrorText: "कूपन लागू नहीं हो सका",
			},
		}, nil
	}

	style := map[string]interface{}{
		"color":      "#000000",
		"fontWeight": "bold",
	}
	response = dto.CheckCouponResponseV2{
		Meta: request.Meta,
		Data: dto.GetCouponsDataV2{
			Coupons: []dto.ValidCoupon{
				{
					Code:        resp.Coupon.Code,
					Description: fmt.Sprintf("कूपन से ₹%s की बचत", fmt.Sprintf("%.1f", math.Round(resp.DiscountAmount))),
					Discount:    resp.Coupon.DiscountValue,
					HighlightedDescription: []dto.HighlightedTextData{
						{
							Text:           "से",
							HighlightStyle: &style,
						},
					},
				},
			},
			Count: 1,
		},
	}
	return response, nil
}

func getOffersBottomSheetTitle(seller string) string {
	val, ok := brands.GetBrandMetaBySeller(seller)
	if ok {
		return fmt.Sprintf("%s पर केवल आपके लिए ऑफर्स", val.Name)
	}
	return "आपके लिए आज के ऑफर्स"
}

func (s *Service) GetCoupons(ctx context.Context, request dto.GetCouponsRequest) (response dto.GetCouponsResponse, err error) {
	coupons := []dto.Coupon{}
	if request.Data.Seller == "zoff_foods" || request.Data.Seller == "" {
		coupons = ZOFF_COUPONS
	} else if request.Data.Seller == "go_desi" {
		coupons = GODESI_COUPONS
	} else if request.Data.Seller == "panchvati" {
		coupons = PANCHVATI_COUPONS
	} else if request.Data.Seller == utils.HUGS {
		coupons = HUGS_COUPONS
	} else if request.Data.Seller == utils.MOTHERS_KITCHEN {
		coupons = MOTHERS_KITCHEN_COUPONS
	} else if request.Data.Seller == utils.APSARA_TEA {
		coupons = APSARA_COUPONS
	} else if request.Data.Seller == utils.MANGALAM {
		coupons = MANGALAM_COUPONS
	} else if request.Data.Seller == utils.NUTRAJ {
		coupons = NUTRAJ_COUPONS
	} else if request.Data.Seller == utils.LOTS {
		coupons = LOTS_COUPONS
		return
	} else if request.Data.Seller == utils.MICHIS {
		coupons = MICHIS_COUPONS
	} else if request.Data.Seller == utils.CRAVITOS {
		coupons = CRAVITOS_COUPONS
	} else if request.Data.Seller == utils.SOOTHE {
		coupons = SOOTHE_COUPONS
	} else if request.Data.Seller == utils.KIRANA_CLUB {
		coupons = KIRANA_CLUB_COUPONS
	} else if request.Data.Seller == utils.CANDYLAKE {
		coupons = CANDYLAKE_COUPONS
	} else if request.Data.Seller == utils.SUGANDH {
		coupons = SUGANDH_COUPONS
	} else if request.Data.Seller == utils.SOMNATH {
		coupons = SOMNATH_COUPONS
	}

	if request.Data.CouponID != 0 {
		coupon := dto.Coupon{}
		for _, c := range coupons {
			if c.ID == request.Data.CouponID {
				coupon = c
			}
		}
		response.Data.Coupons = []dto.Coupon{coupon}
		return
	}
	response.Data.Coupons = coupons

	return
}

func (s *Service) GetApplicableCoupons(ctx context.Context, request dto.GetApplicableCouponsRequest) (response coupons.GetApplicableCouponsResponse, err error) {
	userDetails := <-userdetails.AsyncFetchUserDetails(request.UserID, []string{userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS}, 1*time.Second)
	couponsRawData, err := s.Coupons.GetCouponsByBrandCartAndUser(ctx, request.Data.Seller, request.UserID, &userDetails)
	if err != nil {
		fmt.Println("error in getting coupons from db", err)
	}

	var couponsData = make([]dto.Coupon, 0)
	for _, coupon := range couponsRawData {
		if coupon.IsActive == false || coupon.CouponType != coupons.CouponTypeApp || coupon.Hidden {
			continue
		}

		resp := dto.Coupon{
			ID:          int(coupon.ID),
			Code:        coupon.Code,
			IsActive:    coupon.IsApplicable,
			Title:       coupon.Name,
			Description: coupon.Description.String,
			Color:       "#6A21D0",
			CouponURL:   "https://d2rstorage2.blob.core.windows.net/widget/July/15/c34079f1-e71f-4690-85db-0a7450ea90cd/1752578379811.png",
			Info:        coupon.Description.String,
			SideStrip: &dto.CouponSideStrip{
				Label: coupon.Name,
			},
		}

		var terms []string
		if err := json.Unmarshal(coupon.Terms, &terms); err != nil {
			fmt.Println("Error parsing terms:", err)
		} else {
			resp.BulletPoints = terms
		}
		couponsData = append(couponsData, resp)
	}

	sort.Slice(couponsData, func(i, j int) bool {
		return couponsData[i].IsActive && !couponsData[j].IsActive
	})

	response = coupons.GetApplicableCouponsResponse{
		Meta: request.Meta,
		Data: dto.GetCouponsData{
			Coupons: couponsData,
			Count:   len(couponsData),
		},
	}
	return response, nil
}

func (s *Service) GetCouponsBottomSheet(ctx context.Context, request dto.GetApplicableCouponsRequest) (response coupons.GetCouponsBottomSheetResponse, err error) {
	userDetails := <-userdetails.AsyncFetchUserDetails(request.UserID, []string{userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS}, 1*time.Second)
	couponsRawData, err := s.Coupons.GetCouponsByBrandCartAndUser(ctx, request.Data.Seller, request.UserID, &userDetails)
	if err != nil {
		fmt.Println("error in getting coupons from db", err)
	}

	sort.Slice(couponsRawData, func(i, j int) bool {
		// First, compare by priority (descending)
		if couponsRawData[i].Priority != couponsRawData[j].Priority {
			return couponsRawData[i].Priority > couponsRawData[j].Priority
		}

		// If priorities are equal, compare by MinOrderAmount (ascending)
		// Handle sql.NullFloat64 values
		minOrderI := float64(0)
		minOrderJ := float64(0)

		if couponsRawData[i].MinOrderAmount.Valid {
			minOrderI = couponsRawData[i].MinOrderAmount.Float64
		}

		if couponsRawData[j].MinOrderAmount.Valid {
			minOrderJ = couponsRawData[j].MinOrderAmount.Float64
		}

		return minOrderI < minOrderJ
	})

	var couponsData = make([]dto.Coupon, 0)
	var couponsName = make([]string, 0)
	if request.Data.Seller != "" && request.Data.Seller != utils.MANGALAM && request.Data.Seller != utils.GO_DESI && request.Data.Seller != utils.KIRANA_CLUB {
		for _, coupon := range couponsRawData {
			if !coupon.IsActive || coupon.CouponType != coupons.CouponTypeBackend || coupon.Hidden {
				continue
			}

			if !s.Coupons.IsUserEligible(ctx, coupon, request.UserID, &userDetails, request.Data.Seller) {
				continue
			}

			if !s.Coupons.EvaluateCouponRulesObject(ctx, coupon, request.UserID, request.Data.Seller, false) {
				continue
			}

			// check here if coupon is for certain cohort and order number conditions in rule
			resp := dto.Coupon{
				ID:            int(coupon.ID),
				Code:          coupon.Code,
				IsActive:      coupon.IsApplicable,
				Title:         coupon.Name,
				Description:   coupon.Description.String,
				Color:         "#6A21D0",
				CouponURL:     "https://d2rstorage2.blob.core.windows.net/widget/March/27/5f6a1eca-5f31-4448-9111-b2676a6c1a31/1743053208563.webp",
				OfferImageURL: "https://d2rstorage2.blob.core.windows.net/widget/March/27/5f6a1eca-5f31-4448-9111-b2676a6c1a31/1743053208563.webp",
				Info:          coupon.Description.String,
			}

			var terms []string
			if err := json.Unmarshal(coupon.Terms, &terms); err != nil {
				fmt.Println("Error parsing terms:", err)
			} else {
				resp.BulletPoints = terms
			}
			couponsData = append(couponsData, resp)
			couponsName = append(couponsName, coupon.Name)
		}

		// if request.Data.Seller == utils.ZOFF_FOODS {
		// 	couponsData = append([]dto.Coupon{
		// 		{
		// 			ID:            10,
		// 			Code:          "ZOFF_FOODS",
		// 			IsActive:      true,
		// 			Title:         "मसालों पर एक्स्ट्रा 20% तक मार्जिन",
		// 			Description:   "10-20% तक एक्स्ट्रा मार्जिन",
		// 			Color:         "#6A21D0",
		// 			CouponURL:     "https://d2rstorage2.blob.core.windows.net/widget/March/27/5f6a1eca-5f31-4448-9111-b2676a6c1a31/1743053208563.webp",
		// 			OfferImageURL: "https://d2rstorage2.blob.core.windows.net/widget/March/27/5f6a1eca-5f31-4448-9111-b2676a6c1a31/1743053208563.webp",
		// 			Info:          "यह ऑफर सिर्फ Zoff कंपनी के ऑर्डर पर वैद्य है।",
		// 			BulletPoints: []string{
		// 				"जीरा, हल्दी, मिर्च, धनिया, सोया बड़ी, अदरक लहसुन पेस्ट, आदि पर 10-20% तक एक्स्ट्रा मार्जिन",
		// 				"यह ऑफर सिर्फ Zoff कंपनी के ऑर्डर पर वैद्य है।",
		// 			},
		// 		},
		// 	}, couponsData...)
		// 	couponsName = append([]string{"मसालों पर एक्स्ट्रा 20% तक मार्जिन"}, couponsName...)
		// }
	}

	// utils.Reverse(couponsData)
	// utils.Reverse(couponsName)
	var orderLoyaltyRewardResponse *loyalty.LoyaltyRewardsResponse
	if request.Data.Seller != utils.ZOFF_FOODS {
		orderLoyaltyRewardResponse, err = s.calculateUserLoyaltyRewards(request.UserID, request.Meta.AppVersion, 0, request.Data.Seller, true)
		if err != nil || orderLoyaltyRewardResponse == nil {
			// slack.SendSlackMessage(fmt.Sprintf("Error in calculating user loyalty rewards %s, cashback %f, total value %f: %v", request.UserID, request.Data.RequestedCashback, totalProductPricing, err))
		}
	}

	orderLoyaltyReward := []dto.LoyaltyRewardsComponent{}

	if orderLoyaltyRewardResponse != nil && orderLoyaltyRewardResponse.LoyaltyUser {
		loyaltyItems := []dto.LoyaltyRewardsBenefitsItem{
			{
				Icon: &dto.LoyaltyRewardIcon{
					Url: utils.StrPtr(orderLoyaltyRewardResponse.CoinImageURL),
				},
				Content: dto.LoyaltyRewardsText{
					Text:  fmt.Sprintf("%d पॉइंट हर ₹100 की ख़रीद पर", int(orderLoyaltyRewardResponse.CoinsFactor)),
					Value: orderLoyaltyRewardResponse.CoinsFactor,
					Styles: &map[string]interface{}{
						"color": "#000000",
					},
				},
			},
			{
				Icon: &dto.LoyaltyRewardIcon{
					Url: utils.StrPtr(orderLoyaltyRewardResponse.CashbackImageURL),
				},
				Content: dto.LoyaltyRewardsText{
					Text:  fmt.Sprintf("%d%s कैशबैक", int(orderLoyaltyRewardResponse.CashBackFactor), "%"),
					Value: orderLoyaltyRewardResponse.CashBackFactor,
					Styles: &map[string]interface{}{
						"color": "#000000",
					},
				},
			},
		}

		orderLoyaltyReward = []dto.LoyaltyRewardsComponent{
			{
				CurrentTier: orderLoyaltyRewardResponse.CurrentTier,
				Header: &dto.ComponentFooter{
					Text: fmt.Sprintf("%s टियर एक्सक्लूसिव ऑफर 🔥", orderLoyaltyRewardResponse.TierName),
				},
				Body: loyaltyItems,
				Config: map[string]interface{}{
					"gradient_colors": []string{orderLoyaltyRewardResponse.Colors.Gradient.Start, orderLoyaltyRewardResponse.Colors.Gradient.End},
					"color":           orderLoyaltyRewardResponse.Colors.Primary,
				},
			},
		}
	}

	var durationValue int64 = 3000

	if request.UserID == "7iKxueICXmUVXAOoZaIH0Y6AANI3" {
		durationValue = 500
	}

	headerText := "🔥 बड़े ऑर्डर पर बड़ा मुनाफा!"
	// if request.Data.Seller == utils.ZOFF_FOODS {
	// 	headerText = "🔥 मसालों पर एक्स्ट्रा 20% तक मार्जिन"
	// }
	response = coupons.GetCouponsBottomSheetResponse{
		Meta: request.Meta,
		Data: coupons.CouponBottomSheetData{
			Title: getOffersBottomSheetTitle(request.Data.Seller),
			CouponsData: coupons.CouponData{
				Coupons: couponsData,
				Header: &dto.ComponentFooter{
					Text: headerText,
				},
			},
			CouponsName: couponsName,
			Benefits:    orderLoyaltyReward,
			Count:       len(couponsData),
			Duration:    &durationValue,
		},
	}
	return response, nil
}

func (s *Service) GetBackendDiscount(totalPricingWithoutDiscount, totalProductPricing float64, seller string, userId string, activationCohortUserData *dto.ActivationCohortUserData) ([]shared.TotalPricing, float64, error) {
	tpm := make([]shared.TotalPricing, 0)
	dics := 0.0
	layout := "2006-01-02"
	backendCoupons := []dto.Coupon{}
	if seller == "zoff_foods" || seller == "" {
		backendCoupons = ZOFF_BACKEND_MANDATORY_DISCOUNT
		if activationCohortUserData != nil && activationCohortUserData.Eligible && activationCohortUserData.CouponId != "110" && activationCohortUserData.CouponId != "112" {
			value, ok := ACTIVATION_COUPON_MAP[activationCohortUserData.CouponId]
			if ok {
				backendCoupons = append([]dto.Coupon{value}, backendCoupons...)
			}
		}
	} else if seller == "go_desi" {
		backendCoupons = GODESI_BACKEND_MANDATORY_DISCOUNT
	} else if seller == "panchvati" {
		backendCoupons = PANCHVATI_BACKEND_MANDATORY_DISCOUNT
	} else if seller == utils.HUGS {
		backendCoupons = HUGS_BACKEND_MANDATORY_DISCOUNT
	} else if seller == utils.MOTHERS_KITCHEN {
		backendCoupons = MOTHERS_KITCHEN_MANDATORY_DISCOUNT
	} else if seller == utils.APSARA_TEA {
		backendCoupons = APSARA_BACKEND_MANDATORY_DISCOUNT
	} else if seller == utils.MANGALAM {
		backendCoupons = MANGALAM_BACKEND_MANDATORY_DISCOUNT
	} else if seller == utils.NUTRAJ {
		backendCoupons = NUTRAJ_BACKEND_MANDATORY_DISCOUNT
	} else if seller == utils.LOTS {
		backendCoupons = LOTS_BACKEND_MANDATORY_DISCOUNT
		return tpm, dics, nil
	} else if seller == utils.MICHIS {
		backendCoupons = MICHIS_BACKEND_MANDATORY_DISCOUNT
	} else if seller == utils.CRAVITOS {
		backendCoupons = CRAVITOS_BACKEND_MANDATORY_DISCOUNT
	} else if seller == utils.SOOTHE {
		backendCoupons = SOOTHE_BACKEND_MANDATORY_DISCOUNT
	} else if seller == utils.MILDEN {
		backendCoupons = MILDEN_BACKEND_MANDATORY_DISCOUNT
	} else if seller == utils.RSB_SUPER_STOCKIST {
		backendCoupons = RSB_SUPER_STOCKIST_BACKEND_MANDATORY_DISCOUNT
	} else if seller == utils.KIRANA_CLUB {
		backendCoupons = KIRANA_CLUB_BACKEND_MANDATORY_DISCOUNT
	} else if seller == utils.BOLAS {
		backendCoupons = BOLAS_BACKEND_MANDATORY_DISCOUNT
	} else if seller == utils.CHUK_DE {
		backendCoupons = CHUK_DE_BACKEND_MANDATORY_DISCOUNT
	} else if seller == utils.KIRANACLUB_LOYALTY_REWARDS {
		backendCoupons = KIRANA_CLUB_LOYALTY_REWARDS_BACKEND_MANDATORY_DISCOUNT
	} else if seller == utils.CANDYLAKE {
		backendCoupons = CANDYLAKE_BACKEND_MANDATORY_DISCOUNT
	}

	// adding 100 rs coupon across sellers
	if activationCohortUserData != nil && activationCohortUserData.Eligible && (activationCohortUserData.CouponId == "110" || activationCohortUserData.CouponId == "112") {
		value, ok := ACTIVATION_COUPON_MAP[activationCohortUserData.CouponId]
		if ok {
			backendCoupons = append([]dto.Coupon{value}, backendCoupons...)
		}
	}

	for _, coupon := range backendCoupons {
		validFromDate, err := time.Parse(layout, coupon.ValidFrom)
		today := time.Now().Truncate(24 * time.Hour)
		if err != nil {
			return tpm, dics, err
		}

		validTillDate, err := time.Parse(layout, coupon.ValidTill)
		if err != nil {
			return tpm, dics, err
		}

		if coupon.Valid && totalPricingWithoutDiscount >= coupon.MinimumAmount && (today.After(validFromDate) && today.Before(validTillDate) || today.Equal(validFromDate) || today.Equal(validTillDate)) {
			discount := totalProductPricing * coupon.PercentageDiscount * 0.01
			if coupon.PercentageDiscount == 0 {
				discountAmount, err := strconv.Atoi(coupon.Discount)
				if err == nil {
					discount = float64(discountAmount)
				}
			}
			value := fmt.Sprintf("-₹%.2f", discount)
			style := map[string]interface{}{
				"color":      "#009E7F",
				"fontWeight": 700,
			}

			tpm = append(tpm, shared.TotalPricing{
				Key:        coupon.Description,
				Value:      value,
				TotalValue: -1 * discount,
				Styles:     &style,
				IsInternal: coupon.IsInternal,
			})
			dics += discount
			if coupon.Code != ACTIVATION_COUPON_MAP["104"].Code && coupon.ID != 112 {
				break
			}
		}
	}
	return tpm, dics, nil
}

func (s *Service) GetNextOfferCouponData(
	ctx context.Context,
	seller, userID string,
	sellerMov float64,
	userDetails userdetails.AsyncResult,
	appVersion string,
) (*dto.OfferMessage, error) {

	nextCoupon, err := s.Coupons.GetNextApplicableCouponByBrandCartAndUser(ctx, seller, userID, &userDetails)
	if err != nil {
		return nil, err
	}

	return buildOfferMessage(nextCoupon, sellerMov, appVersion, seller), nil
}

func buildOfferMessage(nextCoupon *coupons.NextApplicableCoupon, sellerMov float64, appVersion, seller string) *dto.OfferMessage {
	cartValue := nextCoupon.CartValue
	minOrderValue := int(sellerMov)

	// Case: cart value below seller minimum
	if cartValue < sellerMov {
		text := fmt.Sprintf("ऑर्डर के लिए ₹%.2f और जोड़ें", sellerMov-cartValue)
		highlight := dto.HighlightedTextData{
			Text:           fmt.Sprintf("₹%.2f", sellerMov-cartValue),
			HighlightStyle: &map[string]interface{}{"color": "#ffffff", "fontWeight": 700},
		}
		styles := map[string]interface{}{
			"color":           "#ffffff",
			"backgroundColor": "#CD5C5C",
			"gradientColors": []string{
				"#09141d",
				"#09141d",
			},
		}

		userAppVersion, _ := semver.NewVersion(appVersion)
		if userAppVersion != nil {
			styleChangeAppVersion, _ := semver.NewVersion("6.5.5")
			if userAppVersion.GreaterThan(styleChangeAppVersion) {
				styles["backgroundColor"] = nil
			}
		}

		progressValue := 0.0
		if cartValue > 0 {
			progressValue = cartValue / float64(minOrderValue)
		}

		progressBar := dto.OfferProgressBar{
			Value: &progressValue,
			Styles: map[string]interface{}{
				"completed_background": []string{
					"#ffffff",
					"#ffffff",
				},
				"incomplete_background": "#43586a",
			},
		}

		leftIconUrl := "https://d2rstorage2.blob.core.windows.net/widget/June/17/228790ca-43a3-4468-86a7-13a660a5a477/1750145400381.webp"
		rightIconUrl := "https://d2rstorage2.blob.core.windows.net/widget/June/13/10083558-cfa9-49a3-9bd5-e98535b89476/1749812306605.webp"

		return &dto.OfferMessage{
			MinOrderValue:   &minOrderValue,
			Text:            &text,
			Styles:          &styles,
			IconType:        utils.StrPtr("warning"),
			HighlightedText: []dto.HighlightedTextData{highlight},
			ProgressBar:     &progressBar,
			LeftIconUrl:     &leftIconUrl,
			RightIconUrl:    &rightIconUrl,
			Seller:          &seller,
		}
	}

	if nextCoupon == nil || nextCoupon.Coupon == nil {
		return nil
	}
	coupon := nextCoupon.Coupon

	// Fetch coupon metadata
	meta, err := coupon.GetCouponMeta()
	if err != nil || meta == nil || meta.OfferMessageData == nil {
		return nil
	}

	messageData := meta.OfferMessageData

	// Fallback to default styles if missing
	styles := messageData.Styles
	if styles == nil {
		styles = map[string]interface{}{"color": "#4E0EAF", "backgroundColor": "#E8D9FF"}
	}

	highlightStyle := messageData.HighlightStyle
	if highlightStyle == nil {
		highlightStyle = map[string]interface{}{"color": "#4E0EAF", "fontWeight": 700}
	}

	// progressBarStyles := make(map[string]interface{})
	// if messageData.ProgressBar != nil {
	// 	progressBarStyles = messageData.ProgressBar.Styles
	// }

	diffAmount := nextCoupon.AmountNeeded
	offerMessageData := dto.NewMessageBuilder().
		WithFormatValues(map[string]interface{}{"amount": diffAmount}).
		WithText(messageData.TextTemplate).
		WithHighlightedTexts(messageData.HighlightedText).
		WithHighlightStyle(highlightStyle).
		WithStyles(styles, appVersion).
		WithIconType(messageData.IconType).
		WithDiscountAmount(int(coupon.MinOrderAmount.Float64)).
		WithMinOrderValue(minOrderValue).
		WithDiscount(coupon.DiscountValue).
		// WithProgressBarStyles(progressBarStyles).
		// WithProgressBarValue(cartValue, coupon.MinOrderAmount.Float64).
		WithRightIconUrl(messageData.RightIconUrl).
		WithLeftIconUrl(messageData.LeftIconUrl).
		WithSeller(seller).
		Build()
	return offerMessageData
}
