// Not in use as handler are not used, directly service is used
// handlers/financialHandlers.go
package handlers

import (
	"kc/internal/ondc/service/kcFinances/dao"
	"kc/internal/ondc/service/kcFinances/finance"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// FinancialHandler handles HTTP requests for financial operations
type FinancialHandler struct {
	service *finance.Service
}

// NewFinancialHandler creates a new financial handler
func NewFinancialHandler(service *finance.Service) *FinancialHandler {
	return &FinancialHandler{service: service}
}

// RegisterRoutes registers all financial routes
func (h *FinancialHandler) RegisterRoutes(r *gin.Engine) {

	{
		api := r.Group("/api/financials")
		// Order financials routes
		api.POST("/orders", h.CreateOrderFinancials)
		api.GET("/orders/:id", h.GetOrderFinancials)

		// Credit note routes
		api.POST("/credit-notes", h.CreateCreditNote)
		api.GET("/credit-notes/:id", h.GetCreditNote)
		api.POST("/returns/:id/credit-note", h.CreateCreditNoteForReturn)

		// Debit note routes
		api.POST("/debit-notes", h.CreateDebitNote)
		api.GET("/debit-notes/:id", h.GetDebitNote)

		// Settlement routes
		api.POST("/settlements", h.GenerateSettlement)
		api.GET("/settlements/:id", h.GetSettlement)
		api.POST("/settlements/:id/orders/:orderID", h.AddOrderToSettlement)
		api.POST("/settlements/:id/finalize", h.FinalizeSettlement)
		api.POST("/settlements/:id/paid", h.MarkSettlementPaid)
	}

	{
		api := r.Group("/api/financials/payments")
		api.POST("", h.CreatePaymentTransaction)
		api.GET("/:id", h.GetPaymentTransaction)
		api.GET("/order/:orderID", h.GetPaymentsByOrder)
		api.POST("/:id/status", h.UpdatePaymentStatus)
		api.POST("/:id/refund", h.ProcessRefund)
		api.POST("/cod/:orderID", h.RecordCODPayment)
		api.POST("/cod/:orderID/remittance", h.RecordCODRemittance)
	}
}

// CreateOrderFinancials handles the creation of a complete order financial record
func (h *FinancialHandler) CreateOrderFinancials(c *gin.Context) {
	var req finance.OrderFinancialsRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	if err := h.service.ProcessOrderFinancials(c, &req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":  "Order financials created successfully",
		"order_id": strconv.Itoa(int(req.OrderFinancials.OrderID)),
	})
}

// GetOrderFinancials retrieves order financial details
func (h *FinancialHandler) GetOrderFinancials(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	orderFinancials, err := h.service.GetOrderFinancialsByOrderID(c, uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, orderFinancials)
}

// CreateCreditNote handles the creation of a credit note
func (h *FinancialHandler) CreateCreditNote(c *gin.Context) {
	var req finance.CreditNoteRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	if _, err := h.service.CreateCreditNote(c, req.CreditNote, req.CreditNoteItems); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":        "Credit note created successfully",
		"credit_note_id": strconv.Itoa(int(req.CreditNote.CreditNoteID)),
	})
}

// GetCreditNote retrieves a credit note by ID
func (h *FinancialHandler) GetCreditNote(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid credit note ID"})
		return
	}

	creditNote, err := h.service.GetCreditNoteByID(c, uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, creditNote)
}

// CreateCreditNoteForReturn creates a credit note for a return
func (h *FinancialHandler) CreateCreditNoteForReturn(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid return ID"})
		return
	}

	var req struct {
		Reason string `json:"reason"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	creditNote, err := h.service.CreateCreditNoteForReturn(c, uint(id), req.Reason)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, creditNote)
}

// CreateDebitNote handles the creation of a debit note
func (h *FinancialHandler) CreateDebitNote(c *gin.Context) {
	var req finance.DebitNoteRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	if err := h.service.CreateDebitNote(c, req.DebitNote, req.DebitNoteItems); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":       "Debit note created successfully",
		"debit_note_id": strconv.Itoa(int(req.DebitNote.DebitNoteID)),
	})
}

// GetDebitNote retrieves a debit note by ID
func (h *FinancialHandler) GetDebitNote(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid debit note ID"})
		return
	}

	debitNote, err := h.service.GetDebitNoteByID(c, uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, debitNote)
}

// GenerateSettlement creates a new settlement for a seller
func (h *FinancialHandler) GenerateSettlement(c *gin.Context) {
	var req finance.SettlementGenerationRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	settlement, err := h.service.GenerateSettlement(c, req.SellerID, req.StartDate, req.EndDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, settlement)
}

// GetSettlement retrieves a settlement by ID
func (h *FinancialHandler) GetSettlement(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid settlement ID"})
		return
	}

	settlement, err := h.service.GetSettlementByID(c, uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, settlement)
}

// AddOrderToSettlement adds an order to a settlement
func (h *FinancialHandler) AddOrderToSettlement(c *gin.Context) {
	settlementID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid settlement ID"})
		return
	}

	orderID, err := strconv.Atoi(c.Param("orderID"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	if err := h.service.AddOrderToSettlement(c, uint(settlementID), uint(orderID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Order added to settlement successfully",
	})
}

// FinalizeSettlement finalizes a settlement for payment
func (h *FinancialHandler) FinalizeSettlement(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid settlement ID"})
		return
	}

	if err := h.service.FinalizeSettlement(c, uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Settlement finalized successfully",
	})
}

// MarkSettlementPaid marks a settlement as paid
func (h *FinancialHandler) MarkSettlementPaid(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid settlement ID"})
		return
	}

	var req struct {
		PaymentDate string `json:"payment_date"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	paymentDate, err := time.Parse("2006-01-02", req.PaymentDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payment date format. Use YYYY-MM-DD"})
		return
	}

	if err := h.service.MarkSettlementAsPaid(c, uint(id), paymentDate); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Settlement marked as paid successfully",
	})
}

// CreatePaymentTransaction handles the creation of a payment transaction
func (h *FinancialHandler) CreatePaymentTransaction(c *gin.Context) {
	var payment dao.PaymentTransaction

	if err := c.ShouldBindJSON(&payment); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	if err := h.service.CreatePaymentTransaction(c, &payment); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":    "Payment transaction created successfully",
		"payment_id": payment.PaymentID,
	})
}

// GetPaymentTransaction retrieves a payment transaction by ID
func (h *FinancialHandler) GetPaymentTransaction(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payment ID"})
		return
	}

	payment, err := h.service.GetPaymentByID(c, uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, payment)
}

// GetPaymentsByOrder retrieves all payment transactions for an order
func (h *FinancialHandler) GetPaymentsByOrder(c *gin.Context) {
	orderID, err := strconv.Atoi(c.Param("orderID"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	payments, err := h.service.GetPaymentsByOrderID(c, uint(orderID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, payments)
}

// UpdatePaymentStatus updates the status of a payment
func (h *FinancialHandler) UpdatePaymentStatus(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payment ID"})
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	if err := h.service.UpdatePaymentStatus(c, uint(id), req.Status); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Payment status updated successfully",
	})
}

// ProcessRefund processes a refund for a payment
func (h *FinancialHandler) ProcessRefund(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payment ID"})
		return
	}

	var req struct {
		Amount    float64 `json:"amount" binding:"required"`
		Reference string  `json:"reference" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	if err := h.service.ProcessRefund(c, uint(id), req.Amount, req.Reference); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Refund processed successfully",
	})
}

// RecordCODPayment records a Cash-on-Delivery payment
func (h *FinancialHandler) RecordCODPayment(c *gin.Context) {
	orderID, err := strconv.Atoi(c.Param("orderID"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	var req struct {
		Amount float64 `json:"amount" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	if err := h.service.RecordCODPayment(c, uint(orderID), req.Amount); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "COD payment recorded successfully",
	})
}

// RecordCODRemittance records the remittance of a COD payment
func (h *FinancialHandler) RecordCODRemittance(c *gin.Context) {
	orderID, err := strconv.Atoi(c.Param("orderID"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	var req struct {
		RemittanceDate string `json:"remittance_date" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	remittanceDate, err := time.Parse("2006-01-02", req.RemittanceDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid remittance date format. Use YYYY-MM-DD"})
		return
	}

	if err := h.service.RecordCODRemittance(c, uint(orderID), remittanceDate); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "COD remittance recorded successfully",
	})
}
