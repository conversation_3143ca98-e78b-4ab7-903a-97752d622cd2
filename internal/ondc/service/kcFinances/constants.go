package kcFinances

const (
	// CreditNoteTypeFullReturn represents a full return credit note
	CreditNoteTypeFullReturn = "FULL_RETURN"

	// CreditNoteTypePartialReturn represents a partial return credit note
	CreditNoteTypePartialReturn = "PARTIAL_RETURN"

	// MissingItems represents a pending adjustment status
	CreditNoteTypeMissingItems = "MISSING_ITEMS"

	// DamagedItems represents a pending adjustment status
	CreditNoteTypeDamagedItems = "DAMAGED_ITEMS"

	// Custom represents a pending adjustment status
	CreditNoteTypeCustom = "CUSTOM"
)

type creditNote struct {
	FULL_RETURN    string
	PARTIAL_RETURN string
	MISSING_ITEMS  string
	DAMAGED_ITEMS  string
	CUSTOM         string
}

var CREDIT_NOTE_TYPE = creditNote{
	FULL_RETURN:    CreditNoteTypeFullReturn,
	PARTIAL_RETURN: CreditNoteTypePartialReturn,
	MISSING_ITEMS:  CreditNoteTypeMissingItems,
	DAMAGED_ITEMS:  CreditNoteTypeDamagedItems,
	CUSTOM:         CreditNoteTypeCustom,
}
