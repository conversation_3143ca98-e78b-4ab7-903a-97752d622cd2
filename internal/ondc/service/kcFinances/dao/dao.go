// models/models.go
package dao

import (
	"time"
)

// Base model for common fields
type BaseModel struct {
	CreatedAt time.Time `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time `json:"updated_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;autoUpdateTime"`
}

// OrderFinancials represents the financial details of an order
type OrderFinancials struct {
	ID             uint64    `json:"id" gorm:"primaryKey;autoIncrement"`
	OrderID        uint64    `json:"order_id" gorm:"index:ix_order_financials_order_id;unique"`
	InvoiceNumber  string    `json:"invoice_number" gorm:"type:varchar(50);index:ix_order_financials_invoice_number"`
	InvoiceDate    time.Time `json:"invoice_date" gorm:"type:timestamp;index:ix_order_financials_invoice_date"`
	GSTINBuyer     string    `json:"gstin_buyer" gorm:"type:varchar(15)"`
	GSTINSeller    string    `json:"gstin_seller" gorm:"type:varchar(15)"`
	PANSeller      string    `json:"pan_seller" gorm:"type:varchar(10)"`
	PlaceOfSupply  string    `json:"place_of_supply" gorm:"type:varchar(50)"`                  // vendor warehouse address state
	PlaceOfDemand  string    `json:"place_of_demand" gorm:"type:varchar(50)"`                  // billing address state
	RetailerID     string    `json:"retailer_id" gorm:"type:varchar(100)"`                     // userid
	SupplierID     uint      `json:"supplier_id" gorm:"index:ix_order_financials_supplier_id"` // seller_id
	CartValue      float64   `json:"cart_value" gorm:"type:decimal(12,2)"`
	TaxableValue   float64   `json:"taxable_value" gorm:"type:decimal(12,2)"`    // cart value - total discount
	CGSTTotal      float64   `json:"cgst_total" gorm:"type:decimal(12,2)"`       // central tax
	SGSTTotal      float64   `json:"sgst_total" gorm:"type:decimal(12,2)"`       // state tax
	IGSTTotal      float64   `json:"igst_total" gorm:"type:decimal(12,2)"`       // integrated tax
	CESSTotal      float64   `json:"cess_total" gorm:"type:decimal(12,2)"`       // cess
	TotalGSTAmount float64   `json:"total_gst_amount" gorm:"type:decimal(12,2)"` // total tax
	OrderTotal     float64   `json:"order_total" gorm:"type:decimal(12,2)"`      // deprecated, use invoice value
	Seller         string    `json:"seller" gorm:"type:varchar(100);not null"`
	InvoiceValue   float64   `json:"invoice_value" gorm:"type:decimal(12,2)"` // invoice value
	OrderValue     float64   `json:"order_value" gorm:"type:decimal(12,2)"`   // order value

	// Discounts
	SellerDiscount   float64 `json:"seller_discount" gorm:"type:float"`
	PlatformDiscount float64 `json:"platform_discount" gorm:"type:float"`
	PlatformCashback float64 `json:"platform_cashback" gorm:"type:float"`
	PaymentCashback  float64 `json:"payment_cashback" gorm:"type:float"`
	MarkdownDiscount float64 `json:"markdown_discount" gorm:"type:float"`
	TotalDiscount    float64 `json:"total_discount" gorm:"type:float"`

	// Charges
	CommissionRate       *float64 `json:"commission_rate" gorm:"type:decimal(5,2)"`
	CommissionAmount     *float64 `json:"commission_amount" gorm:"type:decimal(12,2)"`
	SaaSCharge           *float64 `json:"saas_charge" gorm:"type:decimal(12,2);column:saas_charge"`
	TDSRate              float64  `json:"tds_rate" gorm:"type:decimal(5,2);default:1.00"`
	TDSAmount            float64  `json:"tds_amount" gorm:"type:decimal(10,2)"`
	KCShip               *bool    `json:"kc_ship" gorm:"type:tinyint(1);default:0"`
	TCSApplicable        bool     `json:"tcs_applicable" gorm:"type:tinyint(1);default:0"`
	TCSRate              float64  `json:"tcs_rate" gorm:"type:decimal(5,2)"`
	TCSAmount            float64  `json:"tcs_amount" gorm:"type:decimal(12,2)"`
	ThirdPL              string   `json:"3pl" gorm:"type:varchar(100);column:3pl"`
	ThirdPLChargedWeight *float64 `json:"3pl_charged_weight" gorm:"type:float;column:3pl_charged_weight"`
	ThirdPLCharges       *float64 `json:"3pl_charges" gorm:"type:float;column:3pl_charges"`

	// Payment related fields
	PaymentType               string     `json:"payment_type" gorm:"type:varchar(20)"`
	PaymentGateway            string     `json:"payment_gateway" gorm:"type:varchar(50)"`
	PaymentGatewayCharges     *float64   `json:"payment_gateway_charges" gorm:"type:decimal(10,2);"`
	PaymentGatewayGST         *float64   `json:"payment_gateway_gst" gorm:"type:decimal(10,2);"`
	PaymentTotalCharges       *float64   `json:"payment_total_charges" gorm:"type:decimal(10,2);"`
	AdvanceCollected          float64    `json:"advance_collected" gorm:"type:decimal(12,2);default:0.00"`
	CashCollectedByThirdPL    *float64   `json:"cash_collected_by_3pl" gorm:"type:decimal(12,2);column:cash_collected_by_3pl"`
	CODCharges                *float64   `json:"cod_charges" gorm:"type:decimal(10,2)"`
	CODRemittanceDate         *time.Time `json:"cod_remittance_date" gorm:"type:timestamp"`
	NetPayableToSupplier      *float64   `json:"net_payable_to_supplier" gorm:"type:decimal(12,2)"`
	FinalSettlementToSupplier *float64   `json:"final_settlement_to_supplier" gorm:"type:decimal(12,2)"`
	FinalSettlementDate       *time.Time `json:"final_settlement_date" gorm:"type:date"`

	// Shipping related fields
	ShippingStatus     string     `json:"shipping_status" gorm:"type:varchar(30)"`
	ShippingStatusDate time.Time  `json:"shipping_status_date" gorm:"type:timestamp"`
	OrderConfirmed     time.Time  `json:"order_confirmed" gorm:"type:timestamp"`
	OrderDispatched    *time.Time `json:"order_dispatched" gorm:"type:timestamp"`
	OrderDelivered     *time.Time `json:"order_delivered" gorm:"type:timestamp"`
	Remarks            string     `json:"remarks" gorm:"type:text"`
	CreatedAt          time.Time  `json:"created_at" gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt          time.Time  `json:"updated_at" gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}

// OrderItemFinancials represents the financial details of an order item
type OrderItemFinancials struct {
	ID                uint64    `json:"id" gorm:"primaryKey;autoIncrement"`
	OrderID           uint64    `json:"order_id" gorm:"type:bigint;not null;index:ix_order_item_financials_order_id"`
	OrderItemID       uint64    `json:"order_item_id" gorm:"type:bigint;not null;index:ix_order_item_financials_order_item_id"`
	ProductID         uint64    `json:"product_id" gorm:"type:bigint;not null;index:ix_order_item_financials_product_id"`
	SKUID             string    `json:"sku_id" gorm:"type:varchar(50); column:sku_id"`
	HSNCode           string    `json:"hsn_code" gorm:"type:varchar(20);index:ix_order_item_financials_hsn_code"`
	KCUnitPrice       float64   `json:"kc_unit_price" gorm:"type:decimal(12,2)"`
	KCSellingPrice    float64   `json:"kc_selling_price" gorm:"type:decimal(12,2)"`
	Quantity          int       `json:"quantity" gorm:"type:int"`
	TotalKCPrice      float64   `json:"total_kc_price" gorm:"type:decimal(12,2)"`
	TotalSellingPrice float64   `json:"total_selling_price" gorm:"type:decimal(12,2)"`
	TotalDiscount     float64   `json:"total_discount" gorm:"type:decimal(12,2)"`
	PlatformDiscount  float64   `json:"platform_discount" gorm:"type:float"` // this is platform discount
	SellerDiscount    float64   `json:"seller_discount" gorm:"type:float"`   // this is seller discount
	PaymentDiscount   float64   `json:"payment_discount" gorm:"type:float"`  // this is payment discount
	PlatformCashback  float64   `json:"platform_cashback" gorm:"type:float"` // this is cashback given by platform
	MarkdownDiscount  float64   `json:"markdown_discount" gorm:"type:float"` // this is markdown discount
	TaxableValue      float64   `json:"taxable_value" gorm:"type:decimal(12,2)"`
	GSTRate           float64   `json:"gst_rate" gorm:"type:decimal(5,2)"`
	CGSTRate          float64   `json:"cgst_rate" gorm:"type:decimal(5,2)"`
	SGSTRate          float64   `json:"sgst_rate" gorm:"type:decimal(5,2)"`
	IGSTRate          float64   `json:"igst_rate" gorm:"type:decimal(5,2)"`
	CESSRate          float64   `json:"cess_rate" gorm:"type:decimal(5,2)"`
	CGSTAmount        float64   `json:"cgst_amount" gorm:"type:decimal(12,2)"`
	SGSTAmount        float64   `json:"sgst_amount" gorm:"type:decimal(12,2)"`
	IGSTAmount        float64   `json:"igst_amount" gorm:"type:decimal(12,2)"`
	CESSAmount        float64   `json:"cess_amount" gorm:"type:decimal(12,2)"`
	TotalTax          float64   `json:"total_tax" gorm:"type:decimal(12,2)"`
	ItemTotal         float64   `json:"item_total" gorm:"type:decimal(12,2)"`
	IsFreeItem        bool      `json:"is_free_item" gorm:"type:tinyint(1);default:0"`
	FreeItemReason    string    `json:"free_item_reason" gorm:"type:varchar(100)"`
	CreatedAt         time.Time `json:"created_at" gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt         time.Time `json:"updated_at" gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}

// OrderDiscount represents a discount applied to an order or order item
type OrderDiscount struct {
	ID                  uint64  `json:"id" gorm:"primaryKey"`
	OrderID             uint64  `json:"order_id" gorm:"index"`
	OrderItemID         uint64  `json:"order_item_id" gorm:"index"`
	DiscountType        string  `json:"discount_type" gorm:"type:varchar(50);index"`
	DiscountProvider    string  `json:"discount_provider" gorm:"type:varchar(30);index"`
	DiscountCode        string  `json:"discount_code" gorm:"type:varchar(50)"`
	IsPercentage        bool    `json:"is_percentage" gorm:"type:boolean"`
	DiscountPercentage  float64 `json:"discount_percentage" gorm:"type:decimal(5,2)"`
	DiscountAmount      float64 `json:"discount_amount" gorm:"type:decimal(12,2)"`
	DiscountDescription string  `json:"discount_description" gorm:"type:text"`
	OfferType           string  `json:"offer_type" gorm:"type:varchar(50)"`
	IsPreTax            bool    `json:"is_pre_tax" gorm:"type:boolean"`
	BaseModel
}

// FreeItem represents a free item included with an order
type FreeItem struct {
	ID               uint      `json:"id" gorm:"primaryKey"`
	OrderID          uint      `json:"order_id" gorm:"index"`
	ParentItemID     uint      `json:"parent_item_id" gorm:"index"`
	ProductID        uint      `json:"product_id" gorm:"index"`
	SKUID            string    `json:"sku_id" gorm:"type:varchar(50); column:sku_id"`
	Quantity         int       `json:"quantity" gorm:"type:int"`
	RetailValue      float64   `json:"retail_value" gorm:"type:decimal(10,2)"`
	ProvidedBy       string    `json:"provided_by" gorm:"type:varchar(20);index"`
	OfferDescription string    `json:"offer_description" gorm:"type:text"`
	ThresholdAmount  float64   `json:"threshold_amount" gorm:"type:decimal(10,2)"`
	CreatedAt        time.Time `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
}

// ShipmentFinancials represents the financial details of a shipment
type ShipmentFinancials struct {
	ID                            uint       `json:"id" gorm:"primaryKey"`
	OrderID                       uint       `json:"order_id" gorm:"index"`
	WaybillNumber                 string     `json:"waybill_number" gorm:"type:varchar(50);index"`
	CourierPartner                string     `json:"courier_partner" gorm:"type:varchar(50);index"`
	CourierPartnerGSTIN           string     `json:"courier_partner_gstin" gorm:"type:varchar(15)"`
	LogisticsZone                 string     `json:"logistics_zone" gorm:"type:varchar(50)"`
	SellerDeadWeight              float64    `json:"seller_dead_weight" gorm:"type:decimal(8,3)"`
	SellerLengthCm                float64    `json:"seller_length_cm" gorm:"type:decimal(8,2)"`
	SellerBreadthCm               float64    `json:"seller_breadth_cm" gorm:"type:decimal(8,2)"`
	SellerHeightCm                float64    `json:"seller_height_cm" gorm:"type:decimal(8,2)"`
	SellerVolumetricWeight        float64    `json:"seller_volumetric_weight" gorm:"type:decimal(8,3)"`
	CourierDeadWeight             *float64   `json:"courier_dead_weight" gorm:"type:decimal(8,3)"`
	CourierLengthCm               *float64   `json:"courier_length_cm" gorm:"type:decimal(8,2)"`
	CourierBreadthCm              *float64   `json:"courier_breadth_cm" gorm:"type:decimal(8,2)"`
	CourierHeightCm               *float64   `json:"courier_height_cm" gorm:"type:decimal(8,2)"`
	CourierVolumetricWeight       *float64   `json:"courier_volumetric_weight" gorm:"type:decimal(8,3)"`
	ChargeableWeight              *float64   `json:"chargeable_weight" gorm:"type:decimal(8,3)"`
	CourierChargesActual          *float64   `json:"courier_charges_actual" gorm:"type:decimal(10,2)"`
	CourierChargesBilled          *float64   `json:"courier_charges_billed" gorm:"type:decimal(10,2)"`
	CourierMarkupAmount           *float64   `json:"courier_markup_amount" gorm:"type:decimal(10,2)"`
	CourierGSTRate                *float64   `json:"courier_gst_rate" gorm:"type:decimal(5,2)"`
	CourierGSTAmount              *float64   `json:"courier_gst_amount" gorm:"type:decimal(10,2)"`
	IsRTO                         bool       `json:"is_rto" gorm:"type:boolean;default:false"`
	RTOCharges                    float64    `json:"rto_charges" gorm:"type:decimal(10,2);default:0"`
	RTOReason                     string     `json:"rto_reason" gorm:"type:varchar(100)"`
	SettlementReceivedFromCourier *float64   `json:"settlement_received_from_courier" gorm:"type:decimal(10,2)"`
	SettlementReceivedDate        *time.Time `json:"settlement_received_date" gorm:"type:date"`
	CODCollectionCharges          *float64   `json:"cod_collection_charges" gorm:"type:decimal(10,2);"`
	CreatedAt                     time.Time  `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt                     time.Time  `json:"updated_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;autoUpdateTime"`
	Origin                        string     `json:"origin" gorm:"type:varchar(10)"`      // origin pincode
	Destination                   string     `json:"destination" gorm:"type:varchar(10)"` // destination pincode
}

// PaymentTransaction represents the payment transaction details
type PaymentTransaction struct {
	PaymentID             uint      `json:"payment_id" gorm:"primaryKey"`
	OrderID               uint      `json:"order_id" gorm:"index"`
	TransactionReference  string    `json:"transaction_reference" gorm:"type:varchar(100);index"`
	PaymentGateway        string    `json:"payment_gateway" gorm:"type:varchar(50)"`
	PaymentMethod         string    `json:"payment_method" gorm:"type:varchar(50)"`       // UPI, NETBANKING, CREDIT_CARD, DEBIT_CARD, WALLET
	PaymentStatus         string    `json:"payment_status" gorm:"type:varchar(20);index"` // SUCCESS, FAILED, PENDING
	Amount                float64   `json:"amount" gorm:"type:decimal(12,2)"`
	Currency              string    `json:"currency" gorm:"type:varchar(3);default:'INR'"`
	GatewayFee            float64   `json:"gateway_fee" gorm:"type:decimal(10,2)"`
	GatewayTax            float64   `json:"gateway_tax" gorm:"type:decimal(10,2)"`
	PaymentDate           time.Time `json:"payment_date" gorm:"type:timestamp"`
	SettlementReference   string    `json:"settlement_reference" gorm:"type:varchar(100)"`
	SettlementDate        time.Time `json:"settlement_date" gorm:"type:date"`
	RefundReference       string    `json:"refund_reference" gorm:"type:varchar(100)"`
	RefundStatus          string    `json:"refund_status" gorm:"type:varchar(20)"`
	RefundAmount          float64   `json:"refund_amount" gorm:"type:decimal(12,2)"`
	RefundProcessedDate   time.Time `json:"refund_processed_date" gorm:"type:timestamp"`
	CustomerPaymentDetail string    `json:"customer_payment_detail" gorm:"type:text"` // JSON string with masked sensitive info
	CreatedAt             time.Time `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt             time.Time `json:"updated_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;autoUpdateTime"`
}

// ReturnsRefunds represents a return or refund for an order
type ReturnsRefunds struct {
	ID                         uint      `json:"id" gorm:"primaryKey"`
	OrderID                    uint      `json:"order_id" gorm:"index"`
	OrderItemID                uint      `json:"order_item_id" gorm:"index"`
	Quantity                   int       `json:"quantity" gorm:"type:int"`
	ReturnType                 string    `json:"return_type" gorm:"type:varchar(30);index"`
	ReturnAmount               float64   `json:"return_amount" gorm:"type:decimal(12,2)"`
	ReturnReason               string    `json:"return_reason" gorm:"type:text"`
	ReturnInitiatedDate        time.Time `json:"return_initiated_date" gorm:"type:date"`
	ReturnConfirmedDate        time.Time `json:"return_confirmed_date" gorm:"type:date"`
	SellerConfirmationReceived bool      `json:"seller_confirmation_received" gorm:"type:boolean"`
	SellerConfirmationDate     time.Time `json:"seller_confirmation_date" gorm:"type:date"`
	CreditNoteID               uint      `json:"credit_note_id" gorm:"index"`
	RefundStatus               string    `json:"refund_status" gorm:"type:varchar(30);index"`
	RefundProcessedDate        time.Time `json:"refund_processed_date" gorm:"type:date"`
	PaymentRefundID            uint64    `json:"payment_refund_id" gorm:"index"` // Link to PaymentTransaction for refund
	BaseModel
}

// CreditNote represents a credit note issued to adjust an invoice
type CreditNote struct {
	CreditNoteID           uint       `json:"credit_note_id" gorm:"primaryKey"`
	CreditNoteNumber       string     `json:"credit_note_number" gorm:"type:varchar(50);unique;index"`
	CreditNoteDate         time.Time  `json:"credit_note_date" gorm:"type:date"`
	RelatedInvoiceNumber   string     `json:"related_invoice_number" gorm:"type:varchar(50);index"`
	RelatedInvoiceDate     time.Time  `json:"related_invoice_date" gorm:"type:date"`
	SellerID               uint       `json:"seller_id" gorm:"index"`
	BuyerID                string     `json:"buyer_id" gorm:"type:varchar(100);index"`
	OrderID                uint       `json:"order_id" gorm:"index"`
	CreditNoteReason       string     `json:"credit_note_reason" gorm:"type:varchar(100)"`
	CreditNoteType         string     `json:"credit_note_type" gorm:"type:varchar(50);index"`
	TotalValueBeforeTax    float64    `json:"total_value_before_tax" gorm:"type:decimal(12,2)"`
	CGSTAmount             float64    `json:"cgst_amount" gorm:"type:decimal(10,2)"`
	SGSTAmount             float64    `json:"sgst_amount" gorm:"type:decimal(10,2)"`
	IGSTAmount             float64    `json:"igst_amount" gorm:"type:decimal(10,2)"`
	TotalCreditNoteValue   float64    `json:"total_credit_note_value" gorm:"type:decimal(12,2)"`
	AdjustmentStatus       string     `json:"adjustment_status" gorm:"type:varchar(20);index"`
	AdjustmentDate         *time.Time `json:"adjustment_date" gorm:"type:date"`
	AdjustedInSettlementID uint       `json:"adjusted_in_settlement_id" gorm:"index"`
	Remarks                string     `json:"remarks" gorm:"type:text"`
	CreatedAt              time.Time  `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt              time.Time  `json:"updated_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;autoUpdateTime"`
	// Relations
	CreditNoteItems []CreditNoteItem `json:"credit_note_items,omitempty" gorm:"foreignKey:CreditNoteID"`
}

func (c *CreditNote) TableName() string {
	return "credit_notes"
}

// CreditNoteItem represents an item in a credit note
type CreditNoteItem struct {
	CreditNoteItemID uint      `json:"credit_note_item_id" gorm:"primaryKey"`
	CreditNoteID     uint      `json:"credit_note_id" gorm:"index"`
	OrderItemID      uint      `json:"order_item_id" gorm:"index"`
	ProductID        uint64    `json:"product_id" gorm:"index"`
	SKUID            string    `json:"sku_id" gorm:"type:varchar(50); column:sku_id"`
	HSNCode          string    `json:"hsn_code" gorm:"type:varchar(20)"`
	Quantity         int       `json:"quantity" gorm:"type:int"`
	UnitPrice        float64   `json:"unit_price" gorm:"type:decimal(10,2)"`
	TotalValue       float64   `json:"total_value" gorm:"type:decimal(12,2)"`
	GSTRate          float64   `json:"gst_rate" gorm:"type:decimal(5,2)"`
	CGSTAmount       float64   `json:"cgst_amount" gorm:"type:decimal(10,2)"`
	SGSTAmount       float64   `json:"sgst_amount" gorm:"type:decimal(10,2)"`
	IGSTAmount       float64   `json:"igst_amount" gorm:"type:decimal(10,2)"`
	TotalTax         float64   `json:"total_tax" gorm:"type:decimal(10,2)"`
	ItemTotal        float64   `json:"item_total" gorm:"type:decimal(12,2)"`
	Reason           string    `json:"reason" gorm:"type:text"`
	CreatedAt        time.Time `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
}

func (c *CreditNoteItem) TableName() string {
	return "credit_note_items"
}

// DebitNote represents a debit note issued to adjust an invoice
type DebitNote struct {
	DebitNoteID            uint      `json:"debit_note_id" gorm:"primaryKey"`
	DebitNoteNumber        string    `json:"debit_note_number" gorm:"type:varchar(50);unique;index"`
	DebitNoteDate          time.Time `json:"debit_note_date" gorm:"type:date"`
	RelatedInvoiceNumber   string    `json:"related_invoice_number" gorm:"type:varchar(50);index"`
	RelatedInvoiceDate     time.Time `json:"related_invoice_date" gorm:"type:date"`
	SellerID               uint      `json:"seller_id" gorm:"index"`
	BuyerID                string    `json:"buyer_id" gorm:"type:varchar(100);index"`
	OrderID                uint      `json:"order_id" gorm:"index"`
	DebitNoteReason        string    `json:"debit_note_reason" gorm:"type:varchar(100)"`
	DebitNoteType          string    `json:"debit_note_type" gorm:"type:varchar(50);index"`
	TotalValueBeforeTax    float64   `json:"total_value_before_tax" gorm:"type:decimal(12,2)"`
	CGSTAmount             float64   `json:"cgst_amount" gorm:"type:decimal(10,2)"`
	SGSTAmount             float64   `json:"sgst_amount" gorm:"type:decimal(10,2)"`
	IGSTAmount             float64   `json:"igst_amount" gorm:"type:decimal(10,2)"`
	TotalDebitNoteValue    float64   `json:"total_debit_note_value" gorm:"type:decimal(12,2)"`
	AdjustmentStatus       string    `json:"adjustment_status" gorm:"type:varchar(20);index"`
	AdjustmentDate         time.Time `json:"adjustment_date" gorm:"type:date"`
	AdjustedInSettlementID uint      `json:"adjusted_in_settlement_id" gorm:"index"`
	Remarks                string    `json:"remarks" gorm:"type:text"`
	BaseModel
	// Relations
	DebitNoteItems []DebitNoteItem `json:"debit_note_items,omitempty" gorm:"foreignKey:DebitNoteID"`
}

// DebitNoteItem represents an item in a debit note
type DebitNoteItem struct {
	DebitNoteItemID uint      `json:"debit_note_item_id" gorm:"primaryKey"`
	DebitNoteID     uint      `json:"debit_note_id" gorm:"index"`
	OrderItemID     uint      `json:"order_item_id" gorm:"index"`
	ProductID       uint      `json:"product_id" gorm:"index"`
	SKUID           string    `json:"sku_id" gorm:"type:varchar(50); column:sku_id"`
	HSNCode         string    `json:"hsn_code" gorm:"type:varchar(20)"`
	Quantity        int       `json:"quantity" gorm:"type:int"`
	UnitPrice       float64   `json:"unit_price" gorm:"type:decimal(10,2)"`
	TotalValue      float64   `json:"total_value" gorm:"type:decimal(12,2)"`
	GSTRate         float64   `json:"gst_rate" gorm:"type:decimal(5,2)"`
	CGSTAmount      float64   `json:"cgst_amount" gorm:"type:decimal(10,2)"`
	SGSTAmount      float64   `json:"sgst_amount" gorm:"type:decimal(10,2)"`
	IGSTAmount      float64   `json:"igst_amount" gorm:"type:decimal(10,2)"`
	TotalTax        float64   `json:"total_tax" gorm:"type:decimal(10,2)"`
	ItemTotal       float64   `json:"item_total" gorm:"type:decimal(12,2)"`
	Reason          string    `json:"reason" gorm:"type:text"`
	CreatedAt       time.Time `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
}

// SellerSettlement relations
type SellerSettlement struct {
	SettlementID               uint      `json:"settlement_id" gorm:"primaryKey"`
	SellerID                   uint      `json:"seller_id" gorm:"index"`
	SettlementPeriodStart      time.Time `json:"settlement_period_start" gorm:"type:date"`
	SettlementPeriodEnd        time.Time `json:"settlement_period_end" gorm:"type:date"`
	InvoiceNumber              string    `json:"invoice_number" gorm:"type:varchar(50);index"`
	InvoiceDate                time.Time `json:"invoice_date" gorm:"type:date"`
	TotalOrderValue            float64   `json:"total_order_value" gorm:"type:decimal(14,2)"`
	TotalCommission            float64   `json:"total_commission" gorm:"type:decimal(12,2)"`
	TotalOMSCharges            float64   `json:"total_oms_charges" gorm:"type:decimal(12,2)"`
	TotalDeliveryCharges       float64   `json:"total_delivery_charges" gorm:"type:decimal(12,2)"`
	TotalDeliveryGST           float64   `json:"total_delivery_gst" gorm:"type:decimal(12,2)"`
	TotalCODCharges            float64   `json:"total_cod_charges" gorm:"type:decimal(12,2)"`
	TotalPaymentGatewayCharges float64   `json:"total_payment_gateway_charges" gorm:"type:decimal(12,2)"`
	TotalRTOCharges            float64   `json:"total_rto_charges" gorm:"type:decimal(12,2)"`
	TotalKCDiscounts           float64   `json:"total_kc_discounts" gorm:"type:decimal(12,2)"`
	TotalSellerDiscounts       float64   `json:"total_seller_discounts" gorm:"type:decimal(12,2)"`
	TotalReturnsRefunds        float64   `json:"total_returns_refunds" gorm:"type:decimal(12,2)"`
	TotalDamagedGoods          float64   `json:"total_damaged_goods" gorm:"type:decimal(12,2)"`
	TotalCreditNotes           float64   `json:"total_credit_notes" gorm:"type:decimal(12,2)"`
	TotalDebitNotes            float64   `json:"total_debit_notes" gorm:"type:decimal(12,2)"`
	TotalTDSAmount             float64   `json:"total_tds_amount" gorm:"type:decimal(12,2)"`
	TotalTCSAmount             float64   `json:"total_tcs_amount" gorm:"type:decimal(12,2)"`
	ServiceInvoiceAmount       float64   `json:"service_invoice_amount" gorm:"type:decimal(12,2)"`
	ServiceInvoiceCGST         float64   `json:"service_invoice_cgst" gorm:"type:decimal(12,2)"`
	ServiceInvoiceSGST         float64   `json:"service_invoice_sgst" gorm:"type:decimal(12,2)"`
	ServiceInvoiceIGST         float64   `json:"service_invoice_igst" gorm:"type:decimal(12,2)"`
	ServiceInvoiceTotal        float64   `json:"service_invoice_total" gorm:"type:decimal(14,2)"`
	NetPayableToSupplier       float64   `json:"net_payable_to_supplier" gorm:"type:decimal(14,2)"`
	PaymentStatus              string    `json:"payment_status" gorm:"type:varchar(20);index"`
	PaymentDate                time.Time `json:"payment_date" gorm:"type:date"`
	PaymentTransactionID       uint      `json:"payment_transaction_id" gorm:"index"` // Link to payment transaction
	Remarks                    string    `json:"remarks" gorm:"type:text"`
	CreatedAt                  time.Time `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt                  time.Time `json:"updated_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;autoUpdateTime"`
	// Relations
	SettlementOrders []SettlementOrder `json:"settlement_orders,omitempty" gorm:"foreignKey:SettlementID"`
	SettlementNotes  []SettlementNote  `json:"settlement_notes,omitempty" gorm:"foreignKey:SettlementID"`
}

// SettlementOrder links settlement with individual orders
type SettlementOrder struct {
	SettlementOrderID    uint      `json:"settlement_order_id" gorm:"primaryKey"`
	SettlementID         uint      `json:"settlement_id" gorm:"index"`
	OrderID              uint      `json:"order_id" gorm:"index"`
	InvoiceNumber        string    `json:"invoice_number" gorm:"type:varchar(50);index"`
	OrderDate            time.Time `json:"order_date" gorm:"type:date"`
	OrderValue           float64   `json:"order_value" gorm:"type:decimal(12,2)"`
	TotalGSTAmount       float64   `json:"total_gst_amount" gorm:"type:decimal(12,2)"`
	CommissionAmount     float64   `json:"commission_amount" gorm:"type:decimal(10,2)"`
	OMSCharge            float64   `json:"oms_charge" gorm:"type:decimal(8,2)"`
	DeliveryCharge       float64   `json:"delivery_charge" gorm:"type:decimal(10,2)"`
	DeliveryGST          float64   `json:"delivery_gst" gorm:"type:decimal(8,2)"`
	CODCharge            float64   `json:"cod_charge" gorm:"type:decimal(8,2)"`
	PaymentGatewayCharge float64   `json:"payment_gateway_charge" gorm:"type:decimal(8,2)"`
	RTOCharge            float64   `json:"rto_charge" gorm:"type:decimal(10,2)"`
	KCDiscount           float64   `json:"kc_discount" gorm:"type:decimal(10,2)"`
	SellerDiscount       float64   `json:"seller_discount" gorm:"type:decimal(10,2)"`
	ReturnsRefunds       float64   `json:"returns_refunds" gorm:"type:decimal(10,2)"`
	TDSAmount            float64   `json:"tds_amount" gorm:"type:decimal(10,2)"`
	TCSAmount            float64   `json:"tcs_amount" gorm:"type:decimal(10,2)"`
	NetPayable           float64   `json:"net_payable" gorm:"type:decimal(12,2)"`
	CreatedAt            time.Time `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
}

// SettlementNote links settlements with credit/debit notes
type SettlementNote struct {
	SettlementNoteID uint      `json:"settlement_note_id" gorm:"primaryKey"`
	SettlementID     uint      `json:"settlement_id" gorm:"index"`
	CreditNoteID     uint      `json:"credit_note_id" gorm:"index"`
	DebitNoteID      uint      `json:"debit_note_id" gorm:"index"`
	NoteType         string    `json:"note_type" gorm:"type:varchar(10)"`
	NoteNumber       string    `json:"note_number" gorm:"type:varchar(50);index"`
	NoteDate         time.Time `json:"note_date" gorm:"type:date"`
	NoteAmount       float64   `json:"note_amount" gorm:"type:decimal(12,2)"`
	CreatedAt        time.Time `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
}

// FinancialAuditLog represents a financial audit log entry
type FinancialAuditLog struct {
	LogID      uint      `json:"log_id" gorm:"primaryKey"`
	TableName  string    `json:"table_name" gorm:"type:varchar(50);index"`
	RecordID   uint      `json:"record_id" gorm:"index"`
	ActionType string    `json:"action_type" gorm:"type:varchar(20)"`
	FieldName  string    `json:"field_name" gorm:"type:varchar(50)"`
	OldValue   string    `json:"old_value" gorm:"type:text"`
	NewValue   string    `json:"new_value" gorm:"type:text"`
	ChangedBy  string    `json:"changed_by" gorm:"type:varchar(50)"`
	ChangeDate time.Time `json:"change_date" gorm:"type:timestamp;not null"`
	Reason     string    `json:"reason" gorm:"type:text"`
}

// HSNTaxRate represents the GST rate for an HSN code
type HSNTaxRate struct {
	HSNID          uint      `json:"hsn_id" gorm:"primaryKey"`
	HSNCode        string    `json:"hsn_code" gorm:"type:varchar(20);index"`
	HSNDescription string    `json:"hsn_description" gorm:"type:text"`
	GSTRate        float64   `json:"gst_rate" gorm:"type:decimal(5,2)"`
	CGSTRate       float64   `json:"cgst_rate" gorm:"type:decimal(5,2)"`
	SGSTRate       float64   `json:"sgst_rate" gorm:"type:decimal(5,2)"`
	IGSTRate       float64   `json:"igst_rate" gorm:"type:decimal(5,2)"`
	CESSRate       float64   `json:"cess_rate" gorm:"type:decimal(5,2);default:0"`
	EffectiveFrom  time.Time `json:"effective_from" gorm:"type:date"`
	EffectiveTo    time.Time `json:"effective_to" gorm:"type:date"`
	CreatedAt      time.Time `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt      time.Time `json:"updated_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;autoUpdateTime"`
}

type ThreePLZonePricingMapping struct {
	ID                  uint     `json:"id" gorm:"primaryKey;autoIncrement"`
	Origin              *string  `json:"origin" gorm:"type:varchar(10)"`
	Destination         *string  `json:"destination" gorm:"type:varchar(10)"`
	BillingZone         *string  `json:"billing_zone" gorm:"type:varchar(50)"`
	Zone                *string  `json:"zone" gorm:"type:varchar(50)"`
	TAT                 *int     `json:"tat" gorm:"type:int"`
	SPSFPickupCutoff    *string  `json:"sp_sf_pickup_cutoff" gorm:"type:varchar(50)"`
	ThreePLName         *string  `json:"3pl_name" gorm:"type:varchar(50);column:3pl_name"`
	ThreePLZone         *string  `json:"3pl_zone" gorm:"type:varchar(50);column:3pl_zone"`
	ODPair              *string  `json:"od_pair" gorm:"type:varchar(50)"`
	WeightRange_0_3KG   *float64 `json:"0_3kg" gorm:"type:decimal(10,2);column:0_3_kg"`
	WeightRange_3_4KG   *float64 `json:"3_4kg" gorm:"type:decimal(10,2);column:3_4_kg"`
	WeightRange_4_5KG   *float64 `json:"4_5kg" gorm:"type:decimal(10,2);column:4_5_kg"`
	WeightRange_5_6KG   *float64 `json:"5_6kg" gorm:"type:decimal(10,2);column:5_6_kg"`
	WeightRange_6_8KG   *float64 `json:"6_8kg" gorm:"type:decimal(10,2);column:6_8_kg"`
	WeightRange_8_10KG  *float64 `json:"8_10kg" gorm:"type:decimal(10,2);column:8_10_kg"`
	WeightRange_ExtraKG *float64 `json:"extra_kg" gorm:"type:decimal(10,2);column:extra_kg"`
}

func (ThreePLZonePricingMapping) TableName() string {
	return "3pl_zone_price_mapping"
}

// CsvUploads represents the csv_uploads table structure
type CsvUploads struct {
	ID               uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	ActionType       string    `gorm:"column:action_type;type:varchar(100);not null" json:"action_type"`
	CsvType          string    `gorm:"column:csv_type;type:varchar(100);not null" json:"csv_type"`
	RawDataURL       *string   `gorm:"column:raw_data_url;type:varchar(400)" json:"raw_data_url"`
	ValidatedDataURL *string   `gorm:"column:validated_data_url;type:varchar(400)" json:"validated_data_url"`
	FailedCount      *int      `gorm:"column:failed_count" json:"failed_count"`
	SuccessCount     *int      `gorm:"column:success_count" json:"success_count"`
	FileID           *string   `gorm:"column:file_id;type:varchar(100)" json:"file_id"`
	UpdatedBy        string    `gorm:"column:updated_by;type:varchar(100);not null" json:"updated_by"`
	TotalRecords     *int      `gorm:"column:total_records" json:"total_records"`
	Force            *bool     `gorm:"column:force" json:"force"`
	CreatedAt        time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName specifies the table name for CsvUploads
func (CsvUploads) TableName() string {
	return "csv_uploads"
}
