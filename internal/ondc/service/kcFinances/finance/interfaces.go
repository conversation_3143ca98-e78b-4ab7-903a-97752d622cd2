// finance/interfaces.go
package finance

import (
	"context"
	"kc/internal/ondc/service/kcFinances/dao"
	"time"
)

// OrderFinancialsService handles operations related to order financial records
type OrderFinancialsService interface {
	// Create new order financial record
	CreateOrderFinancials(ctx context.Context, orderFinancials *dao.OrderFinancials) error

	// Create order items financial records
	CreateOrderItemFinancials(ctx context.Context, orderItemFinancials []*dao.OrderItemFinancials) error

	// Create order discounts
	CreateOrderDiscounts(ctx context.Context, orderDiscounts []*dao.OrderDiscount) error

	// Create free items
	CreateFreeItems(ctx context.Context, freeItems []*dao.FreeItem) error

	// Process a complete order with all related financial data
	ProcessOrderFinancials(ctx context.Context, order *OrderFinancialsRequest) error

	// Get order financials by order ID
	GetOrderFinancialsByOrderID(ctx context.Context, orderID uint) (*dao.OrderFinancials, error)

	// Update order financials
	UpdateOrderFinancials(ctx context.Context, orderFinancials *dao.OrderFinancials) error

	// Calculate net payable amount for an order
	CalculateNetPayableToSupplier(ctx context.Context, orderID uint) (float64, error)
}

// ShipmentFinancialsService handles operations related to shipment financial records
type ShipmentFinancialsService interface {
	// Create shipment financial record
	CreateShipmentFinancials(ctx context.Context, shipmentFinancials *dao.ShipmentFinancials) error

	// Update shipment financial record
	UpdateShipmentFinancials(ctx context.Context, shipmentFinancials *dao.ShipmentFinancials) error

	// Get shipment financials by order ID
	GetShipmentFinancialsByOrderID(ctx context.Context, orderID uint) ([]*dao.ShipmentFinancials, error)

	// Get shipment financials by shipment ID
	GetShipmentFinancialsByShipmentID(ctx context.Context, shipmentID uint) (*dao.ShipmentFinancials, error)

	// Process RTO for a shipment
	ProcessRTO(ctx context.Context, shipmentID uint, rtoReason string, rtoCharges float64) error
}

// ReturnsService handles operations related to returns and refunds
type ReturnsService interface {
	// Create a new return/refund record
	CreateReturn(ctx context.Context, returnRefund *dao.ReturnsRefunds) error

	// Update a return/refund record
	UpdateReturn(ctx context.Context, returnRefund *dao.ReturnsRefunds) error

	// Confirm return (update status and dates)
	ConfirmReturn(ctx context.Context, returnID uint, sellerConfirmed bool) error

	// Process refund for a return
	ProcessRefund(ctx context.Context, returnID uint, refundAmount float64) error

	// Get returns by order ID
	GetReturnsByOrderID(ctx context.Context, orderID uint) ([]*dao.ReturnsRefunds, error)
}

// CreditNoteService handles operations related to credit notes
type CreditNoteService interface {
	// Create a new credit note with items
	CreateCreditNote(ctx context.Context, creditNote *dao.CreditNote, items []*dao.CreditNoteItem) error

	// Get credit note by ID
	GetCreditNoteByID(ctx context.Context, creditNoteID uint) (*dao.CreditNote, error)

	// Get credit notes by order ID
	GetCreditNotesByOrderID(ctx context.Context, orderID uint) ([]*dao.CreditNote, error)

	// Get credit notes by seller ID and date range
	GetCreditNotesBySellerAndPeriod(ctx context.Context, sellerID uint, startDate, endDate time.Time) ([]*dao.CreditNote, error)

	// Create credit note for a return
	CreateCreditNoteForReturn(ctx context.Context, returnID uint, reason string) (*dao.CreditNote, error)

	// Apply credit note to settlement
	ApplyCreditNoteToSettlement(ctx context.Context, creditNoteID, settlementID uint) error
}

// DebitNoteService handles operations related to debit notes
type DebitNoteService interface {
	// Create a new debit note with items
	CreateDebitNote(ctx context.Context, debitNote *dao.DebitNote, items []*dao.DebitNoteItem) error

	// Get debit note by ID
	GetDebitNoteByID(ctx context.Context, debitNoteID uint) (*dao.DebitNote, error)

	// Get debit notes by order ID
	GetDebitNotesByOrderID(ctx context.Context, orderID uint) ([]*dao.DebitNote, error)

	// Get debit notes by seller ID and date range
	GetDebitNotesBySellerAndPeriod(ctx context.Context, sellerID uint, startDate, endDate time.Time) ([]*dao.DebitNote, error)

	// Apply debit note to settlement
	ApplyDebitNoteToSettlement(ctx context.Context, debitNoteID, settlementID uint) error
}

// SettlementService handles operations related to seller settlements
type SettlementService interface {
	// Generate a settlement for a seller for a specific period
	GenerateSettlement(ctx context.Context, sellerID uint, startDate, endDate time.Time) (*dao.SellerSettlement, error)

	// Get settlement by ID
	GetSettlementByID(ctx context.Context, settlementID uint) (*dao.SellerSettlement, error)

	// Get settlements by seller ID
	GetSettlementsBySellerID(ctx context.Context, sellerID uint) ([]*dao.SellerSettlement, error)

	// Add order to settlement
	AddOrderToSettlement(ctx context.Context, settlementID, orderID uint) error

	// Add credit note to settlement
	AddCreditNoteToSettlement(ctx context.Context, settlementID, creditNoteID uint) error

	// Add debit note to settlement
	AddDebitNoteToSettlement(ctx context.Context, settlementID, debitNoteID uint) error

	// Finalize settlement (calculate final amounts and mark as ready for payment)
	FinalizeSettlement(ctx context.Context, settlementID uint) error

	// Mark settlement as paid
	MarkSettlementAsPaid(ctx context.Context, settlementID uint, paymentDate time.Time) error
}

// AuditLogService handles operations related to financial audit logging
type AuditLogService interface {
	// Create an audit log entry
	CreateAuditLog(ctx context.Context, tableName string, recordID uint, actionType string, fieldName string, oldValue, newValue string, changedBy string, reason string) error

	// Get audit logs for a specific record
	GetAuditLogsByRecord(ctx context.Context, tableName string, recordID uint) ([]*dao.FinancialAuditLog, error)
}

// PaymentService handles operations related to payment transactions
type PaymentService interface {
	// Create a new payment transaction
	CreatePaymentTransaction(ctx context.Context, payment *dao.PaymentTransaction) error

	// Update payment transaction status
	UpdatePaymentStatus(ctx context.Context, paymentID uint, status string) error

	// Process refund for a payment
	ProcessRefund(ctx context.Context, paymentID uint, amount float64, reference string) error

	// Get payment transaction by ID
	GetPaymentByID(ctx context.Context, paymentID uint) (*dao.PaymentTransaction, error)

	// Get payment transactions by order ID
	GetPaymentsByOrderID(ctx context.Context, orderID uint) ([]*dao.PaymentTransaction, error)

	// Record COD payment
	RecordCODPayment(ctx context.Context, orderID uint, amount float64) error

	// Record COD remittance
	RecordCODRemittance(ctx context.Context, orderID uint, remittanceDate time.Time) error
}

// Request and response structures for services

// OrderFinancialsRequest contains all data needed to process an order's financials
type OrderFinancialsRequest struct {
	OrderFinancials     *dao.OrderFinancials       `json:"order_financials"`
	OrderItemFinancials []*dao.OrderItemFinancials `json:"order_item_financials"`
	OrderDiscounts      []*dao.OrderDiscount       `json:"order_discounts"`
	FreeItems           []*dao.FreeItem            `json:"free_items"`
	ShipmentFinancials  []*dao.ShipmentFinancials  `json:"shipment_financials"`
}

// CreditNoteRequest contains data for creating a credit note
type CreditNoteRequest struct {
	CreditNote      *dao.CreditNote       `json:"credit_note"`
	CreditNoteItems []*dao.CreditNoteItem `json:"credit_note_items"`
}

// DebitNoteRequest contains data for creating a debit note
type DebitNoteRequest struct {
	DebitNote      *dao.DebitNote       `json:"debit_note"`
	DebitNoteItems []*dao.DebitNoteItem `json:"debit_note_items"`
}

// SettlementGenerationRequest contains data for generating a settlement
type SettlementGenerationRequest struct {
	SellerID       uint      `json:"seller_id"`
	StartDate      time.Time `json:"start_date"`
	EndDate        time.Time `json:"end_date"`
	IncludeOrders  bool      `json:"include_orders"`  // Whether to automatically include all orders in the period
	IncludeCredits bool      `json:"include_credits"` // Whether to automatically include all credit notes in the period
	IncludeDebits  bool      `json:"include_debits"`  // Whether to automatically include all debit notes in the period
}
