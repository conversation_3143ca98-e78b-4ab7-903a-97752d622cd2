// finance/service.go
package finance

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"sync"
	"time"

	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/kcFinances"
	"kc/internal/ondc/service/kcFinances/dao"

	"gorm.io/gorm"
)

// Service implements all the financial service interfaces
type Service struct {
	db             *gorm.DB
	zoneCache      map[string]*dao.ThreePLZonePricingMapping
	zoneCacheMutex sync.RWMutex
}

// NewService creates a new financial service
func NewService(db *gorm.DB) *Service {
	// use kc_finanace database
	// db = db.Exec("USE kc_finances")
	return &Service{
		db:        db,
		zoneCache: make(map[string]*dao.ThreePLZonePricingMapping),
	}
}

// Transaction wraps the execution function in a database transaction
func (s *Service) Transaction(ctx context.Context, fn func(tx *gorm.DB) error) error {
	return s.db.WithContext(ctx).Transaction(fn)
}

// ------------------------------------------------------------------------
// OrderFinancialsService Implementation
// ------------------------------------------------------------------------

// CreateOrderFinancials creates a new order financial record
func (s *Service) CreateOrderFinancials(ctx context.Context, orderFinancials *dao.OrderFinancials) error {
	result := s.db.WithContext(ctx).Create(orderFinancials)
	if result.Error != nil {
		return fmt.Errorf("failed to create order financials: %w", result.Error)
	}
	return nil
}

// CreateOrderItemFinancials creates order items financial records
func (s *Service) CreateOrderItemFinancials(ctx context.Context, orderItemFinancials []*dao.OrderItemFinancials) error {
	if len(orderItemFinancials) == 0 {
		return errors.New("empty order item financials")
	}

	result := s.db.WithContext(ctx).Create(&orderItemFinancials)
	if result.Error != nil {
		return fmt.Errorf("failed to create order item financials: %w", result.Error)
	}
	return nil
}

// CreateOrderDiscounts creates order discounts
func (s *Service) CreateOrderDiscounts(ctx context.Context, orderDiscounts []*dao.OrderDiscount) error {
	if len(orderDiscounts) == 0 {
		return nil // No discounts to create
	}

	result := s.db.WithContext(ctx).Create(&orderDiscounts)
	if result.Error != nil {
		return fmt.Errorf("failed to create order discounts: %w", result.Error)
	}
	return nil
}

// CreateFreeItems creates free items
func (s *Service) CreateFreeItems(ctx context.Context, freeItems []*dao.FreeItem) error {
	if len(freeItems) == 0 {
		return nil // No free items to create
	}

	result := s.db.WithContext(ctx).Create(&freeItems)
	if result.Error != nil {
		return fmt.Errorf("failed to create free items: %w", result.Error)
	}
	return nil
}

// ProcessOrderFinancials handles the complete process of creating all financial records for an order
func (s *Service) ProcessOrderFinancials(ctx context.Context, order *OrderFinancialsRequest) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// Create order financials
		if order.OrderFinancials.OrderID == 0 {
			return errors.New("order financials is required")
		}
		if err := tx.Create(order.OrderFinancials).Error; err != nil {
			return fmt.Errorf("failed to create order financials: %w", err)
		}

		// Create order item financials
		if len(order.OrderItemFinancials) == 0 {
			return errors.New("order item financials are required")
		}
		if err := tx.Create(&order.OrderItemFinancials).Error; err != nil {
			return fmt.Errorf("failed to create order item financials: %w", err)
		}

		// Create order discounts (if any)
		if len(order.OrderDiscounts) > 0 {
			if err := tx.Create(&order.OrderDiscounts).Error; err != nil {
				return fmt.Errorf("failed to create order discounts: %w", err)
			}
		}

		// Create free items (if any)
		if len(order.FreeItems) > 0 {
			if err := tx.Create(&order.FreeItems).Error; err != nil {
				return fmt.Errorf("failed to create free items: %w", err)
			}
		}

		// Create shipment financials (if any)
		if len(order.ShipmentFinancials) > 0 {
			if err := tx.Create(&order.ShipmentFinancials).Error; err != nil {
				return fmt.Errorf("failed to create shipment financials: %w", err)
			}
		}

		return nil
	})
}

// GetOrderFinancialsByOrderID retrieves order financials by order ID
func (s *Service) GetOrderFinancialsByOrderID(ctx context.Context, orderID uint) (*dao.OrderFinancials, error) {
	var orderFinancials dao.OrderFinancials

	result := s.db.WithContext(ctx).Where("order_id = ?", orderID).First(&orderFinancials)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("order financials not found for order ID %d", orderID)
		}
		return nil, fmt.Errorf("failed to get order financials: %w", result.Error)
	}

	return &orderFinancials, nil
}

// GetOrderItemFinancialsByOrderID retrieves order item financials by order ID
func (s *Service) GetOrderItemFinancialsByOrderID(ctx context.Context, orderID uint) ([]*dao.OrderItemFinancials, error) {
	var orderItemFinancials []*dao.OrderItemFinancials

	result := s.db.WithContext(ctx).Where("order_id = ?", orderID).Find(&orderItemFinancials)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get order item financials: %w", result.Error)
	}

	return orderItemFinancials, nil
}

// UpdateOrderFinancials updates an existing order financials record
func (s *Service) UpdateOrderFinancials(ctx context.Context, orderFinancials *dao.OrderFinancials) error {
	if orderFinancials.ID == 0 {
		return errors.New("order financial ID is required for update")
	}

	result := s.db.WithContext(ctx).Save(orderFinancials)
	if result.Error != nil {
		return fmt.Errorf("failed to update order financials: %w", result.Error)
	}

	// Create audit log for the update
	// This would call the audit log service in a real application

	return nil
}

// CalculateNetPayableToSupplier calculates the net amount payable to the supplier for an order
func (s *Service) CalculateNetPayableToSupplier(ctx context.Context, orderID uint) (float64, error) {
	var netPayable float64

	err := s.Transaction(ctx, func(tx *gorm.DB) error {
		// Get order financials
		var orderFinancials dao.OrderFinancials
		if err := tx.Where("order_id = ?", orderID).First(&orderFinancials).Error; err != nil {
			return fmt.Errorf("failed to get order financials: %w", err)
		}

		// Get KC discounts (platform discounts)
		var kcDiscountTotal float64
		if err := tx.Model(&dao.OrderDiscount{}).
			Select("COALESCE(SUM(discount_amount), 0)").
			Where("order_id = ? AND discount_provider = 'KC'", orderID).
			Scan(&kcDiscountTotal).Error; err != nil {
			return fmt.Errorf("failed to get KC discounts: %w", err)
		}

		// Get seller discounts
		var sellerDiscountTotal float64
		if err := tx.Model(&dao.OrderDiscount{}).
			Select("COALESCE(SUM(discount_amount), 0)").
			Where("order_id = ? AND discount_provider = 'SELLER'", orderID).
			Scan(&sellerDiscountTotal).Error; err != nil {
			return fmt.Errorf("failed to get seller discounts: %w", err)
		}

		// Get shipment charges
		var shipmentChargesTotal float64
		if err := tx.Model(&dao.ShipmentFinancials{}).
			Select("COALESCE(SUM(courier_charges_billed), 0)").
			Where("order_id = ?", orderID).
			Scan(&shipmentChargesTotal).Error; err != nil {
			return fmt.Errorf("failed to get shipment charges: %w", err)
		}

		// Get RTO charges
		var rtoChargesTotal float64
		if err := tx.Model(&dao.ShipmentFinancials{}).
			Select("COALESCE(SUM(rto_charges), 0)").
			Where("order_id = ? AND is_rto = true", orderID).
			Scan(&rtoChargesTotal).Error; err != nil {
			return fmt.Errorf("failed to get RTO charges: %w", err)
		}

		// Calculate the net payable amount:
		// = order_total - commission - oms_charge - delivery_charges - tds - tcs - kc_discounts + seller_discounts
		netPayable = orderFinancials.OrderTotal - *orderFinancials.CommissionAmount - *orderFinancials.SaaSCharge - shipmentChargesTotal - orderFinancials.TDSAmount

		// Subtract TCS if applicable
		if orderFinancials.TCSApplicable && orderFinancials.TCSAmount != 0 {
			netPayable -= orderFinancials.TCSAmount
		}

		// KC discounts are borne by KC, not deducted from seller payment
		// Seller discounts are borne by seller, so they reduce the payable amount
		netPayable -= sellerDiscountTotal

		// Subtract RTO charges
		netPayable -= rtoChargesTotal

		return nil
	})

	if err != nil {
		return 0, err
	}

	return netPayable, nil
}

// ------------------------------------------------------------------------
// ShipmentFinancialsService Implementation
// ------------------------------------------------------------------------

// CreateShipmentFinancials creates a new shipment financial record
func (s *Service) CreateShipmentFinancials(ctx context.Context, shipmentFinancials *dao.ShipmentFinancials) error {
	result := s.db.WithContext(ctx).Create(shipmentFinancials)
	if result.Error != nil {
		return fmt.Errorf("failed to create shipment financials: %w", result.Error)
	}
	return nil
}

// UpdateShipmentFinancials updates an existing shipment financial record
func (s *Service) UpdateShipmentFinancials(ctx context.Context, shipmentFinancials *dao.ShipmentFinancials) error {
	if shipmentFinancials.ID == 0 {
		return errors.New("shipment financial ID is required for update")
	}

	result := s.db.WithContext(ctx).Save(shipmentFinancials)
	if result.Error != nil {
		return fmt.Errorf("failed to update shipment financials: %w", result.Error)
	}
	return nil
}

// GetShipmentFinancialsByOrderID retrieves shipment financials by order ID
func (s *Service) GetShipmentFinancialsByOrderID(ctx context.Context, orderID uint) ([]*dao.ShipmentFinancials, error) {
	var shipmentFinancials []*dao.ShipmentFinancials

	result := s.db.WithContext(ctx).Where("order_id = ?", orderID).Find(&shipmentFinancials)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get shipment financials: %w", result.Error)
	}

	return shipmentFinancials, nil
}

// GetShipmentFinancialsByShipmentID retrieves shipment financials by shipment ID
func (s *Service) GetShipmentFinancialsByShipmentID(ctx context.Context, shipmentID uint) (*dao.ShipmentFinancials, error) {
	var shipmentFinancials dao.ShipmentFinancials

	result := s.db.WithContext(ctx).Where("shipment_id = ?", shipmentID).First(&shipmentFinancials)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("shipment financials not found for shipment ID %d", shipmentID)
		}
		return nil, fmt.Errorf("failed to get shipment financials: %w", result.Error)
	}

	return &shipmentFinancials, nil
}

// ProcessRTO handles the financial aspects of a Return To Origin
func (s *Service) ProcessRTO(ctx context.Context, shipmentID uint, rtoReason string, rtoCharges float64) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// Get existing shipment financials
		var shipmentFinancials dao.ShipmentFinancials
		if err := tx.Where("shipment_id = ?", shipmentID).First(&shipmentFinancials).Error; err != nil {
			return fmt.Errorf("failed to get shipment financials: %w", err)
		}

		// Update RTO fields
		shipmentFinancials.IsRTO = true
		shipmentFinancials.RTOReason = rtoReason
		shipmentFinancials.RTOCharges = rtoCharges

		// Save changes
		if err := tx.Save(&shipmentFinancials).Error; err != nil {
			return fmt.Errorf("failed to update shipment financials with RTO: %w", err)
		}

		// Update order financials if needed
		// This might update the net payable amount or add additional charges
		// Depending on your business rules

		return nil
	})
}

// ------------------------------------------------------------------------
// ReturnsService Implementation
// ------------------------------------------------------------------------

// CreateReturn creates a new return/refund record
func (s *Service) CreateReturn(ctx context.Context, returnRefund *dao.ReturnsRefunds) error {
	result := s.db.WithContext(ctx).Create(returnRefund)
	if result.Error != nil {
		return fmt.Errorf("failed to create return: %w", result.Error)
	}
	return nil
}

// UpdateReturn updates an existing return/refund record
func (s *Service) UpdateReturn(ctx context.Context, returnRefund *dao.ReturnsRefunds) error {
	if returnRefund.ID == 0 {
		return errors.New("return ID is required for update")
	}

	result := s.db.WithContext(ctx).Save(returnRefund)
	if result.Error != nil {
		return fmt.Errorf("failed to update return: %w", result.Error)
	}
	return nil
}

// ConfirmReturn updates a return with confirmation details
func (s *Service) ConfirmReturn(ctx context.Context, orderID uint, sellerConfirmed bool) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// Get existing return
		var returnRefund dao.ReturnsRefunds
		if err := tx.Where("order_id = ?", orderID).First(&returnRefund).Error; err != nil {
			return fmt.Errorf("failed to get return: %w", err)
		}

		// Update confirmation details
		returnRefund.SellerConfirmationReceived = sellerConfirmed
		returnRefund.SellerConfirmationDate = time.Time{}
		now := time.Now()
		returnRefund.SellerConfirmationDate = now
		returnRefund.ReturnConfirmedDate = now

		// Save changes
		if err := tx.Save(&returnRefund).Error; err != nil {
			return fmt.Errorf("failed to update return with confirmation: %w", err)
		}

		return nil
	})
}

// GetReturnsByOrderID retrieves all returns for an order
func (s *Service) GetReturnsByOrderID(ctx context.Context, orderID uint) ([]*dao.ReturnsRefunds, error) {
	var returns []*dao.ReturnsRefunds

	result := s.db.WithContext(ctx).Where("order_id = ?", orderID).Find(&returns)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get returns: %w", result.Error)
	}

	return returns, nil
}

// ------------------------------------------------------------------------
// CreditNoteService Implementation
// ------------------------------------------------------------------------

// CreateCreditNote creates a new credit note with items
func (s *Service) CreateCreditNote(ctx context.Context, creditNote *dao.CreditNote, items []*dao.CreditNoteItem) (*dao.CreditNote, error) {
	err := s.Transaction(ctx, func(tx *gorm.DB) error {
		// Create credit note
		if err := tx.Create(creditNote).Error; err != nil {
			return fmt.Errorf("failed to create credit note: %w", err)
		}

		// Set credit note ID for all items
		for i := range items {
			items[i].CreditNoteID = creditNote.CreditNoteID
		}

		// Create credit note items
		if len(items) > 0 {
			if err := tx.Create(&items).Error; err != nil {
				return fmt.Errorf("failed to create credit note items: %w", err)
			}
		}

		// If this credit note is for a return, update the return record
		if creditNote.OrderID != 0 {
			if err := tx.Model(&dao.ReturnsRefunds{}).
				Where("order_id = ?", creditNote.OrderID).
				Update("credit_note_id", creditNote.CreditNoteID).Error; err != nil {
				return fmt.Errorf("failed to update return with credit note ID: %w", err)
			}
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	return creditNote, nil
}

// GetCreditNoteByID retrieves a credit note by ID
func (s *Service) GetCreditNoteByID(ctx context.Context, creditNoteID uint) (*dao.CreditNote, error) {
	var creditNote dao.CreditNote

	result := s.db.WithContext(ctx).
		Preload("CreditNoteItems").
		Where("credit_note_id = ?", creditNoteID).
		First(&creditNote)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("credit note not found for ID %d", creditNoteID)
		}
		return nil, fmt.Errorf("failed to get credit note: %w", result.Error)
	}

	return &creditNote, nil
}

// GetCreditNotesByOrderID retrieves credit notes by order ID
func (s *Service) GetCreditNotesByOrderID(ctx context.Context, orderID uint) ([]*dao.CreditNote, error) {
	var creditNotes []*dao.CreditNote

	result := s.db.WithContext(ctx).
		Preload("CreditNoteItems").
		Where("order_id = ?", orderID).
		Find(&creditNotes)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get credit notes: %w", result.Error)
	}

	return creditNotes, nil
}

// GetCreditNotesBySellerAndPeriod retrieves credit notes by seller ID and date range
func (s *Service) GetCreditNotesBySellerAndPeriod(ctx context.Context, sellerID uint, startDate, endDate time.Time) ([]*dao.CreditNote, error) {
	var creditNotes []*dao.CreditNote

	result := s.db.WithContext(ctx).
		Preload("CreditNoteItems").
		Where("seller_id = ? AND credit_note_date BETWEEN ? AND ?", sellerID, startDate, endDate).
		Find(&creditNotes)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get credit notes: %w", result.Error)
	}

	return creditNotes, nil
}

// CreateCreditNoteForReturn creates a credit note for a return (continued)
func (s *Service) CreateCreditNoteForReturn(ctx context.Context, orderID uint, reason string) (*dao.CreditNote, error) {
	var creditNote *dao.CreditNote

	err := s.Transaction(ctx, func(tx *gorm.DB) error {
		// Get the return details
		var returnRefund []dao.ReturnsRefunds
		if err := tx.Where("order_id = ?", orderID).Find(&returnRefund).Error; err != nil {
			return fmt.Errorf("failed to get return: %w", err)
		}

		// Ensure this return doesn't already have a credit note
		if len(returnRefund) == 0 {
			return fmt.Errorf("not return found for order ID %d", orderID)
		}

		skuReturnedQuantity := make(map[string]int)
		orderItemIDs := make([]uint, 0, len(returnRefund))
		for _, item := range returnRefund {
			skuReturnedQuantity[fmt.Sprintf("%d-%d", item.OrderID, item.OrderItemID)] = item.Quantity
			orderItemIDs = append(orderItemIDs, item.OrderItemID)
		}

		// Get the order financials
		var orderFinancials dao.OrderFinancials
		if err := tx.Where("order_id = ?", orderID).Find(&orderFinancials).Error; err != nil {
			return fmt.Errorf("failed to get order financials: %w", err)
		}

		// Get the order item if this is an item-level return
		var orderItemFinancial []dao.OrderItemFinancials
		if err := tx.Where("order_id = ? and order_item_id in (?)", orderID, orderItemIDs).Find(&orderItemFinancial).Error; err != nil {
			return fmt.Errorf("failed to get order item financials: %w", err)
		}

		// Create the credit note
		now := time.Now()

		// need to add credit number logic
		creditNoteNumber, err := s.GetCreditNoteNumber(uint64(orderID), orderFinancials.InvoiceNumber)
		if err != nil {
			return fmt.Errorf("failed to get credit note number: %w", err)
		}

		newCreditNote := &dao.CreditNote{
			CreditNoteNumber:     creditNoteNumber,
			CreditNoteDate:       now,
			RelatedInvoiceNumber: orderFinancials.InvoiceNumber,
			RelatedInvoiceDate:   orderFinancials.InvoiceDate,
			SellerID:             uint(orderFinancials.SupplierID),
			BuyerID:              orderFinancials.RetailerID,
			OrderID:              orderID,
			CreditNoteReason:     reason,
			CreditNoteType:       "RETURN",
			AdjustmentStatus:     "PENDING",
			CreatedAt:            now,
			UpdatedAt:            now,
		}
		for _, itemFinancial := range orderItemFinancial {
			// Item-level credit note
			skuValueBeforeTax := (itemFinancial.ItemTotal - itemFinancial.TotalTax) / float64(itemFinancial.Quantity)
			skuCGSTAmount := itemFinancial.CGSTAmount / float64(itemFinancial.Quantity)
			skuSGSTAmount := itemFinancial.SGSTAmount / float64(itemFinancial.Quantity)
			skuIGSTAmount := itemFinancial.IGSTAmount / float64(itemFinancial.Quantity)

			newCreditNote.CGSTAmount += skuCGSTAmount * float64(skuReturnedQuantity[fmt.Sprintf("%d-%d", itemFinancial.OrderID, itemFinancial.OrderItemID)])
			newCreditNote.SGSTAmount += skuSGSTAmount * float64(skuReturnedQuantity[fmt.Sprintf("%d-%d", itemFinancial.OrderID, itemFinancial.OrderItemID)])
			newCreditNote.IGSTAmount += skuIGSTAmount * float64(skuReturnedQuantity[fmt.Sprintf("%d-%d", itemFinancial.OrderID, itemFinancial.OrderItemID)])
			newCreditNote.TotalValueBeforeTax += skuValueBeforeTax * float64(skuReturnedQuantity[fmt.Sprintf("%d-%d", itemFinancial.OrderID, itemFinancial.OrderItemID)])
			newCreditNote.TotalCreditNoteValue += (skuValueBeforeTax + skuCGSTAmount + skuSGSTAmount + skuIGSTAmount) * float64(skuReturnedQuantity[fmt.Sprintf("%d-%d", itemFinancial.OrderID, itemFinancial.OrderItemID)])

		}

		// Create the credit note
		if err := tx.Create(newCreditNote).Error; err != nil {
			return fmt.Errorf("failed to create credit note: %w", err)
		}

		// Create credit note items
		var creditNoteItems []*dao.CreditNoteItem

		for _, itemFinancial := range orderItemFinancial {
			// Item-level credit note
			returnedSkuQuantity := float64(skuReturnedQuantity[fmt.Sprintf("%d-%d", itemFinancial.OrderID, itemFinancial.OrderItemID)])
			skuCgstAmount := itemFinancial.CGSTAmount / float64(itemFinancial.Quantity)
			skuSgstAmount := itemFinancial.SGSTAmount / float64(itemFinancial.Quantity)
			skuIgstAmount := itemFinancial.IGSTAmount / float64(itemFinancial.Quantity)
			skuValueBeforeTax := (itemFinancial.ItemTotal - itemFinancial.TotalTax) / float64(itemFinancial.Quantity)
			item := &dao.CreditNoteItem{
				CreditNoteID: newCreditNote.CreditNoteID,
				OrderItemID:  uint(itemFinancial.OrderItemID),
				ProductID:    itemFinancial.ProductID,
				SKUID:        itemFinancial.SKUID,
				HSNCode:      itemFinancial.HSNCode,
				Quantity:     int(returnedSkuQuantity),
				UnitPrice:    (itemFinancial.ItemTotal - itemFinancial.TotalTax) / float64(itemFinancial.Quantity),
				TotalValue:   (itemFinancial.ItemTotal - itemFinancial.TotalTax) / float64(itemFinancial.Quantity) * float64(returnedSkuQuantity),
				GSTRate:      itemFinancial.GSTRate,
				CGSTAmount:   skuCgstAmount * returnedSkuQuantity,
				SGSTAmount:   skuSgstAmount * returnedSkuQuantity,
				IGSTAmount:   skuIgstAmount * returnedSkuQuantity,
				TotalTax:     (skuCgstAmount + skuSgstAmount + skuIgstAmount) * returnedSkuQuantity,
				ItemTotal:    (skuValueBeforeTax + skuCgstAmount + skuSgstAmount + skuIgstAmount) * returnedSkuQuantity,
				Reason:       reason,
				CreatedAt:    now,
			}
			creditNoteItems = append(creditNoteItems, item)
		}

		// Create the credit note items
		if err := tx.Create(&creditNoteItems).Error; err != nil {
			return fmt.Errorf("failed to create credit note items: %w", err)
		}

		// Update the return with the credit note ID
		if err := tx.Model(&returnRefund).Update("credit_note_id", newCreditNote.CreditNoteID).Error; err != nil {
			return fmt.Errorf("failed to update return with credit note ID: %w", err)
		}

		creditNote = newCreditNote
		return nil
	})

	if err != nil {
		return nil, err
	}

	return creditNote, nil
}

func (s *Service) GetCreditNoteNumber(orderID uint64, invoiceNumber string) (string, error) {
	// Validate input
	if len(invoiceNumber) < 3 {
		return "", errors.New("invalid invoice number format")
	}

	// Use transaction to prevent race conditions
	tx := s.db.Begin()
	defer tx.Rollback()

	var totalCreditNotes int
	query := `SELECT COUNT(*) FROM credit_notes WHERE order_id = ? FOR UPDATE`
	if err := tx.Raw(query, orderID).Scan(&totalCreditNotes).Error; err != nil {
		return "", err
	}

	creditNoteNumber := invoiceNumber[2:len(invoiceNumber)-1] + "-" + strconv.Itoa(totalCreditNotes+1)

	tx.Commit()
	return creditNoteNumber, nil
}

// ApplyCreditNoteToSettlement applies a credit note to a settlement
func (s *Service) ApplyCreditNoteToSettlement(ctx context.Context, creditNoteID, settlementID uint) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// Get the credit note
		var creditNote dao.CreditNote
		if err := tx.Where("credit_note_id = ?", creditNoteID).First(&creditNote).Error; err != nil {
			return fmt.Errorf("failed to get credit note: %w", err)
		}

		// Check if already adjusted
		if creditNote.AdjustmentStatus == "ADJUSTED" {
			return fmt.Errorf("credit note ID %d is already adjusted", creditNoteID)
		}

		// Get the settlement
		var settlement dao.SellerSettlement
		if err := tx.Where("settlement_id = ?", settlementID).First(&settlement).Error; err != nil {
			return fmt.Errorf("failed to get settlement: %w", err)
		}

		// Create settlement note entry
		settlementNote := &dao.SettlementNote{
			SettlementID: settlementID,
			CreditNoteID: creditNoteID,
			NoteType:     "CREDIT",
			NoteNumber:   creditNote.CreditNoteNumber,
			NoteDate:     creditNote.CreditNoteDate,
			NoteAmount:   creditNote.TotalCreditNoteValue,
			CreatedAt:    time.Now(),
		}

		if err := tx.Create(settlementNote).Error; err != nil {
			return fmt.Errorf("failed to create settlement note: %w", err)
		}

		// Update settlement totals
		if err := tx.Model(&settlement).
			Update("total_credit_notes", gorm.Expr("total_credit_notes + ?", creditNote.TotalCreditNoteValue)).
			Update("net_payable_to_supplier", gorm.Expr("net_payable_to_supplier - ?", creditNote.TotalCreditNoteValue)).
			Error; err != nil {
			return fmt.Errorf("failed to update settlement amounts: %w", err)
		}

		// Update credit note status
		now := time.Now()
		if err := tx.Model(&creditNote).
			Updates(map[string]interface{}{
				"adjustment_status":         "ADJUSTED",
				"adjustment_date":           now,
				"adjusted_in_settlement_id": settlementID,
				"updated_at":                now,
			}).Error; err != nil {
			return fmt.Errorf("failed to update credit note status: %w", err)
		}

		return nil
	})
}

// ------------------------------------------------------------------------
// DebitNoteService Implementation
// ------------------------------------------------------------------------

// CreateDebitNote creates a new debit note with items
func (s *Service) CreateDebitNote(ctx context.Context, debitNote *dao.DebitNote, items []*dao.DebitNoteItem) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// Create debit note
		if err := tx.Create(debitNote).Error; err != nil {
			return fmt.Errorf("failed to create debit note: %w", err)
		}

		// Set debit note ID for all items
		for i := range items {
			items[i].DebitNoteID = debitNote.DebitNoteID
		}

		// Create debit note items
		if len(items) > 0 {
			if err := tx.Create(&items).Error; err != nil {
				return fmt.Errorf("failed to create debit note items: %w", err)
			}
		}

		return nil
	})
}

// GetDebitNoteByID retrieves a debit note by ID
func (s *Service) GetDebitNoteByID(ctx context.Context, debitNoteID uint) (*dao.DebitNote, error) {
	var debitNote dao.DebitNote

	result := s.db.WithContext(ctx).
		Preload("DebitNoteItems").
		Where("debit_note_id = ?", debitNoteID).
		First(&debitNote)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("debit note not found for ID %d", debitNoteID)
		}
		return nil, fmt.Errorf("failed to get debit note: %w", result.Error)
	}

	return &debitNote, nil
}

// GetDebitNotesByOrderID retrieves debit notes by order ID
func (s *Service) GetDebitNotesByOrderID(ctx context.Context, orderID uint) ([]*dao.DebitNote, error) {
	var debitNotes []*dao.DebitNote

	result := s.db.WithContext(ctx).
		Preload("DebitNoteItems").
		Where("order_id = ?", orderID).
		Find(&debitNotes)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get debit notes: %w", result.Error)
	}

	return debitNotes, nil
}

// GetDebitNotesBySellerAndPeriod retrieves debit notes by seller ID and date range
func (s *Service) GetDebitNotesBySellerAndPeriod(ctx context.Context, sellerID uint, startDate, endDate time.Time) ([]*dao.DebitNote, error) {
	var debitNotes []*dao.DebitNote

	result := s.db.WithContext(ctx).
		Preload("DebitNoteItems").
		Where("seller_id = ? AND debit_note_date BETWEEN ? AND ?", sellerID, startDate, endDate).
		Find(&debitNotes)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get debit notes: %w", result.Error)
	}

	return debitNotes, nil
}

// ApplyDebitNoteToSettlement applies a debit note to a settlement
func (s *Service) ApplyDebitNoteToSettlement(ctx context.Context, debitNoteID, settlementID uint) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// Get the debit note
		var debitNote dao.DebitNote
		if err := tx.Where("debit_note_id = ?", debitNoteID).First(&debitNote).Error; err != nil {
			return fmt.Errorf("failed to get debit note: %w", err)
		}

		// Check if already adjusted
		if debitNote.AdjustmentStatus == "ADJUSTED" {
			return fmt.Errorf("debit note ID %d is already adjusted", debitNoteID)
		}

		// Get the settlement
		var settlement dao.SellerSettlement
		if err := tx.Where("settlement_id = ?", settlementID).First(&settlement).Error; err != nil {
			return fmt.Errorf("failed to get settlement: %w", err)
		}

		// Create settlement note entry
		settlementNote := &dao.SettlementNote{
			SettlementID: settlementID,
			DebitNoteID:  debitNoteID,
			NoteType:     "DEBIT",
			NoteNumber:   debitNote.DebitNoteNumber,
			NoteDate:     debitNote.DebitNoteDate,
			NoteAmount:   debitNote.TotalDebitNoteValue,
			CreatedAt:    time.Now(),
		}

		if err := tx.Create(settlementNote).Error; err != nil {
			return fmt.Errorf("failed to create settlement note: %w", err)
		}

		// Update settlement totals
		if err := tx.Model(&settlement).
			Update("total_debit_notes", gorm.Expr("total_debit_notes + ?", debitNote.TotalDebitNoteValue)).
			Update("net_payable_to_supplier", gorm.Expr("net_payable_to_supplier + ?", debitNote.TotalDebitNoteValue)).
			Error; err != nil {
			return fmt.Errorf("failed to update settlement amounts: %w", err)
		}

		// Update debit note status
		now := time.Now()
		if err := tx.Model(&debitNote).
			Updates(map[string]interface{}{
				"adjustment_status":         "ADJUSTED",
				"adjustment_date":           now,
				"adjusted_in_settlement_id": settlementID,
				"updated_at":                now,
			}).Error; err != nil {
			return fmt.Errorf("failed to update debit note status: %w", err)
		}

		return nil
	})
}

// ------------------------------------------------------------------------
// SettlementService Implementation
// ------------------------------------------------------------------------

// GenerateSettlement creates a new settlement for a seller for a specific period
func (s *Service) GenerateSettlement(ctx context.Context, sellerID uint, startDate, endDate time.Time) (*dao.SellerSettlement, error) {
	var settlement *dao.SellerSettlement

	err := s.Transaction(ctx, func(tx *gorm.DB) error {
		// Check if a settlement already exists for this period
		var existingCount int64
		if err := tx.Model(&dao.SellerSettlement{}).
			Where("seller_id = ? AND ((settlement_period_start <= ? AND settlement_period_end >= ?) OR (settlement_period_start <= ? AND settlement_period_end >= ?) OR (settlement_period_start >= ? AND settlement_period_end <= ?))",
				sellerID, startDate, startDate, endDate, endDate, startDate, endDate).
			Count(&existingCount).Error; err != nil {
			return fmt.Errorf("failed to check existing settlements: %w", err)
		}

		if existingCount > 0 {
			return fmt.Errorf("a settlement already exists for seller %d that overlaps with the period %s to %s",
				sellerID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
		}

		// Generate invoice number
		now := time.Now()
		invoiceNumber := fmt.Sprintf("INV-KC-%d-%s", sellerID, now.Format("20060102-150405"))

		// Create new settlement with initial values
		newSettlement := &dao.SellerSettlement{
			SellerID:              sellerID,
			SettlementPeriodStart: startDate,
			SettlementPeriodEnd:   endDate,
			InvoiceNumber:         invoiceNumber,
			InvoiceDate:           now,
			PaymentStatus:         "PENDING",
			CreatedAt:             now,
			UpdatedAt:             now,
		}

		// Create the settlement record
		if err := tx.Create(newSettlement).Error; err != nil {
			return fmt.Errorf("failed to create settlement: %w", err)
		}

		// Calculate settlement totals (initial values)
		// This will be updated as orders, credit notes, and debit notes are added

		settlement = newSettlement
		return nil
	})

	if err != nil {
		return nil, err
	}

	// Optionally, you could automatically add orders, credit notes, and debit notes here
	// based on the startDate and endDate

	return settlement, nil
}

// GetSettlementByID retrieves a settlement by ID
func (s *Service) GetSettlementByID(ctx context.Context, settlementID uint) (*dao.SellerSettlement, error) {
	var settlement dao.SellerSettlement

	result := s.db.WithContext(ctx).
		Preload("SettlementOrders").
		Preload("SettlementNotes").
		Where("settlement_id = ?", settlementID).
		First(&settlement)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("settlement not found for ID %d", settlementID)
		}
		return nil, fmt.Errorf("failed to get settlement: %w", result.Error)
	}

	return &settlement, nil
}

// GetSettlementsBySellerID retrieves settlements by seller ID
func (s *Service) GetSettlementsBySellerID(ctx context.Context, sellerID uint) ([]*dao.SellerSettlement, error) {
	var settlements []*dao.SellerSettlement

	result := s.db.WithContext(ctx).
		Where("seller_id = ?", sellerID).
		Order("settlement_period_start DESC").
		Find(&settlements)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get settlements: %w", result.Error)
	}

	return settlements, nil
}

// AddOrderToSettlement adds an order to a settlement
func (s *Service) AddOrderToSettlement(ctx context.Context, settlementID, orderID uint) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// Check if order already exists in this or another settlement
		var existingCount int64
		if err := tx.Model(&dao.SettlementOrder{}).
			Where("order_id = ?", orderID).
			Count(&existingCount).Error; err != nil {
			return fmt.Errorf("failed to check existing settlement orders: %w", err)
		}

		if existingCount > 0 {
			return fmt.Errorf("order ID %d is already included in a settlement", orderID)
		}

		// Get the settlement
		var settlement dao.SellerSettlement
		if err := tx.Where("settlement_id = ?", settlementID).First(&settlement).Error; err != nil {
			return fmt.Errorf("failed to get settlement: %w", err)
		}

		// Get order financials
		var orderFinancials dao.OrderFinancials
		if err := tx.Where("order_id = ?", orderID).First(&orderFinancials).Error; err != nil {
			return fmt.Errorf("failed to get order financials: %w", err)
		}

		// Verify seller matches
		if uint(orderFinancials.SupplierID) != settlement.SellerID {
			return fmt.Errorf("order ID %d seller (%d) does not match settlement seller (%d)",
				orderID, orderFinancials.SupplierID, settlement.SellerID)
		}

		// Get all shipment financials for this order
		var shipmentFinancials []*dao.ShipmentFinancials
		if err := tx.Where("order_id = ?", orderID).Find(&shipmentFinancials).Error; err != nil {
			return fmt.Errorf("failed to get shipment financials: %w", err)
		}

		// Calculate shipment charges
		var deliveryCharge float64
		var deliveryGST float64
		var rtoCharge float64

		for _, sf := range shipmentFinancials {
			deliveryCharge += *sf.CourierChargesBilled
			deliveryGST += *sf.CourierGSTAmount
			if sf.IsRTO {
				rtoCharge += sf.RTOCharges
			}
		}

		// Get KC discounts
		var kcDiscountTotal float64
		if err := tx.Model(&dao.OrderDiscount{}).
			Select("COALESCE(SUM(discount_amount), 0)").
			Where("order_id = ? AND discount_provider = 'KC'", orderID).
			Scan(&kcDiscountTotal).Error; err != nil {
			return fmt.Errorf("failed to get KC discounts: %w", err)
		}

		// Get seller discounts
		var sellerDiscountTotal float64
		if err := tx.Model(&dao.OrderDiscount{}).
			Select("COALESCE(SUM(discount_amount), 0)").
			Where("order_id = ? AND discount_provider = 'SELLER'", orderID).
			Scan(&sellerDiscountTotal).Error; err != nil {
			return fmt.Errorf("failed to get seller discounts: %w", err)
		}

		// Calculate returns/refunds amount
		var returnsRefundsTotal float64
		if err := tx.Model(&dao.ReturnsRefunds{}).
			Select("COALESCE(SUM(return_amount), 0)").
			Where("order_id = ? AND refund_status = 'PROCESSED'", orderID).
			Scan(&returnsRefundsTotal).Error; err != nil {
			return fmt.Errorf("failed to get returns/refunds: %w", err)
		}

		// Calculate TCS
		var tcsAmount float64
		if orderFinancials.TCSApplicable && orderFinancials.TCSAmount != 0 {
			tcsAmount = orderFinancials.TCSAmount
		}

		// Calculate net payable for this order
		// = order_total - commission - oms_charge - delivery_charges - tds - tcs - seller_discounts - returns/refunds
		netPayable := orderFinancials.OrderTotal - *orderFinancials.CommissionAmount - *orderFinancials.SaaSCharge - deliveryCharge - orderFinancials.TDSAmount - tcsAmount - sellerDiscountTotal - returnsRefundsTotal

		// Create settlement order record
		settlementOrder := &dao.SettlementOrder{
			SettlementID:     settlementID,
			OrderID:          orderID,
			InvoiceNumber:    orderFinancials.InvoiceNumber,
			OrderDate:        orderFinancials.InvoiceDate,
			OrderValue:       orderFinancials.OrderTotal,
			TotalGSTAmount:   orderFinancials.TotalGSTAmount,
			CommissionAmount: *orderFinancials.CommissionAmount,
			OMSCharge:        *orderFinancials.SaaSCharge,
			DeliveryCharge:   deliveryCharge,
			DeliveryGST:      deliveryGST,
			CODCharge:        0, // Add logic if needed
			RTOCharge:        rtoCharge,
			KCDiscount:       kcDiscountTotal,
			SellerDiscount:   sellerDiscountTotal,
			ReturnsRefunds:   returnsRefundsTotal,
			TDSAmount:        orderFinancials.TDSAmount,
			TCSAmount:        tcsAmount,
			NetPayable:       netPayable,
			CreatedAt:        time.Now(),
		}

		if err := tx.Create(settlementOrder).Error; err != nil {
			return fmt.Errorf("failed to create settlement order: %w", err)
		}

		// Update settlement totals
		if err := tx.Model(&settlement).
			Updates(map[string]interface{}{
				"total_order_value":       gorm.Expr("total_order_value + ?", orderFinancials.OrderTotal),
				"total_commission":        gorm.Expr("total_commission + ?", orderFinancials.CommissionAmount),
				"total_oms_charges":       gorm.Expr("total_oms_charges + ?", orderFinancials.SaaSCharge),
				"total_delivery_charges":  gorm.Expr("total_delivery_charges + ?", deliveryCharge),
				"total_delivery_gst":      gorm.Expr("total_delivery_gst + ?", deliveryGST),
				"total_rto_charges":       gorm.Expr("total_rto_charges + ?", rtoCharge),
				"total_kc_discounts":      gorm.Expr("total_kc_discounts + ?", kcDiscountTotal),
				"total_seller_discounts":  gorm.Expr("total_seller_discounts + ?", sellerDiscountTotal),
				"total_returns_refunds":   gorm.Expr("total_returns_refunds + ?", returnsRefundsTotal),
				"total_tds_amount":        gorm.Expr("total_tds_amount + ?", orderFinancials.TDSAmount),
				"total_tcs_amount":        gorm.Expr("total_tcs_amount + ?", tcsAmount),
				"net_payable_to_supplier": gorm.Expr("net_payable_to_supplier + ?", netPayable),
				"updated_at":              time.Now(),
			}).Error; err != nil {
			return fmt.Errorf("failed to update settlement totals: %w", err)
		}

		// Update order financials with settlement info
		if err := tx.Model(&orderFinancials).
			Updates(map[string]interface{}{
				"final_settlement_to_supplier": netPayable,
				"updated_at":                   time.Now(),
			}).Error; err != nil {
			return fmt.Errorf("failed to update order financials: %w", err)
		}

		return nil
	})
}

// FinalizeSettlement calculates final settlement amounts and marks it as ready for payment
func (s *Service) FinalizeSettlement(ctx context.Context, settlementID uint) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// Get the settlement
		var settlement dao.SellerSettlement
		if err := tx.Where("settlement_id = ?", settlementID).First(&settlement).Error; err != nil {
			return fmt.Errorf("failed to get settlement: %w", err)
		}

		// Calculate service invoice amount
		serviceAmount := settlement.TotalCommission + settlement.TotalOMSCharges + settlement.TotalDeliveryCharges

		// Calculate GST on service invoice (assuming 18% GST)
		// This is simplified - in production you would get the correct GST rate for your services
		gstRate := 18.0
		gstAmount := serviceAmount * (gstRate / 100)

		// Determine if IGST or CGST+SGST applies (simplified - in production you'd check place of supply)
		// This assumes all sellers are in the same state as KC (CGST+SGST) or different state (IGST)
		var cgstAmount, sgstAmount, igstAmount float64

		// For this example, let's assume all are in different states (IGST)
		igstAmount = gstAmount

		// Update settlement with service invoice details
		if err := tx.Model(&settlement).
			Updates(map[string]interface{}{
				"service_invoice_amount": serviceAmount,
				"service_invoice_cgst":   cgstAmount,
				"service_invoice_sgst":   sgstAmount,
				"service_invoice_igst":   igstAmount,
				"service_invoice_total":  serviceAmount + cgstAmount + sgstAmount + igstAmount,
				"payment_status":         "READY_FOR_PAYMENT",
				"updated_at":             time.Now(),
			}).Error; err != nil {
			return fmt.Errorf("failed to finalize settlement: %w", err)
		}

		// Create service invoice record (you would have a separate service for this)
		// ...

		return nil
	})
}

// finance/service.go (final part)

// MarkSettlementAsPaid marks a settlement as paid
func (s *Service) MarkSettlementAsPaid(ctx context.Context, settlementID uint, paymentDate time.Time) error {
	return s.db.WithContext(ctx).
		Model(&dao.SellerSettlement{}).
		Where("settlement_id = ?", settlementID).
		Updates(map[string]interface{}{
			"payment_status": "PAID",
			"payment_date":   paymentDate,
			"updated_at":     time.Now(),
		}).Error
}

// ------------------------------------------------------------------------
// AuditLogService Implementation
// ------------------------------------------------------------------------

// CreateAuditLog creates an audit log entry for financial data changes
func (s *Service) CreateAuditLog(ctx context.Context, tableName string, recordID uint, actionType string, fieldName string, oldValue, newValue string, changedBy string, reason string) error {
	auditLog := &dao.FinancialAuditLog{
		TableName:  tableName,
		RecordID:   recordID,
		ActionType: actionType,
		FieldName:  fieldName,
		OldValue:   oldValue,
		NewValue:   newValue,
		ChangedBy:  changedBy,
		ChangeDate: time.Now(),
		Reason:     reason,
	}

	result := s.db.WithContext(ctx).Create(auditLog)
	if result.Error != nil {
		return fmt.Errorf("failed to create audit log: %w", result.Error)
	}
	return nil
}

// GetAuditLogsByRecord retrieves audit logs for a specific record
func (s *Service) GetAuditLogsByRecord(ctx context.Context, tableName string, recordID uint) ([]*dao.FinancialAuditLog, error) {
	var logs []*dao.FinancialAuditLog

	result := s.db.WithContext(ctx).
		Where("table_name = ? AND record_id = ?", tableName, recordID).
		Order("change_date DESC").
		Find(&logs)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get audit logs: %w", result.Error)
	}

	return logs, nil
}

// ------------------------------------------------------------------------
// Utility Methods
// ------------------------------------------------------------------------

// CalculateGSTComponents calculates CGST, SGST, and IGST based on place of supply
func CalculateGSTComponents(taxableAmount float64, gstRate float64, buyerState, sellerState string) (cgst, sgst, igst float64) {
	totalTax := taxableAmount * (gstRate / 100)

	if buyerState == sellerState {
		// Intra-state: CGST + SGST
		cgst = totalTax / 2
		sgst = totalTax / 2
		igst = 0
	} else {
		// Inter-state: IGST
		cgst = 0
		sgst = 0
		igst = totalTax
	}

	return
}

// GetHSNCodeTaxRate retrieves the applicable GST rate for an HSN code
func (s *Service) GetHSNCodeTaxRate(ctx context.Context, hsnCode string, effectiveDate time.Time) (float64, error) {
	var hsnTax dao.HSNTaxRate

	result := s.db.WithContext(ctx).
		Where("hsn_code = ? AND effective_from <= ? AND (effective_to IS NULL OR effective_to >= ?)",
			hsnCode, effectiveDate, effectiveDate).
		Order("effective_from DESC").
		First(&hsnTax)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return 0, fmt.Errorf("no tax rate found for HSN code %s at date %s", hsnCode, effectiveDate.Format("2006-01-02"))
		}
		return 0, fmt.Errorf("failed to get HSN tax rate: %w", result.Error)
	}

	return hsnTax.GSTRate, nil
}

// GenerateInvoiceNumber generates a unique invoice number
func GenerateInvoiceNumber(prefix string, entityID uint) string {
	now := time.Now()
	return fmt.Sprintf("%s-%d-%s", prefix, entityID, now.Format("20060102-150405"))
}

// Implementation of PaymentService
func (s *Service) CreatePaymentTransaction(ctx context.Context, payment *dao.PaymentTransaction) error {
	result := s.db.WithContext(ctx).Create(payment)
	if result.Error != nil {
		return fmt.Errorf("failed to create payment transaction: %w", result.Error)
	}

	// Update order financials with payment information
	err := s.updateOrderFinancialsWithPayment(ctx, payment)
	if err != nil {
		return fmt.Errorf("failed to update order financials with payment: %w", err)
	}

	return nil
}

func (s *Service) UpdatePaymentStatus(ctx context.Context, paymentID uint, status string) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// Get existing payment
		var payment dao.PaymentTransaction
		if err := tx.Where("payment_id = ?", paymentID).First(&payment).Error; err != nil {
			return fmt.Errorf("failed to get payment: %w", err)
		}

		// Update status
		payment.PaymentStatus = status
		payment.UpdatedAt = time.Now()

		if err := tx.Save(&payment).Error; err != nil {
			return fmt.Errorf("failed to update payment status: %w", err)
		}

		// If payment status changed to SUCCESS, update order financials
		if status == "SUCCESS" && payment.PaymentStatus != "SUCCESS" {
			err := s.updateOrderFinancialsWithPayment(ctx, &payment)
			if err != nil {
				return fmt.Errorf("failed to update order financials with payment: %w", err)
			}
		}

		return nil
	})
}

func (s *Service) ProcessRefund(ctx context.Context, paymentID uint, amount float64, reference string) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// Get existing payment
		var payment dao.PaymentTransaction
		if err := tx.Where("payment_id = ?", paymentID).First(&payment).Error; err != nil {
			return fmt.Errorf("failed to get payment: %w", err)
		}

		// Ensure payment was successful
		if payment.PaymentStatus != "SUCCESS" {
			return errors.New("cannot refund a payment that was not successful")
		}

		// Ensure refund amount doesn't exceed original payment
		if amount > payment.Amount {
			return errors.New("refund amount cannot exceed original payment amount")
		}

		// Update payment with refund information
		now := time.Now()
		refundStatus := "PROCESSED"

		payment.RefundReference = reference
		payment.RefundStatus = refundStatus
		payment.RefundAmount = amount
		payment.RefundProcessedDate = now
		payment.UpdatedAt = now

		if err := tx.Save(&payment).Error; err != nil {
			return fmt.Errorf("failed to update payment with refund: %w", err)
		}

		return nil
	})
}

func (s *Service) GetPaymentByID(ctx context.Context, paymentID uint) (*dao.PaymentTransaction, error) {
	var payment dao.PaymentTransaction

	result := s.db.WithContext(ctx).Where("payment_id = ?", paymentID).First(&payment)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("payment not found for ID %d", paymentID)
		}
		return nil, fmt.Errorf("failed to get payment: %w", result.Error)
	}

	return &payment, nil
}

func (s *Service) GetPaymentsByOrderID(ctx context.Context, orderID uint) ([]*dao.PaymentTransaction, error) {
	var payments []*dao.PaymentTransaction

	result := s.db.WithContext(ctx).Where("order_id = ?", orderID).Find(&payments)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get payments: %w", result.Error)
	}

	return payments, nil
}

func (s *Service) RecordCODPayment(ctx context.Context, orderID uint, amount float64) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// Get order financials
		var orderFinancials dao.OrderFinancials
		if err := tx.Where("order_id = ?", orderID).First(&orderFinancials).Error; err != nil {
			return fmt.Errorf("failed to get order financials: %w", err)
		}

		// Update cash collected
		orderFinancials.CashCollectedByThirdPL = &amount
		orderFinancials.UpdatedAt = time.Now()

		if err := tx.Save(&orderFinancials).Error; err != nil {
			return fmt.Errorf("failed to update order financials with COD payment: %w", err)
		}

		// Create payment transaction record
		now := time.Now()
		payment := &dao.PaymentTransaction{
			OrderID:              orderID,
			TransactionReference: fmt.Sprintf("COD-%d-%s", orderID, now.Format("20060102-150405")),
			PaymentGateway:       "COD",
			PaymentMethod:        "CASH",
			PaymentStatus:        "SUCCESS",
			Amount:               amount,
			Currency:             "INR",
			PaymentDate:          now,
			CreatedAt:            now,
			UpdatedAt:            now,
		}

		if err := tx.Create(payment).Error; err != nil {
			return fmt.Errorf("failed to create COD payment record: %w", err)
		}

		return nil
	})
}

func (s *Service) RecordCODRemittance(ctx context.Context, orderID uint, remittanceDate time.Time) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// Get order financials
		var orderFinancials dao.OrderFinancials
		if err := tx.Where("order_id = ?", orderID).First(&orderFinancials).Error; err != nil {
			return fmt.Errorf("failed to get order financials: %w", err)
		}

		// Update COD remittance date
		orderFinancials.CODRemittanceDate = &remittanceDate
		orderFinancials.UpdatedAt = time.Now()

		if err := tx.Save(&orderFinancials).Error; err != nil {
			return fmt.Errorf("failed to update order financials with COD remittance: %w", err)
		}

		// Update payment transaction
		var payment dao.PaymentTransaction
		if err := tx.Where("order_id = ? AND payment_gateway = 'COD'", orderID).First(&payment).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("failed to get COD payment: %w", err)
			}
			// If no payment record exists, it's not an error - just return
			return nil
		}

		// Update settlement reference and date
		settlementRef := fmt.Sprintf("COD-REM-%d-%s", orderID, remittanceDate.Format("20060102"))
		payment.SettlementReference = settlementRef
		payment.SettlementDate = remittanceDate
		payment.UpdatedAt = time.Now()

		if err := tx.Save(&payment).Error; err != nil {
			return fmt.Errorf("failed to update COD payment with remittance: %w", err)
		}

		return nil
	})
}

// Helper function to update order financials with payment information
func (s *Service) updateOrderFinancialsWithPayment(ctx context.Context, payment *dao.PaymentTransaction) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		// Get order financials
		var orderFinancials dao.OrderFinancials
		if err := tx.Where("order_id = ?", payment.OrderID).First(&orderFinancials).Error; err != nil {
			return fmt.Errorf("failed to get order financials: %w", err)
		}

		// Update payment related fields
		paymentTotalCharges := payment.GatewayFee + payment.GatewayTax
		orderFinancials.PaymentType = payment.PaymentMethod
		orderFinancials.PaymentGateway = payment.PaymentGateway
		orderFinancials.PaymentGatewayCharges = &payment.GatewayFee
		orderFinancials.PaymentGatewayGST = &payment.GatewayTax
		orderFinancials.PaymentTotalCharges = &paymentTotalCharges

		// Set appropriate fields based on payment method
		if payment.PaymentMethod == "COD" {
			orderFinancials.CODCharges = &payment.GatewayFee
		} else {
			// For prepaid payments, record as advance collected
			orderFinancials.AdvanceCollected = payment.Amount
		}

		orderFinancials.UpdatedAt = time.Now()

		if err := tx.Save(&orderFinancials).Error; err != nil {
			return fmt.Errorf("failed to update order financials with payment: %w", err)
		}

		return nil
	})
}

// GetShipmentBillingZone returns the billing zone for a given origin and destination
func (s *Service) GetShipmentBillingZone(ctx context.Context, origin, destination string, threePLName string) (*string, error) {
	cacheKey := s.generateCacheKey(origin, destination)

	// Try to get from cache first (read lock)
	s.zoneCacheMutex.RLock()
	if cachedZone, exists := s.zoneCache[cacheKey]; exists {
		s.zoneCacheMutex.RUnlock()
		return cachedZone.BillingZone, nil
	}
	s.zoneCacheMutex.RUnlock()

	// Not in cache, fetch from database
	var zoneMapping dao.ThreePLZonePricingMapping

	result := s.db.WithContext(ctx).Where("od_pair = ? AND 3pl_name = ?", fmt.Sprintf("%s-%s", origin, destination), threePLName).First(&zoneMapping)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("shipment zone not found for origin %s and destination %s", origin, destination)
		}
		return nil, fmt.Errorf("failed to get shipment zone: %w", result.Error)
	}

	// Store in cache (write lock)
	s.zoneCacheMutex.Lock()
	s.zoneCache[cacheKey] = &zoneMapping
	s.zoneCacheMutex.Unlock()
	return zoneMapping.BillingZone, nil
}

// generateCacheKey creates a consistent cache key from origin and destination
func (s *Service) generateCacheKey(origin, destination string) string {
	return fmt.Sprintf("%s:%s", origin, destination)
}

func (s *Service) ProcessBulkUpdate(ctx context.Context, req *dto.OrderFinancialsBulkUpdateRequest) (*dto.OrderFinancialsBulkUpdateResponse, error) {
	// Step 1: Insert initial record into csv_uploads table
	csvUpload := dao.CsvUploads{
		UpdatedBy:  req.UpdatedBy,
		ActionType: req.ActionType,
		CsvType:    req.Data.Type,
	}

	if req.ActionType == "update" {
		csvUpload.Force = &req.Force
		csvUpload.FileID = &req.Data.FileID
	}

	if err := s.db.Create(&csvUpload).Error; err != nil {
		return nil, fmt.Errorf("failed to create csv_uploads record: %w", err)
	}

	// Step 2: Call external API
	externalResp, err := s.callExternalAPI(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to call external API: %w", err)
	}

	// Step 3: Update csv_uploads record with response data
	updateData := map[string]interface{}{
		"failed_count":  externalResp.Data.Data.Failed,
		"success_count": externalResp.Data.Data.Success,
		"total_records": externalResp.Data.Data.TotalRecords,
	}

	if externalResp.Data.Data.RequestCSVURL != "" {
		updateData["raw_data_url"] = externalResp.Data.Data.RequestCSVURL
	}

	if externalResp.Data.Data.ResponseCSVURL != "" {
		updateData["validated_data_url"] = externalResp.Data.Data.ResponseCSVURL
	}
	if externalResp.Data.FileID != "" {
		updateData["file_id"] = externalResp.Data.FileID
	}

	if err := s.db.Model(&dao.CsvUploads{}).Where("id = ?", csvUpload.ID).Updates(updateData).Error; err != nil {
		return nil, fmt.Errorf("failed to update csv_uploads record: %w", err)
	}

	return externalResp, nil
}

// callExternalAPI makes the HTTP request to the external API
func (s *Service) callExternalAPI(ctx context.Context, req *dto.OrderFinancialsBulkUpdateRequest) (*dto.OrderFinancialsBulkUpdateResponse, error) {
	url := "https://asia-south1-op-d2r.cloudfunctions.net/order_financials_bulk_update_preprod"

	// Marshal request to JSON
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("accept", "application/json, text/plain, */*")
	httpReq.Header.Set("accept-language", "en-GB,en-US;q=0.9,en;q=0.8")
	httpReq.Header.Set("content-type", "application/json")
	httpReq.Header.Set("origin", "https://b2b.retailpulse.ai")
	httpReq.Header.Set("priority", "u=1, i")
	httpReq.Header.Set("referer", "https://b2b.retailpulse.ai/")
	httpReq.Header.Set("sec-ch-ua", `"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"`)
	httpReq.Header.Set("sec-ch-ua-mobile", "?0")
	httpReq.Header.Set("sec-ch-ua-platform", `"macOS"`)
	httpReq.Header.Set("sec-fetch-dest", "empty")
	httpReq.Header.Set("sec-fetch-mode", "cors")
	httpReq.Header.Set("sec-fetch-site", "cross-site")
	httpReq.Header.Set("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// Send request
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("external API returned status code %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var externalResp dto.OrderFinancialsBulkUpdateResponse
	if err := json.Unmarshal(body, &externalResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &externalResp, nil

}

func (s *Service) GetBulkActionLogs(ctx context.Context, request *dto.GetBulkActionLogsRequest) (*dto.GetBulkActionLogsResponse, error) {
	var csvUploads []dao.CsvUploads
	limit := request.Data.Limit
	offset := request.Data.Offset
	if limit == nil {
		limit = new(int)
		*limit = 10
	}
	if offset == nil {
		offset = new(int)
		*offset = 0
	}

	result := s.db.WithContext(ctx).
		Order("updated_at DESC").
		Limit(*limit).
		Offset(*offset).
		Find(&csvUploads)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get csv uploads: %w", result.Error)
	}

	var response dto.GetBulkActionLogsResponse
	for _, csvUpload := range csvUploads {
		validatedDataURL := csvUpload.ValidatedDataURL
		if validatedDataURL == nil {
			validatedDataURL = new(string)
		}

		rawDataURL := csvUpload.RawDataURL
		if rawDataURL == nil {
			rawDataURL = new(string)
		}
		response.Data = append(response.Data, dto.GetBulkActionLogsResponseData{
			ID:             fmt.Sprint(csvUpload.ID),
			UpdatedAt:      csvUpload.UpdatedAt.Unix(),
			UpdatedBy:      csvUpload.UpdatedBy,
			Seller:         "",
			Type:           csvUpload.CsvType,
			TotalRecords:   *csvUpload.TotalRecords,
			Success:        *csvUpload.SuccessCount,
			Failed:         *csvUpload.FailedCount,
			ResponseCsvUrl: *validatedDataURL,
			RequestCsvUrl:  *rawDataURL,
			Status:         nil,
			Message:        nil,
			ActionType:     &csvUpload.ActionType,
			FileID:         csvUpload.FileID,
			Force:          csvUpload.Force,
		})
	}
	return &response, nil
}

// this return FinancialMasterData
func (s *Service) GetBulkData(ctx context.Context, orderIds []int64) ([]*kcFinances.FinancialMasterData, error) {	

	return nil, nil
}
