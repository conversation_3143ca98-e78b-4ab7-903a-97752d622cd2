# API Curl Commands for Financial Service

## Order Financials APIs

### 1. Create Order Financials

```bash
curl -X POST http://localhost:8080/api/financials/orders \
  -H "Content-Type: application/json" \
  -d '{
    "order_financials": {
      "order_id": 12345,
      "invoice_number": "INV-KC-2025-0001",
      "invoice_date": "2025-05-10",
      "gstin_buyer": "29**********1Z5",
      "gstin_seller": "27BBBBB0000B1Z6",
      "pan_seller": "**********",
      "place_of_supply": "Karnataka",
      "place_of_demand": "Maharashtra",
      "retailer_id": 5001,
      "supplier_id": 1001,
      "order_confirmed_date": "2025-05-09",
      "order_ready_to_ship_date": "2025-05-10",
      "order_subtotal": 10000.00,
      "taxable_value": 9500.00,
      "cgst_total": 0.00,
      "sgst_total": 0.00,
      "igst_total": 1710.00,
      "cess_total": 0.00,
      "total_gst_amount": 1710.00,
      "order_total": 11210.00,
      "commission_rate": 5.00,
      "commission_amount": 500.00,
      "oms_charge": 2.00,
      "tds_rate": 1.00,
      "tds_amount": 100.00,
      "kc_ship": true,
      "tcs_applicable": true,
      "tcs_rate": 0.1,
      "tcs_amount": 11.21,
      "payment_type": "PREPAID",
      "payment_gateway": "Razorpay",
      "payment_gateway_charges": 112.10,
      "payment_gateway_gst": 20.18,
      "payment_total_charges": 132.28,
      "advance_collected": 11210.00,
      "cash_collected_by_courier": 0.00,
      "cod_charges": 0.00,
      "net_payable_to_supplier": 10450.00
    },
    "order_item_financials": [
      {
        "order_id": 12345,
        "order_item_id": 67890,
        "product_id": 12345,
        "sku_id": "SKU-12345",
        "hsn_code": "1905",
        "supplier_unit_price": 200.00,
        "retailer_selling_price": 250.00,
        "quantity": 40,
        "total_supplier_price": 8000.00,
        "total_retailer_price": 10000.00,
        "item_discount": 500.00,
        "taxable_value": 9500.00,
        "gst_rate": 18.00,
        "cgst_rate": 0.00,
        "sgst_rate": 0.00,
        "igst_rate": 18.00,
        "cess_rate": 0.00,
        "cgst_amount": 0.00,
        "sgst_amount": 0.00,
        "igst_amount": 1710.00,
        "cess_amount": 0.00,
        "total_tax": 1710.00,
        "item_total": 11210.00,
        "is_free_item": false
      }
    ],
    "order_discounts": [
      {
        "order_id": 12345,
        "discount_type": "CART_VALUE",
        "discount_provider": "KC",
        "discount_code": "WELCOME10",
        "is_percentage": true,
        "discount_percentage": 5.00,
        "discount_amount": 500.00,
        "discount_description": "Special welcome discount for first purchase",
        "offer_type": "VOLUME_BASED",
        "is_pre_tax": true
      }
    ],
    "free_items": [],
    "shipment_financials": [
      {
        "order_id": 12345,
        "shipment_id": 7890,
        "waybill_number": "WB12345678",
        "courier_partner": "Delhivery",
        "courier_partner_gstin": "07ABCDE1234F1ZX",
        "logistics_zone": "Zone B",
        "seller_dead_weight": 5.200,
        "seller_length_cm": 40.5,
        "seller_breadth_cm": 30.2,
        "seller_height_cm": 20.5,
        "seller_volumetric_weight": 5.000,
        "courier_dead_weight": 5.350,
        "courier_length_cm": 41.0,
        "courier_breadth_cm": 31.0,
        "courier_height_cm": 21.0,
        "courier_volumetric_weight": 5.300,
        "chargeable_weight": 5.350,
        "courier_charges_actual": 428.00,
        "courier_charges_billed": 500.00,
        "courier_markup_amount": 72.00,
        "courier_gst_rate": 18.00,
        "courier_gst_amount": 77.04,
        "is_rto": false
      }
    ]
  }'
```

### 2. Get Order Financials

```bash
curl -X GET http://localhost:8080/api/financials/orders/12345 \
  -H "Content-Type: application/json"
```

### 3. Calculate Net Payable to Supplier

```bash
curl -X GET http://localhost:8080/api/financials/orders/12345/net-payable \
  -H "Content-Type: application/json"
```

## Payment Transaction APIs

### 4. Create Payment Transaction (Prepaid)

```bash
curl -X POST http://localhost:8080/api/financials/payments \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": 12345,
    "transaction_reference": "pay_GT5MUEzFCu6YBO",
    "payment_gateway": "Razorpay",
    "payment_method": "UPI",
    "payment_status": "SUCCESS",
    "amount": 11210.00,
    "currency": "INR",
    "gateway_fee": 112.10,
    "gateway_tax": 20.18,
    "payment_date": "2025-05-09T14:35:22",
    "customer_payment_detail": "{\"vpa\":\"user@okicici\",\"bank\":\"ICICI\",\"masked_vpa\":\"us***@okicici\"}"
  }'
```

### 5. Create COD Payment

```bash
curl -X POST http://localhost:8080/api/financials/payments/cod/12346 \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 8500.00
  }'
```

### 6. Get Payment Transaction

```bash
curl -X GET http://localhost:8080/api/financials/payments/3456 \
  -H "Content-Type: application/json"
```

### 7. Get Payments By Order

```bash
curl -X GET http://localhost:8080/api/financials/payments/order/12345 \
  -H "Content-Type: application/json"
```

### 8. Update Payment Status

```bash
curl -X POST http://localhost:8080/api/financials/payments/3456/status \
  -H "Content-Type: application/json" \
  -d '{
    "status": "SUCCESS"
  }'
```

### 9. Process Refund

```bash
curl -X POST http://localhost:8080/api/financials/payments/3456/refund \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 1003.00,
    "reference": "ref_MOpx3aZq4PVRhr"
  }'
```

### 10. Record COD Remittance

```bash
curl -X POST http://localhost:8080/api/financials/payments/cod/12346/remittance \
  -H "Content-Type: application/json" \
  -d '{
    "remittance_date": "2025-05-20"
  }'
```

## Returns and Refunds APIs

### 11. Create Return

```bash
curl -X POST http://localhost:8080/api/financials/returns \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": 12346,
    "order_item_id": 67891,
    "return_type": "DAMAGED",
    "return_amount": 850.00,
    "return_reason": "Product damaged during transit",
    "return_initiated_date": "2025-05-10",
    "refund_status": "INITIATED"
  }'
```

### 12. Get Returns By Order

```bash
curl -X GET http://localhost:8080/api/financials/returns/order/12346 \
  -H "Content-Type: application/json"
```

### 13. Confirm Return

```bash
curl -X POST http://localhost:8080/api/financials/returns/789/confirm \
  -H "Content-Type: application/json" \
  -d '{
    "seller_confirmed": true
  }'
```

### 14. Process Return Refund

```bash
curl -X POST http://localhost:8080/api/financials/returns/789/refund \
  -H "Content-Type: application/json" \
  -d '{
    "refund_amount": 850.00
  }'
```

## Credit Note APIs

### 15. Create Credit Note For Return

```bash
curl -X POST http://localhost:8080/api/financials/returns/789/credit-note \
  -H "Content-Type: application/json" \
  -d '{
    "reason": "Product damaged during transit"
  }'
```

### 16. Create Credit Note (Manual)

```bash
curl -X POST http://localhost:8080/api/financials/credit-notes \
  -H "Content-Type: application/json" \
  -d '{
    "credit_note": {
      "credit_note_number": "CN-12346-20250511-123456",
      "credit_note_date": "2025-05-11",
      "related_invoice_number": "INV-KC-2025-0002",
      "related_invoice_date": "2025-05-10",
      "seller_id": 1001,
      "buyer_id": 5002,
      "order_id": 12346,
      "credit_note_reason": "Price adjustment",
      "credit_note_type": "PRICE_CORRECTION",
      "total_value_before_tax": 500.00,
      "cgst_amount": 0.00,
      "sgst_amount": 0.00,
      "igst_amount": 90.00,
      "total_credit_note_value": 590.00,
      "adjustment_status": "PENDING"
    },
    "credit_note_items": [
      {
        "product_id": 12346,
        "sku_id": "SKU-12346",
        "hsn_code": "1905",
        "quantity": 1,
        "unit_price": 500.00,
        "total_value": 500.00,
        "gst_rate": 18.00,
        "cgst_amount": 0.00,
        "sgst_amount": 0.00,
        "igst_amount": 90.00,
        "total_tax": 90.00,
        "item_total": 590.00,
        "reason": "Price difference adjustment"
      }
    ]
  }'
```

### 17. Get Credit Note

```bash
curl -X GET http://localhost:8080/api/financials/credit-notes/456 \
  -H "Content-Type: application/json"
```

### 18. Get Credit Notes By Order

```bash
curl -X GET http://localhost:8080/api/financials/credit-notes/order/12346 \
  -H "Content-Type: application/json"
```

## Debit Note APIs

### 19. Create Debit Note

```bash
curl -X POST http://localhost:8080/api/financials/debit-notes \
  -H "Content-Type: application/json" \
  -d '{
    "debit_note": {
      "debit_note_number": "DN-12346-20250511-123456",
      "debit_note_date": "2025-05-11",
      "related_invoice_number": "INV-KC-2025-0002",
      "related_invoice_date": "2025-05-10",
      "seller_id": 1001,
      "buyer_id": 5002,
      "order_id": 12346,
      "debit_note_reason": "Incorrect discount applied",
      "debit_note_type": "PRICE_CORRECTION",
      "total_value_before_tax": 300.00,
      "cgst_amount": 0.00,
      "sgst_amount": 0.00,
      "igst_amount": 54.00,
      "total_debit_note_value": 354.00,
      "adjustment_status": "PENDING"
    },
    "debit_note_items": [
      {
        "product_id": 12346,
        "sku_id": "SKU-12346",
        "hsn_code": "1905",
        "quantity": 1,
        "unit_price": 300.00,
        "total_value": 300.00,
        "gst_rate": 18.00,
        "cgst_amount": 0.00,
        "sgst_amount": 0.00,
        "igst_amount": 54.00,
        "total_tax": 54.00,
        "item_total": 354.00,
        "reason": "Discount correction"
      }
    ]
  }'
```

### 20. Get Debit Note

```bash
curl -X GET http://localhost:8080/api/financials/debit-notes/123 \
  -H "Content-Type: application/json"
```

### 21. Get Debit Notes By Order

```bash
curl -X GET http://localhost:8080/api/financials/debit-notes/order/12346 \
  -H "Content-Type: application/json"
```

## Settlement APIs

### 22. Generate Settlement

```bash
curl -X POST http://localhost:8080/api/financials/settlements \
  -H "Content-Type: application/json" \
  -d '{
    "seller_id": 1001,
    "start_date": "2025-05-01",
    "end_date": "2025-05-31",
    "include_orders": true,
    "include_credits": true,
    "include_debits": true
  }'
```

### 23. Get Settlement

```bash
curl -X GET http://localhost:8080/api/financials/settlements/789 \
  -H "Content-Type: application/json"
```

### 24. Get Settlements By Seller

```bash
curl -X GET http://localhost:8080/api/financials/settlements/seller/1001 \
  -H "Content-Type: application/json"
```

### 25. Add Order To Settlement

```bash
curl -X POST http://localhost:8080/api/financials/settlements/789/orders/12345 \
  -H "Content-Type: application/json"
```

### 26. Add Credit Note To Settlement

```bash
curl -X POST http://localhost:8080/api/financials/settlements/789/credit-notes/456 \
  -H "Content-Type: application/json"
```

### 27. Add Debit Note To Settlement

```bash
curl -X POST http://localhost:8080/api/financials/settlements/789/debit-notes/123 \
  -H "Content-Type: application/json"
```

### 28. Finalize Settlement

```bash
curl -X POST http://localhost:8080/api/financials/settlements/789/finalize \
  -H "Content-Type: application/json"
```

### 29. Mark Settlement as Paid

```bash
curl -X POST http://localhost:8080/api/financials/settlements/789/paid \
  -H "Content-Type: application/json" \
  -d '{
    "payment_date": "2025-06-05"
  }'
```

## Additional Utilities

### 30. Get HSN Code Tax Rates

```bash
curl -X GET http://localhost:8080/api/financials/hsn-rates/1905 \
  -H "Content-Type: application/json"
```

### 31. Get Financial Audit Logs

```bash
curl -X GET http://localhost:8080/api/financials/audit-logs/credit_note/456 \
  -H "Content-Type: application/json"
```

### 32. Create Audit Log Entry (for manual operations)

```bash
curl -X POST http://localhost:8080/api/financials/audit-logs \
  -H "Content-Type: application/json" \
  -d '{
    "table_name": "credit_note",
    "record_id": 456,
    "action_type": "UPDATE",
    "field_name": "adjustment_status",
    "old_value": "PENDING",
    "new_value": "ADJUSTED",
    "changed_by": "admin.user",
    "reason": "Manual adjustment after seller communication"
  }'
```

## Complex Operational Examples

### 33. Full Order Processing Example (COD)

```bash
# Step 1: Create Order Financials (COD)
curl -X POST http://localhost:8080/api/financials/orders \
  -H "Content-Type: application/json" \
  -d '{
    "order_financials": {
      "order_id": 12346,
      "invoice_number": "INV-KC-2025-0002",
      "invoice_date": "2025-05-10",
      "gstin_buyer": "29**********1Z5",
      "gstin_seller": "27BBBBB0000B1Z6",
      "pan_seller": "**********",
      "place_of_supply": "Karnataka",
      "place_of_demand": "Maharashtra",
      "retailer_id": 5002,
      "supplier_id": 1001,
      "order_confirmed_date": "2025-05-09",
      "order_ready_to_ship_date": "2025-05-10",
      "order_subtotal": 8000.00,
      "taxable_value": 7600.00,
      "cgst_total": 0.00,
      "sgst_total": 0.00,
      "igst_total": 1368.00,
      "cess_total": 0.00,
      "total_gst_amount": 1368.00,
      "order_total": 8968.00,
      "commission_rate": 5.00,
      "commission_amount": 400.00,
      "oms_charge": 2.00,
      "tds_rate": 1.00,
      "tds_amount": 80.00,
      "kc_ship": true,
      "tcs_applicable": true,
      "tcs_rate": 0.1,
      "tcs_amount": 8.97,
      "payment_type": "COD",
      "payment_gateway": "",
      "payment_gateway_charges": 0.00,
      "payment_gateway_gst": 0.00,
      "payment_total_charges": 0.00,
      "advance_collected": 0.00,
      "cash_collected_by_courier": 0.00,
      "cod_charges": 50.00,
      "net_payable_to_supplier": 8336.03
    },
    "order_item_financials": [
      {
        "order_id": 12346,
        "order_item_id": 67891,
        "product_id": 12346,
        "sku_id": "SKU-12346",
        "hsn_code": "1905",
        "supplier_unit_price": 200.00,
        "retailer_selling_price": 250.00,
        "quantity": 32,
        "total_supplier_price": 6400.00,
        "total_retailer_price": 8000.00,
        "item_discount": 400.00,
        "taxable_value": 7600.00,
        "gst_rate": 18.00,
        "cgst_rate": 0.00,
        "sgst_rate": 0.00,
        "igst_rate": 18.00,
        "cess_rate": 0.00,
        "cgst_amount": 0.00,
        "sgst_amount": 0.00,
        "igst_amount": 1368.00,
        "cess_amount": 0.00,
        "total_tax": 1368.00,
        "item_total": 8968.00,
        "is_free_item": false
      }
    ],
    "order_discounts": [
      {
        "order_id": 12346,
        "discount_type": "CART_VALUE",
        "discount_provider": "KC",
        "discount_code": "WELCOME10",
        "is_percentage": true,
        "discount_percentage": 5.00,
        "discount_amount": 400.00,
        "discount_description": "Welcome discount for first purchase",
        "offer_type": "VOLUME_BASED",
        "is_pre_tax": true
      }
    ],
    "shipment_financials": [
      {
        "order_id": 12346,
        "shipment_id": 7891,
        "waybill_number": "WB12345679",
        "courier_partner": "Delhivery",
        "courier_partner_gstin": "07ABCDE1234F1ZX",
        "logistics_zone": "Zone B",
        "seller_dead_weight": 4.100,
        "seller_length_cm": 35.5,
        "seller_breadth_cm": 25.2,
        "seller_height_cm": 15.5,
        "seller_volumetric_weight": 4.000,
        "courier_dead_weight": 4.250,
        "courier_length_cm": 36.0,
        "courier_breadth_cm": 26.0,
        "courier_height_cm": 16.0,
        "courier_volumetric_weight": 4.200,
        "chargeable_weight": 4.250,
        "courier_charges_actual": 340.00,
        "courier_charges_billed": 400.00,
        "courier_markup_amount": 60.00,
        "courier_gst_rate": 18.00,
        "courier_gst_amount": 61.20,
        "is_rto": false
      }
    ]
  }'

# Step 2: Record COD collection
curl -X POST http://localhost:8080/api/financials/payments/cod/12346 \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 8968.00
  }'

# Step 3: Record COD remittance
curl -X POST http://localhost:8080/api/financials/payments/cod/12346/remittance \
  -H "Content-Type: application/json" \
  -d '{
    "remittance_date": "2025-05-20"
  }'
```

### 34. Full Return and Credit Note Process

```bash
# Step 1: Create return
curl -X POST http://localhost:8080/api/financials/returns \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": 12345,
    "order_item_id": 67890,
    "return_type": "DAMAGED",
    "return_amount": 1003.00,
    "return_reason": "Product damaged during transit",
    "return_initiated_date": "2025-05-15",
    "refund_status": "INITIATED"
  }'

# Step 2: Confirm return
curl -X POST http://localhost:8080/api/financials/returns/790/confirm \
  -H "Content-Type: application/json" \
  -d '{
    "seller_confirmed": true
  }'

# Step 3: Generate credit note
curl -X POST http://localhost:8080/api/financials/returns/790/credit-note \
  -H "Content-Type: application/json" \
  -d '{
    "reason": "Product damaged during transit"
  }'

# Step 4: Process refund (if prepaid)
curl -X POST http://localhost:8080/api/financials/payments/3456/refund \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 1003.00,
    "reference": "ref_QA5x3aZq4PVRhy"
  }'
```

### 35. Complete Monthly Settlement Process

```bash
# Step 1: Generate settlement
curl -X POST http://localhost:8080/api/financials/settlements \
  -H "Content-Type: application/json" \
  -d '{
    "seller_id": 1001,
    "start_date": "2025-05-01",
    "end_date": "2025-05-31",
    "include_orders": false,
    "include_credits": false,
    "include_debits": false
  }'

# Step 2: Add order to settlement (do this for each order)
curl -X POST http://localhost:8080/api/financials/settlements/789/orders/12345 \
  -H "Content-Type: application/json"

# Step 3: Add credit notes to settlement (do this for each credit note)
curl -X POST http://localhost:8080/api/financials/settlements/789/credit-notes/456 \
  -H "Content-Type: application/json"

# Step 4: Add debit notes to settlement (do this for each debit note)
curl -X POST http://localhost:8080/api/financials/settlements/789/debit-notes/123 \
  -H "Content-Type: application/json"

# Step 5: Finalize settlement
curl -X POST http://localhost:8080/api/financials/settlements/789/finalize \
  -H "Content-Type: application/json"

# Step 6: Mark settlement as paid
curl -X POST http://localhost:8080/api/financials/settlements/789/paid \
  -H "Content-Type: application/json" \
  -d '{
    "payment_date": "2025-06-05"
  }'
```