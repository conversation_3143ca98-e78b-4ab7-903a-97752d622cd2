# API Response Examples

## Order Financials APIs

### 1. Create Order Financials Response

```json
{
  "success": true,
  "message": "Order financials created successfully",
  "data": {
    "order_id": 12345,
    "order_financial_id": 5678
  }
}
```

### 2. Get Order Financials Response (Continued)

```json
{
  "success": true,
  "data": {
    "order_financial_id": 5678,
    "order_id": 12345,
    "invoice_number": "INV-KC-2025-0001",
    "invoice_date": "2025-05-10",
    "gstin_buyer": "29**********1Z5",
    "gstin_seller": "27BBBBB0000B1Z6",
    "pan_seller": "**********",
    "place_of_supply": "Karnataka",
    "place_of_demand": "Maharashtra",
    "retailer_id": 5001,
    "supplier_id": 1001,
    "order_confirmed_date": "2025-05-09",
    "order_ready_to_ship_date": "2025-05-10",
    "order_subtotal": 10000.00,
    "taxable_value": 9500.00,
    "cgst_total": 0.00,
    "sgst_total": 0.00,
    "igst_total": 1710.00,
    "cess_total": 0.00,
    "total_gst_amount": 1710.00,
    "order_total": 11210.00,
    "commission_rate": 5.00,
    "commission_amount": 500.00,
    "oms_charge": 2.00,
    "tds_rate": 1.00,
    "tds_amount": 100.00,
    "kc_ship": true,
    "tcs_applicable": true,
    "tcs_rate": 0.1,
    "tcs_amount": 11.21,
    "payment_type": "PREPAID",
    "payment_gateway": "Razorpay",
    "payment_gateway_charges": 112.10,
    "payment_gateway_gst": 20.18,
    "payment_total_charges": 132.28,
    "advance_collected": 11210.00,
    "cash_collected_by_courier": 0.00,
    "cod_charges": 0.00,
    "net_payable_to_supplier": 10450.00,
    "final_settlement_to_supplier": 0.00,
    "shipping_status": "PROCESSING",
    "created_at": "2025-05-10T10:15:22",
    "updated_at": "2025-05-10T10:15:22",
    "order_items": [
      {
        "item_financial_id": 9012,
        "order_id": 12345,
        "order_item_id": 67890,
        "product_id": 12345,
        "sku_id": "SKU-12345",
        "hsn_code": "1905",
        "supplier_unit_price": 200.00,
        "retailer_selling_price": 250.00,
        "quantity": 40,
        "total_supplier_price": 8000.00,
        "total_retailer_price": 10000.00,
        "item_discount": 500.00,
        "taxable_value": 9500.00,
        "gst_rate": 18.00,
        "cgst_rate": 0.00,
        "sgst_rate": 0.00,
        "igst_rate": 18.00,
        "cess_rate": 0.00,
        "cgst_amount": 0.00,
        "sgst_amount": 0.00,
        "igst_amount": 1710.00,
        "cess_amount": 0.00,
        "total_tax": 1710.00,
        "item_total": 11210.00,
        "is_free_item": false
      }
    ],
    "discounts": [
      {
        "discount_id": 3456,
        "order_id": 12345,
        "discount_type": "CART_VALUE",
        "discount_provider": "KC",
        "discount_code": "WELCOME10",
        "is_percentage": true,
        "discount_percentage": 5.00,
        "discount_amount": 500.00,
        "discount_description": "Special welcome discount for first purchase",
        "offer_type": "VOLUME_BASED",
        "is_pre_tax": true
      }
    ],
    "shipments": [
      {
        "shipment_financial_id": 2345,
        "order_id": 12345,
        "shipment_id": 7890,
        "waybill_number": "WB12345678",
        "courier_partner": "Delhivery",
        "courier_partner_gstin": "07ABCDE1234F1ZX",
        "logistics_zone": "Zone B",
        "seller_dead_weight": 5.200,
        "seller_length_cm": 40.5,
        "seller_breadth_cm": 30.2,
        "seller_height_cm": 20.5,
        "seller_volumetric_weight": 5.000,
        "courier_dead_weight": 5.350,
        "courier_length_cm": 41.0,
        "courier_breadth_cm": 31.0,
        "courier_height_cm": 21.0,
        "courier_volumetric_weight": 5.300,
        "chargeable_weight": 5.350,
        "courier_charges_actual": 428.00,
        "courier_charges_billed": 500.00,
        "courier_markup_amount": 72.00,
        "courier_gst_rate": 18.00,
        "courier_gst_amount": 77.04,
        "is_rto": false
      }
    ]
  }
}
```

### 3. Calculate Net Payable to Supplier Response

```json
{
  "success": true,
  "data": {
    "order_id": 12345,
    "net_payable_to_supplier": 10450.00,
    "calculation_details": {
      "order_total": 11210.00,
      "commission_amount": 500.00,
      "oms_charge": 2.00,
      "delivery_charges": 500.00,
      "tds_amount": 100.00,
      "tcs_amount": 11.21,
      "kc_discounts": 500.00,
      "seller_discounts": 0.00,
      "rto_charges": 0.00
    }
  }
}
```

## Payment Transaction APIs

### 4. Create Payment Transaction (Prepaid) Response

```json
{
  "success": true,
  "message": "Payment transaction created successfully",
  "data": {
    "payment_id": 3456,
    "order_id": 12345,
    "transaction_reference": "pay_GT5MUEzFCu6YBO",
    "payment_status": "SUCCESS",
    "amount": 11210.00
  }
}
```

### 5. Create COD Payment Response

```json
{
  "success": true,
  "message": "COD payment recorded successfully",
  "data": {
    "payment_id": 3457,
    "order_id": 12346,
    "transaction_reference": "COD-12346-20250510-145622",
    "payment_status": "SUCCESS",
    "amount": 8500.00
  }
}
```

### 6. Get Payment Transaction Response

```json
{
  "success": true,
  "data": {
    "payment_id": 3456,
    "order_id": 12345,
    "transaction_reference": "pay_GT5MUEzFCu6YBO",
    "payment_gateway": "Razorpay",
    "payment_method": "UPI",
    "payment_status": "SUCCESS",
    "amount": 11210.00,
    "currency": "INR",
    "gateway_fee": 112.10,
    "gateway_tax": 20.18,
    "payment_date": "2025-05-09T14:35:22",
    "settlement_reference": null,
    "settlement_date": null,
    "refund_reference": null,
    "refund_status": null,
    "refund_amount": null,
    "refund_processed_date": null,
    "customer_payment_detail": "{\"vpa\":\"user@okicici\",\"bank\":\"ICICI\",\"masked_vpa\":\"us***@okicici\"}",
    "created_at": "2025-05-09T14:36:05",
    "updated_at": "2025-05-09T14:36:05"
  }
}
```

### 7. Get Payments By Order Response

```json
{
  "success": true,
  "data": [
    {
      "payment_id": 3456,
      "order_id": 12345,
      "transaction_reference": "pay_GT5MUEzFCu6YBO",
      "payment_gateway": "Razorpay",
      "payment_method": "UPI",
      "payment_status": "SUCCESS",
      "amount": 11210.00,
      "currency": "INR",
      "gateway_fee": 112.10,
      "gateway_tax": 20.18,
      "payment_date": "2025-05-09T14:35:22",
      "created_at": "2025-05-09T14:36:05",
      "updated_at": "2025-05-09T14:36:05"
    }
  ]
}
```

### 8. Update Payment Status Response

```json
{
  "success": true,
  "message": "Payment status updated successfully",
  "data": {
    "payment_id": 3456,
    "transaction_reference": "pay_GT5MUEzFCu6YBO",
    "payment_status": "SUCCESS"
  }
}
```

### 9. Process Refund Response

```json
{
  "success": true,
  "message": "Refund processed successfully",
  "data": {
    "payment_id": 3456,
    "refund_reference": "ref_MOpx3aZq4PVRhr",
    "refund_status": "PROCESSED",
    "refund_amount": 1003.00,
    "refund_processed_date": "2025-05-15T10:22:15"
  }
}
```

### 10. Record COD Remittance Response

```json
{
  "success": true,
  "message": "COD remittance recorded successfully",
  "data": {
    "order_id": 12346,
    "cod_remittance_date": "2025-05-20",
    "settlement_reference": "COD-REM-12346-20250520",
    "amount": 8500.00
  }
}
```

## Returns and Refunds APIs

### 11. Create Return Response

```json
{
  "success": true,
  "message": "Return created successfully",
  "data": {
    "return_id": 789,
    "order_id": 12346,
    "order_item_id": 67891,
    "return_type": "DAMAGED",
    "return_amount": 850.00,
    "return_status": "INITIATED"
  }
}
```

### 12. Get Returns By Order Response

```json
{
  "success": true,
  "data": [
    {
      "return_id": 789,
      "order_id": 12346,
      "order_item_id": 67891,
      "return_type": "DAMAGED",
      "return_amount": 850.00,
      "return_reason": "Product damaged during transit",
      "return_initiated_date": "2025-05-10",
      "return_confirmed_date": null,
      "seller_confirmation_received": false,
      "seller_confirmation_date": null,
      "credit_note_id": null,
      "refund_status": "INITIATED",
      "refund_processed_date": null,
      "payment_refund_id": null,
      "created_at": "2025-05-10T15:45:22",
      "updated_at": "2025-05-10T15:45:22"
    }
  ]
}
```

### 13. Confirm Return Response

```json
{
  "success": true,
  "message": "Return confirmed successfully",
  "data": {
    "return_id": 789,
    "seller_confirmation_received": true,
    "seller_confirmation_date": "2025-05-11T09:15:10",
    "return_confirmed_date": "2025-05-11T09:15:10"
  }
}
```

### 14. Process Return Refund Response

```json
{
  "success": true,
  "message": "Return refund processed successfully",
  "data": {
    "return_id": 789,
    "refund_status": "PROCESSED",
    "refund_processed_date": "2025-05-11T09:30:22",
    "refund_amount": 850.00
  }
}
```

## Credit Note APIs

### 15. Create Credit Note For Return Response

```json
{
  "success": true,
  "message": "Credit note created successfully",
  "data": {
    "credit_note_id": 456,
    "credit_note_number": "CN-12346-20250511-123456",
    "credit_note_date": "2025-05-11T09:23:45",
    "related_invoice_number": "INV-KC-2025-0002",
    "related_invoice_date": "2025-05-10",
    "seller_id": 1001,
    "buyer_id": 5002,
    "order_id": 12346,
    "return_id": 789,
    "credit_note_reason": "Product damaged during transit",
    "credit_note_type": "RETURN",
    "total_value_before_tax": 850.00,
    "cgst_amount": 0.00,
    "sgst_amount": 0.00,
    "igst_amount": 153.00,
    "total_credit_note_value": 1003.00,
    "adjustment_status": "PENDING",
    "credit_note_items": [
      {
        "credit_note_item_id": 789,
        "credit_note_id": 456,
        "order_item_id": 67891,
        "product_id": 12346,
        "sku_id": "SKU-12346",
        "hsn_code": "1905",
        "quantity": 1,
        "unit_price": 850.00,
        "total_value": 850.00,
        "gst_rate": 18.00,
        "cgst_amount": 0.00,
        "sgst_amount": 0.00,
        "igst_amount": 153.00,
        "total_tax": 153.00,
        "item_total": 1003.00,
        "reason": "Product damaged during transit",
        "created_at": "2025-05-11T09:23:45"
      }
    ]
  }
}
```

### 16. Create Credit Note (Manual) Response

```json
{
  "success": true,
  "message": "Credit note created successfully",
  "data": {
    "credit_note_id": 457,
    "credit_note_number": "CN-12346-20250511-123456",
    "credit_note_date": "2025-05-11",
    "related_invoice_number": "INV-KC-2025-0002",
    "related_invoice_date": "2025-05-10",
    "seller_id": 1001,
    "buyer_id": 5002,
    "order_id": 12346,
    "credit_note_reason": "Price adjustment",
    "credit_note_type": "PRICE_CORRECTION",
    "total_value_before_tax": 500.00,
    "cgst_amount": 0.00,
    "sgst_amount": 0.00,
    "igst_amount": 90.00,
    "total_credit_note_value": 590.00,
    "adjustment_status": "PENDING"
  }
}
```

### 17. Get Credit Note Response

```json
{
  "success": true,
  "data": {
    "credit_note_id": 456,
    "credit_note_number": "CN-12346-20250511-123456",
    "credit_note_date": "2025-05-11T09:23:45",
    "related_invoice_number": "INV-KC-2025-0002",
    "related_invoice_date": "2025-05-10",
    "seller_id": 1001,
    "buyer_id": 5002,
    "order_id": 12346,
    "return_id": 789,
    "credit_note_reason": "Product damaged during transit",
    "credit_note_type": "RETURN",
    "total_value_before_tax": 850.00,
    "cgst_amount": 0.00,
    "sgst_amount": 0.00,
    "igst_amount": 153.00,
    "total_credit_note_value": 1003.00,
    "adjustment_status": "PENDING",
    "adjustment_date": null,
    "adjusted_in_settlement_id": null,
    "created_at": "2025-05-11T09:23:45",
    "updated_at": "2025-05-11T09:23:45",
    "credit_note_items": [
      {
        "credit_note_item_id": 789,
        "credit_note_id": 456,
        "order_item_id": 67891,
        "product_id": 12346,
        "sku_id": "SKU-12346",
        "hsn_code": "1905",
        "quantity": 1,
        "unit_price": 850.00,
        "total_value": 850.00,
        "gst_rate": 18.00,
        "cgst_amount": 0.00,
        "sgst_amount": 0.00,
        "igst_amount": 153.00,
        "total_tax": 153.00,
        "item_total": 1003.00,
        "reason": "Product damaged during transit",
        "created_at": "2025-05-11T09:23:45"
      }
    ]
  }
}
```

### 18. Get Credit Notes By Order Response

```json
{
  "success": true,
  "data": [
    {
      "credit_note_id": 456,
      "credit_note_number": "CN-12346-20250511-123456",
      "credit_note_date": "2025-05-11T09:23:45",
      "related_invoice_number": "INV-KC-2025-0002",
      "credit_note_type": "RETURN",
      "total_credit_note_value": 1003.00,
      "adjustment_status": "PENDING"
    },
    {
      "credit_note_id": 457,
      "credit_note_number": "CN-12346-20250511-123457",
      "credit_note_date": "2025-05-11",
      "related_invoice_number": "INV-KC-2025-0002",
      "credit_note_type": "PRICE_CORRECTION",
      "total_credit_note_value": 590.00,
      "adjustment_status": "PENDING"
    }
  ]
}
```

## Debit Note APIs

### 19. Create Debit Note Response

```json
{
  "success": true,
  "message": "Debit note created successfully",
  "data": {
    "debit_note_id": 123,
    "debit_note_number": "DN-12346-20250511-123456",
    "debit_note_date": "2025-05-11",
    "related_invoice_number": "INV-KC-2025-0002",
    "related_invoice_date": "2025-05-10",
    "seller_id": 1001,
    "buyer_id": 5002,
    "order_id": 12346,
    "debit_note_reason": "Incorrect discount applied",
    "debit_note_type": "PRICE_CORRECTION",
    "total_value_before_tax": 300.00,
    "cgst_amount": 0.00,
    "sgst_amount": 0.00,
    "igst_amount": 54.00,
    "total_debit_note_value": 354.00,
    "adjustment_status": "PENDING"
  }
}
```

### 20. Get Debit Note Response

```json
{
  "success": true,
  "data": {
    "debit_note_id": 123,
    "debit_note_number": "DN-12346-20250511-123456",
    "debit_note_date": "2025-05-11",
    "related_invoice_number": "INV-KC-2025-0002",
    "related_invoice_date": "2025-05-10",
    "seller_id": 1001,
    "buyer_id": 5002,
    "order_id": 12346,
    "debit_note_reason": "Incorrect discount applied",
    "debit_note_type": "PRICE_CORRECTION",
    "total_value_before_tax": 300.00,
    "cgst_amount": 0.00,
    "sgst_amount": 0.00,
    "igst_amount": 54.00,
    "total_debit_note_value": 354.00,
    "adjustment_status": "PENDING",
    "adjustment_date": null,
    "adjusted_in_settlement_id": null,
    "created_at": "2025-05-11T10:15:45",
    "updated_at": "2025-05-11T10:15:45",
    "debit_note_items": [
      {
        "debit_note_item_id": 234,
        "debit_note_id": 123,
        "order_item_id": null,
        "product_id": 12346,
        "sku_id": "SKU-12346",
        "hsn_code": "1905",
        "quantity": 1,
        "unit_price": 300.00,
        "total_value": 300.00,
        "gst_rate": 18.00,
        "cgst_amount": 0.00,
        "sgst_amount": 0.00,
        "igst_amount": 54.00,
        "total_tax": 54.00,
        "item_total": 354.00,
        "reason": "Discount correction",
        "created_at": "2025-05-11T10:15:45"
      }
    ]
  }
}
```

### 21. Get Debit Notes By Order Response

```json
{
  "success": true,
  "data": [
    {
      "debit_note_id": 123,
      "debit_note_number": "DN-12346-20250511-123456",
      "debit_note_date": "2025-05-11",
      "related_invoice_number": "INV-KC-2025-0002",
      "debit_note_type": "PRICE_CORRECTION",
      "total_debit_note_value": 354.00,
      "adjustment_status": "PENDING"
    }
  ]
}
```

## Settlement APIs

### 22. Generate Settlement Response

```json
{
  "success": true,
  "message": "Settlement generated successfully",
  "data": {
    "settlement_id": 789,
    "seller_id": 1001,
    "settlement_period_start": "2025-05-01",
    "settlement_period_end": "2025-05-31",
    "invoice_number": "INV-KC-1001-20250601-123456",
    "invoice_date": "2025-06-01",
    "payment_status": "PENDING"
  }
}
```

### 23. Get Settlement Response

```json
{
  "success": true,
  "data": {
    "settlement_id": 789,
    "seller_id": 1001,
    "settlement_period_start": "2025-05-01",
    "settlement_period_end": "2025-05-31",
    "invoice_number": "INV-KC-1001-20250601-123456",
    "invoice_date": "2025-06-01",
    "total_order_value": 45000.00,
    "total_commission": 2250.00,
    "total_oms_charges": 10.00,
    "total_delivery_charges": 2500.00,
    "total_delivery_gst": 450.00,
    "total_cod_charges": 150.00,
    "total_payment_gateway_charges": 450.00,
    "total_rto_charges": 250.00,
    "total_kc_discounts": 1500.00,
    "total_seller_discounts": 750.00,
    "total_returns_refunds": 3000.00,
    "total_credit_notes": 3540.00,
    "total_debit_notes": 500.00,
    "total_tds_amount": 450.00,
    "total_tcs_amount": 42.00,
    "service_invoice_amount": 5360.00,
    "service_invoice_cgst": 0.00,
    "service_invoice_sgst": 0.00,
    "service_invoice_igst": 964.80,
    "service_invoice_total": 6324.80,
    "net_payable_to_supplier": 36273.20,
    "payment_status": "PENDING",
    "payment_date": null,
    "created_at": "2025-06-01T00:05:22",
    "updated_at": "2025-06-01T10:45:15",
    "settlement_orders": [
      {
        "settlement_order_id": 1001,
        "settlement_id": 789,
        "order_id": 12345,
        "invoice_number": "INV-KC-2025-0001",
        "order_date": "2025-05-10",
        "order_value": 11210.00,
        "total_gst_amount": 1710.00,
        "commission_amount": 500.00,
        "oms_charge": 2.00,
        "delivery_charge": 500.00,
        "delivery_gst": 77.04,
        "payment_gateway_charge": 112.10,
        "rto_charge": 0.00,
        "kc_discount": 500.00,
        "seller_discount": 0.00,
        "returns_refunds": 1003.00,
        "tds_amount": 100.00,
        "tcs_amount": 11.21,
        "net_payable": 10450.00
      }
    ],
    "settlement_notes": [
      {
        "settlement_note_id": 501,
        "settlement_id": 789,
        "credit_note_id": 456,
        "debit_note_id": null,
        "note_type": "CREDIT",
        "note_number": "CN-12346-20250511-123456",
        "note_date": "2025-05-11",
        "note_amount": 1003.00
      },
      {
        "settlement_note_id": 502,
        "settlement_id": 789,
        "credit_note_id": null,
        "debit_note_id": 123,
        "note_type": "DEBIT",
        "note_number": "DN-12346-20250511-123456",
        "note_date": "2025-05-11",
        "note_amount": 354.00
      }
    ]
  }
}
```

### 24. Get Settlements By Seller Response

```json
{
  "success": true,
  "data": [
    {
      "settlement_id": 789,
      "seller_id": 1001,
      "settlement_period_start": "2025-05-01",
      "settlement_period_end": "2025-05-31",
      "invoice_number": "INV-KC-1001-20250601-123456",
      "invoice_date": "2025-06-01",
      "total_order_value": 45000.00,
      "net_payable_to_supplier": 36273.20,
      "payment_status": "PENDING",
      "created_at": "2025-06-01T00:05:22"
    },
    {
      "settlement_id": 788,
      "seller_id": 1001,
      "settlement_period_start": "2025-04-01",
      "settlement_period_end": "2025-04-30",
      "invoice_number": "INV-KC-1001-20250501-123455",
      "invoice_date": "2025-05-01",
      "total_order_value": 38750.00,
      "net_payable_to_supplier": 31250.00,
      "payment_status": "PAID",
      "payment_date": "2025-05-05",
      "created_at": "2025-05-01T00:10:33"
    }
  ]
}
```

### 25-27. Add Order/Credit Note/Debit Note To Settlement Response

```json
{
  "success": true,
  "message": "Item added to settlement successfully",
  "data": {
    "settlement_id": 789,
    "updated_net_payable": 36273.20
  }
}
```

### 28. Finalize Settlement Response

```json
{
  "success": true,
  "message": "Settlement finalized successfully",
  "data": {
    "settlement_id": 789,
    "service_invoice_amount": 5360.00,
    "service_invoice_cgst": 0.00,
    "service_invoice_sgst": 0.00,
    "service_invoice_igst": 964.80,
    "service_invoice_total": 6324.80,
    "net_payable_to_supplier": 36273.20,
    "payment_status": "READY_FOR_PAYMENT"
  }
}
```

### 29. Mark Settlement as Paid Response

```json
{
  "success": true,
  "message": "Settlement marked as paid successfully",
  "data": {
    "settlement_id": 789,
    "payment_status": "PAID",
    "payment_date": "2025-06-05"
  }
}
```

## Additional Utilities

# API Response Examples (Final Part)

## Additional Utilities (Continued)

### 30. Get HSN Code Tax Rates Response

```json
{
  "success": true,
  "data": {
    "hsn_id": 456,
    "hsn_code": "1905",
    "hsn_description": "Bread, pastry, cakes, biscuits and other bakers' wares",
    "gst_rate": 18.00,
    "cgst_rate": 9.00,
    "sgst_rate": 9.00,
    "igst_rate": 18.00,
    "cess_rate": 0.00,
    "effective_from": "2022-01-01",
    "effective_to": null,
    "created_at": "2022-01-01T00:00:00",
    "updated_at": "2022-01-01T00:00:00"
  }
}
```

### 31. Get Financial Audit Logs Response

```json
{
  "success": true,
  "data": [
    {
      "log_id": 12345,
      "table_name": "credit_note",
      "record_id": 456,
      "action_type": "UPDATE",
      "field_name": "adjustment_status",
      "old_value": "PENDING",
      "new_value": "ADJUSTED",
      "changed_by": "admin.user",
      "change_date": "2025-05-31T15:45:22",
      "reason": "Adjusted in monthly settlement"
    },
    {
      "log_id": 12344,
      "table_name": "credit_note",
      "record_id": 456,
      "action_type": "CREATE",
      "field_name": "all",
      "old_value": "",
      "new_value": "New credit note created",
      "changed_by": "system",
      "change_date": "2025-05-11T09:23:45",
      "reason": "Created from return ID 789"
    }
  ]
}
```

### 32. Create Audit Log Entry Response

```json
{
  "success": true,
  "message": "Audit log entry created successfully",
  "data": {
    "log_id": 12346
  }
}
```

## Complex Operational Examples

### 33. Full Order Processing Example (COD) Response

For the first step (Create Order Financials):
```json
{
  "success": true,
  "message": "Order financials created successfully",
  "data": {
    "order_id": 12346,
    "order_financial_id": 5679
  }
}
```

For the second step (Record COD Collection):
```json
{
  "success": true,
  "message": "COD payment recorded successfully",
  "data": {
    "payment_id": 3457,
    "order_id": 12346,
    "transaction_reference": "COD-12346-20250510-145622",
    "payment_status": "SUCCESS",
    "amount": 8968.00
  }
}
```

For the third step (Record COD Remittance):
```json
{
  "success": true,
  "message": "COD remittance recorded successfully",
  "data": {
    "order_id": 12346,
    "cod_remittance_date": "2025-05-20",
    "settlement_reference": "COD-REM-12346-20250520",
    "amount": 8968.00
  }
}
```

### 34. Full Return and Credit Note Process Response

For the first step (Create Return):
```json
{
  "success": true,
  "message": "Return created successfully",
  "data": {
    "return_id": 790,
    "order_id": 12345,
    "order_item_id": 67890,
    "return_type": "DAMAGED",
    "return_amount": 1003.00,
    "return_status": "INITIATED"
  }
}
```

For the second step (Confirm Return):
```json
{
  "success": true,
  "message": "Return confirmed successfully",
  "data": {
    "return_id": 790,
    "seller_confirmation_received": true,
    "seller_confirmation_date": "2025-05-15T10:15:10",
    "return_confirmed_date": "2025-05-15T10:15:10"
  }
}
```

For the third step (Generate Credit Note):
```json
{
  "success": true,
  "message": "Credit note created successfully",
  "data": {
    "credit_note_id": 458,
    "credit_note_number": "CN-12345-20250515-123458",
    "credit_note_date": "2025-05-15T10:22:35",
    "related_invoice_number": "INV-KC-2025-0001",
    "related_invoice_date": "2025-05-10",
    "seller_id": 1001,
    "buyer_id": 5001,
    "order_id": 12345,
    "return_id": 790,
    "credit_note_reason": "Product damaged during transit",
    "credit_note_type": "RETURN",
    "total_value_before_tax": 850.00,
    "cgst_amount": 0.00,
    "sgst_amount": 0.00,
    "igst_amount": 153.00,
    "total_credit_note_value": 1003.00,
    "adjustment_status": "PENDING"
  }
}
```

For the fourth step (Process Refund):
```json
{
  "success": true,
  "message": "Refund processed successfully",
  "data": {
    "payment_id": 3456,
    "refund_reference": "ref_QA5x3aZq4PVRhy",
    "refund_status": "PROCESSED",
    "refund_amount": 1003.00,
    "refund_processed_date": "2025-05-15T10:24:18"
  }
}
```

### 35. Complete Monthly Settlement Process Response

For the first step (Generate Settlement):
```json
{
  "success": true,
  "message": "Settlement generated successfully",
  "data": {
    "settlement_id": 789,
    "seller_id": 1001,
    "settlement_period_start": "2025-05-01",
    "settlement_period_end": "2025-05-31",
    "invoice_number": "INV-KC-1001-20250601-123456",
    "invoice_date": "2025-06-01",
    "payment_status": "PENDING"
  }
}
```

For each subsequent step (Add Order/Credit Note/Debit Note):
```json
{
  "success": true,
  "message": "Item added to settlement successfully",
  "data": {
    "settlement_id": 789,
    "updated_net_payable": 36273.20
  }
}
```

For the finalize step:
```json
{
  "success": true,
  "message": "Settlement finalized successfully",
  "data": {
    "settlement_id": 789,
    "service_invoice_amount": 5360.00,
    "service_invoice_cgst": 0.00,
    "service_invoice_sgst": 0.00,
    "service_invoice_igst": 964.80,
    "service_invoice_total": 6324.80,
    "net_payable_to_supplier": 36273.20,
    "payment_status": "READY_FOR_PAYMENT"
  }
}
```

For the final step (Mark as Paid):
```json
{
  "success": true,
  "message": "Settlement marked as paid successfully",
  "data": {
    "settlement_id": 789,
    "payment_status": "PAID",
    "payment_date": "2025-06-05"
  }
}
```

## Error Response Examples

### 1. Validation Error

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": {
      "order_id": ["Order ID is required"],
      "supplier_unit_price": ["Must be a positive number"]
    }
  }
}
```

### 2. Resource Not Found Error

```json
{
  "success": false,
  "error": {
    "code": "NOT_FOUND",
    "message": "Credit note not found for ID 9999"
  }
}
```

### 3. Business Rule Violation Error

```json
{
  "success": false,
  "error": {
    "code": "BUSINESS_RULE_VIOLATION",
    "message": "Return ID 789 already has a credit note"
  }
}
```

### 4. Database Error

```json
{
  "success": false,
  "error": {
    "code": "DATABASE_ERROR",
    "message": "Failed to create order financials"
  }
}
```

### 5. Permission Error

```json
{
  "success": false,
  "error": {
    "code": "PERMISSION_DENIED",
    "message": "You do not have permission to finalize this settlement"
  }
}
```

### 6. API Rate Limit Error

```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "API rate limit exceeded. Try again in 60 seconds"
  }
}
```

### 7. Settlement Period Conflict Error

```json
{
  "success": false,
  "error": {
    "code": "CONFLICT",
    "message": "A settlement already exists for seller 1001 that overlaps with the period 2025-05-01 to 2025-05-31"
  }
}
```

### 8. Invalid Credit Note Adjustment Error

```json
{
  "success": false,
  "error": {
    "code": "INVALID_STATE",
    "message": "Credit note ID 456 is already adjusted"
  }
}
```

### 9. System Error

```json
{
  "success": false,
  "error": {
    "code": "INTERNAL_SERVER_ERROR",
    "message": "An unexpected error occurred. Please try again later"
  }
}
```