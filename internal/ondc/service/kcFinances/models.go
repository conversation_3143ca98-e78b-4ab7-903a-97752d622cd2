package kcFinances

import "time"

type AllDiscounts struct {
	TotalDiscount    float64 `json:"total_discount"`
	PlatformDiscount float64 `json:"platform_discount"`
	SellerDiscount   float64 `json:"seller_discount"`
	PlatformCashback float64 `json:"platform_cashback"`
	PaymentCashback  float64 `json:"payment_cashback"`
	MarkdownDiscount float64 `json:"markdown_discount"`
}

type Tax struct {
	CartValue      float64 `json:"cart_value"` // cart value
	TaxableValue   float64 `json:"taxable_value"`
	CGSTTotal      float64 `json:"cgst_total"`
	SGSTTotal      float64 `json:"sgst_total"`
	IGSTTotal      float64 `json:"igst_total"`
	CESSTotal      float64 `json:"cess_total"`
	TotalGSTAmount float64 `json:"total_gst_amount"`
	OrderTotal     float64 `json:"order_total"` //  deprecated, use invoice value
	InvoiceValue   float64 `json:"invoice_value"`
	OrderValue     float64 `json:"order_value"`
}

// FinancialMasterData represents the complete financial data structure for orders
type FinancialMasterData struct {
	// Order Information
	OrderID             int64      `json:"order_id"`
	UserID              int64      `json:"user_id"`
	KCF                 *bool      `json:"kcf,omitempty"`
	Seller              string     `json:"seller"`
	DisplayStatus       *string    `json:"display_status,omitempty"`
	ShipmentStatus      *string    `json:"shipment_status,omitempty"`
	LastStatusUpdatedAt *time.Time `json:"last_status_updated_at,omitempty"`

	// Order Timeline
	OrderPlaced          *time.Time `json:"order_placed,omitempty"`
	OrderConfirmed       *time.Time `json:"order_confirmed,omitempty"`
	OrderShipmentCreated *time.Time `json:"order_shipment_created,omitempty"`
	OrderDispatched      *time.Time `json:"order_dispatched,omitempty"`
	OrderDelivered       *time.Time `json:"order_delivered,omitempty"`
	OrderRTODelivered    *time.Time `json:"order_rto_delivered,omitempty"`

	// Invoice Information
	InvoiceNumber *string    `json:"invoice_number,omitempty"`
	InvoiceDate   *time.Time `json:"invoice_date,omitempty"`

	// Value Information
	CartValue    *float64 `json:"cart_value,omitempty"`
	OrderValue   *float64 `json:"order_value,omitempty"`
	InvoiceValue *float64 `json:"invoice_value,omitempty"`

	// Discount Information
	SellerDiscount   *float64 `json:"seller_discount,omitempty"`
	PlatformDiscount *float64 `json:"platform_discount,omitempty"`
	PaymentDiscount  *float64 `json:"payment_discount,omitempty"`
	MarkdownDiscount *float64 `json:"markdown_discount,omitempty"`
	PlatformCashback *float64 `json:"platform_cashback,omitempty"`
	TotalDiscount    *float64 `json:"total_discount,omitempty"`

	// Tax Information
	GSTINBuyer    *string  `json:"gstin_buyer,omitempty"`
	GSTINSeller   *string  `json:"gstin_seller,omitempty"`
	PANSeller     *string  `json:"pan_seller,omitempty"`
	PlaceOfSupply *string  `json:"place_of_supply,omitempty"`
	PlaceOfDemand *string  `json:"place_of_demand,omitempty"`
	TaxableValue  *float64 `json:"taxable_value,omitempty"`
	CGST          *float64 `json:"cgst,omitempty"`
	SGST          *float64 `json:"sgst,omitempty"`
	IGST          *float64 `json:"igst,omitempty"`
	CESS          *float64 `json:"cess,omitempty"`
	TotalGST      *float64 `json:"total_gst,omitempty"`

	// Commission and Charges
	Commission *float64 `json:"commission,omitempty"`
	SaasCharge *float64 `json:"saas_charge,omitempty"`
	TDS        *float64 `json:"tds,omitempty"`
	TCS        *float64 `json:"tcs,omitempty"`

	// Payment Information
	PaymentType        *string  `json:"payment_type,omitempty"`
	AdvanceCollected   *float64 `json:"advance_collected,omitempty"`
	CashCollectedBy3PL *float64 `json:"cash_collected_by_3pl,omitempty"`
	PG                 *string  `json:"pg,omitempty"`

	// Payment Gateway Charges
	PlatformPGCharges    *float64 `json:"platform_pg_charges,omitempty"`
	PlatformPGChargesTax *float64 `json:"platform_pg_charges_tax,omitempty"`
	SellerPGCharges      *float64 `json:"seller_pg_charges,omitempty"`

	// COD Charges
	PlatformCODCharges    *float64   `json:"platform_cod_charges,omitempty"`
	PlatformCODChargesTax *float64   `json:"platform_cod_charges_tax,omitempty"`
	SellerCODCharges      *float64   `json:"seller_cod_charges,omitempty"`
	CODRemittanceDate     *time.Time `json:"cod_remittance_date,omitempty"`

	// 3PL Charges
	Platform3PLCharges    *float64 `json:"platform_3pl_charges,omitempty"`
	Platform3PLChargesTax *float64 `json:"platform_3pl_charges_tax,omitempty"`
	Seller3PLCharges      *float64 `json:"seller_3pl_charges,omitempty"`

	// Settlement Information
	NetPayableToSupplier      *float64   `json:"net_payable_to_supplier,omitempty"`
	FinalSettlementToSupplier *float64   `json:"final_settlement_to_supplier,omitempty"`
	FinalSettlementDate       *time.Time `json:"final_settlement_date,omitempty"`

	// Refund Information
	PlatformRefund          *float64 `json:"platform_refund,omitempty"`
	PlatformRefundCharges   *float64 `json:"platform_refund_charges,omitempty"`
	PlatformRefundChargeTax *float64 `json:"platform_refund_charge_tax,omitempty"`
	SellerRefund            *float64 `json:"seller_refund,omitempty"`
	SellerRefundCharges     *float64 `json:"seller_refund_charges,omitempty"`
	SellerRefundChargesTax  *float64 `json:"seller_refund_charges_tax,omitempty"`

	// Logistics Information
	ThirdPartyLogistics  *string  `json:"3pl,omitempty"`
	AWBNumber            *string  `json:"awb_number,omitempty"`
	ThirdPLChargedWeight *float64 `json:"3pl_charged_weight,omitempty"`
	ThirdPLZone          *string  `json:"3pl_zone,omitempty"`
}
