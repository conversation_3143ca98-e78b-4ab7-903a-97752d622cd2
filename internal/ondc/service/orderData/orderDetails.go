package orderdata

import (
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/repositories/sqlRepo"
)

func GetOrderDetails(sqlRepo *sqlRepo.Repository, orderId string) (*dao.KiranaBazarOrderDetails, error) {
	data := []dao.KiranaBazarOrderDetail{}
	query := fmt.Sprintf("select kod.order_details from kiranabazar_order_details kod where kod.order_id = '%s'", orderId)
	_, err := sqlRepo.CustomQuery(&data, query)
	if err != nil {
		return nil, err
	}

	if len(data) > 0 {
		orderDetails := dao.KiranaBazarOrderDetails{}
		err = json.Unmarshal(data[0].OrderDetails, &orderDetails)
		if err != nil {
			return nil, err
		}
		return &orderDetails, nil
	}
	return nil, nil
}

func GetOrderInfo(repo *sqlRepo.Repository, orderId int64) (orderInfo dao.KiranaBazarOrder, err error) {
	_, err = repo.Find(map[string]interface{}{
		"id": orderId,
	}, &orderInfo)
	return
}
