package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/logistics/couriers"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	"kc/internal/ondc/utils"
	"slices"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/mixpanel/mixpanel-go"
	"gorm.io/datatypes"
)

var idCounter uint64 = 0
var serverID uint64 = 1

// agentCache is a struct to hold the agent cache
type agentCache struct {
	agentsIdMap     map[string]*dao.CSAgent  // map of agent ID to agent data
	agentsEmailMap  map[string]*dao.CSAgent  // map of agent email to agent data
	sellerAgentsMap map[string][]dao.CSAgent // map of seller code to agent data
	mutex           sync.RWMutex             // mutex to protect the map
	expiresAt       time.Time                // cache expiration time
}

type ticketCache struct {
	ticketCategoryIdMap   map[int]*dao.CsTicketCategory    // map of agent ID to agent data
	ticketCategoryCodeMap map[string]*dao.CsTicketCategory // map of agent email to agent data
	mutex                 sync.RWMutex                     // mutex to protect the map
	expiresAt             time.Time                        // cache expiration time
}

// cacheExpiration is the duration after which the cache expires
const cacheExpiration = 10 * time.Minute

// agentCacheInstance is the singleton instance of agentCache
var agentCacheInstance *agentCache
var ticketCacheInstance *ticketCache

// initAgentCache initializes the agent cache
func (s *Service) initCache() {
	if agentCacheInstance == nil {
		agentCacheInstance = &agentCache{
			agentsIdMap:     make(map[string]*dao.CSAgent),
			agentsEmailMap:  make(map[string]*dao.CSAgent),
			sellerAgentsMap: make(map[string][]dao.CSAgent),
			expiresAt:       time.Now().Add(cacheExpiration),
		}
	}
	if ticketCacheInstance == nil {
		ticketCacheInstance = &ticketCache{
			ticketCategoryIdMap:   make(map[int]*dao.CsTicketCategory),
			ticketCategoryCodeMap: make(map[string]*dao.CsTicketCategory),
			expiresAt:             time.Now().Add(cacheExpiration),
		}
	}
}

// refreshAgentCache refreshes the agent cache by fetching all agents from the database
// This is only used by GetAgentsByIDs for batch operations when the cache is expired
func (s *Service) refreshAgentCache(ctx context.Context) error {
	agentCacheInstance.mutex.Lock()
	defer agentCacheInstance.mutex.Unlock()

	// Reset the cache
	agentCacheInstance.agentsIdMap = make(map[string]*dao.CSAgent)
	agentCacheInstance.agentsEmailMap = make(map[string]*dao.CSAgent)
	agentCacheInstance.sellerAgentsMap = make(map[string][]dao.CSAgent)
	agentCacheInstance.expiresAt = time.Now().Add(cacheExpiration)

	// Fetch all agents from the database
	query := `SELECT * FROM cs_agents WHERE active = 1`
	var agents []*dao.CSAgent
	_, err := s.repository.CustomQuery(&agents, query)
	if err != nil {
		return fmt.Errorf("failed to fetch agents: %w", err)
	}

	// Populate the cache
	for _, agent := range agents {
		agentCacheInstance.agentsIdMap[agent.ID] = agent
		agentCacheInstance.agentsEmailMap[agent.Email] = agent
		if agent.Seller != nil {
			agentCacheInstance.sellerAgentsMap[*agent.Seller] = append(agentCacheInstance.sellerAgentsMap[*agent.Seller], *agent)
		}
	}

	return nil
}

func (s *Service) refreshTicketCache(ctx context.Context) error {
	s.initCache()

	ticketCacheInstance.mutex.Lock()
	defer ticketCacheInstance.mutex.Unlock()

	// Reset the cache
	ticketCacheInstance.ticketCategoryIdMap = make(map[int]*dao.CsTicketCategory)
	ticketCacheInstance.ticketCategoryCodeMap = make(map[string]*dao.CsTicketCategory)
	ticketCacheInstance.expiresAt = time.Now().Add(cacheExpiration)

	// Fetch all agents from the database
	query := `SELECT * FROM cs_ticket_category`
	var ticketCategory []*dao.CsTicketCategory
	_, err := s.repository.CustomQuery(&ticketCategory, query)
	if err != nil {
		return fmt.Errorf("failed to fetch tickets category: %w", err)
	}

	// Populate the cache
	for _, category := range ticketCategory {
		ticketCacheInstance.ticketCategoryIdMap[category.ID] = category
		ticketCacheInstance.ticketCategoryCodeMap[category.Code] = category
	}

	return nil
}

// GetTicketCategoryByID fetches a ticket category by ID, using the cache if possible
func (s *Service) GetTicketCategoryByID(ctx context.Context, categoryID int) (*dao.CsTicketCategory, error) {
	// Initialize the cache if it doesn't exist
	s.initCache()

	// First check if the category is in the cache and cache is not expired
	ticketCacheInstance.mutex.RLock()
	if !time.Now().After(ticketCacheInstance.expiresAt) {
		if category, exists := ticketCacheInstance.ticketCategoryIdMap[categoryID]; exists {
			ticketCacheInstance.mutex.RUnlock()
			return category, nil
		}
	}
	ticketCacheInstance.mutex.RUnlock()

	if err := s.refreshTicketCache(ctx); err != nil {
		return nil, fmt.Errorf("failed to refresh ticket cache: %w", err)
	}

	ticketCacheInstance.mutex.Lock()
	defer ticketCacheInstance.mutex.Unlock()

	// Check if category exists after refresh
	if category, exists := ticketCacheInstance.ticketCategoryIdMap[categoryID]; exists {
		return category, nil
	}

	// Category not found even after refresh
	return nil, fmt.Errorf("ticket category with ID %d not found", categoryID)
}

// GetTicketCategoryByCode fetches a ticket category by code, using the cache if possible
func (s *Service) GetTicketCategoryByCode(ctx context.Context, categoryCode string) (*dao.CsTicketCategory, error) {
	// Initialize the cache if it doesn't exist
	s.initCache()

	// First check if the category is in the cache and cache is not expired
	ticketCacheInstance.mutex.RLock()
	if !time.Now().After(ticketCacheInstance.expiresAt) {
		if category, exists := ticketCacheInstance.ticketCategoryCodeMap[categoryCode]; exists {
			ticketCacheInstance.mutex.RUnlock()
			return category, nil
		}
	}
	ticketCacheInstance.mutex.RUnlock()

	// Refresh the cache
	if err := s.refreshTicketCache(ctx); err != nil {
		return nil, fmt.Errorf("failed to refresh ticket cache: %w", err)
	}

	ticketCacheInstance.mutex.Lock()
	defer ticketCacheInstance.mutex.Unlock()

	// Check if category exists after refresh
	if category, exists := ticketCacheInstance.ticketCategoryCodeMap[categoryCode]; exists {
		return category, nil
	}

	// Category not found even after refresh
	return nil, fmt.Errorf("ticket category with code %s not found", categoryCode)
}

// GetAgentsByIDs fetches multiple agents by their IDs, using the cache where possible
func (s *Service) GetAgentsByIDs(ctx context.Context, agentIDs []string) (map[string]*dao.CSAgent, error) {
	// Initialize result map and track missing IDs
	result := make(map[string]*dao.CSAgent)
	missingIDs := []string{}

	// Initialize the cache if it doesn't exist
	s.initCache()

	// Check if the cache is expired
	agentCacheInstance.mutex.RLock()
	if time.Now().After(agentCacheInstance.expiresAt) {
		agentCacheInstance.mutex.RUnlock()
		if err := s.refreshAgentCache(ctx); err != nil {
			return nil, fmt.Errorf("failed to refresh agent cache: %w", err)
		}
		agentCacheInstance.mutex.RLock()
	}

	// Get agents from cache and identify missing IDs
	for _, id := range agentIDs {
		if agent, exists := agentCacheInstance.agentsIdMap[id]; exists {
			result[id] = agent
		} else {
			missingIDs = append(missingIDs, id)
		}
	}
	agentCacheInstance.mutex.RUnlock()

	// If all agents were found in the cache, return them
	if len(missingIDs) == 0 {
		return result, nil
	}

	// Fetch missing agents from the database
	agentCacheInstance.mutex.Lock()
	defer agentCacheInstance.mutex.Unlock()

	// Re-check if any of the missing IDs are now in cache
	// This handles the case where another goroutine updated the cache
	updatedMissingIDs := []string{}
	for _, id := range missingIDs {
		if agent, exists := agentCacheInstance.agentsIdMap[id]; exists {
			result[id] = agent
		} else {
			updatedMissingIDs = append(updatedMissingIDs, id)
		}
	}
	missingIDs = updatedMissingIDs

	// Only query database if there are still missing IDs
	if len(missingIDs) > 0 {
		query := fmt.Sprintf(`SELECT * FROM cs_agents WHERE id IN ('%s') AND active = 1`, strings.Join(missingIDs, "','"))
		var agents []*dao.CSAgent
		_, err := s.repository.CustomQuery(&agents, query)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch agents: %w", err)
		}

		// Add fetched agents to both the cache and the result
		for _, agent := range agents {
			agentCacheInstance.agentsIdMap[agent.ID] = agent
			// Assuming there's an email map as discussed in GetAgentByEmail
			if agent.Email != "" {
				agentCacheInstance.agentsEmailMap[agent.Email] = agent
			}
			result[agent.ID] = agent
		}
	}

	return result, nil
}

// GetAgentByID fetches an agent by ID, using the cache if possible
func (s *Service) GetAgentByID(ctx context.Context, agentID string) (*dao.CSAgent, error) {
	// Initialize the cache if it doesn't exist
	s.initCache()

	// First check if the agent is in the cache and cache is not expired
	agentCacheInstance.mutex.RLock()
	if !time.Now().After(agentCacheInstance.expiresAt) {
		if agent, exists := agentCacheInstance.agentsIdMap[agentID]; exists {
			agentCacheInstance.mutex.RUnlock()
			return agent, nil
		}
	}
	agentCacheInstance.mutex.RUnlock()

	// Refresh the cache
	if err := s.refreshAgentCache(ctx); err != nil {
		return nil, fmt.Errorf("failed to refresh agent cache: %w", err)
	}
	// Need to fetch from database, so acquire write lock
	agentCacheInstance.mutex.Lock()
	defer agentCacheInstance.mutex.Unlock()

	// Check if agent exists after refresh
	if agent, exists := agentCacheInstance.agentsIdMap[agentID]; exists {
		return agent, nil
	}

	// Agent not found even after refresh
	return nil, fmt.Errorf("agent with ID %s not found", agentID)
}

// GetAgentByEmail fetches an agent by email, using the cache if possible
func (s *Service) GetAgentByEmail(ctx context.Context, email string) (*dao.CSAgent, error) {
	// Initialize the cache if it doesn't exist
	s.initCache()

	// First check if the agent is in the cache and cache is not expired
	agentCacheInstance.mutex.RLock()
	if !time.Now().After(agentCacheInstance.expiresAt) {
		// Assuming there's an agentsEmailMap in the cache structure
		if agent, exists := agentCacheInstance.agentsEmailMap[email]; exists {
			agentCacheInstance.mutex.RUnlock()
			return agent, nil
		}
	}
	agentCacheInstance.mutex.RUnlock()
	// Need to fetch from database, so acquire write lock

	// Refresh the cache
	if err := s.refreshAgentCache(ctx); err != nil {
		return nil, fmt.Errorf("failed to refresh agent cache: %w", err)
	}

	agentCacheInstance.mutex.Lock()
	defer agentCacheInstance.mutex.Unlock()

	// Check if agent exists after refresh
	if agent, exists := agentCacheInstance.agentsEmailMap[email]; exists {
		return agent, nil
	}

	// If not found in cache after refresh, try a direct database query
	query := fmt.Sprintf(`SELECT * FROM cs_agents WHERE email = '%s' AND active = 1 LIMIT 1`, email)
	var agents []*dao.CSAgent
	_, err := s.repository.CustomQuery(&agents, query)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch agent by email: %w", err)
	}

	if len(agents) == 0 {
		return nil, fmt.Errorf("agent with email %s not found", email)
	}

	// Add the found agent to the cache
	agent := agents[0]
	agentCacheInstance.agentsIdMap[agent.ID] = agent
	agentCacheInstance.agentsEmailMap[email] = agent

	return agent, nil
}

// GetAgentByID fetches an agent by ID, using the cache if possible
func (s *Service) GetAgentBySeller(ctx context.Context, seller string) ([]dao.CSAgent, error) {
	// Initialize the cache if it doesn't exist
	s.initCache()

	// First check if the agent is in the cache and cache is not expired
	agentCacheInstance.mutex.RLock()
	if !time.Now().After(agentCacheInstance.expiresAt) {
		if agent, exists := agentCacheInstance.sellerAgentsMap[seller]; exists {
			agentCacheInstance.mutex.RUnlock()
			return agent, nil
		}
	}
	agentCacheInstance.mutex.RUnlock()

	// Refresh the cache
	if err := s.refreshAgentCache(ctx); err != nil {
		return nil, fmt.Errorf("failed to refresh agent cache: %w", err)
	}
	// Need to fetch from database, so acquire write lock
	agentCacheInstance.mutex.Lock()
	defer agentCacheInstance.mutex.Unlock()

	// Check if agent exists after refresh
	if agent, exists := agentCacheInstance.sellerAgentsMap[seller]; exists {
		return agent, nil
	}

	// Agent not found even after refresh
	return nil, fmt.Errorf("agent with ID %s not found", seller)
}

// ClearAgentCache clears the agent cache, forcing a refresh on the next call
func (s *Service) ClearCache() {
	if agentCacheInstance != nil {
		agentCacheInstance.mutex.Lock()
		defer agentCacheInstance.mutex.Unlock()
		agentCacheInstance.agentsIdMap = make(map[string]*dao.CSAgent)
		agentCacheInstance.expiresAt = time.Now()
	}
	if ticketCacheInstance != nil {
		ticketCacheInstance.mutex.Lock()
		defer ticketCacheInstance.mutex.Unlock()
		ticketCacheInstance.ticketCategoryIdMap = make(map[int]*dao.CsTicketCategory)
		ticketCacheInstance.ticketCategoryCodeMap = make(map[string]*dao.CsTicketCategory)
		ticketCacheInstance.expiresAt = time.Now()
	}
}

func (s *Service) getAssigneeFilterCondition(ctx context.Context, assignees []string) (string, error) {
	var assigneeIDs []string
	var ownershipStates []string

	for _, assignee := range assignees {
		switch assignee {
		case "seller":
			// For seller, we'll filter by ownership state
			ownershipStates = append(ownershipStates, utils.AGENT_TEAM_SELLER)

		case "tech", "ops", "cs":
			// Get team leader ID based on email mapping
			teamEmailMap := map[string]string{
				"cs":   "<EMAIL>",
				"tech": "<EMAIL>",
				"ops":  "<EMAIL>",
			}

			email := teamEmailMap[assignee]
			agentData, err := s.GetAgentByEmail(ctx, email)
			if err != nil {
				return "", fmt.Errorf("error fetching agent by email for team %s: %v", assignee, err)
			}

			assigneeIDs = append(assigneeIDs, agentData.ID)

		default:
			// Treat as direct email address
			agentData, err := s.GetAgentByEmail(ctx, assignee)
			if err != nil {
				return "", fmt.Errorf("error fetching agent by email %s: %v", assignee, err)
			}

			assigneeIDs = append(assigneeIDs, agentData.ID)
		}
	}

	// Build the filter condition
	var conditions []string

	if len(assigneeIDs) > 0 {
		assigneeIDsList := strings.Join(assigneeIDs, "','")
		// Direct assignee match
		conditions = append(conditions, fmt.Sprintf("t.current_assignee_id in ('%s')", assigneeIDsList))
		// If assignee is null, check owner instead
		conditions = append(conditions, fmt.Sprintf("(t.current_assignee_id is null and t.primary_owner_id in ('%s'))", assigneeIDsList))
	}

	if len(ownershipStates) > 0 {
		conditions = append(conditions, fmt.Sprintf("t.ownership_state in ('%s')", strings.Join(ownershipStates, "','")))
	}

	// If no conditions, return empty string
	if len(conditions) == 0 {
		return "", nil
	}

	// Join all conditions with OR
	return " and (" + strings.Join(conditions, " or ") + ")", nil
}

func (s *Service) GetSupportTickets(ctx context.Context, request *dto.GetSupportTicketsRequest) (*dto.GetSupportTicketsResponse, error) {
	requestSource := request.Data.Source
	query := `
		select 
			t.id as ticket_id,
			t.order_id,
			t.created_at,
			u.name as customer_name,
			u.profile_image_url as customer_image,
			ctc.name as category,
			t.updated_at as updated_at,
			kop.amount as total_amount,
			ko.display_status as order_status,
			t.status as status,
			s.name as seller,
			t.seller as seller_code,
			t.priority as priority,
			t.primary_owner_id as assigned_to,
			t.current_assignee_id as current_assignee,
			t.user_id as user_id,
			t.conversation_id as conversation_id,
			t.progress_state as progress_state,
			(
				SELECT sender_type 
				FROM cs_messages m
				WHERE m.conversation_id = t.conversation_id
				AND m.private = 0
				AND (m.visible = 1 OR m.visible IS NULL)
				ORDER BY m.created_at DESC
				LIMIT 1
			) as last_public_sender_type
		`
	if requestSource != "KC_APP" {
		query += `,
		ta.id as ticket_action_id,
		ta.status as ticket_action_status,
		ta.requested_action as requested_action,
		ta.requested_by as requested_by,
		ta.requested_at as requested_at,
		ta.requested_message_id as requested_message_id
		`
	}
	query += `
		from cs_tickets t
		left join kiranabazar_order_payments kop on t.order_id = kop.order_id
		left join users u on t.user_id = u.user_id
		left join kiranabazar_sellers s on t.seller = s.code
		left join cs_ticket_category ctc on t.category = ctc.id
		left join kiranabazar_orders ko on t.order_id = ko.id
		`
	var countByStatusQuery string
	if requestSource == "B2B_EXTERNAL" {
		countByStatusQuery = `
		select 
			case
				when ta.status = 'pending' then 'open'
				when ta.status = 'completed' then 'closed'
				else 'other'
			end as status,
			count(*) as count
		FROM cs_tickets t
		LEFT JOIN kiranabazar_order_payments kop ON t.order_id = kop.order_id
		LEFT JOIN users u ON t.user_id = u.user_id
	`
	} else {
		countByStatusQuery = `
			select
				t.status,
				count(*) as count
			FROM cs_tickets t
			LEFT JOIN kiranabazar_order_payments kop ON t.order_id = kop.order_id
			LEFT JOIN users u ON t.user_id = u.user_id
		`
	}
	if requestSource != "KC_APP" {
		joinCondition := `left join cs_ticket_actions ta on t.id = ta.ticket_id
							and ta.created_at = (
								select max(created_at)
								from cs_ticket_actions ta2
								where ta2.ticket_id = t.id
							)`
		query += joinCondition
		countByStatusQuery += joinCondition
	}

	query += `where t.active = 1 and t.archived = 0`
	countByStatusQuery += `where t.active = 1 and t.archived = 0`

	if len(request.Data.Filters.Status) > 0 {
		var filterCondition string
		if request.Data.Source == "B2B_EXTERNAL" {
			filterCondition = fmt.Sprintf(` and ta.status in ('%s')`, strings.Join(request.Data.Filters.Status, "','"))
		} else {
			filterCondition = fmt.Sprintf(` and t.status in ('%s')`, strings.Join(request.Data.Filters.Status, "','"))
		}
		query += filterCondition
	}

	if len(request.Data.Filters.Category) > 0 {
		categoryStrings := make([]string, len(request.Data.Filters.Category))
		for i, val := range request.Data.Filters.Category {
			categoryStrings[i] = fmt.Sprintf("%d", val)
		}
		filterCondition := fmt.Sprintf(` and t.category in ('%s')`, strings.Join(categoryStrings, "','"))
		query += filterCondition
		countByStatusQuery += filterCondition
	}

	if len(request.Data.Filters.Assignee) > 0 {
		filterCondition, err := s.getAssigneeFilterCondition(ctx, request.Data.Filters.Assignee)
		if err != nil {
			return nil, fmt.Errorf("error getting assignee filter condition: %v", err)
		}
		query += filterCondition
		countByStatusQuery += filterCondition
	}

	if len(request.Data.Filters.NotStatus) > 0 {
		filterCondition := fmt.Sprintf(` and t.status not in ('%s')`, strings.Join(request.Data.Filters.NotStatus, "','"))
		query += filterCondition
	}

	if request.Data.Filters.SearchKey != "" {
		searchKey := strings.TrimSpace(request.Data.Filters.SearchKey)
		filterCondition := fmt.Sprintf(` and (t.order_id = '%s' or u.phone = '%s' or u.user_id = '%s')`, searchKey, searchKey, searchKey)
		query += filterCondition
		countByStatusQuery += filterCondition
	}

	if len(request.Data.Filters.Priority) > 0 {
		filterCondition := fmt.Sprintf(` and t.priority in ('%s')`, strings.Join(request.Data.Filters.Priority, "','"))
		query += filterCondition
		countByStatusQuery += filterCondition
	}

	if requestSource == "B2B_EXTERNAL" {
		if len(request.Data.Filters.Seller) == 0 {
			return nil, fmt.Errorf("seller code is required")
		}
	}

	if requestSource == "B2B_EXTERNAL" {
		// filterCondition := fmt.Sprintln(` and ta.id is not null and t.status not in ('resolved', 'closed')`)
		filterCondition := fmt.Sprintln(` and ta.id is not null`)
		query += filterCondition
		countByStatusQuery += filterCondition
	}

	if len(request.Data.Filters.Seller) > 0 {
		filterCondition := fmt.Sprintf(` and t.seller in ('%s')`, strings.Join(request.Data.Filters.Seller, "','"))
		query += filterCondition
		countByStatusQuery += filterCondition
	}

	if requestSource == "KC_APP" && request.Data.Filters.UserID == "" {
		return nil, fmt.Errorf("user id is required")
	}

	if request.Data.Filters.UserID != "" {
		filterCondition := fmt.Sprintf(` and t.user_id = '%s'`, request.Data.Filters.UserID)
		query += filterCondition
		countByStatusQuery += filterCondition
	}

	if request.Data.Filters.SearchKey == "" {
		if request.Data.Filters.OrderAmount.Max > 0 {
			query += fmt.Sprintf(` and kop.amount >= %f and kop.amount <= %f`, request.Data.Filters.OrderAmount.Min, request.Data.Filters.OrderAmount.Max)

			timeFilterCondition := fmt.Sprintf(` and t.created_at >= %d and t.created_at <= %d`, request.Data.From, request.Data.To)
			query += timeFilterCondition
			countByStatusQuery += timeFilterCondition
		}
	}

	if request.Data.Filters.SellerTatHours > 0 {
		// 48 hours = 48 * 60 * 60 = 172800 seconds
		tatUnixMillis := time.Now().UnixMilli() - int64(request.Data.Filters.SellerTatHours*3600*1000)
		tatExceededCondition := fmt.Sprintf(` and ta.requested_at < %d and ta.status != 'completed'`, tatUnixMillis)
		query += tatExceededCondition
		countByStatusQuery += tatExceededCondition
	}

	// Add GROUP BY to the status count query
	if requestSource == "B2B_EXTERNAL" {
		countByStatusQuery += " GROUP BY case when ta.status = 'pending' then 'open' when ta.status = 'completed' then 'closed' else 'other' end"
	} else {
		countByStatusQuery += " GROUP BY t.status"
	}

	query += " ORDER BY t.created_at DESC"
	query += fmt.Sprintf(" limit %d offset %d", request.Data.Limit, request.Data.Offset)

	ticketListData := []dto.SupportTickets{}
	statusCounts := []struct {
		Status string `db:"status"`
		Count  int64  `db:"count"`
	}{}

	var wg sync.WaitGroup
	var getStatusCountsErr, getTicketListDataErr error
	wg.Add(2)

	go func() {
		defer wg.Done()
		_, getTicketListDataErr = s.ReadOnlyRepository.CustomQuery(&ticketListData, query)
	}()

	go func() {
		defer wg.Done()
		if requestSource != "KC_APP" {
			_, getStatusCountsErr = s.ReadOnlyRepository.CustomQuery(&statusCounts, countByStatusQuery)
		}
	}()

	wg.Wait()

	if getTicketListDataErr != nil {
		return nil, fmt.Errorf("failed to get ticket list data: %w", getTicketListDataErr)
	}
	if getStatusCountsErr != nil {
		return nil, fmt.Errorf("failed to get status counts: %w", getStatusCountsErr)
	}

	var orderTagsMap map[string][]string
	if requestSource == "B2B_INTERNAL" {
		var orderIDs []string
		for _, ticket := range ticketListData {
			orderIDs = append(orderIDs, fmt.Sprintf(`%d`, ticket.OrderID))
		}
		// Get concatenated tags for orders
		orderTagsMap, _ = s.getOrdersConcatenatedTags(ctx, orderIDs)
	}

	// Convert status counts array to a map
	total := 0
	statusCountMap := map[string]int64{}
	if requestSource != "B2B_EXTERNAL" {
		statusCountMap["all"] = 0
	} else {
		statusCountMap["pending"] = 0
		statusCountMap["completed"] = 0
	}
	for _, sc := range statusCounts {
		if request.Data.Source == "B2B_EXTERNAL" {
			switch sc.Status {
			case "open":
				sc.Status = "pending"
			case "closed":
				sc.Status = "completed"
			default:
				sc.Status = "other"
			}
		}
		statusCountMap[sc.Status] = sc.Count
		total += int(sc.Count)
	}
	if request.Data.Source != "B2B_EXTERNAL" {
		statusCountMap["all"] = int64(total)
	}

	if requestSource == "KC_APP" {
		return &dto.GetSupportTicketsResponse{
			Tickets: ticketListData,
		}, nil
	}

	for i := range ticketListData {
		primaryAssignee, err := s.GetAgentByID(ctx, ticketListData[i].AssignedTo)
		if err != nil {
			return nil, err
		}
		ticketListData[i].AssignedTo = primaryAssignee.Name
		if ticketListData[i].CurrentAssignee != "" {
			currentAssignee, err := s.GetAgentByID(ctx, ticketListData[i].CurrentAssignee)
			if err != nil {
				return nil, err
			}
			if currentAssignee.Team != nil && *currentAssignee.Team == utils.AGENT_TEAM_SELLER {
				if currentAssignee.Seller != nil {
					ticketListData[i].CurrentAssignee = strings.Title(strings.ReplaceAll(*currentAssignee.Seller, "_", " "))
				} else {
					ticketListData[i].CurrentAssignee = currentAssignee.Name
				}
			} else {
				// split name into first and last name
				names := strings.Split(currentAssignee.Name, " ")
				if len(names) > 1 {
					ticketListData[i].CurrentAssignee = names[0]
				} else {
					ticketListData[i].CurrentAssignee = currentAssignee.Name
				}
			}
		} else {
			ticketListData[i].CurrentAssignee = primaryAssignee.Name
		}
		if (ticketListData[i].TicketActionID != "") && (ticketListData[i].TicketActionStatus == "pending") {
			ticketListData[i].BlockSellerAssignee = true
			switch ticketListData[i].RequestedAction {
			case utils.TICKET_REQUESTED_ACTION_REFUND:
				ticketListData[i].ShowApproveButton = true
				ticketListData[i].ShowChallengeButton = true
			case utils.TICKET_REQUESTED_ACTION_CLARIFICATION:
				ticketListData[i].ShowApproveButton = true
			}
		}
		if ticketListData[i].LastPublicSenderType == utils.MESSAGE_SENDER_USER {
			ticketListData[i].PendingReply = true
		}
		if requestSource != "B2B_EXTERNAL" {
			ticketTags := []string{}
			if LOYALTY_DATA_7_MAY[ticketListData[i].UserId] || LOYALTY_DATA_5_JULY[ticketListData[i].UserId] {
				ticketTags = append(ticketTags, "loyalty_large_orders")
			}
			ticketListData[i].TicketTags = ticketTags
		}
		if requestSource == "B2B_INTERNAL" {
			orderTags, exists := orderTagsMap[fmt.Sprintf(`%d`, ticketListData[i].OrderID)]
			if exists {
				ticketListData[i].OrderTags = orderTags
			}
		}
	}

	response := dto.GetSupportTicketsResponse{
		Tickets: ticketListData,
		Stats:   statusCountMap,
	}

	return &response, nil
}

func GenerateTicketID(categoryID string, orderID int) (string, error) {
	initials := getInitials(categoryID)
	counter := atomic.AddUint64(&idCounter, 1) % 100
	ticketID := fmt.Sprintf("T%s%d%02d", initials, orderID, counter)
	return ticketID, nil
}

func getInitials(categoryID string) string {
	words := splitCamelCase(categoryID)

	var initials strings.Builder
	for _, word := range words {
		if len(word) > 0 {
			initials.WriteString(strings.ToUpper(string(word[0])))
		}
	}

	return initials.String()
}

func splitCamelCase(input string) []string {
	var words []string
	var currentWord strings.Builder

	for i, char := range input {
		if i > 0 && isUpperCase(char) && !isUpperCase(rune(input[i-1])) {
			words = append(words, currentWord.String())
			currentWord.Reset()
		}
		currentWord.WriteRune(char)
	}

	if currentWord.Len() > 0 {
		words = append(words, currentWord.String())
	}

	return words
}

func isUpperCase(r rune) bool {
	return r >= 'A' && r <= 'Z'
}

func (s *Service) AppGetSupportTickets(ctx context.Context, request *dto.AppGetCustomerSupportHome) (*map[string]interface{}, error) {
	toEpoch := time.Now().UTC().UnixMilli()
	fromEpoch := time.Now().UTC().Add(-180 * 24 * time.Hour).UnixMilli()
	supportTicketsData, err := s.GetSupportTickets(ctx, &dto.GetSupportTicketsRequest{
		Data: dto.GetSupportTicketsRequestData{
			Filters: dto.GetSupportTicketsRequestFilters{
				NotStatus: []string{},
				UserID:    request.Data.UserID,
			},
			From:   fromEpoch,
			To:     toEpoch,
			Limit:  request.Data.Limit,
			Offset: request.Data.Offset,
			Source: "KC_APP",
		},
	})
	if err != nil {
		return nil, err
	}
	appSupportTickets := []map[string]interface{}{}
	if supportTicketsData != nil && len(supportTicketsData.Tickets) > 0 {
		for _, ticket := range supportTicketsData.Tickets {
			if slices.Contains([]string{utils.TICKET_STATUS_RESOLVED, utils.TICKET_STATUS_CLOSED}, ticket.Status) && (ticket.Category == "Order Status") && (ticket.OrderStatus == displaystatus.DELIVERED) {
				continue // Skip closed or resolved tickets
			}
			appSupportTickets = append(appSupportTickets, map[string]interface{}{
				"ticket_id":       ticket.TicketID,
				"order_id":        ticket.OrderID,
				"category":        ticket.Category,
				"updated_at":      ticket.UpdatedAt,
				"order_value":     ticket.TotalAmount,
				"status":          ticket.Status,
				"seller":          ticket.Seller,
				"seller_code":     ticket.SellerCode,
				"user_id":         ticket.UserId,
				"conversation_id": ticket.ConversationID,
			})
		}

	}
	response := map[string]interface{}{
		"tickets": appSupportTickets,
	}
	return &response, nil
}

func (s *Service) AppGetSupportOrders(ctx context.Context, request *dto.AppGetCustomerSupportHome) (*map[string]interface{}, error) {
	type UserOrderData struct {
		OrderID               *string `gorm:"column:order_id" json:"order_id"`
		Seller                *string `gorm:"column:seller" json:"seller"`
		SellerCode            *string `gorm:"column:seller_code" json:"seller_code"`
		UserID                *string `gorm:"column:user_id" json:"user_id"`
		OrderValue            *string `gorm:"column:order_value" json:"order_value"`
		Status                *string `gorm:"column:status" json:"status"`
		CreatedAt             *string `gorm:"column:created_at" json:"created_at"`
		PromisedDelivery      *int64  `gorm:"column:promised_delivery" json:"promised_delivery"`
		EstimatedDelivery     *int64  `gorm:"column:estimated_delivery" json:"estimated_delivery"`
		DeliveredAt           *int64  `gorm:"column:delivered_at" json:"delivered_at"`
		ReturnedAt            *int64  `gorm:"column:returned_at" json:"returned_at"`
		SupportTicketID       *string `gorm:"column:support_ticket_id" json:"support_ticket_id"`
		SupportConversationID *string `gorm:"column:support_conversation_id" json:"support_conversation_id"`
		TicketStatus          string  `gorm:"column:ticket_status" json:"ticket_status"`
		TicketCategory        string  `gorm:"column:ticket_category" json:"ticket_category"`
	}
	query := `
		select
			ko.id as order_id,
			s.name as seller,
			s.code as seller_code,
			ko.user_id as user_id,
			kop.amount as order_value,
			ko.display_status as status,
			ko.created_at as created_at,
			kam.pdd as promised_delivery,
			kam.edd as estimated_delivery,
			kbr.order_delivered as delivered_at,
			kbr.order_returned as returned_at,
			ct.id as support_ticket_id,
			ct.conversation_id as support_conversation_id,
			ct.status as ticket_status,
			ctc.name as ticket_category
		from kiranabazar_orders ko
		left join kiranabazar_order_payments kop on ko.id = kop.order_id
		left join kiranabazar_awb_master kam on ko.id = kam.order_id
		left join cs_tickets ct on ko.id = ct.order_id
			and ct.created_at = (
				select max(created_at)
				from cs_tickets ct2
				where ct2.order_id = ko.id
			)
		left join kc_bazar_reconciliation kbr on ko.id = kbr.order_id
		left join kiranabazar_sellers s on ko.seller = s.code
		left join cs_ticket_category ctc on ct.category = ctc.id
		where ko.user_id = '%s' and ko.is_archived = 0`
	query = fmt.Sprintf(query, request.Data.UserID)
	query += " order by ko.created_at desc"
	query += fmt.Sprintf(" limit %d offset %d", request.Data.Limit, request.Data.Offset)
	userOrderData := []UserOrderData{}
	_, err := s.ReadOnlyRepository.CustomQuery(&userOrderData, query)
	if err != nil {
		return nil, err
	}
	appSupportOrders := []map[string]interface{}{}
	if userOrderData != nil && len(userOrderData) > 0 {
		for i := range userOrderData {
			supportTicketExists := false
			resolvedOrderStatusTicket := slices.Contains([]string{utils.TICKET_STATUS_RESOLVED, utils.TICKET_STATUS_CLOSED}, userOrderData[i].TicketStatus) && (userOrderData[i].TicketCategory == "Order Status") && ((userOrderData[i].Status != nil) && (*userOrderData[i].Status == displaystatus.DELIVERED))
			if userOrderData[i].SupportTicketID != nil && !resolvedOrderStatusTicket {
				supportTicketExists = true
			}
			ticketStatusText := ""
			if (userOrderData[i].Status != nil) && (*userOrderData[i].Status == displaystatus.DELIVERED) && (userOrderData[i].DeliveredAt != nil) {
				ticketStatusText += "डिलीवर किया गया: "
				ticketStatusText += time.UnixMilli(*userOrderData[i].DeliveredAt).Format("2 Jan 2006")
			} else if (userOrderData[i].Status != nil) && (*userOrderData[i].Status == displaystatus.RETURNED) && (userOrderData[i].ReturnedAt != nil) {
				ticketStatusText += "रिटर्न किया गया: "
				ticketStatusText += time.UnixMilli(*userOrderData[i].ReturnedAt).Format("2 Jan 2006")
			} else if (userOrderData[i].Status != nil) && (*userOrderData[i].Status == displaystatus.IN_TRANSIT) && ((userOrderData[i].EstimatedDelivery != nil) || (userOrderData[i].PromisedDelivery != nil)) {
				ticketStatusText += "अपेक्षित डिलीवरी: "
				if userOrderData[i].EstimatedDelivery != nil {
					ticketStatusText += time.UnixMilli(*userOrderData[i].EstimatedDelivery).Format("2 Jan 2006")
				} else {
					ticketStatusText += time.UnixMilli(*userOrderData[i].PromisedDelivery).Format("2 Jan 2006")
				}
			}

			appSupportOrders = append(appSupportOrders, map[string]interface{}{
				"order_id":                userOrderData[i].OrderID,
				"seller":                  userOrderData[i].Seller,
				"seller_code":             userOrderData[i].SellerCode,
				"user_id":                 userOrderData[i].UserID,
				"order_value":             userOrderData[i].OrderValue,
				"status":                  userOrderData[i].Status,
				"created_at":              userOrderData[i].CreatedAt,
				"ticket_status_text":      ticketStatusText,
				"support_ticket_id":       userOrderData[i].SupportTicketID,
				"support_ticket_exists":   supportTicketExists,
				"support_ticket_status":   userOrderData[i].TicketStatus,
				"support_conversation_id": userOrderData[i].SupportConversationID,
			})
		}
	}
	response := map[string]interface{}{
		"orders": appSupportOrders,
	}
	if len(appSupportOrders) == 0 {
		response = map[string]interface{}{
			"orders": []map[string]interface{}{},
		}
	}
	return &response, nil
}

func GenerateDistributedID(idType string) string {
	millisTime := time.Now().UnixMilli()
	counter := atomic.AddUint64(&idCounter, 1) % 100
	idTypeInt := 0
	if idType == "conversation" {
		idTypeInt = 1
	} else if idType == "message" {
		idTypeInt = 2
	} else if idType == "attachment" {
		idTypeInt = 3
	} else if idType == "ticket_action" {
		idTypeInt = 4
	}
	return fmt.Sprintf("%d%d%02d", millisTime, idTypeInt, counter)
}

func FormatIssueMessage(issue dto.AppSubmitTicketIssueDetails) string {
	var sb strings.Builder

	sb.WriteString(fmt.Sprintf("Issue Category: %s\n", issue.IssueCategory.Text))

	if issue.IssueSubCategory.Text != "" {
		sb.WriteString(fmt.Sprintf("Issue Sub-Category: %s\n", issue.IssueSubCategory.Text))
	}

	// Add affected items if not empty
	if len(issue.AffectedItems) > 0 {
		sb.WriteString("Affected Items:\n")
		for i, item := range issue.AffectedItems {
			// Check if product name exists
			if item.ProductName != "" {
				weightInfo := ""
				if item.Weight != "" {
					weightInfo = fmt.Sprintf(" (%s)", item.Weight)
				}

				sb.WriteString(fmt.Sprintf("      %d. %s%s * %d\n",
					i+1,
					item.ProductName,
					weightInfo,
					item.QuantityAffected))
			}
		}
	}

	// Add item missing questions if not empty
	if len(issue.ItemMissingQuestions) > 0 {
		sb.WriteString("Item Missing Questions:\n")
		for i, question := range issue.ItemMissingQuestions {
			sb.WriteString(fmt.Sprintf("      %d. %s", i+1, question.Question))
			if question.Answer != "" {
				sb.WriteString(fmt.Sprintf(": %s", question.Answer))
			}
			sb.WriteString("\n")
		}
	}

	// Add additional questions if not empty
	if issue.AdditionalQuestion.Question != "" {
		sb.WriteString("Additional Questions:\n")
		sb.WriteString(fmt.Sprintf("      1. %s", issue.AdditionalQuestion.Question))
		if issue.AdditionalQuestion.Answer != "" {
			sb.WriteString(fmt.Sprintf(": %s", issue.AdditionalQuestion.Answer))
		}
		sb.WriteString("\n")
	}

	// Add description if not empty
	if issue.Description != "" {
		sb.WriteString(fmt.Sprintf("Description: %s", issue.Description))
	}

	return sb.String()
}

func (s *Service) trackAppSubmitTicket(
	ctx context.Context,
	ticketID string,
	media []string,
	ticketCategory *dao.CsTicketCategory,
	ticketDetails *dao.CSTicket,
	request *dto.AppSubmitTicketRequest,
	orderData *dao.GetOrderEssentials,
) error {
	// Track the ticket submission event
	ticketPrimaryOwner, err := s.GetAgentByID(ctx, ticketDetails.PrimaryOwnerID)
	if err != nil {
		return fmt.Errorf("error fetching ticket primary owner: %v", err)
	}

	affected_items := []string{}
	for _, item := range request.Data.IssueDetails.AffectedItems {
		affected_items = append(affected_items, fmt.Sprintf(`%s X %d`, item.ProductName, item.QuantityAffected))
	}

	var subCategory string
	if request.Data.IssueDetails.IssueSubCategory.ID != "" {
		subCategoryInfo, err := s.GetTicketCategoryByCode(ctx, request.Data.IssueDetails.IssueSubCategory.ID)
		if err != nil {
			return fmt.Errorf("error fetching sub-category info: %v", err)
		}
		subCategory = subCategoryInfo.Name
	}
	eventObject := map[string]interface{}{
		"ticket_id":      ticketID,
		"order_id":       request.Data.OrderID,
		"category":       ticketCategory.Name,
		"sub_category":   subCategory,
		"media":          media,
		"description":    request.Data.IssueDetails.Description,
		"seller":         utils.SafeString(orderData.Seller),
		"order_value":    orderData.OrderValue,
		"display_status": orderData.DisplayStatus,
		"courier_name":   couriers.GetActualCourierName(orderData.Courier),
		"awb_number":     utils.SafeString(orderData.AwbNumber),
		"assigned_to":    ticketPrimaryOwner.Email,
		"affected_items": affected_items,
		"source":         utils.SafeString(ticketDetails.Source),
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Support Ticket Created", request.Data.UserID, eventObject),
	})

	webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{request.Data.UserID},
		EventName:   "Support Ticket Created",
		EventObject: eventObject,
	})
	return nil
}

func (s *Service) AppSubmitTicket(ctx context.Context, request *dto.AppSubmitTicketRequest, use3pl bool) (*map[string]interface{}, error) {
	orderIDInt64, err := strconv.ParseInt(request.Data.OrderID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid order ID format: %v", err)
	}
	ticketID, _ := GenerateTicketID(request.Data.IssueDetails.IssueCategory.ID, int(orderIDInt64))
	conversationID := GenerateDistributedID("conversation")

	orderData, err := GetOrderEssentials(s.repository, orderIDInt64)
	if err != nil {
		return nil, fmt.Errorf("error fetching order data: %v", err)
	}

	createdAt, err := time.Parse(time.RFC3339Nano, request.Data.CreatedAt)
	if err != nil {
		return nil, fmt.Errorf("invalid timestamp format: %v", err)
	}
	defultAgent, err := s.GetAgentByEmail(ctx, "<EMAIL>")
	if err != nil {
		return nil, fmt.Errorf("error fetching default agent: %v", err)
	}
	defaul3plAgent, err := s.GetAgentByEmail(ctx, "<EMAIL>")
	if err != nil {
		return nil, fmt.Errorf("error fetching default 3PL agent: %v", err)
	}
	ticketCategory, err := s.GetTicketCategoryByCode(ctx, request.Data.IssueDetails.IssueCategory.ID)
	if err != nil {
		return nil, fmt.Errorf("error fetching ticket category: %v", err)
	}
	defaultPriority := "p1"
	defaultStatus := utils.TICKET_STATUS_OPEN
	primaryOwnerID := defultAgent.ID
	if use3pl {
		defaultStatus = utils.TICKET_STATUS_INPROGRESS_3PL
		primaryOwnerID = defaul3plAgent.ID
	}
	issueMeta, err := json.Marshal([]dto.AppSubmitTicketIssueDetails{request.Data.IssueDetails})
	if err != nil {
		return nil, fmt.Errorf("error marshalling issue details: %v", err)
	}
	csTicket := dao.CSTicket{
		ID:                ticketID,
		OrderID:           int(orderIDInt64),
		UserID:            request.Data.UserID,
		CreatedAt:         createdAt.UnixMilli(),
		UpdatedAt:         createdAt.UnixMilli(),
		UpdatedBy:         request.Data.UserID,
		PrimaryOwnerID:    primaryOwnerID,
		CurrentAssigneeID: &primaryOwnerID,
		Priority:          &defaultPriority,
		Status:            &defaultStatus,
		Category:          &ticketCategory.ID,
		ConversationID:    conversationID,
		Meta:              issueMeta,
		Seller:            orderData.Seller,
		Source:            &request.Data.FlowID,
	}
	_, err = s.repository.Create(&csTicket)
	if err != nil {
		return nil, fmt.Errorf("error creating ticket: %v", err)
	}

	messageContent := FormatIssueMessage(request.Data.IssueDetails)
	messageID := GenerateDistributedID("message")
	csMessage := dao.CSMessage{
		ID:             messageID,
		ConversationID: conversationID,
		Content:        &messageContent,
		SenderID:       request.Data.UserID,
		SenderType:     utils.MESSAGE_SENDER_USER,
		Private:        false,
		MessageType:    func(s string) *string { return &s }(utils.MESSAGE_TYPE_TICKET_CREATION),
		CreatedAt:      createdAt.UnixMilli(),
		UpdatedAt:      createdAt.UnixMilli(),
	}
	_, err = s.repository.Create(&csMessage)
	if err != nil {
		return nil, fmt.Errorf("error creating message: %v", err)
	}

	go func() {
		err := s.handleProgressStateTransition(ctx, ticketID, "ticket_created", request.Data.UserID, nil)
		if err != nil {
			fmt.Printf("Error setting initial progress state: %v\n", err)
		}
	}()

	media := []string{}
	for _, file := range request.Data.IssueDetails.Media {
		attachmentID := GenerateDistributedID("attachment")
		csAttachment := dao.CSAttachment{
			ID:               attachmentID,
			MessageID:        messageID,
			FileType:         file.FileType,
			FileURL:          file.FileURL,
			FileSize:         file.SizeBytes,
			CreatedAt:        createdAt.UnixMilli(),
			UpdatedAt:        createdAt.UnixMilli(),
			FileThumbnailURL: &file.FileURL,
			ContentType:      &file.ContentType,
		}
		media = append(media, file.FileURL)
		_, err = s.repository.Create(&csAttachment)
		if err != nil {
			return nil, fmt.Errorf("error creating attachment: %v", err)
		}
		go func() {
			err = processVideoMedia([]dao.ReviewMedia{{URI: file.FileURL, Type: file.FileType}}, "<EMAIL>", attachmentID, updateCustomerSupportMediaUrl)
			if err != nil {
				fmt.Printf("Error processing video media: %v\n", err)
			}
		}()
	}
	ticketDetails := dao.CSTicket{}
	_, err = s.repository.Find(map[string]interface{}{
		"id": ticketID,
	}, &ticketDetails)
	if err != nil {
		return nil, fmt.Errorf("error fetching ticket details: %v", err)
	}

	go func() {
		err := s.trackAppSubmitTicket(ctx, ticketID, media, ticketCategory, &ticketDetails, request, orderData)
		if err != nil {
			fmt.Printf("Error tracking ticket reassigned: %v\n", err)
		}
	}()

	go func() {
		result, err := s.ProcessAutoReplyForOrderDelay(ctx, ticketID, int(orderIDInt64), request.Data.UserID)
		if err != nil {
			fmt.Printf("Error processing auto reply: %v\n", err)
			return
		}
		if result.ShouldSendAutoReply {
			fmt.Printf("Auto reply sent for ticket %s with message: %s\n", ticketID, result.Message)
			if result.ShouldResolveTicket {
				fmt.Printf("Ticket %s resolved automatically with resolution note: %s\n", ticketID, result.ResolutionNote)
			}
		}
	}()

	response := map[string]interface{}{
		"message": "Ticket created successfully",
		"ticket":  ticketDetails,
	}
	return &response, nil
}

func (s *Service) GetMessages(ctx context.Context, request *dto.GetTicketMessagesRequest) (*dto.GetTicketMessagesResponse, error) {
	// Get conversation ID - either directly from request or by fetching the ticket
	conversationID := request.Data.ConversationID

	// If conversation ID is not provided but ticket ID is, fetch the ticket to get conversation ID
	if conversationID == "" {
		if request.Data.TicketID == "" {
			return nil, fmt.Errorf("either ticket_id or conversation_id is required")
		}

		var ticket dao.CSTicket
		_, err := s.repository.Find(map[string]interface{}{
			"id": request.Data.TicketID,
		}, &ticket)
		if err != nil {
			return nil, fmt.Errorf("error fetching ticket: %v", err)
		}

		conversationID = ticket.ConversationID
	}

	// Base query with JOIN for messages and attachments
	query := `
		SELECT 
			m.id,
			m.conversation_id,
			m.content,
			m.sender_id,
			m.sender_type,
			m.private,
			m.message_type,
			m.created_at,
			m.updated_at,
			m.visible,
			u.name AS sender_name,
			u.profile_image_url as sender_avatar,
			JSON_ARRAYAGG(
				IF(a.id IS NULL, 
					JSON_OBJECT(),
					JSON_OBJECT(
						'id', a.id,
						'message_id', a.message_id,
						'file_type', a.file_type,
						'file_url', a.file_url,
						'file_size', a.file_size,
						'created_at', a.created_at,
						'updated_at', a.updated_at,
						'file_thumbnail_url', a.file_thumbnail_url,
						'private', a.private,
						'meta', a.meta,
						'file_raw_url', a.file_raw_url
					)
				)
			) AS attachments
		FROM cs_messages m
		LEFT JOIN cs_attachments a ON m.id = a.message_id
		LEFT JOIN users u ON m.sender_id = u.user_id
		WHERE m.conversation_id = '%s'
	`

	// Format query with conversation ID
	query = fmt.Sprintf(query, conversationID)

	// Apply visibility filter - In WhatsApp style, we typically only show visible messages to users
	query += " AND (m.visible = 1 OR m.visible IS NULL)"

	// Apply privacy filter - Depends on user type (customer vs agent)
	if (strings.ToLower(request.Data.UserType) == utils.MESSAGE_SENDER_USER) || (request.Data.Source == "KC_APP") {
		// Regular users don't see private messages
		query += " AND m.private = 0"
	}

	if request.Data.AnchorMessageID != "" {
		if request.Data.Direction == "before" {
			query += fmt.Sprintf(" AND m.id < %s", request.Data.AnchorMessageID)
			query += " GROUP BY m.id ORDER BY m.created_at DESC" // Newest of the older messages first
		} else if request.Data.Direction == "after" {
			// Load messages AFTER the anchor (newer messages)
			query += fmt.Sprintf(" AND m.created_at > %s", request.Data.AnchorMessageID)
			query += " GROUP BY m.id ORDER BY m.created_at ASC" // Oldest of the newer messages first
		} else {
			// Load messages AROUND the anchor (both sides)
			// For simplicity, default to just loading the newest messages
			query += " GROUP BY m.id ORDER BY m.created_at DESC"
		}
	} else {
		query += " GROUP BY m.id ORDER BY m.created_at DESC" // Oldest of the newer messages first
	}

	// Apply limit for pagination (WhatsApp typically loads 20-50 messages at a time)
	limit := 20 // Default limit
	if request.Data.Limit > 0 {
		limit = request.Data.Limit
	}
	query += fmt.Sprintf(" LIMIT %d", limit)

	// Define a struct for the query result that includes the JSON attachment array
	type MessageWithAttachments struct {
		dao.CSMessage
		SenderName   string          `db:"sender_name"`
		SenderAvatar *string         `db:"sender_avatar"`
		Attachments  json.RawMessage `db:"attachments"`
	}

	// Execute query
	var messagesWithAttachmentsJson []MessageWithAttachments
	_, err := s.repository.CustomQuery(&messagesWithAttachmentsJson, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching messages with attachments: %v", err)
	}

	// Early return if no messages found
	if len(messagesWithAttachmentsJson) == 0 {
		return &dto.GetTicketMessagesResponse{
			Messages: []dto.TicketMessage{},
			Pagination: dto.WhatsAppPaginationInfo{
				HasOlderMessages: false,
				HasNewerMessages: false,
				Count:            0,
			},
		}, nil
	}

	// Collect all unique sender IDs
	agentIDs := make([]string, 0)
	for _, msg := range messagesWithAttachmentsJson {
		if msg.SenderType == utils.MESSAGE_SENDER_AGENT || msg.SenderType == utils.MESSAGE_SENDER_SYSTEM {
			agentIDs = append(agentIDs, msg.SenderID)
		}
	}

	agentInfoMap, err := s.GetAgentsByIDs(ctx, agentIDs)
	if err != nil {
		return nil, fmt.Errorf("error fetching agent info: %v", err)
	}

	// Process all messages and convert them to the response format
	messages := []dto.TicketMessage{}
	var messageIDs []string

	for _, msgWithAtt := range messagesWithAttachmentsJson {
		messageIDs = append(messageIDs, msgWithAtt.ID)

		senderInfo := dto.MessageSender{}
		// Add sender details from batch queries
		if msgWithAtt.SenderType == utils.MESSAGE_SENDER_AGENT {
			if agentInfo, exists := agentInfoMap[msgWithAtt.SenderID]; exists {
				senderInfo.ID = agentInfo.ID
				senderInfo.Name = agentInfo.Name
				senderInfo.Role = agentInfo.Role
				senderInfo.AvatarURL = agentInfo.AvatarURL
				senderInfo.Type = msgWithAtt.SenderType
			}
		} else if msgWithAtt.SenderType == utils.MESSAGE_SENDER_SYSTEM {
			senderInfo.ID = msgWithAtt.SenderID
			senderInfo.Name = msgWithAtt.SenderName
			senderInfo.Type = msgWithAtt.SenderType
		} else {
			senderInfo.ID = msgWithAtt.SenderID
			senderInfo.Name = msgWithAtt.SenderName
			senderInfo.Type = msgWithAtt.SenderType
			senderInfo.AvatarURL = msgWithAtt.SenderAvatar
		}

		// Parse the attachments JSON
		var attachmentsArray []dto.MessageAttachment
		if len(msgWithAtt.Attachments) > 0 && string(msgWithAtt.Attachments) != "[{}]" {
			if err := json.Unmarshal(msgWithAtt.Attachments, &attachmentsArray); err != nil {
				// If there's an error parsing, just log it and continue with empty attachments
				fmt.Printf("Error parsing attachments for message %s: %v\n", msgWithAtt.ID, err)
				attachmentsArray = []dto.MessageAttachment{}
			}
		}

		// Build ticket message
		ticketMessage := dto.TicketMessage{
			ID:             msgWithAtt.ID,
			ConversationID: msgWithAtt.ConversationID,
			Content:        msgWithAtt.Content,
			Sender:         senderInfo,
			Private:        msgWithAtt.Private,
			MessageType:    msgWithAtt.MessageType,
			CreatedAt:      msgWithAtt.CreatedAt,
			UpdatedAt:      msgWithAtt.UpdatedAt,
			Visible:        msgWithAtt.Visible,
			DeliveryStatus: "delivered", // Default status
			Attachments:    attachmentsArray,
		}

		messages = append(messages, ticketMessage)
	}

	// Check for older/newer messages in single queries
	hasOlderMessages := false
	hasNewerMessages := false

	if len(messages) > 0 {
		// Check for older messages (for scrolling up)
		oldestMessageTime := messagesWithAttachmentsJson[len(messagesWithAttachmentsJson)-1].CreatedAt
		olderQuery := fmt.Sprintf(`
			SELECT EXISTS(
				SELECT 1 
				FROM cs_messages m 
				WHERE m.conversation_id = '%s' 
				AND (m.visible = 1 OR m.visible IS NULL)
				AND m.created_at < %d
				LIMIT 1
			) as has_older`, conversationID, oldestMessageTime)

		var olderResults []struct {
			HasOlder bool `db:"has_older"`
		}
		_, err = s.repository.CustomQuery(&olderResults, olderQuery)
		if err == nil && len(olderResults) > 0 {
			hasOlderMessages = olderResults[0].HasOlder
		}

		// Check for newer messages (for scrolling down)
		newestMessageTime := messagesWithAttachmentsJson[0].CreatedAt
		newerQuery := fmt.Sprintf(`
			SELECT EXISTS(
				SELECT 1
				FROM cs_messages m 
				WHERE m.conversation_id = '%s' 
				AND (m.visible = 1 OR m.visible IS NULL)
				AND m.created_at > %d
				LIMIT 1
			) as has_newer`, conversationID, newestMessageTime)

		var newerResults []struct {
			HasNewer bool `db:"has_newer"`
		}
		_, err = s.repository.CustomQuery(&newerResults, newerQuery)
		if err == nil && len(newerResults) > 0 {
			hasNewerMessages = newerResults[0].HasNewer
		}
	}

	// Build response with pagination info
	var firstMessageID, lastMessageID string
	if len(messages) > 0 {
		firstMessageID = messages[0].ID
		lastMessageID = messages[len(messages)-1].ID
	}

	response := dto.GetTicketMessagesResponse{
		Messages: messages,
		Pagination: dto.WhatsAppPaginationInfo{
			HasOlderMessages: hasOlderMessages,
			HasNewerMessages: hasNewerMessages,
			FirstMessageID:   firstMessageID,
			LastMessageID:    lastMessageID,
			Count:            len(messages),
		},
	}

	if request.Data.TicketID != "" && request.Data.UserType == utils.MESSAGE_SENDER_AGENT {
		go func() {
			err := s.handleProgressStateTransition(ctx, request.Data.TicketID, "agent_viewed",
				request.Data.AgentEmail, nil)
			if err != nil {
				fmt.Printf("Error updating progress state on view: %v\n", err)
			}
		}()
	}

	return &response, nil
}

func (s *Service) SendMessage(ctx context.Context, request *dto.SendMessageRequest) (*dto.SendMessageResponse, error) {
	// Validate request
	if request.Data.TicketID == "" && request.Data.ConversationID == "" {
		return nil, fmt.Errorf("either ticket_id or conversation_id is required")
	}

	if request.Data.Content == "" && len(request.Data.Attachments) == 0 {
		return nil, fmt.Errorf("either content or attachments are required")
	}

	var ticket dao.CSTicket
	_, err := s.repository.Find(map[string]interface{}{
		"id": request.Data.TicketID,
	}, &ticket)
	if err != nil {
		return nil, fmt.Errorf("error fetching ticket: %v", err)
	}
	// Get conversation ID if only ticket ID is provided
	conversationID := ticket.ConversationID

	// Create a new message
	now := time.Now().UTC().UnixMilli()
	messageID := GenerateDistributedID("message")
	var private bool
	if request.Data.Private != nil {
		private = *request.Data.Private
	}
	senderType := strings.ToLower(request.Data.SenderType)
	if request.Data.Source == "KC_APP" {
		senderType = utils.MESSAGE_SENDER_USER
	}
	processedContent := strings.Trim(request.Data.Content, "\"'")
	message := dao.CSMessage{
		ID:             messageID,
		ConversationID: conversationID,
		Content:        &processedContent,
		SenderID:       request.Data.SenderID,
		SenderType:     senderType,
		Private:        private,
		MessageType:    &request.Data.MessageType,
		CreatedAt:      now,
		UpdatedAt:      now,
		Visible:        request.Data.Visible,
		Status:         &request.Data.Status,
	}

	// Insert the message
	_, err = s.repository.Create(&message)
	if err != nil {
		return nil, fmt.Errorf("error creating message: %v", err)
	}

	// Process any attachments
	var attachments []dao.CSAttachment

	for _, attachment := range request.Data.Attachments {
		attachmentID := GenerateDistributedID("attachment")

		// Create attachment record
		att := dao.CSAttachment{
			ID:               attachmentID,
			MessageID:        messageID,
			FileType:         attachment.FileType,
			FileURL:          attachment.FileURL,
			FileSize:         attachment.FileSize,
			CreatedAt:        now,
			UpdatedAt:        now,
			FileThumbnailURL: attachment.FileThumbnailURL,
			Private:          private, // Inherit privacy from message
			Meta:             attachment.Meta,
			ContentType:      attachment.ContentType,
		}

		attachments = append(attachments, att)

		// Insert the attachment
		_, err = s.repository.Create(&att)
		if err != nil {
			// Log error but continue, don't fail the whole message if one attachment fails
			fmt.Printf("Error creating attachment: %v\n", err)
		}
		go func() {
			err = processVideoMedia([]dao.ReviewMedia{{URI: attachment.FileURL, Type: attachment.FileType}}, "<EMAIL>", attachmentID, updateCustomerSupportMediaUrl)
			if err != nil {
				fmt.Printf("Error processing video media: %v\n", err)
			}
		}()
	}

	openStatus := fmt.Sprintf(`%s`, utils.TICKET_STATUS_OPEN)
	if (ticket.Status != nil) &&
		(slices.Contains([]string{utils.TICKET_STATUS_RESOLVED, utils.TICKET_STATUS_INPROGRESS_3PL}, *ticket.Status)) &&
		(senderType == utils.MESSAGE_SENDER_USER) &&
		(request.Data.MessageType != utils.MESSAGE_TYPE_TICKET_RESOLUTION_RATING_ADDED) {
		currentAssigneeID := ticket.PrimaryOwnerID
		if (ticket.Source != nil) && (slices.Contains([]string{utils.TICKET_FLOW_FOFD, utils.TICKET_FLOW_APP_STANDARD}, *ticket.Source)) {
			defultCsAgent, err := s.GetAgentByEmail(ctx, "<EMAIL>")
			if err != nil {
				return nil, fmt.Errorf("error fetching default agent: %v", err)
			}
			currentAssigneeID = defultCsAgent.ID
		}
		updateTicket := dao.CSTicket{
			Status:            &openStatus,
			UpdatedAt:         now,
			CurrentAssigneeID: &currentAssigneeID,
		}
		_, _, err = s.repository.Update(&dao.CSTicket{
			ID: ticket.ID,
		}, &updateTicket)
		if err != nil {
			fmt.Printf("Error updating ticket status: %v\n", err)
		}
		s.addActionSystemMessage(ctx, ticket, dto.TicketActionRequestData{
			TicketID: ticket.ID,
			Action:   "ticket_reopened",
		}, "", "")
	} else if (ticket.Status != nil) && (*ticket.Status == utils.TICKET_STATUS_OPEN) && (request.Data.SenderType == utils.MESSAGE_SENDER_AGENT) {
		go func() {
			err := s.changeTicketStatusToInProgress(ctx, ticket, "", request.Data.SenderID)
			if err != nil {
				fmt.Printf("Error changing ticket status to in progress: %v\n", err)
			}
		}()
	}

	// Get sender info for response
	senderInfo := dto.MessageSender{
		ID:   request.Data.SenderID,
		Type: senderType,
	}

	// Get sender details based on type
	if (request.Data.SenderType == utils.MESSAGE_SENDER_AGENT) || (request.Data.SenderType == utils.MESSAGE_SENDER_SYSTEM) {
		agentQuery := fmt.Sprintf(`
			SELECT 
				a.name,
				a.role,
				a.avatar_url
			FROM cs_agents a
			WHERE a.id = '%s'
		`, request.Data.SenderID)

		var agents []struct {
			Name      string  `db:"name"`
			Role      string  `db:"role"`
			AvatarURL *string `db:"avatar_url"`
		}

		_, err = s.repository.CustomQuery(&agents, agentQuery)
		if err == nil && len(agents) > 0 {
			senderInfo.Name = agents[0].Name
			senderInfo.Role = agents[0].Role
			senderInfo.AvatarURL = agents[0].AvatarURL
		}
	} else if request.Data.SenderType == utils.MESSAGE_SENDER_USER {
		userQuery := fmt.Sprintf(`
			SELECT 
				u.name,
				u.phone
			FROM users u
			WHERE u.user_id = '%s'
		`, request.Data.SenderID)

		var users []struct {
			Name  string `db:"name"`
			Phone string `db:"phone"`
		}

		_, err = s.repository.CustomQuery(&users, userQuery)
		if err == nil && len(users) > 0 {
			senderInfo.Name = users[0].Name
		}
	}

	// Format attachments for response
	responseAttachments := []dto.MessageAttachment{}
	for _, att := range attachments {
		attachmentIdInt, err := strconv.ParseInt(att.ID, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("error parsing attachment ID: %v", err)
		}
		messageIdInt, err := strconv.ParseInt(att.MessageID, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("error parsing attachment ID: %v", err)
		}
		responseAttachment := dto.MessageAttachment{
			ID:               attachmentIdInt,
			MessageID:        messageIdInt,
			FileType:         att.FileType,
			FileURL:          att.FileURL,
			FileSize:         att.FileSize,
			CreatedAt:        att.CreatedAt,
			UpdatedAt:        att.UpdatedAt,
			FileThumbnailURL: att.FileThumbnailURL,
			Meta:             att.Meta,
		}
		responseAttachments = append(responseAttachments, responseAttachment)
	}

	// send webengage notification to user
	if !message.Private {
		supportUrl := fmt.Sprintf(`https://webapps.retailpulse.ai/customer-support/chat?ticketId=%s&conversationId=%s&userId=%s&status=%s`, ticket.ID, conversationID, ticket.UserID, *ticket.Status)
		eventObject := map[string]interface{}{
			"order_id":    fmt.Sprintf(`%d`, ticket.OrderID),
			"message":     message.Content,
			"timestamp":   message.UpdatedAt,
			"support_url": supportUrl,
		}
		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{ticket.UserID},
			EventName:   "CS Agent Replied on Ticket",
			EventObject: eventObject,
		})
	}

	// Build response
	pendingReply := request.Data.PendingReply
	if (message.SenderType == utils.MESSAGE_SENDER_AGENT) && (!message.Private) && (pendingReply) {
		pendingReply = false
	}
	response := dto.SendMessageResponse{
		Message: dto.TicketMessage{
			ID:             messageID,
			ConversationID: conversationID,
			Content:        &processedContent,
			Sender:         senderInfo,
			Private:        private,
			MessageType:    &request.Data.MessageType,
			CreatedAt:      now,
			UpdatedAt:      now,
			Visible:        request.Data.Visible,
			DeliveryStatus: "sent",
			Attachments:    responseAttachments,
			PendingReply:   pendingReply,
		},
		Success: true,
	}

	if ticket.ID != "" {
		go func() {
			var actionType string
			actionBy := request.Data.SenderID

			// Determine action type based on sender
			switch message.SenderType {
			case utils.MESSAGE_SENDER_USER:
				actionType = "user_message"

			case utils.MESSAGE_SENDER_AGENT:
				actionType = "agent_message"
				// Check if it's actually from a different team
				if agentData, err := s.GetAgentByID(ctx, request.Data.SenderID); err == nil && agentData.Team != nil {
					switch *agentData.Team {
					case utils.AGENT_TEAM_SELLER:
						actionType = "seller_message"
					case utils.AGENT_TEAM_TECH:
						actionType = "tech_message"
					case utils.AGENT_TEAM_OPS:
						actionType = "ops_message"
					}
				}

			case utils.MESSAGE_SENDER_SELLER:
				actionType = "seller_message"
			}

			if actionType != "" {
				err := s.handleProgressStateTransition(ctx, request.Data.TicketID, actionType, actionBy, nil)
				if err != nil {
					fmt.Printf("Error updating progress state for message: %v\n", err)
				}
			}

		}()
	}

	return &response, nil
}

func (s *Service) GetCsMeta(ctx context.Context, request *dto.GetCsMetaRequest) (map[string]interface{}, error) {
	// Fetch agent details
	if request.Data.AgentEmail == "" {
		return nil, fmt.Errorf("agent email is required")
	}

	agentData, err := s.GetAgentByEmail(ctx, request.Data.AgentEmail)
	if err != nil {
		return nil, fmt.Errorf("agent not found")
	}

	ticketCategoryQuery := fmt.Sprintf(`select * from cs_ticket_category where is_active = 1 and type = 'category'`)
	ticketCategory := []dao.CsTicketCategory{}
	_, err = s.repository.CustomQuery(&ticketCategory, ticketCategoryQuery)
	if err != nil {
		return nil, fmt.Errorf("error fetching ticket categories: %v", err)
	}

	response := map[string]interface{}{
		"agent_data":      agentData,
		"ticket_priority": utils.TICKETS_PRIORITY_VALUES,
		"ticket_category": ticketCategory,
	}
	if request.Data.Source == "B2B_EXTERNAL" {
		response["ticket_status"] = utils.TICKETS_STATUS_VALUES_SELLER_SIDE
	} else {
		response["ticket_status"] = utils.TICKETS_STATUS_VALUES
	}
	if (request.Data.Source == "B2B_INTERNAL") || (request.Data.Source == "") {
		response["agent_roles"] = utils.AGENT_ROLES
		response["agent_team"] = utils.AGENT_TEAM
		response["sender_type"] = utils.MESSAGE_SENDER_TYPE
		response["agents"] = agentCacheInstance.agentsEmailMap
	}
	return response, nil
}

func getKiranaBazarOrderDetails(repository *sqlRepo.Repository, orderID string) (*dto.CsTicketOrderDetails, *string, error) {
	query := fmt.Sprintf(`
		SELECT
			ko.id as order_id,
			s.name as seller,
			ko.user_id as user_id,
			kop.amount as order_value,
			ko.display_status as status,
			ko.created_at as created_at,
			kos.courier as courier,
			kos.awb_number as awb_number,
			kod.order_details as order_details,
			kam.pdd as promised_delivery,
			kbr.order_placed as order_placed,
			kbr.order_confirmed as order_confirmed,
			kbr.order_delivered as order_delivered,
			kbr.order_returned as order_returned,
			kbr.order_cancelled as order_cancelled,
			kbr.order_dispatched as order_dispatched,
			kbr.order_shipment_created as order_shipment_created,
			kbr.order_ofd as order_ofd,
			kn.ndr_attempt_count as attempt_count
		FROM kiranabazar_orders ko
		LEFT JOIN kiranabazar_order_payments kop ON ko.id = kop.order_id
		LEFT JOIN kiranabazar_awb_master kam ON ko.id = kam.order_id
		LEFT JOIN kiranabazar_order_details kod ON ko.id = kod.order_id
		LEFT JOIN kc_bazar_reconciliation kbr ON ko.id = kbr.order_id
		LEFT JOIN kiranabazar_ndrs kn ON ko.id = kn.order_id
		LEFT JOIN kiranabazar_order_status kos ON ko.id = kos.id
		LEFT JOIN kiranabazar_sellers s ON ko.seller = s.code
		WHERE ko.id = %s;
	`, orderID)
	orderDetails := []dto.CsTicketOrderDetails{}
	_, err := repository.CustomQuery(&orderDetails, query)
	if err != nil {
		return nil, nil, fmt.Errorf("error fetching order details: %v", err)
	}

	if len(orderDetails) == 0 {
		return nil, nil, fmt.Errorf("no order details found for order ID: %s", orderID)
	}

	result := orderDetails[0]

	if result.Status != nil {
		switch *result.Status {
		case "CANCELLED":
			// If cancelled, set delivered and returned to nil
			result.OrderDelivered = nil
			result.OrderReturned = nil
		case "RETURNED":
			// If returned, set delivered and cancelled to nil
			result.OrderDelivered = nil
			result.OrderCancelled = nil
		case "DELIVERED":
			// If delivered, set returned and cancelled to nil
			result.OrderReturned = nil
			result.OrderCancelled = nil
		}
	}
	var promisedDeliveryDate *string
	if result.PromisedDelivery != nil {
		englighDeliveryDate := utils.ConvertUTCToIST(*result.PromisedDelivery)
		promisedDeliveryDate = &englighDeliveryDate
	}
	return &result, promisedDeliveryDate, nil
}

func (s *Service) GetTicketByOrderID(ctx context.Context, orderID string) (*[]dao.CSTicket, error) {
	if orderID == "" {
		return nil, fmt.Errorf("order_id is required")
	}

	var ticket []dao.CSTicket
	query := fmt.Sprintf(`
		SELECT
			t.*
		FROM cs_tickets t
		WHERE t.order_id = '%s'
		AND t.status != 'closed'
		ORDER BY t.created_at DESC
	`, orderID)
	_, err := s.repository.CustomQuery(&ticket, query)
	if err != nil {
		return nil, fmt.Errorf("ticket not found: %v", err)
	}

	return &ticket, nil
}

func (s *Service) GetTicketByID(ctx context.Context, ticketID string, source string) (map[string]interface{}, error) {
	ticket := map[string]interface{}{}
	query := fmt.Sprintf(`
		SELECT
			t.*,
			ta.id as ticket_action_id,
			ta.requested_action as requested_action,
			ta.requested_by as requested_by,
			ta.requested_at as requested_at,
			ta.requested_message_id as requested_message_id,
			ta.response_action as response_action,
			ta.response_by as response_by,
			ta.response_at as response_at,
			ta.response_message_id as response_message_id,
			ta.status as ticket_action_status,
			ta.requested_meta as requested_meta,
			ta.response_meta as response_meta,
			kn.customer_escalated as escalated_to_3pl
		FROM cs_tickets t
		LEFT JOIN kiranabazar_ndrs kn ON t.order_id = kn.order_id
		LEFT JOIN cs_ticket_actions ta ON t.id = ta.ticket_id 
			and ta.created_at = (
					select max(created_at)
					from cs_ticket_actions ta2
					where ta2.ticket_id = t.id
			)
		WHERE t.id = '%s'
	`, ticketID)
	_, err := s.repository.CustomQuery(&ticket, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching ticket: %v", err)
	}

	if ticketActionID, ok := ticket["ticket_action_id"].(int64); ok && ticketActionID != 0 {
		if ticketActionStatus, ok := ticket["ticket_action_status"].(string); ok && ticketActionStatus == "pending" {
			ticket["block_seller_assignee"] = true
			if requestedAction, ok := ticket["requested_action"].(string); ok {
				if requestedAction == utils.TICKET_REQUESTED_ACTION_REFUND {
					ticket["show_approve_button"] = true
					ticket["show_challenge_button"] = true
				} else if requestedAction == utils.TICKET_REQUESTED_ACTION_CLARIFICATION {
					ticket["show_approve_button"] = true
				}
			}
		}
	}

	if source != "B2B_EXTERNAL" {
		ticketTags := []string{}
		if userId, ok := ticket["user_id"].(string); ok {
			if LOYALTY_DATA_7_MAY[userId] || LOYALTY_DATA_5_JULY[userId] {
				ticketTags = append(ticketTags, "loyalty_large_orders")
			}
		}
		ticket["ticket_tags"] = ticketTags
	}

	return ticket, nil
}

func (s *Service) GetTicketUserDetails(ctx context.Context, ticketID string, userID string) (*map[string]interface{}, error) {
	var ticket map[string]interface{}

	query := `	
		SELECT
			uos.total_placed_order_amount as total_placed_order_amount,
			uos.total_confirmed_order_amount as total_confirmed_order_amount,
			uos.total_delivered_order_amount as total_delivered_order_amount,
			uos.total_returned_order_amount as total_returned_order_amount,
			uos.total_cancelled_order_amount as total_cancelled_order_amount,
			uos.order_placed as order_placed,
			uos.confirmed_order as confirmed_order,
			uos.order_delivered as order_delivered,
			uos.returned_order as returned_order,
			uos.cancelled_order as cancelled_order
		FROM user_order_stats uos 
		WHERE `

	if userID != "" {
		// If userID is provided, use it directly
		query += fmt.Sprintf("uos.user_id = '%s'", userID)
	} else {
		// If userID is not provided, fetch it using ticketID
		query += fmt.Sprintf("uos.user_id = (SELECT user_id FROM cs_tickets WHERE id = '%s')", ticketID)
	}

	_, err := s.ReadOnlyRepository.CustomQuery(&ticket, query)
	if err != nil {
		fmt.Printf("Error fetching ticket user details: %v\n", err)
		return nil, nil
	}
	return &ticket, nil
}

func (s *Service) GetOrderScans(ctx context.Context, orderID string) (*string, *string, *string, error) {
	if orderID == "" {
		return nil, nil, nil, fmt.Errorf("order_id is required")
	}
	type OrderAwbs struct {
		AwbNumber string `db:"awb_number"`
	}
	query := fmt.Sprintf(`
		SELECT
			kam.awb_number
		FROM kiranabazar_awb_master kam
		WHERE kam.order_id = '%s' and kam.is_primary = true
	`, orderID)

	var orderAWBs []OrderAwbs
	_, err := s.ReadOnlyRepository.CustomQuery(&orderAWBs, query)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("error fetching order scans: %v", err)
	}
	if len(orderAWBs) > 0 {
		scansData, err := s.AWBMaster.GetAWBScans(context.Background(), orderAWBs[0].AwbNumber)
		if err != nil {
			return nil, nil, nil, fmt.Errorf("error fetching order scans: %v", err)
		}
		if scansData != nil {
			destiinationCity := scansData.Destination
			originCity := scansData.Origin
			currentCity := utils.FormatScanLocation(scansData.Scans[len(scansData.Scans)-1].Location)
			return &destiinationCity, &originCity, &currentCity, nil
		}
	}

	return nil, nil, nil, fmt.Errorf("error fetching order scans: %v", err)
}

func (s *Service) GetPendingReply(ctx context.Context, conversationID string) (bool, error) {
	if conversationID == "" {
		return false, fmt.Errorf("conversation_id is required")
	}

	query := fmt.Sprintf(`
		SELECT sender_type
		FROM cs_messages m
		WHERE m.conversation_id = '%s'
		AND m.private = 0
		AND (m.visible = 1 OR m.visible IS NULL)
		ORDER BY m.created_at DESC
		LIMIT 1
	`, conversationID)

	var result []struct {
		SenderType string `db:"sender_type"`
	}

	_, err := s.ReadOnlyRepository.CustomQuery(&result, query)
	if err != nil {
		return false, fmt.Errorf("error fetching last public sender type: %v", err)
	}

	// If no messages found, return nil
	if len(result) == 0 {
		return false, nil
	}

	pendingReply := false
	if result[0].SenderType == utils.MESSAGE_SENDER_USER {
		pendingReply = true
	}

	return pendingReply, nil
}

func (s *Service) GetTotalRefundProcessed(ctx context.Context, userID string) (int64, error) {
	var totalRefundProcessed int64
	query := fmt.Sprintf(`
		SELECT COALESCE(SUM(kop.refund_amount), 0)
		FROM kiranabazar_orders ko
		LEFT JOIN kiranabazar_order_payments kop ON ko.id = kop.order_id
		WHERE ko.user_id = '%s'
	`, userID)
	_, err := s.ReadOnlyRepository.CustomQuery(&totalRefundProcessed, query)
	if err != nil {
		return 0, fmt.Errorf("error fetching total refund processed: %v", err)
	}
	return totalRefundProcessed, nil
}

func (s *Service) GetTotalTicketsRaised(ctx context.Context, userID string) (int64, error) {
	var totalTicketsRaised int64
	query := fmt.Sprintf(`
		SELECT count(ct.id)
		FROM cs_tickets ct
		WHERE ct.user_id = '%s'
	`, userID)
	_, err := s.ReadOnlyRepository.CustomQuery(&totalTicketsRaised, query)
	if err != nil {
		return 0, fmt.Errorf("error fetching total tickets raised: %v", err)
	}
	return totalTicketsRaised, nil
}

func (s *Service) GetRecommendedAction(order dto.CsTicketOrderDetails, ticket map[string]interface{}, email string) []string {
	var categoryID, subCategoryID string
	recommendedActions := []string{}
	if meta, exists := ticket["meta"]; exists {
		if metaSlice, ok := meta.(string); ok {
			var issueDetailsArray []dto.AppSubmitTicketIssueDetails
			err := json.Unmarshal([]byte(metaSlice), &issueDetailsArray)
			if err != nil {
				return recommendedActions
			}
			if len(issueDetailsArray) > 0 {
				issueDetails := issueDetailsArray[0]
				categoryID = issueDetails.IssueCategory.ID
				subCategoryID = issueDetails.IssueSubCategory.ID
			}
		}
	}
	if categoryID == "otherIssues" && subCategoryID == "orderCancel" {
		canCancelOrder := canCancelOrder(utils.SafeString(order.Status), utils.SafeString(order.Seller), utils.SafeString(order.Courier), email)
		if canCancelOrder {
			recommendedActions = append(recommendedActions, "cancel_order")
		}
	}
	return recommendedActions
}

func (s *Service) GetCSTicketDetails(ctx context.Context, request *dto.GetCSTicketDetailsRequest) (*dto.GetCSTicketDetailsResponse, error) {
	var wg sync.WaitGroup
	var ticketErr, userErr, orderErr, trackingErr, scanErr, pendingReplyErr, totalRefundProcessedErr, totalTicketsRaisedErr error
	var ticket map[string]interface{}
	var userDetails *map[string]interface{}
	var orderDetails *dto.CsTicketOrderDetails
	var promisedDeliveryDate, destinationCity, currentCity *string
	var trackingInfo *dto.FetchAllWaybillsResponse
	var pendingReply bool
	var totalRefundProcessed int64
	var totalTicketsRaised int64

	wg.Add(8)

	go func() {
		defer wg.Done()
		ticket, ticketErr = s.GetTicketByID(ctx, request.Data.TicketID, request.Data.Source)
	}()

	go func() {
		defer wg.Done()
		userDetails, userErr = s.GetTicketUserDetails(ctx, request.Data.TicketID, request.Data.UserID)
	}()

	go func() {
		defer wg.Done()
		orderDetails, promisedDeliveryDate, orderErr = getKiranaBazarOrderDetails(s.repository, request.Data.OrderID)
	}()

	go func() {
		defer wg.Done()
		trackingInfo, trackingErr = s.FetchWallWaybills(ctx, dto.FetchAllWaybillsRequest{
			OrderID: request.Data.OrderID})
	}()

	go func() {
		defer wg.Done()
		destinationCity, _, currentCity, scanErr = s.GetOrderScans(ctx, request.Data.OrderID)
	}()

	go func() {
		defer wg.Done()
		pendingReply, pendingReplyErr = s.GetPendingReply(ctx, request.Data.ConversationID)
	}()

	go func() {
		defer wg.Done()
		totalRefundProcessed, totalRefundProcessedErr = s.GetTotalRefundProcessed(ctx, request.Data.UserID)
	}()

	go func() {
		defer wg.Done()
		totalTicketsRaised, totalTicketsRaisedErr = s.GetTotalTicketsRaised(ctx, request.Data.UserID)
	}()

	wg.Wait()

	if ticketErr != nil {
		return nil, fmt.Errorf("failed to get ticket details: %w", ticketErr)
	}
	if orderErr != nil {
		return nil, fmt.Errorf("failed to get order details: %w", orderErr)
	}
	if userErr != nil {
		fmt.Printf("Error fetching tracking info: %v\n", trackingErr)
	}
	if trackingErr != nil {
		fmt.Printf("Error fetching tracking info: %v\n", trackingErr)
	}
	if scanErr != nil {
		fmt.Printf("Error fetching tracking info: %v\n", scanErr)
	}
	if pendingReplyErr != nil {
		fmt.Printf("Error getting pending reply: %v\n", pendingReplyErr)
	}
	if totalRefundProcessedErr != nil {
		fmt.Printf("Error getting total refund processed: %v\n", totalRefundProcessedErr)
	}
	if totalTicketsRaisedErr != nil {
		fmt.Printf("Error getting total tickets raised: %v\n", totalTicketsRaisedErr)
	}

	// Prepare delivery template parameters
	var awbNumber, courier string
	if len(trackingInfo.AWBs) > 0 {
		awbNumber = trackingInfo.AWBs[0].AWBNumber
		courier = trackingInfo.AWBs[0].Courier
	}

	cannedResponses := utils.GetDefaultTemplates(dto.DeliveryTemplateParams{
		EstimatedDelivery: utils.SafeDeref(promisedDeliveryDate),
		CurrentCity:       utils.SafeDeref(currentCity),
		DestinationCity:   utils.SafeDeref(destinationCity),
		AWB:               awbNumber,
		Courier:           courier,
	})

	ticket["pending_reply"] = pendingReply
	response := &dto.GetCSTicketDetailsResponse{
		Ticket:             ticket,
		OrderDetails:       orderDetails,
		TrackingInfo:       trackingInfo.AWBs,
		RecommendedActions: s.GetRecommendedAction(*orderDetails, ticket, request.Email),
	}
	if (request.Data.Source == "B2B_INTERNAL") || (request.Data.Source == "") {
		response.CannedResponses = cannedResponses
	}
	if userDetails != nil && *userDetails != nil {
		(*userDetails)["total_refund_processed"] = totalRefundProcessed
		(*userDetails)["total_tickets_raised"] = totalTicketsRaised
		response.UserDetails = *userDetails
	}

	return response, nil
}

// PerformTicketAction handles various actions that can be performed on a ticket
func (s *Service) PerformTicketAction(ctx context.Context, request *dto.TicketActionRequest) (*dto.TicketActionResponse, error) {
	// Validate request
	if request.Data.TicketID == "" {
		return nil, fmt.Errorf("ticket_id is required")
	}

	// Fetch the ticket
	var ticket dao.CSTicket
	_, err := s.repository.Find(map[string]interface{}{
		"id": request.Data.TicketID,
	}, &ticket)
	if err != nil {
		return nil, fmt.Errorf("ticket not found: %v", err)
	}

	// Track changes for audit and response
	changes := make(map[string]interface{})
	now := time.Now().Unix()

	// Dispatch to appropriate handler based on action type
	switch request.Data.Action {
	case "change_assignee", "seller_response":
		changes, err = s.assignTicket(ctx, ticket, request.Data)
	case "change_priority":
		changes, err = s.changeTicketPriority(ticket, request.Data.NewPriority, request.Data.ActionBy, request.Data.IgnoreTicketStatusChange)
	case "change_category":
		changes, err = s.changeTicketCategory(ctx, ticket, request.Data.NewCategory, request.Data.ActionBy)
	case "change_status":
		if request.Data.NewStatus == *ticket.Status {
			return nil, fmt.Errorf("ticket is already in the requested status")
		}
		changes, err = s.changeTicketStatus(ctx, ticket, request.Data)
	default:
		return nil, fmt.Errorf("invalid action type: %s", request.Data.Action)
	}

	if err != nil {
		return nil, err
	}

	// Fetch the updated ticket
	var updatedTicket dao.CSTicket
	_, err = s.repository.Find(map[string]interface{}{
		"id": request.Data.TicketID,
	}, &updatedTicket)
	if err != nil {
		return nil, fmt.Errorf("error fetching updated ticket: %v", err)
	}

	// Prepare response
	response := dto.TicketActionResponse{
		Success:      true,
		TicketID:     request.Data.TicketID,
		Changes:      changes,
		UpdatedAt:    now,
		UpdatedBy:    request.Data.ActionBy,
		ActionType:   request.Data.Action,
		UpdatedState: updatedTicket,
	}

	return &response, nil
}

// assignTicket handles ticket assignment changes
func (s *Service) assignTicket(ctx context.Context, ticket dao.CSTicket, request dto.TicketActionRequestData) (map[string]interface{}, error) {
	changes := make(map[string]interface{})

	// Determine the new assignee and update ticket state
	assigneeID, ownershipState, err := s.determineAssigneeDetails(ctx, ticket, request)
	if err != nil {
		return nil, err
	}

	// Update ticket status if it's open
	ticketStatus := *ticket.Status
	if ticketStatus == utils.TICKET_STATUS_OPEN {
		ticketStatus = utils.TICKET_STATUS_INPROGRESS
	}

	// Update the ticket in database
	err = s.updateTicketAssignment(ticket.ID, assigneeID, request.ActionBy, ticketStatus, ownershipState)
	if err != nil {
		return nil, err
	}

	if request.Action == "change_assignee" {
		// Fire the reassigned event in a goroutine
		go func() {
			err := s.trackTicketReassigned(ctx, ticket.ID, request.ActionBy, request.NewAssignee, request.ActionData.Note)
			if err != nil {
				fmt.Printf("Error tracking ticket reassigned: %v\n", err)
			}
		}()
	}

	// Add action message to conversation
	actionMessageResponse, err := s.addActionMessage(ctx, ticket, request, ticket.CurrentAssigneeID, &assigneeID)
	if err != nil {
		return nil, fmt.Errorf("error adding action message: %v", err)
	}

	// Handle special agent actions (request/respond)
	if isAgentActionRequest(request.ActionData.RequestedAction) || isSellerResponseAction(request.ActionData.ResponseAction) {
		var actionMessageID string
		if actionMessageResponse != nil {
			actionMessageID = actionMessageResponse.Message.ID
		}

		_, err := s.recordTicketAction(ctx, ticket, request, actionMessageID)
		if err != nil {
			return nil, fmt.Errorf("error recording ticket action: %v", err)
		}
	}

	go func() {
		metadata := map[string]interface{}{
			"new_assignee": request.NewAssignee,
		}
		err := s.handleProgressStateTransition(ctx, ticket.ID, "ticket_assigned",
			request.ActionBy, metadata)
		if err != nil {
			fmt.Printf("Error updating progress state for assignment: %v\n", err)
		}
	}()

	// Track changes for response
	changes["assignee"] = map[string]interface{}{
		"old": ticket.CurrentAssigneeID,
		"new": assigneeID,
	}

	return changes, nil
}

// determineAssigneeDetails decides the new assignee and related states based on the request
func (s *Service) determineAssigneeDetails(ctx context.Context, ticket dao.CSTicket, request dto.TicketActionRequestData) (string, string, error) {
	var assigneeID string
	var ownershipState string

	if request.Action == "change_assignee" {
		switch request.NewAssignee {
		case "seller":
			// Assign to seller
			sellerAgents, err := s.GetAgentBySeller(ctx, *ticket.Seller)
			if err != nil {
				return "", "", fmt.Errorf("error fetching seller agents: %v", err)
			}
			if len(sellerAgents) == 0 {
				return "", "", fmt.Errorf("no agents found for seller: %s", *ticket.Seller)
			}
			assigneeID = sellerAgents[0].ID
			ownershipState = utils.AGENT_TEAM_SELLER
		case "tech", "ops", "cs":
			// Assign to a team
			teamEmailMap := map[string]string{
				"cs":   "<EMAIL>",
				"tech": "<EMAIL>",
				"ops":  "<EMAIL>",
			}

			email := teamEmailMap[request.NewAssignee]
			agentData, err := s.GetAgentByEmail(ctx, email)
			if err != nil {
				return "", "", fmt.Errorf("error fetching agent by email: %v", err)
			}

			assigneeID = agentData.ID
			ownershipState = *agentData.Team

		default:
			// Assign to a specific agent by email
			agentData, err := s.GetAgentByEmail(ctx, request.NewAssignee)
			if err != nil {
				return "", "", fmt.Errorf("error fetching agent by email: %v", err)
			}

			assigneeID = agentData.ID
			ownershipState = *agentData.Team
		}
	} else if request.Action == "seller_response" {
		// Return to original owner after seller response
		assigneeID = ticket.PrimaryOwnerID
		ownershipState = utils.AGENT_TEAM_CS
	}

	return assigneeID, ownershipState, nil
}

// updateTicketAssignment updates the ticket in the database
func (s *Service) updateTicketAssignment(ticketID, assigneeID, actionBy, status, ownershipState string) error {
	query := fmt.Sprintf(`
		UPDATE cs_tickets
		SET current_assignee_id = '%s', 
			updated_at = %d, 
			updated_by = '%s', 
			status = '%s', 
			ownership_state = '%s'
		WHERE id = '%s'
	`, assigneeID, time.Now().UnixMilli(), actionBy, status, ownershipState, ticketID)

	_, err := s.repository.CustomQuery(nil, query)
	return err
}

func (s *Service) changeTicketStatusToInProgress(ctx context.Context, ticket dao.CSTicket, actionByEmail, actionByID string) error {
	var email string
	if actionByEmail != "" {
		email = actionByEmail
	} else {
		agent, err := s.GetAgentByID(ctx, actionByID)
		if err != nil {
			return fmt.Errorf("error fetching agent by ID: %v", err)
		}
		email = agent.Email
	}
	_, err := s.changeTicketStatus(ctx, ticket, dto.TicketActionRequestData{
		TicketID:  ticket.ID,
		Action:    "change_status",
		ActionBy:  email,
		NewStatus: utils.TICKET_STATUS_INPROGRESS,
	})
	if err != nil {
		return fmt.Errorf("error changing ticket status: %v", err)
	}
	return nil
}

// changeTicketPriority handles changing the priority of a ticket
func (s *Service) changeTicketPriority(ticket dao.CSTicket, priority string, actionBy string, ignoreTicketStatusChange bool) (map[string]interface{}, error) {
	changes := make(map[string]interface{})
	agent, err := s.GetAgentByEmail(context.Background(), actionBy)
	if err != nil {
		return nil, fmt.Errorf("error fetching agent by ID: %v", err)
	}
	// Update ticket in database
	query := fmt.Sprintf(`
		UPDATE cs_tickets
		SET priority = '%s', updated_at = %d, updated_by = '%s'
		WHERE id = '%s'
	`, priority, time.Now().UnixMilli(), agent.ID, ticket.ID)
	_, err = s.repository.CustomQuery(nil, query)
	if err != nil {
		return nil, fmt.Errorf("error updating ticket priority: %v", err)
	}

	// Add system message for priority change
	oldPriorityStr := ""
	if ticket.Priority != nil {
		oldPriorityStr = utils.TICKETS_PRIORITY_VALUES_MAP[*ticket.Priority]["label"]
	}
	newPriorityStr := utils.TICKETS_PRIORITY_VALUES_MAP[priority]["label"]

	actionRequest := dto.TicketActionRequestData{
		Action:   "change_priority",
		ActionBy: actionBy,
	}

	_, err = s.addActionSystemMessage(context.Background(), ticket, actionRequest, oldPriorityStr, newPriorityStr)
	if err != nil {
		return nil, fmt.Errorf("error adding priority change system message: %v", err)
	}

	if !ignoreTicketStatusChange && ticket.Status != nil && *ticket.Status != utils.TICKET_STATUS_INPROGRESS {
		go func() {
			err := s.changeTicketStatusToInProgress(context.Background(), ticket, actionBy, "")
			if err != nil {
				fmt.Printf("Error changing ticket status to in progress: %v\n", err)
			}
		}()
	}

	// Track change for response
	changes["priority"] = map[string]interface{}{
		"old": ticket.Priority,
		"new": priority,
	}

	return changes, nil
}

// changeTicketCategory handles changing the category of a ticket
func (s *Service) changeTicketCategory(ctx context.Context, ticket dao.CSTicket, categoryID int, actionBy string) (map[string]interface{}, error) {
	changes := make(map[string]interface{})
	agent, err := s.GetAgentByEmail(context.Background(), actionBy)
	if err != nil {
		return nil, fmt.Errorf("error fetching agent by ID: %v", err)
	}
	// Update ticket in database
	query := fmt.Sprintf(`
		UPDATE cs_tickets
		SET category = %d, updated_at = %d, updated_by = '%s'
		WHERE id = '%s'
	`, categoryID, time.Now().UnixMilli(), agent.ID, ticket.ID)
	_, err = s.repository.CustomQuery(nil, query)
	if err != nil {
		return nil, fmt.Errorf("error updating ticket category: %v", err)
	}

	// Add system message for category change
	oldCategoryData, err := s.GetTicketCategoryByID(ctx, *ticket.Category)
	if err != nil {
		return nil, fmt.Errorf("error fetching old category data: %v", err)
	}
	newCategoryData, err := s.GetTicketCategoryByID(ctx, categoryID)
	if err != nil {
		return nil, fmt.Errorf("error fetching new category data: %v", err)
	}

	actionRequest := dto.TicketActionRequestData{
		Action:   "change_category",
		ActionBy: actionBy,
	}

	_, err = s.addActionSystemMessage(context.Background(), ticket, actionRequest, oldCategoryData.Name, newCategoryData.Name)
	if err != nil {
		return nil, fmt.Errorf("error adding category change system message: %v", err)
	}

	if ticket.Status != nil && *ticket.Status != utils.TICKET_STATUS_INPROGRESS {
		go func() {
			err := s.changeTicketStatusToInProgress(context.Background(), ticket, actionBy, "")
			if err != nil {
				fmt.Printf("Error changing ticket status to in progress: %v\n", err)
			}
		}()
	}

	// Track change for response
	changes["category"] = map[string]interface{}{
		"old": ticket.Category,
		"new": categoryID,
	}

	return changes, nil
}

// changeTicketStatus handles changing the status of a ticket
func (s *Service) changeTicketStatus(ctx context.Context, ticket dao.CSTicket, request dto.TicketActionRequestData) (map[string]interface{}, error) {
	changes := make(map[string]interface{})

	// Determine progress state based on status
	progressState := *ticket.ProgressState
	if request.NewStatus == utils.TICKET_STATUS_RESOLVED {
		progressState = utils.TICKET_PROGRESS_RESOLVED
	} else if request.NewStatus == utils.TICKET_STATUS_CLOSED {
		progressState = utils.TICKET_PROGRESS_CLOSED
	}

	actionBy, err := s.GetAgentByEmail(context.Background(), request.ActionBy)
	if err != nil {
		return nil, fmt.Errorf("error fetching agent by email: %v", err)
	}

	// Update ticket in database
	query := fmt.Sprintf(`
		UPDATE cs_tickets
		SET status = '%s', updated_at = %d, updated_by = '%s', progress_state = '%s'
		WHERE id = '%s'
	`, request.NewStatus, time.Now().UnixMilli(), actionBy.ID, progressState, ticket.ID)

	_, err = s.repository.CustomQuery(nil, query)
	if err != nil {
		return nil, fmt.Errorf("error updating ticket status: %v", err)
	}

	if request.NewStatus == utils.TICKET_STATUS_RESOLVED {
		// Fire the resolved event in a goroutine
		go func() {
			err := s.trackTicketResolved(ctx, ticket.ID, actionBy.Email, request.ActionData.Note)
			if err != nil {
				fmt.Printf("Error tracking ticket resolved: %v\n", err)
			}
		}()
	}

	go func() {
		metadata := map[string]interface{}{
			"new_status": request.NewStatus,
		}
		err := s.handleProgressStateTransition(context.Background(), ticket.ID, "status_changed",
			actionBy.ID, metadata)
		if err != nil {
			fmt.Printf("Error updating progress state for status change: %v\n", err)
		}
	}()

	// Add system message for status change
	oldStatusStr := ""
	if ticket.Status != nil {
		oldStatusStr = *ticket.Status
	}

	actionRequest := dto.TicketActionRequestData{
		Action:   "change_status",
		ActionBy: actionBy.Email,
	}

	_, err = s.addActionSystemMessage(context.Background(), ticket, actionRequest, oldStatusStr, request.NewStatus)
	if err != nil {
		return nil, fmt.Errorf("error adding status change system message: %v", err)
	}

	if includes([]string{utils.TICKET_STATUS_RESOLVED, utils.TICKET_STATUS_INPROGRESS_3PL}, request.NewStatus) {
		_, err = s.addDetailedActionMessage(ctx, ticket, request)
		if err != nil {
			return nil, fmt.Errorf("error adding detailed action message: %v", err)
		}
	}

	// Track change for response
	changes["status"] = map[string]interface{}{
		"old": ticket.Status,
		"new": request.NewStatus,
	}

	return changes, nil
}

// recordTicketAction records agent requests or seller responses in the ticket_actions table
func (s *Service) recordTicketAction(ctx context.Context, ticket dao.CSTicket, request dto.TicketActionRequestData, messageID string) (*string, error) {
	timestamp := time.Now().UTC().UnixMilli()

	// Handle agent requests (refund, clarification)
	if isAgentActionRequest(request.ActionData.RequestedAction) {
		return s.createTicketActionRecord(ctx, ticket, request, messageID, timestamp)
	} else if isSellerResponseAction(request.ActionData.ResponseAction) { // Handle seller responses (approve, challenge, clarify)
		return s.updateTicketActionRecord(ctx, request, messageID, timestamp)
	}

	return nil, nil
}

// createTicketActionRecord creates a new ticket action record
func (s *Service) createTicketActionRecord(ctx context.Context, ticket dao.CSTicket, request dto.TicketActionRequestData, messageID string, timestamp int64) (*string, error) {
	ticketActionID := GenerateDistributedID("ticket_action")

	// Convert string to int64
	messageIDInt, err := strconv.ParseInt(messageID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("error parsing message ID: %v", err)
	}

	// Get the requesting agent
	requestedBy, err := s.GetAgentByEmail(ctx, request.ActionData.RequestedBy)
	if err != nil {
		return nil, fmt.Errorf("error fetching agent by email: %v", err)
	}

	// Create the ticket action record
	ticketAction := dao.CSTicketAction{
		ID:                 ticketActionID,
		TicketID:           ticket.ID,
		RequestedAction:    request.ActionData.RequestedAction,
		RequestedBy:        requestedBy.ID,
		RequestedAt:        timestamp,
		RequestedMessageID: &messageIDInt,
		Status:             "pending",
		CreatedAt:          timestamp,
		UpdatedAt:          timestamp,
		ConversationID:     ticket.ConversationID,
		RequestedMeta:      request.ActionData.RequestedMeta,
	}

	_, err = s.repository.Create(&ticketAction)
	if err != nil {
		return nil, fmt.Errorf("error creating ticket action: %v", err)
	}

	return &ticketActionID, nil
}

// updateTicketActionRecord updates an existing ticket action record with response information
func (s *Service) updateTicketActionRecord(ctx context.Context, request dto.TicketActionRequestData, messageID string, timestamp int64) (*string, error) {
	// Convert string to int64
	messageIDInt, err := strconv.ParseInt(messageID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("error parsing message ID: %v", err)
	}

	// Get the responding agent
	responseBy, err := s.GetAgentByEmail(ctx, request.ActionData.ResponseBy)
	if err != nil {
		return nil, fmt.Errorf("error fetching agent by email: %v", err)
	}

	var responseMeta string
	if string(request.ActionData.ResponseMeta) == "" {
		responseMeta = "{}" // Valid empty JSON object
	} else {
		responseMeta = string(request.ActionData.ResponseMeta)
	}

	updateQuery := fmt.Sprintf(`
		UPDATE cs_ticket_actions
		SET response_action = '%s',
			response_by = '%s',
			response_at = %d,
			response_message_id = %d,
			status = 'completed',
			updated_at = %d,
			response_meta = '%s'
		WHERE id = '%s'
	`, request.ActionData.ResponseAction, responseBy.ID, timestamp, messageIDInt, timestamp, responseMeta, request.ActionData.TicketActionID)
	_, err = s.repository.CustomQuery(nil, updateQuery)
	if err != nil {
		return nil, fmt.Errorf("error updating ticket action: %v", err)
	}

	return nil, nil
}

// addActionMessage adds action messages to the conversation
func (s *Service) addActionMessage(ctx context.Context, ticket dao.CSTicket, request dto.TicketActionRequestData, oldValue *string, newValue *string) (*dto.SendMessageResponse, error) {
	// Convert possibly nil string pointers to empty strings
	oldValueStr := ""
	if oldValue != nil {
		oldValueStr = *oldValue
	}

	newValueStr := ""
	if newValue != nil {
		newValueStr = *newValue
	}

	// Add system message
	_, err := s.addActionSystemMessage(ctx, ticket, request, oldValueStr, newValueStr)
	if err != nil {
		return nil, fmt.Errorf("error adding action system message: %v", err)
	}

	// Handle special user-visible messages for assignment changes or seller responses
	if request.Action == "change_assignee" || request.Action == "seller_response" {
		return s.addDetailedActionMessage(ctx, ticket, request)
	}

	return nil, nil
}

// addActionSystemMessage adds a system message to the conversation about the action taken
func (s *Service) addActionSystemMessage(ctx context.Context, ticket dao.CSTicket, request dto.TicketActionRequestData, oldValue string, newValue string) (*dto.SendMessageResponse, error) {
	var messageContent string
	var messageType string
	var actionBy string

	if request.ActionBy != "" {
		agentInfo, err := s.GetAgentByEmail(ctx, request.ActionBy)
		if err != nil {
			return nil, fmt.Errorf("error fetching agent by email: %v", err)
		}
		actionBy = agentInfo.Name
	}

	// Create appropriate message based on action type
	switch request.Action {
	case "change_assignee":
		if newValue == "" {
			return nil, fmt.Errorf("new assignee is required")
		}

		// Get current assignee's name
		currentAssignee := ""
		if ticket.CurrentAssigneeID != nil {
			agentData, err := s.GetAgentByID(ctx, *ticket.CurrentAssigneeID)
			if err != nil {
				return nil, fmt.Errorf("error fetching agent by ID: %v", err)
			}

			if (agentData.Team != nil) && (*agentData.Team == "seller") {
				currentAssignee = "seller"
			} else {
				currentAssignee = agentData.Name
			}
		}

		// Get new assignee's name
		newAssignee := ""
		agentData, err := s.GetAgentByID(ctx, newValue)
		if err != nil {
			return nil, fmt.Errorf("error fetching agent by ID: %v", err)
		}

		if (agentData.Team != nil) && (*agentData.Team == "seller") {
			newAssignee = "seller"
		} else {
			newAssignee = agentData.Name
		}

		// Create appropriate message
		if currentAssignee == "" {
			messageContent = fmt.Sprintf("%s assigned ticket to %s.", actionBy, newAssignee)
		} else {
			messageContent = fmt.Sprintf("%s assigned ticket from %s to %s.", actionBy, currentAssignee, newAssignee)
		}

		messageType = utils.MESSAGE_TYPE_ASSIGNEE_CHANGE

	case "change_priority":
		messageContent = fmt.Sprintf("%s changed ticket priority from %s to %s.", actionBy, oldValue, newValue)
		messageType = utils.MESSAGE_TYPE_PRIORITY_CHANGE

	case "change_category":
		messageContent = fmt.Sprintf("%s changed ticket status from %s to %s.", actionBy, oldValue, newValue)
		messageType = utils.MESSAGE_TYPE_CATEGORY_CHANGE

	case "change_status":
		messageContent = fmt.Sprintf("%s changed status from %s to %s", actionBy, oldValue, newValue)
		messageType = utils.MESSAGE_TYPE_STATUS_CHANGE

	case "seller_response":
		messageContent = fmt.Sprintf("Seller responded with: %s", utils.TICKET_ACTION_MAP[request.ActionData.ResponseAction])
		messageType = utils.MESSAGE_TYPE_SELLER_REPLY

	case "ticket_rating":
		messageContent = fmt.Sprintf("User rated the ticket with %s", newValue)
		messageType = utils.MESSAGE_TYPE_TICKET_RESOLUTION_RATING_ADDED

	case "ticket_reopened":
		messageContent = fmt.Sprintf("User Reopened this ticket")
		messageType = utils.MESSAGE_TYPE_TICKET_REOPENED

	default:
		return nil, fmt.Errorf("invalid action type: %s", request.Action)
	}

	// Send the system message
	privateMessage := true
	messageResponse, err := s.SendMessage(ctx, &dto.SendMessageRequest{
		Data: dto.SendMessageRequestData{
			TicketID:       ticket.ID,
			ConversationID: ticket.ConversationID,
			Content:        messageContent,
			SenderID:       utils.MESSAGE_SENDER_SYSTEM,
			SenderType:     utils.MESSAGE_SENDER_SYSTEM,
			Private:        &privateMessage,
			MessageType:    messageType,
			Status:         "sent",
			Attachments:    request.ActionData.Attachments,
			Source:         utils.MESSAGE_SENDER_SYSTEM,
		},
	})

	if err != nil {
		return nil, fmt.Errorf("error sending system message: %v", err)
	}

	return messageResponse, nil
}

// addDetailedActionMessage adds a detailed message from an agent or seller about the action
func (s *Service) addDetailedActionMessage(ctx context.Context, ticket dao.CSTicket, request dto.TicketActionRequestData) (*dto.SendMessageResponse, error) {
	var messageContent string
	var messageType string
	var senderType string
	var senderID string

	var requestedMeta map[string]interface{}
	if (request.ActionData.RequestedMeta != nil) && (string(request.ActionData.RequestedMeta) != "") {
		err := json.Unmarshal(request.ActionData.RequestedMeta, &requestedMeta)
		if err != nil {
			return nil, fmt.Errorf("error unmarshalling requested meta: %v", err)
		}
	}

	if request.Action == "change_assignee" {
		// Handle assignee change message
		messageContent = fmt.Sprintf("Requested Action from %s\n", request.NewAssignee)
		messageType = utils.MESSAGE_TYPE_ASSIGNEE_CHANGE
		senderType = utils.MESSAGE_SENDER_AGENT

		// Add special content for seller assignment
		if request.NewAssignee == "seller" {
			// Add recommended action
			messageContent += fmt.Sprintf(" Recommended Action: %s\n", utils.TICKET_ACTION_MAP[request.ActionData.RequestedAction])

			if requestedMeta["refund_amount"] != nil {
				messageContent += fmt.Sprintf(" Refund Amount: %s\n", requestedMeta["refund_amount"])
			}

			// Set appropriate message type
			if request.ActionData.RequestedAction == utils.TICKET_REQUESTED_ACTION_REFUND {
				messageType = utils.MESSAGE_TYPE_AGENT_REQUEST_REFUND
			} else if request.ActionData.RequestedAction == utils.TICKET_REQUESTED_ACTION_CLARIFICATION {
				messageType = utils.MESSAGE_TYPE_AGENT_REQUEST_CLARIFICATION
			} else {
				return nil, fmt.Errorf("invalid requested action: %s", request.ActionData.RequestedAction)
			}
		}

		// Add note if present
		messageContent += fmt.Sprintf("Note: %s", request.ActionData.Note)

	} else if request.Action == "seller_response" {
		// Handle seller response message
		messageContent = fmt.Sprintf("Seller responded with: %s\n", utils.TICKET_ACTION_MAP[request.ActionData.ResponseAction])
		messageContent += fmt.Sprintf("Note: %s", request.ActionData.Note)
		senderType = utils.MESSAGE_SENDER_SELLER

		// Set appropriate message type based on response action
		switch request.ActionData.ResponseAction {
		case utils.TICKET_RESPONSE_ACTION_APPROVE_REFUND:
			messageType = utils.MESSAGE_TYPE_SELLER_APPROVE_REFUND
		case utils.TICKET_RESPONSE_ACTION_CHALLENGE_REFUND:
			messageType = utils.MESSAGE_TYPE_SELLER_CHALLENGE_REFUND
		case utils.TICKET_RESPONSE_ACTION_PROVIDE_CLARIFICATION:
			messageType = utils.MESSAGE_TYPE_SELLER_PROVIDE_CLARIFICATION
		default:
			return nil, fmt.Errorf("invalid response action: %s", request.ActionData.ResponseAction)
		}
	} else if (request.Action == "change_status") && ((request.NewStatus == utils.TICKET_STATUS_RESOLVED) || (request.NewStatus == utils.TICKET_STATUS_INPROGRESS_3PL)) {
		// Handle status change message
		messageContent = fmt.Sprintf("%s changed status to %s\n", request.ActionBy, request.NewStatus)
		messageContent += fmt.Sprintf("Note: %s", request.ActionData.Note)
		messageType = utils.MESSAGE_TYPE_TICKET_RESOLUTION
		senderType = utils.MESSAGE_SENDER_AGENT
	} else if request.Action == "ticket_rating" {
		var rating float64
		if requestedMeta["rating"] != nil {
			rating = requestedMeta["rating"].(float64)
		}
		messageContent = fmt.Sprintf("User Rated Ticket With %1f\n", rating)
		messageContent += fmt.Sprintf("Note: %s", request.ActionData.Note)
		senderType = utils.MESSAGE_SENDER_USER
		messageType = utils.MESSAGE_TYPE_TICKET_RESOLUTION_RATING_ADDED
	} else {
		return nil, fmt.Errorf("invalid action type for detailed message: %s", request.Action)
	}

	// Get the agent who performed the action
	if request.Action != "ticket_rating" {
		actionAgent, err := s.GetAgentByEmail(ctx, request.ActionBy)
		if err != nil {
			return nil, fmt.Errorf("error fetching agent by Email: %v", err)
		}
		senderID = actionAgent.ID
	} else {
		senderID = request.ActionBy
	}

	// Send the message
	privateMessage := true
	messageResponse, err := s.SendMessage(ctx, &dto.SendMessageRequest{
		Data: dto.SendMessageRequestData{
			TicketID:       ticket.ID,
			ConversationID: ticket.ConversationID,
			Content:        messageContent,
			SenderID:       senderID,
			SenderType:     senderType,
			Private:        &privateMessage,
			MessageType:    messageType,
			Status:         "sent",
			Attachments:    request.ActionData.Attachments,
			Source:         utils.MESSAGE_SENDER_SYSTEM,
		},
	})

	if err != nil {
		return nil, fmt.Errorf("error sending detailed action message: %v", err)
	}

	return messageResponse, nil
}

// Helper functions

// isAgentActionRequest checks if the action is an agent request action
func isAgentActionRequest(action string) bool {
	return slices.Contains(
		[]string{
			utils.TICKET_REQUESTED_ACTION_REFUND,
			utils.TICKET_REQUESTED_ACTION_CLARIFICATION,
		},
		action,
	)
}

// isSellerResponseAction checks if the action is a seller response action
func isSellerResponseAction(action string) bool {
	return slices.Contains(
		[]string{
			utils.TICKET_RESPONSE_ACTION_APPROVE_REFUND,
			utils.TICKET_RESPONSE_ACTION_CHALLENGE_REFUND,
			utils.TICKET_RESPONSE_ACTION_PROVIDE_CLARIFICATION,
		},
		action,
	)
}

func (s *Service) handleProgressStateTransition(
	ctx context.Context,
	ticketID string,
	actionType string,
	actionBy string,
	metadata map[string]interface{},
) error {
	// Fetch current ticket state
	var ticket dao.CSTicket
	_, err := s.repository.Find(map[string]interface{}{
		"id": ticketID,
	}, &ticket)
	if err != nil {
		return fmt.Errorf("error fetching ticket: %v", err)
	}

	// If ticket doesn't have a progress state, set a default
	currentProgressState := utils.TICKET_PROGRESS_NEW
	if ticket.ProgressState != nil {
		currentProgressState = *ticket.ProgressState
	}

	currentOwnershipState := ""
	if ticket.OwnershipState != nil {
		currentOwnershipState = *ticket.OwnershipState
	}

	newProgressState := currentProgressState

	// Determine the new progress state based on action type
	switch actionType {
	case "ticket_created":
		newProgressState = utils.TICKET_PROGRESS_NEW

	case "agent_viewed":
		// When agent views ticket for the first time
		if currentProgressState == utils.TICKET_PROGRESS_NEW {
			newProgressState = utils.TICKET_PROGRESS_PENDING_AGENT_RESP
		}
		// When agent views ticket that's pending their review
		if currentProgressState == utils.TICKET_PROGRESS_PENDING_AGENT_REVIEW {
			newProgressState = utils.TICKET_PROGRESS_PENDING_AGENT_RESP
		}
		// Team-specific viewing transitions
		if currentOwnershipState == utils.AGENT_TEAM_SELLER &&
			currentProgressState == utils.TICKET_PROGRESS_PENDING_SELLER_REVIEW {
			newProgressState = utils.TICKET_PROGRESS_PENDING_SELLER_RESP
		}
		if currentOwnershipState == utils.AGENT_TEAM_TECH &&
			currentProgressState == utils.TICKET_PROGRESS_PENDING_TECH_REVIEW {
			newProgressState = utils.TICKET_PROGRESS_PENDING_TECH_RESP
		}
		if currentOwnershipState == utils.AGENT_TEAM_OPS &&
			currentProgressState == utils.TICKET_PROGRESS_PENDING_OPS_REVIEW {
			newProgressState = utils.TICKET_PROGRESS_PENDING_OPS_RESP
		}

	case "user_message":
		// When user sends a message after any team reply
		if slices.Contains([]string{
			utils.TICKET_PROGRESS_AGENT_REPLIED,
			utils.TICKET_PROGRESS_SELLER_REPLIED,
			utils.TICKET_PROGRESS_TECH_REPLIED,
			utils.TICKET_PROGRESS_OPS_REPLIED,
		}, currentProgressState) {
			// User responding - ticket goes back to agent for review
			newProgressState = utils.TICKET_PROGRESS_PENDING_AGENT_REVIEW
		}

	case "agent_message":
		// When CS agent sends a message
		if slices.Contains([]string{
			utils.TICKET_PROGRESS_NEW,
			utils.TICKET_PROGRESS_PENDING_AGENT_RESP,
			utils.TICKET_PROGRESS_PENDING_AGENT_REVIEW,
		}, currentProgressState) {
			newProgressState = utils.TICKET_PROGRESS_AGENT_REPLIED
		}

	case "seller_message":
		// When seller sends a message - ticket should go back to agent
		if currentProgressState == utils.TICKET_PROGRESS_PENDING_SELLER_REVIEW ||
			currentProgressState == utils.TICKET_PROGRESS_PENDING_SELLER_RESP {
			// Mark as seller replied, but it will go back to agent
			newProgressState = utils.TICKET_PROGRESS_SELLER_REPLIED
		}

	case "tech_message":
		// When tech team sends a message - ticket should go back to agent
		if currentProgressState == utils.TICKET_PROGRESS_PENDING_TECH_REVIEW ||
			currentProgressState == utils.TICKET_PROGRESS_PENDING_TECH_RESP {
			// Mark as tech replied, but it will go back to agent
			newProgressState = utils.TICKET_PROGRESS_TECH_REPLIED
		}

	case "ops_message":
		// When ops team sends a message - ticket should go back to agent
		if currentProgressState == utils.TICKET_PROGRESS_PENDING_OPS_REVIEW ||
			currentProgressState == utils.TICKET_PROGRESS_PENDING_OPS_RESP {
			// Mark as ops replied, but it will go back to agent
			newProgressState = utils.TICKET_PROGRESS_OPS_REPLIED
		}

	case "ticket_assigned":
		// When ticket is assigned to different teams
		if newAssignee, ok := metadata["new_assignee"].(string); ok {
			switch newAssignee {
			case "seller":
				newProgressState = utils.TICKET_PROGRESS_PENDING_SELLER_REVIEW
			case "tech":
				newProgressState = utils.TICKET_PROGRESS_PENDING_TECH_REVIEW
			case "ops":
				newProgressState = utils.TICKET_PROGRESS_PENDING_OPS_REVIEW
			default:
				// Assigned to a specific agent - check their team
				if agentData, err := s.GetAgentByEmail(ctx, newAssignee); err == nil {
					if agentData.Team != nil {
						switch *agentData.Team {
						case utils.AGENT_TEAM_SELLER:
							newProgressState = utils.TICKET_PROGRESS_PENDING_SELLER_REVIEW
						case utils.AGENT_TEAM_TECH:
							newProgressState = utils.TICKET_PROGRESS_PENDING_TECH_REVIEW
						case utils.AGENT_TEAM_OPS:
							newProgressState = utils.TICKET_PROGRESS_PENDING_OPS_REVIEW
						case utils.AGENT_TEAM_CS:
							// Assigned to another CS agent
							newProgressState = utils.TICKET_PROGRESS_PENDING_AGENT_RESP
						}
					}
				}
			}
		}

	case "ticket_returned_to_agent":
		// When ticket comes back to agent after other team replies
		newProgressState = utils.TICKET_PROGRESS_PENDING_AGENT_REVIEW

	case "status_changed":
		// When ticket status changes
		if newStatus, ok := metadata["new_status"].(string); ok {
			switch newStatus {
			case utils.TICKET_STATUS_RESOLVED:
				newProgressState = utils.TICKET_PROGRESS_RESOLVED
			case utils.TICKET_STATUS_CLOSED:
				newProgressState = utils.TICKET_PROGRESS_CLOSED
			case utils.TICKET_STATUS_OPEN:
				// If reopening a ticket
				newProgressState = utils.TICKET_PROGRESS_PENDING_AGENT_RESP
			case utils.TICKET_STATUS_INPROGRESS_3PL:
				newProgressState = utils.TICKET_PROGRESS_PENDING_OPS_REVIEW
			}
		}

	case "seller_response":
		// When seller responds to an action request
		newProgressState = utils.TICKET_PROGRESS_SELLER_REPLIED
	}

	// Only update if the state has changed
	if newProgressState != currentProgressState {
		query := fmt.Sprintf(`
			UPDATE cs_tickets
			SET progress_state = '%s', 
				updated_at = %d, 
				updated_by = '%s'
			WHERE id = '%s'
		`, newProgressState, time.Now().UnixMilli(), actionBy, ticketID)

		_, err := s.repository.CustomQuery(nil, query)
		if err != nil {
			return fmt.Errorf("error updating ticket progress state: %v", err)
		}

		// If seller/tech/ops replied, ticket should automatically go back to agent
		if slices.Contains([]string{
			utils.TICKET_PROGRESS_SELLER_REPLIED,
		}, newProgressState) {
			// Also update ownership back to CS agent
			updateOwnershipQuery := fmt.Sprintf(`
				UPDATE cs_tickets
				SET current_assignee_id = primary_owner_id,
					ownership_state = '%s',
					progress_state = '%s',
					updated_at = %d
				WHERE id = '%s'
			`, utils.AGENT_TEAM_CS, utils.TICKET_PROGRESS_PENDING_AGENT_REVIEW,
				time.Now().UnixMilli(), ticketID)

			_, err = s.repository.CustomQuery(nil, updateOwnershipQuery)
			if err != nil {
				return fmt.Errorf("error returning ticket to agent: %v", err)
			}
		}
	}

	return nil
}

func (s *Service) SubmitRating(ctx context.Context, request *dto.SubmitRatingRequest) (*dto.SubmitRatingResponse, error) {
	// Validate rating range
	if request.Data.Rating < 1 || request.Data.Rating > 5 {
		return nil, fmt.Errorf("rating must be between 1 and 5")
	}

	// Validate required fields
	if request.Data.OrderID == 0 && request.Data.TicketID == "" {
		return nil, fmt.Errorf("either order_id or ticket_id is required")
	}

	if request.Data.UserID == "" {
		return nil, fmt.Errorf("user_id is required")
	}

	// Get ticket information if ticket_id is provided
	var ticketInfo *dao.CSTicket
	if request.Data.TicketID != "" {
		ticket := dao.CSTicket{}
		_, err := s.repository.Find(map[string]interface{}{
			"id": request.Data.TicketID,
		}, &ticket)
		if err == nil {
			ticketInfo = &ticket
			// Set order_id from ticket if not provided
			if request.Data.OrderID == 0 {
				request.Data.OrderID = ticket.OrderID
			}
		}
	}

	// If only order_id is provided, try to find associated ticket
	if request.Data.OrderID != 0 && request.Data.TicketID == "" && ticketInfo == nil {
		ticket := dao.CSTicket{}
		_, err := s.repository.Find(map[string]interface{}{
			"order_id": request.Data.OrderID,
		}, &ticket)
		if err == nil {
			ticketInfo = &ticket
			request.Data.TicketID = ticket.ID
		}
	}

	now := uint64(time.Now().UnixMilli())

	// Prepare metadata
	metaData := request.Data.Meta
	if metaData == nil {
		metaData = make(map[string]interface{})
	}
	metaData["source"] = request.Data.Source
	if ticketInfo != nil && ticketInfo.Status != nil {
		metaData["ticket_status"] = *ticketInfo.Status
	}

	metaJSON, err := json.Marshal(metaData)
	if err != nil {
		return nil, fmt.Errorf("error marshalling metadata: %v", err)
	}

	var savedRating dao.CSRating

	// Create new rating
	rating := dao.CSRating{
		UserID:    request.Data.UserID,
		Rating:    &request.Data.Rating,
		CreatedAt: now,
		UpdatedAt: now,
		Meta:      metaJSON,
	}

	if request.Data.OrderID != 0 {
		rating.OrderID = &request.Data.OrderID
	}
	if request.Data.TicketID != "" {
		rating.TicketID = &request.Data.TicketID
	}
	if request.Data.Feedback != "" {
		rating.Feedback = &request.Data.Feedback
	}

	// Create the rating
	_, err = s.repository.Create(&rating)
	if err != nil {
		return nil, fmt.Errorf("error creating rating: %v", err)
	}

	savedRating = rating

	// Add system message about rating
	if ticketInfo != nil {
		s.addActionSystemMessage(ctx, *ticketInfo, dto.TicketActionRequestData{
			TicketID: ticketInfo.ID,
			Action:   "ticket_rating",
			ActionBy: request.Data.UserID,
		}, "", fmt.Sprintf(`%d`, int(request.Data.Rating)))

		jsonBytes, err := json.Marshal(map[string]interface{}{"rating": request.Data.Rating})
		if err != nil {
			return nil, err
		}
		s.addDetailedActionMessage(ctx, *ticketInfo, dto.TicketActionRequestData{
			TicketID: ticketInfo.ID,
			Action:   "ticket_rating",
			ActionBy: ticketInfo.UserID,
			ActionData: dto.ActionData{
				Note:          request.Data.Feedback,
				RequestedMeta: datatypes.JSON(jsonBytes),
			},
		})
	}

	orderData, err := GetOrderEssentials(s.repository, int64(ticketInfo.OrderID))
	if err != nil {
		return nil, fmt.Errorf("error fetching order data: %v", err)
	}

	var ticketMeta []dto.AppSubmitTicketIssueDetails
	if err := json.Unmarshal(ticketInfo.Meta, &ticketMeta); err != nil {
		return nil, fmt.Errorf("error unmarshaling JSON: %w", err)
	}
	// Prepare response
	responseData := dto.RatingResponseData{
		ID:        savedRating.ID,
		OrderID:   savedRating.OrderID,
		TicketID:  savedRating.TicketID,
		Rating:    savedRating.Rating,
		Feedback:  savedRating.Feedback,
		UserID:    savedRating.UserID,
		CreatedAt: savedRating.CreatedAt,
	}

	categoryInfo, err := s.GetTicketCategoryByCode(ctx, ticketMeta[0].IssueCategory.ID)
	if err != nil {
		return nil, fmt.Errorf("error fetching category info: %v", err)
	}
	var subCategory string
	if ticketMeta[0].IssueSubCategory.ID != "" {
		subCategoryInfo, err := s.GetTicketCategoryByCode(ctx, ticketMeta[0].IssueSubCategory.ID)
		if err != nil {
			return nil, fmt.Errorf("error fetching sub-category info: %v", err)
		}
		subCategory = subCategoryInfo.Name
	}
	primaryOwner, err := s.GetAgentByID(ctx, ticketInfo.PrimaryOwnerID)
	if err != nil {
		return nil, fmt.Errorf("error fetching primary owner: %v", err)
	}

	istLocation, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		return nil, err
	}
	ticketCreatedAt := time.UnixMilli(ticketInfo.CreatedAt).In(istLocation).Format("2006-01-02 15:04:05")

	eventObject := map[string]interface{}{
		"ticket_id":         ticketInfo.ID,
		"order_id":          ticketInfo.OrderID,
		"user_id":           ticketInfo.UserID,
		"category":          categoryInfo.Name,
		"sub_category":      subCategory,
		"rating":            request.Data.Rating,
		"description":       request.Data.Feedback,
		"seller":            *orderData.Seller,
		"order_value":       orderData.OrderValue,
		"display_status":    orderData.DisplayStatus,
		"courier":           orderData.Courier,
		"awb_number":        orderData.AwbNumber,
		"assigned_to":       primaryOwner.Name,
		"primary_owner":     primaryOwner.Name,
		"ticket_created_at": ticketCreatedAt,
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Support Ticket Rating Submitted", ticketInfo.UserID, eventObject),
	})
	webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{ticketInfo.UserID},
		EventName:   "Support Ticket Rating Submitted",
		EventObject: eventObject,
	})

	return &dto.SubmitRatingResponse{
		Success: true,
		Message: "आपके फीडबैक के लिए धन्यवाद|",
		Rating:  responseData,
	}, nil
}

// Main function that orchestrates all queries for Get Support Insights
func (s *Service) GetSupportInsights(ctx context.Context, request *dto.GetSupportInsightsRequest) (*dto.GetSupportInsightsResponse, error) {
	// Build base where clause
	baseWhere := s.buildBaseWhereClause(request)

	// Create channels for results
	assigneeChan := make(chan []dto.OpenTicketsByAssignee, 1)
	categoryChan := make(chan []dto.OpenTicketsByCategory, 1)
	sellerChan := make(chan []dto.OpenTicketsBySeller, 1)
	statusChan := make(chan []dto.TicketsByStatus, 1)
	totalChan := make(chan map[string]int, 1)
	errorChan := make(chan error, 5)

	// Use WaitGroup to ensure all goroutines complete
	var wg sync.WaitGroup
	wg.Add(5)

	// Execute all queries in parallel
	go func() {
		defer wg.Done()
		result, err := s.getTicketsByAssignee(ctx, baseWhere)
		if err != nil {
			errorChan <- err
			return
		}
		assigneeChan <- result
	}()

	go func() {
		defer wg.Done()
		result, err := s.getTicketsByCategory(ctx, baseWhere)
		if err != nil {
			errorChan <- err
			return
		}
		categoryChan <- result
	}()

	go func() {
		defer wg.Done()
		result, err := s.getTicketsBySeller(ctx, baseWhere)
		if err != nil {
			errorChan <- err
			return
		}
		sellerChan <- result
	}()

	go func() {
		defer wg.Done()
		result, err := s.getTicketsByStatus(ctx, baseWhere)
		if err != nil {
			errorChan <- err
			return
		}
		statusChan <- result
	}()

	go func() {
		defer wg.Done()
		result, err := s.getTotalCounts(ctx, baseWhere)
		if err != nil {
			errorChan <- err
			return
		}
		totalChan <- result
	}()

	// Wait for all queries to complete
	wg.Wait()
	close(errorChan)

	// Check for errors
	for err := range errorChan {
		if err != nil {
			return nil, err
		}
	}

	// Collect results
	assigneeData := <-assigneeChan
	categoryData := <-categoryChan
	sellerData := <-sellerChan
	statusData := <-statusChan
	totalData := <-totalChan

	// Get totals from the map
	totalTickets := totalData["total_count"]
	openTickets := totalData["open_count"]

	// Update percentages now that we have totals
	assigneeData = s.updateAssigneePercentages(assigneeData, totalTickets, openTickets)
	categoryData = s.updateCategoryPercentages(categoryData, totalTickets, openTickets)
	sellerData = s.updateSellerPercentages(sellerData, totalTickets, openTickets)

	// Add created status as first item
	statusData = s.addCreatedStatus(statusData, totalTickets)

	// Build response
	response := &dto.GetSupportInsightsResponse{
		Data: dto.GetSupportInsightsResponseData{
			TotalTickets:     totalTickets,
			TotalOpenTickets: openTickets,
			ByAssignee:       assigneeData,
			ByCategory:       categoryData,
			BySeller:         sellerData,
			ByStatus:         statusData,
		},
	}

	return response, nil
}

// Build base where clause
func (s *Service) buildBaseWhereClause(request *dto.GetSupportInsightsRequest) string {
	baseConditions := []string{
		"t.active = 1",
		"t.archived = 0",
	}

	defaultFrom := time.Now().AddDate(0, 0, -7).UnixMilli()
	defaultTo := time.Now().UnixMilli()

	if request.Data.From > 0 {
		baseConditions = append(baseConditions, fmt.Sprintf("t.created_at >= %d", request.Data.From))
		baseConditions = append(baseConditions, fmt.Sprintf("t.created_at <= %d", request.Data.To))
	} else {
		baseConditions = append(baseConditions, fmt.Sprintf("t.created_at >= %d", defaultFrom))
		baseConditions = append(baseConditions, fmt.Sprintf("t.created_at <= %d", defaultTo))
	}

	if seller, ok := request.Data.Filters["seller"].(string); ok {
		baseConditions = append(baseConditions, fmt.Sprintf("t.seller = '%s'", seller))
	}

	return strings.Join(baseConditions, " AND ")
}

func (s *Service) getTicketsByAssignee(ctx context.Context, baseWhere string) ([]dto.OpenTicketsByAssignee, error) {
	// Query for non-seller teams
	regularQuery := `
		SELECT
			COALESCE(a.name, 'Unassigned') as assignee_name,
			t.current_assignee_id as assignee_id,
			a.avatar_url as assignee_avatar,
			a.team as assignee_team,
			a.email as assignee_email,
			COUNT(*) as total_count,
			COUNT(CASE WHEN t.status NOT IN ('resolved', 'closed') THEN 1 END) as open_count
		FROM cs_tickets t
		LEFT JOIN cs_agents a ON t.current_assignee_id = a.id
		WHERE t.active = 1 AND t.archived = 0
		AND (a.team IS NULL OR a.team != 'seller')
		GROUP BY t.current_assignee_id, a.name, a.avatar_url, a.team, a.email`

	// Query for seller teams (grouped by seller)
	sellerQuery := `
		SELECT
			a.seller as assignee_name,
			a.seller as assignee_id,
			NULL as assignee_avatar,
			'seller' as assignee_team,
			NULL as assignee_email,
			COUNT(*) as total_count,
			COUNT(CASE WHEN t.status NOT IN ('resolved', 'closed') THEN 1 END) as open_count
		FROM cs_tickets t
		INNER JOIN cs_agents a ON t.current_assignee_id = a.id
		WHERE t.active = 1 AND t.archived = 0 AND a.team = 'seller'
		GROUP BY a.seller`

	// Execute both queries
	var regularResults, sellerResults []struct {
		AssigneeName   string `db:"assignee_name"`
		AssigneeID     string `db:"assignee_id"`
		AssigneeAvatar string `db:"assignee_avatar"`
		AssigneeTeam   string `db:"assignee_team"`
		AssigneeEmail  string `db:"assignee_email"`
		TotalCount     int    `db:"total_count"`
		OpenCount      int    `db:"open_count"`
	}

	_, err := s.repository.CustomQuery(&regularResults, regularQuery)
	if err != nil {
		return nil, fmt.Errorf("error fetching regular tickets: %v", err)
	}

	_, err = s.repository.CustomQuery(&sellerResults, sellerQuery)
	if err != nil {
		return nil, fmt.Errorf("error fetching seller tickets: %v", err)
	}

	for i := range sellerResults {
		sellerData, exists := brands.GetBrandMetaBySeller(sellerResults[i].AssigneeID)
		if exists {
			sellerResults[i].AssigneeName = sellerData.Name
			sellerResults[i].AssigneeAvatar = sellerData.Logo
		}
	}

	// Combine and sort results
	allResults := append(regularResults, sellerResults...)

	// Sort by open_count DESC, then total_count DESC
	sort.Slice(allResults, func(i, j int) bool {
		if allResults[i].OpenCount != allResults[j].OpenCount {
			return allResults[i].OpenCount > allResults[j].OpenCount
		}
		return allResults[i].TotalCount > allResults[j].TotalCount
	})

	// Convert to DTO
	assigneeData := make([]dto.OpenTicketsByAssignee, 0, len(allResults))
	for _, result := range allResults {
		assigneeData = append(assigneeData, dto.OpenTicketsByAssignee{
			AssigneeName:   result.AssigneeName,
			AssigneeID:     result.AssigneeID,
			AssigneeAvatar: result.AssigneeAvatar,
			AssigneeTeam:   result.AssigneeTeam,
			AssigneeEmail:  result.AssigneeEmail,
			TotalCount:     result.TotalCount,
			OpenCount:      result.OpenCount,
		})
	}

	return assigneeData, nil
}

// Get tickets grouped by category
func (s *Service) getTicketsByCategory(ctx context.Context, baseWhere string) ([]dto.OpenTicketsByCategory, error) {
	query := fmt.Sprintf(`
		SELECT
			ctc.name as category_name,
			ctc.code as category_code,
			t.category as category_id,
			COUNT(*) as total_count,
			COUNT(CASE WHEN t.status not in ('resolved', 'closed') THEN 1 END) as open_count
		FROM cs_tickets t
		LEFT JOIN cs_ticket_category ctc ON t.category = ctc.id
		WHERE %s
		GROUP BY t.category, ctc.name, ctc.code
		ORDER BY open_count DESC, total_count DESC
	`, baseWhere)

	var results []struct {
		CategoryName string `db:"category_name"`
		CategoryCode string `db:"category_code"`
		CategoryID   int    `db:"category_id"`
		TotalCount   int    `db:"total_count"`
		OpenCount    int    `db:"open_count"`
	}

	_, err := s.repository.CustomQuery(&results, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching tickets by category: %v", err)
	}

	// Convert to DTO (without percentages)
	categoryData := []dto.OpenTicketsByCategory{}
	for _, result := range results {
		categoryData = append(categoryData, dto.OpenTicketsByCategory{
			CategoryName: result.CategoryName,
			CategoryCode: result.CategoryCode,
			CategoryID:   result.CategoryID,
			TotalCount:   result.TotalCount,
			OpenCount:    result.OpenCount,
		})
	}

	return categoryData, nil
}

// Get tickets grouped by seller
func (s *Service) getTicketsBySeller(ctx context.Context, baseWhere string) ([]dto.OpenTicketsBySeller, error) {
	query := fmt.Sprintf(`
		SELECT
			COALESCE(s.name, t.seller) as seller_name,
			t.seller as seller_code,
			COUNT(*) as total_count,
			COUNT(CASE WHEN t.status not in ('resolved', 'closed') THEN 1 END) as open_count
		FROM cs_tickets t
		LEFT JOIN kiranabazar_sellers s ON t.seller = s.code
		WHERE %s
		GROUP BY t.seller, s.name
		ORDER BY open_count DESC, total_count DESC
	`, baseWhere)

	var results []struct {
		SellerName string `db:"seller_name"`
		SellerCode string `db:"seller_code"`
		TotalCount int    `db:"total_count"`
		OpenCount  int    `db:"open_count"`
	}

	_, err := s.repository.CustomQuery(&results, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching tickets by seller: %v", err)
	}

	// Convert to DTO (without percentages)
	sellerData := []dto.OpenTicketsBySeller{}
	for _, result := range results {
		sellerData = append(sellerData, dto.OpenTicketsBySeller{
			SellerName: result.SellerName,
			SellerCode: result.SellerCode,
			TotalCount: result.TotalCount,
			OpenCount:  result.OpenCount,
		})
	}

	return sellerData, nil
}

// Get tickets grouped by status
func (s *Service) getTicketsByStatus(ctx context.Context, baseWhere string) ([]dto.TicketsByStatus, error) {
	query := fmt.Sprintf(`
		SELECT
			t.status,
			COUNT(*) as count
		FROM cs_tickets t
		WHERE %s
		GROUP BY t.status
		ORDER BY 
			CASE t.status
				WHEN 'open' THEN 1
				WHEN 'in_progress' THEN 2
				WHEN 'on_hold' THEN 3
				WHEN 'resolved' THEN 4
				WHEN 'closed' THEN 5
				ELSE 6
			END
	`, baseWhere)

	var results []struct {
		Status string `db:"status"`
		Count  int    `db:"count"`
	}

	_, err := s.repository.CustomQuery(&results, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching tickets by status: %v", err)
	}

	// Convert to DTO with labels (percentages will be calculated later)
	statusData := []dto.TicketsByStatus{}
	for _, result := range results {
		statusLabel := result.Status
		for _, statusInfo := range utils.TICKETS_STATUS_VALUES {
			if statusInfo["value"] == result.Status {
				statusLabel = statusInfo["label"]
				break
			}
		}

		statusData = append(statusData, dto.TicketsByStatus{
			Status: result.Status,
			Label:  statusLabel,
			Count:  result.Count,
		})
	}

	return statusData, nil
}

// Get total counts
func (s *Service) getTotalCounts(ctx context.Context, baseWhere string) (map[string]int, error) {
	query := fmt.Sprintf(`
		SELECT 
			COUNT(*) as total_count,
			COUNT(CASE WHEN t.status not in ('resolved', 'closed') THEN 1 END) as open_count
		FROM cs_tickets t
		WHERE %s
	`, baseWhere)

	var results []struct {
		TotalCount int `db:"total_count"`
		OpenCount  int `db:"open_count"`
	}

	_, err := s.repository.CustomQuery(&results, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching total count: %v", err)
	}

	totalData := map[string]int{
		"total_count": 0,
		"open_count":  0,
	}

	if len(results) > 0 {
		totalData["total_count"] = results[0].TotalCount
		totalData["open_count"] = results[0].OpenCount
	}

	return totalData, nil
}

// Update percentages for assignee data
func (s *Service) updateAssigneePercentages(data []dto.OpenTicketsByAssignee, totalTickets, openTickets int) []dto.OpenTicketsByAssignee {
	for i := range data {
		if openTickets > 0 {
			data[i].OpenPercentage = (float64(data[i].OpenCount) / float64(openTickets)) * 100
		}
		if totalTickets > 0 {
			data[i].TotalPercentage = (float64(data[i].TotalCount) / float64(totalTickets)) * 100
		}
	}
	return data
}

// Update percentages for category data
func (s *Service) updateCategoryPercentages(data []dto.OpenTicketsByCategory, totalTickets, openTickets int) []dto.OpenTicketsByCategory {
	for i := range data {
		if openTickets > 0 {
			data[i].OpenPercentage = (float64(data[i].OpenCount) / float64(openTickets)) * 100
		}
		if totalTickets > 0 {
			data[i].TotalPercentage = (float64(data[i].TotalCount) / float64(totalTickets)) * 100
		}
	}
	return data
}

// Update percentages for seller data
func (s *Service) updateSellerPercentages(data []dto.OpenTicketsBySeller, totalTickets, openTickets int) []dto.OpenTicketsBySeller {
	for i := range data {
		if openTickets > 0 {
			data[i].OpenPercentage = (float64(data[i].OpenCount) / float64(openTickets)) * 100
		}
		if totalTickets > 0 {
			data[i].TotalPercentage = (float64(data[i].TotalCount) / float64(totalTickets)) * 100
		}
	}
	return data
}

// Add created status and update percentages
func (s *Service) addCreatedStatus(data []dto.TicketsByStatus, totalTickets int) []dto.TicketsByStatus {
	// Add created status as first item
	result := []dto.TicketsByStatus{
		{
			Status:     "created",
			Label:      "Created",
			Count:      totalTickets,
			Percentage: 100.0,
		},
	}

	// Update percentages for existing statuses
	for i := range data {
		if totalTickets > 0 {
			data[i].Percentage = (float64(data[i].Count) / float64(totalTickets)) * 100
		}
		result = append(result, data[i])
	}

	return result
}

func (s *Service) trackTicketResolved(ctx context.Context, ticketID string, resolvedBy string, resolutionComment string) error {
	// Fetch ticket details
	ticket := dao.CSTicket{}
	_, err := s.repository.Find(map[string]interface{}{
		"id": ticketID,
	}, &ticket)
	if err != nil {
		return fmt.Errorf("error fetching ticket: %v", err)
	}

	// Get order essentials
	orderData, err := GetOrderEssentials(s.repository, int64(ticket.OrderID))
	if err != nil {
		return fmt.Errorf("error fetching order data: %v", err)
	}

	// Get resolution time in hours
	resolutionTimeHours := int((time.Now().UnixMilli() - ticket.CreatedAt) / (1000 * 60 * 60))

	// Get category info
	categoryInfo, err := s.GetTicketCategoryByID(ctx, *ticket.Category)
	if err != nil {
		return fmt.Errorf("error fetching category info: %v", err)
	}

	// Get sub-category if exists
	var ticketMeta []dto.AppSubmitTicketIssueDetails
	var subCategory string
	if err := json.Unmarshal(ticket.Meta, &ticketMeta); err == nil && len(ticketMeta) > 0 {
		if ticketMeta[0].IssueSubCategory.ID != "" {
			subCategoryInfo, err := s.GetTicketCategoryByCode(ctx, ticketMeta[0].IssueSubCategory.ID)
			if err == nil {
				subCategory = subCategoryInfo.Name
			}
		}
	}

	// Get assignee info
	lastAssignee := ""
	if ticket.CurrentAssigneeID != nil {
		if agent, err := s.GetAgentByID(ctx, *ticket.CurrentAssigneeID); err == nil {
			lastAssignee = agent.Email
		}
	}

	// Get owner info
	ownerEmail := ""
	if ownerAgent, err := s.GetAgentByID(ctx, ticket.PrimaryOwnerID); err == nil {
		ownerEmail = ownerAgent.Email
	}

	istLocation, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		return err
	}
	ticketCreatedAt := time.UnixMilli(ticket.CreatedAt).In(istLocation).Format("2006-01-02 15:04:05")
	// Prepare event object
	eventObject := map[string]interface{}{
		"resolved_by":            resolvedBy,
		"primary_owner":          ownerEmail,
		"assigned_to":            lastAssignee,
		"order_id":               fmt.Sprintf("%d", ticket.OrderID),
		"ticket_id":              ticket.ID,
		"order_value":            orderData.OrderValue,
		"seller":                 utils.SafeDeref(orderData.Seller),
		"category":               categoryInfo.Name,
		"sub_category":           subCategory,
		"resolution_comment":     resolutionComment,
		"ticket_created_at":      ticketCreatedAt,
		"display_status":         orderData.DisplayStatus,
		"awb":                    utils.SafeDeref(orderData.AwbNumber),
		"courier_name":           couriers.GetActualCourierName(orderData.Courier),
		"ticket_resolution_time": resolutionTimeHours,
		"user_id":                ticket.UserID,
	}
	// Fire Mixpanel event
	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Support Ticket Resolved", ticket.UserID, eventObject),
	})

	// Fire Webengage event
	webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{ticket.UserID},
		EventName:   "Support Ticket Resolved",
		EventObject: eventObject,
	})

	return nil
}

func (s *Service) trackTicketReassigned(ctx context.Context, ticketID string, assignedBy string, assignedTo string, note string) error {
	// Fetch ticket details
	ticket := dao.CSTicket{}
	_, err := s.repository.Find(map[string]interface{}{
		"id": ticketID,
	}, &ticket)
	if err != nil {
		return fmt.Errorf("error fetching ticket: %v", err)
	}

	// Get order essentials
	orderData, err := GetOrderEssentials(s.repository, int64(ticket.OrderID))
	if err != nil {
		return fmt.Errorf("error fetching order data: %v", err)
	}

	// Get category info
	categoryInfo, err := s.GetTicketCategoryByID(ctx, *ticket.Category)
	if err != nil {
		return fmt.Errorf("error fetching category info: %v", err)
	}

	// Get sub-category if exists
	var ticketMeta []dto.AppSubmitTicketIssueDetails
	var subCategory string
	if err := json.Unmarshal(ticket.Meta, &ticketMeta); err == nil && len(ticketMeta) > 0 {
		if ticketMeta[0].IssueSubCategory.ID != "" {
			subCategoryInfo, err := s.GetTicketCategoryByCode(ctx, ticketMeta[0].IssueSubCategory.ID)
			if err == nil {
				subCategory = subCategoryInfo.Name
			}
		}
	}

	// Get owner info
	ownerEmail := ""
	if ownerAgent, err := s.GetAgentByID(ctx, ticket.PrimaryOwnerID); err == nil {
		ownerEmail = ownerAgent.Email
	}

	istLocation, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		return err
	}
	ticketCreatedAt := time.UnixMilli(ticket.CreatedAt).In(istLocation).Format("2006-01-02 15:04:05")

	// Prepare event object
	eventObject := map[string]interface{}{
		"assigned_by":       assignedBy,
		"assigned_to":       assignedTo,
		"note":              note,
		"primary_owner":     ownerEmail,
		"order_id":          fmt.Sprintf("%d", ticket.OrderID),
		"ticket_id":         ticket.ID,
		"order_value":       orderData.OrderValue,
		"seller":            utils.SafeDeref(orderData.Seller),
		"category":          categoryInfo.Name,
		"sub_category":      subCategory,
		"ticket_created_at": ticketCreatedAt,
		"display_status":    orderData.DisplayStatus,
		"awb":               utils.SafeDeref(orderData.AwbNumber),
		"courier_name":      couriers.GetActualCourierName(orderData.Courier),
		"user_id":           ticket.UserID,
	}

	// Fire Mixpanel event
	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Support Ticket Reassigned", ticket.UserID, eventObject),
	})

	// Fire Webengage event
	webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{ticket.UserID},
		EventName:   "Support Ticket Reassigned",
		EventObject: eventObject,
	})

	return nil
}

func (s *Service) UpdateSupportTicketStatusByOrderId(ctx context.Context, orderID string, newStatus string, note string, updatedBy string, action string) error {
	ticket, err := s.GetTicketByOrderID(ctx, orderID)
	if err != nil {
		fmt.Printf("error getting ticket by order ID: %v", err)
	}
	if ticket == nil {
		return nil
	}
	var actionableTicket dao.CSTicket
	if ticket != nil && len(*ticket) > 0 {
		actionableTicket = (*ticket)[0]
	}
	orderStatusCategory, err := s.GetTicketCategoryByCode(ctx, "orderStatus")
	if err != nil {
		fmt.Printf("Error fetching order status category: %v", err)
	}
	if (actionableTicket.Category != nil) && (*actionableTicket.Category == orderStatusCategory.ID) && (actionableTicket.Status != nil) && (*actionableTicket.Status != newStatus) {
		_, err = s.PerformTicketAction(ctx, &dto.TicketActionRequest{
			Data: dto.TicketActionRequestData{
				TicketID:  actionableTicket.ID,
				Action:    "change_status",
				ActionBy:  updatedBy,
				NewStatus: newStatus,
				ActionData: dto.ActionData{
					Note: note,
				},
			},
		})
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error updating ticket progress state: %v", err))
			return err
		}
		if newStatus == "resolved" && action == "order_delivered" {
			messageContent := "आपका ऑर्डर डिलिवर हो गया है। यदि आपको किसी और सहायता की आवश्यकता हो, तो कृपया हमसे संपर्क करें।\nधन्यवाद,\nKirana Club सपोर्ट टीम"
			privateMessage := false
			_, err := s.SendMessage(ctx, &dto.SendMessageRequest{
				Data: dto.SendMessageRequestData{
					TicketID:       actionableTicket.ID,
					ConversationID: actionableTicket.ConversationID,
					Content:        messageContent,
					SenderID:       updatedBy,
					SenderType:     utils.MESSAGE_SENDER_AGENT,
					Private:        &privateMessage,
					MessageType:    utils.MESSAGE_TYPE_TICKET_RESOLUTION,
					Status:         "sent",
					Source:         utils.MESSAGE_SENDER_SYSTEM,
				},
			})

			if err != nil {
				return fmt.Errorf("error sending system message: %v", err)
			}
		}
	}
	return nil
}

func (s *Service) UpdateTicketPriority(ctx context.Context, ticket *dao.CSTicket) error {
	// Check if user is a power user or first-time user
	shouldPriorityChangeUserOrderTags, err := s.checkUserOrderTagsForPriorityChange(ctx, ticket.OrderID)
	if err != nil {
		return fmt.Errorf("failed to check user status: %w", err)
	}

	// Check if order is in NDR with attempt count > 2
	isHighNDR, err := s.checkNDRStatus(ctx, ticket.OrderID)
	if err != nil {
		return fmt.Errorf("failed to check NDR status: %w", err)
	}

	// Update priority based on conditions
	shouldPriorityChange := shouldPriorityChangeUserOrderTags || isHighNDR

	// Update the ticket priority if it has changed
	if shouldPriorityChange {
		newPriority := utils.TICKET_PRIORITY_P0
		updateTicket := dao.CSTicket{
			Priority:  &newPriority,
			UpdatedAt: time.Now().UnixMilli(),
		}
		_, _, err = s.repository.Update(&dao.CSTicket{
			ID: ticket.ID,
		}, &updateTicket)
		if err != nil {
			return fmt.Errorf("failed to update ticket priority: %w", err)
		}
	}

	return nil
}

// checkUserStatus checks if user is a power user or first-time user based on order tags
func (s *Service) checkUserOrderTagsForPriorityChange(ctx context.Context, orderID int) (shouldPriorityChangeUserOrderTags bool, err error) {
	query := fmt.Sprintf(`
		SELECT ot.tag
		FROM kiranabazar_order_tag_mapping otm
		JOIN kiranabazar_order_tags ot ON otm.tag_id = ot.id
		WHERE otm.order_id = %d AND otm.is_active = 1 AND ot.is_active = 1
	`, orderID)

	var tags []string
	if _, err := s.ReadOnlyRepository.CustomQuery(&tags, query); err != nil {
		return false, err
	}
	var isPowerUser, isFirstOrder bool
	for _, tag := range tags {
		switch tag {
		case "Power User":
			isPowerUser = true
		case "First Time":
			isFirstOrder = true
		}
	}

	return isPowerUser || isFirstOrder, nil
}

// checkNDRStatus checks if order is in NDR with attempt count > 2
func (s *Service) checkNDRStatus(ctx context.Context, orderID int) (bool, error) {
	var ndr dao.KiranaBazarNdrs
	query := fmt.Sprintf(`
		SELECT kn.order_id, kn.ndr_attempt_count FROM kiranabazar_ndrs kn WHERE kn.order_id = %d
	`, orderID)
	if _, err := s.ReadOnlyRepository.CustomQuery(&ndr, query); err != nil {
		return false, err
	}

	// Check if NDR attempt count > 2
	if (ndr.NDRAttemptCount != nil) && (*ndr.NDRAttemptCount > 2) {
		return true, nil
	}

	return false, nil
}

func (s *Service) UpdateMedia(ctx context.Context, request *dto.UpdateMediaRequest) (response *dto.UpdateMediaResponse, err error) {
	reviewMediaData := dao.CSAttachment{}

	_, err = s.repository.Find(map[string]interface{}{
		"id": request.JobID,
	}, &reviewMediaData)

	if err != nil {
		return
	}

	contentType := "application/octet-stream"
	if strings.Contains(request.StreamingURL, ".mp4") {
		contentType = "video/mp4"
	}
	newReviewMediaData := dao.CSAttachment{
		FileURL:          request.StreamingURL,
		UpdatedAt:        time.Now().UnixMilli(),
		FileThumbnailURL: &request.ThumbnailURL,
		ContentType:      &contentType,
		FileRawURL:       &reviewMediaData.FileURL,
	}

	_, _, err = s.repository.Update(dao.CSAttachment{
		ID: reviewMediaData.ID,
	}, newReviewMediaData)

	if err != nil {
		return
	}

	response = &dto.UpdateMediaResponse{
		Message: "Success",
		Success: true,
	}

	return

}
