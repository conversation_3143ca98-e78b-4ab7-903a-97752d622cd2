package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/repositories/sqlRepo"
)

// Handler function for select request
func (s *Service) Select(ctx context.Context, req *dto.AppSelectRequest) (*dto.AppSelectResponse, error) {
	msgId := getMessageID()
	identifier := fmt.Sprintf("%s%s", *req.Meta.Context.TransactionID, msgId)

	// select request context
	selectRequestContext, err := getContext(&req.Meta.Context, SELECT, msgId)
	if err != nil {
		return nil, err
	}

	selectMessage, err := getSelectMessage(req, s.repository)
	if err != nil {
		return nil, err
	}

	// select request object to ondc
	selectRequest := &dto.SelectRequest{
		Context: selectRequestContext,
		Message: selectMessage,
	}

	return s.handleONDCSelectRequest(ctx, *selectRequest, identifier, *req.Meta.Context.TransactionID)

}

// handleONDCSelectRequest
func (s *Service) handleONDCSelectRequest(ctx context.Context, selectRequest dto.SelectRequest, identifier, transactionID string) (*dto.AppSelectResponse, error) {

	adjustedReqJSON, err := json.Marshal(selectRequest)
	if err != nil {
		logger.Error(ctx, "Marshal adjusted request failed: %v", err)
		return nil, err
	}

	fmt.Println("SELECT req is", string(adjustedReqJSON))

	resp, err := s.syncingONDCRequest(ctx, adjustedReqJSON, identifier, SELECT)
	if err != nil {
		return nil, err
	}

	redisResp, ok := resp.(string)
	if !ok {
		logger.Error(ctx, "not able to typecast the redis resp")
		return nil, fmt.Errorf("not able to typecast the redis resp")
	}
	appSelectResp := &dto.AppSelectResponse{}
	err = json.Unmarshal([]byte(redisResp), appSelectResp)
	if err != nil {
		logger.Error(ctx, "not able to unmarshal the redis resp, err is %s", err.Error())
		return nil, err
	}
	appSelectResp.Cart.Meta.TransactionID = transactionID
	return appSelectResp, nil
}

// parse selected items for select request.
func getSelectedItems(reqItems []shared.SellerItems, providerReq dto.AppSelectProvider) (items []dto.OrderItemsInner, provider dto.OrderProvider, err error) {
	for _, d := range reqItems {
		di := dto.OrderItemsInner{
			ID:            d.ID,
			FulfilmentIds: d.FulfilmentIds,
			LocationIds:   d.LocationIds,
			Quantity: dto.QuantitySelected{
				Selected: dto.SelectedCount{
					Count: d.Quantity,
				},
			},
			Tags: []dto.TagGroup{
				{
					Descriptor: dto.Descriptor{
						Code: "BUYER_TERMS",
					},
					List: []dto.Tag{
						{
							Descriptor: dto.Descriptor{
								Code: "PACKAGING_REQ",
							},
							Value: "No special packaging or shipment needed",
						},
					},
				},
			},
		}
		items = append(items, di)
	}

	providerLocationIds := []dto.OrderProviderLocationsInner{}
	for _, loc := range providerReq.ProviderLocation {
		providerLocationIds = append(providerLocationIds, dto.OrderProviderLocationsInner{
			ID: loc,
		})
	}
	provider = dto.OrderProvider{
		ID:        providerReq.ProviderID,
		Locations: providerLocationIds,
		TTL:       &providerReq.ProviderTTL,
	}

	return items, provider, err
}

// getSelect Message returns the select message response body.
func getSelectMessage(req *dto.AppSelectRequest, repo *sqlRepo.Repository) (*dto.SelectMessage, error) {
	// selected items
	items, provider, err := getSelectedItems(req.Data.Items, req.Data.Provider)
	if err != nil {
		return nil, err
	}
	address, _, err := getUserAddress(req.UserID, true, "", repo)
	if err != nil {
		return nil, err
	}
	return &dto.SelectMessage{
		Order: &dto.Order{
			Provider: &provider,
			Items:    items,
			Fulfillments: []dto.Fulfillment{
				{
					Type: "Delivery",
					ID:   "1",
					Stops: []dto.Stops{
						{
							Type: "end",
							Location: dto.Location{
								Gps:      fmt.Sprintf("%f,%f", address.Latitude, address.Longitude),
								AreaCode: *address.PostalCode,
								City: &dto.City{
									Name: *address.District,
								},
								State:   &dto.City{Name: *address.State},
								Country: &dto.Country{Code: "IND"},
								Address: fmt.Sprintf("%s, %s", *address.Name, *address.Line),
							},
							Contact: &dto.Contact{Phone: *address.Phone},
						},
					},
				},
			},
			Payment: []dto.Payment{
				{
					Type: "ON-FULFILLMENT",
				},
			},
			Tags: []dto.TagGroup{
				{
					Descriptor: dto.Descriptor{
						Code: "buyer_id",
					},
					List: []dto.Tag{
						{
							Descriptor: dto.Descriptor{Code: "buyer_id_code"},
							Value:      "gst",
						},
						{
							Descriptor: dto.Descriptor{Code: "buyer_id_no"},
							Value:      "dummy_gst_number",
						},
					},
				},
			},
		},
	}, nil
}
