package service

import (
	"context"
	"encoding/csv"
	"fmt"
	"net/http"
	"strings"
	"time"

	"kc/internal/ondc/models/dto"

	"github.com/gin-gonic/gin"
)

// ProcessBulkUpdate processes the bulk update request
func (s *Service) ProcessBulkUpdate(ctx context.Context, req *dto.OrderFinancialsBulkUpdateRequest) (*dto.OrderFinancialsBulkUpdateResponse, error) {
	return s.FinancialService.ProcessBulkUpdate(ctx, req)
}

func (s *Service) GetBulkActionLog(ctx context.Context, request *dto.GetBulkActionLogsRequest) (*dto.GetBulkActionLogsResponse, error) {
	return s.FinancialService.GetBulkActionLogs(ctx, request)
}

func (s *Service) getPlacedOrderIds(ctx context.Context, placedFrom int64, placedTo int64, seller []string, orderIds *[]int64) {
	// select * from kc_bazar_reconciliation kbr where kbr.order_placed between 1 and 10000000000000000 and oms in ("zoff_foods", "cravitos");
	orders, err := s.repository.CustomQuery(orderIds, fmt.Sprintf(
		`select
			kbr.order_id
		from
			kiranaclubdb.kc_bazar_reconciliation kbr
		where
			kbr.order_placed between %d and %d
			and kbr.oms in (%s);`, placedFrom, placedTo, "'"+strings.Join(seller, "','")+"'"))
	if err != nil {
		logger.Error(ctx, "error while getting orders by date range")
		return
	}
	for _, order := range orders {
		*orderIds = append(*orderIds, order.ID)
	}
}

func (s *Service) GetBulkData(ctx *gin.Context, request *dto.OrderFinancialsBulkGetRequest) {
	orderIds := []int64{}
	if request.Filters.OrderIds != nil && len(request.Filters.OrderIds) > 0 {
		orderIds = request.Filters.OrderIds
	} else {
		placedFrom := request.Filters.PlacedFrom
		placedTo := request.Filters.PlacedTo
		seller := request.Filters.Sellers
		s.getPlacedOrderIds(ctx, placedFrom, placedTo, seller, &orderIds)
	}
	data, err := s.FinancialService.GetBulkData(ctx, request.Data.OrderIds)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.Header("Content-Disposition", `attachment; filename="OrderFinancials_`+time.Now().Format("20060102")+`.csv"`)
	ctx.Header("Content-Type", "text/csv")

	writer := csv.NewWriter(ctx.Writer)
	// order_id	user_id	kcf	seller	display_status	shipment_status	last_status_updated_at	order_placed	order_confirmed	order_shipment_created	order_dispatched	order_delivered	order_rto_delivered	invoice_number	invoice_date	cart_value	order_value	invoice_value	seller_discount	platform_discount	payment_discount	markdown_discount	platform_cashback	total_discount	gstin_buyer	gstin_seller	pan_seller	place_of_supply	place_of_demand	taxable_value	cgst	sgst	igst	cess	total_gst	commission	saas_charge	tds	tcs	payment_type	advance_collected	cash_collected_by_3pl	pg	platform_pg_charges	platform_pg_charges_tax	seller_pg_charges	platform_cod_charges	platform_cod_charges_tax	seller_cod_charges	cod_remittance_date	platform_3pl_charges	platform_3pl_charges_tax	seller_3pl_charges	net_payable_to_supplier	final_settlement_to_supplier	final_settlement_date	platform_refund	platform_refund_charges	platform_refund_charge_tax	seller_refund	seller_refund_charges	seller_refund_charges_tax	3pl	awb_number	3pl_charged_weight	3pl_zone
	header := []string{
		"order_id",
		"user_id",
		"kcf",
		"seller",
		"display_status",
		"shipment_status",
		"last_status_updated_at",
		"order_placed",
		"order_confirmed",
		"order_shipment_created",
		"order_dispatched",
		"order_delivered",
		"order_rto_delivered",
		"invoice_number",
		"invoice_date",
		"cart_value",
		"order_value",
		"invoice_value",
		"seller_discount",
		"platform_discount",
		"payment_discount",
		"markdown_discount",
		"platform_cashback",
		"total_discount",
		"gstin_buyer",
		"gstin_seller",
		"pan_seller",
		"place_of_supply",
		"place_of_demand",
		"taxable_value",
		"cgst",
		"sgst",
		"igst",
		"cess",
		"total_gst",
		"commission",
		"saas_charge",
		"tds",
		"tcs",
		"payment_type",
		"advance_collected",
		"cash_collected_by_3pl",
		"pg",
		"platform_pg_charges",
		"platform_pg_charges_tax",
		"seller_pg_charges",
		"platform_cod_charges",
		"platform_cod_charges_tax",
		"seller_cod_charges",
		"cod_remittance_date",
		"platform_3pl_charges",
		"platform_3pl_charges_tax",
		"seller_3pl_charges",
		"net_payable_to_supplier",
		"final_settlement_to_supplier",
		"final_settlement_date",
		"platform_refund",
		"platform_refund_charges",
		"platform_refund_charge_tax",
		"seller_refund",
		"seller_refund_charges",
		"seller_refund_charges_tax",
		"3pl",
		"awb_number",
		"3pl_charged_weight",
		"3pl_zone",
	}

	err = writer.Write(header)
	if err != nil {
		logger.Error(ctx, "error while writing csv header")
		return
	}

	for _, record := range data {
		// most of the record are pointer to string, int or time.Time, so create a function here and get all the values
		row := []string{
			getValueFromInterface(record.OrderID),
			getValueFromInterface(record.UserID),
			getValueFromInterface(record.KCF),
			getValueFromInterface(record.Seller),
			getValueFromInterface(record.DisplayStatus),
			getValueFromInterface(record.ShipmentStatus),
			getValueFromInterface(record.LastStatusUpdatedAt),
			getValueFromInterface(record.OrderPlaced),
			getValueFromInterface(record.OrderConfirmed),
			getValueFromInterface(record.OrderShipmentCreated),
			getValueFromInterface(record.OrderDispatched),
			getValueFromInterface(record.OrderDelivered),
			getValueFromInterface(record.OrderRTODelivered),
			getValueFromInterface(record.InvoiceNumber),
			getValueFromInterface(record.InvoiceDate),
			getValueFromInterface(record.CartValue),
			getValueFromInterface(record.OrderValue),
			getValueFromInterface(record.InvoiceValue),
			getValueFromInterface(record.SellerDiscount),
			getValueFromInterface(record.PlatformDiscount),
			getValueFromInterface(record.PaymentDiscount),
			getValueFromInterface(record.MarkdownDiscount),
			getValueFromInterface(record.PlatformCashback),
			getValueFromInterface(record.TotalDiscount),
			getValueFromInterface(record.GSTINBuyer),
			getValueFromInterface(record.GSTINSeller),
			getValueFromInterface(record.PANSeller),
			getValueFromInterface(record.PlaceOfSupply),
			getValueFromInterface(record.PlaceOfDemand),
			getValueFromInterface(record.TaxableValue),
			getValueFromInterface(record.CGST),
			getValueFromInterface(record.SGST),
			getValueFromInterface(record.IGST),
			getValueFromInterface(record.CESS),
			getValueFromInterface(record.TotalGST),
			getValueFromInterface(record.Commission),
			getValueFromInterface(record.SaasCharge),
			getValueFromInterface(record.TDS),
			getValueFromInterface(record.TCS),
			getValueFromInterface(record.PaymentType),
			getValueFromInterface(record.AdvanceCollected),
			getValueFromInterface(record.CashCollectedBy3PL),
			getValueFromInterface(record.PG),
			getValueFromInterface(record.PlatformPGCharges),
			getValueFromInterface(record.PlatformPGChargesTax),
			getValueFromInterface(record.SellerPGCharges),
			getValueFromInterface(record.PlatformCODCharges),
			getValueFromInterface(record.PlatformCODChargesTax),
			getValueFromInterface(record.SellerCODCharges),
			getValueFromInterface(record.CODRemittanceDate),
			getValueFromInterface(record.Platform3PLCharges),
			getValueFromInterface(record.Platform3PLChargesTax),
			getValueFromInterface(record.Seller3PLCharges),
			getValueFromInterface(record.NetPayableToSupplier),
			getValueFromInterface(record.FinalSettlementToSupplier),
			getValueFromInterface(record.FinalSettlementDate),
			getValueFromInterface(record.PlatformRefund),
			getValueFromInterface(record.PlatformRefundCharges),
			getValueFromInterface(record.PlatformRefundChargeTax),
			getValueFromInterface(record.SellerRefund),
			getValueFromInterface(record.SellerRefundCharges),
			getValueFromInterface(record.SellerRefundChargesTax),
			getValueFromInterface(record.ThirdPartyLogistics),
			getValueFromInterface(record.AWBNumber),
			getValueFromInterface(record.ThirdPLChargedWeight),
			getValueFromInterface(record.ThirdPLZone),
		}
		if err := writer.Write(row); err != nil {
			fmt.Printf("Error writing record: %v\n", err)
			continue
		}
	}
	writer.Flush()
	if err := writer.Error(); err != nil {
		logger.Error(ctx, "error while flushing csv writer")
		return
	}
}

// this function will take interface which can be int, int64, string, bool, time.Time, float64 or pointer to these return a string with the value if exists else return empty string
// also handle the pointer cases for every type
func getValueFromInterface(value interface{}) string {
	if value == nil {
		return ""
	}
	switch v := value.(type) {
	case int:
		return fmt.Sprintf("%d", v)
	case *int:
		return fmt.Sprintf("%d", *v)
	case int64:
		return fmt.Sprintf("%d", v)
	case *int64:
		return fmt.Sprintf("%d", *v)
	case string:
		return v
	case *string:
		return *v
	case bool:
		return fmt.Sprintf("%t", v)
	case *bool:
		return fmt.Sprintf("%t", *v)
	case time.Time:
		return v.Format("2006-01-02 15:04:05")
	case *time.Time:
		return v.Format("2006-01-02 15:04:05")
	case float64:
		return fmt.Sprintf("%.2f", v)
	case *float64:
		return fmt.Sprintf("%.2f", *v)
	default:
		return ""
	}
}
