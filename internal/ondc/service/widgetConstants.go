package service

import "kc/internal/ondc/models/dto"

var starRatingTexts = map[string]dto.RatingText{
	"1": {
		Text:  "पसंद नहीं आया 😞",
		Color: "#000000",
	},
	"2": {
		Text:  "ठीक-ठाक है 😐",
		Color: "#000000",
	},
	"3": {
		Text:  "सामान्य है 🙂",
		Color: "#FFC403",
	},
	"4": {
		Text:  "अच्छा है 😊",
		Color: "#038944",
	},
	"5": {
		Text:  "बहुत अच्छा है 😍",
		Color: "#038944",
	},
}

var reviewFeedbackSection = dto.FeedbackSection{
	Title: "प्रोडक्ट के बारें में अपनी राय दे",
	InputField: dto.InputField{
		Placeholder:     "अपना फ़ीडबैक यहां दर्ज करें...",
		Multiline:       true,
		BorderRadius:    16,
		ContainerHeight: 100,
		MaxLines:        4,
		KeyboardType:    "default",
	},
}

var mediaSection = dto.MediaSection{
	Title: "प्रोडक्ट का वीडियो/फोटो जोड़े",
	UploadPlaceholder: dto.UploadPlaceholder{
		Text:     "अच्छे दुकानदार हमेशा दूसरों की मदद के लिए वीडियो/फोटो जोड़ते हैं।",
		ImageURL: "https://d2rstorage2.blob.core.windows.net/widget/November/14/c778fa08-c248-43f2-9e36-908131167961/1731569516955.webp",
	},
}

var SubmittedSection = dto.SubmittedSection{
	Text:     "धन्यवाद ! आपका रिव्यू सफ़लतापूर्वक जमा हो गया है|",
	ImageURL: "https://d2rstorage2.blob.core.windows.net/widget/November/21/c55bb335-c4bc-4c99-a214-de0cec1ea80e/1732188243133.webp",
}
