package search

import (
	"context"
	"encoding/json"
	"fmt"
	elasticRepo "kc/internal/ondc/repositories/elasticRepo"
	"math"
)

// Query represents a search term for an entity
type Query struct {
	Value string `json:"value"`
}

// Product represents a product document
type Product struct {
	ProductID  int     `json:"product_id"`
	CategoryID int     `json:"category_id"`
	Seller     string  `json:"seller"`
	Queries    []Query `json:"queries"`
	Score      float64 `json:"score"`
}

// ProductSearchService provides product-specific search functionality
type ProductSearchService struct {
	esClient              *elasticRepo.ElasticSearchClient
	indexName             string
	enhancedIndex         string
	categoryIndex         string // Add category index
	categoryEnhancedIndex string // Add enhanced category index
	sellerWeights         map[string]float64
}

// Update NewProductSearchService to accept category indices
func NewProductSearchService(
	esClient *elasticRepo.ElasticSearchClient,
	indexName,
	enhancedIndex string,
	categoryIndex string, // New parameter for category index
	categoryEnhancedIndex string, // New parameter for enhanced category index
	sellerWeights map[string]float64,
) *ProductSearchService {
	return &ProductSearchService{
		esClient:              esClient,
		indexName:             indexName,
		enhancedIndex:         enhancedIndex,
		categoryIndex:         categoryIndex,
		categoryEnhancedIndex: categoryEnhancedIndex,
		sellerWeights:         sellerWeights,
	}
}

// SearchProducts searches for products with given search terms and optional seller filter
func (ps *ProductSearchService) SearchProducts(ctx context.Context, searchTerm string, sellerFilter string, from, size int, diversify bool, innerHitSize int, enablePhonetic bool) ([]Product, int, error) {
	options := elasticRepo.DefaultSearchOptions()
	options.From = from
	options.Size = size
	options.DiversifySellers = diversify
	options.InnerHitsSize = innerHitSize // Get top products per seller
	options.EnablePhonetic = enablePhonetic

	// Set up seller filter if provided
	if sellerFilter != "" {
		options.FilterField = "seller"
		options.FilterValue = []string{sellerFilter}
		// When filtering for a specific seller, diversification doesn't make sense
		options.DiversifySellers = false
	} else if len(ps.sellerWeights) > 0 {
		// If no filter but we have seller weights, apply them
		options.SellerWeights = ps.sellerWeights
	}

	// Choose the appropriate index based on whether enhanced search is enabled
	indexToUse := ps.indexName
	if enablePhonetic && ps.enhancedIndex != "" {
		indexToUse = ps.enhancedIndex
	}

	var products []Product
	total, err := ps.esClient.SearchEntities(ctx, []string{indexToUse}, searchTerm, options, &products)
	if err != nil {
		return nil, 0, err
	}

	return products, total, nil
}

func (ps *ProductSearchService) SearchProductsWithScores(
	ctx context.Context,
	searchTerm string,
	sellerFilter string,
	excludeSellers []string,
	from, size int,
	diversify bool,
	innerHitSize int,
	enablePhonetic bool,
	productWeight float64, // New parameter for product weight
	categoryWeight float64, // New parameter for category weight
) (*ProductsResponse, error) {
	options := elasticRepo.DefaultSearchOptions()
	options.From = from
	options.Size = size
	options.DiversifySellers = diversify
	options.InnerHitsSize = innerHitSize
	options.EnablePhonetic = enablePhonetic

	// Set up seller filter if provided
	if sellerFilter != "" {
		options.FilterField = "seller"
		options.FilterValue = []string{sellerFilter}
		options.DiversifySellers = false
	} else if len(ps.sellerWeights) > 0 {
		options.SellerWeights = ps.sellerWeights
	}

	if len(excludeSellers) > 0 {
		options.ExcludeField = "seller"
		options.ExcludeValue = excludeSellers
	}
	// Choose the appropriate indices based on whether enhanced search is enabled
	productIdx := ps.indexName
	categoryIdx := ps.categoryIndex

	if enablePhonetic && ps.enhancedIndex != "" {
		productIdx = ps.enhancedIndex
		categoryIdx = ps.categoryEnhancedIndex
	}

	// Use SearchWithCategoryBoost instead of regular Search
	result, err := ps.esClient.SearchWithCategoryBoost(
		ctx,
		productIdx,
		categoryIdx,
		searchTerm,
		options,
		productWeight,
		categoryWeight,
	)
	if err != nil {
		return nil, err
	}

	// The rest of the function remains unchanged
	// Create a more detailed seller score map
	type sellerScoreDetails struct {
		total     float64
		max       float64
		count     int
		positions map[int]bool
	}

	sellerScoreMap := make(map[string]*sellerScoreDetails)

	// Process all hits to get detailed seller scores
	for i, hit := range result.Hits.Hits {
		var product Product
		if err := json.Unmarshal(hit.Source, &product); err != nil {
			fmt.Printf("Error unmarshaling product: %s\n", err)
			continue
		}

		seller := product.Seller

		if _, exists := sellerScoreMap[seller]; !exists {
			sellerScoreMap[seller] = &sellerScoreDetails{
				total:     hit.Score,
				max:       hit.Score,
				count:     1,
				positions: map[int]bool{i: true},
			}
		} else {
			// Update existing seller score
			sellerScoreMap[seller].total += hit.Score
			sellerScoreMap[seller].count++
			sellerScoreMap[seller].positions[i] = true

			if hit.Score > sellerScoreMap[seller].max {
				sellerScoreMap[seller].max = hit.Score
			}
		}
	}

	// Convert to simpler map for score calculation
	sellerScores := make(map[string]float64)

	// For each seller, calculate a weighted score
	for seller, details := range sellerScoreMap {
		// Start with max score as base
		score := details.max

		// Add a more significant bonus based on average score
		avgScore := details.total / float64(details.count)
		score += avgScore * 0.25

		// Add position bonus for sellers with products in top results
		highestPosition := len(result.Hits.Hits)
		for pos := range details.positions {
			if pos < highestPosition {
				highestPosition = pos
			}
		}

		// Position bonus (smaller is better)
		positionFactor := 0.15 * (1.0 - float64(highestPosition)/float64(len(result.Hits.Hits)))
		score *= (1.0 + positionFactor)

		// Apply a bonus for sellers with multiple products in the results
		if details.count > 1 {
			// Logarithmic bonus that increases with product count but doesn't overwhelm
			countBonus := 0.05 * math.Log(float64(details.count)+1)
			score *= (1.0 + countBonus)
		}

		// Store the enhanced score
		sellerScores[seller] = score
	}

	// Process results
	var products []Product
	var total int
	if options.DiversifySellers {
		total, err = ps.esClient.ProcessDiversifiedResults(result, &products, options)
	} else {
		total, err = ps.esClient.ProcessStandardResults(result, &products)
	}

	if err != nil {
		return nil, err
	}

	// Calculate seller scores from the products with actual scores from Elasticsearch
	scoredSellers := CalculateSellerScores(products, ps.sellerWeights, sellerScores)

	// Return the enhanced response
	return &ProductsResponse{
		Products:     products,
		SellerScores: scoredSellers,
		TotalCount:   total,
	}, nil
}

// GetProductsBySellerScores groups products by seller, sorted by seller score
func GetProductsBySellerScores(products []Product, sellerScores []SellerScore) map[string][]Product {
	// Group products by seller
	productsBySeller := make(map[string][]Product)

	for _, product := range products {
		productsBySeller[product.Seller] = append(productsBySeller[product.Seller], product)
	}

	return productsBySeller
}
