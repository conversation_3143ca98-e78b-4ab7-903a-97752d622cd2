// SellerScore.go - A new file to add

package search

import (
	"math"
	"sort"
)

// SellerScore represents a seller's score information
type SellerScore struct {
	Seller     string  `json:"seller"`
	Score      float64 `json:"score"`       // Raw relevance score
	Weight     float64 `json:"weight"`      // Configured seller weight
	FinalScore float64 `json:"final_score"` // Combined score with all factors
	Count      int     `json:"count"`       // Number of products from this seller
}

// ProductsResponse enhances the search results with seller scores
type ProductsResponse struct {
	Products     []Product     `json:"products"`
	SellerScores []SellerScore `json:"seller_scores"`
	TotalCount   int           `json:"total_count"`
}

// CalculateSellerScores extracts seller scores from search results
func CalculateSellerScores(products []Product, sellerWeights map[string]float64, sellerScores map[string]float64) []SellerScore {
	// Map to track seller scores
	sellerScoreMap := make(map[string]*SellerScore)

	// Process all products
	for _, product := range products {
		seller := product.Seller

		// Add or update seller score
		if _, exists := sellerScoreMap[seller]; !exists {
			// Get seller weight if available
			weight := 1.0
			if w, ok := sellerWeights[seller]; ok {
				weight = w
			}

			// Get score if available
			score := 1.0
			if s, ok := sellerScores[seller]; ok {
				score = s
			}

			sellerScoreMap[seller] = &SellerScore{
				Seller:     seller,
				Score:      score,
				Weight:     weight,
				FinalScore: score * weight,
				Count:      1,
			}
		} else {
			// Update existing seller score's count
			sellerScoreMap[seller].Count++

			// For tie-breaking, adjust the final score based on product count
			// This creates a slight advantage for sellers with more products at the same score
			// Apply a logarithmic bonus to avoid overwhelming the base score
			countBonus := 0.05 * math.Log(float64(sellerScoreMap[seller].Count)+1)
			sellerScoreMap[seller].FinalScore = sellerScoreMap[seller].Score * sellerScoreMap[seller].Weight * (1.0 + countBonus)
		}
	}

	// Convert map to slice
	scores := make([]SellerScore, 0, len(sellerScoreMap))
	for _, score := range sellerScoreMap {
		scores = append(scores, *score)
	}

	// Sort by final score (descending)
	sortSellerScores(scores)

	return scores
}

// sortSellerScores sorts seller scores by final score in descending order
func sortSellerScores(scores []SellerScore) {
	// Sort by final score descending
	sort.Slice(scores, func(i, j int) bool {
		// Primary sort by final score
		if scores[i].FinalScore != scores[j].FinalScore {
			return scores[i].FinalScore > scores[j].FinalScore
		}

		// If scores are tied, secondary sort by product count
		if scores[i].Count != scores[j].Count {
			return scores[i].Count > scores[j].Count
		}

		// Last resort: alphabetical by seller name
		return scores[i].Seller < scores[j].Seller
	})
}
