package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	"kc/internal/ondc/service/products"
	"kc/internal/ondc/utils"
	"slices"
	"sort"

	"github.com/mixpanel/mixpanel-go"
)

type GetUserClaimedRewardData struct {
	RewardClaimed   bool    `json:"reward_claimed"`
	RewardProductId string  `json:"reward_product_id"`
	TotalOrderValue float64 `json:"total_order_value"`
	OrderId         string  `json:"order_id"`
}

const (
	REWARD_PARTIAL     = "PARTIAL"
	REWARD_ALL         = "REWARD_ALL"
	REWARD_NULL        = "NULL"
	REWARD_CLAIMED     = "CLAIMED"
	MEGA_TARGET_SCHEME = "megaRewardTargetScheme"
)

func getSortedSlabPrices() []float64 {
	slabPrices := make([]float64, 0, len(loyaltyRewardSlabs))
	for price := range loyaltyRewardSlabs {
		slabPrices = append(slabPrices, price)
	}

	sort.Slice(slabPrices, func(i, j int) bool {
		return slabPrices[i] > slabPrices[j]
	})

	return slabPrices
}

// Common function to get lowest price from slabs
func getLowestSlabPrice(slabPrices []float64) float64 {
	return slabPrices[len(slabPrices)-1]
}

// Common function to create ineligible reward response
func createIneligibleResponse(rewardClaimType string) *dto.CheckUserRewardsResponse {
	return &dto.CheckUserRewardsResponse{
		ShowRewards:     false,
		RewardClaimType: rewardClaimType,
	}
}

func getSortedSlabAndVerifyTotalOrderValue(totalOrderValue float64) ([]float64, error) {
	slabPrices := getSortedSlabPrices()
	if len(slabPrices) == 0 {
		return nil, fmt.Errorf("no loyalty reward slabs configured")
	}

	lowestPrice := getLowestSlabPrice(slabPrices)

	if lowestPrice <= 0 {
		return nil, fmt.Errorf("no valid lowest slab price found")
	}

	if totalOrderValue < lowestPrice {
		return nil, fmt.Errorf("total order value %.2f is less than the minimum required %.2f for any rewards", totalOrderValue, lowestPrice)
	}

	return slabPrices, nil
}

func (s *Service) CheckUserRewards(ctx context.Context, req dto.CheckUserRewardsRequest) (*dto.CheckUserRewardsResponse, error) {

	isValid, totalOrderValue, err := s.validateAndGiveTotalOrderValue(req.UserID)

	if err != nil {
		return nil, err
	}

	res := dto.CheckUserRewardsResponse{
		ShowRewards: isValid == REWARD_ALL,
	}

	if isValid != REWARD_ALL {
		res.RewardClaimType = isValid
		return &res, nil
	}

	slabPrices, err := getSortedSlabAndVerifyTotalOrderValue(totalOrderValue)

	if err != nil {
		return createIneligibleResponse(REWARD_NULL), nil
	}

	rewardProducts := make([]dto.RewardProductDetails, 0)

	for _, price := range slabPrices {
		if totalOrderValue >= price {
			for _, productDetails := range loyaltyRewardSlabs[price] {
				productInfo, exists := products.GetProductByID(productDetails)
				if !exists {
					continue
				}

				rewardProducts = append(rewardProducts, dto.RewardProductDetails{
					ProductId:    productInfo.ID,
					ProductName:  productInfo.Name,
					ProductImage: productInfo.ProductImageURLs[0],
				})
			}
			break
		}
	}

	userAddressResponse, err := s.GetUserAddress(ctx, &dto.GetUserAddressRequest{
		UserID: req.UserID,
		Data: dto.AppServiceAbilityAPIRequestData{
			Seller: utils.KIRANACLUB_LOYALTY_REWARDS,
			Source: utils.KIRANACLUB_LOYALTY_REWARDS,
		},
	})

	if err != nil {
		return nil, err

	}
	res.Products = rewardProducts
	res.RewardClaimType = isValid
	res.UserAddress = userAddressResponse

	return &res, nil
}

func (s *Service) ClaimUserRewards(ctx context.Context, req dto.CreateUserLoyaltyOrderRequest) (*dto.AppKiranaBazarOrderResponse, error) {
	// Validate request first
	if len(req.Data.ProductIds) == 0 {
		return nil, fmt.Errorf("no product ids found in request")
	}

	// Only allow claiming one product at a time
	if len(req.Data.ProductIds) > 1 {
		return nil, fmt.Errorf("only one product can be claimed at a time, received %d products", len(req.Data.ProductIds))
	}

	requestedProductId := req.Data.ProductIds[0]
	if requestedProductId == "" {
		return nil, fmt.Errorf("invalid empty product id")
	}

	// Validate user eligibility
	isValid, totalOrderValue, err := s.validateAndGiveTotalOrderValue(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to validate user eligibility: %w", err)
	}

	if isValid != REWARD_ALL {
		return nil, fmt.Errorf("user is not eligible for rewards, current status: %s", isValid)
	}

	// Handle empty loyalty reward slabs
	if len(loyaltyRewardSlabs) == 0 {
		return nil, fmt.Errorf("no loyalty reward slabs configured")
	}

	slabPrices, err := getSortedSlabAndVerifyTotalOrderValue(totalOrderValue)

	if err != nil {
		return nil, fmt.Errorf("failed to get sorted slab prices: %w", err)
	}

	// Find qualifying products for user's order value
	productIds := []string{}
	foundQualifyingSlab := false

	for _, price := range slabPrices {
		if totalOrderValue >= price {
			productIds = append(productIds, loyaltyRewardSlabs[price]...)
			foundQualifyingSlab = true
			break // Found the highest qualifying slab, stop here
		}
	}

	// This shouldn't happen given our earlier check, but good to be safe
	if !foundQualifyingSlab || len(productIds) == 0 {
		return nil, fmt.Errorf("no qualifying products found for order value %.2f", totalOrderValue)
	}

	// Validate that requested product is in the qualifying list
	if !slices.Contains(productIds, requestedProductId) {
		return nil, fmt.Errorf("product id %s is not eligible for rewards at current order value %.2f", requestedProductId, totalOrderValue)
	}

	// Create the loyalty reward order
	resp, err := s.CreateUserLoyaltyRewardOrder(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to create loyalty reward order: %w", err)
	}

	// Mark the reward as claimed
	_, err = s.setUserClaimedReward(req.UserID, requestedProductId, MEGA_TARGET_SCHEME, totalOrderValue, resp)
	if err != nil {
		// This is critical - if we can't mark as claimed, we might have a duplicate claim issue
		// You might want to rollback the order creation here depending on your business logic
		return nil, fmt.Errorf("failed to mark reward as claimed: %w", err)
	}

	s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
		s.Mixpanel.NewEvent("Target Scheme Reward Claimed", req.UserID, map[string]interface{}{
			"user_id":     req.UserID,
			"product_id":  req.Data.ProductIds[0],
			"order_id":    safeParse(resp),
			"order_total": totalOrderValue,
		}),
	})

	return resp, nil
}

func safeParse(resp *dto.AppKiranaBazarOrderResponse) string {
	if resp != nil && len(resp.Data) > 0 {
		return resp.Data[0].ID
	}
	return "INVALID ORDER ID"
}

func (s *Service) getUserClaimedReward(userId string, targetScheme string) (*GetUserClaimedRewardData, error) {

	var userClaimedRewardData GetUserClaimedRewardData

	err := s.FirebaseRepository.RtDb.NewRef(fmt.Sprintf(`/%s/%s`, targetScheme, userId)).Get(context.Background(), &userClaimedRewardData)

	if err != nil {
		return nil, err
	}

	return &userClaimedRewardData, nil
}

func (s *Service) setUserClaimedReward(userId string, productId string, targetScheme string, totalOrderValue float64, resp *dto.AppKiranaBazarOrderResponse) (string, error) {

	err := s.FirebaseRepository.RtDb.NewRef(fmt.Sprintf(`/%s/%s`, targetScheme, userId)).Set(context.Background(), map[string]interface{}{
		"reward_claimed":    true,
		"reward_product_id": productId,
		"total_order_value": totalOrderValue,
		"order_id":          safeParse(resp),
	})

	if err != nil {
		return "", err
	}

	return productId, nil
}

func (s *Service) validateAndGiveTotalOrderValue(userId string) (string, float64, error) {
	rewardClaimed, err := s.getUserClaimedReward(userId, MEGA_TARGET_SCHEME)

	if err != nil {
		return REWARD_NULL, 0, err
	}
	if rewardClaimed.RewardClaimed {
		return REWARD_CLAIMED, 0, nil
	}

	str := fmt.Sprintf(`
		select
			ko.id,
			ko.display_status,
			kod.order_details
		from
			kiranaclubdb.kiranabazar_orders ko
		join kiranaclubdb.kiranabazar_order_details kod on
			ko.id = kod.order_id
		where
			user_id = '%s'
			and ko.created_at >= '2025-05-08'
			and ko.created_at < '2025-06-26'
    `, userId)

	var orderCounts []dao.OrderDetails
	_, err = s.repository.CustomQuery(&orderCounts, str)
	if err != nil {
		return REWARD_NULL, 0, err
	}
	if len(orderCounts) == 0 {
		return REWARD_NULL, 0, nil
	}

	var totalDeliveredValue float64
	isPartial := false
	hasDelivered := false

	for _, order := range orderCounts {
		if includes([]string{displaystatus.IN_TRANSIT, displaystatus.CONFIRMED, displaystatus.NDR, displaystatus.PENDING_CONFIRMATION, displaystatus.SHIPMENT_CREATED}, order.DisplayStatus) {
			isPartial = true
			break
		}

		if order.DisplayStatus == displaystatus.DELIVERED {
			hasDelivered = true
			var kbOrderDetails dao.KiranaBazarOrderDetails
			err := json.Unmarshal(order.KbOrderDetail, &kbOrderDetails)

			if err != nil {
				return REWARD_NULL, 0, nil
			}

			totalDeliveredValue += kbOrderDetails.GetOrderValue()
		}
	}

	if isPartial {
		return REWARD_PARTIAL, 0, nil
	}
	if hasDelivered {
		return REWARD_ALL, totalDeliveredValue, nil
	}
	return REWARD_NULL, 0, nil
}
