package products

import (
	"encoding/json"
	"fmt"
	"github.com/Masterminds/semver"
	"gorm.io/datatypes"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/service/brands"
	inventoryDao "kc/internal/ondc/service/inventory/models/dao"
	"kc/internal/ondc/utils"
	"math"
	"sort"
	"strings"
)

type Products []*Product

func (p *Product) ToSellerItems(conditions ToSellerItemsCondition, appVersion string, userContext *PricingContext) shared.SellerItems {
	// sort size variants and push oos products in the end
	sort.Slice(p.SizeVariants, func(i, j int) bool {
		if p.SizeVariants[i].IsOOS != nil && *p.SizeVariants[i].IsOOS {
			return false
		}
		if p.SizeVariants[j].IsOOS != nil && *p.SizeVariants[j].IsOOS {
			return true
		}
		return p.SizeVariants[i].Rank != nil && p.SizeVariants[j].Rank != nil && *p.SizeVariants[i].Rank < *p.SizeVariants[j].Rank
	})

	// size variant of the product
	var sizeVariants []shared.SellerItemsData
	if len(p.SizeVariants) > 1 && conditions.IncludeVariant {
		for _, j := range p.SizeVariants {
			if j.IsActive != nil && *j.IsActive {
				sizeVariants = append(sizeVariants, j.SizeVariantsSellerItems(appVersion, userContext, nil))
			}
		}
	}

	if len(sizeVariants) <= 1 {
		sizeVariants = nil // If only contains self size variants, set to nil
	}

	parentProduct := p.SizeVariantsSellerItems(appVersion, userContext, nil)

	return shared.SellerItems{
		SellerItemsData: parentProduct,
		SizeVariants:    sizeVariants,
	}
}

func (p *Product) ToSellerItemsB2B(conditions ToSellerItemsCondition) shared.SellerItemsB2B {
	// size variant of the product
	sizeVariants := []shared.SellerItemsDataB2B{}
	if len(p.SizeVariants) > 0 && conditions.IncludeVariant {
		for _, j := range p.SizeVariants {
			sizeVariants = append(sizeVariants, j.SizeVariantsSellerItemsB2B(conditions))
		}
	}

	parentProduct := p.SizeVariantsSellerItemsB2B(conditions)

	return shared.SellerItemsB2B{
		SellerItemsDataB2B: parentProduct,
		SizeVariants:       sizeVariants,
	}
}

func (p *Product) SizeVariantsSellerItems(appVersion string, userContext *PricingContext, productArray *[]Product) shared.SellerItemsData {
	userAppVersion, _ := semver.NewVersion(appVersion)
	productsScreenChangeAppVersion, _ := semver.NewVersion("6.5.2")

	// Convert to banner image URLs
	bannerUrls := utils.GetProductBannerImageUrls(p.ProductMediaUrls, false)

	// Convert size variant code
	var sizeVariantCode int64
	var productType *string
	if p.SizeVariantCode != nil {
		sizeVariantCode = int64(*p.SizeVariantCode)
	}
	if p.ProductType != "" {
		productType = &p.ProductType
	}

	p.MetaProperties.MarkupMarginKey = "मार्जिन:"
	p.MetaProperties.MarkupMarginValue = GetMarginPercentageString(p.MetaProperties.MarkupMarginString, p.MetaProperties.MarkupMargin)
	p.MetaProperties.MRPStringValue = utils.FormatINR(p.MetaProperties.MRPNumber)

	if userContext != nil {
		rules := GetInstance().getApplicablePricingRules(p.ID, userContext)
		if len(rules) > 0 {
			topRule := SelectPricingRule(rules)
			if topRule != nil {
				impactedPrice, _ := applyPricingRule(topRule, userContext, p.MetaProperties.WholesaleRate)
				if impactedPrice > 0 {
					clonedWholesaleRate := p.MetaProperties.WholesaleRate
					discountedAmount := clonedWholesaleRate - impactedPrice
					brandDiscountShare := discountedAmount * topRule.BrandShare

					if p.MetaProperties.BrandWholesaleRate == nil {
						p.MetaProperties.BrandWholesaleRate = &clonedWholesaleRate
					} else {
						// let the brand wholesale rate be the original value it has
					}

					if brandDiscountShare > 0 {
						effectiveRate := *p.MetaProperties.BrandWholesaleRate - brandDiscountShare
						p.MetaProperties.EffectiveWholesaleRate = &effectiveRate
					} else {
						p.MetaProperties.EffectiveWholesaleRate = p.MetaProperties.BrandWholesaleRate
					}

					p.MetaProperties.WholesaleRate = impactedPrice
					p.MetaProperties.WholesaleRateString = utils.FormatINR(impactedPrice)
					p.MetaProperties.MarkupMargin = math.Round(((1-(impactedPrice/p.MetaProperties.MRPNumber))*100)*100) / 100 // Round to 2 decimal places
					p.MetaProperties.MarkupMarginString = fmt.Sprintf("मार्जिन: %s%%", GetStringFromNumberUpto1Decimal(p.MetaProperties.MarkupMargin))
					p.MetaProperties.MarkupMarginValue = GetMarginPercentageString(p.MetaProperties.MarkupMarginString, p.MetaProperties.MarkupMargin)
				}
			}
		}
	}

	source, exist := brands.GetSourceBySeller(p.Seller)
	if !exist {
		fmt.Println("failed to get seller by source: %s", p.Seller)
		source = p.Seller // Fallback to original seller if not found
	}
	sellerName, err := brands.GetNameMappingBySeller(p.Seller)
	if err != nil {
		fmt.Println("failed to get seller name mapping for seller: %s, error: %v", p.Seller, err)
		sellerName = p.Seller
	}

	metaPropertiesJson, err := json.Marshal(p.MetaProperties)
	if err != nil {
		fmt.Println("Error marshalling MetaProperties:", err)
	}

	var currentInventoryQuantity *int
	if p.DisplayQuantity != nil && p.MetaProperties.PackSize != 0 {
		availableQuantity := *p.DisplayQuantity / p.MetaProperties.PackSize
		currentInventoryQuantity = &availableQuantity
	}
	topBadge := buildTopBadge(*p)
	bottomBadge := buildBottomBadge(*p, userAppVersion, productsScreenChangeAppVersion, &topBadge, currentInventoryQuantity)
	bottomInfoBadge := buildBottomInfoBadge(*p)

	return shared.SellerItemsData{
		ProviderID:      source,
		Seller:          p.Seller,
		SellerName:      sellerName,
		Name:            p.Name,
		ImageUrls:       p.ProductImageURLs,
		BannerImageUrls: bannerUrls,
		CategoryIds:     []string{fmt.Sprintf("%d", p.CategoryID)}, //TODO: check if this is correct
		ID:              fmt.Sprintf("%d", p.ID),
		Quantity:        0,
		Description:     getDescriptionForProduct(p, productArray),
		Meta:            datatypes.JSON(metaPropertiesJson),
		ProductType:     productType,
		SizeVariantCode: &sizeVariantCode,
		IsDefault:       utils.SafeBool(p.IsDefault),
		IsOos:           bottomBadge,
		IsActive:        utils.SafeBool(p.IsActive),
		BottomBadge:     bottomBadge,
		BottomInfoBadge: bottomInfoBadge,
		TopBadge:        topBadge,
		Type:            33,
		Rank:            utils.SafeInt(p.Rank),
		PopularityValue: p.PopularityValue,
		Rating:          nil,
		RatingsCount:    nil,
		ParentItemID:    fmt.Sprintf("%d", *p.SizeVariantCode),
		Manufacturer:    p.Manufacturer,
	}
}

func (p *Product) SizeVariantsSellerItemsB2B(conditions ToSellerItemsCondition) shared.SellerItemsDataB2B {
	analysisType := conditions.AnalysisType
	if len(analysisType) == 0 {
		analysisType = DEFAULT_SALES_ANALYSIS
	}

	// Convert to banner image URLs
	bannerUrls := utils.GetProductBannerImageUrls(p.ProductMediaUrls, false)
	var productType *string
	if p.ProductType != "" {
		productType = &p.ProductType
	}
	// Calculate rating
	// rating := 0.0
	// if p.RatingsCount > 0 {
	// 	rating = float64(p.RatingsSum) / float64(p.RatingsCount)
	// }

	// Convert size variant code
	var sizeVariantCode int64
	if p.SizeVariantCode != nil {
		sizeVariantCode = int64(*p.SizeVariantCode)
	}

	var currentInventoryQuantity *int
	if p.DisplayQuantity != nil && p.MetaProperties.PackSize != 0 {
		availableQuantity := *p.DisplayQuantity / p.MetaProperties.PackSize
		currentInventoryQuantity = &availableQuantity
	}
	topBadge := buildTopBadge(*p)
	bottomBadge := buildBottomBadge(*p, nil, nil, &topBadge, currentInventoryQuantity)

	piecesQuantity, daysOfStockLeft, salesThroughputPerDay, actualSalesDays, throughputUpdateDate := calculateStockMetrics(p.DisplayQuantity, p.InventoryAnalysis)

	source, exist := brands.GetSourceBySeller(p.Seller)
	if !exist {
		fmt.Println("failed to get seller by source: %s", p.Seller)
		source = p.Seller // Fallback to original seller if not found
	}

	sellerName, err := brands.GetNameMappingBySeller(p.Seller)
	if err != nil {
		fmt.Println("failed to get seller name mapping for seller: %s, error: %v", p.Seller, err)
		sellerName = p.Seller
	}

	return shared.SellerItemsDataB2B{
		ProviderID:      source,
		Seller:          p.Seller,
		SellerName:      sellerName,
		Name:            p.Name,
		ImageUrls:       p.ProductImageURLs,
		BannerImageUrls: bannerUrls,
		CategoryIds:     []string{fmt.Sprintf("%d", p.CategoryID)},
		ID:              fmt.Sprintf("%d", p.ID),
		Quantity:        0,
		Description:     getDescriptionForProduct(p, nil),
		Meta:            datatypes.JSON(p.Meta),
		ProductType:     productType,
		SizeVariantCode: &sizeVariantCode,
		IsDefault:       utils.SafeBool(p.IsDefault),
		IsOos:           bottomBadge,
		BottomBadge:     bottomBadge,
		TopBadge:        topBadge,
		Type:            33,
		Rank:            utils.SafeInt(p.Rank),
		IsActive:        utils.SafeBool(p.IsActive),
		// Rating:          &rating,
		// RatingsCount:    &p.RatingsCount,
		ParentItemID:           fmt.Sprintf("%d", *p.SizeVariantCode),
		HSNCode:                p.MetaProperties.HSNCode,
		SKUCode:                &p.Code,
		PiecesQuantity:         piecesQuantity,
		SalesThroughputPerDay:  salesThroughputPerDay,
		ThroughputUpdationDate: throughputUpdateDate,
		DaysStockLeft:          daysOfStockLeft,
		ActualSalesDays:        actualSalesDays,
		SalesAnalysisData:      calculateSalesMetrics(p.InventoryAnalysis),
	}
}

func (products Products) RemoveSelfVariantReferences() []*Product {
	if len(products) == 0 {
		return products
	}

	result := make(Products, len(products))

	for i, product := range products {
		// Create a copy of the product
		productCopy := *product

		// If this product has variants
		if productCopy.SizeVariants != nil && len(productCopy.SizeVariants) > 0 {
			// Create a filtered variant list that excludes self-references
			filteredVariants := make([]*Product, 0, len(productCopy.SizeVariants))

			for _, variant := range productCopy.SizeVariants {
				// Skip variants that have the same ID as the parent product
				if variant.ID != product.ID {
					// For these variants, ensure they don't reference back to the parent
					variantCopy := *variant

					// Clear the size variants from each variant to prevent cycles
					variantCopy.SizeVariants = nil

					filteredVariants = append(filteredVariants, &variantCopy)
				}
			}

			// Set the filtered variant list
			productCopy.SizeVariants = filteredVariants
		}

		result[i] = &productCopy
	}

	return result
}

func buildBottomBadge(product Product, userAppVersion, productsScreenChangeAppVersion *semver.Version,
	topBadge **shared.Badge, currentInventoryQuantity *int) *shared.Badge {

	if product.IsOOS == nil || (product.IsOOS != nil && !*product.IsOOS) {
		return nil
	}

	cartPOSMProductsVersion := "6.6.0"
	cartPOSMProductsVersionSemver, _ := semver.NewVersion(cartPOSMProductsVersion)
	if product.ProductType == string(ProductTypePosm) {
		// if app version >= 6.6.0 send oos nil
		if userAppVersion != nil && (userAppVersion.GreaterThan(cartPOSMProductsVersionSemver) || userAppVersion.Equal(cartPOSMProductsVersionSemver)) {
			// For POSM products, we don't want to show OOS badge
			return nil
		} else {
			return &shared.Badge{
				Text:    "बिलकुल फ्री",
				Color:   "#068f01",
				BgColor: []string{"#ffffff", "#ffffff"},
			}
		}
	}

	if currentInventoryQuantity != nil && *currentInventoryQuantity > 2 && *product.IsOOS == false {
		return nil
	}

	if product.Source != nil && *product.Source == "kiranaclub_rewards" {
		return &shared.Badge{
			Text:    "",
			BgColor: []string{"#ffffff", "#ffffff"},
			Color:   "#ffffff",
		}
	}
	if (userAppVersion != nil && productsScreenChangeAppVersion != nil && topBadge != nil) && (userAppVersion.GreaterThan(productsScreenChangeAppVersion)) {
		return &shared.Badge{
			Text:    "स्टॉक ख़त्म",
			BgColor: []string{"#D9D9D9", "#D9D9D9"},
			Color:   "#ffffff",
		}
	}
	return &shared.Badge{
		Text:    "स्टॉक ख़त्म",
		Color:   "#F9F1FF",
		BgColor: []string{"#E43535", "#A50707"},
	}
}

func GetTopBadge(seller string, text *string, styles *dao.TopBadgeStyles) *shared.Badge {
	if text == nil {
		return nil
	}
	color := "#6f3b00"
	bgColor := []string{"#ffdbb2", "#ffdbb2"}

	if styles != nil {
		if styles.Color != "" {
			color = styles.Color
		}
		if len(styles.BgColor) > 0 {
			bgColor = styles.BgColor
		}
	}

	return &shared.Badge{
		Text:    *text,
		Color:   color,
		BgColor: bgColor,
	}
}

func buildTopBadge(product Product) *shared.Badge {
	var topBadgeText *string = nil
	var topBadgeStyles *dao.TopBadgeStyles

	// Check for badge text in metadata
	if product.MetaProperties.BadgeText != nil {
		topBadgeText = product.MetaProperties.BadgeText
	}

	// Get styles from extended metadata
	if product.ExtendedMetaProperties.TopBadgeStyles != nil {
		topBadgeStyles = product.ExtendedMetaProperties.TopBadgeStyles
	}

	return GetTopBadge(product.Seller, topBadgeText, topBadgeStyles)
}

// buildBottomInfoBadge creates bottom info badge
func buildBottomInfoBadge(product Product) *shared.Badge {
	return &shared.Badge{
		Text:    fmt.Sprintf("1 पैक = %d यूनिट", product.MetaProperties.PackSize),
		Color:   "#074E87",
		BgColor: []string{"#E3F6FF", "#E3F6FF"},
	}
}

func getDescriptionForProduct(p *Product, productArray *[]Product) *string {
	if p == nil {
		return nil
	}

	sizeVariants := p.SizeVariants
	if sizeVariants == nil && productArray != nil {
		// If size variants are not set, use the provided product array
		sizeVariants = make([]*Product, len(*productArray))
		for i := range *productArray {
			sizeVariants[i] = &(*productArray)[i] // Fix: avoid loop variable address issue
		}
	}

	if len(sizeVariants) == 0 {
		return nil // No size variants to generate description
	}

	var description strings.Builder
	sizeCount := 0

	for _, variant := range sizeVariants {
		if variant.IsActive == nil || !*variant.IsActive {
			continue
		}
		var productMetaData = variant.MetaProperties

		if sizeCount < utils.MAX_SIZES_TO_SHOW {
			description.WriteString(productMetaData.Quantity)
			description.WriteString(", ")
		}
		sizeCount++
	}

	if sizeCount <= 1 {
		return nil
	}

	// Build the final description
	finalDesc := fmt.Sprintf("उपलब्ध साइज - %s", strings.TrimSuffix(description.String(), ", "))
	if sizeCount > utils.MAX_SIZES_TO_SHOW {
		finalDesc += " आदि"
	}

	return &finalDesc
}

func calculateStockMetrics(displayQuantity *int, analysisList []*inventoryDao.KiranaBazarProductsInventoryAnalysisHistory) (
	piecesCount *float64, daysOfStock *float64, throughputPerDay float64, actualSalesDays *string, throughputUpdateDate *string) {
	if displayQuantity != nil {
		count := float64(*displayQuantity)
		piecesCount = &count
	}

	for _, analysis := range analysisList {
		if analysis != nil && analysis.AnalysisType == "LAST_7_DAYS_ANALYSIS" {
			throughputPerDay = math.Round(analysis.ThroughputPerDay)
			formattedDate := analysis.AnalysisDate.Format("02-01-2006")
			throughputUpdateDate = &formattedDate
			salesDayString := fmt.Sprintf("%d/%d days", analysis.ActualSalesDays, analysis.AnalysisPeriodDays)
			actualSalesDays = &salesDayString

			if throughputPerDay > 0 && piecesCount != nil {
				val := *piecesCount / throughputPerDay
				val = math.Round(val*100) / 100
				daysOfStock = &val
			}
			break // Assuming we only care about the first matching analysis
		}
	}

	if piecesCount != nil {
		count := math.Round(*piecesCount*100) / 100 // Round to 2 decimal places
		piecesCount = &count
	}

	return piecesCount, daysOfStock, throughputPerDay, actualSalesDays, throughputUpdateDate
}

func calculateSalesMetrics(analysisList []*inventoryDao.KiranaBazarProductsInventoryAnalysisHistory) []shared.SalesAnalysisData {
	salesAnalysis := make([]shared.SalesAnalysisData, 0)
	for _, analysis := range analysisList {
		if analysis != nil {
			salesAnalysis = append(salesAnalysis, shared.SalesAnalysisData{
				Days:               analysis.AnalysisPeriodDays,
				AverageSalesPerDay: math.Round(analysis.ThroughputPerDay), // Round to 2 decimal places
				TotalQuantity:      analysis.QuantitySold,
			})
		}
	}
	return salesAnalysis
}

// GetMarginPercentageString extracts the margin percentage from the input string.
// If not found, it formats the fallback float value to 2 decimal places with a '%' sign.
func GetMarginPercentageString(input string, fallback float64) string {
	parts := strings.Split(input, "मार्जिन:")
	if len(parts) > 1 {
		result := strings.TrimSpace(parts[1])
		if result != "" {
			return result
		}
	}
	// Fallback to formatting float value
	return fmt.Sprintf("%.1f%%", fallback)
}

func GetStringFromNumberUpto1Decimal(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return strings.TrimSpace(v)
	case float64:
		if v == math.Trunc(v) {
			return fmt.Sprintf("%.0f", v)
		} else {
			return fmt.Sprintf("%.1f", v)
		}
	case int:
		return fmt.Sprintf("%d", v)
	case int64:
		return fmt.Sprintf("%d", v)
	default:
		return fmt.Sprintf("%d", v)
	}
}
