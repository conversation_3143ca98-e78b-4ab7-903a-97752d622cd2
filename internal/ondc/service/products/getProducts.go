package products

import (
	inventoryDao "kc/internal/ondc/service/inventory/models/dao"
	"sort"
	"strconv"
	"strings"
)

// ProductFilters represents filtering options for products
type ProductFilters struct {
	MinRating *float64
	MaxRating *float64
	MinPrice  *float64
	MaxPrice  *float64
	IsActive  *bool
	IsOOS     *bool
	IsDefault *bool
	SortBy    string
	SortOrder string
}

// ProductStats represents product statistics
type ProductStats struct {
	TotalProducts     int
	ActiveProducts    int
	OutOfStockCount   int
	CategoryCounts    map[int]int
	AverageRating     float64
	RatedProductCount int
}

// SearchProducts searches products by name or code (thread-safe)
func SearchProducts(query string) []*Product {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	results := make([]*Product, 0)
	query = strings.ToLower(query)

	for _, product := range ps.productsByID {
		if product != nil && (strings.Contains(strings.ToLower(product.Name), query) ||
			strings.Contains(strings.ToLower(product.Code), query)) {
			results = append(results, ps.createLightweightCopy(product))
		}
	}
	return results
}

// GetProductsByIDs retrieves products by IDs (thread-safe)
func GetProductsByIDs(ids []uint) []*Product {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	results := make([]*Product, 0, len(ids))
	for _, id := range ids {
		if product, exists := ps.productsByID[id]; exists {
			results = append(results, ps.createLightweightCopy(product))
		}
	}
	return results
}

// GetProductsByCodes retrieves products by codes (thread-safe)
func GetProductsByCodes(codes []string) []*Product {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	results := make([]*Product, 0, len(codes))
	for _, code := range codes {
		if product, exists := ps.productsByCode[code]; exists {
			results = append(results, ps.createLightweightCopy(product))
		}
	}
	return results
}

// GetProductByCode retrieves a product by code (thread-safe)
func GetProductByCode(code string) *Product {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	if product, exists := ps.productsByCode[code]; exists {
		return ps.createLightweightCopy(product)
	}
	return nil
}

func GetProductInventory(productId int) (*[]*inventoryDao.KiranaBazarProductsInventoryAnalysisHistory, error) {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	productInventory, exists := ps.productsInventoryAnalysis[productId]
	if !exists {
		empty := []*inventoryDao.KiranaBazarProductsInventoryAnalysisHistory{}
		return &empty, nil
	}
	return &productInventory, nil
}

// GetProductsBySizeVariantCode retrieves products by size variant code (thread-safe)
func GetProductsBySizeVariantCode(code int) []*Product {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	results := make([]*Product, 0)
	for _, product := range ps.productsByID {
		if product != nil && product.SizeVariantCode != nil && int(*product.SizeVariantCode) == code {
			results = append(results, ps.createLightweightCopy(product))
		}
	}
	return results
}

// GetProductsByCategoryWithFilters retrieves products by category with filters (thread-safe)
func GetProductsByCategoryWithFilters(categoryID int, filters ProductFilters) []*Product {
	products := GetProductsByCategory(categoryID)
	return applyFilters(products, filters)
}

// GetTopRatedProducts retrieves top rated products (thread-safe)
func GetTopRatedProducts(limit int) []*Product {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	// Collect all products with ratings
	var ratedProducts []*Product
	for _, product := range ps.productsByID {
		if product != nil && product.RatingsCount > 0 {
			ratedProducts = append(ratedProducts, ps.createLightweightCopy(product))
		}
	}

	// Sort by rating
	sort.Slice(ratedProducts, func(i, j int) bool {
		ratingI := float64(ratedProducts[i].RatingsSum) / float64(ratedProducts[i].RatingsCount)
		ratingJ := float64(ratedProducts[j].RatingsSum) / float64(ratedProducts[j].RatingsCount)
		return ratingI > ratingJ
	})

	// Return top N
	if limit > 0 && limit < len(ratedProducts) {
		return ratedProducts[:limit]
	}
	return ratedProducts
}

// GetOutOfStockProducts retrieves out-of-stock products (thread-safe)
func GetOutOfStockProducts() []*Product {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	results := make([]*Product, 0)
	for _, product := range ps.productsByID {
		if product != nil && product.IsOOS != nil && *product.IsOOS {
			results = append(results, ps.createLightweightCopy(product))
		}
	}
	return results
}

// GetInStockProducts retrieves in-stock products (thread-safe)
func GetInStockProducts() []*Product {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	results := make([]*Product, 0)
	for _, product := range ps.productsByID {
		if product != nil && (product.IsOOS == nil || !*product.IsOOS) {
			results = append(results, ps.createLightweightCopy(product))
		}
	}
	return results
}

// GetProductVariants retrieves product variants by product code (thread-safe)
func GetProductVariants(productCode string) []*Product {
	product := GetProductByCode(productCode)
	if product == nil || product.SizeVariants == nil {
		return []*Product{}
	}
	return product.SizeVariants
}

// GetProductsWithFilters retrieves products with filters (thread-safe)
func GetProductsWithFilters(filters ProductFilters) []*Product {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	var products []*Product
	for _, product := range ps.productsByID {
		if product != nil {
			products = append(products, ps.createLightweightCopy(product))
		}
	}

	return applyFilters(products, filters)
}

// GetProductStatistics returns product statistics (thread-safe)
func GetProductStatistics() ProductStats {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	stats := ProductStats{
		CategoryCounts: make(map[int]int),
	}

	var totalRating float64
	var ratedCount int

	for _, product := range ps.productsByID {
		if product == nil {
			continue
		}

		stats.TotalProducts++

		if product.IsActive != nil && *product.IsActive {
			stats.ActiveProducts++
		}

		if product.IsOOS != nil && *product.IsOOS {
			stats.OutOfStockCount++
		}

		if product.CategoryID > 0 {
			stats.CategoryCounts[product.CategoryID]++
		}

		if product.RatingsCount > 0 {
			rating := float64(product.RatingsSum) / float64(product.RatingsCount)
			totalRating += rating
			ratedCount++
		}
	}

	if ratedCount > 0 {
		stats.AverageRating = totalRating / float64(ratedCount)
		stats.RatedProductCount = ratedCount
	}

	return stats
}

// GetProductByID retrieves a product by ID (thread-safe)
func GetProductByID(id interface{}) (*Product, bool) {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	var productID uint
	switch v := id.(type) {
	case uint:
		productID = v
	case int:
		productID = uint(v)
	case int64:
		productID = uint(v)
	case string:
		// Try to parse as uint
		if parsed, err := strconv.ParseUint(v, 10, 32); err == nil {
			productID = uint(parsed)
		} else {
			return nil, false
		}
	default:
		return nil, false
	}

	if product, exists := ps.productsByID[productID]; exists {
		return ps.createLightweightCopy(product), true
	}
	return nil, false
}

// GetProductsByCategory retrieves products by category (thread-safe)
func GetProductsByCategory(categoryID int) []*Product {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	if products, exists := ps.productsByCategory[categoryID]; exists {
		return ps.createLightweightCopies(products)
	}
	return []*Product{}
}

// GetActiveProducts retrieves all active products (thread-safe)
func GetActiveProducts() []*Product {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	results := make([]*Product, 0)
	for _, product := range ps.productsByID {
		if product != nil && (product.IsActive == nil || *product.IsActive) {
			results = append(results, ps.createLightweightCopy(product))
		}
	}
	return results
}

// GetProductsBySource retrieves products by source (thread-safe)
func GetProductsBySource(source string) []*Product {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	results := make([]*Product, 0)
	for _, product := range ps.productsByID {
		if product != nil && product.Source != nil && *product.Source == source {
			results = append(results, ps.createLightweightCopy(product))
		}
	}
	return results
}

// GetCategoryByID retrieves a category by ID (thread-safe)
func GetCategoryByID(id interface{}) (*Category, bool) {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	var categoryID int
	switch v := id.(type) {
	case int:
		categoryID = v
	case uint:
		categoryID = int(v)
	case int64:
		categoryID = int(v)
	case string:
		// Try to parse as int
		if parsed, err := strconv.Atoi(v); err == nil {
			categoryID = parsed
		} else {
			return nil, false
		}
	default:
		return nil, false
	}

	if category, exists := ps.categories[categoryID]; exists {
		return category, true
	}
	return nil, false
}

// applyFilters applies filters to a slice of products
func applyFilters(products []*Product, filters ProductFilters) []*Product {
	if filters == (ProductFilters{}) {
		return products
	}

	filtered := make([]*Product, 0)
	for _, product := range products {
		if productMatchesFilters(product, filters) {
			filtered = append(filtered, product)
		}
	}

	// Apply sorting
	if filters.SortBy != "" {
		sort.Slice(filtered, func(i, j int) bool {
			pi, pj := filtered[i], filtered[j]

			switch filters.SortBy {
			case "rank":
				if pi.Rank == nil {
					return false
				}
				if pj.Rank == nil {
					return true
				}
				return compareByOrder(float64(*pi.Rank), float64(*pj.Rank), filters.SortOrder)
			case "rating":
				ratingI := float64(pi.RatingsSum) / float64(pi.RatingsCount)
				ratingJ := float64(pj.RatingsSum) / float64(pj.RatingsCount)
				return compareByOrder(ratingI, ratingJ, filters.SortOrder)
			case "popularity":
				return compareByOrder(pi.PopularityValue, pj.PopularityValue, filters.SortOrder)
			}
			return false
		})
	}

	return filtered
}

// productMatchesFilters checks if a product matches the given filters
func productMatchesFilters(p *Product, f ProductFilters) bool {
	if p == nil {
		return false
	}

	// Rating filters
	if f.MinRating != nil {
		if p.RatingsCount == 0 {
			return false
		}
		rating := float64(p.RatingsSum) / float64(p.RatingsCount)
		if rating < *f.MinRating {
			return false
		}
	}

	if f.MaxRating != nil {
		if p.RatingsCount == 0 {
			return false
		}
		rating := float64(p.RatingsSum) / float64(p.RatingsCount)
		if rating > *f.MaxRating {
			return false
		}
	}

	// Status filters
	if f.IsActive != nil && p.IsActive != nil && *p.IsActive != *f.IsActive {
		return false
	}

	if f.IsOOS != nil && p.IsOOS != nil && *p.IsOOS != *f.IsOOS {
		return false
	}

	if f.IsDefault != nil && p.IsDefault != nil && *p.IsDefault != *f.IsDefault {
		return false
	}

	return true
}

// --- ProductCache Utility Methods ---
func compareByOrder(a, b float64, order string) bool {
	if order == "desc" {
		return a > b
	}
	return a < b
}
