package products

import (
	"context"
	"encoding/json"
	"fmt"
	"gorm.io/datatypes"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/brands"
	inventoryDao "kc/internal/ondc/service/inventory/models/dao"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

// Config represents cache configuration
type Config struct {
	RefreshInterval time.Duration `default:"5m"`
	MaxRetries      int           `default:"3"`
	RetryDelay      time.Duration `default:"1s"`
	Timeout         time.Duration `default:"30s"`
}

// ProductService is a thread-safe singleton for product operations
type ProductService struct {
	mu sync.RWMutex

	// Core data maps - protected by RWMutex for high QPS
	productsByID       map[uint]*Product
	productsByCode     map[string]*Product
	productsByCategory map[int][]*Product
	categories         map[int]*Category

	entityProductMappings     map[int][]int // map of all product IDs for each category
	productEntityMappings     map[int][]int // map of all category mappings for product
	productsInventoryAnalysis map[int][]*inventoryDao.KiranaBazarProductsInventoryAnalysisHistory

	pricingRules           map[uint]ProductPricingRule        // rule_id -> rule
	productPricingMappings map[uint][]uint                    // product_id -> []rule_ids
	pricingConditions      map[uint][]ProductPricingCondition // rule_id -> conditions
	pricingTiers           map[uint][]ProductPricingTier      // rule_id -> tiers

	// Metadata
	lastUpdate  time.Time
	initialized int32

	// Dependencies
	db     *sqlRepo.Repository
	config *Config

	// Background refresh
	stopCh chan struct{}
	wg     sync.WaitGroup
}

// Global singleton instance
var (
	instance *ProductService
	once     sync.Once
)

// GetInstance returns the singleton instance of ProductService
func GetInstance() *ProductService {
	once.Do(func() {
		instance = &ProductService{
			productsByID:       make(map[uint]*Product),
			productsByCode:     make(map[string]*Product),
			productsByCategory: make(map[int][]*Product),
			categories:         make(map[int]*Category),
			stopCh:             make(chan struct{}),
		}
	})
	return instance
}

// Initialize sets up the service with database connection and starts background refresh
func (ps *ProductService) Initialize(db *sqlRepo.Repository, config *Config) error {
	if db == nil {
		return fmt.Errorf("repository cannot be nil")
	}

	// Check atomic first, then acquire mutex only if needed
	if atomic.LoadInt32(&ps.initialized) == 1 {
		return nil // Already initialized
	}

	ps.mu.Lock()
	if atomic.LoadInt32(&ps.initialized) == 1 {
		ps.mu.Unlock()
		return nil // Already initialized
	}

	ps.db = db
	if config == nil {
		config = &Config{
			RefreshInterval: 5 * time.Minute,
			MaxRetries:      3,
			RetryDelay:      time.Second,
			Timeout:         30 * time.Second,
		}
	}
	ps.config = config

	// Release the lock before calling refresh to avoid deadlock
	ps.mu.Unlock()

	// Initial load
	ctx, cancel := context.WithTimeout(context.Background(), config.Timeout)
	defer cancel()

	if err := ps.refresh(ctx); err != nil {
		return fmt.Errorf("failed initial load: %w", err)
	}

	// Start background refresh
	ps.wg.Add(1)
	go ps.backgroundRefresh()

	atomic.StoreInt32(&ps.initialized, 1)
	return nil
}

// backgroundRefresh runs periodic cache refresh
func (ps *ProductService) backgroundRefresh() {
	defer ps.wg.Done()

	ticker := time.NewTicker(ps.config.RefreshInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ctx, cancel := context.WithTimeout(context.Background(), ps.config.Timeout)
			if err := ps.refresh(ctx); err != nil {
				fmt.Printf("Background refresh failed: %v\n", err)
			}
			cancel()

		case <-ps.stopCh:
			return
		}
	}
}

// Stop gracefully shuts down the service
func (ps *ProductService) Stop(ctx context.Context) error {
	close(ps.stopCh)

	done := make(chan struct{})
	go func() {
		ps.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// refresh rebuilds the cache data
func (ps *ProductService) refresh(ctx context.Context) error {
	start := time.Now()
	fmt.Println("Starting product cache refresh")

	// Fetch all data
	products, categories, entityMappings, inventoryAnalysis, pricingRules, productPricingMappings, pricingConditions, pricingTiers, err := ps.fetchDataFromDB()
	if err != nil {
		return fmt.Errorf("failed to fetch data: %w", err)
	}

	// Build new maps
	newProductsByID := make(map[uint]*Product)
	newProductsByCode := make(map[string]*Product)
	newProductsByCategory := make(map[int][]*Product)
	newCategories := make(map[int]*Category)
	newEntityProductMappings := make(map[int][]int)
	newProductEntityMappings := make(map[int][]int)
	newProductsInventoryAnalysis := make(map[int][]*inventoryDao.KiranaBazarProductsInventoryAnalysisHistory)

	// Process products
	for _, product := range products {
		if product == nil {
			continue
		}

		// Map by ID
		newProductsByID[product.ID] = product

		// Map by code
		if product.Code != "" {
			newProductsByCode[product.Code] = product
		}

		// Map by category
		if product.CategoryID != 0 {
			newProductsByCategory[product.CategoryID] = append(newProductsByCategory[product.CategoryID], product)
		}
	}

	// Process categories
	for _, category := range categories {
		if category != nil {
			newCategories[category.ID] = category
		}
	}

	// Process entity mappings
	newProductEntityMappings, newEntityProductMappings = ps.mapEntityProductMappings(entityMappings)

	// Process inventory analysis
	for _, analysis := range inventoryAnalysis {
		if analysis != nil {
			productID := int(analysis.ProductID)
			newProductsInventoryAnalysis[productID] = append(newProductsInventoryAnalysis[productID], analysis)
		}
	}

	// Sort products by category
	for _, products := range newProductsByCategory {
		sort.Slice(products, func(i, j int) bool {
			pi, pj := products[i], products[j]

			// Sort by OOS status first
			if pi.IsOOS != nil && pj.IsOOS != nil {
				if *pi.IsOOS != *pj.IsOOS {
					return !*pi.IsOOS // Non-OOS first
				}
			}

			// Then by rank
			if pi.Rank != nil && pj.Rank != nil {
				return *pi.Rank < *pj.Rank
			}

			return false
		})
	}

	// Atomic swap of data
	// TODO:
	ps.mu.Lock()
	ps.productsByID = newProductsByID
	ps.productsByCode = newProductsByCode
	ps.productsByCategory = newProductsByCategory
	ps.categories = newCategories
	ps.entityProductMappings = newEntityProductMappings
	ps.productEntityMappings = newProductEntityMappings
	ps.productsInventoryAnalysis = newProductsInventoryAnalysis
	ps.pricingRules = pricingRules
	ps.productPricingMappings = productPricingMappings
	ps.pricingConditions = pricingConditions
	ps.pricingTiers = pricingTiers
	ps.lastUpdate = time.Now()
	ps.mu.Unlock()

	fmt.Printf("Product cache refresh completed in %v\n", time.Since(start))
	return nil
}

// fetchDataFromDB retrieves all product, category, entity mapping, inventory analysis, and pricing data
func (ps *ProductService) fetchDataFromDB() ([]*Product, []*Category, []*EntityMapping, []*inventoryDao.KiranaBazarProductsInventoryAnalysisHistory,
	map[uint]ProductPricingRule, map[uint][]uint, map[uint][]ProductPricingCondition,
	map[uint][]ProductPricingTier, error) {

	var productEntityMappings []*EntityMapping
	productCategoriesMappingsQuery := `select * from kiranaclubdb.kiranabazar_entities_mapping kcm 
         where ` + "`type`" + ` = "product" and is_active = true`
	_, err := ps.db.CustomQuery(&productEntityMappings, productCategoriesMappingsQuery)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, nil, fmt.Errorf("error fetching product-category mappings: %v", err)
	}

	// Fetch categories
	categoriesQuery := `
        SELECT 
            id, domain, sub_domain, category, code, 
            image_url, is_active, created_at, updated_at, source
        FROM 
            kiranabazar_categories`

	var categories []*Category
	_, err = ps.db.CustomQuery(&categories, categoriesQuery)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, nil, fmt.Errorf("error fetching categories: %v", err)
	}

	// Fetch products (existing query)
	productsQuery := `
		SELECT 
			p.id, 
			p.category_id, 
			p.name, 
			p.code, 
			p.` + "`rank`" + `, 
			p.created_at, 
			p.updated_at,
			p.meta, 
			p.extended_meta,
			p.gtv_calculation_rate,
			p.is_active, 
			p.image_urls, 
			p.media_urls,
			p.name_label, 
			p.code_label,
			p.ratings_sum, 
			p.ratings_count, 
			p.size_variant_code, 
			p.is_default, 
			p.is_oos, 
			p.popularity_value,
			p.seller,
			i.inventory_quantity,
			i.display_quantity,
			i.low_stock_threshold,
			p.manufacturer,
			p.product_type
		FROM 
			kiranabazar_products p
		LEFT JOIN 
			kiranabazar_products_inventory i ON p.id = i.id;
    `

	var products []*Product
	_, err = ps.db.CustomQuery(&products, productsQuery)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, nil, fmt.Errorf("error fetching products: %v", err)
	}

	// Fetch inventory analysis (existing query)
	inventoryAnalysisQuery := `
        WITH ranked_analysis AS (
            SELECT 
                *, 
                ROW_NUMBER() OVER (
                PARTITION BY product_id, 
                analysis_type 
                ORDER BY 
                    analysis_date DESC, 
                    updated_at DESC
                ) AS rn 
            FROM 
                kiranabazar_product_inventory_analysis_history 
            WHERE 
                analysis_type IN ( '` +
		strings.Join(DEFAULT_SALES_ANALYSIS, "', '") +
		`') 
                AND analysis_date = CURDATE()
        ) 
        SELECT 
        * 
        FROM 
            ranked_analysis 
        WHERE 
            rn = 1;
    `
	var inventoryAnalysis []*inventoryDao.KiranaBazarProductsInventoryAnalysisHistory
	_, err = ps.db.CustomQuery(&inventoryAnalysis, inventoryAnalysisQuery)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, nil, fmt.Errorf("error fetching inventory analysis: %v", err)
	}

	// Fetch pricing data in 3 optimized queries
	pricingRules, productPricingMappings, pricingConditions, pricingTiers, err := ps.fetchPricingData()
	if err != nil {
		return nil, nil, nil, nil, nil, nil, nil, nil, fmt.Errorf("error fetching pricing data: %v", err)
	}

	// Process existing data
	for i, product := range products {
		if product.IsOOS == nil || !*product.IsOOS {
			products[i].IsOOS = nil
		}
	}
	ps.mapProductRawJson(products)
	ps.mapSizeVariant(products)
	ps.mapVirtualSkus(products)

	return products, categories, productEntityMappings, inventoryAnalysis, pricingRules, productPricingMappings, pricingConditions, pricingTiers, nil
}

// mapEntityProductMappings maps entity product mappings
func (ps *ProductService) mapEntityProductMappings(mappings []*EntityMapping) (map[int][]int, map[int][]int) {
	// Create a map to hold the product ID to category ID mappings
	productEntityMap := make(map[int][]int)
	entityProductMap := make(map[int][]int)

	for _, mapping := range mappings {
		if mapping != nil && mapping.EntityId != 0 && mapping.TargetId != "" {
			// Parse the product ID from the target ID
			productID, err := parseID(mapping.TargetId)
			if err != nil {
				continue // Skip invalid IDs
			}
			intProductId := int(productID)
			intEntityId := int(mapping.EntityId)
			// Add the mapping to the productEntityMap
			if len(productEntityMap[intProductId]) == 0 {
				productEntityMap[intProductId] = make([]int, 0)
			}
			if len(entityProductMap[intEntityId]) == 0 {
				entityProductMap[intEntityId] = make([]int, 0)
			}

			productEntityMap[intProductId] = append(productEntityMap[intProductId], intEntityId)
			entityProductMap[intEntityId] = append(entityProductMap[intEntityId], intProductId)
		} else {
			fmt.Printf("Invalid mapping found: %+v\n", mapping)
		}
	}
	return productEntityMap, entityProductMap
}

// fetchPricingData fetches pricing data and returns the maps
func (ps *ProductService) fetchPricingData() (map[uint]ProductPricingRule, map[uint][]uint, map[uint][]ProductPricingCondition, map[uint][]ProductPricingTier, error) {
	// Step 1: Fetch pricing rules with conditions
	pricingRulesWithConditionsQuery := `
        SELECT 
            pr.name as rule_name, pr.rule_type, pr.priority,
            pr.is_active, pr.valid_from, pr.valid_until, pr.brand_share,
            pc.id as condition_id, pc.pricing_rule_id as rule_id,
            pc.condition_type, pc.operator, pc.condition_value, 
            pc.logical_operator, pc.condition_order
        FROM 
            kiranabazar_products_pricing_rules pr
        LEFT JOIN 
            kiranabazar_products_pricing_conditions pc ON pr.id = pc.pricing_rule_id
        WHERE 
            pr.is_active = true
            AND (pr.valid_from IS NULL OR pr.valid_from <= UNIX_TIMESTAMP()*1000)
            AND (pr.valid_until IS NULL OR pr.valid_until >= UNIX_TIMESTAMP()*1000)
        ORDER BY 
            pr.id, pc.condition_order
    `

	var rulesWithConditions []PricingRuleWithCondition
	_, err := ps.db.CustomQuery(&rulesWithConditions, pricingRulesWithConditionsQuery)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("error fetching pricing rules with conditions: %v", err)
	}

	// Step 2: Fetch pricing rules with tiers
	pricingRulesWithTiersQuery := `
        SELECT 
            pr.name as rule_name, pr.rule_type, pr.priority,
            pr.is_active, pr.valid_from, pr.valid_until, pr.brand_share,
            pt.id as tier_id, pt.pricing_rule_id as rule_id,
            pt.tier_name, pt.min_quantity, pt.max_quantity, 
            pt.price_type, pt.price_value
        FROM 
            kiranabazar_products_pricing_rules pr
        LEFT JOIN 
            kiranabazar_products_pricing_tiers pt ON pr.id = pt.pricing_rule_id
        WHERE 
            pr.is_active = true
            AND (pr.valid_from IS NULL OR pr.valid_from <= UNIX_TIMESTAMP()*1000)
            AND (pr.valid_until IS NULL OR pr.valid_until >= UNIX_TIMESTAMP()*1000)
        ORDER BY 
            pr.id, pt.tier_order
    `

	var rulesWithTiers []PricingRuleWithTier
	_, err = ps.db.CustomQuery(&rulesWithTiers, pricingRulesWithTiersQuery)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("error fetching pricing rules with tiers: %v", err)
	}

	// Process results to build rule_id: {conditions, tiers} structure
	rulesMap := make(map[uint]ProductPricingRule)
	conditionsMap := make(map[uint][]ProductPricingCondition)
	tiersMap := make(map[uint][]ProductPricingTier)

	// Process rules with conditions
	for _, rwc := range rulesWithConditions {
		if rwc.ConditionID != 0 {
			condition := ProductPricingCondition{
				ID:             rwc.ConditionID,
				PricingRuleID:  rwc.RuleID,
				ConditionType:  PricingConditionType(rwc.ConditionType),
				Operator:       PricingConditionOperator(rwc.Operator),
				ConditionValue: rwc.ConditionValue,
			}

			if rwc.LogicalOperator != nil {
				condition.LogicalOperator = LogicalOperator(*rwc.LogicalOperator)
			}
			if rwc.ConditionOrder != nil {
				condition.ConditionOrder = *rwc.ConditionOrder
			}

			conditionsMap[rwc.RuleID] = append(conditionsMap[rwc.RuleID], condition)

			// Add condition if exists
			if _, exists := rulesMap[rwc.RuleID]; !exists {
				rulesMap[rwc.RuleID] = ProductPricingRule{
					ID:         rwc.RuleID,
					Name:       rwc.RuleName,
					Priority:   rwc.Priority,
					RuleType:   rwc.RuleType,
					IsActive:   rwc.IsActive,
					ValidFrom:  rwc.ValidFrom,
					ValidUntil: rwc.ValidUntil,
					Conditions: conditionsMap[rwc.RuleID],
					BrandShare: rwc.BrandShare,
				}
			} else {
				rule := rulesMap[rwc.RuleID]
				rule.Conditions = conditionsMap[rwc.RuleID]
				rulesMap[rwc.RuleID] = rule
			}
		}
	}

	// Process rules with tiers
	for _, rwt := range rulesWithTiers {
		// Add tier if exists
		if rwt.TierID != 0 {
			tier := ProductPricingTier{
				ID:            rwt.TierID,
				PricingRuleID: rwt.RuleID,
				TierName:      rwt.TierName,
				MinQuantity:   rwt.MinQuantity,
				PriceType:     PriceType(rwt.PriceType),
				PriceValue:    rwt.PriceValue,
				MaxQuantity:   rwt.MaxQuantity,
			}

			tiersMap[rwt.RuleID] = append(tiersMap[rwt.RuleID], tier)

			// Add condition if exists
			if _, exists := rulesMap[rwt.RuleID]; !exists {
				rulesMap[rwt.RuleID] = ProductPricingRule{
					ID:         rwt.RuleID,
					Name:       rwt.RuleName,
					Priority:   rwt.Priority,
					RuleType:   rwt.RuleType,
					IsActive:   rwt.IsActive,
					ValidFrom:  rwt.ValidFrom,
					ValidUntil: rwt.ValidUntil,
					Tiers:      tiersMap[rwt.RuleID],
					BrandShare: rwt.BrandShare,
				}
			} else {
				// If rule already exists, append tier to existing rule
				rule := rulesMap[rwt.RuleID]
				rule.Tiers = tiersMap[rwt.RuleID]
				rulesMap[rwt.RuleID] = rule
			}
		}
	}

	// Step 3: Fetch product-pricing mappings
	mappingsQuery := `
        SELECT 
            product_id, pricing_rule_id
        FROM 
            kiranabazar_products_pricing_mapping
        WHERE 
            is_active = true
    `

	type productPricingMapping struct {
		ProductID     uint `json:"product_id"`
		PricingRuleID uint `json:"pricing_rule_id"`
	}

	var mappings []productPricingMapping
	_, err = ps.db.CustomQuery(&mappings, mappingsQuery)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("error fetching pricing mappings: %v", err)
	}

	// Build product mappings
	rulesProductMappings := make(map[uint][]uint)
	for _, mapping := range mappings {
		rulesProductMappings[mapping.ProductID] = append(rulesProductMappings[mapping.ProductID], mapping.PricingRuleID)
	}

	// Sort conditions and tiers for each rule
	for ruleID := range conditionsMap {
		sort.Slice(conditionsMap[ruleID], func(i, j int) bool {
			return conditionsMap[ruleID][i].ConditionOrder < conditionsMap[ruleID][j].ConditionOrder
		})
	}

	for ruleID := range tiersMap {
		sort.Slice(tiersMap[ruleID], func(i, j int) bool {
			return tiersMap[ruleID][i].MinQuantity < tiersMap[ruleID][j].MinQuantity
		})
	}

	return rulesMap, rulesProductMappings, conditionsMap, tiersMap, nil
}

// parseProductJSON parses JSON fields in product
func (ps *ProductService) parseProductJSON(product *Product) {
	// Parse meta
	var productMetaData shared.KiranaBazarProductMeta
	var productImageUrls []string
	var productMediaUrls []dao.KiranaBazarProductMediaUrl
	var productExtendedMeta dao.KiranaBazarProductExtendedMeta

	if err := json.Unmarshal(product.Meta, &productMetaData); err == nil {
		product.MetaProperties = productMetaData
	}

	if err := json.Unmarshal(product.ImageURLs, &productImageUrls); err == nil {
		product.ProductImageURLs = productImageUrls
	}

	if err := json.Unmarshal(product.MediaUrls, &productMediaUrls); err == nil {
		product.ProductMediaUrls = productMediaUrls
	}

	if err := json.Unmarshal(product.ExtendedMeta, &productExtendedMeta); err == nil {
		product.ExtendedMetaProperties = productExtendedMeta
	}
}

// mapProductRawJson maps product raw JSON
func (ps *ProductService) mapProductRawJson(products []*Product) {
	for _, product := range products {
		ps.parseProductJSON(product)
	}
}

// mapVirtualSkus maps virtual SKUs to products
func (ps *ProductService) mapVirtualSkus(products []*Product) {
	// Create a map to group products by their virtual SKU
	productVirtualSkuMap := make(map[string][]*shared.KiranaBazarVirtualSkuMapping)
	virtualSkuIds := make([]string, 0)
	PRODUCT_TYPE_VIRTUAL := string(ProductTypeVirtual)
	for _, product := range products {
		if product.ProductType == PRODUCT_TYPE_VIRTUAL {
			virtualSkuId := product.ID
			virtualSkuIds = append(virtualSkuIds, fmt.Sprintf("'%d'", virtualSkuId))
		}
	}
	if len(virtualSkuIds) == 0 {
		return // No virtual SKUs to map
	}
	virtualSkuQuery := fmt.Sprintf(`
		SELECT kvsm.id, kvsm.virtual_sku_id, kvsm.product_id, kvsm.quantity, kvsm.wholesale_rate, kvsm.is_active, kvsm.created_at, kvsm.updated_at, kvsm.updated_by
        FROM kiranaclubdb.kiranabazar_virtual_sku_mapping kvsm where kvsm.virtual_sku_id IN (%s) and kvsm.is_active = true
	`, strings.Join(virtualSkuIds, ","))
	var virtualSkuMappings []*shared.KiranaBazarVirtualSkuMapping
	_, err := ps.db.CustomQuery(&virtualSkuMappings, virtualSkuQuery)
	if err != nil {
		fmt.Printf("Error fetching virtual SKU mappings: %v\n", err)
		return
	}

	// Group virtual SKUs by their product ID
	for _, mapping := range virtualSkuMappings {
		if mapping != nil && mapping.VirtualSkuId != 0 {
			productVirtualSkuMap[fmt.Sprint(mapping.VirtualSkuId)] = append(productVirtualSkuMap[fmt.Sprint(mapping.VirtualSkuId)], mapping)
		}
	}

	// Assign the virtual SKUs to each product
	for _, product := range products {
		virtualSkus, exists := productVirtualSkuMap[fmt.Sprint(product.ID)]
		if exists && len(virtualSkus) > 0 {
			product.OriginalSkus = virtualSkus
		}
	}
}

// mapSizeVariant maps size variant to product
func (ps *ProductService) mapSizeVariant(products []*Product) {
	// Create a map to group products by their SizeVariantCode
	productVariantMap := make(map[uint][]*Product)

	for _, product := range products {
		productSource, exists := brands.GetSourceBySeller(product.Seller)
		if !exists {
			//fmt.Println("source not found for seller: %s get products v2 -- localfdsfsdfdsf", product.Seller)
			//slack.SendSlackMessage(fmt.Sprintf("source not found for seller: %s get products v2 -- local", product.Seller))
			//continue
		}
		product.Source = &productSource
		if product.SizeVariantCode != nil {
			variantCode := uint(*product.SizeVariantCode)
			productVariantMap[variantCode] = append(productVariantMap[variantCode], product)
		}
	}

	// Assign the appropriate size variants to each product and sort them
	for i, product := range products {
		if product.SizeVariantCode == nil {
			continue
		}

		variantCode := uint(*product.SizeVariantCode)
		if variants, exists := productVariantMap[variantCode]; exists && len(variants) > 0 {
			// Use the already grouped variants directly instead of adding the product again
			// This prevents duplicates
			products[i].SizeVariants = variants

			// Sort the variants by rank
			sort.Slice(products[i].SizeVariants, func(i2, j int) bool {
				variantI := products[i].SizeVariants[i2]
				variantJ := products[i].SizeVariants[j]

				// Check if either variant is out of stock
				isOOSI := variantI.IsOOS != nil && *variantI.IsOOS
				isOOSJ := variantJ.IsOOS != nil && *variantJ.IsOOS

				// If OOS status differs, non-OOS comes first
				if isOOSI != isOOSJ {
					return !isOOSI // Non-OOS (false) comes before OOS (true)
				}

				// Both have same OOS status, compare by rank
				// Handle nil ranks safely
				if variantI.Rank == nil {
					return false // Nil rank goes to the end
				}
				if variantJ.Rank == nil {
					return true // Nil rank goes to the end
				}

				// Both have ranks, lower rank first
				return *variantI.Rank < *variantJ.Rank
			})
		}
	}
}

// createLightweightCopy creates a lightweight copy of a product
func (ps *ProductService) createLightweightCopy(p *Product) *Product {
	if p == nil {
		return nil
	}

	// Create a shallow copy to avoid modifying the original
	copy := &Product{
		ID:                 p.ID,
		CategoryID:         p.CategoryID,
		Name:               p.Name,
		Code:               p.Code,
		RatingsSum:         p.RatingsSum,
		RatingsCount:       p.RatingsCount,
		PopularityValue:    p.PopularityValue,
		Seller:             p.Seller,
		Manufacturer:       p.Manufacturer,
		Rank:               p.Rank,
		SizeVariantCode:    p.SizeVariantCode,
		NameLabel:          p.NameLabel,
		CodeLabel:          p.CodeLabel,
		ProductType:        p.ProductType,
		ImageURLs:          p.ImageURLs,
		MediaUrls:          p.MediaUrls,
		ProductImageURLs:   p.ProductImageURLs,
		ProductMediaUrls:   p.ProductMediaUrls,
		GTVCalculationRate: p.GTVCalculationRate,

		InventoryQuantity: p.InventoryQuantity,
		LowStockThreshold: p.LowStockThreshold,
		DisplayQuantity:   p.DisplayQuantity,
		InventoryAnalysis: p.InventoryAnalysis,
		ProductEntities:   p.ProductEntities,
		OriginalSkus:      p.OriginalSkus,
	}

	// Copy pointer fields
	if p.Category != nil {
		rankCategory := *p.Category
		copy.Category = &rankCategory
	}
	if p.Source != nil {
		codeSource := *p.Source
		copy.Source = &codeSource
	}
	if p.IsDefault != nil {
		defaultCopy := *p.IsDefault
		copy.IsDefault = &defaultCopy
	}
	if p.IsOOS != nil {
		oosCopy := *p.IsOOS
		copy.IsOOS = &oosCopy
	}
	if p.IsActive != nil {
		activeCopy := *p.IsActive
		copy.IsActive = &activeCopy
	}

	copy.Meta = json.RawMessage(deepCopyJSON(datatypes.JSON(p.Meta)))
	copy.ExtendedMeta = json.RawMessage(deepCopyJSON(datatypes.JSON(p.ExtendedMeta)))
	ps.parseProductJSON(copy)

	// Copy size variants
	if p.SizeVariants != nil {
		copy.SizeVariants = make([]*Product, len(p.SizeVariants))
		for i, variant := range p.SizeVariants {
			// Avoid recursive copying to prevent stack overflow
			if variant != nil {
				variantCopy := &Product{
					ID:                 variant.ID,
					CategoryID:         variant.CategoryID,
					Name:               variant.Name,
					Code:               variant.Code,
					RatingsSum:         variant.RatingsSum,
					RatingsCount:       variant.RatingsCount,
					PopularityValue:    variant.PopularityValue,
					Seller:             variant.Seller,
					Manufacturer:       variant.Manufacturer,
					Rank:               variant.Rank,
					SizeVariantCode:    variant.SizeVariantCode,
					NameLabel:          variant.NameLabel,
					CodeLabel:          variant.CodeLabel,
					ProductType:        variant.ProductType,
					ImageURLs:          variant.ImageURLs,
					MediaUrls:          variant.MediaUrls,
					ProductImageURLs:   variant.ProductImageURLs,
					ProductMediaUrls:   variant.ProductMediaUrls,
					GTVCalculationRate: variant.GTVCalculationRate,

					InventoryQuantity: variant.InventoryQuantity,
					LowStockThreshold: variant.LowStockThreshold,
					DisplayQuantity:   variant.DisplayQuantity,
					InventoryAnalysis: variant.InventoryAnalysis,
					ProductEntities:   variant.ProductEntities,
					OriginalSkus:      variant.OriginalSkus,
				}

				// Copy pointer fields
				if variant.Category != nil {
					rankCategory := *variant.Category
					variantCopy.Category = &rankCategory
				}
				if variant.Source != nil {
					codeSource := *variant.Source
					variantCopy.Source = &codeSource
				}
				if variant.IsDefault != nil {
					defaultCopy := *variant.IsDefault
					variantCopy.IsDefault = &defaultCopy
				}
				if variant.IsOOS != nil {
					oosCopy := *variant.IsOOS
					variantCopy.IsOOS = &oosCopy
				}
				if variant.IsActive != nil {
					activeCopy := *variant.IsActive
					variantCopy.IsActive = &activeCopy
				}

				variantCopy.Meta = json.RawMessage(deepCopyJSON(datatypes.JSON(variant.Meta)))
				variantCopy.ExtendedMeta = json.RawMessage(deepCopyJSON(datatypes.JSON(variant.ExtendedMeta)))
				ps.parseProductJSON(variantCopy)

				variantCopy.SizeVariants = nil
				copy.SizeVariants[i] = variantCopy
			}
		}
	}
	return copy
}

// GetProduct retrieves a product by ID (thread-safe)
func GetProduct(id uint) (*Product, bool) {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	if product, exists := ps.productsByID[id]; exists {
		return ps.createLightweightCopy(product), true
	}
	return nil, false
}

// GetCategory retrieves a category by ID (thread-safe)
func GetCategory(id int) (*Category, bool) {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	if category, exists := ps.categories[id]; exists {
		return category, true
	}
	return nil, false
}

// GetLastUpdate returns the last update time (thread-safe)
func GetLastUpdate() time.Time {
	ps := GetInstance()
	ps.mu.RLock()
	defer ps.mu.RUnlock()
	return ps.lastUpdate
}

// createLightweightCopies creates lightweight copies of products slice
func (ps *ProductService) createLightweightCopies(products []*Product) []*Product {
	if products == nil {
		return nil
	}

	result := make([]*Product, len(products))
	for i, product := range products {
		result[i] = ps.createLightweightCopy(product)
	}
	return result
}

// getProductById gets a product by ID from database
func (ps *ProductService) getProductById(productId int) (*Product, error) {
	var product Product
	query := fmt.Sprintf("select * from kiranaclubdb.kiranabazar_products where id=%d", productId)
	_, err := ps.db.CustomQuery(&product, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query product: %v", err)
	}
	return &product, nil
}

// GetSizeVariantsByCode gets all size variants ordered by rank
func (ps *ProductService) GetSizeVariantsByCode(sizeVariantCode int) ([]Product, error) {
	var sizeVariants []Product
	query := fmt.Sprintf("select * from kiranaclubdb.kiranabazar_products kp where size_variant_code=%d order by kp.rank", sizeVariantCode)
	_, err := ps.db.CustomQuery(&sizeVariants, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query size variants: %v", err)
	}
	return sizeVariants, nil
}

// updateSizeVariantCodeForAll updates size variant code for all variants
func (ps *ProductService) updateSizeVariantCodeForAll(variants []Product, newSizeVariantCode int) error {
	if len(variants) == 0 {
		return nil
	}

	updateProductIds := make([]string, 0, len(variants))
	for _, variant := range variants {
		updateProductIds = append(updateProductIds, fmt.Sprintf("%d", variant.ID))
	}

	query := fmt.Sprintf("update kiranaclubdb.kiranabazar_products set size_variant_code=%d where id IN (%s)",
		newSizeVariantCode, strings.Join(updateProductIds, ","))
	_, err := ps.db.CustomQuery(nil, query)
	if err != nil {
		return fmt.Errorf("failed to update size variant code: %v", err)
	}
	return nil
}

// updateProductDefaultStatus updates default status of a product
func (ps *ProductService) updateProductDefaultStatus(productId int, isDefault bool) error {
	query := fmt.Sprintf("update kiranaclubdb.kiranabazar_products set is_default=%t where id=%d", isDefault, productId)
	_, err := ps.db.CustomQuery(nil, query)
	if err != nil {
		return fmt.Errorf("failed to update product default status: %v", err)
	}
	return nil
}

// switchDefaultProduct switches default product between two products
func (ps *ProductService) switchDefaultProduct(oldDefaultId, newDefaultId int, variants []Product) error {
	// Update size variant code for all variants
	err := ps.updateSizeVariantCodeForAll(variants, newDefaultId)
	if err != nil {
		return err
	}

	// Remove default status from old product
	err = ps.updateProductDefaultStatus(oldDefaultId, false)
	if err != nil {
		return err
	}

	// Set default status for new product
	err = ps.updateProductDefaultStatus(newDefaultId, true)
	if err != nil {
		return err
	}

	return nil
}

// handleProductInStockMarking handles product activation scenario
func (ps *ProductService) handleProductInStockMarking(productId int, currentProduct *Product) error {
	if currentProduct == nil || currentProduct.SizeVariantCode == nil {
		return nil
	}

	variants, err := ps.GetSizeVariantsByCode(*currentProduct.SizeVariantCode)
	if err != nil {
		return err
	}

	if len(variants) <= 1 {
		return nil
	}

	// Check if current product is top ranking
	if !IsTopRankingProduct(variants, currentProduct) {
		return nil
	}

	// Find current default product
	currentDefaultProduct := FindCurrentDefaultProduct(variants)
	if currentDefaultProduct == nil || int(currentDefaultProduct.ID) == productId {
		return nil
	}

	// Switch default product
	return ps.switchDefaultProduct(int(currentDefaultProduct.ID), productId, variants)
}

// handleProductOOSMarking handles product OOS scenario
func (ps *ProductService) handleProductOOSMarking(productId int, currentProduct *Product) error {
	// Check if this product is a default product
	if currentProduct == nil || currentProduct.IsDefault == nil || !*currentProduct.IsDefault {
		return nil
	}

	if currentProduct.SizeVariantCode == nil {
		return nil
	}

	variants, err := ps.GetSizeVariantsByCode(*currentProduct.SizeVariantCode)
	if err != nil {
		return err
	}

	if len(variants) <= 1 {
		return nil
	}

	// Find next available product
	nextAvailableProduct := FindNextAvailableProduct(variants, productId)
	if nextAvailableProduct == nil {
		return nil
	}

	// Switch default product
	return ps.switchDefaultProduct(productId, int(nextAvailableProduct.ID), variants)
}

// RefreshCacheExternalCall allows external refresh of the cache
func RefreshCacheExternalCall() error {
	ps := GetInstance()
	ctx, cancel := context.WithTimeout(context.Background(), ps.config.Timeout)
	defer cancel()
	return ps.refresh(ctx)
}
