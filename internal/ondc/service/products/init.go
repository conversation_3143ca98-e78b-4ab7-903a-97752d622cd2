package products

import (
	"context"
	"kc/internal/ondc/repositories/sqlRepo"
	"sync/atomic"
	"time"
)

func DefaultConfig() *Config {
	return &Config{
		RefreshInterval: 5 * time.Minute,
		MaxRetries:      3,
		RetryDelay:      time.Second,
		Timeout:         30 * time.Second,
	}
}

// InitializeService initializes the products service singleton with default configuration
func InitializeService(db *sqlRepo.Repository) error {
	ps := GetInstance()
	return ps.Initialize(db, DefaultConfig())
}

// InitializeServiceWithConfig initializes the products service singleton with custom configuration
func InitializeServiceWithConfig(db *sqlRepo.Repository, config *Config) error {
	ps := GetInstance()
	return ps.Initialize(db, config)
}

// ShutdownService gracefully shuts down the products service
func ShutdownService(ctx context.Context) error {
	ps := GetInstance()
	return ps.Stop(ctx)
}

// IsInitialized checks if the products service has been initialized
func IsInitialized() bool {
	ps := GetInstance()
	return atomic.LoadInt32(&ps.initialized) == 1
}
