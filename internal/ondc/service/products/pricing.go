package products

import (
	"encoding/json"
	"fmt"
	"kc/internal/ondc/utils"
	"sort"
	"time"
)

// PricingContext holds the context for pricing calculations
type PricingContext struct {
	UserID     *string   `json:"user_id,omitempty"`
	UserCohort *[]string `json:"user_cohort,omitempty"`
	Quantity   int       `json:"quantity"`
}

// getApplicablePricingRules returns all pricing rules that apply to the product and context
func (ps *ProductService) getApplicablePricingRules(productID uint, context *PricingContext) []ProductPricingRule {
	var applicableRules []ProductPricingRule

	// Get product's pricing rule mappings
	ruleIDs, exists := ps.productPricingMappings[productID]
	if !exists {
		return applicableRules
	}
	fmt.Println("Product ID:", productID, "has rule IDs:", ruleIDs)
	// Check each rule
	for _, ruleID := range ruleIDs {
		rule, exists := ps.pricingRules[ruleID]
		if !exists {
			continue
		}

		// Check if rule is active and valid
		if !rule.IsActive {
			continue
		}

		// Check validity period
		now := time.Now()
		if rule.ValidFrom != nil && now.Before(time.UnixMilli(*rule.ValidFrom)) {
			continue
		}
		if rule.ValidUntil != nil && now.After(time.UnixMilli(*rule.ValidUntil)) {
			continue
		}

		// Check conditions if rule has them
		if len(rule.Conditions) > 0 {
			if !evaluateConditions(rule.Conditions, *context) {
				continue
			}
		}

		applicableRules = append(applicableRules, rule)
	}

	return applicableRules
}

func SelectPricingRule(rules []ProductPricingRule) *ProductPricingRule {
	// return the rule with the highest priority if priorities are equal then which was created last
	if len(rules) == 0 {
		return nil // Return an empty rule if no rules are provided
	}

	// Sort rules by priority (descending) and creation time (descending)
	sort.Slice(rules, func(i, j int) bool {
		if rules[i].Priority == rules[j].Priority {
			return rules[i].CreatedAt.After(rules[j].CreatedAt)
		}
		return rules[i].Priority > rules[j].Priority
	})

	// Return the first rule (highest priority and most recently created)
	return &rules[0]
}

// evaluateConditions evaluates all conditions for a pricing rule
func evaluateConditions(conditions []ProductPricingCondition, context PricingContext) bool {
	if len(conditions) == 0 {
		return true
	}

	// Sort conditions by order
	sort.Slice(conditions, func(i, j int) bool {
		return conditions[i].ConditionOrder < conditions[j].ConditionOrder
	})

	// Evaluate conditions based on logical operator
	var result bool
	var logicalOp LogicalOperator

	for i, condition := range conditions {
		conditionResult := evaluateCondition(condition, context)

		if i == 0 {
			result = conditionResult
			logicalOp = condition.LogicalOperator
		} else {
			switch logicalOp {
			case LogicalAnd:
				result = result && conditionResult
			case LogicalOr:
				result = result || conditionResult
			default:
				result = result && conditionResult // Default to AND
			}
		}
	}

	return result
}

// evaluateCondition evaluates a single pricing condition
func evaluateCondition(condition ProductPricingCondition, context PricingContext) bool {
	switch condition.ConditionType {
	case ConditionUserCohort:
		return evaluateUserCohortCondition(condition, context)
	default:
		return false
	}
}

// evaluateUserCohortCondition evaluates user cohort condition
func evaluateUserCohortCondition(condition ProductPricingCondition, context PricingContext) bool {
	if context.UserCohort == nil || len(*context.UserCohort) == 0 {
		return false
	}

	var expectedCohorts []string
	if err := json.Unmarshal(condition.ConditionValue, &expectedCohorts); err != nil {
		return false
	}

	switch condition.Operator {
	case OpEquals:
		for _, userCohort := range *context.UserCohort {
			if userCohort == expectedCohorts[0] {
				return true
			}
		}
		return false
	case OpNotEquals:
		for _, userCohort := range *context.UserCohort {
			if userCohort == expectedCohorts[0] {
				return false
			}
		}
		return true
	case OpIn:
		for _, cohort := range expectedCohorts {
			for _, userCohort := range *context.UserCohort {
				if cohort == userCohort {
					return true
				}
			}
		}
		return false
	case OpNotIn:
		for _, cohort := range expectedCohorts {
			for _, userCohort := range *context.UserCohort {
				if cohort == userCohort {
					return false
				}
			}
		}
		return true
	case OpContains:
		for _, userCohort := range *context.UserCohort {
			if contains(userCohort, expectedCohorts[0]) {
				return false
			}
		}
		return true
	default:
		return false
	}
}

// applyPricingRule applies a pricing rule and returns the price impact
func applyPricingRule(rule *ProductPricingRule, context *PricingContext, currentPrice float64) (float64, *PricingTierInfo) {
	switch rule.RuleType {
	case PricingRuleTypeConditional:
		return applyConditionalPricing(rule, context, currentPrice), nil
	case PricingRuleTypeTierBased:
		return applyTierBasedPricing(rule, context, currentPrice)
	default:
		return 0, nil
	}
}

// applyConditionalPricing applies conditional pricing rule
func applyConditionalPricing(rule *ProductPricingRule, context *PricingContext, currentPrice float64) float64 {
	if len(rule.Tiers) == 0 {
		return 0
	}
	if len(rule.Tiers) > 1 {
		fmt.Println("Warning: Multiple tiers found in conditional pricing rule, using first tier only")
	}

	// Sort tiers by min quantity
	sort.Slice(rule.Tiers, func(i, j int) bool {
		return rule.Tiers[i].MinQuantity < rule.Tiers[j].MinQuantity
	})

	return calculatePriceImpact(rule.Tiers[0], currentPrice)
}

// applyTierBasedPricing applies tier-based pricing rule
func applyTierBasedPricing(rule *ProductPricingRule, context *PricingContext, currentPrice float64) (float64, *PricingTierInfo) {
	if len(rule.Tiers) == 0 {
		return 0, nil
	}

	// Sort tiers by min quantity
	sort.Slice(rule.Tiers, func(i, j int) bool {
		return rule.Tiers[i].MinQuantity < rule.Tiers[j].MinQuantity
	})

	// Find applicable tier
	var applicableTier *ProductPricingTier
	var nextTierAt *int

	for i, tier := range rule.Tiers {
		if context.Quantity >= tier.MinQuantity && (tier.MaxQuantity == nil || context.Quantity <= *tier.MaxQuantity) {
			applicableTier = &rule.Tiers[i]

			// Find next tier for nextTierAt calculation
			if i+1 < len(rule.Tiers) {
				nextTier := rule.Tiers[i+1]
				nextTierAt = &nextTier.MinQuantity
			}
			break
		}
	}

	if applicableTier == nil {
		return 0, nil
	}

	impactedPrice := calculatePriceImpact(*applicableTier, currentPrice)

	tierInfo := &PricingTierInfo{
		TierName:    applicableTier.TierName,
		MinQuantity: applicableTier.MinQuantity,
		MaxQuantity: applicableTier.MaxQuantity,
		CurrentTier: true,
		NextTierAt:  nextTierAt,
	}

	return impactedPrice, tierInfo
}

// calculatePriceImpact calculates the price impact of a pricing tier
func calculatePriceImpact(tier ProductPricingTier, currentPrice float64) float64 {
	if tier.PriceValue <= 0 {
		return currentPrice
	}

	switch tier.PriceType {
	case PriceTypeFixed:
		// Fixed price per unit
		if tier.PriceValue >= currentPrice {
			return currentPrice
		}
		return tier.PriceValue
	case PriceTypePercentageOff:
		// Percentage discount
		if tier.PriceValue >= 50 {
			tier.PriceValue = 50 // Cap percentage discount at 50%
		}
		discountAmount := currentPrice * (tier.PriceValue / 100.0)
		return utils.Round(currentPrice - discountAmount)
	case PriceTypeAbsoluteOff:
		// Absolute discount per unit
		if tier.PriceValue <= (currentPrice * 0.5) {
			return currentPrice
		}
		return utils.Round(currentPrice - tier.PriceValue)
	default:
		return 0
	}
}

// Helper function to check if a string contains another string
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr)))
}
