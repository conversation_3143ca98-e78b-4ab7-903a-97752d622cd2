package products

import (
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/utils"
	"log"
	"math"
	"reflect"
	"strconv"
	"strings"
	"time"
)

// Main function that orchestrates the default product management
func updateDefaultProductSizeVariantCode(productId int, isMarkingOos bool) error {
	ps := GetInstance()
	currentProduct, err := getProductById(productId)
	if err != nil {
		return err
	}

	if isMarkingOos {
		return ps.handleProductOOSMarking(productId, currentProduct)
	} else {
		return ps.handleProductInStockMarking(productId, currentProduct)
	}
}

// Helper function to get product by ID
func getProductById(productId int) (*Product, error) {
	var product Product
	ps := GetInstance()
	query := fmt.Sprintf("select * from kiranaclubdb.kiranabazar_products where id=%d", productId)
	_, err := ps.db.CustomQuery(&product, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query product: %v", err)
	}
	return &product, nil
}

// Helper function to get all size variants ordered by rank
func getSizeVariantsByCode(sizeVariantCode int) ([]Product, error) {
	var sizeVariants []Product
	ps := GetInstance()
	query := fmt.Sprintf("select * from kiranaclubdb.kiranabazar_products kp where size_variant_code=%d order by kp.rank", sizeVariantCode)
	_, err := ps.db.CustomQuery(&sizeVariants, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query size variants: %v", err)
	}
	return sizeVariants, nil
}

// Helper function to update size variant code for all variants
func updateSizeVariantCodeForAll(variants []Product, newSizeVariantCode int) error {
	if len(variants) == 0 {
		return nil
	}
	ps := GetInstance()
	updateProductIds := make([]string, 0, len(variants))
	for _, variant := range variants {
		updateProductIds = append(updateProductIds, fmt.Sprintf("%d", variant.ID))
	}

	query := fmt.Sprintf("update kiranaclubdb.kiranabazar_products set size_variant_code=%d where id IN (%s)",
		newSizeVariantCode, strings.Join(updateProductIds, ","))
	_, err := ps.db.CustomQuery(nil, query)
	if err != nil {
		return fmt.Errorf("failed to update size variant code: %v", err)
	}
	return nil
}

// Helper function to update default status of a product
func updateProductDefaultStatus(productId int, isDefault bool) error {
	ps := GetInstance()
	query := fmt.Sprintf("update kiranaclubdb.kiranabazar_products set is_default=%t where id=%d", isDefault, productId)
	_, err := ps.db.CustomQuery(nil, query)
	if err != nil {
		return fmt.Errorf("failed to update product default status: %v", err)
	}
	return nil
}

// Helper function to switch default product between two products
func switchDefaultProduct(oldDefaultId, newDefaultId int, variants []Product) error {
	// Update size variant code for all variants
	err := updateSizeVariantCodeForAll(variants, newDefaultId)
	if err != nil {
		return err
	}

	// Remove default status from old product
	err = updateProductDefaultStatus(oldDefaultId, false)
	if err != nil {
		return err
	}

	// Set default status for new product
	err = updateProductDefaultStatus(newDefaultId, true)
	if err != nil {
		return err
	}

	return nil
}

// Add this helper function for proper number formatting
func formatCurrency(value float64) string {
	// Check if it's a whole number
	if value == float64(int(value)) {
		return fmt.Sprintf("₹%d", int(value))
	}
	// Format with up to 2 decimal places and remove trailing zeros
	formatted := fmt.Sprintf("₹%.2f", value)
	formatted = strings.TrimRight(formatted, "0")
	formatted = strings.TrimRight(formatted, ".")
	return formatted
}

func formatMRP(value float64) string {
	// Check if it's a whole number
	if value == float64(int(value)) {
		return fmt.Sprintf("MRP: ₹%d", int(value))
	}
	// Format with up to 2 decimal places and remove trailing zeros
	formatted := fmt.Sprintf("MRP: ₹%.2f", value)
	formatted = strings.TrimRight(formatted, "0")
	formatted = strings.TrimRight(formatted, ".")
	return formatted
}

// Handle product OOS scenario
func handleProductOOSMarking(productId int, currentProduct *Product) error {
	// Check if this product is a default product
	if currentProduct == nil || currentProduct.IsDefault == nil || !*currentProduct.IsDefault {
		return nil
	}

	if currentProduct.SizeVariantCode == nil {
		return nil
	}

	variants, err := getSizeVariantsByCode(*currentProduct.SizeVariantCode)
	if err != nil {
		return err
	}

	if len(variants) <= 1 {
		return nil
	}

	// Find next available product
	newDefaultProduct := FindNextAvailableProduct(variants, productId)
	if newDefaultProduct == nil {
		return nil
	}

	// Switch default product
	return switchDefaultProduct(productId, int(newDefaultProduct.ID), variants)
}

// Helper function to get a map of direct columns and their types
func getDirectColumns() map[string]string {
	productType := reflect.TypeOf(Product{})
	columns := make(map[string]string)

	for i := 0; i < productType.NumField(); i++ {
		field := productType.Field(i)

		// Skip fields marked with gorm:"-"
		if strings.Contains(field.Tag.Get("gorm"), "-") {
			continue
		}

		jsonTag := field.Tag.Get("json")
		if jsonTag == "" {
			continue
		}

		// Handle cases like "field,omitempty"
		parts := strings.Split(jsonTag, ",")

		// Skip Meta field since we handle it separately
		if parts[0] == "meta" || parts[0] == "meta_properties" || parts[0] == "image_urls" || parts[0] == "extended_meta" {
			continue
		}

		// Get the type name
		typeName := utils.GetTypeName(field.Type)
		columns[parts[0]] = typeName
	}

	return columns
}

// Helper function to get the field value by name
func getFieldValue(productData *Product, fieldName string) (interface{}, error) {
	val := reflect.ValueOf(productData).Elem()

	// Find the field by JSON tag
	productType := reflect.TypeOf(*productData)
	for i := 0; i < productType.NumField(); i++ {
		field := productType.Field(i)
		jsonTag := field.Tag.Get("json")

		if jsonTag == "" {
			continue
		}

		// Handle cases like "field,omitempty"
		parts := strings.Split(jsonTag, ",")
		if parts[0] == fieldName {
			fieldVal := val.Field(i)

			// Handle pointer types
			if field.Type.Kind() == reflect.Ptr && !fieldVal.IsNil() {
				return fieldVal.Elem().Interface(), nil
			} else if field.Type.Kind() != reflect.Ptr {
				return fieldVal.Interface(), nil
			} else {
				return nil, nil // nil pointer
			}
		}
	}

	return nil, fmt.Errorf("field %s not found", fieldName)
}

// updateNameLabel updates the name_label in the meta map based on product name and pack size
func updateNameLabel(name string, packSize interface{}) (string, bool) {
	if packSize == nil || packSize == "" {
		return "", false
	}

	nameLabel := fmt.Sprintf("%s Pack of %v", name, packSize)
	return nameLabel, true
}

// updatePricingFields updates wholesale rate string, margin and margin string
// Returns a slice of history data entries for the update log
func updatePricingFields(metaMap map[string]interface{}, wholesaleRate interface{}) ([]map[string]interface{}, error) {
	updateLog := []map[string]interface{}{}

	if wholesaleRate == nil || wholesaleRate == "" {
		err := fmt.Errorf("wholesale rate is nil or empty")
		return updateLog, err
	}

	// Convert wholesale rate to float
	wholesaleRateFloat, ok := wholesaleRate.(float64)
	if !ok {
		// Try to convert to float if it's not already
		if strVal, isStr := wholesaleRate.(string); isStr {
			if parsedFloat, err := strconv.ParseFloat(strVal, 64); err == nil {
				wholesaleRateFloat = parsedFloat
			} else {
				err := fmt.Errorf("failed to convert wholesale rate to float")
				return updateLog, err
			}
		} else {
			err := fmt.Errorf("invalid wholesale rate type")
			return updateLog, err
		}
	}

	// Update wholesale_rate_string using the new formatting function
	previousWholesaleRateString := metaMap["wholesale_rate_string"]
	wholesaleRateString := formatCurrency(wholesaleRateFloat)
	metaMap["wholesale_rate_string"] = wholesaleRateString

	// Add to update log
	updateLog = append(updateLog, map[string]interface{}{
		"key":            "wholesale_rate_string",
		"updated_value":  wholesaleRateString,
		"previous_value": previousWholesaleRateString,
	})

	// Calculate margin if mrp exists
	if mrp, exists := metaMap["mrp_number"]; exists && mrp != nil && mrp != "" {
		mrpFloat, ok := mrp.(float64)
		if !ok {
			// Try to convert if not already a float
			if strVal, isStr := mrp.(string); isStr {
				if parsedFloat, err := strconv.ParseFloat(strVal, 64); err == nil {
					mrpFloat = parsedFloat
				} else {
					err := fmt.Errorf("failed to convert MRP to float")
					return updateLog, err
				}
			} else {
				err := fmt.Errorf("invalid MRP type")
				return updateLog, err
			}
		}

		// Calculate margin if both values are valid
		if mrpFloat > 0 && wholesaleRateFloat > 0 {
			previousMargin := metaMap["markup_margin"]
			previousMarginString := metaMap["markup_margin_string"]

			marginWholeSaleRate := math.Round((((mrpFloat-wholesaleRateFloat)/mrpFloat)*100)*100) / 100
			brandWholesaleRate, exists := metaMap["brand_wholesale_rate"]
			if exists && brandWholesaleRate != nil && brandWholesaleRate != "" {
				brandWholesaleRateFloat, ok := brandWholesaleRate.(float64)
				if !ok {
					err := fmt.Errorf("failed to convert brand wholesale rate to float")
					return updateLog, err
				}
				marginBrand := math.Round((((mrpFloat-brandWholesaleRateFloat)/mrpFloat)*100)*100) / 100
				marginExtra := math.Round((((brandWholesaleRateFloat-wholesaleRateFloat)/mrpFloat)*100)*100) / 100
				marginString := fmt.Sprintf("मार्जिन: %.0f+%.0f%%", marginBrand, marginExtra)
				metaMap["markup_margin"] = marginBrand + marginExtra
				metaMap["markup_margin_string"] = marginString
			} else {
				metaMap["markup_margin"] = marginWholeSaleRate
				marginString := fmt.Sprintf("मार्जिन: %.0f%%", marginWholeSaleRate)
				metaMap["markup_margin_string"] = marginString
			}
			// Add to update log
			updateLog = append(updateLog, map[string]interface{}{
				"key":            "markup_margin",
				"updated_value":  metaMap["markup_margin"],
				"previous_value": previousMargin,
			})

			updateLog = append(updateLog, map[string]interface{}{
				"key":            "markup_margin_string",
				"updated_value":  metaMap["markup_margin_string"],
				"previous_value": previousMarginString,
			})
		}
	}

	return updateLog, nil
}

// Helper function to recursively update nested objects in extended meta
func updateNestedExtendedMeta(extendedMetaMap map[string]interface{}, key string, value interface{}) (interface{}, error) {
	// Split key by dots to handle nested paths like "top_badge_styles.color"
	keyParts := strings.Split(key, ".")

	if len(keyParts) == 1 {
		// Simple key-value update
		previousValue := extendedMetaMap[key]
		if value == nil || value == "" {
			delete(extendedMetaMap, key)
		} else {
			extendedMetaMap[key] = value
		}
		return previousValue, nil
	}

	// Handle nested object updates
	currentLevel := extendedMetaMap
	var previousValue interface{}

	// Navigate to the parent of the final key
	for i := 0; i < len(keyParts)-1; i++ {
		if currentLevel[keyParts[i]] == nil {
			// Create new nested object
			currentLevel[keyParts[i]] = make(map[string]interface{})
		}

		// Type assertion to ensure it's a map
		nextLevel, ok := currentLevel[keyParts[i]].(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("cannot create nested structure at key '%s' - existing value is not an object", strings.Join(keyParts[:i+1], "."))
		}
		currentLevel = nextLevel
	}

	// Update the final key
	finalKey := keyParts[len(keyParts)-1]
	previousValue = currentLevel[finalKey]

	if value == nil || value == "" {
		delete(currentLevel, finalKey)
	} else {
		currentLevel[finalKey] = value
	}

	return previousValue, nil
}

// Helper function to check if a key belongs to extended meta
func isExtendedMetaField(key string, extendedMetaFieldMap map[string]string) bool {
	// Check direct field match
	if _, exists := extendedMetaFieldMap[key]; exists {
		return true
	}

	// Check nested field match (for keys like "top_badge_styles.color")
	if strings.Contains(key, ".") {
		parts := strings.Split(key, ".")
		parentKey := parts[0]
		// Check if parent key exists with nested marker
		if _, exists := extendedMetaFieldMap[parentKey+"."]; exists {
			return true
		}
		// Also check if parent key exists as a direct field
		if _, exists := extendedMetaFieldMap[parentKey]; exists {
			return true
		}
	}

	return false
}

// Helper function to validate extended meta field structure
func validateExtendedMetaUpdate(extendedMetaType reflect.Type, extendedMetaFieldMap map[string]string, key string, value interface{}) error {
	// Check if this key is actually part of extended meta
	if !isExtendedMetaField(key, extendedMetaFieldMap) {
		return fmt.Errorf("field %s is not part of extended meta", key)
	}

	// Basic validation - ensure value types are JSON-serializable
	switch value.(type) {
	case nil, string, float64, int, bool:
		return nil
	case []interface{}, map[string]interface{}:
		return nil
	default:
		// Try to marshal to ensure it's JSON-serializable
		if _, err := json.Marshal(value); err != nil {
			return fmt.Errorf("value for key %s is not JSON-serializable: %v", key, err)
		}
		return nil
	}
}

func UpdateProductData(req *dto.UpdateProductDetailsRequest, productData *Product) ([]interface{}, error) {
	updates := make(map[string]interface{})
	updates["updated_at"] = time.Now()
	updateLog := []interface{}{}

	// Initialize meta map for JSON fields
	metaMap := make(map[string]interface{})
	if productData.Meta != nil && len(productData.Meta) > 0 {
		if err := json.Unmarshal(productData.Meta, &metaMap); err != nil {
			return nil, fmt.Errorf("failed to unmarshal existing meta data: %v", err)
		}
	}

	// Get a map of direct database columns from Product struct
	directColumns := getDirectColumns()

	// Get reflection type of KiranaBazarProductMeta for JSON field mapping
	metaType := reflect.TypeOf(shared.KiranaBazarProductMeta{})
	metaFieldMap := make(map[string]string)

	// Map JSON tags to field names for meta fields
	for i := 0; i < metaType.NumField(); i++ {
		field := metaType.Field(i)
		tag := field.Tag.Get("json")
		if tag != "" {
			// Handle cases like "field,omitempty"
			parts := strings.Split(tag, ",")
			metaFieldMap[parts[0]] = field.Name
		}
	}

	extendedMetaMap := make(map[string]interface{})
	if productData.ExtendedMeta != nil {
		if err := json.Unmarshal(productData.ExtendedMeta, &extendedMetaMap); err != nil {
			return nil, fmt.Errorf("failed to unmarshal existing extended meta data: %v", err)
		}
	}

	extendedMetaType := reflect.TypeOf(dao.KiranaBazarProductExtendedMeta{})
	extendedMetaFieldMap := make(map[string]string)

	// Map JSON tags to field names for extended meta fields (including nested keys)
	for i := 0; i < extendedMetaType.NumField(); i++ {
		field := extendedMetaType.Field(i)
		tag := field.Tag.Get("json")
		if tag != "" {
			// Handle cases like "field,omitempty"
			parts := strings.Split(tag, ",")
			extendedMetaFieldMap[parts[0]] = field.Name

			// Also map nested keys for this field (assuming all nested keys belong to extended meta)
			// This allows keys like "top_badge_styles.color" to be recognized as extended meta
			if field.Type.Kind() == reflect.Struct || field.Type.Kind() == reflect.Map {
				// Mark this as a parent for nested keys
				extendedMetaFieldMap[parts[0]+"."] = field.Name
			}
		}
	}

	metaUpdated := false
	extendedMetaUpdated := false

	// Track if certain fields are updated
	nameUpdated := false
	packSizeUpdated := false
	var newName string
	var newPackSize interface{}
	var newMrp interface{}

	// Track if wholesale rate fields are updated
	wholesaleRateUpdated := false
	brandWholesaleRateUpdated := false
	mrpUpdated := false
	// imageURLsUpdated := false
	imageURLs := []string{}

	// var newWholesaleRate, newBrandWholesaleRate interface{}
	var newWholesaleRate interface{}

	// Process all updates
	for _, update := range req.Data.Updates {
		histData := map[string]interface{}{
			"key": update.Key,
		}
		if update.Key == "image_urls" {
			previousImageURLs := productData.ProductImageURLs

			// Handle different input types
			switch v := update.Value.(type) {
			case []interface{}:
				// Convert slice of interface{} to slice of strings
				for _, url := range v {
					if strURL, ok := url.(string); ok {
						imageURLs = append(imageURLs, strURL)
					}
				}
			case []string:
				imageURLs = v
			case string:
				// Try to unmarshal if it's a JSON string
				if err := json.Unmarshal([]byte(v), &imageURLs); err != nil {
					// If not valid JSON, treat as a single URL
					imageURLs = []string{v}
				}
			default:
				return nil, fmt.Errorf("invalid value type for image_urls: expected array or string")
			}

			// Marshal back to JSON
			imageURLsJSON, err := json.Marshal(imageURLs)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal image URLs: %v", err)
			}

			// imageURLsUpdated = true
			mediaUrls := []dao.KiranaBazarProductMediaUrl{}
			for _, url := range imageURLs {
				mediaUrls = append(mediaUrls, dao.KiranaBazarProductMediaUrl{
					Url: url,
				})
			}
			existingMediaUrls := []dao.KiranaBazarProductMediaUrl{}
			if productData.MediaUrls != nil {
				if err := json.Unmarshal(productData.MediaUrls, &existingMediaUrls); err != nil {
					return nil, fmt.Errorf("failed to unmarshal existing media URLs: %v", err)
				}
			}
			for _, mediaUrl := range existingMediaUrls {
				if mediaUrl.VideoUrl != nil && *mediaUrl.VideoUrl != "" {
					mediaUrls = append(mediaUrls, dao.KiranaBazarProductMediaUrl{
						Url: *mediaUrl.VideoUrl,
					})
				}
			}

			mediaUrlsJSON, err := json.Marshal(mediaUrls)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal media URLs: %v", err)
			}
			updates["media_urls"] = string(mediaUrlsJSON)
			updates["image_urls"] = string(imageURLsJSON)

			histData["updated_value"] = imageURLs
			histData["previous_value"] = previousImageURLs
			updateLog = append(updateLog, histData)
		} else if _, isDirect := directColumns[update.Key]; isDirect {
			// This is a direct database column
			previousValue, err := getFieldValue(productData, update.Key)
			if err != nil {
				log.Printf("Warning: couldn't get previous value for %s: %v", update.Key, err)
			}

			// Validate and convert the value to the correct type
			typedValue, err := utils.ConvertToCorrectType(update.Value, directColumns[update.Key])
			if err != nil {
				return nil, fmt.Errorf("invalid value for %s: %v", update.Key, err)
			}

			// Track if name is updated
			if update.Key == "name" {
				nameUpdated = true
				newName = fmt.Sprintf("%v", typedValue)
			}

			updates[update.Key] = typedValue
			histData["updated_value"] = typedValue
			histData["previous_value"] = previousValue
		} else if _, isMeta := metaFieldMap[update.Key]; isMeta {
			// This is a meta field
			previousValue := metaMap[update.Key]

			// Handle null values for deletion
			if update.Value == nil {
				delete(metaMap, update.Key)
				metaUpdated = true
				histData["updated_value"] = nil
				histData["previous_value"] = previousValue

				// Track specific meta field updates for null values
				switch update.Key {
				case "brand_wholesale_rate":
					brandWholesaleRateUpdated = true
				default:
					return nil, fmt.Errorf("%s updated value cannot be nil", update.Key)
				}
			} else {
				// Get expected type from the meta struct
				expectedType, err := utils.GetMetaFieldType(metaType, metaFieldMap[update.Key])
				if err != nil {
					return nil, fmt.Errorf("couldn't determine type for meta field %s: %v", update.Key, err)
				}

				// Validate and convert the value to the correct type
				typedValue, err := utils.ConvertToCorrectType(update.Value, expectedType)
				if err != nil {
					return nil, fmt.Errorf("invalid value for meta field %s: %v", update.Key, err)
				}

				// Track specific meta field updates
				switch update.Key {
				case "pack_size":
					packSizeUpdated = true
					newPackSize = typedValue
				case "wholesale_rate":
					wholesaleRateUpdated = true
					newWholesaleRate = typedValue
				case "brand_wholesale_rate":
					brandWholesaleRateUpdated = true
				case "mrp_number":
					mrpUpdated = true
					newMrp = typedValue
				}

				if typedValue == "" {
					delete(metaMap, update.Key)
				} else {
					metaMap[update.Key] = typedValue
				}
				metaUpdated = true
				histData["updated_value"] = typedValue
				histData["previous_value"] = previousValue
			}
		} else if isExtendedMetaField(update.Key, extendedMetaFieldMap) {
			// This is an extended meta field (handles both flat and nested keys)

			// Validate the extended meta update
			if err := validateExtendedMetaUpdate(extendedMetaType, extendedMetaFieldMap, update.Key, update.Value); err != nil {
				return nil, fmt.Errorf("invalid extended meta field %s: %v", update.Key, err)
			}

			// Update the extended meta map (handles nested keys with dot notation)
			previousValue, err := updateNestedExtendedMeta(extendedMetaMap, update.Key, update.Value)
			if err != nil {
				return nil, fmt.Errorf("failed to update extended meta field %s: %v", update.Key, err)
			}

			extendedMetaUpdated = true
			histData["updated_value"] = update.Value
			histData["previous_value"] = previousValue
		} else {
			log.Printf("Warning: unknown field '%s' will be ignored", update.Key)
			continue // Skip adding to updateLog
		}

		updateLog = append(updateLog, histData)
	}

	// Handle name_label updates if name or pack_size was updated
	if nameUpdated || packSizeUpdated {
		// If name wasn't updated, get it from the product data
		if !nameUpdated {
			newName = productData.Name
		}

		// Get pack size (either updated or existing)
		var packSize interface{}
		if packSizeUpdated {
			packSize = newPackSize
		} else {
			packSize, _ = metaMap["pack_size"]
		}

		// Update name_label using the helper function
		if nameLabel, updated := updateNameLabel(newName, packSize); updated {
			previousNameLabel := metaMap["name_label"]
			metaMap["name_label"] = nameLabel
			metaUpdated = true

			// Add to update log
			histData := map[string]interface{}{
				"key":            "name_label",
				"updated_value":  nameLabel,
				"previous_value": previousNameLabel,
			}
			updateLog = append(updateLog, histData)
		}
	}

	// Handle wholesale rate related updates
	if wholesaleRateUpdated || brandWholesaleRateUpdated {
		// Get the latest values to work with
		var wholesaleRate interface{}

		if wholesaleRateUpdated {
			wholesaleRate = newWholesaleRate
		} else {
			wholesaleRate = metaMap["wholesale_rate"]
		}

		// Update pricing fields using the helper function
		if wholesaleRate != nil && wholesaleRate != "" {
			pricingUpdates, err := updatePricingFields(metaMap, wholesaleRate)
			if err != nil {
				return nil, fmt.Errorf("failed to update pricing fields: %v", err)
			}
			for _, update := range pricingUpdates {
				updateLog = append(updateLog, update)
				metaUpdated = true
			}
		}

	}

	if mrpUpdated {
		if newMrp != nil && newMrp != "" {
			// Convert MRP to float for formatting
			mrpFloat, ok := newMrp.(float64)
			if !ok {
				// Try to convert if not already a float
				if strVal, isStr := newMrp.(string); isStr {
					if parsedFloat, err := strconv.ParseFloat(strVal, 64); err == nil {
						mrpFloat = parsedFloat
					} else {
						return nil, fmt.Errorf("failed to convert MRP to float for mrp_string update: %v", err)
					}
				} else {
					return nil, fmt.Errorf("invalid MRP type for mrp_string update")
				}
			}

			previousMrpString := metaMap["mrp_string"]
			mrpString := formatMRP(mrpFloat) // Use the new formatting function
			metaMap["mrp_string"] = mrpString
			metaUpdated = true

			// Add to update log
			histData := map[string]interface{}{
				"key":            "mrp_string",
				"updated_value":  mrpString,
				"previous_value": previousMrpString,
			}
			updateLog = append(updateLog, histData)
		}
	}

	// If meta was updated, marshal it back to JSON
	if metaUpdated {
		metaJSON, err := json.Marshal(metaMap)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal meta data: %v", err)
		}
		updates["meta"] = string(metaJSON)
	}

	if extendedMetaUpdated {
		extendedMetaJSON, err := json.Marshal(extendedMetaMap)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal extended meta data: %v", err)
		}
		updates["extended_meta"] = string(extendedMetaJSON)
	}

	// Build and execute query
	updateQuery := "UPDATE kiranabazar_products SET " +
		formatSetClause(updates) +
		" WHERE id = " + strconv.Itoa(req.Data.ProductID)

	ps := GetInstance()
	_, err := ps.db.CustomQuery(nil, updateQuery)
	if err != nil {
		return nil, fmt.Errorf("failed to update product: %v", err)
	}

	if (updates["is_oos"] == true) || (updates["is_oos"] == false) {
		isMarkingOos := updates["is_oos"].(bool)
		err := updateDefaultProductSizeVariantCode(req.Data.ProductID, isMarkingOos)
		if err != nil {
			return nil, fmt.Errorf("failed to update default product size variant code: %v", err)
		}
		popularityValue := productData.PopularityValue
		productSeller := productData.SizeVariantsSellerItems("", nil, nil).Seller

		message := ""
		switch updates["is_oos"] {
		case true:
			message = fmt.Sprintf("*Product Marked Out of Stock ❌*\nBy: %s\nProduct ID: %d\nName: %s\nSeller: %s\nPopularity Value: %d",
				req.UpdatedBy, req.Data.ProductID, productData.Name, productSeller, int(popularityValue))
		case false:
			message = fmt.Sprintf("*Product Marked In Stock ✅*\nBy: %s\nProduct ID: %d\nName: %s\nSeller: %s\nPopularity Value: %d",
				req.UpdatedBy, req.Data.ProductID, productData.Name, productSeller, int(popularityValue))
		}

		slack.SendSlackMessageOnSellerUpdates(message)
	}

	return updateLog, nil
}

func formatSetClause(updates map[string]interface{}) string {
	var result string
	firstItem := true

	for field, value := range updates {
		if !firstItem {
			result += ", "
		}

		switch v := value.(type) {
		case string:
			// Escape single quotes in strings
			escapedValue := strings.ReplaceAll(v, "\\", "\\\\")
			escapedValue = strings.ReplaceAll(escapedValue, "'", "''")
			result += fmt.Sprintf("%s = '%s'", field, escapedValue)
		case time.Time:
			result += fmt.Sprintf("%s = '%s'", field, v.Format("2006-01-02 15:04:05"))
		case bool:
			if v {
				result += fmt.Sprintf("%s = 1", field)
			} else {
				result += fmt.Sprintf("%s = 0", field)
			}
		default:
			result += fmt.Sprintf("%s = %v", field, v)
		}

		firstItem = false
	}

	return result
}

func getRoundOffFloat(value float64, roundOff int) float64 {
	if value == 0 {
		return 0
	}
	roundOffFloat := float64(roundOff)
	return math.Round(value*10*roundOffFloat) / (10 * roundOffFloat)
}

func GetRateAndMargin(wholesaleRate float64, brandWholesaleRate *float64, mrpFloat float64) (float64, string, *float64, float64, error) {
	if mrpFloat == 0 {
		return 0, "", nil, wholesaleRate, fmt.Errorf("MRP cannot be zero")
	}
	if wholesaleRate >= mrpFloat {
		return 0, "", nil, wholesaleRate, fmt.Errorf("wholesale rate should be less than MRP")
	}
	wholesaleRate = math.Round(wholesaleRate*100) / 100
	if brandWholesaleRate != nil {
		if *brandWholesaleRate <= wholesaleRate {
			return 0, "", nil, wholesaleRate, fmt.Errorf("brand wholesale rate should be greater than wholesale rate")
		}
		marginBrand := math.Round((((mrpFloat-*brandWholesaleRate)/mrpFloat)*100)*100) / 100
		marginExtra := math.Round((((*brandWholesaleRate-wholesaleRate)/mrpFloat)*100)*100) / 100
		markupMarginFloat := marginBrand + marginExtra
		markupMarginString := fmt.Sprintf("मार्जिन: %.1f+%.1f%%", marginBrand, marginExtra)
		brandWholesaleRateRoundOff := getRoundOffFloat(*brandWholesaleRate, 1)
		return getRoundOffFloat(markupMarginFloat, 1), markupMarginString, &brandWholesaleRateRoundOff, getRoundOffFloat(wholesaleRate, 1), nil
	} else {
		margin := math.Round((((mrpFloat-wholesaleRate)/mrpFloat)*100)*100) / 100
		markupMarginString := fmt.Sprintf("मार्जिन: %d%%", int(margin))
		return getRoundOffFloat(margin, 1), markupMarginString, nil, getRoundOffFloat(wholesaleRate, 1), nil
	}
}
