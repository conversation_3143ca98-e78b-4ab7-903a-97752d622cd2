package products

import (
	"fmt"
	"gorm.io/datatypes"
	"strconv"
)

func parseID(id interface{}) (uint, error) {
	switch v := id.(type) {
	case string:
		parsed, err := strconv.ParseUint(v, 10, 32)
		if err != nil {
			return 0, fmt.Errorf("invalid ID format: %v", err)
		}
		return uint(parsed), nil
	case int:
		if v < 0 {
			return 0, fmt.<PERSON><PERSON><PERSON>("negative ID not allowed: %d", v)
		}
		return uint(v), nil
	case int64:
		if v < 0 {
			return 0, fmt.<PERSON><PERSON><PERSON>("negative ID not allowed: %d", v)
		}
		return uint(v), nil
	case uint:
		return v, nil
	case uint64:
		if v > uint64(^uint(0)) {
			return 0, fmt.Errorf("ID exceeds maximum uint value: %d", v)
		}
		return uint(v), nil
	default:
		return 0, fmt.Errorf("invalid ID type: %T", id)
	}
}

// Helper function to check if product is top ranking among active variants
func IsTopRankingProduct(variants []Product, currentProduct *Product) bool {
	if len(variants) == 0 {
		return false
	}
	activeInStockVariants := make([]Product, 0)
	for _, variant := range variants {
		if variant.IsActive != nil && *variant.IsActive && variant.IsOOS != nil && !*variant.IsOOS && variant.ID != currentProduct.ID {
			activeInStockVariants = append(activeInStockVariants, variant)
		}
	}
	if len(activeInStockVariants) == 0 {
		return false
	}

	return int(*activeInStockVariants[0].Rank) > *currentProduct.Rank
}

// Helper function to find current active default product among variants
func FindCurrentDefaultProduct(variants []Product) *Product {
	for _, variant := range variants {
		if variant.IsActive != nil && *variant.IsActive &&
			variant.IsOOS != nil && !*variant.IsOOS &&
			variant.IsDefault != nil && *variant.IsDefault {
			return &variant
		}
	}
	return nil
}

// Helper function to find next available active product (for OOS scenario)
func FindNextAvailableProduct(variants []Product, excludeProductId int) *Product {
	for _, variant := range variants {
		if variant.IsActive != nil && *variant.IsActive &&
			variant.IsOOS != nil && !*variant.IsOOS &&
			int(variant.ID) != excludeProductId {
			return &variant
		}
	}
	return nil
}

// deepCopyJSON creates a deep copy of JSON fields
func deepCopyJSON(original datatypes.JSON) datatypes.JSON {
	if original == nil {
		return nil
	}

	// Create a new byte slice and copy the data
	copied := make([]byte, len(original))
	copy(copied, original)
	return datatypes.JSON(copied)
}
