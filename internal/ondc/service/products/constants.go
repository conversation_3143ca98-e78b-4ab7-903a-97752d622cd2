package products

var DEFAULT_SALES_ANALYSIS = []string{"LAST_7_DAYS_ANALYSIS", "LAST_30_DAYS_ANALYSIS"}

type PricingRuleType string

const (
	PricingRuleTypeConditional PricingRuleType = "CONDITIONAL"
	PricingRuleTypeTierBased   PricingRuleType = "TIER_BASED"
	PricingRuleTypeHybrid      PricingRuleType = "HYBRID"
)

type PricingConditionType string

const (
	ConditionUserCohort       PricingConditionType = "USER_COHORT"
	ConditionOrderCount       PricingConditionType = "ORDER_COUNT"
	ConditionTotalSpent       PricingConditionType = "TOTAL_SPENT"
	ConditionRegistrationDate PricingConditionType = "REGISTRATION_DATE"
	ConditionLastOrderDate    PricingConditionType = "LAST_ORDER_DATE"
	ConditionCustom           PricingConditionType = "CUSTOM"
)

type PricingConditionOperator string

const (
	OpEquals       PricingConditionOperator = "EQUALS"
	OpNotEquals    PricingConditionOperator = "NOT_EQUALS"
	OpGreaterThan  PricingConditionOperator = "GREATER_THAN"
	OpLessThan     PricingConditionOperator = "LESS_THAN"
	OpGreaterEqual PricingConditionOperator = "GREATER_EQUAL"
	OpLessEqual    PricingConditionOperator = "LESS_EQUAL"
	OpIn           PricingConditionOperator = "IN"
	OpNotIn        PricingConditionOperator = "NOT_IN"
	OpContains     PricingConditionOperator = "CONTAINS"
)

type LogicalOperator string

const (
	LogicalAnd LogicalOperator = "AND"
	LogicalOr  LogicalOperator = "OR"
)

type PriceType string

const (
	PriceTypeFixed         PriceType = "FIXED"
	PriceTypePercentageOff PriceType = "PERCENTAGE_OFF"
	PriceTypeAbsoluteOff   PriceType = "ABSOLUTE_OFF"
)

type ProductType string

const (
	ProductTypeProduct  ProductType = "product"
	ProductTypeVirtual ProductType = "virtual"
	ProductTypePosm    ProductType = "posm"
)
