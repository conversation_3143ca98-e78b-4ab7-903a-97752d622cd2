package products

import (
	"encoding/json"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/shared"
	inventoryDao "kc/internal/ondc/service/inventory/models/dao"
	"time"
)

// Product TODO: Id making changes in the future, then make changes in deep copy functions as well
type Product struct {
	ID                     uint                                                        `json:"id"`
	CategoryID             int                                                         `json:"category_id"`
	Name                   string                                                      `json:"name"`
	Code                   string                                                      `json:"code"`
	Rank                   *int                                                        `json:"rank"`
	CreatedAt              *time.Time                                                  `json:"created_at"`
	UpdatedAt              *time.Time                                                  `json:"updated_at"`
	Meta                   json.RawMessage                                             `json:"meta"`
	MetaProperties         shared.KiranaBazarProductMeta                               `json:"meta_properties" gorm:"-"`
	IsActive               *bool                                                       `json:"is_active"`
	ImageURLs              json.RawMessage                                             `json:"image_urls"`
	ProductImageURLs       []string                                                    `json:"product_image_urls" gorm:"-"`
	MediaUrls              json.RawMessage                                             `json:"media_urls,omitempty"` // Optional field for media URLs
	ProductMediaUrls       []dao.KiranaBazarProductMediaUrl                            `json:"product_media_urls" gorm:"-"`
	NameLabel              *string                                                     `json:"name_label"`
	CodeLabel              *string                                                     `json:"code_label"`
	RatingsSum             int                                                         `json:"ratings_sum"`
	RatingsCount           int                                                         `json:"ratings_count"`
	SizeVariantCode        *int                                                        `json:"size_variant_code"`
	IsDefault              *bool                                                       `json:"is_default"`
	IsOOS                  *bool                                                       `json:"is_oos"`
	SizeVariants           []*Product                                                  `json:"size_variants" gorm:"-"`
	Category               *Category                                                   `json:"category,omitempty"` // Embedded category details
	PopularityValue        float64                                                     `json:"popularity_value"`
	ProductEntities        []int                                                       `json:"product_entities" gorm:"-"`
	Seller                 string                                                      `json:"seller"`
	Source                 *string                                                     `json:"source"`
	InventoryQuantity      *int                                                        `json:"inventory_quantity"`
	LowStockThreshold      *int                                                        `json:"low_stock_threshold"`
	DisplayQuantity        *int                                                        `json:"display_quantity"`
	InventoryAnalysis      []*inventoryDao.KiranaBazarProductsInventoryAnalysisHistory `json:"inventory_analysis,omitempty" gorm:"-"`
	Manufacturer           string                                                      `json:"manufacturer"`
	ExtendedMeta           json.RawMessage                                             `json:"extended_meta"`
	ExtendedMetaProperties dao.KiranaBazarProductExtendedMeta                          `json:"extended_meta_properties" gorm:"-"`
	ProductType            string                                                      `json:"product_type"`
	GTVCalculationRate     *float64                                                    `json:"gtv_calculation_rate"`
	OriginalSkus           []*shared.KiranaBazarVirtualSkuMapping                      `json:"original_skus,omitempty" gorm:"-"`
}

type Category struct {
	ID        int        `json:"id"`
	Domain    string     `json:"domain"`
	SubDomain string     `json:"sub_domain"`
	Category  string     `json:"category"`
	Code      string     `json:"code"`
	ImageURL  *string    `json:"image_url"`
	IsActive  bool       `json:"is_active"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	Source    *string    `json:"source"`
}

type EntityMapping struct {
	ID        uint       `json:"id"`
	EntityId  uint       `json:"entity_id"`
	TargetId  string     `json:"target_id"`
	Type      string     `json:"type"` // product
	Tag       string     `json:"tag"`  // screen:products
	IsActive  bool       `json:"is_active"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
}

type ToSellerItemsCondition struct {
	IncludeVariant bool
	AnalysisType   []string
}

type ProductPricingRule struct {
	ID          uint            `json:"id" gorm:"primaryKey"`
	Name        string          `json:"name" gorm:"not null"`
	Description string          `json:"description"`
	RuleType    PricingRuleType `json:"rule_type" gorm:"type:enum('CONDITIONAL','TIER_BASED','HYBRID');not null"`
	Priority    uint            `json:"priority" gorm:"default:0"`
	IsActive    bool            `json:"is_active" gorm:"default:true"`
	ValidFrom   *int64          `json:"valid_from" gorm:"column:valid_from"`   // Epoch in milliseconds
	ValidUntil  *int64          `json:"valid_until" gorm:"column:valid_until"` // Epoch in milliseconds
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
	CreatedBy   string          `json:"created_by"`
	BrandShare  float64         `json:"brand_share" gorm:"default:0.0"` // Percentage share for brand

	// Loaded relationships
	Conditions []ProductPricingCondition `json:"conditions,omitempty" gorm:"foreignKey:PricingRuleID"`
	Tiers      []ProductPricingTier      `json:"tiers,omitempty" gorm:"foreignKey:PricingRuleID"`
}

type ProductPricingMapping struct {
	ID            uint      `json:"id" gorm:"primaryKey"`
	ProductID     uint      `json:"product_id" gorm:"not null;index:idx_product_active"`
	PricingRuleID uint      `json:"pricing_rule_id" gorm:"not null"`
	IsActive      bool      `json:"is_active" gorm:"default:true;index:idx_product_active"`
	CreatedAt     time.Time `json:"created_at"`

	// Relationships
	PricingRule ProductPricingRule `json:"pricing_rule,omitempty" gorm:"foreignKey:PricingRuleID"`
}

type ProductPricingCondition struct {
	ID              uint                     `json:"id" gorm:"primaryKey"`
	PricingRuleID   uint                     `json:"pricing_rule_id" gorm:"not null"`
	ConditionType   PricingConditionType     `json:"condition_type" gorm:"type:enum('USER_COHORT');not null"`
	Operator        PricingConditionOperator `json:"operator" gorm:"type:enum('EQUALS','NOT_EQUALS','IN','NOT_IN','CONTAINS');not null"`
	ConditionValue  json.RawMessage          `json:"condition_value" gorm:"type:json;not null"`
	LogicalOperator LogicalOperator          `json:"logical_operator" gorm:"type:enum('AND','OR');default:'AND'"`
	ConditionOrder  int                      `json:"condition_order" gorm:"default:0"`
}

type ProductPricingTier struct {
	ID            uint      `json:"id" gorm:"primaryKey"`
	PricingRuleID uint      `json:"pricing_rule_id" gorm:"not null"`
	TierName      string    `json:"tier_name" gorm:"size:100"`
	MinQuantity   int       `json:"min_quantity" gorm:"not null;default:0"`
	MaxQuantity   *int      `json:"max_quantity"` // NULL means unlimited
	PriceType     PriceType `json:"price_type" gorm:"type:enum('FIXED','PERCENTAGE_OFF','ABSOLUTE_OFF');not null"`
	PriceValue    float64   `json:"price_value" gorm:"type:decimal(10,2);not null"`
	Currency      string    `json:"currency" gorm:"size:3;default:'INR'"`
}

type ProductPricingResult struct {
	ProductID     uint                 `json:"product_id"`
	Quantity      int                  `json:"quantity"`
	BasePrice     float64              `json:"base_price"`
	ComputedPrice float64              `json:"computed_price"`
	Discount      float64              `json:"discount"`
	DiscountType  string               `json:"discount_type"`
	AppliedRules  []AppliedPricingRule `json:"applied_rules"`
	TierInfo      *PricingTierInfo     `json:"tier_info,omitempty"`
	ComputedAt    time.Time            `json:"computed_at"`
}

type AppliedPricingRule struct {
	RuleID      uint    `json:"rule_id"`
	RuleName    string  `json:"rule_name"`
	RuleType    string  `json:"rule_type"`
	PriceImpact float64 `json:"price_impact"`
	Description string  `json:"description"`
}

type PricingTierInfo struct {
	TierName    string `json:"tier_name"`
	MinQuantity int    `json:"min_quantity"`
	MaxQuantity *int   `json:"max_quantity"`
	CurrentTier bool   `json:"current_tier"`
	NextTierAt  *int   `json:"next_tier_at,omitempty"`
}

type PricingRuleWithCondition struct {
	RuleName        string          `json:"rule_name"`
	RuleType        PricingRuleType `json:"rule_type"`
	Priority        uint            `json:"priority"`
	IsActive        bool            `json:"is_active"`
	ValidFrom       *int64          `json:"valid_from"`
	ValidUntil      *int64          `json:"valid_until"`
	BrandShare      float64         `json:"brand_share"`
	ConditionID     uint            `json:"condition_id"`
	RuleID          uint            `json:"rule_id"`
	ConditionType   string          `json:"condition_type"`
	Operator        string          `json:"operator"`
	ConditionValue  []byte          `json:"condition_value"`
	LogicalOperator *string         `json:"logical_operator"`
	ConditionOrder  *int            `json:"condition_order"`
}

type PricingRuleWithTier struct {
	RuleName    string          `json:"rule_name"`
	RuleType    PricingRuleType `json:"rule_type"`
	Priority    uint            `json:"priority"`
	IsActive    bool            `json:"is_active"`
	ValidFrom   *int64          `json:"valid_from"`
	ValidUntil  *int64          `json:"valid_until"`
	BrandShare  float64         `json:"brand_share"`
	TierID      uint            `json:"tier_id"`
	RuleID      uint            `json:"rule_id"`
	TierName    string          `json:"tier_name"`
	MinQuantity int             `json:"min_quantity"`
	MaxQuantity *int            `json:"max_quantity"` // NULL means unlimited
	PriceType   string          `json:"price_type"`   // FIXED, PERCENTAGE_OFF, ABSOLUTE_OFF
	PriceValue  float64         `json:"price_value"`
}

func (ProductPricingMapping) TableName() string   { return "kiranabazar_products_pricing_mapping" }
func (ProductPricingRule) TableName() string      { return "kiranabazar_products_pricing_rules" }
func (ProductPricingCondition) TableName() string { return "kiranabazar_products_pricing_conditions" }
func (ProductPricingTier) TableName() string      { return "kiranabazar_products_pricing_tiers" }
