package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/external/loyalty"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	ordervalue "kc/internal/ondc/service/orderBill/orderValue"
	"kc/internal/ondc/service/orderStatus/constants"
	"kc/internal/ondc/service/products"
	"kc/internal/ondc/utils"
	"time"
)

func generateLoyaltyCashbackComponent(cashback float64) *dto.CashbackComponent {
	return &dto.CashbackComponent{
		Header: &dto.ComponentHeader{
			Url: "https://d2rstorage2.blob.core.windows.net/widget/February/12/bfa84eff-225d-407b-904b-7fc2e2b40011/1739368517289.webp",
		},
		Body: &dto.ComponentBody{
			Text:          "वॉलेट",
			PrimaryColor:  utils.StrPtr("rgba(189, 26, 234, 1)"),
			SecondaryText: fmt.Sprintf("उपलब्ध कैशबैक बैलेंस: ₹%0.2f", cashback),
			CashbackValue: cashback,
			SecondaryTextV2: &dto.WaysToPayText{
				Text: fmt.Sprintf("उपलब्ध कैशबैक बैलेंस: ₹%0.2f", cashback),
				HighlightText: []dto.Text{
					{
						Content: fmt.Sprintf("₹%0.2f", cashback),
						Style: dto.TextStyle{
							Color:      "#007E0D",
							FontWeight: "bold",
						},
					},
				},
			},
		},
		IconImageUrl: "https://d2rstorage2.blob.core.windows.net/widget/March/24/ce98c0db-96ac-4ce0-8313-237115a85973/1742809317873.webp",
		// Footer: &dto.ComponentFooter{
		// 	Text:    "₹1000 कैशबैक उपयोग करके 1000 रुपये अतिरिक्त बचाए।",
		// 	Icon:    "",
		// 	Color:   utils.StrPtr("rgba(22, 160, 10, 1)"),
		// 	IconSet: "",
		// },
		ActiveColor: utils.StrPtr("#6A21D0"),
	}
}

func generateLoyaltyRewardsComponent(loyaltRewardsData loyalty.LoyaltyRewardsResponse) *dto.LoyaltyRewardsComponent {
	headerText := fmt.Sprintf("इस ऑर्डर पर %s टियर के फायदे", loyaltRewardsData.TierName)
	loyaltyItems := []dto.LoyaltyRewardsBenefitsItem{
		{
			Icon: &dto.LoyaltyRewardIcon{
				Url: utils.StrPtr(loyaltRewardsData.CoinImageURL),
			},
			Content: dto.LoyaltyRewardsText{
				Text:  fmt.Sprintf("%d पॉइंट", loyaltRewardsData.Coins),
				Value: float64(loyaltRewardsData.Coins),
				Styles: &map[string]interface{}{
					"color": "#000000",
				},
			},
		},
	}

	if loyaltRewardsData.Cashback > 0 {
		text := fmt.Sprintf("%.2f%% कैशबैक = ₹%d", loyaltRewardsData.CashBackFactor, loyaltRewardsData.Cashback)
		if loyaltRewardsData.CashBackFactor == 0 {
			text = fmt.Sprintf("₹%d कैशबैक", loyaltRewardsData.Cashback)
		}
		loyaltyItems = append(loyaltyItems, dto.LoyaltyRewardsBenefitsItem{
			Icon: &dto.LoyaltyRewardIcon{
				Url: utils.StrPtr(loyaltRewardsData.CashbackImageURL),
			},
			Content: dto.LoyaltyRewardsText{
				Text:  text,
				Value: float64(loyaltRewardsData.Cashback),
				HighlightedText: []dto.HighlightedTextData{
					{
						Text: fmt.Sprintf("₹%d", loyaltRewardsData.Cashback),
						HighlightStyle: &map[string]interface{}{
							"color":      loyaltRewardsData.Colors.Primary,
							"fontWeight": "bold",
						},
					},
				},
				Styles: &map[string]interface{}{
					"color": "#000000",
				},
			},
		})
	}
	data := &dto.LoyaltyRewardsComponent{
		CurrentTier: loyaltRewardsData.CurrentTier,
		Header: &dto.ComponentFooter{
			Text:  headerText,
			Color: utils.StrPtr(loyaltRewardsData.Colors.Primary),
		},
		Body: loyaltyItems,
		Config: map[string]interface{}{
			"gradient_colors": []string{loyaltRewardsData.Colors.Gradient.Start, loyaltRewardsData.Colors.Gradient.End},
			"color":           loyaltRewardsData.Colors.Primary,
		},
	}
	return data
}

func (s *Service) GetUserCashbackBalance(ctx context.Context, request dto.GetUserCashbackBalanceRequest) (dto.GetUserCashbackBalanceResponse, error) {
	cashbackBalance := loyalty.GetCashbackBalanceResponse{}
	requestObject := map[string]interface{}{
		"data": map[string]interface{}{
			"uid":        request.UserID,
			"appVersion": request.Meta.AppVersion,
			"deviceId":   "a1d4g6hh",
		},
	}
	resp, err := loyalty.CallLoyaltyAPI("GET_CASHBACK_BALANCE", requestObject, nil)
	if err != nil {
		return dto.GetUserCashbackBalanceResponse{}, err
	}
	err = json.Unmarshal(resp, &cashbackBalance)
	if err != nil {
		return dto.GetUserCashbackBalanceResponse{}, err
	}
	return dto.GetUserCashbackBalanceResponse{
		Data: dto.GetUserCashbackBalanceData{
			Cashback: &cashbackBalance.Cashback,
		},
		Meta: request.Meta,
	}, nil
}

func (s *Service) RedeemUserCashback(ctx context.Context, request dto.RedeemUserCashbackRequest) (dto.RedeemUserCashbackResponse, error) {
	requestObject := map[string]interface{}{
		"data": map[string]interface{}{
			"uid":          request.UserID,
			"appVersion":   request.Meta.AppVersion,
			"deviceId":     request.Meta.DeviceId,
			"orderAmount":  request.Data.OrderAmount,
			"sourceId":     request.Data.SourceId,
			"orderId":      request.Data.OrderId,
			"redeemAmount": request.Data.RedeemCashbackAmount,
		},
	}
	_, err := loyalty.CallLoyaltyAPI("REDEEM_CASHBACK", requestObject, nil)
	if err != nil {
		return dto.RedeemUserCashbackResponse{}, err
	}
	return dto.RedeemUserCashbackResponse{
		Meta:    request.Meta,
		Error:   "",
		Message: "User Cashback Redeemed Successfully",
	}, nil
}

func (s *Service) RevertUserCashback(ctx context.Context, request dto.RevertUserCashbackRequest) (dto.RevertUserCashbackResponse, error) {
	requestObject := map[string]interface{}{
		"data": map[string]interface{}{
			"uid":        request.UserID,
			"orderId":    request.Data.OrderId,
			"appVersion": request.Meta.AppVersion,
			"deviceId":   request.Meta.DeviceId,
		},
	}
	_, err := loyalty.CallLoyaltyAPI("REVERT_CASHBACK", requestObject, nil)
	if err != nil {
		return dto.RevertUserCashbackResponse{}, err
	}
	return dto.RevertUserCashbackResponse{
		Meta:    request.Meta,
		Error:   "",
		Message: "User Cashback Reverted Successfully",
	}, nil
}

func (s *Service) AddUserLoyaltyPoints(ctx context.Context, request dto.AddUserLoyaltyPointsRequest) (dto.AddUserLoyaltyPointsResponse, error) {
	orderCount, err := ordervalue.GetUserLevelOrderCount(request.UserID)
	if err != nil {
		fmt.Println("error in getting user order count", err)
	}
	isFirstTimeUser := false
	if orderCount == 0 {
		isFirstTimeUser = true
	}
	requestObject := map[string]interface{}{
		"data": map[string]interface{}{
			"uid":             request.UserID,
			"appVersion":      request.Meta.AppVersion,
			"deviceId":        request.Meta.DeviceId,
			"sourceId":        request.Data.SourceId,
			"orderAmount":     request.Data.OrderAmount,
			"orderId":         request.Data.OrderId,
			"isFirstTimeUser": isFirstTimeUser,
			"platform":        request.Meta.Platform,
		},
	}
	_, err = loyalty.CallLoyaltyAPI("ADD_LOYALTY_REWARDS", requestObject, nil)
	if err != nil {
		return dto.AddUserLoyaltyPointsResponse{}, err
	}
	return dto.AddUserLoyaltyPointsResponse{
		Meta:    request.Meta,
		Error:   "",
		Message: "User Loyalty Points Added Successfully",
	}, nil
}

func (s *Service) ActivateUserLoyaltyRewards(ctx context.Context, request dto.ActivateUserLoyaltyRewardsRequest) (dto.ActivateUserLoyaltyRewardsResponse, error) {
	requestObject := map[string]interface{}{
		"data": map[string]interface{}{
			"uid":        request.UserID,
			"appVersion": request.Meta.AppVersion,
			"deviceId":   request.Meta.DeviceId,
			"orderId":    request.Data.OrderId,
		},
	}
	_, err := loyalty.CallLoyaltyAPI("ACTIVATE_LOYALTY_REWARDS", requestObject, nil)
	if err != nil {
		return dto.ActivateUserLoyaltyRewardsResponse{}, err
	}
	return dto.ActivateUserLoyaltyRewardsResponse{
		Meta:    request.Meta,
		Error:   "",
		Message: "User Loyalty Rewards Activated Successfully",
	}, nil
}

func (s *Service) DeactivateUserLoyaltyRewards(ctx context.Context, request dto.ActivateUserLoyaltyRewardsRequest) (dto.ActivateUserLoyaltyRewardsResponse, error) {
	requestObject := map[string]interface{}{
		"data": map[string]interface{}{
			"uid":        request.UserID,
			"appVersion": request.Meta.AppVersion,
			"deviceId":   request.Meta.DeviceId,
			"orderId":    request.Data.OrderId,
		},
	}
	_, err := loyalty.CallLoyaltyAPI("DEACTIVATE_LOYALTY_REWARDS", requestObject, nil)
	if err != nil {
		return dto.ActivateUserLoyaltyRewardsResponse{}, err
	}
	return dto.ActivateUserLoyaltyRewardsResponse{
		Meta:    request.Meta,
		Error:   "",
		Message: "User Loyalty Rewards Deactivated Successfully",
	}, nil
}

func (s *Service) CreateUserLoyaltyRewardOrder(ctx context.Context, request dto.CreateUserLoyaltyOrderRequest) (*dto.AppKiranaBazarOrderResponse, error) {
	// get productIds from request and create loyalty order from backend

	// get user default address id
	shippingAddressId := ""
	if request.Data.AddressId != nil {
		shippingAddressId = *request.Data.AddressId
	} else {
		userAddressResponse, err := s.GetUserAddress(ctx, &dto.GetUserAddressRequest{
			UserID: request.UserID,
			Data: dto.AppServiceAbilityAPIRequestData{
				Seller: utils.KIRANACLUB_LOYALTY_REWARDS,
				Source: utils.KIRANACLUB_LOYALTY_REWARDS,
			},
		})
		if err != nil {
			return nil, err
		}

		if len(userAddressResponse.Address) == 0 {
			err = errors.New("user cannot place order")
			return nil, err
		}
		shippingAddressId = fmt.Sprint(userAddressResponse.Address[0].ID)
	}
	// generate cart object
	cart := make([]shared.SellerItems, 0)
	for _, productId := range request.Data.ProductIds {
		productInfo, exists := products.GetProductByID(productId)
		if !exists {
			continue
		}
		productCartObject := productInfo.ToSellerItems(products.ToSellerItemsCondition{
			IncludeVariant: true,
		}, request.Meta.AppVersion, nil)
		//TODO: To use conditional pricing we need to pass userContext
		productCartObject.Quantity = 1
		cart = append(cart, productCartObject)
	}

	requestObject := dto.BillDifferenceRequest{
		Meta: request.Meta,
		Data: dto.BillDifferenceData{
			GetBillDetailsData: dto.GetBillDetailsData{
				Seller:    utils.KIRANACLUB_LOYALTY_REWARDS,
				Source:    utils.KIRANACLUB_LOYALTY_REWARDS,
				AddressID: shippingAddressId,
				Cart:      cart,
			},
		},
		UserID: request.UserID,
	}

	resp, err := s.CreateThirdPartySellerOrder(ctx, requestObject)
	if err != nil {
		return nil, err
	}

	// confirm the order if placed succesfully
	confirmOrderResp, err := s.UpdateB2BOrderStatus(ctx, &dto.UpdateB2BOrderStatusRequest{
		UpdatedBy: "BACKEND",
		Data: dto.UpdateB2BOrderStatusData{
			OrderID:        resp.Data[0].ID,
			UpdatedBy:      "BACKEND",
			Source:         "BACKEND",
			OrderStatus:    constants.CONFIRMED,
			UpdatedAt:      time.Now().UnixMilli(),
			OrderingModule: utils.KIRANACLUB_LOYALTY_REWARDS,
		},
	})
	if err != nil {
		return nil, err
	}

	if confirmOrderResp == nil {
		return nil, errors.New("failed to confirm order")
	}

	return &resp, nil
}

func (s *Service) GetUserTierInfo(ctx context.Context, request *dto.GetUserTierInfoRequest) (interface{}, error) {
	if request == nil {
		return nil, errors.New("request cannot be nil")
	}

	data, ok := request.Data.(map[string]interface{})
	if !ok {
		return nil, errors.New("invalid data format in request")
	}

	email, exists := data["email"].(string)
	if !exists || email == "" {
		return nil, errors.New("email is required to get user tier info")
	}

	csAgent, err := s.GetAgentByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("failed to get CS agent: %w", err)
	}

	deviceId := csAgent.ID

	data["deviceId"] = deviceId
	requestObject := map[string]interface{}{
		"data": data,
	}

	resp, err := loyalty.CallLoyaltyAPI("GET_USER_TIER_INFO", requestObject, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call loyalty API: %w", err)
	}
	var response interface{}
	err = json.Unmarshal(resp, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal loyalty API response: %w", err)
	}
	return response, nil
}

func (s *Service) GetUsersOrderHistory(ctx context.Context, request *dto.GetUsersOrderHistoryRequest) (interface{}, error) {
	if request == nil {
		return nil, errors.New("request cannot be nil")
	}

	data, ok := request.Data.(map[string]interface{})
	if !ok {
		return nil, errors.New("invalid data format in request")
	}

	email, exists := data["email"].(string)
	if !exists || email == "" {
		return nil, errors.New("email is required to get user order history")
	}

	csAgent, err := s.GetAgentByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("failed to get CS agent: %w", err)
	}

	deviceId := csAgent.ID

	data["deviceId"] = deviceId
	requestObject := map[string]interface{}{
		"data": data,
	}

	resp, err := loyalty.CallLoyaltyAPI("ORDERS_HISTORY", requestObject, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call loyalty API: %w", err)
	}
	var response interface{}
	err = json.Unmarshal(resp, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal loyalty API response: %w", err)
	}
	return response, nil
}

func (s *Service) GetUsersTierUpgradeHistory(ctx context.Context, request *dto.GetUsersTierUpgradeHistoryRequest) (interface{}, error) {
	if request == nil {
		return nil, errors.New("request cannot be nil")
	}

	data, ok := request.Data.(map[string]interface{})
	if !ok {
		return nil, errors.New("invalid data format in request")
	}

	email, exists := data["email"].(string)
	if !exists || email == "" {
		return nil, errors.New("email is required to get user tier upgrade history")
	}

	csAgent, err := s.GetAgentByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("failed to get CS agent: %w", err)
	}

	deviceId := csAgent.ID

	data["deviceId"] = deviceId
	requestObject := map[string]interface{}{
		"data": data,
	}

	resp, err := loyalty.CallLoyaltyAPI("TIER_UPGRADE_HISTORY", requestObject, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call loyalty API: %w", err)
	}
	var response interface{}
	err = json.Unmarshal(resp, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal loyalty API response: %w", err)
	}
	return response, nil
}

func (s *Service) GetUsersCoinsWallet(ctx context.Context, request *dto.GetUsersCoinsWalletRequest) (interface{}, error) {
	if request == nil {
		return nil, errors.New("request cannot be nil")
	}

	data, ok := request.Data.(map[string]interface{})
	if !ok {
		return nil, errors.New("invalid data format in request")
	}

	email, exists := data["email"].(string)
	if !exists || email == "" {
		return nil, errors.New("email is required to get user coins wallet")
	}

	csAgent, err := s.GetAgentByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("failed to get CS agent: %w", err)
	}

	deviceId := csAgent.ID

	data["deviceId"] = deviceId
	requestObject := map[string]interface{}{
		"data": data,
	}

	resp, err := loyalty.CallLoyaltyAPI("GET_COINS_WALLET", requestObject, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call loyalty API: %w", err)
	}
	var response interface{}
	err = json.Unmarshal(resp, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal loyalty API response: %w", err)
	}
	return response, nil
}

func (s *Service) GetUsersCashbackWallet(ctx context.Context, request *dto.GetUsersCashbackWalletRequest) (interface{}, error) {
	if request == nil {
		return nil, errors.New("request cannot be nil")
	}

	data, ok := request.Data.(map[string]interface{})
	if !ok {
		return nil, errors.New("invalid data format in request")
	}

	email, exists := data["email"].(string)
	if !exists || email == "" {
		return nil, errors.New("email is required to get user cashback wallet")
	}

	csAgent, err := s.GetAgentByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("failed to get CS agent: %w", err)
	}

	deviceId := csAgent.ID

	data["deviceId"] = deviceId
	requestObject := map[string]interface{}{
		"data": data,
	}

	resp, err := loyalty.CallLoyaltyAPI("GET_CASHBACK_WALLET", requestObject, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call loyalty API: %w", err)
	}
	var response interface{}
	err = json.Unmarshal(resp, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal loyalty API response: %w", err)
	}
	return response, nil
}
