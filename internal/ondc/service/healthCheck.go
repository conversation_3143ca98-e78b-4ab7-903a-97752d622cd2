package service

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"io/ioutil"
	"kc/internal/ondc/infrastructure/logging"
	"net/http"
	"strings"
)

var logger = logging.New("dto")

func (s *Service) HealthCheck(ctx *gin.Context) error {
	url := "https://staging.registry.ondc.org/ondc/vlookup"
	method := "POST"

	payload := strings.NewReader(`{
"sender_subscriber_id": "ondc.retailpulse.ai",
"request_id": "27baa06d-f90a-486c-85e5-cc621b787f04",
"timestamp": "2022-09-13T20:45:07.060Z",
"signature": "vFbEBrxqMfANQZmaNzkNat1zk8wJjOFCURcxy6rw76ONzICSRvpylCiefL/ogpCBiBmKKFmJFwgvslPHuZqBCw==",
"search_parameters": {
"country": "IND",
"domain": "ONDC:RET10",
"type": "buyerApp",
"city":"std:080",
"subscriber_id": "ondc.retailpulse.ai"
}
}`)

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payload)

	if err != nil {
		fmt.Println(err)
		return err
	}
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return err
	}
	fmt.Println(string(body))
	return nil
}
