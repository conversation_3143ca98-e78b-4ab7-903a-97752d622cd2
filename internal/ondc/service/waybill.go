package service

import (
	"context"
	"errors"
	"fmt"
	"kc/internal/ondc/models/dto"
	awbMasterDTO "kc/internal/ondc/service/awbMaster/dto"
	"kc/internal/ondc/service/orderStatus/constants"
	delhiverystatus "kc/internal/ondc/service/orderStatus/delhiveryStatus"
	ekartstatus "kc/internal/ondc/service/orderStatus/ekart"
	shipdelightstatus "kc/internal/ondc/service/orderStatus/shipdelightStatus"
	"slices"
	"strings"
)

type WayBillTempName struct {
	Couriername string
	UpdatedBy   string
}

// fetchAndUpdatePrimaryWaybill updated the primary awb number for the orderid.
func (s *Service) fetchAndUpdatePrimaryWaybill(orderID uint64, updatedBy string) {
	primaryWaybills, err := s.AWBMaster.GetPrimaryWaybillForOrderID(context.Background(), []uint64{orderID})
	if err != nil {
		return
	}
	primaryWaybill := ""
	courier := ""
	for _, pwb := range primaryWaybills {
		if pwb.AWBNumber != nil {
			primaryWaybill = *pwb.AWBNumber
			courier = *pwb.Courier
		}
	}
	if primaryWaybill != "" {
		s.UpdatePrimaryWaybill(context.Background(), &dto.UpdatePrimaryWaybillRequest{
			AWBNumber: primaryWaybill,
			UpdatedBy: updatedBy,
			Comment:   "Updated via webhook",
		})
		// updating the status in DB for order level awb number
		s.repository.CustomQuery(nil, fmt.Sprintf(`update kiranabazar_order_status kos set awb_number = "%s", courier = "%s" where id = %d;`, primaryWaybill, courier, orderID))
		// updating the orderStatus for primaryWaybill in kiranabazar_orders_table i.e calling b2borderStatus
		s.UpdateOrderStatusForPrimaryWaybill(context.Background(), primaryWaybill, updatedBy)
	}
}

func (s *Service) UpdatePrimaryWaybill(ctx context.Context, request *dto.UpdatePrimaryWaybillRequest) (interface{}, error) {
	if request.AWBNumber == "" {
		return "invalid waybill number", errors.New("invalid waybill number")
	}
	awbD, err := s.AWBMaster.GetWaybillDetails(context.Background(), request.AWBNumber, 0)
	if err != nil {
		return nil, err
	}
	requestAwbDetails := awbD[0]
	isMutable := true

	awbDetails, err := s.AWBMaster.GetWaybillDetails(context.Background(), "", *requestAwbDetails.OrderID)
	if err != nil {
		return nil, err
	}
	var orderID uint64
	for _, awb := range awbDetails {
		orderID = *awb.OrderID
		if *awb.AWBNumber == request.AWBNumber {
			requestAwbDetails = awb
		}
		if awb.IsMutable != nil && !*awb.IsMutable {
			isMutable = false
			break
		}
	}

	if !isMutable && !strings.Contains(request.UpdatedBy, "kirana.club") {
		return "awbnumber is immutable", nil
	}

	if requestAwbDetails.IsPrimary != nil && *requestAwbDetails.IsPrimary {
		return "waybill already a primary", nil
	}
	err = s.AWBMaster.UpdatePrimaryWaybill(context.Background(), awbMasterDTO.UpdatePrimaryRequest{
		AWBNumber: request.AWBNumber,
		UpdatedBy: request.UpdatedBy,
	})
	if err != nil {
		return nil, err
	}
	// if the primary waybill is updated then also updated the order status
	// updating the status in DB for order level awb number
	courier := *requestAwbDetails.Courier

	s.repository.CustomQuery(nil, fmt.Sprintf(`update kiranabazar_order_status kos set awb_number = "%s", courier = "%s" where id = %d;`, request.AWBNumber, courier, orderID))
	// updating the orderStatus for primaryWaybill in kiranabazar_orders_table i.e calling b2borderStatus
	s.UpdateOrderStatusForPrimaryWaybill(context.Background(), request.AWBNumber, request.UpdatedBy)

	return nil, nil
}

func (s *Service) UpdateOrderStatusForPrimaryWaybill(ctx context.Context, waybill string, updatedBy string) (any, error) {
	if updatedBy == constants.SHIPDELIGHT_WEBHOOK {
		s.HandleShipDelightUpdateOrderStatusForPrimaryWaybill(ctx, waybill, updatedBy)
	} else if updatedBy == "DELHIVERY" || updatedBy == "DELHIVERY_WEBHOOK" {
		s.HandleDelhiveryUpdateOrderStatusForPrimaryWaybill(ctx, waybill, updatedBy)
	} else if updatedBy == "EKART_WEBHOOK" {
		s.HandleEkartUpdateOrderStatusForPrimaryWaybill(ctx, waybill, updatedBy)
	} else {
		return "status not updatable", nil
	}
	return nil, nil
}

func (s *Service) HandleEkartUpdateOrderStatusForPrimaryWaybill(ctx context.Context, waybill string, updatedBy string) (*dto.ShipwayWebhookResponse, error) {
	waybillDetails, err := s.AWBMaster.GetWaybillDetails(context.Background(), waybill, 0)
	if err != nil {
		return nil, err
	}
	if len(waybillDetails) == 0 {
		return nil, errors.New("no such waybill exists")
	}
	waybillDetail := waybillDetails[0]

	if waybillDetail.NSLCode == nil {
		return nil, errors.New("nslCode cannot be empty for ekart")
	}

	// Valid status codes using ekartstatus constants
	if slices.Contains(ekartstatus.ValidEkartStatus, *waybillDetail.NSLCode) {
		orderInfo, err := GetOrderInfo(s.repository, int64(*waybillDetail.OrderID))
		if err != nil {
			return nil, errors.New("order does not exists")
		}
		// checking if the status is same or not which is in DB
		if waybillDetail.NSLCode != nil && *waybillDetail.NSLCode == *orderInfo.OrderStatus {
			return &dto.ShipwayWebhookResponse{
				Message: "order status already updated",
			}, nil
		}
		_, err = s.UpdateB2BOrderStatus(ctx, &dto.UpdateB2BOrderStatusRequest{
			UpdatedBy: updatedBy,
			Data: dto.UpdateB2BOrderStatusData{
				OrderID:         fmt.Sprintf("%d", *orderInfo.ID),
				UpdatedBy:       *waybillDetail.Courier,
				OrderStatus:     *waybillDetail.NSLCode,
				OrderStatusType: *waybillDetail.StatusType,
				UpdatedAt:       *waybillDetail.LastStatusUpdatedAt,
				OrderingModule:  orderInfo.Seller,
				Source:          updatedBy,
				OrderMeta: dto.OrderMeta{
					Courier:   *waybillDetail.Courier,
					AWBNumber: *waybillDetail.AWBNumber,
					Note:      *waybillDetail.Instructions,
				},
			},
		})
		if err != nil {
			return nil, err
		}
	}
	return nil, nil
}

func (s *Service) HandleShipDelightUpdateOrderStatusForPrimaryWaybill(ctx context.Context, waybill string, updatedBy string) (*dto.ShipwayWebhookResponse, error) {

	waybillDetails, err := s.AWBMaster.GetWaybillDetails(context.Background(), waybill, 0)
	if err != nil {
		return nil, err
	}
	if len(waybillDetails) == 0 {
		return nil, errors.New("no such waybill exists")
	}
	waybillDetail := waybillDetails[0]

	if waybillDetail.StatusCode == nil {
		return nil, errors.New("statusCode cannot be empty for shipdelight")
	}

	// Valid status codes using shipdelightstatus constants
	if includes(shipdelightstatus.ValidStatusCodes, *waybillDetail.StatusCode) {
		orderInfo, err := GetOrderInfo(s.repository, int64(*waybillDetail.OrderID))
		if err != nil {
			return nil, errors.New("order does not exists")
		}
		// checking if the status is same or not which is in DB
		if waybillDetail.StatusCode != nil && *waybillDetail.StatusCode == *orderInfo.OrderStatus {
			return &dto.ShipwayWebhookResponse{
				Message: "order status already updated",
			}, nil
		}
		_, err = s.UpdateB2BOrderStatus(ctx, &dto.UpdateB2BOrderStatusRequest{
			UpdatedBy: updatedBy,
			Data: dto.UpdateB2BOrderStatusData{
				OrderID:         fmt.Sprintf("%d", *orderInfo.ID),
				UpdatedBy:       *waybillDetail.Courier,
				OrderStatus:     *waybillDetail.StatusCode,
				OrderStatusType: *waybillDetail.StatusType,
				UpdatedAt:       *waybillDetail.LastStatusUpdatedAt,
				OrderingModule:  orderInfo.Seller,
				Source:          "SHIPDELIGHT",
				OrderMeta: dto.OrderMeta{
					Courier:   *waybillDetail.Courier,
					AWBNumber: *waybillDetail.AWBNumber,
					Note:      *waybillDetail.Instructions,
				},
			},
		})
		if err != nil {
			return nil, err
		}
	}
	return nil, nil
}

func (s *Service) HandleDelhiveryUpdateOrderStatusForPrimaryWaybill(ctx context.Context, waybill string, updatedBy string) (*dto.ShipwayWebhookResponse, error) {

	waybillDetails, err := s.AWBMaster.GetWaybillDetails(context.Background(), waybill, 0)
	if err != nil {
		return nil, err
	}
	if len(waybillDetails) == 0 {
		return nil, errors.New("no such waybill exists")
	}
	waybillDetail := waybillDetails[0]

	if (waybillDetail.StatusType != nil && *waybillDetail.StatusType != "UD") || (waybillDetail.StatusType != nil && *waybillDetail.StatusType == "UD" && contains([]string{delhiverystatus.XPROM, delhiverystatus.DTUP212, delhiverystatus.CSCSL, delhiverystatus.FMPUR101, delhiverystatus.XDWS, delhiverystatus.XDLL2F, delhiverystatus.ST114, delhiverystatus.XUNEX, delhiverystatus.XAWD, delhiverystatus.XDBL1F, delhiverystatus.PL105, delhiverystatus.XILL2F, delhiverystatus.XOLL2F, delhiverystatus.DTUP219, delhiverystatus.ST105, delhiverystatus.ST110, delhiverystatus.DTUP207, delhiverystatus.DTUP205, delhiverystatus.DTUP213, delhiverystatus.XPNP, delhiverystatus.XPIOM, delhiverystatus.DTUP231, delhiverystatus.DTUP235, delhiverystatus.ST107, delhiverystatus.DTUP203, delhiverystatus.CS104, delhiverystatus.CS101, delhiverystatus.DTUP209, delhiverystatus.LFIN, delhiverystatus.FMOFP101, delhiverystatus.FMEOD152, delhiverystatus.XUCI, delhiverystatus.XDDD3FD, delhiverystatus.EOD11, delhiverystatus.DLYDC109, delhiverystatus.DLYMR118, delhiverystatus.DLYMPS101, delhiverystatus.EOD6, delhiverystatus.XIBD3F, delhiverystatus.DTUP210, delhiverystatus.EOD3, delhiverystatus.DLYLH105, delhiverystatus.DLYRG135, delhiverystatus.DLYSU100, delhiverystatus.EOD104, delhiverystatus.EOD111, delhiverystatus.EOD43, delhiverystatus.EOD15, delhiverystatus.EOD74, delhiverystatus.XPPOM, delhiverystatus.EOD86, delhiverystatus.EOD3, delhiverystatus.EOD40, delhiverystatus.DLYLH126, delhiverystatus.DOFF128, delhiverystatus.DLYB2B101, delhiverystatus.DLYDC102, delhiverystatus.EOD69, delhiverystatus.ST102, delhiverystatus.EOD105, delhiverystatus.DLYDG119, delhiverystatus.DLYLH151, delhiverystatus.DLYLH152, delhiverystatus.DLYRG125, delhiverystatus.DLYHD007, delhiverystatus.DLYDC107, delhiverystatus.XNSZ, delhiverystatus.DLYDC101, delhiverystatus.XILL1F, delhiverystatus.ST116, delhiverystatus.ST115, delhiverystatus.EOD6O, delhiverystatus.XSC, delhiverystatus.CL106, delhiverystatus.ST108, delhiverystatus.RT108, delhiverystatus.ST6W, delhiverystatus.RT109, delhiverystatus.RDPD20, delhiverystatus.ST107, delhiverystatus.ST105, delhiverystatus.CL105, delhiverystatus.DLYSU100}, *waybillDetail.NSLCode)) {
		orderInfo, err := GetOrderInfo(s.repository, int64(*waybillDetail.OrderID))
		if err != nil {
			return nil, errors.New("order does not exists")
		}
		// checking if the status is same or not which is in DB
		if waybillDetail.NSLCode != nil && *waybillDetail.NSLCode == *orderInfo.OrderStatus {
			return &dto.ShipwayWebhookResponse{
				Message: "order status already updated",
			}, nil
		}
		_, err = s.UpdateB2BOrderStatus(ctx, &dto.UpdateB2BOrderStatusRequest{
			UpdatedBy: updatedBy,
			Data: dto.UpdateB2BOrderStatusData{
				OrderID:         fmt.Sprintf("%d", *orderInfo.ID),
				UpdatedBy:       *waybillDetail.Courier,
				OrderStatus:     *waybillDetail.NSLCode,
				OrderStatusType: *waybillDetail.StatusType,
				UpdatedAt:       *waybillDetail.LastStatusUpdatedAt,
				OrderingModule:  orderInfo.Seller,
				Source:          "DELHIVERY",
				OrderMeta: dto.OrderMeta{
					Courier:   *waybillDetail.Courier,
					AWBNumber: *waybillDetail.AWBNumber,
					Note:      *waybillDetail.Instructions,
				},
			},
		})
		if err != nil {
			return nil, err
		}
	}
	return nil, nil
}
