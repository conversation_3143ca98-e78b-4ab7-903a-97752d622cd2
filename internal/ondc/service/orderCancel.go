package service

import (
	"context"
	"errors"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/external/whatsapp"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/service/inventory"
	inventoryDao "kc/internal/ondc/service/inventory/models/dao"
	orderreason "kc/internal/ondc/service/orderReason"
	cancelreason "kc/internal/ondc/service/orderReason/cancelReason"
	orderS "kc/internal/ondc/service/orderStatus"
	"kc/internal/ondc/service/orderStatus/constants"
	"kc/internal/ondc/utils"
	"strconv"
	"time"

	"github.com/mixpanel/mixpanel-go"
	"go.uber.org/zap"
)

func (s *Service) CancelKiranaBazarOrder(ctx context.Context, request dto.AppCancelKiranaBazarOrderRequest) (response dto.AppCancelKiranaBazarOrderResponse, err error) {
	orderID := request.Data.OrderID
	if orderID == "" {
		err = errors.New("orderID cannot be empty")
		return
	}

	orderid, err := strconv.Atoi(orderID)
	if err != nil {
		err = errors.New("orderID is not defined")
		return
	}
	orderid64 := int64(orderid)
	orderInfo, err := GetOrderInfo(s.repository, orderid64)
	if err != nil {
		return
	}
	orderDetails, err := GetOrderDetails(s.repository, orderID)
	if err != nil {
		return
	}

	orderShippingDetails, _ := GetOrderStatus(s.repository, orderid64)

	if orderInfo.DisplayStatus == "CANCELLED" {
		err = errors.New("order is already cancelled")
		return
	}

	cartItems := orderDetails.Cart

	var courier string
	if orderShippingDetails.Courier != nil {
		courier = *orderShippingDetails.Courier
	}
	canCancelOrderFlag := canCancelOrder(orderInfo.DisplayStatus, orderInfo.Seller, courier, request.Data.Email)
	if !canCancelOrderFlag {
		err = errors.New("order cancellation conditions not met")
		return
	}

	cancelledOrderStatus := "CANCELLED"
	orderStatuses := orderS.MapOrderStatus(cancelledOrderStatus, request.Data.StatusType, orderS.OrderStatusResponse{})
	_, _, err = s.repository.Update(dao.KiranaBazarOrder{
		ID: &orderid64,
	}, dao.KiranaBazarOrder{
		OrderStatus:      &cancelledOrderStatus,
		UpdatedAt:        time.Now(),
		DeliveryStatus:   orderStatuses.ShipmentStatus,
		DisplayStatus:    orderStatuses.DisplayStatus,
		ProcessingStatus: orderStatuses.ProcessingStatus,
	})
	if err != nil {
		return
	}

	_, err = s.RefundAdvancePayment(ctx, dto.RefundPaymentApiRequest{
		UserID:  request.UserID,
		OrderID: orderid64,
		Amount:  -1,
		Reason:  utils.StrPtr(constants.CANCELLED),
	})
	if err != nil {
		logger.Warn(ctx, "failed to refund payment", zap.Error(err))
	}

	_, err = s.RevertUserCashback(ctx, dto.RevertUserCashbackRequest{
		UserID: request.UserID,
		Meta:   request.Meta,
		Data: dto.RevertUserUserCashbackData{
			OrderId: orderid64,
		},
	})
	if err != nil {
		logger.Error(ctx, "failed to revert user cashback", zap.Error(err))
	}

	orderPaymentsDetails := dao.KiranaBazarOrderPayment{}
	s.repository.Find(map[string]interface{}{
		"order_id": orderid64,
	}, &orderPaymentsDetails)

	source := "APP"
	if request.Data.Email == "IVR" {
		source = "AUTOMATION"
	} else if (request.Data.Email != "") && (request.Data.Source != "") {
		source = request.Data.Source
	} else if request.Data.Email != "" {
		source = "D2R"
	}

	cancelReasonMap := orderreason.GetOrderCancelCodeFromReason(request.Data.Reason)

	eventObject := map[string]interface{}{
		"distinct_id":              request.UserID,
		"order_id":                 orderid64,
		"cart_value":               int(orderDetails.GetCartValue()),
		"order_value":              int(orderDetails.GetOrderValue()),
		"reason":                   request.Data.Reason,
		"cancellation_reason":      cancelReasonMap.ReasonString,
		"cancellation_reason_code": cancelReasonMap.ReasonCode,
		"seller":                   orderInfo.Seller,
		"explaination":             request.Data.Explanation,
		"source":                   source,
		"ordering_module":          utils.MakeTitleCase(orderDetails.Seller),
	}

	// write this properly
	if cancelReasonMap.ReasonCode == cancelreason.PNS || cancelReasonMap.ReasonCode == cancelreason.SIS {
		whatsapp.SendOrderCancelledMessage(*orderDetails.ShippingAddress.Phone, int(orderDetails.GetOrderValue()), fmt.Sprintf("KC_%06d", orderid64))
	}

	if request.Data.Email != "" {
		eventObject["email"] = request.Data.Email
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Cancelled", request.UserID, eventObject,
			fmt.Sprintf("%s_%d", "order_cancelled", orderid64)),
	})

	err = webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{request.UserID},
		EventName:   "Order Cancelled",
		EventObject: eventObject,
	})
	if err != nil {
		fmt.Println("failed to send webengage event")
	}

	// updating kiranaBazarCallStatus
	err = UpdateCallStatus(s.repository, request.Data.OrderID, "", request.Data.Explanation, request.Data.Email, time.Now().UnixMilli(), request.Data.Reason, "", "")
	if err != nil {
		return
	}

	// this is go routine fired to call external api to update the widget
	go func(uid, orderid, seller, orderStatus string, orderDetails *dao.KiranaBazarOrderDetails, orderid64 int64) {

		s.DeactivateUserLoyaltyRewards(context.Background(), dto.ActivateUserLoyaltyRewardsRequest{
			UserID: request.UserID,
			Data: dto.ActivateUserLoyaltyRewardsRequestData{
				OrderId: orderid64,
			},
		})

		// call this API again for zoff_foods or RSB for thailand scheme
		// reqObject := map[string]interface{}{
		// 	"user_id":  request.UserID,
		// 	"order_id": request.Data.OrderID,
		// 	"amount":   orderDetails.TotalAmount,
		// 	"status":   constants.CANCELLED,
		// }
		// utils.CallExternalAPIAsync(utils.PROGRESS_WIDGET_RESOLVER_API, "POST", reqObject, nil)
		s.UpdateProgressWidget(context.Background(), &dto.UpdateProgressWidgetRequest{
			UserID: uid,
			Data: dto.UpdateProgressWidgetData{
				OrderID:      orderid,
				Status:       orderStatus,
				Amount:       orderDetails.TotalAmount,
				OrderDetails: orderDetails,
				Seller:       seller,
			},
		})
	}(request.UserID, request.Data.OrderID, orderInfo.Seller, constants.CANCELLED, orderDetails, orderid64)

	go func(mp *mixpanelRepo.Repository, userID string, orderID int64, orderValue int, s *Service, note, email string, source string, seller string, orderStatuses orderS.OrderStatusResponse) {
		trackingObject := map[string]interface{}{
			"distinct_id":             userID,
			"order_id":                orderID,
			"order_value":             orderValue,
			"status":                  constants.CANCELLED,
			"notes":                   note,
			"ordering_module":         utils.MakeTitleCase(seller),
			"seller":                  seller,
			"email":                   email,
			"source":                  source,
			"shipment_status":         orderStatuses.ShipmentStatus,
			"processing_status":       orderStatuses.ProcessingStatus,
			"display_status":          orderStatuses.DisplayStatus,
			"previous_display_status": orderInfo.DisplayStatus,
			"event_trigger":           "order_cancelled",
		}
		mp.Track(context.Background(), []*mixpanel.Event{
			mp.NewEvent("Order Status Updated", userID, trackingObject),
		})

		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{userID},
			EventName:   "Order Status Updated",
			EventObject: trackingObject,
		})

		s.AddDataForReconciliation(context.Background(), &dto.AddReconciliationRequest{
			OrderID: orderID,
			Data: []dto.StatusTimeStamp{
				{
					TimeStamp:   time.Now().UnixMilli(),
					OrderStatus: "order_cancelled",
				},
			},
			Service: source,
		})
	}(s.Mixpanel, request.UserID, orderid64, int(orderDetails.GetOrderValue()), s, request.Data.Explanation, request.Data.Email, source, orderInfo.Seller, orderStatuses)

	response = dto.AppCancelKiranaBazarOrderResponse{
		Data: dto.AppCancelKiranaBazarOrderData{
			OrderID: orderID,
			Message: "Order has been cancelled",
		},
	}

	// add in redis the order id for kirana club seller
	go func(userId, seller string) {
		if seller == utils.KIRANA_CLUB {
			err := s.udpateUserKiranaClubOrder(ctx, fmt.Sprintf("KIRANA_CLUB_ORDER::%s", userId), "", time.Hour)
			if err != nil {
				fmt.Println("error in adding order id to redis", err)
			}
		}
	}(request.UserID, orderInfo.Seller)

	_, inStockMarkProducts, err := s.Inventory.UpdateProductsInventory(ctx, cartItems, inventory.INVENTORY_OPERATION_TYPES.ADD_INVENORY)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("Error updating inventory for order %d: %v", orderid64, err))
		return response, nil
	}

	go func(ctx context.Context, orderId int64, inStockMarkProducts []inventoryDao.KiranaBazarProductsInventory) {
		if len(inStockMarkProducts) > 0 {
			// _, err := s.UpdateProductsStockStatus(ctx, inStockMarkProducts, false, "<EMAIL>")
			// if err != nil {
			// 	slack.SendSlackMessage(fmt.Sprintf("Error marking products instock for order %d: %v", orderId, err))
			// }
		}
	}(ctx, orderid64, inStockMarkProducts)

	return
}
