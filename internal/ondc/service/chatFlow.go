package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/infrastructure/payments"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/utils"
	"os"
	"path/filepath"
	"slices"
	"strconv"
	"strings"
	"time"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

func readFlowDataFromFile(filename string) (map[string]interface{}, error) {
	// Get the absolute path to the JSON file
	filePath, err := filepath.Abs(filename)
	if err != nil {
		return nil, fmt.Errorf("error getting absolute path: %v", err)
	}

	// Read the file content
	fileData, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("error reading file %s: %v", filePath, err)
	}

	// Parse the JSON data
	var flowData map[string]interface{}
	if err := json.Unmarshal(fileData, &flowData); err != nil {
		return nil, fmt.Errorf("error parsing JSON file: %v", err)
	}

	return flowData, nil
}

func (s *Service) GetCsTicketFlow(ctx context.Context, req *dto.CsTicketFlowRequest) (map[string]interface{}, error) {
	// Parse the JSON data into a map
	var flowData map[string]interface{}
	var err error
	if req.Data.FlowID == "fofdPlusOneFlow" {
		flowData, err = readFlowDataFromFile("internal/ondc/utils/fofdPlusOneFlow.json")
		if err != nil {
			fmt.Printf("Error: %v\n", err)
			return nil, fmt.Errorf("error reading flow data: %v", err)
		}
	} else {
		flowData, err = readFlowDataFromFile("internal/ondc/utils/ticketCreationFlow.json")
		if err != nil {
			fmt.Printf("Error: %v\n", err)
			return nil, fmt.Errorf("error reading flow data: %v", err)
		}
	}
	flow, ok := flowData["flow"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid flow data structure: 'data.flow' not found or not an object")
	}

	screens, ok := flow["screens"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid flow data structure: 'data.flow.screens' not found or not an object")
	}
	components, ok := flowData["components"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid flow data structure: 'data.flow.components' not found or not an object")
	}
	version, ok := flowData["version"].(string)
	flowId, ok := flowData["flowId"].(string)
	// for fofdPlusOneFlow, we don't need to get order details

	orderDetailsRequest := dto.AppGetKiranaBazarOrderRequest{
		UserID: req.Data.UserID,
		Data: dto.AppGetKiranaBazarOrderData{
			OrderID: req.Data.OrderID,
		},
	}
	orderData, err := s.GetKiranaBazarOrder(ctx, orderDetailsRequest)
	if err != nil {
		return nil, fmt.Errorf("error getting order details: %v", err)
	}
	var trackingHistory []dto.ShippingHistory
	var tentativeDeliveryDate string
	var courier string
	var awbNumber string
	if (len(orderData.Data) > 0) && (orderData.Data[0].OrderStatusDetails != nil) && (orderData.Data[0].OrderStatusDetails.ShippingHistory != nil) {
		trackingHistory = orderData.Data[0].OrderStatusDetails.ShippingHistory
		if orderData.Data[0].OrderStatusDetails.TentativeDeliveryDate != nil {
			t, err := time.Parse("2006-01-02 15:04:05", *orderData.Data[0].OrderStatusDetails.TentativeDeliveryDate)
			if err != nil {
				return nil, fmt.Errorf("error parsing tentative delivery date: %v", err)
			}

			tentativeDeliveryDate = t.Format("2 Jan 2006")
		}
		courier = orderData.Data[0].OrderStatusDetails.Courier
		awbNumber = orderData.Data[0].OrderStatusDetails.AWBNumber
	}
	orderDetails := dao.KiranaBazarOrderDetails{}
	err = json.Unmarshal(orderData.Data[0].OrderDetails, &orderDetails)
	if err != nil {
		fmt.Printf("Error unmarshaling JSON: %v\n", err)
		return nil, fmt.Errorf("error unmarshaling order details: %v", err)
	}

	// Convert products to map array (do this once)
	productMaps := make([]interface{}, len(orderDetails.Cart))
	for i, product := range orderDetails.Cart {
		cartMeta := shared.KiranaBazarProductMeta{}
		err = json.Unmarshal(product.Meta, &cartMeta)
		productMaps[i] = map[string]interface{}{
			"sku_id":         product.ID,
			"name":           product.Name,
			"quantity":       product.Quantity,
			"weight":         cartMeta.Quantity,
			"pack_size":      cartMeta.PackSize,
			"mrp":            cartMeta.MRPString,
			"wholesale_rate": cartMeta.WholesaleRate,
			"image_url":      product.ImageUrls[0],
		}
	}

	// Convert tracking history to map array (do this once)
	trackingHistoryMaps := make([]interface{}, len(trackingHistory))
	for i, event := range trackingHistory {
		trackingHistoryMaps[i] = map[string]interface{}{
			"status":   event.Status,
			"time":     event.Time,
			"location": event.Location,
			"color":    event.Color,
		}
	}

	// Create order details map (do this once)
	orderDetailsMap := map[string]interface{}{
		"orderNumber":      orderData.Data[0].ID,
		"orderDate":        orderData.Data[0].OrderDate.Format("2 Jan 2006"),
		"expectedDelivery": tentativeDeliveryDate,
		"status":           orderData.Data[0].DisplayStatus,
		"courier":          courier,
		"awbNumber":        awbNumber,
		"orderAmount":      orderData.Data[0].TotalAmount,
	}

	// Iterate through all screens and update component properties based on component type
	for _, screenData := range screens {
		screen, ok := screenData.(map[string]interface{})
		if !ok {
			continue
		}

		component, ok := screen["component"].(map[string]interface{})
		if !ok {
			continue
		}

		screenID, _ := screen["id"].(string)
		componentType, ok := component["type"].(string)
		if !ok {
			continue
		}

		properties, ok := component["properties"].(map[string]interface{})
		if !ok {
			continue
		}

		// Check component type and update properties accordingly
		switch componentType {
		case "orderStatusComponent":
			// Update order status component with order details and tracking history
			properties["orderDetails"] = orderDetailsMap
			properties["trackingHistory"] = trackingHistoryMaps
			if orderData.Data[0].OrderPayment.PaymentStatus == payments.StatusDelayedDispatch {
				properties["orderStatusCard"] = orderData.Data[0].OrderPayment.StatusMeta
			}
		case "productListingComponent":
			// Update product listing component with product data
			properties["products"] = productMaps
		case "optionsComponent":
			if (req.Data.FlowID == "fofdPlusOneFlow") && (screenID == "deliveryCallStatus") {
				caser := cases.Title(language.English)
				seller := caser.String(strings.ToLower(strings.ReplaceAll(orderData.Data[0].Seller, "_", " ")))

				properties["description"] = "Order: " + orderData.Data[0].ID + " | Seller: " + seller + " | Amount: " + fmt.Sprintf(`%.1f`, orderData.Data[0].TotalAmount)
			}
		}
	}

	response := map[string]interface{}{
		"version":    version,
		"components": components,
		"flow_id":    flowId,
		"flow":       flow,
	}
	return response, nil
}

// CORE FUNCTION 1: Check if we need to process NDR
func (s *Service) shouldProcessNDR(ctx context.Context, request *dto.AppSubmitTicketRequest) bool {
	// Case 1: fofdPlusOneFlow - except special case
	if request.Data.FlowID == utils.TICKET_FLOW_FOFD {
		// Exception: deliveryCallStatusNo + customerCareConnect = no NDR
		if request.Data.IssueDetails.IssueCategory.ID == "deliveryCallStatusNo" &&
			request.Data.IssueDetails.IssueSubCategory.ID == "customerCareConnect" {
			return false
		}
		return true
	}

	// Case 2: noDoorstep = always NDR
	if request.Data.IssueDetails.IssueSubCategory.ID == "noDoorstep" {
		return true
	}

	// Case 3: noCall + (OFD happened OR NDR exists) = NDR
	if request.Data.IssueDetails.IssueSubCategory.ID == "noCall" {
		isOrderOfdHappened, _ := s.checkFofdHappenedYet(ctx, request.Data.OrderID)
		orderID, err := strconv.Atoi(request.Data.OrderID)
		if err == nil {
			orderNDRInfo, _ := s.GetNDROrderV2(orderID)
			if isOrderOfdHappened || orderNDRInfo != nil {
				return true
			}
		}
	}

	return false
}

// CORE FUNCTION 2: Check if ticket needs 3PL handling
func (s *Service) shouldUse3PLHandling(ctx context.Context, request *dto.AppSubmitTicketRequest) bool {
	// Same conditions as NDR - if we process NDR, we use 3PL handling
	return s.shouldProcessNDR(ctx, request)
}

// NDR OPERATION
func (s *Service) processNDR(ctx context.Context, request *dto.AppSubmitTicketRequest) error {
	var ndrCfAction string
	if request.Data.IssueDetails.IssueCategory.ID == "cancelOrder" {
		ndrCfAction = "RTO"
	} else {
		ndrCfAction = "REATTEMPT"
	}

	orderID, err := strconv.Atoi(request.Data.OrderID)
	if err != nil {
		return fmt.Errorf("error converting order ID to int: %v", err)
	}

	orderNDRInfo, _ := s.GetNDROrderV2(orderID)
	source := request.Data.FlowID

	if orderNDRInfo == nil {
		_, err := s.AddNDRV2(ctx, request.Data.OrderID, "", request.Data.UserID, "", source, true)
		if err != nil {
			return fmt.Errorf("error adding NDR: %v", err)
		}
		orderNDRInfo, err = s.GetNDROrderV2(orderID)
		if err != nil {
			return fmt.Errorf("error getting NDR order info: %v", err)
		}
	}

	callConnected := "NA"
	nextActionAt := time.Now().Add(24 * time.Hour).Format("2006-01-02T15:04:05")
	cfNdrUserReason := "Other"
	cfNote := request.Data.IssueDetails.IssueSubCategory.Text
	if cfNote == "" {
		cfNote = request.Data.IssueDetails.IssueCategory.Text
	}
	customerEscalated := true
	_, err = s.UpdateNDROrderV2(ctx, &dto.UpdateNDROrderRequestV2{
		Data: dto.UpdateNDROrderRequestV2Data{
			OrderID:           request.Data.OrderID,
			ActionID:          orderNDRInfo.OrderActionID,
			UpdatedBy:         request.Data.UserID,
			CallConnected:     &callConnected,
			Action:            &ndrCfAction,
			NextActionAt:      &nextActionAt,
			NDRStage:          "CF",
			Reason:            &cfNdrUserReason,
			Note:              &cfNote,
			Source:            &source,
			CustomerEscalated: &customerEscalated,
		},
	})
	if err != nil {
		return fmt.Errorf("error updating NDR order: %v", err)
	}

	return nil
}

func (s *Service) shouldCreateNewTicket(ticket *dao.CSTicket) bool {
	// If ticket has category "orderStatus" and status "resolved", create new ticket
	category, err := s.GetTicketCategoryByCode(context.Background(), "orderStatus")
	if err != nil {
		fmt.Printf("error getting ticket category: %v", err)
		return false
	}
	if (ticket == nil) || ((ticket.Category != nil && *ticket.Category == category.ID) && (ticket.Status != nil && slices.Contains([]string{utils.TICKET_STATUS_RESOLVED, utils.TICKET_STATUS_CLOSED}, *ticket.Status))) {
		return true
	}
	return false
}

// TICKET OPERATION
func (s *Service) processTicket(ctx context.Context, request *dto.AppSubmitTicketRequest, use3PL bool) error {
	// Always set to orderStatus for consistency
	if use3PL {
		request.Data.IssueDetails.IssueCategory.ID = "orderStatus"
	}

	ticket, err := s.GetTicketByOrderID(ctx, request.Data.OrderID)
	if err != nil {
		fmt.Printf("error getting ticket by order ID: %v", err)
	}
	var actionableTicket *dao.CSTicket
	if (ticket != nil) && (len(*ticket) > 0) {
		actionTicketRef := (*ticket)[0]
		actionableTicket = &actionTicketRef
	}
	if s.shouldCreateNewTicket(actionableTicket) {
		// Create new ticket with 3PL flag
		response, err := s.AppSubmitTicket(ctx, request, use3PL)
		if err != nil {
			return fmt.Errorf("error submitting ticket: %v", err)
		}
		responseMap, ok := (*response)["ticket"].(dao.CSTicket)
		if !ok {
			slack.SendSlackMessage(fmt.Sprintf("Error getting ticket from response: %v", err))
			return nil
		}
		go func() {
			err := s.UpdateTicketPriority(ctx, &responseMap)
			if err != nil {
				fmt.Printf("error updating ticket priority: %v", err)
			}
		}()
	} else {
		// Update existing ticket
		err = s.updateExistingTicket(ctx, actionableTicket, request)
		if err != nil {
			return fmt.Errorf("error updating existing ticket: %v", err)
		}
	}

	return nil
}

func (s *Service) updateExistingTicket(ctx context.Context, ticket *dao.CSTicket, request *dto.AppSubmitTicketRequest) error {
	note := fmt.Sprintf(`%s %s`, request.Data.IssueDetails.IssueCategory.Text, request.Data.IssueDetails.IssueSubCategory.Text)

	// Change status
	_, err := s.PerformTicketAction(ctx, &dto.TicketActionRequest{
		Data: dto.TicketActionRequestData{
			TicketID:  ticket.ID,
			Action:    "change_status",
			ActionBy:  "<EMAIL>",
			NewStatus: utils.TICKET_STATUS_INPROGRESS_3PL,
			ActionData: dto.ActionData{
				Note: note,
			},
		},
	})
	if err != nil {
		fmt.Printf("error changing ticket status %s: %v", ticket.ID, err)
		return err
	}

	// Change assignee
	_, err = s.PerformTicketAction(ctx, &dto.TicketActionRequest{
		Data: dto.TicketActionRequestData{
			TicketID:    ticket.ID,
			Action:      "change_assignee",
			ActionBy:    "<EMAIL>",
			NewAssignee: "<EMAIL>",
			ActionData: dto.ActionData{
				Note:        note,
				RequestedBy: "<EMAIL>",
			},
		},
	})
	if err != nil {
		fmt.Printf("error changing ticket assignee %s: %v", ticket.ID, err)
		return err
	}

	return nil
}

func (s *Service) checkFofdHappenedYet(ctx context.Context, orderID string) (bool, error) {
	response, err := s.GetReconciliationData(ctx, &dto.GetReconciliationRequest{
		OrderID: orderID,
	})
	if err != nil {
		return false, fmt.Errorf("error getting reconciliation data: %v", err)
	}
	isOrderOfdHappened := false
	if response.Data.OrderOfd != nil {
		isOrderOfdHappened = true
	}
	return isOrderOfdHappened, nil
}

// MAIN ORCHESTRATOR - Simple and Clean
func (s *Service) AppSubmitFlowResponse(ctx context.Context, request *dto.AppSubmitTicketRequest) (*map[string]interface{}, error) {
	// Check conditions once
	err := s.FixCategoryMapping(ctx, &request.Data.IssueDetails)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("Error fixing category mapping for order in AppSubmitFlowResponse %s: %v", request.Data.OrderID, err))
	}
	needs3PL := s.shouldUse3PLHandling(ctx, request)

	// 1. Process NDR if needed
	if needs3PL {
		err := s.processNDR(ctx, request)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error processing NDR for order in AppSubmitFlowResponse %s: %v", request.Data.OrderID, err))
			return nil, fmt.Errorf("error processing NDR: %v", err)
		}

		// Set Redis flag for NDR cases
		submittedRedisKey := fmt.Sprintf("firstNdrPnTicketSubmitted_%s_%s", request.Data.OrderID, request.Data.UserID)
		s.GcpRedis.RedisClient.Set(ctx, submittedRedisKey, "true", time.Duration(45*24)*time.Hour)
	}

	if request.Data.FlowID == "" {
		request.Data.FlowID = utils.TICKET_FLOW_APP_STANDARD
	}
	// 2. Process Ticket with 3PL flag
	err = s.processTicket(ctx, request, needs3PL)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("Error processing ticket for order in AppSubmitFlowResponse %s: %v", request.Data.OrderID, err))
		return nil, fmt.Errorf("error processing ticket: %v", err)
	}

	return nil, nil
}

func (s *Service) FixCategoryMapping(ctx context.Context, issueDetails *dto.AppSubmitTicketIssueDetails) error {
	// Skip if no subcategory is provided
	if issueDetails.IssueSubCategory.ID == "" {
		return nil
	}

	// Get subcategory details using its code (ID field contains the code)
	subCategory, err := s.GetTicketCategoryByCode(ctx, issueDetails.IssueSubCategory.ID)
	if err != nil {
		return fmt.Errorf("failed to get subcategory details: %w", err)
	}

	// Skip if subcategory has no parent (shouldn't happen for valid subcategories)
	if subCategory.ParentID == nil {
		return nil
	}

	// Get current category details using its code
	currentCategory, err := s.GetTicketCategoryByCode(ctx, issueDetails.IssueCategory.ID)
	if err != nil {
		return fmt.Errorf("failed to get current category details: %w", err)
	}

	// Check if the subcategory's parent matches the current category
	if *subCategory.ParentID == currentCategory.ID {
		// Mapping is correct, no need to fix
		return nil
	}

	// Get the correct parent category
	correctParentCategory, err := s.GetTicketCategoryByID(ctx, *subCategory.ParentID)
	if err != nil {
		return fmt.Errorf("failed to get correct parent category: %w", err)
	}

	// Update the issue category with correct parent category data
	issueDetails.IssueCategory = dto.IssueCategoryInfo{
		ID:         correctParentCategory.Code, // Use code as ID
		NextScreen: issueDetails.IssueCategory.NextScreen,
		Text:       correctParentCategory.Text,
	}

	return nil
}
