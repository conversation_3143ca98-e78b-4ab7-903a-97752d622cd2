package cart

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"kc/internal/ondc/models/shared"
	"time"

	"gorm.io/datatypes"
)

// this is the dao object of the cart (sql db) -- not exported
type KiranaBazarCart struct {
	ID        uint64         `json:"id"`
	UserID    string         `json:"user_id"`
	Cart      CartDatas      `json:"cart"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	IsValid   bool           `json:"is_valid"`
	Seller    string         `json:"seller"`
	Context   datatypes.JSON `json:"context"`
	OnSelect  datatypes.JSON `json:"on_select"`
	OnInit    datatypes.JSON `json:"on_init"`
}

func (KiranaBazarCart) TableName() string {
	return "kiranabazar_cart"
}

type CartDatas struct {
	raw    []byte
	parsed []shared.SellerItems
}

// Scan implements sql.Scanner interface
func (c *CartDatas) Scan(value interface{}) error {
	if value == nil {
		c.raw = nil
		c.parsed = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	// Store raw bytes
	c.raw = bytes

	// Parse only if needed
	return nil
}

// Value implements driver.Valuer interface
func (c CartDatas) Value() (driver.Value, error) {
	if c.raw != nil {
		return c.raw, nil
	}
	if c.parsed != nil {
		data, err := json.Marshal(c.parsed)
		if err != nil {
			return nil, err
		}
		c.raw = data
		return data, nil
	}
	return nil, nil
}

// MarshalJSON implements json.Marshaler
func (c CartDatas) MarshalJSON() ([]byte, error) {
	if c.raw != nil {
		return c.raw, nil
	}
	return json.Marshal(c.parsed)
}

// UnmarshalJSON implements json.Unmarshaler
func (c *CartDatas) UnmarshalJSON(data []byte) error {
	c.raw = data
	return nil
}

// Get returns parsed data, parsing raw data if necessary
func (c *CartDatas) Get() ([]shared.SellerItems, error) {
	if c.parsed == nil && c.raw != nil {
		var v []shared.SellerItems
		if err := json.Unmarshal(c.raw, &v); err != nil {
			return nil, err
		}
		c.parsed = v
	}
	return c.parsed, nil
}

// Set stores data directly without marshaling
func (c *CartDatas) Set(v interface{}) {
	c.parsed = v.([]shared.SellerItems)
	json.Unmarshal(c.raw, &v)
}

type CartValidationCoinditions struct {
	IncludeOOSProducts         bool
	IncludeVariants            bool
	CheckMaxProductQuantityCap bool
}
