package cart

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/repositories/firebaseRepo"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/products"
	userdetails "kc/internal/ondc/service/userDetails"
	"log"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
)

type CartData struct {
	UserID    string    `json:"user_id"`
	Seller    string    `json:"seller"`
	UpdatedAt time.Time `json:"updated_at"`
	Data      []byte    `json:"data"`
}

type CartService struct {
	redisClient        *redis.Client
	repository         *sqlRepo.Repository
	mixpanel           *mixpanelRepo.Repository
	firebaseRepository *firebaseRepo.FirebaseRepo
	isRunning          bool
	workerMutex        sync.RWMutex
}

var (
	service *CartService
	once    sync.Once
)

// InitializeCartService initializes the cart service singleton
func InitializeCartService(redisClient *redis.Client, repository *sqlRepo.Repository, mixpanel *mixpanelRepo.Repository, fbRepo *firebaseRepo.FirebaseRepo) error {
	var initErr error
	once.Do(func() {
		service = &CartService{
			redisClient:        redisClient,
			repository:         repository,
			mixpanel:           mixpanel,
			firebaseRepository: fbRepo,
		}

		// Start background worker TODO: @sanket stopping background process and upserting data to cart everytime as of now, will optimise further to decrease the writes on sql
		// if err := service.startBatchWorker(context.Background()); err != nil {
		// 	initErr = fmt.Errorf("failed to start batch worker: %v", err)
		// }
	})
	return initErr
}

// GetCart retrieves cart data from Redis or falls back to SQL
func (s *CartService) getCart(ctx context.Context, userID, seller string) (*KiranaBazarCart, error) {
	cartKey := fmt.Sprintf("cart:%s:%s", userID, seller)
	kiranaBazarCart := KiranaBazarCart{}
	// Try Redis first
	cartData, err := s.redisClient.Get(ctx, cartKey).Bytes()
	if err == redis.Nil {
		// Not in Redis, get from SQL
		_, err = s.repository.CustomQuery(&kiranaBazarCart, fmt.Sprintf(`select * from kiranabazar_cart where user_id = '%s' and seller = "%s";`, userID, seller))
		if err != nil {
			return nil, fmt.Errorf("failed to get cart from SQL: %v", err)
		}
		// Cache in Redis
		err = s.handleCartUpdate(ctx, userID, seller, &kiranaBazarCart)
		return &kiranaBazarCart, nil
	} else if err != nil {
		return nil, fmt.Errorf("failed to get cart from Redis: %v", err)
	}
	err = json.Unmarshal(cartData, &kiranaBazarCart)
	return &kiranaBazarCart, err
}

func (s *CartService) upsertCartToPersistantSQL(ctx context.Context, userID, seller string, cartData *KiranaBazarCart) error {
	_, err := s.repository.Upsert(cartData)
	return err
}

// HandleCartUpdate updates cart data in Redis and queues SQL sync
func (s *CartService) handleCartUpdate(ctx context.Context, userID, seller string, cartData *KiranaBazarCart) error {
	if cartData == nil {
		return errors.New("not a valid cart data")
	}

	// upsert cart data to sql
	go s.upsertCartToPersistantSQL(ctx, userID, seller, cartData)

	// insert to redis
	cartKey := fmt.Sprintf("cart:%s:%s", userID, seller)

	// Store cart data and queue update in a pipeline
	pipe := s.redisClient.Pipeline()

	// Store cart data with TTL
	cartByte, err := json.Marshal(cartData)
	if err != nil {
		return err
	}
	pipe.Set(ctx, cartKey, cartByte, 168*time.Hour)

	// Add to update queue with minimal data
	updateInfo := fmt.Sprintf("%s:%s", userID, seller)
	pipe.LPush(ctx, "cart:updates", updateInfo)

	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to handle cart update: %v", err)
	}

	return nil
}

// startBatchWorker starts the background worker for processing cart updates
func (s *CartService) startBatchWorker(ctx context.Context) error {
	s.workerMutex.Lock()
	if s.isRunning {
		s.workerMutex.Unlock()
		return fmt.Errorf("worker already running")
	}
	s.isRunning = true
	s.workerMutex.Unlock()

	const (
		batchSize = 100
		maxWait   = 5 * time.Second
	)

	go func() {
		for {
			select {
			case <-ctx.Done():
				s.workerMutex.Lock()
				s.isRunning = false
				s.workerMutex.Unlock()
				return
			default:
				updates := make([]string, 0, batchSize)
				deadline := time.Now().Add(maxWait)

				// Get first item (blocking)
				result, err := s.redisClient.BRPop(ctx, maxWait, "cart:updates").Result()
				if err != nil {
					if err != redis.Nil { // timeout is expected
						log.Printf("Error in BRPOP: %v", err)
						time.Sleep(time.Second) // Avoid tight loop on error
					}
					continue
				}
				updates = append(updates, result[1])

				// Try to get more items (non-blocking)
				for len(updates) < batchSize && time.Now().Before(deadline) {
					result, err := s.redisClient.RPop(ctx, "cart:updates").Result()
					if err == redis.Nil {
						break
					}
					if err != nil {
						log.Printf("Error in RPop: %v", err)
						break
					}
					updates = append(updates, result)
				}

				if len(updates) > 0 {
					if err := s.processBatch(ctx, updates); err != nil {
						log.Printf("Error processing batch: %v", err)
					}
				}
			}
		}
	}()

	return nil
}

// processBatch processes a batch of cart updates
func (s *CartService) processBatch(ctx context.Context, updates []string) error {
	// Get all cart data in one pipeline
	pipe := s.redisClient.Pipeline()
	for _, update := range updates {
		parts := strings.Split(update, ":")
		if len(parts) != 2 {
			log.Printf("Invalid update format: %s", update)
			continue
		}
		userID, seller := parts[0], parts[1]
		cartKey := fmt.Sprintf("cart:%s:%s", userID, seller)
		pipe.Get(ctx, cartKey)
	}

	results, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to get cart data: %v", err)
	}

	// Start SQL transaction

	fmt.Println("starting saving cart to db")
	// tx, err := s.repository.BeginTx(ctx)
	// if err != nil {
	// 	return fmt.Errorf("failed to start transaction: %v", err)
	// }
	// defer tx.Rollback()

	// Process each update
	for i, cmd := range results {
		getCmd := cmd.(*redis.StringCmd)
		_, err := getCmd.Bytes()
		if err != nil {
			log.Printf("Error getting cart data: %v", err)
			continue
		}

		parts := strings.Split(updates[i], ":")
		userID, seller := parts[0], parts[1]

		fmt.Println("saving data to db", userID, seller)
		// if err := s.repository.UpdateCartTx(ctx, tx, userID, seller, cartData); err != nil {
		// 	log.Printf("Error updating cart in SQL for user %s, seller %s: %v", userID, seller, err)
		// 	continue
		// }
	}

	// Commit transaction
	// if err := tx.Commit(); err != nil {
	// 	return fmt.Errorf("failed to commit transaction: %v", err)
	// }

	return nil
}

// Validate Cart function
func (cart *KiranaBazarCart) ValidateCart(conditions CartValidationCoinditions, appVersion string) (*KiranaBazarCart, error) {
	// Unmarshal cart products
	allProducts, _ := cart.Cart.Get()

	// Filter and update products
	inStock, outOfStock, _, _ := cart.FilterAndUpdateProducts(allProducts, conditions, appVersion, false)
	// Combine products based on conditions
	validProducts := cart.combineProducts(inStock, outOfStock, conditions)

	cart.Cart.Set(validProducts)
	return cart, nil
}

// VirtualSkuProcessResult holds the result of virtual SKU processing
type VirtualSkuProcessResult struct {
	VirtualSkuCart       []shared.SellerItems `json:"virtual_sku_cart"`
	ProcessedVirtualSkus []string             `json:"processed_virtual_skus"`
	FinalProducts        []shared.SellerItems `json:"final_products"`
}

// processVirtualSkuProduct processes a single virtual SKU and returns its original SKUs
func (cart *KiranaBazarCart) processVirtualSkuProduct(virtualProduct shared.SellerItems, virtualProductInfo *products.Product, appVersion string) ([]shared.SellerItems, map[string]string, map[string]float64, error) {
	if len(virtualProductInfo.OriginalSkus) == 0 {
		return nil, nil, nil, fmt.Errorf("no original SKUs found for virtual product %s", virtualProduct.ID)
	}

	originalSkuProducts := make([]shared.SellerItems, 0)
	oosSkus := make(map[string]string)
	allRequiredSkusValid := true
	brandShareMap := make(map[string]float64)

	// First, validate all original SKUs to ensure they're all available
	// If any are invalid, we'll skip the entire virtual SKU
	for _, virtualSkuMapping := range virtualProductInfo.OriginalSkus {
		if !virtualSkuMapping.IsActive {
			continue
		}

		// Get original product info
		originalProductInfo, exists := products.GetProductByID(virtualSkuMapping.ProductId)
		if !exists {
			oosSkus[fmt.Sprintf("%d", virtualSkuMapping.ProductId)] = fmt.Sprintf("original product %d not found", virtualSkuMapping.ProductId)
			allRequiredSkusValid = false
			continue
		}

		// Check if original product is active
		if originalProductInfo.IsActive != nil && !*originalProductInfo.IsActive {
			oosSkus[fmt.Sprintf("%d", originalProductInfo.ID)] = fmt.Sprintf("original product %d is inactive", virtualSkuMapping.ProductId)
			allRequiredSkusValid = false
			continue
		}

		// Check if original product is OOS
		if originalProductInfo.IsOOS != nil && *originalProductInfo.IsOOS {
			oosSkus[fmt.Sprintf("%d", originalProductInfo.ID)] = fmt.Sprintf("original product %d is out of stock", virtualSkuMapping.ProductId)
			allRequiredSkusValid = false
			continue
		}

		// Calculate quantity for original SKU based on virtual SKU quantity and mapping
		originalQuantity := int32(virtualProduct.Quantity) * int32(virtualSkuMapping.Quantity)

		// Check inventory availability
		if originalProductInfo.DisplayQuantity != nil {
			availableInventory := *originalProductInfo.DisplayQuantity
			currentPiecesCount := int(originalQuantity) * originalProductInfo.MetaProperties.PackSize

			if currentPiecesCount > availableInventory {
				oosSkus[fmt.Sprintf("%d", originalProductInfo.ID)] = fmt.Sprintf(
					"insufficient inventory for product %d (required: %d, available: %d)",
					virtualSkuMapping.ProductId,
					currentPiecesCount,
					availableInventory,
				)
				allRequiredSkusValid = false
				continue
			}
		}
	}

	// If any required SKU is invalid, return early with error
	if !allRequiredSkusValid {
		// TODO: @sitaram add mixpanel event here
		slack.SendSlackDebugMessage(fmt.Sprintf("Virtual SKU processing failed for user %s, seller %s: %v", cart.UserID, cart.Seller, oosSkus))
		return nil, oosSkus, brandShareMap, fmt.Errorf("one or more original SKUs are unavailable for virtual product %s", virtualProduct.ID)
	}

	// Process all SKUs since they're all valid
	for _, virtualSkuMapping := range virtualProductInfo.OriginalSkus {
		if !virtualSkuMapping.IsActive {
			continue
		}

		// Get original product info (we know it exists from validation above)
		originalProductInfo, _ := products.GetProductByID(virtualSkuMapping.ProductId)

		// Calculate quantity for original SKU
		originalQuantity := int32(virtualProduct.Quantity) * int32(virtualSkuMapping.Quantity)

		originalSellerItem := originalProductInfo.ToSellerItems(
			products.ToSellerItemsCondition{IncludeVariant: false},
			appVersion,
			nil,
		)

		// Set the calculated quantity
		originalSellerItem.Quantity = originalQuantity
		brandShareMap[fmt.Sprint(originalProductInfo.ID)] = virtualSkuMapping.BrandShare

		// Update pricing if virtual SKU mapping has custom wholesale rate
		if virtualSkuMapping.WholesaleRate != nil {
			updateProductWithCustomWholesaleRate(&originalSellerItem, *virtualSkuMapping.WholesaleRate, virtualSkuMapping.BrandShare)
		}

		originalSkuProducts = append(originalSkuProducts, originalSellerItem)
	}

	return originalSkuProducts, oosSkus, brandShareMap, nil
}

// Helper function to update product with custom wholesale rate
func updateProductWithCustomWholesaleRate(product *shared.SellerItems, wholesaleRate, brandShare float64) {
	var meta shared.KiranaBazarProductMeta
	if err := json.Unmarshal(product.Meta, &meta); err != nil {
		return // Skip update if unmarshal fails
	}

	// handle here rate for brand share
	clonedWholesaleRate := meta.WholesaleRate // 6.8
	discountedAmount := clonedWholesaleRate - wholesaleRate // 6.8 - 6.14
	brandDiscountShare := discountedAmount * brandShare //  6.8 - 6.14 * 1

	if meta.BrandWholesaleRate == nil {
		meta.BrandWholesaleRate = &clonedWholesaleRate
	}

	if meta.EffectiveWholesaleRate == nil {
		meta.EffectiveWholesaleRate = meta.BrandWholesaleRate // 7.5
	}
	// meta.BrandWholesaleRate 7.5 or 6.8

	if brandDiscountShare > 0 {
		effectiveRate := *meta.EffectiveWholesaleRate - brandDiscountShare
		meta.EffectiveWholesaleRate = &effectiveRate
	} else {
		meta.EffectiveWholesaleRate = meta.BrandWholesaleRate
	}

	// EffectiveWholesaleRate = 7.5 - brandDiscountShare // 7.5 - 0.66 = 6.84

	// truncate to 2 decimal places
	wholesaleRate = float64(int(wholesaleRate*100)) / 100.0 // Truncate to 2 decimal place
	meta.WholesaleRate = wholesaleRate
	meta.WholesaleRateString = fmt.Sprintf(`₹%.2f`, wholesaleRate)

	// Recalculate margin with new wholesale rate
	if meta.MRPNumber > 0 {
		newMarkupMargin := ((meta.MRPNumber - wholesaleRate) / meta.MRPNumber) * 100
		meta.MarkupMargin = newMarkupMargin
		meta.MarkupMarginString = fmt.Sprintf("मार्जिन: %.1f%%", newMarkupMargin)
		meta.MarkupMarginValue = fmt.Sprintf("%.1f%%", newMarkupMargin)
	}

	updatedMeta, err := json.Marshal(meta)
	if err == nil {
		product.Meta = updatedMeta
	}
}

func getOriginalSkusData(originalSkus []*shared.KiranaBazarVirtualSkuMapping, oosSkusMap map[string]string) []shared.OriginalSkuForVirtualSku {
	oosOriginalSkus := make([]shared.OriginalSkuForVirtualSku, 0)

	for _, originalSku := range originalSkus {
		if originalSku == nil {
			continue
		}

		productId := fmt.Sprint(originalSku.ProductId)

		skuData := shared.OriginalSkuForVirtualSku{
			ProductID: productId,
			Quantity:  int32(originalSku.Quantity),
			IsActive:  originalSku.IsActive,
		}

		// Add OOS reason if available
		if reason, exists := oosSkusMap[productId]; exists {
			skuData.Notes = reason
		}

		if originalSku.WholesaleRate != nil {
			skuData.WholesaleRate = *originalSku.WholesaleRate
		}

		oosOriginalSkus = append(oosOriginalSkus, skuData)
	}

	return oosOriginalSkus
}

// Modified ProcessVirtualSkus to use the improved processVirtualSkuProduct
func (cart *KiranaBazarCart) ProcessVirtualSkus(inStockProducts []shared.SellerItems, appVersion string) (*VirtualSkuProcessResult, error) {
	if len(inStockProducts) == 0 {
		return &VirtualSkuProcessResult{
			VirtualSkuCart:       []shared.SellerItems{},
			ProcessedVirtualSkus: []string{},
			FinalProducts:        []shared.SellerItems{},
		}, nil
	}

	finalProducts := make([]shared.SellerItems, 0)
	newProductsToAddInCart := make([]shared.SellerItems, 0)
	virtualSkuCart := make([]shared.SellerItems, 0)
	processedVirtualSkus := make([]string, 0)
	alreadyPresentSkus := make(map[string]shared.SellerItems)
	brandDiscountShareMap := make(map[string]float64)

	PRODUCT_TYPE_VIRTUAL := string(products.ProductTypeVirtual)

	// First pass: separate virtual SKUs from regular products
	for _, product := range inStockProducts {
		if product.Seller != cart.Seller || product.Quantity <= 0 {
			continue
		}

		// Get updated product info
		productInfo, exists := products.GetProductByID(product.ID)
		if !exists {
			continue
		}

		// Process virtual SKUs
		if productInfo.ProductType == PRODUCT_TYPE_VIRTUAL {
			// Get all original SKUs of this virtual SKU
			originalSkus, oosSkusMap, brandShareMap, err := cart.processVirtualSkuProduct(product, productInfo, appVersion)
			brandDiscountShareMap = brandShareMap

			// Create a copy of the product with OOS information
			virtualProduct := product

			if err != nil || len(oosSkusMap) > 0 {
				// If any error or OOS original SKUs, add to virtual cart with OOS info
				oosOriginalSkus := getOriginalSkusData(productInfo.OriginalSkus, oosSkusMap)

				virtualProduct.OriginalSkus = oosOriginalSkus
				// virtualSkuCart = append(virtualSkuCart, virtualProduct)
				processedVirtualSkus = append(processedVirtualSkus, virtualProduct.ID)
			} else {
				// All original SKUs are available
				newProductsToAddInCart = append(newProductsToAddInCart, originalSkus...)

				// Add the original virtual SKU info for reference
				originalSkus := getOriginalSkusData(productInfo.OriginalSkus, oosSkusMap)
				virtualProduct.OriginalSkus = originalSkus
				virtualSkuCart = append(virtualSkuCart, virtualProduct)
				processedVirtualSkus = append(processedVirtualSkus, virtualProduct.ID)
			}
		} else {
			// Regular product - add to final products
			alreadyPresentSkus[product.ID] = product
			finalProducts = append(finalProducts, product)
		}
	}

	// Second pass: merge original SKUs with existing products if needed
	for _, productToAdd := range newProductsToAddInCart {
		if existingProduct, exists := alreadyPresentSkus[productToAdd.ID]; exists {
			// Product already exists in cart, merge quantities and update pricing
			mergedProduct := mergeProducts(existingProduct, productToAdd, brandDiscountShareMap)

			// Update in final products array
			for i, finalProduct := range finalProducts {
				if finalProduct.ID == existingProduct.ID {
					finalProducts[i] = mergedProduct
					break
				}
			}
		} else {
			// New product, add to final products
			finalProducts = append(finalProducts, productToAdd)
		}
	}

	return &VirtualSkuProcessResult{
		VirtualSkuCart:       virtualSkuCart,
		ProcessedVirtualSkus: processedVirtualSkus,
		FinalProducts:        finalProducts,
	}, nil
}

// Helper function to merge two products (same SKU)
func mergeProducts(existing, toAdd shared.SellerItems, brandDiscountShareMap map[string]float64) shared.SellerItems {
	// Use the existing product as base
	result := existing

	// Sum quantities
	totalQuantity := existing.Quantity + toAdd.Quantity
	result.Quantity = totalQuantity

	// Update meta with weighted average of wholesale rates
	existingMeta := shared.KiranaBazarProductMeta{}
	if err := json.Unmarshal(existing.Meta, &existingMeta); err != nil {
		return result // Return unmodified if unmarshal fails
	}

	addMeta := shared.KiranaBazarProductMeta{}
	if err := json.Unmarshal(toAdd.Meta, &addMeta); err != nil {
		return result // Return unmodified if unmarshal fails
	}

	// Calculate weighted average of wholesale rates
	totalValueW1 := existingMeta.WholesaleRate * float64(existing.Quantity)
	totalValueW2 := addMeta.WholesaleRate * float64(toAdd.Quantity)
	updatedWholesaleRate := (totalValueW1 + totalValueW2) / float64(totalQuantity)

	// handle here rate for brand share and calculate effective brand wholesale rate
	if existingMeta.BrandWholesaleRate == nil {
		existingMeta.BrandWholesaleRate = &existingMeta.WholesaleRate
	}
	if existingMeta.EffectiveWholesaleRate == nil {
		existingMeta.EffectiveWholesaleRate = existingMeta.BrandWholesaleRate
	}
	if addMeta.BrandWholesaleRate == nil {
		addMeta.BrandWholesaleRate = existingMeta.BrandWholesaleRate
	}

	brandShare, exists := brandDiscountShareMap[fmt.Sprint(existing.ID)]
	if !exists {
		brandShare = 0.0 // Default to 0 if not found
	}
	existingBrandDiscount := (*existingMeta.EffectiveWholesaleRate - *existingMeta.BrandWholesaleRate) * float64(existing.Quantity) // here why we have not considered brand share?
	virtualSkuBrandDiscount := (*addMeta.BrandWholesaleRate - addMeta.WholesaleRate) * float64(toAdd.Quantity) * brandShare // here addMeta already contains updated whdolesale rates for skus
	totalDiscount := existingBrandDiscount + virtualSkuBrandDiscount
	perPieceDiscount := totalDiscount / float64(totalQuantity)

	updatedEffectiveWholesaleRate := *existingMeta.BrandWholesaleRate + perPieceDiscount
	existingMeta.EffectiveWholesaleRate = &updatedEffectiveWholesaleRate

	// Update meta with new rate
	updatedWholesaleRate = float64(int(updatedWholesaleRate*100)) / 100.0 // Truncate to 1 decimal place
	existingMeta.WholesaleRate = updatedWholesaleRate
	existingMeta.WholesaleRateString = fmt.Sprintf(`₹%.1f`, updatedWholesaleRate)

	// Recalculate margin
	if existingMeta.MRPNumber > 0 {
		newMarkupMargin := ((existingMeta.MRPNumber - updatedWholesaleRate) / existingMeta.MRPNumber) * 100
		existingMeta.MarkupMargin = newMarkupMargin
		existingMeta.MarkupMarginString = fmt.Sprintf("मार्जिन: %.1f%%", newMarkupMargin)
		existingMeta.MarkupMarginValue = fmt.Sprintf("%.1f%%", newMarkupMargin)
	}

	// Update meta in result
	updatedMeta, err := json.Marshal(existingMeta)
	if err == nil {
		result.Meta = updatedMeta
	}

	return result
}

// FilterAndUpdateProducts processes products and separates them into in-stock and out-of-stock
func (cart *KiranaBazarCart) FilterAndUpdateProducts(allProducts []shared.SellerItems, conditions CartValidationCoinditions, appVersion string, processVirtualSku bool) (inStock, outOfStock []shared.SellerItems, inActive []shared.SellerItems, virtualSkus []shared.SellerItems) {
	inStock = make([]shared.SellerItems, 0)
	outOfStock = make([]shared.SellerItems, 0)
	inActive = make([]shared.SellerItems, 0)

	userDetailsChannel := userdetails.AsyncFetchUserDetails(cart.UserID, []string{userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS,
		userdetails.USER_DETAILS_TYPES.USER_GEOGRAPHY}, 1*time.Minute)
	userDetails := <-userDetailsChannel

	// Extract user cohort names once to avoid repetition
	userCohortNames := []string{}
	userGeography := &userdetails.UserGeoData{}
	if userDetails.Data != nil {
		if userDetails.Data.UserDynamicDetails != nil {
			userCohortNames = userDetails.Data.UserDynamicDetails.UserCohortNames
		}
		if userDetails.Data.UserGeography != nil {
			userGeography = userDetails.Data.UserGeography
		}
	}

	for _, product := range allProducts {
		// Skip products not matching cart seller
		if product.Seller != cart.Seller {
			continue
		}

		if product.Quantity <= 0 {
			continue
		}

		// Get updated product info
		productInfo, exists := products.GetProductByID(product.ID)
		if !exists {
			continue
		}

		// Skip POSM products
		if productInfo.ProductType == string(products.ProductTypePosm) {
			continue
		}

		// Check and adjust quantity based on max cap - SKIP FOR VIRTUAL SKU PROCESSING
		if productInfo.MetaProperties.PackSize > 0 {
			itemCount := int(product.Quantity) * productInfo.MetaProperties.PackSize
			if conditions.CheckMaxProductQuantityCap && productInfo.MetaProperties.MaxCap != nil {
				if itemCount > *productInfo.MetaProperties.MaxCap && productInfo.MetaProperties.PackSize > 0 {
					updatedQuantity := int32(*productInfo.MetaProperties.MaxCap / productInfo.MetaProperties.PackSize)
					product.Quantity = updatedQuantity
				} else if itemCount == *productInfo.MetaProperties.MaxCap && productInfo.MetaProperties.PackSize > 0 {
					disableAddingMoreProducts := true
					productInfo.MetaProperties.DisableAddMore = &disableAddingMoreProducts
				}
			}
		}

		// Check inventory availability and adjust quantity if needed
		if productInfo.DisplayQuantity != nil {
			availableInventory := *productInfo.DisplayQuantity
			currentCartPiecesCount := int(product.Quantity) * productInfo.MetaProperties.PackSize
			if currentCartPiecesCount >= availableInventory && productInfo.MetaProperties.PackSize > 0 {
				// Adjust quantity based on available inventory
				availableInventoryQuantity := int32(availableInventory / productInfo.MetaProperties.PackSize)
				newQuantity := int32(availableInventoryQuantity / 2)
				if newQuantity == 0 {
					newQuantity = 1
				}
				product.Quantity = newQuantity
				disableAddingMoreProducts := true
				productInfo.MetaProperties.DisableAddMore = &disableAddingMoreProducts
			}
		}

		// Create user context for pricing
		sellerSpecificCohorts := userdetails.GetUserDerivedCohorts(cart.UserID, &[]string{productInfo.Seller}, userGeography)
		combinedCohorts := append(userCohortNames, sellerSpecificCohorts...)

		userPricingContext := products.PricingContext{
			UserID:     &cart.UserID,
			Quantity:   int(product.Quantity),
			UserCohort: &combinedCohorts,
		}

		// Convert and preserve quantity
		updatedProduct := productInfo.ToSellerItems(
			products.ToSellerItemsCondition{IncludeVariant: conditions.IncludeVariants},
			appVersion,
			&userPricingContext,
		)

		updatedProduct.Quantity = product.Quantity

		// Categorize based on stock status
		if productInfo.IsOOS == nil || *productInfo.IsOOS == false {
			inStock = append(inStock, updatedProduct)
		} else if productInfo.IsActive != nil && *productInfo.IsActive == false {
			inActive = append(inActive, updatedProduct)
		} else {
			outOfStock = append(outOfStock, updatedProduct)
		}
	}

	if processVirtualSku {
		resp, err := cart.ProcessVirtualSkus(inStock, appVersion)
		if err != nil {
			log.Printf("Error processing virtual SKUs: %v", err)
		}

		return resp.FinalProducts, outOfStock, inActive, resp.VirtualSkuCart
	}

	return inStock, outOfStock, inActive, nil
}

// combineProducts combines in-stock and out-of-stock products based on conditions
func (cart *KiranaBazarCart) combineProducts(inStock, outOfStock []shared.SellerItems, conditions CartValidationCoinditions) []shared.SellerItems {
	if conditions.IncludeOOSProducts {
		// Include both in-stock and out-of-stock products
		return append(outOfStock, inStock...)
	}
	// Return only in-stock products
	return inStock
}

func (cart *KiranaBazarCart) CartValue() (float64, error) {
	allProducts, _ := cart.Cart.Get()
	// Filter and update products
	inStockProducts, _, _, _ := cart.FilterAndUpdateProducts(allProducts, CartValidationCoinditions{CheckMaxProductQuantityCap: true}, "", false)
	totalProductPricing := 0.0
	for _, item := range inStockProducts {
		meta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(item.Meta, &meta)
		if err != nil {
			return 0.0, err
		}
		totalProductPricing += cart.GetProductPricing(item, &meta)
	}
	return totalProductPricing, nil
}

func (cart *KiranaBazarCart) GetProductPricing(item shared.SellerItems, meta *shared.KiranaBazarProductMeta) float64 {
	if meta == nil {
		meta = &shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(item.Meta, &meta)
		if err != nil {
			return 0.0
		}
	}
	return meta.WholesaleRate * float64(item.Quantity) * float64(meta.PackSize)
}

// GetAllProducts from Cart
func (cart *KiranaBazarCart) GetProducts() ([]shared.SellerItems, error) {
	allProducts, _ := cart.Cart.Get()
	return allProducts, nil
}

// Public wrapper functions for the service singleton
func Get(ctx context.Context, userID, seller string) (*KiranaBazarCart, error) {
	if service == nil {
		return nil, fmt.Errorf("cart service not initialized")
	}
	return service.getCart(ctx, userID, seller)
}

func Update(ctx context.Context, userID, seller string, cartData *KiranaBazarCart) error {
	if service == nil {
		return fmt.Errorf("cart service not initialized")
	}
	return service.handleCartUpdate(ctx, userID, seller, cartData)
}

func Empty(ctx context.Context, userID, seller string) error {
	cartData := &KiranaBazarCart{}
	if service == nil {
		return fmt.Errorf("cart service not initialized")
	}
	return service.handleCartUpdate(ctx, userID, seller, cartData)
}

func GetCartObject(userID, seller string, isValid bool, updatedAt time.Time, context []byte, products []shared.SellerItems) (*KiranaBazarCart, error) {
	cartObject := &KiranaBazarCart{
		UserID:    userID,
		UpdatedAt: time.Now(),
		IsValid:   true,
		Seller:    seller,
		Context:   context,
	}
	if len(products) > 0 {
		cartObject.Cart.Set(products)
	}
	return cartObject, nil
}
