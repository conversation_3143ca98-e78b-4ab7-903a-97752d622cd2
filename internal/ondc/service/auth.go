package service

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"io/ioutil"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/auth"
	"net/http"
)

// Auth service methods for your main service
func (s *Service) Login(ctx *gin.Context, req *auth.LoginRequest, clientIP string, userAgent string) (*auth.AuthResponse, error) {
	return s.AuthService.Login(ctx, req, clientIP, userAgent)
}

func (s *Service) RefreshToken(ctx *gin.Context, req *auth.RefreshRequest) (*auth.AuthResponse, error) {
	return s.AuthService.RefreshToken(ctx, req)
}

func (s *Service) Logout(ctx *gin.Context, req *auth.RefreshRequest) error {
	return s.AuthService.Logout(ctx, req)
}

func (s *Service) GenerateCSRFToken(ctx *gin.Context) string {
	return s.AuthService.GenerateCSRFToken()
}

func (s *Service) SaveTrueCallerWebhook(ctx *gin.Context, req dto.TrueCallerWebhookRequest) error {
	key := "truecaller:webhook:" + req.RequestId
	data, err := json.Marshal(req)
	if err != nil {
		return err
	}
	_, err = s.AzureRedis.Create(ctx, key, string(data))
	if err != nil {
		return fmt.Errorf("failed to save webhook data: %w", err)
	}
	return nil
}

func (s *Service) ResolveWebhook(ctx *gin.Context, requestId string) (map[string]interface{}, error) {
	key := "truecaller:webhook:" + requestId
	val, err := s.AzureRedis.Find(ctx, key)
	if err != nil || val == "" {
		return nil, fmt.Errorf("webhook data not found for requestId: %s", requestId)
	}
	var webhook dto.TrueCallerWebhookRequest
	if err := json.Unmarshal([]byte(val), &webhook); err != nil {
		return nil, fmt.Errorf("invalid webhook data: %w", err)
	}

	client := &http.Client{}
	req, err := http.NewRequest("GET", webhook.Endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Authorization", "Bearer "+webhook.AccessToken)
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to call truecaller endpoint: %w", err)
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read truecaller response: %w", err)
	}
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("invalid truecaller response: %w", err)
	}
	return result, nil
}
