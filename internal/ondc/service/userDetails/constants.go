package userdetails

type UserDetailsTypes struct {
	USER_STATIC_DETAILS  string `json:"userStaticDetails"`
	USER_DYNAMIC_DETAILS string `json:"userDynamicDetails"`
	NUX_STATUS           string `json:"nuxStatus"`
	USER_GEOGRAPHY       string `json:"userGeography"`
	NEWS_SCREEN_INDEX    string `json:"newsScreenIndex"`
	USER_ORDER_STATUS    string `json:"userOrderStatus"`
	LAST_SEEN            string `json:"lastSeen"`
	ORDERING_PROGRESS    string `json:"orderingProgress"`
}

var USER_DETAILS_TYPES = UserDetailsTypes{
	USER_STATIC_DETAILS:  "userStaticDetails",
	USER_DYNAMIC_DETAILS: "userDynamicDetails",
	NUX_STATUS:           "nuxStatus",
	USER_GEOGRAPHY:       "userGeography",
	NEWS_SCREEN_INDEX:    "newsScreenIndex",
	USER_ORDER_STATUS:    "userOrderStatus",
	LAST_SEEN:            "lastSeen",
	ORDERING_PROGRESS:    "orderingProgress",
}

const CF_URL = "https://asia-south1-op-d2r.cloudfunctions.net/widgetsResolver"

// const CF_URL = "http://localhost:8080"
