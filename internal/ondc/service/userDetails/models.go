package userdetails

type AsyncResult struct {
	Data *UserDetails
	Err  error
}

type UserDynamicDetails struct {
	UserCohortNames []string    `json:"userCohortNames"`
	UserCohort      interface{} `json:"userCohort"`
}

type UserGeoData struct {
	UserID               string  `json:"user_id"`
	Latitude             float64 `json:"latitude"`
	Longitude            float64 `json:"longitude"`
	GeoCity              string  `json:"geo_city"`
	GeoSubDistrict       string  `json:"geo_sub_district"`
	GeoDistrict          string  `json:"geo_district"`
	GeoState             string  `json:"geo_state"`
	GeoPincode           int     `json:"geo_pincode"`
	GeoCodedCluster      string  `json:"geo_coded_cluster"`
	GeoLevel             string  `json:"geo_level"`
	GeoPopulationDensity string  `json:"geo_population_density"`
	UserProfileCity      string  `json:"user_profile_city"`
	UserProfileState     string  `json:"user_profile_state"`
	H3Res4               string  `json:"h3_res4"`
	H3Res5               string  `json:"h3_res5"`
	H3Res6               string  `json:"h3_res6"`
}

type UserDetails struct {
	UserDynamicDetails *UserDynamicDetails `json:"userDynamicDetails"`
	UserGeography      *UserGeoData        `json:"userGeography"`
}
