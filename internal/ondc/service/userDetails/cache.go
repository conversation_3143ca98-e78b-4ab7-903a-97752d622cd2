package userdetails

import (
	"sync"
	"time"
)

type cacheItem struct {
	data      interface{}
	expiresAt time.Time
}

type UserDataCache struct {
	store map[string]cacheItem
	mu    sync.RWMutex
	ttl   time.Duration
}

var UserDataCacheObject = UserDataCache{
	store: make(map[string]cacheItem),
	ttl:   10 * time.Minute,
}

func (c *UserDataCache) Get(uid, dataType string) (interface{}, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	item, ok := c.store[uid+"_"+dataType]
	if !ok || time.Now().After(item.expiresAt) {
		return nil, false
	}
	return item.data, true
}

func (c *UserDataCache) Set(uid, dataType string, value interface{}) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.store[uid+"_"+dataType] = cacheItem{
		data:      value,
		expiresAt: time.Now().Add(c.ttl),
	}
}
