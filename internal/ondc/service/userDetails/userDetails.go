package userdetails

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"kc/internal/ondc/cache"
	"kc/internal/ondc/external/slack"
	ordervalue "kc/internal/ondc/service/orderBill/orderValue"
	"net/http"
	"strings"
	"time"
)

type CloudFunctionRequestData struct {
	UID  string   `json:"uid"`
	Type []string `json:"type"`
}

type CloudFunctionRequest struct {
	Data *CloudFunctionRequestData `json:"data,omitempty"`
}

type CloudFunctionResponse struct {
	UserDetailsData map[string]interface{} `json:"user_details_data"`
	Error           string                 `json:"error"`
}

func mapToStruct(m map[string]interface{}, result interface{}) error {
	// Marshal map to JSON
	jsonData, err := json.Marshal(m)
	if err != nil {
		return err
	}

	// Unmarshal JSON to struct
	return json.Unmarshal(jsonData, result)
}

func FetchUserDetailsWithCache(
	cloudFnURL, uid string,
	types []string,
	timeout time.Duration,
) (map[string]interface{}, error) {
	if uid == "" || cloudFnURL == "" || len(types) == 0 {
		return nil, errors.New("invalid input to FetchUserDetailsEfficient")
	}

	finalResult := make(map[string]interface{})
	missingTypes := []string{}

	for _, t := range types {
		cacheKey := fmt.Sprintf("user_details:%s:%s", uid, t)
		cacheResult, err := cache.GetInstance().GetCachedData(
			context.Background(),
			cacheKey,
			func() (interface{}, error) {
				return nil, nil // This will be called only if cache misses
			},
			10*time.Minute, // Local cache for 10 minutes
			0,              // not caching in redis
		)

		if err == nil && cacheResult.Data != nil {
			finalResult[t] = cacheResult.Data
		} else {
			missingTypes = append(missingTypes, t)
		}
	}

	if len(missingTypes) > 0 {
		reqBody := CloudFunctionRequestData{
			UID: uid, Type: missingTypes,
		}
		bodyBytes, _ := json.Marshal(reqBody)

		client := &http.Client{Timeout: timeout}
		resp, err := client.Post(cloudFnURL, "application/json", bytes.NewBuffer(bodyBytes))
		if err != nil {
			return nil, fmt.Errorf("cloud function call failed: %w", err)
		}
		defer resp.Body.Close()

		respBytes, _ := io.ReadAll(resp.Body)
		if resp.StatusCode != http.StatusOK {
			return nil, fmt.Errorf("cloud function returned error: %s", string(respBytes))
		}

		var parsed CloudFunctionResponse
		if err := json.Unmarshal(respBytes, &parsed); err != nil {
			return nil, fmt.Errorf("failed to parse cloud function response: %w", err)
		}

		if parsed.Error != "" {
			return nil, fmt.Errorf("cloud function error: %s", parsed.Error)
		}

		for k, v := range parsed.UserDetailsData {
			finalResult[k] = v
			cacheKey := fmt.Sprintf("user_details:%s:%s", uid, k)
			cache.GetInstance().GetCachedData(
				context.Background(),
				cacheKey,
				func() (interface{}, error) {
					return v, nil
				},
				10*time.Minute, // Local cache for 10 minutes
				0,              // not caching in redis
			)
		}
	}

	return finalResult, nil
}

func AsyncFetchUserDetails(
	uid string,
	types []string,
	timeout time.Duration,
) <-chan AsyncResult {
	resultChan := make(chan AsyncResult, 1)

	go func() {
		defer close(resultChan)
		data, err := FetchUserDetailsWithCache(CF_URL, uid, types, timeout)
		if err != nil {
			resultChan <- AsyncResult{Data: nil, Err: err}
			return
		}
		userDetails := UserDetails{}
		err = mapToStruct(data, &userDetails)
		if err != nil {
			resultChan <- AsyncResult{Data: nil, Err: err}
			return
		}

		// add all in user cohorts
		if userDetails.UserDynamicDetails != nil {
			userDetails.UserDynamicDetails.UserCohortNames = append(
				userDetails.UserDynamicDetails.UserCohortNames,
				"ALL_USERS",
			)
		}

		resultChan <- AsyncResult{Data: &userDetails, Err: err}
	}()

	return resultChan
}

func GetUserSellerMinOrderValue(userID string, seller string, updating bool, route string) float64 {
	userDetailsChannel := AsyncFetchUserDetails(userID, []string{USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS}, 10*time.Second)
	// sellerLevelOrderCounts, err := ordervalue.GetSellerLevelOrderCount(userID)
	// if err != nil {
	// 	return -1
	// }
	noOfOrders, err := ordervalue.GetUserSellerLevelConfirmedOrderCount(userID, seller)
	if err != nil {
		return -1
	}
	if !updating {
		noOfOrders += 1
	}
	userDetails := <-userDetailsChannel
	userCohortNames := make([]string, 0)
	if userDetails.Data != nil && userDetails.Data.UserDynamicDetails != nil {
		userCohortNames = userDetails.Data.UserDynamicDetails.UserCohortNames
	}
	minOrderValue, err := ordervalue.GetMinOrderValue(seller, noOfOrders, userCohortNames)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("failed to provide min order value for %s, %s, %s ", userID, seller, route))
		return -1
	}
	return minOrderValue
}

func GetUserDerivedCohorts(uid string, sellers *[]string, userGeography *UserGeoData) []string {
	var userCohorts []string
	userCohorts = append(userCohorts, "ALL_USERS")

	userConfirmedCount, err := ordervalue.GetUserLevelConfirmedOrderCount(uid)
	if err != nil {
		fmt.Println(fmt.Errorf("failed to fetch seller order count: %w", err))
		return userCohorts
	}

	if userConfirmedCount == 0 {
		userCohorts = append(userCohorts, "NEW_USER")
	}
	if userConfirmedCount > 0 {
		userCohorts = append(userCohorts, "OLD_USER")
	}

	if userGeography != nil && userGeography.GeoState != "" {
		userCohorts = append(userCohorts, fmt.Sprintf("STATE_%s", strings.ToUpper(userGeography.GeoState)))
	}

	if sellers == nil || len(*sellers) == 0 {
		return userCohorts
	}

	userSellersConfirmedCount := 0
	for _, seller := range *sellers {
		userSellerConfirmedCount, err := ordervalue.GetUserSellerLevelConfirmedOrderCount(uid, seller)
		if err != nil {
			fmt.Println(fmt.Errorf("failed to fetch seller order count: %w", err))
			return userCohorts
		} else {
			userSellersConfirmedCount += userSellerConfirmedCount
		}
	}

	if userSellersConfirmedCount == 0 {
		userCohorts = append(userCohorts, "NEW_SELLER_USER")
	}
	if userSellersConfirmedCount > 0 {
		userCohorts = append(userCohorts, "OLD_SELLER_USER")
	}

	if uid == "82oXtDgnMadYEZvdUwn3VSFQR273" {
		userCohorts = append(userCohorts, "NEW_USER")
		userCohorts = append(userCohorts, "STATE_ODISHA")
	}

	return userCohorts
}
