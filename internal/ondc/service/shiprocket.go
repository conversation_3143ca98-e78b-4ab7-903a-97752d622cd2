package service

import (
	"context"
	"encoding/json"
	"kc/internal/ondc/external/shiprocket"
	"kc/internal/ondc/models/dto"
	"strings"
)

var CourierNewtorkPriorityMap = map[string][]string{
	"zoff_foods": {
		"Ecom Brands",
		"Xpressbees Brands",
		"Delhivery Surface",
		"Blue Dart Surface",
		"DTDC Surface",
		"DTDC Surface 20kg",
		"Loadshare Hyperlocal",
		"Quick-Ola",
		"Xpressbees Reverse Surface",
		"Delhivery Reverse Surface",
		" India Post-Business Parcel Surface",
		"India Post - Speed Post Air",
		"Ecom Express Surface 10kg",
		"Ecom Express Surface 5kg",
		"SRX Priority",
		"DTDC Surface 10kg",
		"Xpressbees Surface 20kg",
		"Xpressbees Surface 10kg",
		"Xpressbees Surface 5kg",
		"Xpressbees Surface 2kg",
		"Ecom Express Surface 2kg",
		"SRX Premium Plus",
		"Xpressbees Air",
	},
	"apsara_tea": {},
}

var PICKUP_POSTAL_CODE = map[string]string{
	"zoff_foods": "493221",
	"apsara_tea": "453111",
}

func (s *Service) CheckShipRocketServiceAbility(ctx context.Context, oms string, request *shiprocket.CourierServiceAblityAPIRequest) (dto.AppserviceAbilityAPIResponse, error) {
	response := dto.AppserviceAbilityAPIResponse{
		Data: dto.AppServiceAbilityAPIData{
			Servicable: false,
			Message:    "",
		},
	}
	// setting pickup pincode to zoff pincode
	pickupPincode := PICKUP_POSTAL_CODE[oms]
	if request.Weight == "0" || request.Weight == "" {
		request.Weight = "1"
	}
	if request.COD == "0" || request.COD == "" {
		request.COD = "1"
	}
	params := map[string]interface{}{
		"cod":               request.COD,
		"pickup_postcode":   pickupPincode,
		"delivery_postcode": request.DeliveryPostCode,
		"weight":            request.Weight,
	}
	serviceAbilityResponse, err := shiprocket.CallShipRocketAPI(oms, "SERVICE_ABILITY", "{}", params)
	if err != nil {
		return response, err
	}
	serviceAbilityAPIResponse := shiprocket.CourierServiceAbilityAPIResponse{}
	err = json.Unmarshal(serviceAbilityResponse, &serviceAbilityAPIResponse)
	if err != nil {
		return response, err
	}

	deliveryTime := ""
	courierServiceName := ""
	CourierNewtorkPriority := CourierNewtorkPriorityMap[oms]
	if len(CourierNewtorkPriority) > 0 {
		for _, j := range CourierNewtorkPriority {
			for _, r := range serviceAbilityAPIResponse.Data.AvailableCourierCompanies {
				if strings.TrimSpace(strings.ToLower(r.CourierName)) == strings.TrimSpace(strings.ToLower(j)) {
					deliveryTime = r.EstimatedDeliveryDays
					courierServiceName = r.CourierName
					break
				}
			}
			if deliveryTime != "" {
				break
			}
		}
		if deliveryTime == "" {
			return response, err
		} else {
			return dto.AppserviceAbilityAPIResponse{
				Data: dto.AppServiceAbilityAPIData{
					Servicable:          true,
					Message:             "",
					MinimumDeliveryDays: deliveryTime,
					CourierServiceName:  courierServiceName,
				},
			}, err
		}
	} else {
		// get the first courier service
		if len(serviceAbilityAPIResponse.Data.AvailableCourierCompanies) > 0 {
			deliveryTime = serviceAbilityAPIResponse.Data.AvailableCourierCompanies[0].EstimatedDeliveryDays
			courierServiceName = serviceAbilityAPIResponse.Data.AvailableCourierCompanies[0].CourierName
			return dto.AppserviceAbilityAPIResponse{
				Data: dto.AppServiceAbilityAPIData{
					Servicable:          true,
					Message:             "",
					MinimumDeliveryDays: deliveryTime,
					CourierServiceName:  courierServiceName,
				},
			}, err
		}
	}
	return response, err
}
