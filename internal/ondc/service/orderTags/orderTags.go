package ordertags

import (
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/utils"
	"log"
	"slices"

	"github.com/Masterminds/semver"
)

func checkForBulkOrder(orderAmount float64) bool {
	return orderAmount > BULK_ORDER_MINIMUM_AMOUNT
}

func CheckForFirstTimeOrder(userOrderStats dto.UserOrderStats) bool {
	return userOrderStats.OrderPlaced == 0
}

func checkForPowerUser(userOrderStats dto.UserOrderStats) bool {
	return userOrderStats.OrderDelivered >= POWER_USER_ORDERS_DELIVERED
}

func checkForSpam(userOrderStats dto.UserOrderStats) bool {
	if userOrderStats.ConfirmedOrder == 0 {
		return false
	}

	returnPercentage := float64(userOrderStats.ReturnedOrder) / float64(userOrderStats.ConfirmedOrder)

	if userOrderStats.ConfirmedOrder == 2 && userOrderStats.ReturnedOrder == 2 {
		return true
	}

	if userOrderStats.ConfirmedOrder >= 3 && userOrderStats.ConfirmedOrder <= 5 && returnPercentage >= 0.6 {
		return true
	}

	if userOrderStats.ConfirmedOrder >= 6 && returnPercentage >= 0.4 {
		return true
	}
	// return userOrderStats.ConfirmedOrder > 0 && userOrderStats.TotalConfirmedOrderAmount > 0 && (userOrderStats.TotalReturnedOrderAmount/userOrderStats.TotalConfirmedOrderAmount) > RET_TO_CNF_RATIO
	return false
}

func checkForManualCall(userStats dto.UserOrderStats, confirmedOrderCount int, appVersion, seller string) bool {
	userID := userStats.UserID

	// Parse the app version
	currentVersion, err := semver.NewVersion(appVersion)
	if err != nil || appVersion == "" {
		currentVersion = nil
	}

	// Define the minimum required version
	minVersion, _ := semver.NewVersion("6.4.1")

	// Determine if the user is using an old app and is a spam user
	isSpamWithOldApp := currentVersion != nil &&
		currentVersion.LessThan(minVersion) &&
		IsUserSpam(userStats)

	// Conditions for requiring manual call
	return utils.NON_IVR_USERS[userID] || isSpamWithOldApp
}
func IsUserSpam(userOrderStats dto.UserOrderStats) bool {
	return checkForSpam(userOrderStats)
}

func GetOrderTags(orderAmount float64, userOrderStats dto.UserOrderStats, paidAmount float64, userCurrentOrderCount int, userAcurateConfirmedOrderCount int, appVersion, seller string, userCohortNames []string) ([]int, []string, []dao.KiranaBazarOrderDetailsTags) {
	tags := []int{}
	tagNames := []string{}
	tagsObject := []dao.KiranaBazarOrderDetailsTags{}
	if checkForBulkOrder(orderAmount) {
		tags = append(tags, BULK)
		tagNames = append(tagNames, tagIdDataMap[BULK].Tag)
		tagsObject = append(tagsObject, dao.KiranaBazarOrderDetailsTags{
			TagId: BULK,
			Tag:   tagIdDataMap[BULK].Tag,
		})

	}
	if CheckForFirstTimeOrder(userOrderStats) {
		tags = append(tags, FIRST_TIME)
		tagNames = append(tagNames, tagIdDataMap[FIRST_TIME].Tag)
		tagsObject = append(tagsObject, dao.KiranaBazarOrderDetailsTags{
			TagId: FIRST_TIME,
			Tag:   tagIdDataMap[FIRST_TIME].Tag,
		})
	}
	if checkForPowerUser(userOrderStats) {
		tags = append(tags, POWER_USER)
		tagNames = append(tagNames, tagIdDataMap[POWER_USER].Tag)
		tagsObject = append(tagsObject, dao.KiranaBazarOrderDetailsTags{
			TagId: POWER_USER,
			Tag:   tagIdDataMap[POWER_USER].Tag,
		})
	}
	if checkForSpam(userOrderStats) {
		tags = append(tags, SPAM)
		tagNames = append(tagNames, tagIdDataMap[SPAM].Tag)
		tagsObject = append(tagsObject, dao.KiranaBazarOrderDetailsTags{
			TagId: SPAM,
			Tag:   tagIdDataMap[SPAM].Tag,
		})
	}
	if checkForManualCall(userOrderStats, userAcurateConfirmedOrderCount, appVersion, seller) {
		tags = append(tags, MANUAL_CALL)
		tagNames = append(tagNames, tagIdDataMap[MANUAL_CALL].Tag)
		tagsObject = append(tagsObject, dao.KiranaBazarOrderDetailsTags{
			TagId: MANUAL_CALL,
			Tag:   tagIdDataMap[MANUAL_CALL].Tag,
		})
	}
	if paidAmount > 0 {
		tags = append(tags, PAYMENT_FAIL)
		tagNames = append(tagNames, tagIdDataMap[PAYMENT_FAIL].Tag)
		tagsObject = append(tagsObject, dao.KiranaBazarOrderDetailsTags{
			TagId: PAYMENT_FAIL,
			Tag:   tagIdDataMap[PAYMENT_FAIL].Tag,
		})
	}

	if len(userCohortNames) > 0 && slices.Contains(userCohortNames, "RSB Geography Cohort") {
		tags = append(tags, RSB_COHORT_USER)
		tagNames = append(tagNames, tagIdDataMap[RSB_COHORT_USER].Tag)
		tagsObject = append(tagsObject, dao.KiranaBazarOrderDetailsTags{
			TagId: RSB_COHORT_USER,
			Tag:   tagIdDataMap[RSB_COHORT_USER].Tag,
		})
	}

	return tags, tagNames, tagsObject
}

func GetTagFromTagId(tagId int) *dto.KiranaBazarOrderTags {
	tag, exists := tagIdDataMap[tagId]
	if !exists {
		return nil
	}
	return &tag
}

func LoadKiranaBazarOrderTags(repo *sqlRepo.Repository) error {
	activeOrderTags := []dao.KiranaBazarOrderTags{}
	query := "SELECT * FROM kiranabazar_order_tags WHERE is_active = 1"
	_, err := repo.CustomQuery(&activeOrderTags, query)
	if err != nil {
		log.Panicf("failed to fetch active order tags: %v", err)
		return err
	}
	for i := range activeOrderTags {
		orderTag := activeOrderTags[i]
		tagIdDataMap[int(orderTag.ID)] = dto.KiranaBazarOrderTags{
			ID:          orderTag.ID,
			Tag:         orderTag.Tag,
			IsActive:    orderTag.IsActive,
			Description: orderTag.Description,
			UpdatedBy:   orderTag.UpdatedBy,
			UpdatedAt:   orderTag.UpdatedAt,
			CreatedAt:   orderTag.CreatedAt,
		}
	}
	return nil
}
