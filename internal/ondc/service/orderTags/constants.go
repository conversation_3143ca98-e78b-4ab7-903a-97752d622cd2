package ordertags

import "kc/internal/ondc/models/dto"

var tagIdDataMap map[int]dto.KiranaBazarOrderTags = make(map[int]dto.KiranaBazarOrderTags)

var (
	BULK_ORDER_MINIMUM_AMOUNT   = 4000.00
	POWER_USER_ORDERS_DELIVERED = 5
	RET_TO_CNF_RATIO            = 0.5
	AUTO_CONFIRM_MAX_AMOUNT     = 4000.0
)
var (
	FIRST_TIME       = 1
	POWER_USER       = 2
	BULK             = 3
	SPAM             = 4
	MANUAL_CALL      = 5
	PAYMENT_FAIL     = 6
	PARTIAL_PAID     = 7
	FULLY_PAID       = 8
	PAYMENT_PENDING  = 9
	ADVANCE_REFUNDED = 10
	REFUNDED         = 11
	COD_AVAILABLE    = 12
	RSB_COHORT_USER  = 13
)
