package ordertags

import (
	"kc/internal/ondc/models/dto"
)

var ORDER_CHECK_TYPES = struct {
	ALLOW_ORDER            string
	SERVICEABILITY_BLOCKED string
	HIGHRISK_USER          string
	SPAM_USER              string
	AUTO_CONFIRM_ORDER     string
}{
	ALLOW_ORDER:            "ALLOW_ORDER",
	SERVICEABILITY_BLOCKED: "SERVICEABILITY_BLOCKED",
	HIGHRISK_USER:          "HIGHRISK_USER",
	SPAM_USER:              "SPAM_USER",
	AUTO_CONFIRM_ORDER:     "AUTO_CONFIRM_ORDER",
}

func PrePlaceOrderCheck(userOrderStats dto.UserOrderStats) *string {

	// High Risk User Check
	// if IsHighRiskUser(userOrderStats) {
	// 	result := ORDER_CHECK_TYPES.HIGHRISK_USER
	// 	return &result
	// }

	// Spam User Check
	if IsUserSpam(userOrderStats) {
		result := ORDER_CHECK_TYPES.SPAM_USER
		return &result
	}

	return nil
}

func CreateOrderCheck(userOrderStats dto.UserOrderStats, geoPincode, shippingPostalCode, seller, selectedPaymentMode string, totalCartValue float64) *string {

	// Auto Confirm Order Check
	if IsAutoConfirmOrder(userOrderStats, shippingPostalCode, geoPincode, selectedPaymentMode, seller, totalCartValue) {
		result := ORDER_CHECK_TYPES.AUTO_CONFIRM_ORDER
		return &result
	}

	// 1️⃣ Trusted User Whitelist
	if IsWhiteListUser(userOrderStats) {
		result := ORDER_CHECK_TYPES.ALLOW_ORDER
		return &result
	}

	// 2️⃣ Serviceable Pincode Check
	if IsPincodeServiceabilityBlocked(geoPincode, shippingPostalCode, seller) {
		result := ORDER_CHECK_TYPES.SERVICEABILITY_BLOCKED
		return &result
	}
	return nil
}
