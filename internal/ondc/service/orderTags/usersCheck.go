package ordertags

import (
	"kc/internal/ondc/external/delhivery"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/orderStatus/constants"
	"kc/internal/ondc/utils"
)

func checkForHighRiskUser(userOrderStats dto.UserOrderStats) bool {
	// Ensure return rate is computed if not already
	returnRate := calculateUserReturnRate(userOrderStats.ReturnedOrder, userOrderStats.OrderDelivered)
	if returnRate >= 0.50 && userOrderStats.ConfirmedOrder >= 4 {
		return true
	}
	return false
}

func checkWhiteListUser(userOrderStats dto.UserOrderStats) bool {
	returnRate := calculateUserReturnRate(userOrderStats.ReturnedOrder, userOrderStats.OrderDelivered)
	if returnRate < 0.30 && userOrderStats.OrderDelivered >= 4 {
		return true
	}
	return false
}

func checkServiceAbilityBlockedPincode(shippingPostCode, geoPincode string) bool {
	if shippingPostCode != "" && geoPincode != "" {
		return delhivery.DELHIVERY_BLOCKED_PINCODES[shippingPostCode] || delhivery.DELHIVERY_BLOCKED_PINCODES[geoPincode]
	}
	if shippingPostCode != "" {
		return delhivery.DELHIVERY_BLOCKED_PINCODES[shippingPostCode]
	}
	if geoPincode != "" {
		return delhivery.DELHIVERY_BLOCKED_PINCODES[geoPincode]
	}
	return false
}

func semiCheckAutoConfirmOrder(userOrderStats dto.UserOrderStats, selectedPaymentMode string, totalCartValue float64) bool {
	return userOrderStats.OrderDelivered >= 2 && selectedPaymentMode == constants.COD && totalCartValue < AUTO_CONFIRM_MAX_AMOUNT
}

func IsHighRiskUser(userOrderStats dto.UserOrderStats) bool {
	return checkForHighRiskUser(userOrderStats)
}

func IsWhiteListUser(userOrderStats dto.UserOrderStats) bool {
	return checkWhiteListUser(userOrderStats)
}

func IsPincodeServiceabilityBlocked(shippingPostCode, geoPincode, seller string) bool {
	return false
	return checkServiceAbilityBlockedPincode(shippingPostCode, geoPincode) && includes([]string{utils.MILDEN, utils.APSARA_TEA, utils.CRAVITOS, utils.MICHIS}, seller)
}

func IsAutoConfirmOrder(userOrderStats dto.UserOrderStats, shippingPostCode, geoPincode, selectedPaymentMode, seller string, totalCartValue float64) bool {
	isPincodeBlocked := IsPincodeServiceabilityBlocked(shippingPostCode, geoPincode, seller)

	return semiCheckAutoConfirmOrder(userOrderStats, selectedPaymentMode, totalCartValue) &&
		!isPincodeBlocked &&
		!checkForSpam(userOrderStats) &&
		!checkForHighRiskUser(userOrderStats)
}
