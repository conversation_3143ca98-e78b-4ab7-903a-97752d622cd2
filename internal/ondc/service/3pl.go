package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	lspAllocation "kc/internal/ondc/service/LSPAllocation"
	awbMasterDTO "kc/internal/ondc/service/awbMaster/dto"
	processingstatus "kc/internal/ondc/service/orderStatus/processingStatus"
	"strconv"
	"time"

	"github.com/Kirana-Club/3pl-go/pkg/models"
)

const (
	OMSInternal = "INTERNAL"
	SourceB2B   = "B2B"
)

var (
	ErrInvalidOrderID    = fmt.Errorf("order id invalid")
	ErrNoLSPFound        = fmt.Errorf("no LSP serviceable for this pincode")
	ErrFailedToPushOrder = fmt.Errorf("failed to push order to OMS")
)

type OrderProcessingData struct {
	OrderInfo      dao.KiranaBazarOrder
	OrderDetails   *dao.KiranaBazarOrderDetails
	PackageDetails dao.KiranabazarOrderPackageDetails
	LSPs           []lspAllocation.LSP
}

type CourierProcessingResult struct {
	AWBs        []string
	Errors      []string
	AssignedLSP lspAllocation.LSP
	CourierName string
}

func (s *Service) PushOrderTo3PL(ctx context.Context, oid string, updatedBy string, invoiceResp *dto.CreateExtInvoiceResponse) (*dto.PushOrderToOMSResponse, error) {
	orderID, err := s.validateAndParseOrderID(oid)
	if err != nil {
		return s.createErrorResponse(oid, "Order id invalid"), err
	}

	orderData, err := s.gatherOrderData(ctx, orderID)
	if err != nil {
		return s.createErrorResponse(oid, "Failed to gather order data"), err
	}

	courierResult, err := s.processCourierAllocation(ctx, orderData, oid)
	if err != nil {
		return s.createErrorResponse(oid, "Failed to process courier allocation"), err
	}

	if len(courierResult.AWBs) == 0 {
		return s.createErrorResponse(oid, "Failed to push order to OMS"),
			fmt.Errorf("courier processing failed: %v", courierResult.Errors)
	}

	masterAWB := courierResult.AWBs[0]

	// this is temp code to insert the awb number in master to handle the reverse orderid mapping in case of ekart as courier
	TRUE := true
	UD := "UD"
	orderituint := uint64(orderID)
	updatedAt := time.Now().UnixMilli()
	accountName := "kiranaclub"
	courier := courierResult.CourierName
	if courierResult.AssignedLSP == lspAllocation.LSP(lspAllocation.LSPs.EKART_LARGE_SURFACE) {
		courier = "Ekart Large"
	} else if courierResult.AssignedLSP == lspAllocation.LSP(lspAllocation.LSPs.DELHIVERY_SURFACE) {
		courier = "Delhivery"
	} else if courierResult.AssignedLSP == lspAllocation.LSP(lspAllocation.LSPs.EKART_NON_LARGE_SURFACE) {
		courier = "Ekart"
	}

	for _, awb := range courierResult.AWBs {
		s.AWBMaster.Create(context.Background(), awbMasterDTO.CreateAWBRequest{
			AWBNumber:           &awb,
			ReferenceID:         &oid,
			OrderID:             &orderituint,
			Courier:             &courier,
			Status:              &processingstatus.SHIPMENT_MANIFESTED,
			IsActive:            &TRUE,
			LastStatusUpdatedAt: &updatedAt,
			AccountName:         &accountName,
			UpdatedBy:           &updatedBy,
			NSLCode:             &processingstatus.SHIPMENT_MANIFESTED,
			StatusType:          &UD,
			Instructions:        &processingstatus.SHIPMENT_CREATED,
			StatusCode:          &processingstatus.SHIPMENT_CREATED,
		})
	}

	type shippingLabelResult struct {
		response *dto.GenerateShippingLabelResponse
		err      error
	}

	shippingLabelChan := make(chan shippingLabelResult)

	// Start shipping label generation goroutine
	go func() {
		resp, err := s.processShippingLabel(ctx, oid, orderID)
		shippingLabelChan <- shippingLabelResult{response: resp, err: err}
	}()

	// Wait for operations to complete
	shippingLabelRes := <-shippingLabelChan

	if shippingLabelRes.err != nil {
		return s.createErrorResponse(oid, "Failed to generate shipping label"), shippingLabelRes.err
	}

	shippingLabel := shippingLabelRes.response

	s.processShipmentCreation(ctx, &ShipmentCreationParams{
		OrderID:          oid,
		OrderIDInt:       orderID,
		CourierRequested: orderData.LSPs[0],
		AssignedCourier:  courierResult.AssignedLSP,
		CourierName:      courierResult.CourierName,
		MasterAWB:        masterAWB,
		ShippingLabelURL: shippingLabel.Result.URL,
		PackageDetails:   orderData.PackageDetails.GetPackageDetails(),
		InvoiceResponse:  invoiceResp,
		UpdatedBy:        updatedBy,
	})

	return &dto.PushOrderToOMSResponse{
		Success:       true,
		Message:       "Order pushed to OMS successfully",
		OrderID:       oid,
		Invoice:       invoiceResp.InvoiceURL,
		PrintingLabel: shippingLabel.Result.URL,
		OMS:           OMSInternal,
		Courier:       courierResult.CourierName,
		AWBNumber:     masterAWB,
	}, nil
}

func (s *Service) validateAndParseOrderID(oid string) (int64, error) {
	orderID, err := strconv.ParseInt(oid, 10, 64)
	if err != nil || orderID <= 0 {
		return 0, ErrInvalidOrderID
	}
	return orderID, nil
}

func (s *Service) gatherOrderData(ctx context.Context, orderID int64) (*OrderProcessingData, error) {
	orderInfo, err := GetOrderInfo(s.repository, orderID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch order info: %w", err)
	}

	orderDetails, err := GetOrderDetails(s.repository, fmt.Sprintf("%d", orderID))
	if err != nil {
		return nil, fmt.Errorf("failed to fetch order details: %w", err)
	}

	packageDetails, err := GetPackageDetails(s.repository, orderID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch package details: %w", err)
	}

	lsps := s.LSPAllocationService.GetServiceableLSPs(*orderDetails.ShippingAddress.PostalCode, orderInfo.Seller)
	if len(lsps) == 0 {
		return nil, ErrNoLSPFound
	}

	return &OrderProcessingData{
		OrderInfo:      orderInfo,
		OrderDetails:   orderDetails,
		PackageDetails: packageDetails,
		LSPs:           lsps,
	}, nil
}

func (s *Service) processCourierAllocation(ctx context.Context, orderData *OrderProcessingData, oid string) (*CourierProcessingResult, error) {
	result := &CourierProcessingResult{
		AWBs:   make([]string, 0),
		Errors: make([]string, 0),
	}

	for _, lsp := range orderData.LSPs {
		switch lsp {
		case lspAllocation.LSP(lspAllocation.LSPs.DELHIVERY_SURFACE):
			awbs, errors, err := s.processDelhiveryOrder(ctx, oid)
			if err != nil {
				continue
			}
			result.AWBs = awbs
			result.Errors = errors
			result.CourierName = "Delhivery"
			result.AssignedLSP = lsp
			return result, nil
		case lspAllocation.LSP(lspAllocation.LSPs.EKART_LARGE_SURFACE):
			awbs, errors, err := s.processEkartOrder(ctx, oid)
			if err != nil {
				continue
			}
			result.AWBs = awbs
			result.Errors = errors
			result.CourierName = "Ekart"
			result.AssignedLSP = lsp
			return result, nil
		case lspAllocation.LSP(lspAllocation.LSPs.EKART_NON_LARGE_SURFACE):
			continue
		default:
			continue
		}
	}

	return result, errors.New("no LSPs processed")
}

func (s *Service) processDelhiveryOrder(ctx context.Context, oid string) ([]string, []string, error) {
	delhiveryResponse, err := s.PushOrderToDelhivery(ctx, oid)
	if err != nil {
		return nil, nil, err
	}
	awbs, errorss := extractAWBsFromDelhiveryResponse(delhiveryResponse)
	// add aws to db
	if len(awbs) > 0 {
		err = s.UpdateAWBsInDatabase(oid, awbs, "Delhivery")
		return awbs, errorss, err
	}
	return awbs, errorss, errors.New("no awbs created")
}

func (s *Service) processEkartOrder(ctx context.Context, oid string) ([]string, []string, error) {
	ekartResponse, err := s.PushOrderToEkart(ctx, oid)
	if err != nil {
		return nil, nil, err
	}
	awbs, errorss := extractAWBsFromEkartResponse(ekartResponse)
	// add aws to db
	if len(awbs) > 0 {
		err = s.UpdateAWBsInDatabase(oid, awbs, "Ekart")
		return awbs, errorss, err
	}
	return awbs, errorss, errors.New("no awbs created")
}

func (s *Service) UpdateAWBsInDatabase(oid string, awbs []string, courier string) error {
	if len(awbs) == 0 {
		return errors.New("no awbs to update")
	}

	// Convert awbs slice to proper JSON array
	awbsJSON, err := json.Marshal(awbs)
	if err != nil {
		return fmt.Errorf("failed to marshal awbs to JSON: %w", err)
	}

	// Upsert query - insert or update on duplicate key
	query := `
		INSERT INTO kiranabazar_order_status (id, awb_numbers, awb_number, courier, created_at, updated_at) 
		VALUES (?, ?, ?, ?, NOW(), NOW()) 
		ON DUPLICATE KEY UPDATE 
			awb_numbers = VALUES(awb_numbers),
			awb_number = VALUES(awb_number),
			courier = VALUES(courier),
			updated_at = NOW()
	`

	if err := s.repository.Db.Exec(query, oid, string(awbsJSON), awbs[0], courier).Error; err != nil {
		return fmt.Errorf("failed to upsert AWB data: %w", err)
	}

	return nil
}

// reverseInvoiceGeneration is used to remove the created invoice url if anything fails
func (s *Service) reverseInvoiceGeneration(ctx context.Context, oid string, orderID int64) error {
	// Delete the invoice url from the database
	query := `UPDATE kiranabazar_order_payments SET ext_invoice = '' WHERE order_id = ?`
	if err := s.repository.Db.Exec(query, orderID).Error; err != nil {
		return fmt.Errorf("failed to delete invoice from database: %w", err)
	}
	return nil
}

// processInvoiceGeneration helps create invoice in shipment manifestation process.
func (s *Service) processInvoiceGeneration(ctx context.Context, oid, courier, masterAWB string, orderID int64) (*dto.CreateExtInvoiceResponse, error) {
	invoiceResp, err := s.CreateExtInvoice(ctx, dto.CreateExtInvoiceRequest{
		OrderID: oid,
		Courier: courier,
		AWB:     masterAWB,
	})
	if err != nil {
		return nil, err
	}

	err = s.updateInvoiceInDatabase(orderID, invoiceResp.InvoiceURL, invoiceResp.InvoiceID)
	return &invoiceResp, err
}

func (s *Service) processShippingLabel(ctx context.Context, oid string, orderID int64) (*dto.GenerateShippingLabelResponse, error) {
	shippingLabel, err := s.GenerateShippingLabel(ctx, &dto.GenerateShippingLabelRequest{
		Data: dto.GenerateShippingLabelRequestData{
			OrderID: oid,
		},
	})
	if err != nil {
		return nil, err
	}

	err = s.updateShippingLabelInDatabase(orderID, shippingLabel.Result.URL)
	return shippingLabel, err
}

type ShipmentCreationParams struct {
	OrderID          string
	OrderIDInt       int64
	CourierRequested lspAllocation.LSP
	AssignedCourier  lspAllocation.LSP
	CourierName      string
	MasterAWB        string
	ShippingLabelURL string
	PackageDetails   shared.PackageDetails
	InvoiceResponse  *dto.CreateExtInvoiceResponse
	UpdatedBy        string
}

func (s *Service) processShipmentCreation(ctx context.Context, params *ShipmentCreationParams) {
	go func() {
		shipmentReq := dto.ShipmentCreatedRequest{
			Data: dto.ShipmentCreatedRequestData{
				OrderID:           params.OrderID,
				OMSOrderID:        fmt.Sprintf("KC_%06d", params.OrderIDInt),
				OrderStatus:       processingstatus.SHIPMENT_CREATED,
				CourierRequested:  string(params.CourierRequested),
				CourierAssigned:   string(params.AssignedCourier),
				Courier:           params.CourierName,
				AWBNumber:         params.MasterAWB,
				ShippingLabel:     params.ShippingLabelURL,
				PackageDetails:    params.PackageDetails,
				CSGT:              params.InvoiceResponse.CGST,
				IGST:              params.InvoiceResponse.IGST,
				SGST:              params.InvoiceResponse.SGST,
				BuyerGST:          params.InvoiceResponse.BuyerGST,
				NoOfSkus:          params.InvoiceResponse.NoOfSkus,
				ItemQuantity:      params.InvoiceResponse.ItemQuantity,
				Discount:          params.InvoiceResponse.Discount,
				InvoiceURL:        params.InvoiceResponse.InvoiceURL,
				InvoiceID:         params.InvoiceResponse.InvoiceID,
				InvoiceAmount:     params.InvoiceResponse.InvoiceAmount,
				InvoiceCreatedAt:  time.Now().UnixMilli(),
				ShipmentCreatedAt: time.Now().UnixMilli(),
				Source:            SourceB2B,
				KCShip:            true,
				OMS:               OMSInternal,
				UpdatedBy:         params.UpdatedBy,
			},
		}

		if _, err := s.HandleShipmentCreatedOrder(context.Background(), shipmentReq); err != nil {
			s.handleAsyncError("HandleShipmentCreatedOrder", params.OrderID, err)
		}
	}()
}

func (s *Service) createErrorResponse(orderID, message string) *dto.PushOrderToOMSResponse {
	return &dto.PushOrderToOMSResponse{
		Success: false,
		Message: message,
		OrderID: orderID,
		OMS:     OMSInternal,
	}
}

func (s *Service) updateInvoiceInDatabase(orderID int64, invoiceURL, invoiceNumber string) error {
	query := `UPDATE kiranabazar_order_payments SET ext_invoice = ?, ext_invoice_number = ? WHERE order_id = ?`

	if err := s.repository.Db.Exec(query, invoiceURL, invoiceNumber, orderID).Error; err != nil {
		return err
	}
	return nil
}

func (s *Service) updateShippingLabelInDatabase(orderID int64, labelURL string) error {
	query := `UPDATE kiranabazar_order_details SET printing_label = ? WHERE order_id = ?`

	if err := s.repository.Db.Exec(query, labelURL, orderID).Error; err != nil {
		return err
	}
	return nil
}

func (s *Service) handleAsyncError(operation, orderID string, err error) {
	errorMsg := fmt.Sprintf("Error in %s for order %s: %v", operation, orderID, err)
	slack.SendSlackMessage(errorMsg)
}

func extractAWBsFromDelhiveryResponse(delhiveryResponse *models.ManifestResponse) ([]string, []string) {
	awbs := make([]string, 0, len(delhiveryResponse.Packages))
	errors := make([]string, 0)

	for _, pkg := range delhiveryResponse.Packages {
		if pkg.Status == "Success" {
			awbs = append(awbs, pkg.Waybill)
		} else {
			errors = append(errors, pkg.Remarks...)
		}
	}

	return awbs, errors
}

func extractAWBsFromEkartResponse(ekartResponse *models.ManifestResponse) ([]string, []string) {
	if len(ekartResponse.Waybills) == 0 {
		return []string{}, []string{"not able to create waybill"}
	}
	return ekartResponse.Waybills, []string{}
}
