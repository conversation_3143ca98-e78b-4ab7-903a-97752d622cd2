package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	awbMasterDTO "kc/internal/ondc/service/awbMaster/dto"
	"kc/internal/ondc/service/orderStatus/constants"
	shipdelightstatus "kc/internal/ondc/service/orderStatus/shipdelightStatus"
)

/* ShipDelightWebhook takes the request body(rb), converts rb into json and stores it in table "shipdelight_webhook_logs" once done it add the details in
"kiranabazar_awb_master" table and returns the success message. */

func (s *Service) ShipDelightWebhook(ctx context.Context, request any) (interface{}, error) {

	reqJSonString, err := json.Marshal(request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		return nil, err
	}

	_, err = s.repository.Create(&dao.ShipDelightWebhookLogs{
		RequestObj: reqJSonString,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	})

	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		return nil, err
	}

	typeCastedRequest := dto.ShipDelightWebhookRequest{}
	err = json.Unmarshal(reqJSonString, &typeCastedRequest)

	if err != nil {
		logger.Error(ctx, "failed to type assert request to TypeCastedShipDelightWebhookRequest")
		return nil, errors.New("invalid request type")
	}

	if strings.ToUpper(typeCastedRequest.CourierName) == "DELHIVERY" {
		return "success - delhivery order found", nil
	}

	if !strings.Contains(strings.ToLower(typeCastedRequest.OrderNo), "kc") || strings.Contains(strings.ToLower(typeCastedRequest.OrderNo), "rep") {
		return "success - non kc order", nil
	}

	go s.AWBMaster.EnqueueAWBSync(typeCastedRequest.AirwayBillNo, typeCastedRequest.CourierName)

	// adding the log data to the awb scans
	s.AWBMaster.CreateAWBScans(context.Background(), string(reqJSonString))

	orderID := ExtractOrderID(typeCastedRequest.OrderNo)
	courier := typeCastedRequest.CourierName
	updatedBy := constants.SHIPDELIGHT_WEBHOOK
	TRUE := true
	accountName := "kiranaclub"
	statusType := shipdelightstatus.GetStatusType(typeCastedRequest.StatusCode)

	// check of the order exists
	result := s.repository.Db.Select("1").Limit(1).First(&dao.KiranaBazarOrder{}, "id = ?", orderID)
	exists := result.RowsAffected > 0

	if !exists {
		//inserting only for kc orders
		return "success - order not found", nil
	}

	lastStatusUpdated, _ := timeToEpochMilliseconds(typeCastedRequest.UpdatedDate)
	_, err = s.AWBMaster.Create(context.Background(), awbMasterDTO.CreateAWBRequest{
		AWBNumber:           &typeCastedRequest.AirwayBillNo,
		Instructions:        &typeCastedRequest.Remarks,
		ReferenceID:         &typeCastedRequest.OrderNo,
		OrderID:             &orderID,
		Courier:             &courier,
		Status:              &typeCastedRequest.Status,
		IsActive:            &TRUE,
		LastStatusUpdatedAt: &lastStatusUpdated,
		UpdatedAt:           &lastStatusUpdated,
		AccountName:         &accountName,
		UpdatedBy:           &updatedBy,
		NSLCode:             &typeCastedRequest.LspStatusCode,
		StatusCode:          &typeCastedRequest.StatusCode,
		StatusType:          &statusType,
	})

	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("failed to create awb master err %v", err))
		slack.SendSlackMessage(string(reqJSonString))
		return "success", nil
	}

	go s.fetchAndUpdatePrimaryWaybill(orderID, constants.SHIPDELIGHT_WEBHOOK)

	return "success", nil
}

func timeToEpochMilliseconds(timeStr string) (int64, error) {
	// Parse the time string using the layout format
	layout := "2006-01-02 15:04:05"
	t, err := time.Parse(layout, timeStr)
	if err != nil {
		return 0, err
	}
	return t.UnixMilli(), nil
}
