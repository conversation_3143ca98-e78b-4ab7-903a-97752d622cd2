package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"kc/internal/ondc/models/dto"
	"time"

	"github.com/google/uuid"
)

func (s *Service) Cancel(ctx context.Context, req *dto.AppCancelRequest) (*dto.AppCancelResponse, error) {
	msgId := uuid.NewString()
	identifier := fmt.Sprintf("%s%s", req.Meta.TransactionID, msgId)
	ttl := "PT30S"
	cancelRequest := &dto.CancelRequest{
		Context: &dto.Context{
			Domain: "ONDC:RET10",
			Location: &dto.Location{
				City: &dto.City{
					Code: "std:080",
				},
				Country: &dto.Country{
					Code: "IND",
				},
			},
			Action:        "cancel",
			CoreVersion:   &ONDC_CORE_VERSION,
			BapID:         &ONDC_BAP_ID,
			BapURI:        &ONDC_BAP_URI,
			BppID:         req.BppID,
			BppURI:        req.BppURL,
			TransactionID: &req.Meta.TransactionID,
			MessageID:     &msgId,
			Timestamp:     time.Now(),
			TTL:           ttl,
		},
		Message: &dto.CancelMessage{
			OrderID:              req.OrderID,
			CancellationReasonID: req.CancellationReasonID,
		},
	}

	adjustedReqJSON, err := json.Marshal(cancelRequest)
	if err != nil {
		logger.Error(ctx, "Marshal adjusted request failed: %v", err)
		return nil, err
	}

	fmt.Println("cancel req is", string(adjustedReqJSON))

	request, err := s.createONDCRequest(ctx, "cancel", req.BppURL, adjustedReqJSON)
	if err != nil {
		logger.Error(ctx, "Cancel request failed: %v", err)
		return nil, err
	}

	// send a request to ONDC network
	response, err := s.httpClient.Do(request)
	if err != nil {
		logger.Error(ctx, "Sending request to ONDC network failed: %v", err)
		return nil, err
	}
	body, err := io.ReadAll(response.Body)
	if err != nil {
		logger.Error(ctx, "Error reading response body: %v", err)
		return nil, err
	}

	fmt.Printf("Cancel Response Body: %s\n", body)
	defer response.Body.Close()
	fmt.Println("setting in Cancel", identifier)

	resp, err := s.syncifyClient.WaitForAsyncReq(ctx, identifier)
	if err != nil {
		logger.Error(ctx, "error in ONDC calcel api, error is %s", err.Error())
		return nil, err
	}
	redisResp, ok := resp.(string)
	if !ok {
		logger.Error(ctx, "not able to typecast the redis resp")
		return nil, fmt.Errorf("not able to typecast the redis resp")
	}
	appCancelResponse := &dto.AppCancelResponse{}
	err = json.Unmarshal([]byte(redisResp), appCancelResponse)
	if err != nil {
		logger.Error(ctx, "not able to unmarshal the redis resp, err is %s", err.Error())
		return nil, err
	}
	appCancelResponse.Meta.TransactionID = req.Meta.TransactionID
	return appCancelResponse, nil

}

func (s *Service) OnCancel(ctx context.Context, req *dto.OnCancelRequest) error {
	key := fmt.Sprintf("%s%s", *req.Context.TransactionID, *req.Context.MessageID)
	fmt.Println("setting in cancel", key)

	meta := dto.Meta{
		Provider:    req.Message.Order.Provider,
		Items:       req.Message.Order.Items,
		Tags:        req.Message.Order.Tags,
		Payment:     req.Message.Order.Payment,
		Fulfillment: req.Message.Order.Fulfillments,
		BppID:       req.Context.BppID,
		BppUrl:      req.Context.BppURI,
	}

	resp := dto.AppCancelResponse{
		ID:           req.Message.Order.ID,
		OrderState:   req.Message.Order.State,
		Cancellation: *req.Message.Order.Cancellation,
		Meta:         meta,
	}
	if req.Error != nil && req.Error.Code != nil {
		resp.Error = dto.AppResponseError{
			Code:        req.Error.Code,
			Message:     &req.Error.Message,
			Description: &req.Error.Path,
			Type:        &req.Error.Type,
		}
	}

	_, err := s.Cache.Create(ctx, key, resp)
	if err != nil {
		return err
	}
	return nil

}
