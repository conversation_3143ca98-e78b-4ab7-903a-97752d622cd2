package service

import (
	"context"
	"fmt"
	"kc/internal/ondc/cache"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"strconv"
	"time"
)

// GetUserAddress is logic layer for the function
func (s *Service) GetUserAddress(ctx context.Context, request *dto.GetUserAddressRequest, applyCache ...*bool) (response dto.GetUserAddressResponse, err error) {
	// Create cache key based on user ID and address ID
	cacheKey := fmt.Sprintf("user_address:%s", request.UserID)
	if request.AddressID != nil {
		cacheKey = fmt.Sprintf("%s:%s", cacheKey, *request.AddressID)
	}

	localCacheExpiry := 10 * time.Minute
	if len(applyCache) > 0 && applyCache[0] != nil && !*applyCache[0] {
		localCacheExpiry = 0
	}

	// Try to get from cache first
	cacheResult, err := cache.GetInstance().GetCachedData(
		ctx,
		cacheKey,
		func() (interface{}, error) {
			userAddresses := []dao.UserAddress{}
			query := fmt.Sprintf(`select * from user_address where user_id = '%s' and is_active = 1 `, request.UserID)
			if request.AddressID != nil {
				query += fmt.Sprintf(" and id = %s ", *request.AddressID)
			}
			query += " order by is_default desc "
			_, err := s.repository.CustomQuery(&userAddresses, query)
			if err != nil {
				return nil, err
			}
			return userAddresses, nil
		},
		localCacheExpiry, // Local cache for 10 minutes
		0,                // not caching in redis
	)

	if err != nil {
		return response, err
	}

	userAddresses, ok := cacheResult.Data.([]dao.UserAddress)
	if !ok {
		return response, fmt.Errorf("invalid cache data type")
	}

	for _, address := range userAddresses {
		var minimumDeliveryDays *string = nil
		if (address.IsDefault || len(userAddresses) == 1) && request.Data.Seller != "" && request.Data.Source != "" {
			serviceabilityRequest := dto.AppServiceAblityAPIRequest{
				UserID:           request.UserID,
				DeliveryPostCode: *address.PostalCode,
				Data: dto.AppServiceAbilityAPIRequestData{
					Seller: request.Data.Seller,
					Source: request.Data.Source,
				},
			}

			response, err := s.CheckServiceAbility(ctx, &serviceabilityRequest)
			if err == nil {
				if response.Data.Servicable && response.Data.MinimumDeliveryDays != "" {
					if request.Data.Seller == utils.LOTS {
						minimumDeliveryDaysString := fmt.Sprintf("%s दिन", response.Data.MinimumDeliveryDays)
						minimumDeliveryDays = &minimumDeliveryDaysString
					} else {
						minimumDeliveryDaysInt, _ := strconv.Atoi(response.Data.MinimumDeliveryDays)
						minimumDeliveryDaysString := fmt.Sprintf("%s - %d दिन", response.Data.MinimumDeliveryDays, minimumDeliveryDaysInt+2)
						minimumDeliveryDays = &minimumDeliveryDaysString
					}
				} else {
					if request.Data.Seller == utils.MILDEN {
						minimumDeliveryDaysString := "ऑफ़र की भारी माँग के कारण डिलीवरी में 10-12 दिन लग सकते हैं।"
						minimumDeliveryDays = &minimumDeliveryDaysString
					} else {
						minimumDeliveryDaysString := fmt.Sprintf("%d - %d दिन", 5, 7)
						minimumDeliveryDays = &minimumDeliveryDaysString
					}
				}
			}
		} else {
			if request.Data.Seller == utils.MILDEN {
				minimumDeliveryDaysString := "ऑफ़र की भारी माँग के कारण डिलीवरी में 10-12 दिन लग सकते हैं।"
				minimumDeliveryDays = &minimumDeliveryDaysString
			} else {
				minimumDeliveryDaysString := fmt.Sprintf("%d - %d दिन", 5, 7)
				minimumDeliveryDays = &minimumDeliveryDaysString
			}
		}

		var tag *string = nil
		if address.Tag != nil && *address.Tag == utils.ADDRESS_STORE {
			tagValue := utils.ADDRESS_STORE_HINDI
			tag = &tagValue
		} else if address.Tag != nil && *address.Tag == utils.ADDRESS_HOME {
			tagValue := utils.ADDRESS_HOME_HINDI
			tag = &tagValue
		}
		userAddress := dto.UserAddress{
			ID:                  *address.ID,
			Name:                *address.Name,
			Phone:               *address.Phone,
			Line:                *address.Line,
			District:            *address.District,
			State:               *address.State,
			PostalCode:          *address.PostalCode,
			Tag:                 *tag,
			UpdatedAt:           address.UpdatedAt,
			IsDefault:           address.IsDefault,
			Latitude:            address.Latitude,
			Longitude:           address.Longitude,
			GST:                 address.GST,
			Neighbourhood:       address.Neighbourhood,
			HouseNumber:         address.HouseNumber,
			Landmark:            address.Landmark,
			Village:             address.Village,
			Line1:               address.Line1,
			Line2:               address.Line2,
			StoreName:           address.StoreName,
			MinimumDeliveryDays: minimumDeliveryDays,
			BillingAddressID:    address.BillingAddress,
		}
		if address.AlternatePhone != nil {
			userAddress.AlternatePhone = *address.AlternatePhone
		}
		response.Address = append(response.Address, userAddress)
	}
	return
}

// UpdateUserAddress is logic layer for the function
func (s *Service) UpdateUserAddress(ctx context.Context, request dto.UpdateUserAddressRequest) (dto.UpdateUserAddressResponse, error) {
	response := dto.UpdateUserAddressResponse{}
	if request.Address.IsDefault {
		_, err := s.repository.CustomQuery(nil, fmt.Sprintf(`update user_address set is_default = false where user_id = '%s'`, request.UserID))
		if err != nil {
			return response, err
		}
	}
	var billingAddressID *uint64
	if request.BillingAddress != nil {
		billingAddressDao := s.createAddressDAO(*request.BillingAddress)
		billingAddressDao.CreatedAt = time.Now()
		billingAddressDao.UserID = &request.UserID
		var address interface{}
		var err error
		if billingAddressDao.ID == nil || *billingAddressDao.ID == 0 {
			billingAddressDao.ID = nil
			address, err = s.repository.Create(&billingAddressDao)
			if err != nil {
				return response, err
			}
		} else {
			address, _, err = s.repository.Update(&dao.UserAddress{ID: billingAddressDao.ID}, &billingAddressDao)
			if err != nil {
				return response, err
			}
		}

		billingAddress := address.(*dao.UserAddress)
		if err != nil {
			return response, err
		}
		billingAddressID = billingAddress.ID
	}

	newDao := s.createAddressDAO(request.Address)
	newDao.BillingAddress = billingAddressID
	_, rowsAffected, err := s.repository.Update(dao.UserAddress{ID: &request.Address.ID}, newDao)
	if err != nil {
		return response, err
	}
	if rowsAffected == 0 {
		return response, fmt.Errorf("failed to update the data")
	}

	alternatePhone := ""
	if newDao.AlternatePhone != nil {
		alternatePhone = *newDao.AlternatePhone
	}

	response.Data.Address = dto.UserAddress{
		ID:               *newDao.ID,
		Name:             *newDao.Name,
		Phone:            *newDao.Phone,
		Line:             *newDao.Line,
		District:         *newDao.District,
		State:            *newDao.State,
		PostalCode:       *newDao.PostalCode,
		Tag:              *newDao.Tag,
		UpdatedAt:        newDao.UpdatedAt,
		IsDefault:        newDao.IsDefault,
		Latitude:         newDao.Latitude,
		Longitude:        newDao.Longitude,
		GST:              newDao.GST,
		Neighbourhood:    newDao.Neighbourhood,
		HouseNumber:      newDao.HouseNumber,
		Landmark:         newDao.Landmark,
		Village:          newDao.Village,
		Line1:            newDao.Line1,
		Line2:            newDao.Line2,
		StoreName:        newDao.StoreName,
		AlternatePhone:   alternatePhone,
		BillingAddressID: newDao.BillingAddress,
	}

	cacheKey := fmt.Sprintf("user_address:%s:%d", request.UserID, *newDao.ID)
	_ = cache.GetInstance().Clear(ctx, cacheKey)

	return response, nil
}

// CreateUserAddress is logic layer to store the user address
func (s *Service) SaveUserAddress(ctx context.Context, request dto.SaveUserAddressRequest) (dto.SaveUserAddressResponse, error) {
	response := dto.SaveUserAddressResponse{}
	if request.Address.IsDefault {
		_, err := s.repository.CustomQuery(nil, fmt.Sprintf(`update user_address set is_default = false where user_id = '%s'`, request.UserID))
		if err != nil {
			return response, err
		}
	}
	var billingAddressID *uint64
	if request.BillingAddress != nil {
		billingAddressDao := s.createAddressDAO(*request.BillingAddress)
		billingAddressDao.ID = nil
		billingAddressDao.CreatedAt = time.Now()
		billingAddressDao.UserID = &request.UserID
		address, err := s.repository.Create(&billingAddressDao)
		billingAddress := address.(*dao.UserAddress)
		if err != nil {
			return response, err
		}
		billingAddressID = billingAddress.ID
	}
	addressDao := s.createAddressDAO(request.Address)
	addressDao.ID = nil
	addressDao.CreatedAt = time.Now()
	addressDao.UserID = &request.UserID
	addressDao.BillingAddress = billingAddressID

	address, err := s.repository.Create(&addressDao)

	newAddress := address.(*dao.UserAddress)
	if err != nil {
		return response, err
	}
	response.Data.Address = dto.UserAddress{
		ID:               *newAddress.ID,
		Name:             *newAddress.Name,
		Phone:            *newAddress.Phone,
		Line:             *newAddress.Line,
		District:         *newAddress.District,
		State:            *newAddress.State,
		PostalCode:       *newAddress.PostalCode,
		Tag:              *newAddress.Tag,
		UpdatedAt:        newAddress.UpdatedAt,
		IsDefault:        newAddress.IsDefault,
		Latitude:         newAddress.Latitude,
		Longitude:        newAddress.Longitude,
		GST:              newAddress.GST,
		Neighbourhood:    newAddress.Neighbourhood,
		HouseNumber:      newAddress.HouseNumber,
		Landmark:         newAddress.Landmark,
		Village:          newAddress.Village,
		Line1:            newAddress.Line1,
		Line2:            newAddress.Line2,
		StoreName:        newAddress.StoreName,
		BillingAddressID: newAddress.BillingAddress,
	}
	if newAddress.AlternatePhone != nil {
		response.Data.Address.AlternatePhone = *newAddress.AlternatePhone
	}

	return response, nil
}

func (s *Service) UpdateDefaultAddress(ctx context.Context, request dto.UpdateUserAddressRequest) (dto.UpdateUserAddressResponse, error) {
	if request.Address.ID == 0 || request.UserID == "" {
		return dto.UpdateUserAddressResponse{}, fmt.Errorf("address ID and user_id is required")
	}
	response := dto.UpdateUserAddressResponse{}
	_, err := s.repository.CustomQuery(nil, fmt.Sprintf(`UPDATE user_address SET is_default = false WHERE user_id = '%s'`, request.UserID))
	if err != nil {
		return response, fmt.Errorf("failed to reset default addresses: %v", err)
	}

	_, err = s.repository.CustomQuery(nil, fmt.Sprintf(`UPDATE user_address SET is_default = true WHERE user_id = '%s' AND id = %d`, request.UserID, request.Address.ID))
	if err != nil {
		return response, fmt.Errorf("failed to set the specified address as default: %v", err)
	}

	response.Data.Address = request.Address
	response.Data.Address.IsDefault = true

	cacheKey := fmt.Sprintf("user_address:%s:%d", request.UserID, request.Address.ID)
	_ = cache.GetInstance().Clear(ctx, cacheKey)

	cacheKey = fmt.Sprintf("user_address:%s", request.UserID)
	_ = cache.GetInstance().Clear(ctx, cacheKey)

	return response, nil
}

func (s *Service) GetUserOrderStats(ctx context.Context, request dto.GetUserOrderStatsRequest) (dto.GetUserOrderStatsResponse, error) {
	response := dao.UserOrderStats{}
	_, err := s.repository.Find(map[string]interface{}{
		"user_id": request.UserID,
	}, &response)
	if err != nil {
		return dto.GetUserOrderStatsResponse{}, err
	}

	userOrderStats := dto.UserOrderStats{
		UserID:                    response.UserID,
		TotalPlacedOrderAmount:    response.TotalPlacedOrderAmount,
		TotalDeliveredOrderAmount: response.TotalDeliveredOrderAmount,
		OrderPlaced:               response.OrderPlaced,
		OrderDelivered:            response.OrderDelivered,
		ConfirmedOrder:            response.ConfirmedOrder,
		TotalConfirmedOrderAmount: response.TotalConfirmedOrderAmount,
		TotalCancelledOrderAmount: response.TotalCancelledOrderAmount,
		CancelledOrder:            response.CancelledOrder,
		ReturnedOrder:             response.ReturnedOrder,
		TotalReturnedOrderAmount:  response.TotalReturnedOrderAmount,
	}

	return dto.GetUserOrderStatsResponse{
		Data: userOrderStats,
	}, nil
}

func (s *Service) createAddressDAO(address dto.UserAddress) dao.UserAddress {
	addressTag := utils.ADDRESS_TAG[address.Tag]

	storeHouseName := ""
	if addressTag == utils.ADDRESS_STORE {
		storeHouseName = address.StoreName
		address.HouseNumber = ""
	} else if addressTag == utils.ADDRESS_HOME {
		storeHouseName = address.HouseNumber
		address.StoreName = ""
	}
	line1 := fmt.Sprintf("%s, %s", storeHouseName, address.Neighbourhood)
	line2 := fmt.Sprintf("%s, %s", address.Village, address.Landmark)
	if address.Line == "" {
		address.Line = fmt.Sprintf("%s, %s", line1, line2)
	}
	newDao := dao.UserAddress{
		ID:            &address.ID,
		Name:          &address.Name,
		Phone:         &address.Phone,
		Line:          &address.Line,
		District:      &address.District,
		State:         &address.State,
		PostalCode:    &address.PostalCode,
		Tag:           &addressTag,
		UpdatedAt:     time.Now(),
		IsActive:      true,
		IsDefault:     address.IsDefault,
		Latitude:      address.Latitude,
		Longitude:     address.Longitude,
		GST:           address.GST,
		Neighbourhood: address.Neighbourhood,
		HouseNumber:   address.HouseNumber,
		StoreName:     address.StoreName,
		Landmark:      address.Landmark,
		Village:       address.Village,
		Line1:         line1,
		Line2:         line2,
	}
	if address.AlternatePhone != "" {
		newDao.AlternatePhone = &address.AlternatePhone
	}
	return newDao
}

func (s *Service) GetUserAddressMeta(ctx context.Context, request *dto.GetUserAddressMetaRequest) (response dto.GetUserAddressMetaResponse, err error) {
	response.MetaData.States = INDIAN_STATES
	if request.Data.AddressID != "" {
		// send user address here
		getAddressResponse, err := s.GetUserAddress(ctx, &dto.GetUserAddressRequest{
			UserID:     request.UserID,
			AddressID:  &request.Data.AddressID,
			AppVersion: request.Meta.AppVersion,
		})
		if err != nil || len(getAddressResponse.Address) == 0 {
			return response, err
		}
		response.Data.Address = getAddressResponse.Address[0]
		return response, nil
	}
	if request.Data.Latitude != nil && request.Data.Longitude != nil {
		var locationData *dto.GeoLocationResponse
		locationData, err = utils.GetLocationDetailsFromLatLong(*request.Data.Latitude, *request.Data.Longitude)
		if err == nil {
			// TODO:@sitaram cache user details here
			var name string
			err = s.FirebaseRepository.RtDb.NewRef(fmt.Sprintf(`/users/%s/name`, request.UserID)).Get(ctx, &name)
			if err != nil {
				fmt.Println("Error getting name from Firebase:", err)
				name = ""
			}

			var shopName string
			err = s.FirebaseRepository.RtDb.NewRef(fmt.Sprintf(`/users/%s/storeName`, request.UserID)).Get(ctx, &shopName)
			if err != nil {
				fmt.Println("Error getting shop name from Firebase:", err)
				shopName = ""
			}

			var phoneNumber int
			err = s.FirebaseRepository.RtDb.NewRef(fmt.Sprintf(`/users/%s/phoneNumber`, request.UserID)).Get(ctx, &phoneNumber)
			if err != nil {
				fmt.Println("Error getting phone number from Firebase:", err)
				phoneNumber = 0
			}
			response.Data.Address.Name = name
			response.Data.Address.StoreName = shopName
			if phoneNumber != 0 {
				response.Data.Address.Phone = strconv.Itoa(phoneNumber)
			}
			response.Data.Address.Latitude = *request.Data.Latitude
			response.Data.Address.Longitude = *request.Data.Longitude
			response.Data.Address.District = locationData.Data.District
			response.Data.Address.State = locationData.Data.State
			response.Data.Address.PostalCode = locationData.Data.Pincode
		}
	}
	return response, nil
}
