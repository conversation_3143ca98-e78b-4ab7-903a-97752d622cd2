package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"kc/internal/ondc/models/dto"
	"log"
	"net/http"
	"time"
)

// ConvertUrlsToZip converts a list of URLs to a ZIP file using the external API
func (s *Service) ConvertUrlsToZip(ctx context.Context, urls []string) (*dto.ConvertUrlsToZipResponse, error) {
	if len(urls) == 0 {
		return nil, fmt.Errorf("no URLs provided for ZIP conversion")
	}

	request := dto.ConvertUrlsToZipExternalRequest{
		Data: dto.ConvertUrlsToZipExternalData{
			Urls: urls,
		},
	}

	response, err := fetchConvertUrlsToZip(request)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// fetchConvertUrlsToZip makes the HTTP request to the ZIP conversion API
func fetchConvertUrlsToZip(request dto.ConvertUrlsToZipExternalRequest) (dto.ConvertUrlsToZipResponse, error) {
	fmt.Println("calling convert URLs to ZIP external api")
	const (
		baseURL      = "https://asia-south1-op-d2r.cloudfunctions.net"
		endpoint     = "/convertUrlstoZIP"
		timeout      = 120 * time.Second // Increased timeout for ZIP processing
		maxRetries   = 3
		retryBackoff = 500 * time.Millisecond
	)

	var (
		resp       *http.Response
		respBody   []byte
		retryCount = 0
		lastErrMsg string
	)

	for retryCount <= maxRetries {
		payloadBytes, err := json.Marshal(request)
		if err != nil {
			return dto.ConvertUrlsToZipResponse{}, fmt.Errorf("failed to marshal request: %w", err)
		}

		// Create request
		client := &http.Client{Timeout: timeout}
		req, err := http.NewRequest(http.MethodPost, baseURL+endpoint, bytes.NewReader(payloadBytes))
		if err != nil {
			return dto.ConvertUrlsToZipResponse{}, fmt.Errorf("failed to create request: %w", err)
		}

		// Set headers
		req.Header.Set("Content-Type", "application/json")

		// Execute request
		resp, err = client.Do(req)

		if err == nil && resp.StatusCode == http.StatusOK {
			// Success - break out of retry loop
			defer resp.Body.Close()

			// Read response
			respBody, err = io.ReadAll(resp.Body)
			if err != nil {
				lastErrMsg = fmt.Sprintf("failed to read response body: %v", err)
				retryCount++
				time.Sleep(retryBackoff * time.Duration(retryCount))
				continue
			}

			response := dto.ConvertUrlsToZipResponse{}
			err = json.Unmarshal(respBody, &response)
			if err != nil {
				return dto.ConvertUrlsToZipResponse{}, fmt.Errorf("failed to unmarshal response: %w", err)
			}

			// Log response for debugging
			log.Printf("Convert URLs to ZIP response: %s", string(respBody))

			// Check if the conversion was successful
			if !response.Result.Success {
				return dto.ConvertUrlsToZipResponse{}, fmt.Errorf("ZIP conversion failed")
			}

			return response, nil
		}

		// Handle error case
		if resp != nil {
			respBody, _ = io.ReadAll(resp.Body)
			resp.Body.Close()
			lastErrMsg = fmt.Sprintf("unexpected status code: %d, body: %s", resp.StatusCode, string(respBody))
		} else {
			lastErrMsg = fmt.Sprintf("failed to execute request: %v", err)
		}

		// Retry logic
		retryCount++
		if retryCount <= maxRetries {
			log.Printf("Retrying convert URLs to ZIP request (attempt %d of %d): %s", retryCount, maxRetries, lastErrMsg)
			time.Sleep(retryBackoff * time.Duration(retryCount))
		}
	}

	// All retries failed
	return dto.ConvertUrlsToZipResponse{}, fmt.Errorf("after %d retries, last error: %s", maxRetries, lastErrMsg)
}
