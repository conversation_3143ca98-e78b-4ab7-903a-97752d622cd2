package service

import (
	"context"
	"errors"
	"fmt"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/external/razorpay"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/external/whatsapp"
	"kc/internal/ondc/infrastructure/logging"
	"kc/internal/ondc/infrastructure/payments"
	report "kc/internal/ondc/middleware"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/queue"
	queueModels "kc/internal/ondc/queue/models"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	ordervalue "kc/internal/ondc/service/orderBill/orderValue"
	cancelReason "kc/internal/ondc/service/orderReason/cancelReason"
	orderstatus "kc/internal/ondc/service/orderStatus"
	"kc/internal/ondc/service/orderStatus/constants"
	processingStatus "kc/internal/ondc/service/orderStatus/processingStatus"
	shipmentstatus "kc/internal/ondc/service/orderStatus/shipmentStatus"
	ordertags "kc/internal/ondc/service/orderTags"
	"kc/internal/ondc/utils"
	"math"
	"sort"
	"strconv"
	"sync"
	"time"

	"github.com/Masterminds/semver"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/mixpanel/mixpanel-go"
)

type RNNCacheStruct struct {
	items      []dto.RNNItem
	Expiration time.Time
	Set        bool
	mu         sync.RWMutex
}

var RNNCache = RNNCacheStruct{
	Set: false,
}

var paymentLogger = logging.GetLogrusLogger("payment_processing")

func (s *Service) GetPaymentMethods(userId string, seller string, amount float64, appVersion string) (dto.PaymentSection, *dto.WaysToPayDataV2, dto.PaymentSectionHeader, error) {
	paymentCtx := context.Background()
	paymentCtx = logging.WithUserID(paymentCtx, userId)

	conditions, flag := utils.BRAND_PAYMENT_CONDITIONS[seller]
	productScreenChangesApkVersion, _ := semver.NewVersion("6.5.3")
	userAppVersion, _ := semver.NewVersion(appVersion)

	var paymentMethods []dto.WaysToPayDataV2
	var paymentSection dto.PaymentSection
	var defaultPaymentMethod *dto.WaysToPayDataV2 = nil
	var paymentSectionHeader = dto.PaymentSectionHeader{}
	if !flag {
		conditions, flag = utils.BRAND_PAYMENT_CONDITIONS["default"]
	}
	if !flag {
		paymentLogger.Warn(paymentCtx, "No payment conditions found for seller", "seller", seller)
		return paymentSection, defaultPaymentMethod, paymentSectionHeader, nil
	}
	var methodData dto.WaysToPayDataV2

	userOrderStats, err := s.GetUserOrderStats(context.Background(), dto.GetUserOrderStatsRequest{
		UserID: userId,
	})
	// isSpam := false
	compulsoryPrepayment := false
	if err == nil {
		// isSpam = ordertags.IsUserSpam(userOrderStats.Data)
		preplaceOrderChecks := ordertags.PrePlaceOrderCheck(userOrderStats.Data)
		if preplaceOrderChecks != nil && (*preplaceOrderChecks == ordertags.ORDER_CHECK_TYPES.HIGHRISK_USER || *preplaceOrderChecks == ordertags.ORDER_CHECK_TYPES.SPAM_USER) {
			compulsoryPrepayment = true
		}
	}

	for _, condition := range conditions {
		//if !utils.CheckApplicableOn(condition.ApplicableOn, "payment") {
		//	continue
		//}
		if condition.MinimumAmountForValidity > amount || condition.MaximumAmountForValidity < amount {
			continue
		}

		if condition.Type == constants.COD && compulsoryPrepayment {
			continue
		}

		methodData = dto.WaysToPayDataV2{
			Id:             utils.PaymentMethodsMap[condition.Type].Id,
			Title:          utils.PaymentMethodsMap[condition.Type].Title,
			ImageUrl:       utils.PaymentMethodsMap[condition.Type].ImageUrl,
			Text:           utils.PaymentMethodsMap[condition.Type].Text,
			Type:           condition.Type,
			CTAText:        utils.PaymentMethodsMap[condition.Type].CTAText,
			Notes:          dto.PaymentNotes{},
			DiscountColors: utils.PaymentMethodsMap[condition.Type].DiscountColors,
			ActiveColor:    utils.PaymentMethodsMap[condition.Type].ActiveColor,
		}

		if utils.PaymentMethodsMap[condition.Type].BottomBarData != nil {
			methodData.BottomBarData = utils.PaymentMethodsMap[condition.Type].BottomBarData
		}

		if utils.PaymentMethodsMap[condition.Type].PaymentNudge != nil {
			methodData.PaymentNudge = utils.PaymentMethodsMap[condition.Type].PaymentNudge
		}

		paymentDiscount := condition.DiscountFixedAmount
		if paymentDiscount < 0 {
			paymentDiscount = math.Floor(min(amount*condition.DiscountPercentage, condition.MaximumDiscountAmount))
		}

		if seller == utils.KIRANA_CLUB {
			methodData.Text.Text = "आपको ₹450 का मुनाफा होगा 🤩"
			methodData.Text.Style = dto.TextStyle{
				Color:      "#01A501",
				FontWeight: "500",
			}
			// methodData.Text.HighlightText[0].Content = "आपको ₹450 का मुनाफा होगा 🤩"
			methodData.Text.HighlightText = append(methodData.Text.HighlightText, dto.Text{
				Content: "आपको ₹450 का मुनाफा होगा 🤩",
				Style: dto.TextStyle{
					Color:      "#01A501",
					FontWeight: "500",
				},
			})
		}

		amountAfterDiscount := utils.Round(amount - paymentDiscount)

		payableAmount := condition.Fixed
		if payableAmount < 0 {
			payableAmount = utils.Round(max(min(amountAfterDiscount*condition.Percentage, condition.MaximumPaymentAmount), condition.MinimumPaymentAmount))
			if utils.Round(payableAmount) != utils.Round(amountAfterDiscount) {
				payableAmount = math.Ceil((math.Round(payableAmount / 100)) * 100)
			}
		}
		payableAmount = min(payableAmount, amountAfterDiscount)

		methodData.ActualAmount = utils.Round(amount)
		methodData.PaymentDiscount = utils.Round(paymentDiscount)

		methodData.PaymentAmount = utils.Round(payableAmount)
		methodData.LeftToPayAmount = utils.Round(amount - payableAmount)
		methodData.DefferAmount = utils.Round(amount - (payableAmount + paymentDiscount))
		//if seller == utils.KIRANA_CLUB {
		//	methodData.PaymentAmount = 100
		//	methodData.LeftToPayAmount = 0
		//	methodData.DefferAmount = 0
		//}

		paymentMethods = append(paymentMethods, methodData)
	}

	var section1 = make([]dto.WaysToPayDataV2, 0)
	var section2 = make([]dto.WaysToPayDataV2, 0)

	confirmedOrderCount := 0
	confirmedOrderCount, err = ordervalue.GetUserLevelConfirmedOrderCount(userId)
	if err != nil {
		logger.Error(context.Background(), "Error in getting user confirmed order count", "error", err)
	}
	if confirmedOrderCount > 0 {
		for i := range paymentMethods {
			if paymentMethods[i].PaymentNudge != nil {
				for j := range paymentMethods {
					if paymentMethods[j].Id == paymentMethods[i].PaymentNudge.SecondaryCTA.PaymentId {
						paymentMethods[i].PaymentNudge.SecondaryCTA.Option = &paymentMethods[j]
						paymentMethods[i].PaymentNudge.SubHeading = fmt.Sprintf("₹%.0f बचाएं", utils.Round(paymentMethods[j].PaymentDiscount))
					}
				}
			}
		}
	} else {
		for i := range paymentMethods {
			if paymentMethods[i].PaymentNudge != nil {
				paymentMethods[i].PaymentNudge = nil
			}
		}
	}

	for _, method := range paymentMethods {
		if method.Type == constants.COD {
			section2 = append(section2, method)
		} else {
			section1 = append(section1, method)
		}
	}

	sort.Slice(section1, func(i, j int) bool {
		return section1[i].Id < section1[j].Id
	})

	sort.Slice(section2, func(i, j int) bool {
		return section2[i].Id < section2[j].Id
	})

	paymentSection.Section1 = section2
	paymentSection.Section2 = section1

	if len(section2) > 0 {
		paymentSectionHeader.PaymentSection1 = &dto.WaysToPayText{
			Text: "कैश ऑन डिलीवरी",
		}
	}
	if len(section1) > 0 {
		paymentSectionHeader.PaymentSection2 = &dto.WaysToPayText{
			Text: fmt.Sprintf("ऑनलाइन पेमेंट करें, ₹%.0f बचाएं ✅", utils.Round(section1[0].PaymentDiscount)),
			HighlightText: []dto.Text{
				{
					Content: fmt.Sprintf("₹%.0f बचाएं", utils.Round(section1[0].PaymentDiscount)),
					Style: dto.TextStyle{
						Color:      "#028A26",
						FontWeight: "500",
					},
				},
			},
		}
	}

	if confirmedOrderCount > 0 {
		if len(section1) >= 2 {
			defaultPaymentMethod = &section1[1]
		} else if len(section1) >= 1 {
			defaultPaymentMethod = &section1[0]
		} else if len(section2) != 0 {
			defaultPaymentMethod = &section2[0]
		}
	} else {
		if len(section2) != 0 {
			defaultPaymentMethod = &section2[0]
		} else if len(section1) >= 2 {
			defaultPaymentMethod = &section1[1]
		} else if len(section1) >= 1 {
			defaultPaymentMethod = &section1[0]
		}
	}

	if appVersion != "" && (userAppVersion.GreaterThan(productScreenChangesApkVersion) || userAppVersion.Equal(productScreenChangesApkVersion)) {
		// defaultPaymentMethod = nil
	}

	return paymentSection, defaultPaymentMethod, paymentSectionHeader, nil
}

func (s *Service) IsCODAvailable(userId string, seller string, amount float64) bool {
	paymentCtx := context.Background()
	paymentCtx = logging.WithUserID(paymentCtx, userId)

	conditions, flag := utils.BRAND_PAYMENT_CONDITIONS[seller]
	if !flag {
		conditions, flag = utils.BRAND_PAYMENT_CONDITIONS["default"]
	}
	if !flag {
		paymentLogger.Warn(paymentCtx, "No payment conditions found for seller", "seller", seller)
		return false
	}

	userOrderStats, err := s.GetUserOrderStats(context.Background(), dto.GetUserOrderStatsRequest{
		UserID: userId,
	})
	if err == nil {
		preplaceOrderChecks := ordertags.PrePlaceOrderCheck(userOrderStats.Data)
		if preplaceOrderChecks != nil && (*preplaceOrderChecks == ordertags.ORDER_CHECK_TYPES.HIGHRISK_USER || *preplaceOrderChecks == ordertags.ORDER_CHECK_TYPES.SPAM_USER) {
			return false
		}
	}

	for _, condition := range conditions {
		if condition.Type == constants.COD && condition.MinimumAmountForValidity <= amount && condition.MaximumAmountForValidity >= amount {
			return true
		}
	}

	return false
}

func (s *Service) InitializePayment(ctx *gin.Context, request dto.InitiatePaymentRequest) (dto.InitiatePaymentResponse, error) {
	// initialize the payment order on gateway
	// the orderId in request body is KC order id and in response body is gateway order id
	paymentCtx := ctx.Request.Context()
	paymentCtx = logging.WithUserID(paymentCtx, request.UserID)
	paymentCtx = logging.WithOrderID(paymentCtx, *request.Data.OrderID)

	if request.Data.OrderID == nil {
		paymentLogger.Error(paymentCtx, "Order ID is required")
		return dto.InitiatePaymentResponse{}, errors.New("order id is required")
	}

	if request.Data.PaymentAmount <= 0 {
		paymentLogger.Error(paymentCtx, "Invalid payment amount", "amount", request.Data.PaymentAmount)
		return dto.InitiatePaymentResponse{}, payments.ErrInvalidAmount
	}

	if request.Data.PaymentAmount > request.Data.OrderAmount {
		paymentLogger.Warn(paymentCtx, "Payment amount exceeds order amount, adjusting to order amount", "payment_amount", request.Data.PaymentAmount, "order_amount", request.Data.OrderAmount)
		request.Data.PaymentAmount = request.Data.OrderAmount
	}

	paymentDetails, err := s.Payments.InitiatePayment(ctx, request, "")
	if err != nil {
		report.UpdatePaymentIssueCounter("INITIALIZE_PAYMENT [Razorpay-CreateOrder]", err)
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to initiate the payment")
		return dto.InitiatePaymentResponse{}, err
	}

	intOrderID, err := strconv.Atoi(*request.Data.OrderID)
	paymentSQLData := dao.PaymentGatewayTransaction{
		Gateway:              paymentDetails.Gateway,
		KcTransactionID:      paymentDetails.TransactionId,
		GatewayTransactionID: paymentDetails.PaymentOrderId,
		Entity:               "order",
		OrderAmount:          utils.Round(request.Data.OrderAmount),
		PaymentAmount:        utils.Round(request.Data.PaymentAmount),
		PaidAmount:           0,
		GatewayStatus:        payments.StatusInitiated,
		RefundAmount:         0,
		KcOrderID:            int64(intOrderID),
		KcStatus:             payments.StatusInitiated,
		PaymentDiscount:      utils.Round(request.Data.Mode.PaymentDiscount),
	}

	if request.Data.Source == "B2B" {
		paymentSQLData.GatewayStatus = payments.StatusFailed
		paymentSQLData.KcStatus = payments.StatusFailed
	}

	_, err = s.repository.Create(&paymentSQLData)
	if err != nil {
		report.UpdatePaymentIssueCounter("INITIALIZE_PAYMENT [Razorpay-CreateOrderInSQL]", err)
		return dto.InitiatePaymentResponse{}, err
	}

	err = s.addOrderTags(int64(intOrderID), []int{9}) // 9 - Payment Pending Tag
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to add order tags")
	}

	if request.Data.Source == "" {
		request.Data.Source = "Payment Flow"
	}
	eventObject := map[string]interface{}{
		"distinct_id":      request.UserID,
		"order_id":         intOrderID,
		"seller":           request.Data.Seller,
		"ordering_module":  utils.MakeTitleCase(request.Data.Seller),
		"order_value":      utils.Round(request.Data.OrderAmount),
		"payment_amount":   utils.Round(request.Data.PaymentAmount),
		"payment_discount": utils.Round(request.Data.Mode.PaymentDiscount),
		"payment_method":   request.Data.Mode.Type,
		"payment_order_id": paymentDetails.PaymentOrderId,
		"source":           request.Data.Source,
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Payment Initiated", request.UserID, eventObject),
	})

	return *paymentDetails, nil
}

func (s *Service) InitializePaymentAndCreateKiranaBazarOrder(ctx *gin.Context, request dto.InitializePaymentAndAppCreateKiranaBazarOrderRequest) (dto.InitializePaymentAndAppKiranaBazarOrderResponse, error) {
	// initialize the payment order on gateway and then places the order on KC as COD
	// the orderId in request body is KC order id and in response body is gateway order id
	paymentCtx := ctx.Request.Context()
	paymentCtx = logging.WithUserID(paymentCtx, request.UserID)

	orderRequest := dto.AppCreateKiranaBazarOrderRequest{
		UserID:      request.UserID,
		Data:        request.OrderData,
		Meta:        request.OrderMeta,
		PaymentData: request.PaymentData,
	}
	orderRequest.Data.Latitude = request.Latitude
	orderRequest.Data.Longitude = request.Longitude

	orderDetails, err := s.CreateKiranaBazarOrderV2(ctx, orderRequest)
	if err != nil && err.Error() != exceptions.OosErrorMessage {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to create Kirana Bazar order")
		slack.SendSlackMessage(fmt.Sprintf("err while creating order in InitializePaymentAndCreateKiranaBazarOrder err = %v", err))
		return dto.InitializePaymentAndAppKiranaBazarOrderResponse{}, err
	}

	response := dto.InitializePaymentAndAppKiranaBazarOrderResponse{
		OrderMeta: orderDetails.Meta,
		OrderData: orderDetails.Data,
		Error:     orderDetails.Error,
	}

	if s.IsCODAvailable(request.UserID, request.OrderData.Seller, orderDetails.Data[0].TotalAmount) {
		paymentLogger.Info(ctx, "In COD available block", "user_id", request.UserID, "seller", request.OrderData.Seller,
			"amount", orderDetails.Data[0].TotalAmount, "order_id", orderDetails.Data[0].ID)
		idInt64, _ := strconv.ParseInt(orderDetails.Data[0].ID, 10, 64)
		err = s.addOrderTags(idInt64, []int{12}) // 12 COD available tag
		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to add order tags")
		}
	}

	//var paymentDetails dto.InitiatePaymentResponse
	if request.PaymentData != nil && len(orderDetails.Data) > 0 {
		//xx := "80724"
		request.PaymentData.OrderAmount = orderDetails.Data[0].TotalAmount
		paymentRawData := request.PaymentData
		paymentRawData.OrderID = &orderDetails.Data[0].ID

		InitializePaymentRequest := dto.InitiatePaymentRequest{
			UserID: request.UserID,
			Meta:   request.OrderMeta,
			Data:   paymentRawData,
		}

		paymentDetails, err := s.InitializePayment(ctx, InitializePaymentRequest)
		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to initiate the payment")
			return response, nil
		}
		response.PaymentData = &paymentDetails
		//mode, err := json.Marshal(request.PaymentData.Mode)
		//if err != nil {
		//	mode = nil
		//}
		//nav := &shared.Nav{
		//	Name:    "WebViewOld",
		//	NavType: "Redirect to Screen",
		//	Params: map[string]interface{}{
		//		"uri": fmt.Sprintf("http://192.168.102.77:3000/payment?amount=%d&orderId=%s&transactionId=%d&customerName=%d&customerEmail=%d&customerPhone=%d&seller=%d&razorpayKey=rzp_test_5xAMaVPj8cUofR&source=%d&mode=%d`",
		//			int(paymentDetails.PaymentAmount), paymentDetails.PaymentOrderId, paymentDetails.TransactionId, "Shubhankar", "", "7417222635", request.OrderData.Seller, request.PaymentData.Source, string(mode)),
		//	},
		//}
		//response.PaymentData.NavObj = nav

		//orderCount, err := ordervalue.GetUserLevelOrderCount(request.UserID)
		//if err != nil {
		//	fmt.Println("error in getting user order count", err)
		//}
		//isFirstTimeUser := false
		//if orderCount == 0 {
		//	isFirstTimeUser = true
		//}

		cancelDelayTime := 48 * time.Hour // updated cancellation window to 48 hours by sitaram rathi on 13/06/25, instructed by @shubhankar sir and @aishwarya
		//if orderDetails.Data[0].TotalAmount > 4000 && isFirstTimeUser {
		//	cancelDelayTime = 24 * time.Hour
		//}

		paymentWaTriggerQuqueId := uuid.NewString() + "KC"
		paymentWaData := map[string]interface{}{
			"kc_order_id":      orderDetails.Data[0].ID,
			"payment_order_id": paymentDetails.PaymentOrderId,
			"phone_number":     orderDetails.Data[0].UserPhoneNumber,
			"name":             orderDetails.Data[0].UserName,
			"order_value":      fmt.Sprintf("%0.2f", orderDetails.Data[0].TotalAmount),
			"seller":           request.OrderData.Seller,
			"timeToCancel":     fmt.Sprintf("%d", int(cancelDelayTime.Hours())),
			"user_id":          request.UserID,
		}
		paymentWaTriggerFunc := func() error {
			s.SendRetryPaymentWAMessage(s.repository, s.Mixpanel, paymentWaTriggerQuqueId, paymentWaData, nil)
			return nil
		}

		cancelPaymentFailTriggerQuqueId := uuid.NewString() + "KC"
		cancelPaymentFailData := map[string]interface{}{
			"kc_order_id":      orderDetails.Data[0].ID,
			"payment_order_id": paymentDetails.PaymentOrderId,
			"user_id":          request.UserID,
			"seller":           orderDetails.Data[0].Seller,
		}

		cancelPaymentFailTriggerFunc := func() error {
			s.CancelUnpaidOrders(s.repository, s.Mixpanel, cancelPaymentFailTriggerQuqueId, cancelPaymentFailData, nil)
			return nil
		}

		queue.AddDataToQueue(context.Background(), s.repository, s.Mixpanel, queueModels.QueueInsertParams{
			Data:             paymentWaData,
			ShouldAdjustTime: false,
			TimeAdjustConfig: nil,
			TriggerAt:        time.Now().Add(15 * time.Minute),
			TriggerFunction:  queue.QUEUE_TRIGGER_FUNCTION_TYPES.PAYMENT_WA,
			TriggerFunc:      paymentWaTriggerFunc,
			QueueID:          &paymentWaTriggerQuqueId,
			CreateInDb:       true,
		})

		queue.AddDataToQueue(context.Background(), s.repository, s.Mixpanel, queueModels.QueueInsertParams{
			Data:             cancelPaymentFailData,
			ShouldAdjustTime: false,
			TimeAdjustConfig: nil,
			TriggerAt:        time.Now().Add(cancelDelayTime),
			TriggerFunction:  queue.QUEUE_TRIGGER_FUNCTION_TYPES.CANCEL_PAYMENT_FAIL,
			TriggerFunc:      cancelPaymentFailTriggerFunc,
			QueueID:          &cancelPaymentFailTriggerQuqueId,
			CreateInDb:       true,
		})

	} else if request.UserID == "DAfZYSzm22eG2GPp5rarLe78gSx1" {
		paymentSection, _, _, err := s.GetPaymentMethods(request.UserID,
			request.OrderData.Seller, orderDetails.Data[0].TotalAmount, request.OrderMeta.AppVersion)
		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get payment methods")
			return response, nil
		}

		var partialPaidMethods *dto.WaysToPayDataV2
		for _, method := range append(paymentSection.Section1, paymentSection.Section2...) {
			if method.Type == constants.PARTIALLY_PAID {
				partialPaidMethods = &method
				break
			}
		}

		if partialPaidMethods == nil {
			paymentLogger.Error(paymentCtx, "No partially paid method found for user", "user_id", request.UserID)
			return response, nil
		}

		InitializePaymentRequest := dto.InitiatePaymentRequest{
			UserID: request.UserID,
			Meta:   request.OrderMeta,
			Data: &dto.OrderPaymentDetails{
				Mode:          partialPaidMethods,
				OrderID:       &orderDetails.Data[0].ID,
				OrderAmount:   orderDetails.Data[0].TotalAmount,
				PaymentAmount: partialPaidMethods.PaymentAmount,
				Seller:        request.OrderData.Seller,
				Source:        "AUTO_COD",
			},
		}

		_, err = s.InitializePayment(ctx, InitializePaymentRequest)
		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to initiate the payment")
			return response, nil
		}
	}
	return response, nil
}

func GetPaymentTransactionFromPaymentOrderId(ctx context.Context, PaymentOrderID string, repository *sqlRepo.Repository) (dao.PaymentGatewayTransaction, error) {
	paymentCtx := ctx
	paymentCtx = logging.WithOrderID(paymentCtx, PaymentOrderID)

	paymentTransaction := dao.PaymentGatewayTransaction{}
	_, err := repository.Find(map[string]interface{}{
		"gateway_transaction_id": PaymentOrderID,
	}, &paymentTransaction)

	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to fetch the payment details")
		return dao.PaymentGatewayTransaction{}, err
	}

	return paymentTransaction, nil
}

func GetPaymentTransactionFromKCOrderId(ctx context.Context, kcOrderID int64, repository *sqlRepo.Repository) (dao.PaymentGatewayTransaction, error) {
	paymentCtx := ctx
	paymentCtx = logging.WithOrderID(paymentCtx, fmt.Sprintf("%d", kcOrderID))

	paymentTransaction := dao.PaymentGatewayTransaction{}
	_, err := repository.Find(map[string]interface{}{
		"kc_order_id": kcOrderID,
	}, &paymentTransaction)

	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to fetch the payment details")
		return dao.PaymentGatewayTransaction{}, err
	}

	return paymentTransaction, nil
}

func (s *Service) confirmOrder(ctx context.Context, KcOrderID int64, userId *string, PaymentId string, paymentAmount float64,
	paymentDiscount float64, orderAmount float64, rnn *string) error {
	paymentCtx := ctx
	paymentCtx = logging.WithOrderID(paymentCtx, fmt.Sprintf("%d", KcOrderID))
	paymentCtx = logging.WithData1(paymentCtx, PaymentId)

	if userId == nil {
		val, err := s.getUserIdFromOrderID(KcOrderID)
		userId = &val
		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get the user id from order id")
			return err
		}
		paymentCtx = logging.WithUserID(paymentCtx, *userId)
	}

	advanceTaken := true
	err := UpdatePaymentMeta(s.repository, strconv.FormatInt(KcOrderID, 10), utils.BoolPtr(true),
		&paymentAmount, nil, nil, nil, nil, orderAmount, nil,
		&PaymentId, &paymentDiscount, rnn)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to update the payment meta")
		return err
	}

	if utils.Round(paymentAmount) == utils.Round(orderAmount) {
		err = s.addOrderTags(KcOrderID, []int{8}) // 8 - Fully Paid tag
	} else {
		err = s.addOrderTags(KcOrderID, []int{7}) // 7 - Partially Paid tag
	}

	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to add order tags")
	}

	err = s.removeOrderTag(KcOrderID, 9) // 9 - Payment Pending Tag
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to remove payment pending tag")
	}
	err = s.removeOrderTag(KcOrderID, 12) // 12 - COD available tag
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to remove COD available tag")
	}

	// confirm the order
	_, err = s.HandleConfirmedOrder(ctx, dto.ConfirmOrderRequest{
		Data: dto.ConfirmOrderData{
			UserID:      *userId,
			OrderID:     strconv.FormatInt(KcOrderID, 10),
			Email:       "APP",
			Source:      "APP",
			OrderStatus: processingStatus.CONFIRMED,
			Message:     PaymentId,
			OrderMeta: dto.OrderMeta{
				AdvanceTaken:    &advanceTaken,
				PaidAmount:      utils.Float64Ptr(utils.Round(paymentAmount)),
				PaymentId:       PaymentId,
				PaymentDiscount: utils.Round(paymentDiscount),
			},
		},
	})
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to confirm the order")
		return err
	}
	return nil
}

func (s *Service) handleFailedPayment(ctx *gin.Context, PaymentOrderID string, PaymentID *string, PaymentMode *string) error {
	paymentCtx := ctx.Request.Context()
	paymentCtx = logging.WithOrderID(paymentCtx, PaymentOrderID)

	sqlUpdateData := dao.PaymentGatewayTransaction{
		GatewayStatus: payments.StatusFailed,
	}
	eventName := "Payment Failed"
	if PaymentID != nil {
		sqlUpdateData.PaymentID = *PaymentID
	} else {
		eventName = "Payment Cancelled"
	}

	sqlWhereData := dao.PaymentGatewayTransaction{
		GatewayTransactionID: PaymentOrderID,
	}

	paymentTransaction, err := GetPaymentTransactionFromPaymentOrderId(ctx, PaymentOrderID, s.repository)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get the payment transaction")
		return err
	}

	createEventObject := func(userId string, status string, extraFields map[string]interface{}) map[string]interface{} {
		seller, _ := s.getSellerFromOrderID(paymentTransaction.KcOrderID)

		paymentMethod := constants.PARTIALLY_PAID
		if utils.Round(paymentTransaction.PaymentAmount) == utils.Round(paymentTransaction.OrderAmount) {
			paymentMethod = constants.FULLY_PAID
		}

		eventObject := map[string]interface{}{
			"distinct_id": userId,
			"order_id":    paymentTransaction.KcOrderID,
			"seller":      seller, "payment_amount": utils.Round(paymentTransaction.PaymentAmount),

			"ordering_module":  utils.MakeTitleCase(seller),
			"order_value":      utils.Round(paymentTransaction.OrderAmount),
			"payment_discount": utils.Round(paymentTransaction.PaymentDiscount),
			"payment_method":   paymentMethod,
			"payment_mode":     PaymentMode,
			"payment_order_id": PaymentOrderID,
			"payment_status":   status,
			"payment_id":       PaymentID,
		}

		for k, v := range extraFields {
			eventObject[k] = v
		}
		return eventObject
	}

	sendMixpanelEvent := func(name string, status string, extraFields map[string]interface{}) {
		userId, err := s.getUserIdFromOrderID(paymentTransaction.KcOrderID)
		if err == nil {
			eventObject := createEventObject(userId, "FAILED", extraFields)
			s.Mixpanel.Track(ctx, []*mixpanel.Event{
				s.Mixpanel.NewEvent(name, userId, eventObject),
			})
		}
	}

	if paymentTransaction.GatewayStatus == payments.StatusCompleted {
		paymentLogger.Error(paymentCtx, "Payment is already completed", "transaction", paymentTransaction)
		sendMixpanelEvent(eventName, "FAILED", map[string]interface{}{
			"error": "payment_already_completed",
		})
		return nil
	}

	orderStatus, err := s.GetOrderStatusFromDB(context.Background(), &dto.OrderStatusRequest{
		OrderID: paymentTransaction.KcOrderID,
	})
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get the order status", "order_id", paymentTransaction.KcOrderID)
		return err
	}

	if !(orderStatus.DisplayStatus == constants.PLACED ||
		orderStatus.DisplayStatus == constants.PENDING_CONFIRMATION) {
		sendMixpanelEvent(eventName, "FAILED", map[string]interface{}{
			"error":        "invalid_current_order_status",
			"order_status": orderStatus.DisplayStatus,
		})
		paymentLogger.Error(paymentCtx, "Order is already placed or confirmed", "transaction", paymentTransaction)
		return nil
	}

	var rowsEffected int64
	if _, rowsEffected, err = s.repository.Update(sqlWhereData, sqlUpdateData); err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to update the payment status", "payment_order_id", PaymentOrderID)
		return err
	}

	if rowsEffected != 1 {
		paymentLogger.Error(paymentCtx, "Failed to update the payment status")
		return errors.New("failed to update the payment status")
	}

	sendMixpanelEvent(eventName, "FAILED", nil)

	return nil
}

func (s *Service) handleCompletePayment(ctx *gin.Context, PaymentOrderID string, PaymentID string, paymentDetails *dto.VerifyPaymentResponse, paymentMode *string, rnn *string) error {
	paymentCtx := ctx.Request.Context()
	paymentCtx = logging.WithOrderID(paymentCtx, PaymentOrderID)
	paymentCtx = logging.WithData1(paymentCtx, PaymentID)

	//tx, err := s.repository.BeginTx(ctx)
	//if err != nil {
	//	logger.Error(ctx, "Failed to begin transaction", err)
	//	return err
	//}
	//defer func() {
	//	tx.Rollback()
	//	//if p := recover(); p != nil {
	//	//	tx.Rollback()
	//	//	panic(p)
	//	//} else if err != nil {
	//	//	tx.Rollback()
	//	//} else {
	//	//	tx.Commit()
	//	//}
	//}()

	sqlUpdateData := dao.PaymentGatewayTransaction{
		GatewayStatus: payments.StatusCompleted,
		PaymentID:     PaymentID,
	}
	sqlWhereData := dao.PaymentGatewayTransaction{
		GatewayTransactionID: PaymentOrderID,
	}

	//var paymentTransactionLock dao.PaymentGatewayTransaction
	//query := fmt.Sprintf(`select * from kiranabazar_payment_gateway_orders where gateway_transaction_id ='%s' FOR UPDATE`, PaymentOrderID)
	//_, err = s.repository.CustomQueryTx(tx, &paymentTransactionLock, query)

	// order will be converted into PrePaid or Partially Paid state
	paymentTransaction, err := GetPaymentTransactionFromPaymentOrderId(ctx, PaymentOrderID, s.repository)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get the payment transaction", "payment_order_id", paymentDetails.PaymentOrderId)
		return err
	}

	userId, err := s.getUserIdFromOrderID(paymentTransaction.KcOrderID)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get the user id from order id", "order_id", paymentTransaction.KcOrderID)
		return err
	}

	paymentCtx = logging.WithUserID(paymentCtx, userId)

	orderStatus, err := s.GetOrderStatusFromDB(context.Background(), &dto.OrderStatusRequest{
		OrderID: paymentTransaction.KcOrderID,
	})
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get the order status", "order_id", paymentTransaction.KcOrderID)
		return err
	}

	if orderStatus.DisplayStatus == constants.CANCELLED {
		paymentLogger.Error(paymentCtx, "Order is already cancelled", "transaction", paymentTransaction)
		refundResp, err := s.RefundAdvancePayment(ctx, dto.RefundPaymentApiRequest{
			OrderID:        paymentTransaction.KcOrderID,
			UserID:         userId,
			Amount:         -1,
			Force:          utils.BoolPtr(false),
			InstantPayment: utils.BoolPtr(true),
			Reason:         utils.StrPtr(constants.CANCELLED),
		})
		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to refund advance payment", "order_id", paymentTransaction.KcOrderID)
			return err
		}
		seller, _ := s.getSellerFromOrderID(paymentTransaction.KcOrderID)
		eventObject := map[string]interface{}{
			"distinct_id":      userId,
			"order_id":         paymentTransaction.KcOrderID,
			"seller":           seller,
			"ordering_module":  utils.MakeTitleCase(seller),
			"order_value":      utils.Round(paymentTransaction.OrderAmount),
			"payment_amount":   utils.Round(paymentTransaction.PaymentAmount),
			"payment_discount": utils.Round(paymentTransaction.PaymentDiscount),
			"payment_order_id": paymentTransaction.GatewayTransactionID,
			"payment_status":   "REFUND_INITIATED",
			"payment_id":       PaymentID,
			"error":            "payment_received_after_cancel",
			"refund_reason":    "Order Cancelled",
			"refund_id":        refundResp.RefundOrderId,
			"refund_amount":    utils.Round(refundResp.RefundAmount),
			"refund_status":    refundResp.RefundStatus,
		}
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Payment Received After Order Cancelled", userId, eventObject),
		})

		return fmt.Errorf("order is already cancelled")
	}

	if !(orderStatus.DisplayStatus == constants.PLACED ||
		orderStatus.DisplayStatus == constants.PENDING_CONFIRMATION) {
		paymentLogger.Error(paymentCtx, "Order is already confirmed", "transaction", paymentTransaction)
		refreshResp, err := s.RefreshOrderPayment(ctx, dto.RefreshOrderRequest{
			OrderID: paymentTransaction.KcOrderID,
			UserID:  userId,
			Source:  utils.StrPtr("Payment Received"),
		})
		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to refresh order payment", "order_id", paymentTransaction.KcOrderID)
			return err
		}

		seller, _ := s.getSellerFromOrderID(paymentTransaction.KcOrderID)
		eventObject := map[string]interface{}{
			"distinct_id":      userId,
			"order_id":         paymentTransaction.KcOrderID,
			"seller":           seller,
			"ordering_module":  utils.MakeTitleCase(seller),
			"order_value":      utils.Round(paymentTransaction.OrderAmount),
			"payment_amount":   utils.Round(paymentTransaction.PaymentAmount),
			"payment_order_id": paymentTransaction.GatewayTransactionID,
			"payment_status":   "REFUND_INITIATED",
			"payment_id":       PaymentID,
			"error":            "payment_received_after_confirmed",
			"refund_reason":    "Order Already Confirmed",
			"refund_status":    refreshResp.Status,
		}
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Payment Received After Order Confirmed", userId, eventObject),
		})
		return fmt.Errorf("order is already confirmed")
	}

	paymentDiscount := utils.Round(paymentTransaction.PaymentDiscount + paymentTransaction.PaymentMethodDiscount)

	sqlUpdateData.PaidAmount = utils.Round(paymentDetails.PaymentAmount)
	sqlUpdateData.KcStatus = payments.StatusCompleted

	err = s.confirmOrder(ctx, paymentTransaction.KcOrderID, nil, PaymentID, paymentDetails.PaymentAmount,
		paymentDiscount, paymentTransaction.OrderAmount, rnn)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to confirm the order", "order_id", paymentTransaction.KcOrderID)
		return err
	}

	var rowsEffected int64
	if _, rowsEffected, err = s.repository.Update(sqlWhereData, sqlUpdateData); err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to update the payment status", "payment_order_id", PaymentOrderID)
		return err
	}

	if rowsEffected != 1 {
		paymentLogger.Error(paymentCtx, "Failed to update the payment status")
		return errors.New("failed to update the payment status")
	}

	query := fmt.Sprintf(`UPDATE kiranaclubdb.kiranabazar_order_tag_mapping SET is_active=0 where tag_id=%d and order_id=%d;`, 6, paymentTransaction.KcOrderID)
	_, err = s.repository.CustomQuery(nil, query)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to update the order tag mapping", "order_id", paymentTransaction.KcOrderID)
	}

	paymentMethod := constants.PARTIALLY_PAID
	if utils.Round(paymentTransaction.PaymentAmount) == utils.Round(paymentTransaction.OrderAmount) {
		paymentMethod = constants.FULLY_PAID
	}

	seller, _ := s.getSellerFromOrderID(paymentTransaction.KcOrderID)
	eventObject := map[string]interface{}{
		"distinct_id":      userId,
		"order_id":         paymentTransaction.KcOrderID,
		"seller":           seller,
		"ordering_module":  utils.MakeTitleCase(seller),
		"order_value":      utils.Round(paymentTransaction.OrderAmount),
		"payment_amount":   utils.Round(paymentTransaction.PaymentAmount),
		"payment_discount": utils.Round(paymentTransaction.PaymentDiscount),
		"payment_method":   paymentMethod,
		"payment_mode":     paymentMode,
		"payment_order_id": paymentDetails.PaymentOrderId,
		"payment_status":   "PAID",
		"payment_id":       paymentDetails.PaymentId,
		"source":           "Razorpay Webhook",
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Payment Success", userId, eventObject),
	})

	return nil
}

func (s *Service) handleRefundPayment(ctx *gin.Context, PaymentOrderID string, PaymentID string, PaymentMode string, RefundAmount float64) error {
	paymentCtx := ctx.Request.Context()
	paymentCtx = logging.WithOrderID(paymentCtx, PaymentOrderID)
	paymentCtx = logging.WithData1(paymentCtx, PaymentID)

	sqlUpdateData := dao.PaymentGatewayTransaction{
		GatewayStatus: payments.StatusRefunded,
		RefundAmount:  RefundAmount,
	}

	sqlWhereData := dao.PaymentGatewayTransaction{
		GatewayTransactionID: PaymentOrderID,
	}

	var rowsEffected int64
	var err error
	if _, rowsEffected, err = s.repository.Update(sqlWhereData, sqlUpdateData); err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to update the payment status", "payment_order_id", PaymentOrderID)
		return err
	}

	if rowsEffected != 1 {
		paymentLogger.Error(paymentCtx, "Failed to update the payment status")
		return errors.New("failed to update the payment status")
	}

	paymentTransaction, err := GetPaymentTransactionFromPaymentOrderId(ctx, PaymentOrderID, s.repository)
	if err == nil {
		paymentMethod := constants.PARTIALLY_PAID
		if utils.Round(paymentTransaction.PaymentAmount) == utils.Round(paymentTransaction.OrderAmount) {
			paymentMethod = constants.FULLY_PAID
		}
		userId, err := s.getUserIdFromOrderID(paymentTransaction.KcOrderID)
		if err == nil {
			paymentCtx = logging.WithUserID(paymentCtx, userId)
			seller, _ := s.getSellerFromOrderID(paymentTransaction.KcOrderID)
			eventObject := map[string]interface{}{
				"distinct_id":      userId,
				"order_id":         paymentTransaction.KcOrderID,
				"seller":           seller,
				"ordering_module":  utils.MakeTitleCase(seller),
				"order_value":      utils.Round(paymentTransaction.OrderAmount),
				"payment_amount":   utils.Round(paymentTransaction.PaymentAmount),
				"payment_discount": utils.Round(paymentTransaction.PaymentDiscount),
				"refund_amount":    utils.Round(RefundAmount),
				"payment_method":   paymentMethod,
				"payment_mode":     PaymentMode,
				"payment_order_id": PaymentOrderID,
				"payment_status":   "REFUNDED",
				"payment_id":       PaymentID,
			}

			s.Mixpanel.Track(ctx, []*mixpanel.Event{
				s.Mixpanel.NewEvent("Payment Refunded", userId, eventObject),
			})
		}
	}
	return nil
}

func (s *Service) ValidatePayment(ctx *gin.Context, request dto.ValidatePaymentRequest) (dto.ValidatePaymentResponse, error) {
	paymentCtx := ctx.Request.Context()
	paymentCtx = logging.WithOrderID(paymentCtx, request.Data.PaymentOrderID)
	paymentCtx = logging.WithData1(paymentCtx, request.Data.PaymentID)

	if request.Data.GatewayStatus == payments.StatusFailed {
		report.UpdatePaymentIssueCounter("NoPaymentResponseFromApp", fmt.Errorf("payment failed"))

		err := s.handleFailedPayment(ctx, request.Data.PaymentOrderID, nil, nil)
		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to handle the failed payment", "payment_id", request.Data.PaymentID)
			return dto.ValidatePaymentResponse{}, err
		}

		return dto.ValidatePaymentResponse{
			Meta: request.Meta,
			Data: &dto.ValidateResponse{
				Status:     payments.StatusFailed,
				StatusMeta: s.GetPaymentStatusStyle(payments.StatusFailed, "", ""),
			},
		}, nil
	}

	paymentDetails, err := s.Payments.VerifyPayment(ctx, request.Data, "")
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to verify the payment", "payment_id", request.Data.PaymentID)
		report.UpdatePaymentIssueCounter("PaymentFailedOnApp", fmt.Errorf("Payment failed"))

		return dto.ValidatePaymentResponse{
			Meta: request.Meta,
			Data: &dto.ValidateResponse{
				Status:     payments.StatusPending,
				StatusMeta: s.GetPaymentStatusStyle(payments.StatusPending, "", ""),
			},
		}, nil
	}

	returnStatus := payments.StatusPending
	if paymentDetails.Status == payments.StatusInitiated || paymentDetails.Status == payments.StatusPending {
		// wait for the payment to be completed
		if paymentDetails.Status == payments.StatusInitiated {
			// Ideally this should not happen
			paymentLogger.Warn(paymentCtx, "Payment is initiated, waiting for the payment to be completed", "payment_order_id", paymentDetails.PaymentOrderId)
		}
		returnStatus = payments.StatusPending
	}

	if paymentDetails.Status == payments.StatusRefunded {
		// a refund at this point is not possible, this should never happen
		paymentLogger.Error(paymentCtx, "Refund is not possible at this point", "payment_order_id", paymentDetails.PaymentOrderId)
		returnStatus = payments.StatusFailed // App can only show Transaction has failed at this point of time
	}

	if paymentDetails.Status == payments.StatusFailed {
		// order will be kept in COD state
		// Payment Failed flow needs to be implemented
		// need to show this on UI: payment failed, if money was deducted it will be refunded in 3-5 days
		returnStatus = payments.StatusFailed
		err = s.handleFailedPayment(ctx, request.Data.PaymentOrderID, &request.Data.PaymentID, &paymentDetails.Method)
		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to handle the failed payment", "payment_id", request.Data.PaymentID)
			return dto.ValidatePaymentResponse{}, err
		}
	}

	if paymentDetails.Status == payments.StatusCompleted {
		// order will be converted into PrePaid or Partially Paid state
		returnStatus = payments.StatusCompleted
		//err = s.handleCompletePayment(ctx, request.Data.PaymentOrderID, request.Data.PaymentID, paymentDetails, &paymentDetails.Method)
		//if err != nil {
		//	logger.Error(ctx, "Failed to handle the completed payment from validate", err, request.Data.PaymentOrderID, request.Data.PaymentID)
		//	return dto.ValidatePaymentResponse{}, err
		//}
	}

	return dto.ValidatePaymentResponse{
		Meta: request.Meta,
		Data: &dto.ValidateResponse{
			Status:     returnStatus,
			StatusMeta: s.GetPaymentStatusStyle(returnStatus, "", ""),
		},
	}, nil
}

func (s *Service) RazorpayWebhookType1(ctx *gin.Context, req razorpay.RazorpayWebhookType1Request) error {
	paymentCtx := ctx.Request.Context()
	//payment Events
	//payment.authorized -- Not implimenting as don;t want to take any action on authorized, ll take on fail/capture
	//payment.failed
	//payment.captured
	//
	//payment.downtime.started -- As of now not required
	//payment.downtime.updated -- As of now not required
	//payment.downtime.resolved -- As of now not required
	//
	//order Events
	//order.paid -- Not implimenting as taking action on fail/capture only
	//
	//refund Events -- Not implimenting as of now
	//refund.speed_changed
	//refund.processed
	//refund.failed
	//refund.created
	status := s.Payments.VerifyWebhookSignature(ctx, "type1", "")
	if !status {
		paymentLogger.Error(paymentCtx, "Failed to verify the webhook signature", "request", req)
		return errors.New("invalid signature")
	}

	paymentData := &dao.PaymentGatewayRecords{}
	paymentData, err := s.Payments.HandleWebhookType1(ctx, req.Payload, req.Event, "")
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to handle the webhook", "request", req)
		report.UpdatePaymentIssueCounter("RazorPayWebhookHandleFail", fmt.Errorf("Payment failed"))
		return err
	}

	if req.Event == "payment.captured" {
		if paymentData.Status != payments.StatusCompleted {
			paymentLogger.Error(paymentCtx, "Payment is not completed in capture event", "payment_data", paymentData)
		}
		paymentDetails := dto.VerifyPaymentResponse{
			PaymentAmount:  paymentData.Amount / 100,
			PaymentOrderId: paymentData.OrderID,
			PaymentId:      paymentData.ID,
			Status:         payments.StatusCompleted,
			Gateway:        "razorpay",
		}

		if paymentData.OrderID != "" {
			paymentCtx = logging.WithOrderID(paymentCtx, paymentData.OrderID)
			paymentCtx = logging.WithData1(paymentCtx, paymentData.ID)

			err = s.handleCompletePayment(ctx, paymentData.OrderID, paymentData.ID, &paymentDetails, &paymentData.Method, paymentData.RNN)
			if err != nil {
				paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to handle the completed payment from Webhook", "payment_id", paymentData.ID)
				return err
			}
		} else {
			paymentLogger.Error(paymentCtx, "OrderID is empty in capture event", "payment_data", paymentData)
		}

		if paymentData.RNN != nil && RNNCache.Set {
			RNNCache.items = append(RNNCache.items, dto.RNNItem{
				RNN:       *paymentData.RNN,
				Amount:    paymentData.Amount / 100,
				PaymentId: paymentData.ID,
			})
		}
	}

	if req.Event == "refund.processed" {
		if paymentData.Status != payments.StatusRefundInitiated {
			paymentLogger.Error(paymentCtx, "Payment is not refunded in capture event", "payment_data", paymentData)
		}

		paymentCtx = logging.WithOrderID(paymentCtx, paymentData.OrderID)
		paymentCtx = logging.WithData1(paymentCtx, paymentData.ID)

		err = s.handleRefundPayment(ctx, paymentData.OrderID, paymentData.ID, paymentData.Method, float64(paymentData.AmountRefunded/100))
		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to handle the completed payment from Webhook", "payment_id", paymentData.ID)
			return err
		}
	}

	if req.Event == "payment.failed" {
		if paymentData.Status != payments.StatusFailed {
			paymentLogger.Error(paymentCtx, "Payment is not completed in failed event", "payment_data", paymentData)
		}

		paymentCtx = logging.WithOrderID(paymentCtx, paymentData.OrderID)
		paymentCtx = logging.WithData1(paymentCtx, paymentData.ID)

		err = s.handleFailedPayment(ctx, paymentData.OrderID, &paymentData.ID, &paymentData.Method)
		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to handle the failed payment from Webhook", "payment_id", paymentData.ID)
			return err
		}
	}

	if paymentData != nil {
		_, err = s.repository.Upsert(paymentData)
		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to create the payment records", "order_id", paymentData.OrderID)
			return err
		}
	}

	return nil
}

func (s *Service) GetPaymentStatusStyle(paymentStatus string, platform, reqOrderId string) dto.StatusMeta {
	if platform == "website" && paymentStatus == payments.StatusFailed && reqOrderId != "" {
		return dto.StatusMeta{
			Title:       "पेमेंट पेंडिंग है",
			Description: "अगर अमाउंट कट गया है, तो 3 से 5 दिनों में अपने आप वापस आ जाएगा। कृपया चिंता न करें।",
			ImageUrl:    "https://d2rstorage2.blob.core.windows.net/widget/March/19/7bc7ea45-7bc3-4b11-968d-ae53761dc7f3/1742373819773.webp",
			BgColor:     "#FFF2CC",
			Color:       "#6D5A00",
			Name:        "पेमेंट पेंडिंग है",
		}
	}
	style, ok := payments.StatusUIData[paymentStatus]
	if ok {
		return style
	}
	return payments.StatusUIData[payments.StatusPending]
}

func (s *Service) MapPaymentStatus(paymentStatus string, lastStatusUpdate *time.Time) string {
	if paymentStatus == payments.StatusInitiated || paymentStatus == payments.StatusPending {
		if lastStatusUpdate.Add(time.Minute * 14).Before(time.Now()) {
			return payments.StatusFailed
		} else {
			return payments.StatusPending
		}
	}
	if paymentStatus == payments.StatusCompleted {
		return payments.StatusCompleted
	}
	if paymentStatus == payments.StatusFailed {
		return payments.StatusFailed
	}
	if paymentStatus == payments.StatusRefunded {
		return payments.StatusRefunded
	}
	return payments.StatusPending
}

func (s *Service) GetOrderStatusWithPaymentRefreshed(orderID int64, userId *string) (*orderstatus.OrderStatusResponse, error) {
	paymentCtx := context.Background()
	paymentCtx = logging.WithOrderID(paymentCtx, fmt.Sprintf("%d", orderID))
	if userId != nil {
		paymentCtx = logging.WithUserID(paymentCtx, *userId)
	}

	orderStatus, err := s.GetOrderStatusFromDB(context.Background(), &dto.OrderStatusRequest{
		OrderID: orderID,
	})
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get order status")
		return nil, fmt.Errorf("failed to get order status: %v", err)
	}

	if orderStatus.DisplayStatus != constants.PLACED {
		return orderStatus, nil
	}

	var userIdVal string
	if userId == nil {
		userIdVal, err = s.getUserIdFromOrderID(orderID)
		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get user id from order id")
			return nil, fmt.Errorf("failed to get user id from order id: %v", err)
		}
		paymentCtx = logging.WithUserID(paymentCtx, userIdVal)
	} else {
		userIdVal = *userId
	}

	response, err := s.RefreshOrderPayment(context.Background(), dto.RefreshOrderRequest{
		OrderID: orderID,
		UserID:  userIdVal,
	})
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to refresh order payment")
		return nil, fmt.Errorf("failed to refresh order payment: %v", err)
	}

	if response.Status == payments.Updated {
		orderStatus, err = s.GetOrderStatusFromDB(context.Background(), &dto.OrderStatusRequest{
			OrderID: orderID,
		})
		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get order status after refresh")
			return nil, fmt.Errorf("failed to get order status: %v", err)
		}
	}

	return orderStatus, nil
}

func (s *Service) SendRetryPaymentWAMessage(repository *sqlRepo.Repository, mp *mixpanelRepo.Repository, queueId string, data interface{}, meta interface{}) (string, error) {
	paymentCtx := context.Background()

	dataMap, ok := data.(map[string]interface{})
	if !ok {
		paymentLogger.Error(paymentCtx, "Data is not a map[string]interface{}")
		return "", fmt.Errorf("data is not a map[string]interface{}")
	}
	orderId, exist := dataMap["kc_order_id"].(string)
	if !exist {
		paymentLogger.Error(paymentCtx, "orderId key does not exist in data")
		return "", fmt.Errorf("orderId key does not exist in data")
	}

	paymentCtx = logging.WithOrderID(paymentCtx, orderId)

	seller, exist := dataMap["seller"].(string)
	if !exist {
		paymentLogger.Error(paymentCtx, "seller key does not exist in data")
		return "", fmt.Errorf("seller key does not exist in data")
	}
	phoneNumber, exist := dataMap["phone_number"].(string)
	if !exist {
		paymentLogger.Error(paymentCtx, "phone_number key does not exist in data")
		return "", fmt.Errorf("phone_number key does not exist in data")
	}
	name, exist := dataMap["name"].(string)
	if !exist {
		paymentLogger.Error(paymentCtx, "name key does not exist in data")
		return "", fmt.Errorf("name key does not exist in data")
	}
	timeToCancel, exist := dataMap["timeToCancel"].(string)
	if !exist {
		paymentLogger.Warn(paymentCtx, "timeToCancel key does not exist in data, using default")
		timeToCancel = "1"
	}
	orderValue, exist := dataMap["order_value"].(string)
	if !exist {
		paymentLogger.Error(paymentCtx, "order_value key does not exist in data")
		return "", fmt.Errorf("order_value key does not exist in data")
	}
	parsedOrderID, err := strconv.ParseInt(orderId, 10, 64)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to parse orderId")
		return "", fmt.Errorf("failed to parse orderId: %v", err)
	}

	userId, exist := dataMap["user_id"].(string)
	var userIdAdd *string
	if !exist {
		paymentLogger.Warn(paymentCtx, "user_id key does not exist in data")
		userIdAdd = nil
	} else {
		userIdAdd = &userId
		paymentCtx = logging.WithUserID(paymentCtx, userId)
	}

	orderStatus, err := s.GetOrderStatusWithPaymentRefreshed(parsedOrderID, userIdAdd)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get order status")
		return "", fmt.Errorf("failed to get order status: %v", err)
	}

	if orderStatus.DisplayStatus == constants.PLACED || orderStatus.DisplayStatus == constants.PENDING_CONFIRMATION {
		err = whatsapp.SendRetryPaymentMessage(phoneNumber, name, seller, orderId, orderValue, timeToCancel)
		if err == nil {
			s.UpdateQueueTaskStatus(repository, queueId, "COMPLETED")
		} else {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to send WhatsApp message")
		}
	} else {
		s.UpdateQueueTaskStatus(repository, queueId, "IGNORED")
	}

	return "", nil
}

func (s *Service) CancelUnpaidOrders(repository *sqlRepo.Repository, mp *mixpanelRepo.Repository, queueId string, data interface{}, meta interface{}) (string, error) {
	paymentCtx := context.Background()

	dataMap, ok := data.(map[string]interface{})
	if !ok {
		paymentLogger.Error(paymentCtx, "Data is not a map[string]interface{}")
		return "", fmt.Errorf("data is not a map[string]interface{}")
	}

	orderId, exist := dataMap["kc_order_id"].(string)
	if !exist {
		paymentLogger.Error(paymentCtx, "orderId key does not exist in data")
		return "", fmt.Errorf("orderId key does not exist in data")
	}

	paymentCtx = logging.WithOrderID(paymentCtx, orderId)

	seller, exist := dataMap["seller"].(string)
	if !exist {
		paymentLogger.Error(paymentCtx, "seller key does not exist in data")
		return "", fmt.Errorf("seller key does not exist in data")
	}

	userId, exist := dataMap["user_id"].(string)
	if !exist {
		paymentLogger.Error(paymentCtx, "user_id key does not exist in data")
		return "", fmt.Errorf("user_id key does not exist in data")
	}

	paymentCtx = logging.WithUserID(paymentCtx, userId)

	parsedOrderID, err := strconv.ParseInt(orderId, 10, 64)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to parse orderId")
		return "", fmt.Errorf("failed to parse orderId: %v", err)
	}

	orderStatus, err := s.GetOrderStatusWithPaymentRefreshed(parsedOrderID, &userId)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get order status")
		return "", fmt.Errorf("failed to get order status: %v", err)
	}

	if orderStatus.DisplayStatus == constants.PLACED || orderStatus.DisplayStatus == constants.PENDING_CONFIRMATION {
		_, err = s.UpdateB2BOrderStatus(context.Background(), &dto.UpdateB2BOrderStatusRequest{
			UpdatedBy: "AUTO-CANCELLED",
			Data: dto.UpdateB2BOrderStatusData{
				OrderID:        orderId,
				UpdatedBy:      "AUTO-CANCELLED",
				OrderStatus:    processingStatus.CANCELLED,
				UpdatedAt:      time.Now().Unix(),
				OrderingModule: seller,
				Source:         "PAYMENTS",
				OrderMeta: dto.OrderMeta{
					Note:         "Order cancelled due to payment failure",
					CancelReason: cancelReason.ReasonAdvancePayment,
				},
			},
		})

		if err == nil {
			s.UpdateQueueTaskStatus(repository, queueId, "COMPLETED")
		} else {
			paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to cancel order")
		}
	} else {
		s.UpdateQueueTaskStatus(repository, queueId, "IGNORED")
	}

	return "", nil
}

func (s *Service) CheckAdvancePaymentPossibility(ctx *gin.Context, request dto.CheckAdvancePaymentPossibilityRequest) (dto.CheckAdvancePaymentPossibilityResponse, error) {
	paymentCtx := ctx.Request.Context()
	paymentCtx = logging.WithOrderID(paymentCtx, fmt.Sprintf("%d", request.Data.OrderID))

	orderStatus, err := s.GetOrderStatusFromDB(context.Background(), &dto.OrderStatusRequest{
		OrderID: request.Data.OrderID,
	})
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get order status")
		return dto.CheckAdvancePaymentPossibilityResponse{PaymentAllowed: false}, fmt.Errorf("failed to get order status: %v", err)
	}

	if !(orderStatus.DisplayStatus == constants.PLACED || orderStatus.DisplayStatus == constants.PENDING_CONFIRMATION) {
		paymentLogger.Warn(paymentCtx, "Order status is not placed", "processing_status", orderStatus.ProcessingStatus)
		return dto.CheckAdvancePaymentPossibilityResponse{PaymentAllowed: false}, fmt.Errorf("order status is not placed: %v", orderStatus.ProcessingStatus)
	}

	seller, err := s.getSellerFromOrderID(request.Data.OrderID)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get seller from order ID")
		return dto.CheckAdvancePaymentPossibilityResponse{PaymentAllowed: false}, fmt.Errorf("failed to get seller from order ID: %v", err)
	}

	paymentDetails, err := s.getPaymentDetails(request.Data.OrderID)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get payment details")
		return dto.CheckAdvancePaymentPossibilityResponse{PaymentAllowed: false}, fmt.Errorf("failed to get payment details: %v", err)
	}

	if paymentDetails.PayableAmount <= 0 {
		paymentLogger.Warn(paymentCtx, "Payable amount is zero or negative", "payable_amount", paymentDetails.PayableAmount)
		return dto.CheckAdvancePaymentPossibilityResponse{PaymentAllowed: false}, fmt.Errorf("payable amount is zero or negative: %v", paymentDetails.PayableAmount)
	}

	if paymentDetails.PaidAmount != nil && *paymentDetails.PaidAmount > 0 {
		paymentLogger.Warn(paymentCtx, "Payment is already made", "paid_amount", paymentDetails.PaidAmount)
		return dto.CheckAdvancePaymentPossibilityResponse{PaymentAllowed: false}, fmt.Errorf("payment is already made: %v", paymentDetails.PaidAmount)
	}

	if paymentDetails.AdvancePayment != nil && *paymentDetails.AdvancePayment > 0 {
		paymentLogger.Warn(paymentCtx, "Advance payment is already made", "advance_payment", paymentDetails.AdvancePayment)
		paymentLink, err := whatsapp.SendInitiatePaymentMessage("+91999999999", seller,
			fmt.Sprintf("%d", request.Data.OrderID), "0", false)

		if err != nil {
			paymentLogger.WithError(paymentCtx, err).Warn(paymentCtx, "Failed to send WhatsApp payment initiation message")
		}
		return dto.CheckAdvancePaymentPossibilityResponse{PaymentAllowed: false}, fmt.Errorf("advance payment is already made: %v. Link to Pay: %s", paymentDetails.AdvancePayment, paymentLink)
	}

	phoneNumber, err := s.getPhoneFromOrderID(request.Data.OrderID)
	if err != nil {
		phoneNumber = ""
	}

	return dto.CheckAdvancePaymentPossibilityResponse{
		PaymentAllowed:        true,
		AdvancePaymentAmount:  paymentDetails.PayableAmount,
		PartialPaymentOptions: []float64{100, .1 * paymentDetails.PayableAmount},
		PhoneNumber:           phoneNumber,
	}, nil
}

func (s *Service) InitiatePaymentFromB2B(ctx *gin.Context, request dto.InitiatePaymentFromB2BRequest) (dto.InitiatePaymentFromB2BResponse, error) {
	paymentCtx := ctx.Request.Context()
	paymentCtx = logging.WithOrderID(paymentCtx, fmt.Sprintf("%d", request.Data.OrderId))

	userId, err := s.getUserIdFromOrderID(request.Data.OrderId)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get user id from order id")
		return dto.InitiatePaymentFromB2BResponse{PaymentInitiated: false}, fmt.Errorf("failed to get user id from order id: %v", err)
	}
	paymentCtx = logging.WithUserID(paymentCtx, userId)

	orderStatus, err := s.GetOrderStatusFromDB(context.Background(), &dto.OrderStatusRequest{
		OrderID: request.Data.OrderId,
	})
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get order status")
		return dto.InitiatePaymentFromB2BResponse{PaymentInitiated: false}, fmt.Errorf("failed to get order status: %v", err)
	}

	if !(orderStatus.DisplayStatus == constants.PLACED || orderStatus.DisplayStatus == constants.PENDING_CONFIRMATION) {
		paymentLogger.Warn(paymentCtx, "Order status is not placed", "processing_status", orderStatus.ProcessingStatus)
		return dto.InitiatePaymentFromB2BResponse{PaymentInitiated: false}, fmt.Errorf("order status is not placed: %v", orderStatus.ProcessingStatus)
	}

	seller, err := s.getSellerFromOrderID(request.Data.OrderId)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get seller from order ID")
		return dto.InitiatePaymentFromB2BResponse{PaymentInitiated: false}, fmt.Errorf("failed to get seller from order ID: %v", err)
	}

	paymentDetails, err := s.getPaymentDetails(request.Data.OrderId)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get payment details")
		return dto.InitiatePaymentFromB2BResponse{PaymentInitiated: false}, fmt.Errorf("failed to get payment details: %v", err)
	}

	if paymentDetails.PayableAmount <= 0 {
		paymentLogger.Warn(paymentCtx, "Payable amount is zero or negative", "payable_amount", paymentDetails.PayableAmount)
		return dto.InitiatePaymentFromB2BResponse{PaymentInitiated: false}, fmt.Errorf("payable amount is zero or negative: %v", paymentDetails.PayableAmount)
	}

	if paymentDetails.PaidAmount != nil && *paymentDetails.PaidAmount > 0 {
		paymentLogger.Warn(paymentCtx, "Payment is already made", "paid_amount", paymentDetails.PaidAmount)
		return dto.InitiatePaymentFromB2BResponse{PaymentInitiated: false}, fmt.Errorf("payment is already made: %v", paymentDetails.PaidAmount)
	}

	if paymentDetails.AdvancePayment != nil && *paymentDetails.AdvancePayment > 0 {
		paymentLogger.Warn(paymentCtx, "Advance payment is already made", "advance_payment", paymentDetails.AdvancePayment)
		return dto.InitiatePaymentFromB2BResponse{PaymentInitiated: false}, fmt.Errorf("advance payment is already made: %v", paymentDetails.AdvancePayment)
	}

	orderId := fmt.Sprintf("%d", request.Data.OrderId)

	paymentType := ""
	if utils.Round(request.Data.PaymentAmount) == utils.Round(paymentDetails.PayableAmount) {
		paymentType = "FULLY_PAID"
	} else {
		paymentType = "PARTIALLY_PAID"
	}

	InitializePaymentRequest := dto.InitiatePaymentRequest{
		UserID: userId,
		Data: &dto.OrderPaymentDetails{
			Seller:        seller,
			TransactionID: uuid.NewString(),
			OrderAmount:   paymentDetails.PayableAmount,
			PaymentAmount: request.Data.PaymentAmount,
			OrderID:       &orderId,
			Source:        "B2B",
			Mode: &dto.WaysToPayDataV2{
				PaymentDiscount: request.Data.PaymentDiscount,
				Type:            paymentType,
			},
		},
	}

	initializedPaymentDetails, err := s.InitializePayment(ctx, InitializePaymentRequest)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to initiate the payment")
		return dto.InitiatePaymentFromB2BResponse{PaymentInitiated: false}, err
	}

	phoneNumber := request.Data.PhoneNumber

	paymentLink, err := whatsapp.SendInitiatePaymentMessage(phoneNumber, seller,
		orderId, fmt.Sprintf("%0.2f", request.Data.PaymentAmount), false)

	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Warn(paymentCtx, "Failed to send WhatsApp payment initiation message")
	}

	return dto.InitiatePaymentFromB2BResponse{
		PaymentInitiated: true,
		PaymentLink:      paymentLink,
		PaymentOrderId:   initializedPaymentDetails.PaymentOrderId,
	}, nil
}

func (s *Service) RefreshOrderPayment(ctx context.Context, request dto.RefreshOrderRequest) (dto.RefreshOrderResponse, error) {
	paymentCtx := ctx
	paymentCtx = logging.WithOrderID(paymentCtx, fmt.Sprintf("%d", request.OrderID))
	paymentCtx = logging.WithUserID(paymentCtx, request.UserID)

	orderStatus, err := s.GetOrderStatusFromDB(ctx, &dto.OrderStatusRequest{
		OrderID: request.OrderID,
	})
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get the order status")
		return dto.RefreshOrderResponse{}, fmt.Errorf("failed to get order status: %v", err)
	}

	transactionData, err := GetPaymentTransactionFromKCOrderId(ctx, request.OrderID, s.repository)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get the payment transaction")
		return dto.RefreshOrderResponse{}, fmt.Errorf("failed to get payment transaction: %v", err)
	}

	// Initialize response with default status
	response := dto.RefreshOrderResponse{
		Status: "no_change",
	}

	paymentRecords, err := s.Payments.GetAllTransactionsForPayment(ctx, transactionData.GatewayTransactionID, "")
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to fetch the payment records", "gateway_transaction_id", transactionData.GatewayTransactionID)
		return dto.RefreshOrderResponse{}, err
	}

	if len(paymentRecords) == 0 {
		return response, nil
	}

	_, err = s.repository.Upsert(paymentRecords)
	if err != nil {
		paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to create the payment records from refresh order payments", "gateway_transaction_id", transactionData.GatewayTransactionID)
		return dto.RefreshOrderResponse{}, err
	}

	successPayments := make([]string, 0)
	refundPayments := make([]string, 0)
	failedPayments := make([]string, 0)
	for _, payment := range paymentRecords {
		if payment.Captured && payment.RefundStatus == nil && payment.AmountRefunded == 0 {
			successPayments = append(successPayments, payment.ID)
		}
		if payment.Captured && payment.RefundStatus != nil && payment.AmountRefunded > 0 {
			refundPayments = append(refundPayments, payment.ID)
		}
		if !payment.Captured {
			failedPayments = append(failedPayments, payment.ID)
		}
	}

	if len(successPayments) == 0 {
		if transactionData.GatewayStatus == payments.StatusCompleted {
			if !(orderStatus.DisplayStatus == constants.PLACED || orderStatus.DisplayStatus == constants.CANCELLED) {
				paymentLogger.Error(paymentCtx, "No Payment found for a order with complete payment and order placed")
			}
			slack.SendSlackMessage(fmt.Sprintf("No Payment found for a order with complete payment and order placed: %d", transactionData.KcOrderID))
			return dto.RefreshOrderResponse{}, fmt.Errorf("payment is not completed: %v", transactionData)
		}
	}

	if len(successPayments) >= 1 {
		// Handle refunds for returned or canceled orders -- handle in cron

		//if transactionData.GatewayStatus == payments.StatusRefunded {
		// what to do

		if orderStatus.ShipmentStatus == shipmentstatus.RTO_DELIVERED || orderStatus.DisplayStatus == constants.CANCELLED {
			paymentLogger.Info(ctx, "Order is returned or cancelled, initiating refund", orderStatus.ShipmentStatus, orderStatus.DisplayStatus)

			_, err := s.RefundAdvancePayment(ctx, dto.RefundPaymentApiRequest{
				OrderID:        request.OrderID,
				UserID:         request.UserID,
				Amount:         -1,
				InstantPayment: utils.BoolPtr(true),
				Force:          utils.BoolPtr(false),
				Reason:         utils.StrPtr("Order is returned or cancelled"),
				Source:         utils.StrPtr("PAYMENTS REFRESH"),
			})
			if err != nil {
				paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to initiate refund")
				return response, err
			}
		} else {
			// Check if the payment ID in transaction data is one of the successful payments
			orderPaymentData, err := GetOrderPaymentDataOrderId(ctx, request.OrderID, s.repository)
			if err != nil {
				paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get order payment data")
				return response, fmt.Errorf("failed to get order payment data: %v", err)
			}
			if orderPaymentData.PaymentId == nil || orderPaymentData.PaidAmount == nil || *orderPaymentData.PaidAmount == 0 {
				if orderStatus.DisplayStatus == constants.CONFIRMED || orderStatus.DisplayStatus == constants.SHIPMENT_CREATED ||
					orderStatus.DisplayStatus == constants.DELIVERED || orderStatus.DisplayStatus == constants.IN_TRANSIT ||
					orderStatus.DisplayStatus == constants.OTHERS || orderStatus.DisplayStatus == constants.NDR {
					paymentLogger.Info(paymentCtx, "Order is in a state where payment is not required", "order_status", orderStatus.DisplayStatus, "order_id", request.OrderID, "payment_id", orderPaymentData.PaymentId)

					for _, paymentId := range successPayments {
						// Initiate a refund for this payment
						refundResponse, err := s.Payments.RefundPayment(ctx, dto.RefundPaymentRequest{
							KCOrderId:      request.OrderID,
							PaymentOrderId: transactionData.GatewayTransactionID,
							RefundAmount:   int(-1),
							PaymentId:      paymentId,
						}, "")

						processed := true
						if err != nil {
							paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to refund extra payment", "payment_id", paymentId)
							processed = false
							// Continue with other refunds even if one fails
						} else {
							paymentLogger.Info(paymentCtx, "Successfully initiated refund for extra payment", "payment_id", paymentId, "refund_id", refundResponse.ID)
							response.Status = "updated"
						}

						eventObject := map[string]interface{}{
							"distinct_id":      request.UserID,
							"order_id":         request.OrderID,
							"payment_order_id": transactionData.GatewayTransactionID,
							"payment_id":       paymentId,
							"payment_status":   "REFUND_INITIATED",
							"refund_by":        "AUTO_REFRESH",
							"refund_reason":    "Multiple Successful Payments",
							"payment_amount":   transactionData.PaymentAmount,
							"processed":        processed,
						}
						s.Mixpanel.Track(ctx, []*mixpanel.Event{
							s.Mixpanel.NewEvent("All Payment Received After Order Confirmed", request.UserID, eventObject),
						})

					}
				}
			}

			paymentIDFound := false
			for _, successPaymentID := range successPayments {
				if transactionData.PaymentID == successPaymentID {
					paymentIDFound = true
					break
				}
			}

			if paymentIDFound {
				// Payment in orders is a success,
			} else {
				// We need to swap it - update the payment ID in transaction data to the first successful payment

				sqlUpdateData := dao.PaymentGatewayTransaction{
					PaymentID:     successPayments[0],
					GatewayStatus: payments.StatusCompleted,
					KcStatus:      payments.StatusCompleted,
				}
				sqlWhereData := dao.PaymentGatewayTransaction{
					GatewayTransactionID: transactionData.GatewayTransactionID,
					KcOrderID:            transactionData.KcOrderID,
				}

				_, rowsEffected, err := s.repository.Update(sqlWhereData, sqlUpdateData)
				if err != nil {
					paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to update the payment ID", "gateway_transaction_id", transactionData.GatewayTransactionID)
					return response, err
				}

				if rowsEffected != 1 {
					paymentLogger.Error(paymentCtx, "Failed to update the payment ID, no rows affected")
					return response, errors.New("failed to update the payment ID")
				}

				response.Status = "updated"
				transactionData.PaymentID = successPayments[0]
				transactionData.GatewayStatus = payments.StatusCompleted
				transactionData.KcStatus = payments.StatusCompleted
			}

			// Make a refund for other payment IDs if there are multiple successful payments
			if len(successPayments) > 1 {
				for _, paymentID := range successPayments {
					if paymentID == transactionData.PaymentID {
						// Skip the payment that matches the transaction data
						continue
					}

					// Initiate a refund for this payment
					refundResponse, err := s.Payments.RefundPayment(ctx, dto.RefundPaymentRequest{
						KCOrderId:      request.OrderID,
						PaymentOrderId: transactionData.GatewayTransactionID,
						RefundAmount:   int(-1),
						PaymentId:      paymentID,
					}, "")

					processed := true
					if err != nil {
						paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to refund extra payment", "payment_id", paymentID)
						processed = false
						// Continue with other refunds even if one fails
					} else {
						paymentLogger.Info(paymentCtx, "Successfully initiated refund for extra payment", "payment_id", paymentID, "refund_id", refundResponse.ID)
						response.Status = "updated"
					}

					eventObject := map[string]interface{}{
						"distinct_id":      request.UserID,
						"order_id":         request.OrderID,
						"payment_order_id": transactionData.GatewayTransactionID,
						"payment_id":       paymentID,
						"payment_status":   "REFUND_INITIATED",
						"refund_by":        "AUTO_REFRESH",
						"refund_reason":    "Multiple Successful Payments",
						"payment_amount":   transactionData.PaymentAmount,
						"processed":        processed,
					}
					s.Mixpanel.Track(ctx, []*mixpanel.Event{
						s.Mixpanel.NewEvent("Multiple Payments for Order Refunded", request.UserID, eventObject),
					})

				}
			}

			// Check if the gateway status needs to be updated
			if transactionData.GatewayStatus != payments.StatusCompleted {
				paymentLogger.Info(paymentCtx, "Updating gateway status to completed")

				sqlUpdateData := dao.PaymentGatewayTransaction{
					GatewayStatus: payments.StatusCompleted,
					KcStatus:      payments.StatusCompleted,
				}
				sqlWhereData := dao.PaymentGatewayTransaction{
					GatewayTransactionID: transactionData.GatewayTransactionID,
					KcOrderID:            transactionData.KcOrderID,
				}

				_, rowsEffected, err := s.repository.Update(sqlWhereData, sqlUpdateData)
				if err != nil {
					paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to update the gateway status", "gateway_transaction_id", transactionData.GatewayTransactionID)
					return response, err
				}

				if rowsEffected != 1 {
					paymentLogger.Error(paymentCtx, "Failed to update the gateway status, no rows affected")
					return response, errors.New("failed to update the gateway status")
				}

				response.Status = "updated"

				// If order is in PLACED status, confirm it
				if orderStatus.DisplayStatus == constants.PLACED || orderStatus.DisplayStatus == constants.PENDING_CONFIRMATION {
					paymentLogger.Info(paymentCtx, "Confirming order after payment completion")

					if err != nil {
						paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to get user ID for order")
						return response, err
					}

					err = s.confirmOrder(ctx, request.OrderID, &request.UserID, transactionData.PaymentID, transactionData.PaymentAmount,
						transactionData.PaymentDiscount+transactionData.PaymentMethodDiscount, transactionData.OrderAmount, nil)
					if err != nil {
						paymentLogger.WithError(paymentCtx, err).Error(paymentCtx, "Failed to confirm order")
						return response, err
					}

					seller, _ := s.getSellerFromOrderID(request.OrderID)
					eventObject := map[string]interface{}{
						"distinct_id":      request.UserID,
						"order_id":         request.OrderID,
						"seller":           seller,
						"ordering_module":  utils.MakeTitleCase(seller),
						"order_value":      utils.Round(transactionData.OrderAmount),
						"payment_amount":   utils.Round(transactionData.PaymentAmount),
						"payment_discount": utils.Round(transactionData.PaymentDiscount),
						"payment_method":   "ADVANCE",
						"payment_order_id": transactionData.GatewayTransactionID,
						"payment_status":   "PAID",
						"payment_id":       transactionData.PaymentID,
						"source":           "PAYMENTS REFRESH",
					}

					s.Mixpanel.Track(ctx, []*mixpanel.Event{
						s.Mixpanel.NewEvent("Payment Success", request.UserID, eventObject),
					})
					response.Status = "updated"
				}
			}
		}
	}

	// Handle case where all payments are refunded or failed
	if len(refundPayments)+len(failedPayments) == len(paymentRecords) {
		if orderStatus.DisplayStatus == constants.CONFIRMED {
			paymentLogger.Error(paymentCtx, "All payments are refunded or failed, but order is confirmed.")
			//// Get user ID for the order
			//userId, err := s.getUserIdFromOrderID(request.OrderID)
			//if err != nil {
			//	logger.Error(ctx, "Failed to get user ID for order", err, request.OrderID)
			//	return response, err
			//}
			//
			//// Cancel the order
			//_, err = s.HandleConfirmedOrder(ctx, dto.ConfirmOrderRequest{
			//	Data: dto.ConfirmOrderData{
			//		UserID:      userId,
			//		OrderID:     strconv.FormatInt(request.OrderID, 10),
			//		Email:       "APP",
			//		Source:      "APP",
			//		OrderStatus: constants.CANCELLED,
			//		Message:     "Order cancelled due to payment failure",
			//		OrderMeta: dto.OrderMeta{
			//			CancelReason: cancelReason.ReasonAdvancePayment,
			//		},
			//	},
			//})
			//
			//if err != nil {
			//	logger.Error(ctx, "Failed to cancel order", err, request.OrderID)
			//	return response, err
			//}
			//
			//response.Status = "updated"
			s.Mixpanel.Track(ctx, []*mixpanel.Event{
				s.Mixpanel.NewEvent("Payment Refunded But Order Confirmed", request.UserID, map[string]interface{}{
					"distinct_id":    request.UserID,
					"order_id":       request.OrderID,
					"payment_id":     transactionData.PaymentID,
					"payment_status": "REFUNDED",
					"order_status":   orderStatus.DisplayStatus,
					"payment_amount": transactionData.PaymentAmount,
					"refund_amount":  transactionData.RefundAmount,
				}),
			})
			slack.SendSlackMessage(fmt.Sprintf("All payments refunded but order confirmed: OrderID=%d, PaymentID=%s, Amount=%.2f",
				request.OrderID, transactionData.PaymentID, transactionData.PaymentAmount))
		}
	}

	paymentLogger.Info(paymentCtx, "RefreshOrderPayment completed successfully", "response_status", response.Status)
	return response, nil
}

func (s *Service) GetUnmappedRNN(ctx *gin.Context, request dto.GetUnmappedRNNRequest) (dto.GetUnmappedRNNResponse, error) {
	sevenDaysAgo := time.Now().AddDate(0, 0, -7).Unix()

	// Base query to find RNNs in payment_gateway_records that are not mapped to any order in order_payments
	query := fmt.Sprintf(`
		SELECT pgr.rnn, pgr.amount/100 as amount, pgr.id as payment_id
		FROM kiranabazar_payment_gateway_records pgr
		LEFT JOIN kiranabazar_order_payments kop ON pgr.rnn = kop.rnn
		WHERE pgr.rnn IS NOT NULL
		AND pgr.created_at >= %d
		AND kop.id IS NULL
		AND (pgr.gateway_order_id is NULL OR pgr.gateway_order_id = '')  ORDER BY pgr.created_at DESC`, sevenDaysAgo)

	var unmappedRNNItems []struct {
		RNN       string  `db:"rnn"`
		Amount    float64 `db:"amount"`
		PaymentId string  `db:"payment_id"`
	}
	_, err := s.repository.CustomQuery(&unmappedRNNItems, query)
	if err != nil {
		paymentLogger.WithError(ctx, err).Error(ctx, "Failed to fetch unmapped RNNs")
		return dto.GetUnmappedRNNResponse{}, err
	}

	var rnnItems []dto.RNNItem
	for _, item := range unmappedRNNItems {
		rnnItems = append(rnnItems, dto.RNNItem{
			RNN:       item.RNN,
			Amount:    item.Amount,
			PaymentId: item.PaymentId,
		})
	}

	RNNCache.Set = true
	RNNCache.items = rnnItems

	var responseItems []dto.RNNItem
	if request.Refresh || request.Amount == -1 {
		responseItems = rnnItems
	} else {
		for _, item := range rnnItems {
			if item.Amount == request.Amount {
				responseItems = append(responseItems, item)
			}
		}
	}

	return dto.GetUnmappedRNNResponse{
		Data: responseItems,
	}, nil
}
