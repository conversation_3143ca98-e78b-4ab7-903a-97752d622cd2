package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/external/zoho"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/utils"
	"net/http"
	"strings"
	"time"

	"github.com/mixpanel/mixpanel-go"
)

func (s *Service) CreateZohoContact(c context.Context, request dto.ZohoCreateContactRequest) (*dto.ZohoCreateContactResponse, error) {
	response := dto.ZohoCreateContactResponse{}
	mp := map[string]string{}
	resp, statusCode, err := s.ZohoClient.CallAPI(zoho.ZOHO_ADMIN, zoho.CREATE_CONTACT, request, mp, mp, mp)
	if err != nil {
		return nil, err
	}
	if statusCode != nil && *statusCode != http.StatusOK {
		return nil, errors.New(fmt.Sprintf("not able to create a ticket status = %d", *statusCode))
	}
	err = json.Unmarshal(resp, &response)
	if err != nil {
		return nil, err
	}
	return &response, nil
}

func (s *Service) CreateZohoTicket(c context.Context, request dto.ZohoCreateTicketRequest) (*dto.ZohoCreateTicketResponse, error) {
	response := dto.ZohoCreateTicketResponse{}
	mp := map[string]string{}
	resp, statusCode, err := s.ZohoClient.CallAPI(zoho.ZOHO_ADMIN, zoho.CREATE_TICKET, request, mp, mp, mp)
	if err != nil {
		return nil, err
	}
	if statusCode != nil && *statusCode != http.StatusOK {
		return nil, errors.New(fmt.Sprintf("not able to create a ticket status = %d", *statusCode))
	}
	err = json.Unmarshal(resp, &response)
	if err != nil {
		return nil, err
	}
	return &response, nil
}

func (s *Service) CreateComment(c *context.Context, request dto.ZohoCreateCommentRequest, ticketID string) (*dto.ZohoCreateCommentResponse, error) {
	response := dto.ZohoCreateCommentResponse{}
	mp := map[string]string{}
	params := map[string]string{}
	params["ticket_id"] = ticketID
	resp, statusCode, err := s.ZohoClient.CallAPI(zoho.ZOHO_USER, zoho.CREATE_COMMENT, request, mp, mp, params)
	if err != nil {
		return nil, err
	}
	if statusCode != nil && *statusCode != http.StatusOK {
		return nil, errors.New(fmt.Sprintf("not able to create comment status = %d", *statusCode))
	}
	err = json.Unmarshal(resp, &response)
	if err != nil {
		return nil, err
	}
	return &response, nil
}

func getByteFromInterface(inter interface{}) []byte {
	byt, _ := json.Marshal(inter)
	return byt
}
func handleEventTypeTicketAdd(c context.Context, request dto.ZohoWebhookRequest, repository *sqlRepo.Repository) error {
	payload := dto.AddTicketEventPayload{}
	if err := json.Unmarshal(getByteFromInterface(request.Payload), &payload); err != nil {
		return fmt.Errorf("error parsing payload for Ticket Add: %w", err)
	}
	_, err := repository.Upsert(&dao.ZohoTicket{
		ID:       payload.ID,
		Data:     getByteFromInterface(payload),
		IsActive: true,
	})
	if err != nil {
		return err
	}
	return nil
}
func handleEventTypeTicketUpdate(c context.Context, request dto.ZohoWebhookRequest, repository *sqlRepo.Repository, mp *mixpanelRepo.Repository) error {
	payload := dto.UpdateTicketEventPayload{}
	if err := json.Unmarshal(getByteFromInterface(request.Payload), &payload); err != nil {
		return fmt.Errorf("error parsing payload for Ticket Update: %w", err)
	}
	prevState := dto.UpdateTicketEventPrevState{}
	if err := json.Unmarshal(getByteFromInterface(request.PrevState), &prevState); err != nil {
		return fmt.Errorf("error parsing prevState for Ticket Update: %w", err)
	}
	_, err := repository.Upsert(&dao.ZohoTicket{
		ID:       payload.ID,
		Data:     getByteFromInterface(payload),
		IsActive: true,
	})
	if err != nil {
		return err
	}
	_, err = repository.CustomQuery(nil, fmt.Sprintf(`update kiranaclubdb.kc_bazar_tickets set status = '%s', is_spam = %v where ticket_id = '%s';`, strings.ToUpper(payload.Status), payload.IsSpam, payload.ID))
	if err != nil {
		return err
	}

	if payload.Status != prevState.Status {
		mp.Track(c, []*mixpanel.Event{
			mp.NewEvent("Support Ticket Status Changed", *payload.CustomFields.UserID, map[string]interface{}{
				"ticket_status":   payload.Status,
				"prev_status":     prevState.Status,
				"ticket_id":       payload.ID,
				"email":           "",
				"order_id":        *payload.CustomFields.OrderID,
				"ordering_module": utils.MakeTitleCase(*payload.CustomFields.Seller),
				"seller":          *payload.CustomFields.Seller,
			}),
		})
		if payload.Status == "Closed" {
			mp.Track(c, []*mixpanel.Event{
				mp.NewEvent("Support Ticket Closed", *payload.CustomFields.UserID, map[string]interface{}{
					"category":        payload.Category,
					"ticket_id":       payload.ID,
					"order_id":        *payload.CustomFields.OrderID,
					"sub_category":    payload.SubCategory,
					"ordering_module": utils.MakeTitleCase(*payload.CustomFields.Seller),
					"seller":          *payload.CustomFields.Seller,
					"email":           "",
				}),
			})
		}
	}

	return nil
}
func handleEventTypeTicketDelete(c context.Context, request dto.ZohoWebhookRequest, repository *sqlRepo.Repository) error {
	payload := dto.AddTicketEventPayload{}
	if err := json.Unmarshal(getByteFromInterface(request.Payload), &payload); err != nil {
		return fmt.Errorf("error parsing payload for Ticket Delete: %w", err)
	}
	_, err := repository.CustomQuery(nil, fmt.Sprintf(`update zoho_tickets set is_active = false where id = '%s'`, payload.ID))
	if err != nil {
		return err
	}
	_, err = repository.CustomQuery(nil, fmt.Sprintf(`update kiranaclubdb.kc_bazar_tickets set is_active = true where ticket_id = '%s';`, payload.ID))
	if err != nil {
		return err
	}
	return nil
}

func getTicketIDFromZohoTicketID(zohoticketID string, repository *sqlRepo.Repository) (uint64, string, string, string, *int64, error) {
	ticketData := dao.KcBazarTicket{}
	_, err := repository.Find(map[string]interface{}{
		"ticket_id": zohoticketID,
	}, &ticketData)
	if err != nil {
		return 0, "", "", "", nil, err
	}
	return ticketData.ID, ticketData.UserID, ticketData.Status, ticketData.TicketID, ticketData.OrderID, nil
}

func (s *Service) handleEventTypeTicketCommentAdd(c context.Context, request dto.ZohoWebhookRequest, repository *sqlRepo.Repository, mp *mixpanelRepo.Repository) error {
	payload := dto.TicketCommentAddEventPayload{}
	if err := json.Unmarshal(getByteFromInterface(request.Payload), &payload); err != nil {
		return fmt.Errorf("error parsing payload for Ticket CommentAdd: %w", err)
	}
	_, err := repository.Upsert(&dao.ZohoTicketComment{
		ID:       payload.ID,
		Data:     getByteFromInterface(payload),
		IsActive: true,
	})

	content, err := extractTextFromHTML(payload.Content)
	if err != nil {
		return err
	}

	var ooid int64 = -1
	kcTicketID, userID, status, zohoTicketID, orderID, err := getTicketIDFromZohoTicketID(payload.TicketID, repository)
	if orderID != nil {
		ooid = *orderID
	}

	_, err = repository.Create(&dao.KcBazarTicketConversation{
		ConversationID: payload.ID,
		TicketID:       kcTicketID,
		UserID:         payload.CommenterID,
		UserType:       "agent",
		ContentType:    "plainText",
		Content:        content,
		CreatedAt:      time.Now().UnixMilli(),
		UpdatedAt:      time.Now().UnixMilli(),
	})
	if err != nil {
		return err
	}

	seller, err := s.getSellerFromOrderID(ooid)

	mp.Track(c, []*mixpanel.Event{
		mp.NewEvent("Support Team Commented on Support Ticket", userID, map[string]interface{}{
			"ticket_status":   status,
			"ticket_id":       zohoTicketID,
			"ticket_comment":  content,
			"attachment_url":  "",
			"order_id":        ooid,
			"email":           "",
			"seller":          seller,
			"ordering_module": utils.MakeTitleCase(seller),
		}),
	})
	return nil
}
func handleEventTypeTicketCommentUpdate(c context.Context, request dto.ZohoWebhookRequest, repository *sqlRepo.Repository) error {
	payload := dto.TicketCommentUpdateEventPayload{}
	if err := json.Unmarshal(getByteFromInterface(request.Payload), &payload); err != nil {
		return fmt.Errorf("error parsing payload for Ticket Comment Update: %w", err)
	}
	_, err := repository.Upsert(&dao.ZohoTicketComment{
		ID:       payload.ID,
		Data:     getByteFromInterface(payload),
		IsActive: true,
	})
	if err != nil {
		return err
	}
	return nil
}
func handleEventTypeContactAdd(c context.Context, request dto.ZohoWebhookRequest, repository *sqlRepo.Repository) error {
	payload := dto.ContactAddEventPayload{}
	if err := json.Unmarshal(getByteFromInterface(request.Payload), &payload); err != nil {
		return fmt.Errorf("error parsing payload for Contact Add: %w", err)
	}
	_, err := repository.Upsert(&dao.ZohoContactDetails{
		ID:       payload.ID,
		Data:     getByteFromInterface(payload),
		IsActive: true,
	})
	if err != nil {
		return err
	}
	return nil
}
func handleEventTypeContactUpdate(c context.Context, request dto.ZohoWebhookRequest, repository *sqlRepo.Repository) error {
	payload := dto.ContactUpdateEventPayload{}
	if err := json.Unmarshal(getByteFromInterface(request.Payload), &payload); err != nil {
		return fmt.Errorf("error parsing payload for Contact Update: %w", err)
	}
	_, err := repository.Upsert(&dao.ZohoContactDetails{
		ID:       payload.ID,
		Data:     getByteFromInterface(payload),
		IsActive: true,
	})
	if err != nil {
		return err
	}
	return nil
}
func handleEventTypeContactDelete(c context.Context, request dto.ZohoWebhookRequest, repository *sqlRepo.Repository) error {
	payload := dto.AddTicketEventPayload{}
	if err := json.Unmarshal(getByteFromInterface(request.Payload), &payload); err != nil {
		return fmt.Errorf("error parsing payload for Contact Delete: %w", err)
	}
	_, err := repository.CustomQuery(nil, fmt.Sprintf(`update zoho_contact_details set is_active = false where id = '%s'`, payload.ID))
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) handleEventTypeTicketAttachmentAdd(c context.Context, request dto.ZohoWebhookRequest, repository *sqlRepo.Repository, mp *mixpanelRepo.Repository) error {
	payload := dto.TicketAttachmentAddEventPayload{}
	if err := json.Unmarshal(getByteFromInterface(request.Payload), &payload); err != nil {
		return fmt.Errorf("error parsing payload for Attachment Add: %w", err)
	}
	_, err := repository.Upsert(&dao.ZohoTicketAttachment{
		ID:       payload.ID,
		Data:     getByteFromInterface(payload),
		IsActive: true,
	})
	if err != nil {
		return err
	}

	if payload.CreatorId == "171893000000221405" {
		return nil
	}

	kcTicketID, userID, status, zohoTicketID, orderID, err := getTicketIDFromZohoTicketID(payload.TicketId, repository)

	var ooid int64 = -1
	if orderID != nil {
		ooid = *orderID
	}

	dbResp, err := repository.Create(&dao.KcBazarTicketConversation{
		ConversationID: payload.ID,
		TicketID:       kcTicketID,
		UserID:         payload.CreatorId,
		UserType:       "agent",
		ContentType:    "plainText",
		Content:        "",
		CreatedAt:      time.Now().UnixMilli(),
		UpdatedAt:      time.Now().UnixMilli(),
	})
	if err != nil {
		return err
	}

	ticketConversation := dbResp.(*dao.KcBazarTicketConversation)

	tim := time.Now().UnixMilli()

	a := fmt.Sprintf(`[{"url": "%s", "type": "image"}]`, payload.Href)
	kca := dao.KCBazarConversationAttachment{
		ConversationID: fmt.Sprintf("%s", ticketConversation.ConversationID),
		Attachment:     []byte(a),
		CreatedAt:      &tim,
		UpdatedAt:      &tim,
		IsActive:       true,
	}
	_, err = repository.Create(&kca)
	if err != nil {
		return err
	}

	seller, err := s.getSellerFromOrderID(ooid)

	mp.Track(c, []*mixpanel.Event{
		mp.NewEvent("Support Team Commented on Support Ticket", userID, map[string]interface{}{
			"ticket_status":   status,
			"ticket_id":       zohoTicketID,
			"ticket_comment":  "",
			"attachment_url":  payload.Href,
			"order_id":        ooid,
			"email":           "",
			"seller":          seller,
			"ordering_module": utils.MakeTitleCase(seller),
		}),
	})
	return nil
}
func handleEventTypeTicketAttachmentUpdate(c context.Context, request dto.ZohoWebhookRequest, repository *sqlRepo.Repository) error {
	payload := dto.TicketAttachmentUpdateEventPayload{}
	if err := json.Unmarshal(getByteFromInterface(request.Payload), &payload); err != nil {
		return fmt.Errorf("error parsing payload for Attachment Update: %w", err)
	}
	_, err := repository.Upsert(&dao.ZohoTicketAttachment{
		ID:       payload.ID,
		Data:     getByteFromInterface(payload),
		IsActive: true,
	})
	if err != nil {
		return err
	}
	return nil
}
func handleEventTypeTicketAttachmentDelete(c context.Context, request dto.ZohoWebhookRequest, repository *sqlRepo.Repository) error {
	payload := dto.TicketAttachmentDeleteEventPayload{}
	if err := json.Unmarshal(getByteFromInterface(request.Payload), &payload); err != nil {
		return fmt.Errorf("error parsing payload for Attachment Delete: %w", err)
	}
	_, err := repository.CustomQuery(nil, fmt.Sprintf(`update zoho_ticket_attachments set is_active = false where id = '%s'`, payload.ID))
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) handleWebhookEvent(c context.Context, request dto.ZohoWebhookRequest, repository *sqlRepo.Repository) error {
	switch request.EventType {
	case zoho.EventTypeTicketAdd:
		return handleEventTypeTicketAdd(c, request, repository)
	case zoho.EventTypeTicketUpdate:
		return handleEventTypeTicketUpdate(c, request, repository, s.Mixpanel)
	case zoho.EventTypeTicketDelete:
		return handleEventTypeTicketDelete(c, request, repository)
	case zoho.EventTypeTicketApprovalAdd:
		fmt.Println("Handling Ticket_Approval_Add event")
		return nil
	case zoho.EventTypeTicketApprovalUpdate:
		fmt.Println("Handling Ticket_Approval_Update event")
		return nil
	case zoho.EventTypeTicketThreadAdd:
		fmt.Println("Handling Ticket_Thread_Add event")
		return nil
	case zoho.EventTypeTicketCommentAdd:
		return s.handleEventTypeTicketCommentAdd(c, request, repository, s.Mixpanel)
	case zoho.EventTypeTicketCommentUpdate:
		return handleEventTypeTicketCommentUpdate(c, request, repository)
	case zoho.EventTypeContactAdd:
		return handleEventTypeContactAdd(c, request, repository)
	case zoho.EventTypeContactUpdate:
		return handleEventTypeContactUpdate(c, request, repository)
	case zoho.EventTypeContactDelete:
		return handleEventTypeContactDelete(c, request, repository)
	case zoho.EventTypeAccountAdd:
		// Logic for Account_Add
		fmt.Println("Handling Account_Add event")
		return nil
	case zoho.EventTypeAccountUpdate:
		// Logic for Account_Update
		fmt.Println("Handling Account_Update event")
		return nil
	case zoho.EventTypeAccountDelete:
		// Logic for Account_Delete
		fmt.Println("Handling Account_Delete event")
		return nil
	case zoho.EventTypeDepartmentAdd:
		// Logic for Department_Add
		fmt.Println("Handling Department_Add event")
		return nil
	case zoho.EventTypeDepartmentUpdate:
		// Logic for Department_Update
		fmt.Println("Handling Department_Update event")
		return nil
	case zoho.EventTypeAgentAdd:
		// Logic for Agent_Add
		fmt.Println("Handling Agent_Add event")
		return nil
	case zoho.EventTypeAgentUpdate:
		// Logic for Agent_Update
		fmt.Println("Handling Agent_Update event")
		return nil
	case zoho.EventTypeAgentDelete:
		// Logic for Agent_Delete
		fmt.Println("Handling Agent_Delete event")
		return nil
	case zoho.EventTypeAgentPresenceUpdate:
		// Logic for Agent_Presence_Update
		fmt.Println("Handling Agent_Presence_Update event")
		return nil
	case zoho.EventTypeTicketAttachmentAdd:
		return s.handleEventTypeTicketAttachmentAdd(c, request, repository, s.Mixpanel)
	case zoho.EventTypeTicketAttachmentUpdate:
		return handleEventTypeTicketAttachmentUpdate(c, request, repository)
	case zoho.EventTypeTicketAttachmentDelete:
		return handleEventTypeTicketAttachmentDelete(c, request, repository)
	case zoho.EventTypeTaskAdd:
		// Logic for Task_Add
		fmt.Println("Handling Task_Add event")
		return nil
	case zoho.EventTypeTaskUpdate:
		// Logic for Task_Update
		fmt.Println("Handling Task_Update event")
		return nil
	case zoho.EventTypeTaskDelete:
		// Logic for Task_Delete
		fmt.Println("Handling Task_Delete event")
		return nil
	case zoho.EventTypeCallAdd:
		// Logic for Call_Add
		fmt.Println("Handling Call_Add event")
		return nil
	case zoho.EventTypeCallUpdate:
		// Logic for Call_Update
		fmt.Println("Handling Call_Update event")
		return nil
	case zoho.EventTypeCallDelete:
		// Logic for Call_Delete
		fmt.Println("Handling Call_Delete event")
		return nil
	case zoho.EventTypeEventAdd:
		// Logic for Event_Add
		fmt.Println("Handling Event_Add event")
		return nil
	case zoho.EventTypeEventUpdate:
		// Logic for Event_Update
		fmt.Println("Handling Event_Update event")
		return nil
	case zoho.EventTypeEventDelete:
		// Logic for Event_Delete
		fmt.Println("Handling Event_Delete event")
		return nil
	case zoho.EventTypeTimeEntryAdd:
		// Logic for TimeEntry_Add
		fmt.Println("Handling TimeEntry_Add event")
		return nil
	case zoho.EventTypeTimeEntryUpdate:
		// Logic for TimeEntry_Update
		fmt.Println("Handling TimeEntry_Update event")
		return nil
	case zoho.EventTypeTimeEntryDelete:
		// Logic for TimeEntry_Delete
		fmt.Println("Handling TimeEntry_Delete event")
		return nil
	case zoho.EventTypeArticleAdd:
		// Logic for Article_Add
		fmt.Println("Handling Article_Add event")
		return nil
	case zoho.EventTypeArticleUpdate:
		// Logic for Article_Update
		fmt.Println("Handling Article_Update event")
		return nil
	case zoho.EventTypeArticleDelete:
		// Logic for Article_Delete
		fmt.Println("Handling Article_Delete event")
		return nil
	case zoho.EventTypeArticleTranslationAdd:
		// Logic for Article_Translation_Add
		fmt.Println("Handling Article_Translation_Add event")
		return nil
	case zoho.EventTypeArticleTranslationUpdate:
		// Logic for Article_Translation_Update
		fmt.Println("Handling Article_Translation_Update event")
		return nil
	case zoho.EventTypeArticleTranslationDelete:
		// Logic for Article_Translation_Delete
		fmt.Println("Handling Article_Translation_Delete event")
		return nil
	case zoho.EventTypeArticleFeedbackAdd:
		// Logic for Article_Feedback_Add
		fmt.Println("Handling Article_Feedback_Add event")
		return nil
	case zoho.EventTypeKBRootCategoryAdd:
		// Logic for KBRootCategory_Add
		fmt.Println("Handling KBRootCategory_Add event")
		return nil
	case zoho.EventTypeKBRootCategoryUpdate:
		// Logic for KBRootCategory_Update
		fmt.Println("Handling KBRootCategory_Update event")
		return nil
	case zoho.EventTypeKBRootCategoryDelete:
		// Logic for KBRootCategory_Delete
		fmt.Println("Handling KBRootCategory_Delete event")
		return nil
	case zoho.EventTypeKBSectionAdd:
		// Logic for KBSection_Add
		fmt.Println("Handling KBSection_Add event")
		return nil
	case zoho.EventTypeKBSectionUpdate:
		// Logic for KBSection_Update
		fmt.Println("Handling KBSection_Update event")
		return nil
	case zoho.EventTypeKBSectionDelete:
		// Logic for KBSection_Delete
		fmt.Println("Handling KBSection_Delete event")
		return nil
	case zoho.EventTypeIMMessageAdd:
		// Logic for IM_Message_Add
		fmt.Println("Handling IM_Message_Add event")
		return nil
	default:
		return errors.New("invalid eventType")
	}
}

func (s *Service) ZohoWebhook(c context.Context, request *[]dto.ZohoWebhookRequest) (*dto.ZohoWebhookResponse, error) {
	response := dto.ZohoWebhookResponse{}

	go func(request *[]dto.ZohoWebhookRequest, repository *sqlRepo.Repository) {
		webhookLogs := []dao.ZohoWebhookLogs{}
		for _, reqq := range *request {
			requestObjectByte, err := json.Marshal(reqq)
			if err != nil {
				fmt.Println("not able to marshal the request ", err)
			}
			webhookLog := dao.ZohoWebhookLogs{
				RequestObject: requestObjectByte,
				Type:          reqq.EventType,
				CreatedAt:     time.Now(),
			}
			webhookLogs = append(webhookLogs, webhookLog)
		}
		repository.Create(&webhookLogs)
	}(request, s.repository)

	for _, zohoRequest := range *request {
		err := s.handleWebhookEvent(c, zohoRequest, s.repository)
		if err != nil {
			return nil, err
		}
	}
	return &response, nil
}
