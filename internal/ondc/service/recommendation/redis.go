package recommendation

import (
	"context"
	"fmt"
	"kc/internal/ondc/cache"
	"time"
)

// Key generators for different recommendation types
func GenerateAffinityKey(productID string) string {
	return fmt.Sprintf("RECOMM:PRODUCT_AFFINITY:V1:%s", productID)
}

func GenerateSellerTopKey(seller string) string {
	return fmt.Sprintf("recommendation:seller_top:%s", seller)
}

func GenerateCategoryTopKey(categoryID, seller string) string {
	return fmt.Sprintf("recommendation:category_top:%s:%s", categoryID, seller)
}

// GetAffinityProducts retrieves affinity products from Redis
func GetAffinityProducts(ctx context.Context, productID string) ([][]int, error) {
	key := GenerateAffinityKey(productID)
	var products [][]int
	cacheResult, err := cache.GetInstance().GetRedisCachedData(ctx, key, time.Hour)

	if err != nil {
		return nil, fmt.Errorf("failed to get data from Redis: %w", err)
	}

	if cacheResult.Data != nil {
		// Handle the interface{} to [][]int conversion
		if interfaceSlice, ok := cacheResult.Data.([]interface{}); ok {
			result := make([][]int, len(interfaceSlice))
			for i, row := range interfaceSlice {
				if rowSlice, ok := row.([]interface{}); ok {
					result[i] = make([]int, len(rowSlice))
					for j, val := range rowSlice {
						if floatVal, ok := val.(float64); ok {
							result[i][j] = int(floatVal)
						} else {
							return nil, fmt.Errorf("invalid data type in array")
						}
					}
				} else {
					return nil, fmt.Errorf("invalid row type")
				}
			}
			return result, nil
		}
	}

	return products, nil
}
