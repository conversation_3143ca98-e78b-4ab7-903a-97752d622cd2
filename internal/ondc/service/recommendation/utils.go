package recommendation

//
//import (
//	"fmt"
//	"math"
//	"sort"
//	"time"
//)
//
//// Scoring utility functions
//
//// CalculateAffinityScore calculates affinity score based on co-occurrence and time decay
//func CalculateAffinityScore(affinityProduct AffinityProduct) float64 {
//	// Time decay factor (products viewed together more recently get higher scores)
//	timeDecay := calculateTimeDecay(affinityProduct.LastUpdated)
//
//	// Base score from co-occurrence
//	baseScore := float64(affinityProduct.CoOccurrence) * affinityProduct.AffinityScore
//
//	// Apply time decay
//	return baseScore * timeDecay
//}
//
//// CalculateSellerTopScore calculates score for seller's top products
//func CalculateSellerTopScore(sellerTopProduct SellerTopProduct) float64 {
//	// Combine sales score and popularity score
//	salesWeight := 0.7
//	popularityWeight := 0.3
//
//	return (sellerTopProduct.SalesScore * salesWeight) + (sellerTopProduct.PopularityScore * popularityWeight)
//}
//
//// CalculateCategoryTopScore calculates score for category-seller top products
//func CalculateCategoryTopScore(categoryTopProduct CategoryTopProduct) float64 {
//	// Normalize sales volume and category score
//	normalizedSales := math.Log(float64(categoryTopProduct.SalesVolume) + 1)
//	normalizedCategoryScore := categoryTopProduct.CategoryScore
//
//	return normalizedSales * normalizedCategoryScore
//}
//
//// CalculateCartBasedScore calculates score based on cart analysis
//func CalculateCartBasedScore(productID string, cartItems []string, categories []string, sellers []string) float64 {
//	score := 0.0
//
//	// Check if product is already in cart
//	if contains(cartItems, productID) {
//		return 0.0 // Don't recommend products already in cart
//	}
//
//	// Add score for category diversity
//	if len(categories) > 0 {
//		score += 0.3
//	}
//
//	// Add score for seller diversity
//	if len(sellers) > 0 {
//		score += 0.2
//	}
//
//	// Add score for cart size (larger carts might need complementary products)
//	cartSizeScore := math.Min(float64(len(cartItems))/10.0, 0.5)
//	score += cartSizeScore
//
//	return score
//}
//
//// CalculateFinalScore calculates the final recommendation score
//func CalculateFinalScore(config RecommendationConfig, scores map[string]float64) float64 {
//	finalScore := 0.0
//
//	if affinityScore, exists := scores["affinity"]; exists {
//		finalScore += affinityScore * config.AffinityWeight
//	}
//
//	if sellerTopScore, exists := scores["seller_top"]; exists {
//		finalScore += sellerTopScore * config.SellerTopWeight
//	}
//
//	if categoryTopScore, exists := scores["category_top"]; exists {
//		finalScore += categoryTopScore * config.CategoryTopWeight
//	}
//
//	if cartBasedScore, exists := scores["cart_based"]; exists {
//		finalScore += cartBasedScore * config.CartBasedWeight
//	}
//
//	return finalScore
//}
//
//// Data processing utility functions
//
//// MergeAndDeduplicateRecommendations merges recommendations from different sources and removes duplicates
//func MergeAndDeduplicateRecommendations(recommendations []ProductRecommendation) []ProductRecommendation {
//	seen := make(map[string]bool)
//	merged := make([]ProductRecommendation, 0)
//
//	for _, rec := range recommendations {
//		if !seen[rec.ProductID] {
//			seen[rec.ProductID] = true
//			merged = append(merged, rec)
//		}
//	}
//
//	return merged
//}
//
//// SortRecommendationsByScore sorts recommendations by score in descending order
//func SortRecommendationsByScore(recommendations []ProductRecommendation) []ProductRecommendation {
//	sort.Slice(recommendations, func(i, j int) bool {
//		return recommendations[i].Score > recommendations[j].Score
//	})
//	return recommendations
//}
//
//// FilterRecommendationsByThreshold filters recommendations below a minimum score threshold
//func FilterRecommendationsByThreshold(recommendations []ProductRecommendation, threshold float64) []ProductRecommendation {
//	filtered := make([]ProductRecommendation, 0)
//
//	for _, rec := range recommendations {
//		if rec.Score >= threshold {
//			filtered = append(filtered, rec)
//		}
//	}
//
//	return filtered
//}
//
//// LimitRecommendations limits the number of recommendations returned
//func LimitRecommendations(recommendations []ProductRecommendation, limit int) []ProductRecommendation {
//	if len(recommendations) <= limit {
//		return recommendations
//	}
//	return recommendations[:limit]
//}
//
//// Helper functions
//
//// calculateTimeDecay calculates time decay factor for recommendations
//func calculateTimeDecay(lastUpdated time.Time) float64 {
//	daysSinceUpdate := time.Since(lastUpdated).Hours() / 24
//	decayFactor := math.Exp(-daysSinceUpdate / 30) // 30-day half-life
//	return math.Max(decayFactor, 0.1)              // Minimum decay factor of 0.1
//}
//
//// contains checks if a slice contains a specific string
//func contains(slice []string, item string) bool {
//	for _, s := range slice {
//		if s == item {
//			return true
//		}
//	}
//	return false
//}
//
//// GenerateRequestID generates a unique request ID for caching
//func GenerateRequestID(productID, userID string) string {
//	timestamp := time.Now().Unix()
//	return fmt.Sprintf("%s_%s_%d", productID, userID, timestamp)
//}
//
//// ValidateRecommendationRequest validates the input request
//func ValidateRecommendationRequest(req *RecommendationRequest) error {
//	if req.ProductID == "" {
//		return fmt.Errorf("product_id is required")
//	}
//	if req.UserID == "" {
//		return fmt.Errorf("user_id is required")
//	}
//	if req.Limit <= 0 {
//		req.Limit = 10 // Default limit
//	}
//	if req.Limit > 50 {
//		req.Limit = 50 // Maximum limit
//	}
//	return nil
//}
//
//// GetDefaultConfig returns default configuration for recommendation service
//func GetDefaultConfig() RecommendationConfig {
//	return RecommendationConfig{
//		AffinityWeight:     0.4,
//		SellerTopWeight:    0.3,
//		CategoryTopWeight:  0.2,
//		CartBasedWeight:    0.1,
//		MaxRecommendations: 20,
//		CacheTTL:           300, // 5 minutes
//		MinScoreThreshold:  0.1,
//	}
//}
