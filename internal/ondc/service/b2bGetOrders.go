package service

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/service/brands"
	orderstatus "kc/internal/ondc/service/orderStatus"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	processingstatus "kc/internal/ondc/service/orderStatus/processingStatus"
	shipmentstatus "kc/internal/ondc/service/orderStatus/shipmentStatus"
	"kc/internal/ondc/utils"
	"log"
	"slices"
	"strings"
	"time"
)

var (
	RECO_DAYS = map[string]int{
		"PLACED":          5,
		"CONFIRMED":       5,
		"IN_TRANSIT":      10,
		"CONFIRMED_72HRS": 3,
	}

	RECO_STATUS = []string{"PLACED", "CONFIRMED", "IN_TRANSIT"}
	RECO_STATUS_EXTENDED = []string{"PLACED", "CONFIRMED_72HRS", "IN_TRANSIT"}
)

func getCombinedConditions(recoStatus []string, queryType string) string {
	if queryType == "count" {
		if slices.Contains(recoStatus, "CONFIRMED_72HRS") {
			recoStatus = RECO_STATUS_EXTENDED
		} else {
			recoStatus = RECO_STATUS
		}
	}
	var conditions []string
	for _, status := range recoStatus {
		fmt.Println("status", status)
		recoDays := RECO_DAYS[status]
		if status == "CONFIRMED_72HRS" {
			status = "CONFIRMED"
		}
		if recoDays == 0 {
			recoDays = 5
		}
		timestampXDaysAgo := time.Now().AddDate(0, 0, -recoDays).Truncate(24 * time.Hour).UnixMilli()

		switch status {
		case "PLACED":
			conditions = append(conditions, fmt.Sprintf(`
				(kbr.order_placed <= %d AND
				kbr.order_confirmed IS NULL AND 
				kbr.order_shipment_created IS NULL AND 
				kbr.order_delivered IS NULL AND 
				kbr.order_cancelled IS NULL AND 
				kbr.order_returned IS NULL)`, timestampXDaysAgo))
		case "CONFIRMED":
			conditions = append(conditions, fmt.Sprintf(`
				(kbr.order_confirmed <= %d AND
				kbr.order_shipment_created IS NULL AND 
				kbr.order_delivered IS NULL AND 
				kbr.order_cancelled IS NULL AND 
				kbr.order_returned IS NULL)`, timestampXDaysAgo))
		case "IN_TRANSIT":
			conditions = append(conditions, fmt.Sprintf(`
				(kbr.order_shipment_created <= %d AND
				kbr.order_delivered IS NULL AND 
				kbr.order_cancelled IS NULL AND 
				kbr.order_returned IS NULL)`, timestampXDaysAgo))
		}
	}
	return strings.Join(conditions, " OR ")
}

func formatNullTime(nt sql.NullTime) string {
	if nt.Valid {
		return nt.Time.Format("2006-01-02 15:04:05") // Format the time as needed
	}
	return "" // Return a default value if the time is invalid
}

func formatTimeStamp(ts int64) string {
	if ts == 0 {
		return ""
	}
	t := time.UnixMilli(ts)
	return t.Format("2006-01-02 15:04:05")
}

func (s *Service) GetOrders(ctx context.Context, req *dto.GetOrdersRequest) (response *dto.GetOrdersResponse, err error) {
	// TODO: @sanket -- this code is not written properly please fix this
	// this code is to handle different order status, this is done because FE is written in a way where it can pass display status, we can change it but its a FE task
	if len(req.Status) > 0 && req.Status[0] == processingstatus.EXCEPTION {
		req.Status = []string{displaystatus.SHIPMENT_CREATED}
		req.ProcessingStatus = []string{processingstatus.EXCEPTION}
	} else if len(req.Status) > 0 && req.Status[0] == processingstatus.SHIPMENT_MANIFESTED {
		req.Status = []string{displaystatus.SHIPMENT_CREATED}
		req.ProcessingStatus = []string{processingstatus.SHIPMENT_MANIFESTED}
	} else if len(req.Status) > 0 && req.Status[0] == displaystatus.SHIPMENT_CREATED {
		req.Status = []string{displaystatus.SHIPMENT_CREATED}
		req.ProcessingStatus = []string{displaystatus.SHIPMENT_CREATED}
	} else if len(req.Status) > 0 && req.Status[0] == shipmentstatus.RTO_DELIVERED {
		req.Status = []string{displaystatus.RETURNED}
		req.DeliveryStatus = []string{shipmentstatus.RTO_DELIVERED}
	} else if len(req.Status) > 0 && req.Status[0] == shipmentstatus.RTO_IN_TRANSIT {
		req.Status = []string{displaystatus.RETURNED}
		req.DeliveryStatus = []string{shipmentstatus.RTO_IN_TRANSIT}
	} else if len(req.Status) > 0 && req.Status[0] == shipmentstatus.RTO_RECEIVED {
		req.Status = []string{displaystatus.RETURNED}
		req.DeliveryStatus = []string{shipmentstatus.RTO_RECEIVED}
	} else if len(req.Status) > 0 && req.Status[0] == shipmentstatus.LOST {
		req.Status = []string{displaystatus.RETURNED}
		req.DeliveryStatus = []string{shipmentstatus.LOST}
	}

	// Handling Time (From, To) if the timestamps are 0 then add 1 year before timestamp -- this can be changed to 1 month
	if req.From == 0 {
		req.From = time.Now().Add(time.Duration(-1 * time.Hour * 8760)).UnixMilli()
	}
	if req.To == 0 {
		req.To = time.Now().UnixMilli()
	}

	loc, _ := time.LoadLocation("Asia/Kolkata")
	fromTime := time.UnixMilli(req.From).In(loc)
	toTime := time.UnixMilli(req.To).In(loc)

	fromDate := time.Date(fromTime.Year(), fromTime.Month(), fromTime.Day(), 0, 0, 0, 0, loc).Format("2006-01-02 15:04:05")
	toDate := time.Date(toTime.Year(), toTime.Month(), toTime.Day(), 23, 59, 59, 0, loc).Format("2006-01-02 15:04:05")

	commonJoinQuery := `
		SELECT ko.created_at, kop.paid_amount, kop.payment_meta, ko.seller, ko.user_id, ko.order_status,
		ko.payment_status, ko.delivery_status, ko.display_status, ko.processing_status, kod.order_details, kod.printing_label, kod.picklist, kop.ext_invoice, kop.ext_invoice_number,
		kod.order_id, kcs.call_status, kcs.note, kcs.updated_at, kcs.updated_by, kcs.cancel_reason, kcs.returned_reason, ug.geo_district, kam.expected_return_at
	`
	if len(req.RecoStatus) > 0 {
		commonJoinQuery += `, kbr.order_placed, kbr.order_confirmed, kbr.order_shipment_created, kbr.order_delivered, kbr.order_cancelled, kbr.order_returned `
	}
	commonJoinQuery += `
		FROM kiranabazar_orders ko
		JOIN kiranabazar_order_details kod ON ko.id = kod.order_id
		LEFT JOIN kiranabazar_call_status kcs ON ko.id = kcs.order_id
		LEFT JOIN kiranabazar_order_payments kop ON ko.id = kop.order_id
		LEFT JOIN users_geography ug ON ko.user_id = ug.user_id
		LEFT JOIN kc_bazar_reconciliation kbr ON ko.id = kbr.order_id
		LEFT JOIN kiranabazar_awb_master kam ON ko.id = kam.order_id and kam.is_primary = true WHERE 1=1
	`

	var query string
	if req.UserID != "" {
		query = fmt.Sprintf(`%s AND (ko.user_id = '%s' OR kod.customer_phone = '%s' OR kod.order_id = '%s')`, commonJoinQuery, req.UserID, req.UserID, req.UserID)
	} else if len(req.RecoStatus) > 0 {
		combinedConditions := getCombinedConditions(req.RecoStatus, "data")
		if combinedConditions != "" {
			query = fmt.Sprintf(`%s AND (%s)`, commonJoinQuery, combinedConditions)
		}
	} else if len(req.NotDeliveryStatus) > 0 {
		query = fmt.Sprintf(`%s `, commonJoinQuery)
	} else {
		if req.RequestSource == "B2B_EXTERNAL" {
			query = fmt.Sprintf(`%s AND kbr.order_confirmed BETWEEN %d AND %d`, commonJoinQuery, req.From, req.To)
		} else {
			query = fmt.Sprintf(`%s AND ko.created_at BETWEEN '%s' AND '%s'`, commonJoinQuery, fromDate, toDate)
		}
	}

	if len(req.Status) > 0 && !contains(req.Status, "OTHER") {
		query += fmt.Sprintf(` AND ko.display_status IN ('%s')`, strings.Join(req.Status, "','"))
	}
	if len(req.ProcessingStatus) > 0 && !contains(req.ProcessingStatus, "OTHER") {
		query += fmt.Sprintf(` AND ko.processing_status IN ('%s')`, strings.Join(req.ProcessingStatus, "','"))
	}
	if len(req.DeliveryStatus) > 0 && !contains(req.DeliveryStatus, "OTHER") {
		query += fmt.Sprintf(` AND ko.delivery_status IN ('%s')`, strings.Join(req.DeliveryStatus, "','"))
	}
	if len(req.NotDeliveryStatus) > 0 && !contains(req.NotDeliveryStatus, "OTHER") {
		query += fmt.Sprintf(` AND ko.delivery_status NOT IN ('%s')`, strings.Join(req.NotDeliveryStatus, "','"))
	}

	if len(req.Seller) > 0 {
		query += fmt.Sprintf(` AND ko.seller IN ('%s')`, strings.Join(req.Seller, "','"))
	}
	if (req.OrderAmount.Max > 0) && (req.OrderAmount.Min <= req.OrderAmount.Max) {
		query += `AND kop.amount between ` + fmt.Sprintf("%f", req.OrderAmount.Min) + ` AND ` + fmt.Sprintf("%f", req.OrderAmount.Max)
	}
	if req.RequestSource == "B2B_EXTERNAL" {
		query += ` AND kbr.order_confirmed is not null`
	}
	if req.RequestSource == "B2B_EXTERNAL" {
		query += fmt.Sprintf(` ORDER BY kbr.order_confirmed DESC LIMIT %d OFFSET %d`, req.Limit, req.Offset)
	} else {
		query += fmt.Sprintf(` ORDER BY ko.created_at DESC LIMIT %d OFFSET %d`, req.Limit, req.Offset)
	}

	var countQuery string
	countQuery = fmt.Sprintf(`
		SELECT
			CASE 
				WHEN ko.processing_status in ('EXCEPTION', 'SHIPMENT_MANIFESTED') then ko.processing_status
				WHEN ko.delivery_status in ('RTO_DELIVERED', 'RTO_IN_TRANSIT', 'LOST', 'RTO_RECEIVED') then ko.delivery_status
				WHEN ko.display_status IN ('CANCELLED', 'DELIVERED', 'PLACED', 'RETURNED', 'PENDING_CONFIRMATION', 'IN_TRANSIT', 'CONFIRMED', 'SHIPMENT_CREATED')
				THEN ko.display_status
				ELSE 'OTHER'
			END AS query_status,
			COUNT(*) AS count
		FROM kiranabazar_orders ko
		JOIN kiranabazar_order_details kod ON ko.id = kod.order_id
		LEFT JOIN kiranabazar_call_status kcs ON ko.id = kcs.order_id
		LEFT JOIN kc_bazar_reconciliation kbr ON ko.id = kbr.order_id
	`)

	if (req.OrderAmount.Max > 0) && (req.OrderAmount.Min <= req.OrderAmount.Max) {
		countQuery += ` LEFT JOIN kiranabazar_order_payments kop ON ko.id = kop.order_id`
	}

	if req.UserID != "" {
		countQuery = fmt.Sprintf(`%s WHERE (ko.user_id = '%s' OR kod.customer_phone = '%s' OR kod.order_id = '%s')`, countQuery, req.UserID, req.UserID, req.UserID)
	} else if len(req.NotDeliveryStatus) > 0 {
		countQuery = fmt.Sprintf(`%s WHERE 1=1`, countQuery)
	} else if len(req.RecoStatus) > 0 {
		countQuery = fmt.Sprintf(`%s WHERE 1=1`, countQuery)
		combinedConditions := getCombinedConditions(req.RecoStatus, "count")
		if combinedConditions != "" {
			countQuery = fmt.Sprintf(`%s AND (%s)`, countQuery, combinedConditions)
		}
	} else {
		if req.RequestSource == "B2B_EXTERNAL" {
			countQuery = fmt.Sprintf(`%s WHERE kbr.order_confirmed BETWEEN '%d' AND '%d'`, countQuery, req.From, req.To)
		} else {
			countQuery = fmt.Sprintf(`%s WHERE ko.created_at BETWEEN '%s' AND '%s'`, countQuery, fromDate, toDate)
		}
	}

	if len(req.Seller) > 0 {
		countQuery += fmt.Sprintf(` AND ko.seller IN ('%s')`, strings.Join(req.Seller, "','"))
	}

	if len(req.NotDeliveryStatus) > 0 && !contains(req.NotDeliveryStatus, "OTHER") {
		countQuery += fmt.Sprintf(` AND ko.delivery_status NOT IN ('%s')`, strings.Join(req.NotDeliveryStatus, "','"))
	}

	if (req.OrderAmount.Max > 0) && (req.OrderAmount.Min <= req.OrderAmount.Max) {
		countQuery += ` AND kop.amount between ` + fmt.Sprintf("%f", req.OrderAmount.Min) + ` AND ` + fmt.Sprintf("%f", req.OrderAmount.Max) + ` `
	}

	if req.RequestSource == "B2B_EXTERNAL" {
		countQuery += ` AND kbr.order_confirmed is not null`
	}

	countQuery += `
		GROUP BY
			CASE 
				WHEN ko.processing_status in ('EXCEPTION', 'SHIPMENT_MANIFESTED') THEN ko.processing_status
				WHEN ko.delivery_status IN ('RTO_DELIVERED', 'RTO_IN_TRANSIT', 'LOST', 'RTO_RECEIVED') THEN ko.delivery_status
				WHEN ko.display_status IN ('CANCELLED', 'DELIVERED', 'PLACED', 'RETURNED', 'PENDING_CONFIRMATION', 'IN_TRANSIT', 'CONFIRMED', 'SHIPMENT_CREATED')
				THEN ko.display_status
				ELSE 'OTHER'
			END
		ORDER BY
			CASE
				WHEN query_status = 'OTHER' THEN 1
				ELSE 0
			END,
			query_status;
	`

	orders := []dto.GetOrdersQueryResponse{}
	_, err = s.repository.CustomQuery(&orders, query)
	if err != nil {
		return nil, err
	}

	type OrderCounts struct {
		QueryStatus string `db:"query_status"`
		Count       int    `db:"count"`
	}
	counts := []OrderCounts{}
	_, err = s.repository.CustomQuery(&counts, countQuery)
	if err != nil {
		return nil, err
	}

	stats := make(map[string]int)
	for _, cc := range counts {
		if len(req.RecoStatus) > 0 {
			if contains(RECO_STATUS, cc.QueryStatus) {
				stats[cc.QueryStatus] = cc.Count
			} else {
				stats[cc.QueryStatus] = 0
			}
		} else {
			stats[cc.QueryStatus] = cc.Count
		}
	}
	// adding returned number as returned + rto_delivered + rto_intransit
	_, ok := stats[displaystatus.RETURNED]
	if !ok {
		stats[displaystatus.RETURNED] = 0
	}
	_, ok = stats[shipmentstatus.RTO_DELIVERED]
	if ok {
		stats[displaystatus.RETURNED] += stats[shipmentstatus.RTO_DELIVERED]
	}
	_, ok = stats[shipmentstatus.RTO_IN_TRANSIT]
	if ok {
		stats[displaystatus.RETURNED] += stats[shipmentstatus.RTO_IN_TRANSIT]
	}
	_, ok = stats[shipmentstatus.LOST]
	if ok {
		stats[displaystatus.RETURNED] += stats[shipmentstatus.LOST]
	}
	_, ok = stats[shipmentstatus.RTO_RECEIVED]
	if ok {
		stats[displaystatus.RETURNED] += stats[shipmentstatus.RTO_RECEIVED]
	}

	resp := dto.GetOrdersResponse{
		Stats: stats,
	}

	if len(orders) > 0 {
		var orderDetails []dto.OrderDetail
		for _, order := range orders {
			// Fetch user data from your user service
			// user = getUserDataFromUserID(order.UserID)

			var orderDetailsMap map[string]interface{}
			if err := json.Unmarshal(order.OrderDetails, &orderDetailsMap); err != nil {
				log.Println(err)
				continue
			}

			shippingAddress := orderDetailsMap["shipping_address"].(map[string]interface{})

			var paidAmountProof []string = []string{}
			var advanceTaken bool
			if order.PaymentMeta.Valid {
				var paymentMeta map[string]interface{}
				if err := json.Unmarshal([]byte(order.PaymentMeta.String), &paymentMeta); err == nil {
					if proof, ok := paymentMeta["paid_amount_proof"].([]interface{}); ok {
						for _, p := range proof {
							if proofMap, ok := p.(map[string]interface{}); ok {
								// Extract the string value from the map, assuming the key is "proof"
								if proofValue, ok := proofMap["url"].(string); ok {
									paidAmountProof = append(paidAmountProof, proofValue)
								}
							} else if proofValue, ok := p.(string); ok {
								// Handle the case where p is directly a string
								paidAmountProof = append(paidAmountProof, proofValue)
							}
						}
					}
					if taken, ok := paymentMeta["advance_taken"].(bool); ok {
						advanceTaken = taken
					}
				}
			}
			var orderUpdatedAt string
			if order.UpdatedAt == 0 {
				orderUpdatedAt = ""
			} else {
				orderUpdatedAt = time.UnixMilli(int64(order.UpdatedAt)).Format("2006-01-02T15:04:05.000Z")
			}
			alternatePhone := ""
			val, ok := shippingAddress["alternate_phone"].(string)
			if ok {
				alternatePhone = val
			}

			var packageDetails *shared.PackageDetails
			pushToOMS := false
			if req.Seller[0] == utils.KIRANACLUB_LOYALTY_REWARDS && (includes([]string{"<EMAIL>", "<EMAIL>"}, req.Email)) {
				pushToOMS, packageDetails, err = parseOrderForLoyaltyRewards(order.OrderDetails)
				if err != nil {
					return
				}
				if !pushToOMS {
					continue
				}

			} else if req.Seller[0] == utils.KIRANACLUB_LOYALTY_REWARDS && (includes([]string{"<EMAIL>", "<EMAIL>"}, req.Email)) {
				pushToOMS, packageDetails, err = parseOrderForLoyaltyRewards(order.OrderDetails)
				if err != nil {
					return
				}

				if pushToOMS {
					continue
				}
			} else if req.Seller[0] == utils.KIRANACLUB_LOYALTY_REWARDS {
				pushToOMS, packageDetails, err = parseOrderForLoyaltyRewards(order.OrderDetails)
				if err != nil {
					return
				}
			} else if slices.Contains(brands.GetKcFullFilledBrands(), order.Seller) {
				pushToOMS = true
			}
			orderDetails = append(orderDetails, dto.OrderDetail{
				ID: order.ID,
				User: dto.User{
					ID:       order.UserID,
					District: order.GeoDistrict,
					// Name:         userDetails.Name,
					// ShopName:     userDetails.ShopName,
					// Cluster:      userDetails.Cluster,
					// State:        userDetails.State,
					// Level:        userInfo.UserIncentives.Level,
					// Phone:        userInfo.PhoneNumber,
					// ProfileImage: userInfo.AuthUser.PhotoURL,
				},
				AddressName:          shippingAddress["name"].(string),
				AddressCity:          shippingAddress["district"].(string),
				AddressLine:          shippingAddress["line"].(string),
				PostalCode:           shippingAddress["postal_code"].(string),
				State:                shippingAddress["state"].(string),
				GST:                  shippingAddress["gst"].(string),
				AddressID:            shippingAddress["id"].(float64),
				StoreName:            shippingAddress["store_name"].(string),
				HouseNumber:          shippingAddress["house_number"].(string),
				Neighbourhood:        shippingAddress["neighbourhood"].(string),
				Village:              shippingAddress["village"].(string),
				Landmark:             shippingAddress["landmark"].(string),
				AddressTag:           shippingAddress["tag"].(string),
				AddressLine1:         shippingAddress["line1"].(string),
				AddressLine2:         shippingAddress["line2"].(string),
				Phone:                shippingAddress["phone"].(string),
				AlternatePhone:       alternatePhone,
				Total:                orderDetailsMap["total_amount"].(float64),
				OrderID:              order.OrderID,
				OrderDate:            order.CreatedAt.Format("2006-01-02T15:04:05.000Z"),
				OrderStatus:          order.OrderStatus,
				PaymentStatus:        order.PaymentStatus,
				DeliveryStatus:       order.DeliveryStatus,
				DisplayStatus:        order.DisplayStatus,
				ProcessingStatus:     order.ProcessingStatus,
				Note:                 order.Note.String,
				CallStatus:           order.CallStatus.String,
				UpdatedBy:            order.UpdatedBy.String,
				UpdatedAt:            orderUpdatedAt,
				CancelReason:         order.CancelReason.String,
				ReturnedReason:       order.ReturnedReason.String,
				Seller:               order.Seller,
				PaidAmount:           order.PaidAmount,
				PaidAmountProof:      paidAmountProof,
				AdvanceTaken:         advanceTaken,
				OrderPlaced:          order.OrderPlaced,
				OrderConfirmed:       order.OrderConfirmed,
				OrderShipmentCreated: order.OrderShipmentCreated,
				OrderDelivered:       order.OrderDelivered,
				OrderCancelled:       order.OrderCancelled,
				OrderReturned:        order.OrderReturned,
				PushToOMS:            &pushToOMS,
				StatusDescription:    orderstatus.GetStatusDescription(order.OrderStatus),
				PrintingLabel:        order.PrintingLabel,
				Picklist:             order.Picklist,
				ExtInvoice:           order.ExtInvoice,
				ExtInvoiceNumber:     order.ExtInvoiceNumber,
				ExpectedReturnAt:     formatTimeStamp(order.ExpectedReturnAt),
				PackageDetails:       packageDetails,
			})

		}
		resp.Data = parseOrderData(orderDetails)
		return &resp, nil
	}

	return &resp, nil
}

func parseOrderForLoyaltyRewards(orderDetail []byte) (bool, *shared.PackageDetails, error) {
	orderCartDetails := dao.KiranaBazarOrderDetails{}
	err := json.Unmarshal(orderDetail, &orderCartDetails)
	if err != nil {
		return false, nil, err
	}

	if orderCartDetails.PackageDetails != nil && orderCartDetails.PackageDetails.Dimensions != nil && len(orderCartDetails.PackageDetails.Dimensions) > 0 && orderCartDetails.PackageDetails.Dimensions[0].Weight > 0 {
		return true, orderCartDetails.PackageDetails, nil
	}

	return false, nil, nil
}

func parseOrderData(orderDetails []dto.OrderDetail) (newOrderDetails []dto.OrderDetail) {
	for _, orderDetail := range orderDetails {
		newOrderDetail := orderDetail
		if orderDetail.DeliveryStatus == shipmentstatus.RTO_DELIVERED || orderDetail.DeliveryStatus == shipmentstatus.RTO_IN_TRANSIT {
			newOrderDetail.DisplayStatus = orderDetail.DeliveryStatus
		}
		if orderDetail.ProcessingStatus == processingstatus.EXCEPTION {
			newOrderDetail.DisplayStatus = orderDetail.ProcessingStatus
		}
		if orderDetail.ProcessingStatus == processingstatus.SHIPMENT_MANIFESTED {
			newOrderDetail.DisplayStatus = orderDetail.ProcessingStatus
		}
		if orderDetail.DeliveryStatus == shipmentstatus.RTO_RECEIVED {
			newOrderDetail.DisplayStatus = orderDetail.DeliveryStatus
		}
		newOrderDetails = append(newOrderDetails, newOrderDetail)
	}
	return
}

func (s *Service) GetOrdersPaymentDetails(ctx context.Context, request *dto.GetOrdersPaymentDetailsRequest) (interface{}, error) {
	seller := request.Data.Seller
	if seller == "" {
		return nil, fmt.Errorf("seller is required")
	}
	query := fmt.Sprintf(`
	SELECT 
		YEAR(CONVERT_TZ(kop.created_at, 'UTC', 'Asia/Kolkata')) AS year, 
		MONTH(CONVERT_TZ(kop.created_at, 'UTC', 'Asia/Kolkata')) AS month, 
		COUNT(DISTINCT kop.order_id) AS total_orders, 
		SUM(kop.paid_amount) AS total_paid_amount
	FROM kiranabazar_order_payments kop
	JOIN kiranabazar_orders ko ON ko.id = kop.order_id AND ko.seller = '%s'
	WHERE 
		(kop.status = 'PARTIALLY_PAID' OR kop.paid_amount > 0)
		AND ko.is_archived = false
		AND CONVERT_TZ(kop.created_at, 'UTC', 'Asia/Kolkata') >= 
        DATE_FORMAT(CONVERT_TZ(NOW(), 'UTC', 'Asia/Kolkata') - INTERVAL 2 MONTH, '%%Y-%%m-01')
	GROUP BY year, month
	ORDER BY year DESC, month DESC
	`, seller)

	var ordersPaymentDetails []dto.GetOrdersPaymentDetailsResponseData
	_, err := s.repository.CustomQuery(&ordersPaymentDetails, query)
	if err != nil {
		return nil, err
	}
	return &dto.GetOrdersPaymentDetailsResponse{
		Data: ordersPaymentDetails,
	}, nil
}
