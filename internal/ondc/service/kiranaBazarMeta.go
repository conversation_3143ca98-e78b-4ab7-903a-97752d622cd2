package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/cache"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/brands"
	ordervalue "kc/internal/ondc/service/orderBill/orderValue"
	userdetails "kc/internal/ondc/service/userDetails"
	"kc/internal/ondc/utils"
	"math/rand"
	"strings"
	"time"

	"github.com/Masterminds/semver"
	"github.com/mixpanel/mixpanel-go"
)

// Cache keys for different data types
const (
	widgetCountsCacheKey    = "widget_counts:%s"
	userCartCountCacheKey   = "user_cart_count:%s"
	userAppVersionCacheKey  = "user_app_version:%s"
	userGeoLocationCacheKey = "user_geo_location:%s"
	widgetResolverCacheKey  = "widget_resolver:%s:%s"
	userOrderCountCacheKey  = "user_order_count:%s"
)

func additionalBannersUpdater(seller, userId string, bannerImages []shared.BannerImageUrls, userAppVersion *semver.Version, userOverallOrderCount, userOverallConfirmedOrderCount int, userCohortNames []string, screenName string) []shared.BannerImageUrls {
	updatedBannerImages := make([]shared.BannerImageUrls, 0)
	if seller == utils.LOTS || seller == utils.KIRANA_CLUB {
		return bannerImages
	}

	// if seller == utils.APSARA_TEA {
	// 	updatedBannerImages = append(updatedBannerImages, utils.APSARA_OFFER_BOTTOMSHEET_BANNER)
	// }

	if seller == utils.UNDEFINED && includes(userCohortNames, utils.RSB_STOCKIST_USER_COHORT) {
		updatedBannerImages = append(updatedBannerImages, utils.RSB_UNDEFINED_PRODUCTS_BANNER)
		return updatedBannerImages
	}

	// if seller == utils.MILDEN {
	// 	updatedBannerImages = append(updatedBannerImages, utils.MILDEN_FREE_CANDY_BANNER)
	// }

	// if seller == utils.GO_DESI {
	// 	updatedBannerImages = append(updatedBannerImages, utils.GO_DESI_OFFER_BANNER)
	// }

	// if utils.FIRST_ORDER_FULL_CASHBACK_COHORT[userId] && userOverallOrderCount == 0 {
	// 	updatedBannerImages = append(updatedBannerImages, utils.FIRST_ORDER_FULL_CASHBACK_COHORT_BANNER)
	// }

	// if seller == utils.RSB_SUPER_STOCKIST {
	// 	// if screenName == "Seller_RSB_New_To_Ordering" {
	// 	// 	updatedBannerImages = append(updatedBannerImages, utils.RSB_APSARA_OFFER_BANNER_FOR_NEW_TO_RSB)
	// 	// } else if screenName == "Seller_RSB" {
	// 	// 	updatedBannerImages = append(updatedBannerImages, utils.RSB_APSARA_OFFER_BANNER_FOR_RSB)
	// 	// }

	// 	updatedBannerImages = append(updatedBannerImages, utils.RSB_LOOT_OFFER_BANNER)

	// 	// updatedBannerImages = append(updatedBannerImages, utils.RSB_FREE_BAG_OFFER)
	// 	updatedBannerImages = append(updatedBannerImages, utils.RSB_MARGIN_BANNER)
	// }

	// if seller == utils.SOOTHE {
	// 	updatedBannerImages = append(updatedBannerImages, utils.SOOTHE_OFFER_BANNER)
	// }

	if seller == utils.CHUK_DE {
		updatedBannerImages = append(updatedBannerImages, utils.CHUK_DE_OFFER_BANNER)
	}

	if seller == utils.MILDEN {
		updatedBannerImages = append(updatedBannerImages, utils.MILDEN_RUPEE_1_CANDY_BANNER)
	}

	if seller == utils.ZOFF_FOODS {
		if userOverallConfirmedOrderCount == 0 {
			updatedBannerImages = append(updatedBannerImages, utils.ZOFF_FOODS_NEVER_ORDERED_SPOON_BANNER)
		}
		updatedBannerImages = append(updatedBannerImages, utils.ZOFF_FOODS_ALREADY_ORDERED_SPOON_BANNER)
		// updatedBannerImages = append(updatedBannerImages, utils.ZOFF_HALDI_OFFER_BANNER)
		updatedBannerImages = append(updatedBannerImages, utils.ZOFF_THAILAND_TRIP_BANNER)
		if userOverallConfirmedOrderCount == 0 {
			updatedBannerImages = append(updatedBannerImages, utils.ZOFF_MASALE_NEVER_ORDERED_BANNER)
		}
		updatedBannerImages = append(updatedBannerImages, utils.ZOFF_MASALE_FREE_LADI_BANNER)
	}

	if seller == utils.APSARA_TEA {
		if userOverallConfirmedOrderCount == 0 {
			updatedBannerImages = append(updatedBannerImages, utils.APSARA_TEA_NEVER_ORDERED_BANNER)
		}
		updatedBannerImages = append(updatedBannerImages, utils.APSARA_FREE_TEA_BANNER)
	}

	if seller == utils.RSB_SUPER_STOCKIST {
		if includes(userCohortNames, "RSB Geography Cohort - Not Ordered") || userId == "Y98QKSttXzTjtjoSyl7jPO63KnT2" || userId == "b8JVr0PvfsNgy9YuKahDseEN4kn1" {
			updatedBannerImages = append(updatedBannerImages, utils.RSB_FREE_CROCKERY_SET)
		}

		if userOverallConfirmedOrderCount > 1 {
			updatedBannerImages = append(updatedBannerImages, utils.RSB_FREE_BAG_BANNER)
		}
	}

	loyaltyAppVersion, _ := semver.NewVersion("6.4.0")
	if !userAppVersion.GreaterThan(loyaltyAppVersion) {
		updatedBannerImages = append(updatedBannerImages, utils.UPDATE_APK_BANNER)
	}

	// if seller == utils.RSB_SUPER_STOCKIST && includes(userCohortNames, "RSB Geography Cohort - Not Ordered") {
	// 	updatedBannerImages = append(updatedBannerImages, utils.RSB_NO_ORDER_BANNER)
	// }

	// if includes([]string{
	// 	"b8JVr0PvfsNgy9YuKahDseEN4kn1",
	// 	"7iKxueICXmUVXAOoZaIH0Y6AANI3",
	// 	"wMn4a578ChXPCC34Vl2Qn7KZFHV2",
	// 	"XkOExufEktP25tJlsZ1Cr5jFpD93",
	// 	"zqkrZZNnK9hZ7h6rMGLwo5koWt13",
	// 	"LFgu0CMG4bZXxpjW5oHlX4zEtFf2",
	// 	"FmLRVNOGU6gTufiaNmgmr48xMMX2",
	// 	"k3nIvqWQmQXHKz0gI9QET8Dn3973",
	// 	"hUWFEnLsOyOO6eFlfZv6zQFSkqp2",
	// }, userId) {
	// 	updatedBannerImages = append(updatedBannerImages, utils.TESTING_BANNER_TYPE_48)
	// }

	updatedBannerImages = append(updatedBannerImages, bannerImages...)
	return updatedBannerImages
}

func GetUserActiveCartCount(userID string, sqlRepo *sqlRepo.Repository, count chan int) {
	cacheKey := fmt.Sprintf(userCartCountCacheKey, userID)

	// Try to get cached cart count
	cacheResult, err := cache.GetInstance().GetCachedData(
		context.Background(),
		cacheKey,
		func() (interface{}, error) {
			var cartCount int = 0
			query := fmt.Sprintf(`select count(*) from kiranaclubdb.kiranabazar_cart kc where user_id = "%s" and is_valid = true and json_length(kc.cart) > 0`, userID)
			_, err := sqlRepo.CustomQuery(&cartCount, query)
			if err != nil {
				fmt.Println("not able to fetch usercart count")
				return 0, err
			}
			return cartCount, nil
		},
		10*time.Minute, // Local cache for 10 minutes
		0,              // No caching in redis
	)

	if err != nil {
		fmt.Printf("Error getting cached cart count: %v\n", err)
		count <- 0
		return
	}

	cartCount, ok := cacheResult.Data.(int)
	if !ok {
		fmt.Printf("Invalid cache data type for cart count\n")
		count <- 0
		return
	}

	count <- cartCount
}

func GetSideDrawerData(ctx context.Context, seller string, source string, bannerImageUrls []shared.BannerImageUrls) (sideDrawerData dto.SideDrawer, err error) {
	sideDrawerData = dto.SideDrawer{}
	sideDrawerData.BannerImageUrls = bannerImageUrls

	brandDetails, exists := brands.GetBrandDetailsForSeller(seller)

	if !exists {
		err = fmt.Errorf("brand details not found for seller: %s", seller)
		return sideDrawerData, err
	}

	helpCenterForm := brandDetails["help_center_form"]
	bulkInquiryForm, exists := brandDetails["bulk_order_form"]
	home := dto.SideDrawerData{
		Cta: dto.Cta{
			Icon:              "home",
			Text:              "होम",
			MixpanelEventName: "Ordering Sidebar Home",
			Nav: &shared.Nav{
				Name:    "OrderingModule",
				NavType: "",
				Params: map[string]interface{}{
					"screen": "Products",
					"params": map[string]interface{}{
						"seller": seller,
						"source": source,
					},
				},
			},
		},
		IconSet: "AntDesign",
	}

	myOrders := dto.SideDrawerData{
		Cta: dto.Cta{
			Icon:              "shopping-cart",
			Text:              "मेरे ऑर्डर",
			MixpanelEventName: "Ordering Sidebar My Orders",
			Nav: &shared.Nav{
				Name:    "OrderingModule",
				NavType: "",
				Params: map[string]interface{}{
					"screen": "MyOrders",
					"params": map[string]interface{}{
						"seller": seller,
						"source": source,
					},
				},
			},
		},
		IconSet: "Entypo",
	}
	helpCenter := dto.SideDrawerData{
		Cta: dto.Cta{
			Icon:              "text-document-inverted",
			Text:              "सहायता फॉर्म",
			MixpanelEventName: "Ordering Sidebar Help Center",
			Nav: &shared.Nav{
				Name:    "WebViewOld",
				NavType: "Redirect to WebviewOld",
				Params: map[string]interface{}{
					"screenTitle": "",
					"showHeader":  false,
					"uri":         helpCenterForm,
				},
			},
		},
		IconSet: "Entypo",
	}

	sideDrawerData.List = append(sideDrawerData.List, home, myOrders, helpCenter)
	if exists {
		bulkOrder := dto.SideDrawerData{
			Cta: dto.Cta{
				Icon:              "info-with-circle",
				Text:              "थोक ऑर्डर जानकारी",
				MixpanelEventName: "Ordering Sidebar Bulk Order",
				Nav: &shared.Nav{
					Name:    "WebViewOld",
					NavType: "Redirect to WebviewOld",
					Params: map[string]interface{}{
						"screenTitle": "",
						"showHeader":  false,
						"uri":         bulkInquiryForm,
					},
				},
			},
			IconSet: "Entypo",
		}

		sideDrawerData.List = append(sideDrawerData.List, bulkOrder)
	}
	return sideDrawerData, nil
}

func GetUserOrderCount(userID string, sqlRepo *sqlRepo.Repository, count chan int) {
	cacheKey := fmt.Sprintf(userOrderCountCacheKey, userID)

	// Try to get cached order count
	cacheResult, err := cache.GetInstance().GetCachedData(
		context.Background(),
		cacheKey,
		func() (interface{}, error) {
			var orderCount int = 0
			_, err := sqlRepo.CustomQuery(&orderCount, fmt.Sprintf(`select count(*) from kiranaclubdb.kiranabazar_orders ko where user_id = '%s';`, userID))
			if err != nil {
				fmt.Println("not able to fetch user order count")
				return 0, err
			}
			return orderCount, nil
		},
		1*time.Minute,
		0, // not in redis
	)

	if err != nil {
		fmt.Printf("Error getting cached order count: %v\n", err)
		count <- 0
		return
	}

	orderCount, ok := cacheResult.Data.(int)
	if !ok {
		fmt.Printf("Invalid cache data type for order count\n")
		count <- 0
		return
	}

	count <- orderCount
}

func (s *Service) GetUserCartWidgetData(userId string, seller *string) []map[string]interface{} {
	if seller == nil {
		return nil
	}
	data, err := s.GetCartProducts(context.Background(), userId, seller)
	if err != nil {
		fmt.Printf("Error getting user cart widget data: %v\n", err)
		return nil
	}

	widgetList := make([]map[string]interface{}, 0)
	for _, product := range data {
		if product.Seller == utils.KIRANA_CLUB {
			continue // Skip Kirana Club products for the cart widget
		}
		// redisKey := fmt.Sprintf(`d2r:products3::?id=%s`, product.ID)
		// encodedKey := base64.StdEncoding.EncodeToString([]byte(redisKey))
		widgetList = append(widgetList, map[string]interface{}{
			"image_url": product.ImageURL,
			"nav": map[string]interface{}{
				"name":     "OrderingModule",
				"nav_type": "Redirect to Screen",
				"params": map[string]interface{}{
					"screen": "Products",
					"params": map[string]interface{}{
						"seller":     product.Seller,
						"source":     product.Seller,
						"product_id": product.ID,
					},
				},
			},
		})
	}

	if len(widgetList) < 1 {
		return nil
	}
	return widgetList
}

func GetOrderingProgressWidgetData(userID string, ch chan<- *dto.ProgressWidgetImageUrlResponse) {
	// ch <- nil
	// return

	reqObject := map[string]interface{}{
		"user_id": userID,
		"scheme":  "thiland_scheme",
	}

	apiResponse, statusCode, err := utils.CallExternalAPI(utils.PROGRESS_WIDGET_RESOLVER_API, "POST", reqObject, nil)
	if err != nil {
		ch <- nil
		return
	}
	if statusCode == nil || *statusCode != 200 {
		err = fmt.Errorf("error in fetching PROGRESS WIDGET DATA: %s", string(apiResponse))
		ch <- nil
		return
	}

	apiResponseData := dto.ProgressWidgetImageUrlResponse{}
	err = json.Unmarshal(apiResponse, &apiResponseData)
	if err != nil {
		ch <- nil
		return
	}
	ch <- &apiResponseData
}

var sudhanshuCount = 0
var sanketCount = 0
var nakshataCount = 0
var gauravCount = 0
var sumitCount = 0
var hrithikCount = 0

func replaceBannerForOrderCount(seller string, orderCount int, minOrderValueSellerData *ordervalue.MinOrderRule, bannerImageUrls []shared.BannerImageUrls) []shared.BannerImageUrls {
	if minOrderValueSellerData == nil {
		return bannerImageUrls
	}
	if minOrderValueSellerData.OrderNumber == orderCount && minOrderValueSellerData.BannerImageUrl != "" {
		val := minOrderValueSellerData.BannerImageUrl
		positionIndex := minOrderValueSellerData.PositionIndex
		bannerImageUrls[positionIndex].Url = val
	}
	return bannerImageUrls
}

func (s *Service) GetKiranaBazarMeta(ctx context.Context, req *dto.AppGetKiranaBazarMetaRequest) (response *dto.AppGetKiranaBazarMetaResponse, err error) {
	if req.UserID == "" || req.Meta.ScreenName == "" {
		err = fmt.Errorf("user_id and screen_name is required")
		return
	}

	widgetViewCountChannel := make(chan map[string]int)
	progressWidgetImageChannel := make(chan *dto.ProgressWidgetImageUrlResponse)
	userCartCountChannel := make(chan int, 1)
	activationCohortEligibilityChannel := make(chan *dto.ActivationCohortUserData, 1)

	s.GetAllWidgetCountsAsync(ctx, req.UserID, widgetViewCountChannel)

	requestAppVersion := strings.TrimSpace(req.Meta.AppVersion)

	userDetailsChannel := userdetails.AsyncFetchUserDetails(req.UserID, []string{userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS}, 1*time.Second)

	// this is important because version is not correct from react native client.
	appVersionFromRTDBResult, _ := s.GenericCache.GetCachedData(
		ctx,
		fmt.Sprintf(userAppVersionCacheKey, req.UserID),
		func() (interface{}, error) {
			var appVersion string
			err = s.FirebaseRepository.RtDb.NewRef(fmt.Sprintf(`/users/%s/version`, req.UserID)).Get(ctx, &appVersion)
			if err != nil {
				return nil, err
			}
			return appVersion, nil
		},
		20*time.Minute,
		time.Hour*2, // Cache for 2 hours
	)
	appVersionFromRTDB := appVersionFromRTDBResult.Data.(string)

	appVersion := requestAppVersion
	if !includes([]string{"6.5.0", "6.5.1", "6.5.2", "6.5.3", "6.5.4"}, appVersion) {
		appVersion = appVersionFromRTDB
	}
	// appVersion = "6.3.1"
	// appVersion := req.Meta.AppVersion

	if appVersion == "" {
		appVersion = "6.3.0"
	}

	userAppVersion, _ := semver.NewVersion(appVersion)
	paymentsKCApkAppVersion, _ := semver.NewVersion("6.4.1")

	go GetOrderingProgressWidgetData(req.UserID, progressWidgetImageChannel)
	go getActivationCohortUserData(ctx, req.UserID, utils.ZOFF_FOODS, s.GcpRedis, activationCohortEligibilityChannel)

	if true || appVersion == "" || userAppVersion.LessThan(paymentsKCApkAppVersion) {
		userCartCountChannel <- 0
	} else {
		go GetUserActiveCartCount(req.UserID, s.repository, userCartCountChannel)
	}

	metaScreenConfig := utils.META_SCREEN_CONFIG
	searchTexts := utils.SEARCH_GLOBAL_SUGGESTION_TEXTS
	screenName := req.Meta.ScreenName

	userDetails := <-userDetailsChannel
	userCohortNames := []string{}
	if userDetails.Data != nil && userDetails.Data.UserDynamicDetails != nil {
		userCohortNames = userDetails.Data.UserDynamicDetails.UserCohortNames
	}

	// this is for testing purpose to make life easy
	if req.UserID == "YkmCvjx1ItMQtXJwcwdhLD3nSbq1" {
		if sudhanshuCount%2 == 0 {
			userCohortNames = []string{utils.RSB_STOCKIST_USER_COHORT}
		} else {
			userCohortNames = []string{}
		}
		sudhanshuCount++
	}

	if req.UserID == "wMn4a578ChXPCC34Vl2Qn7KZFHV2" {
		if sanketCount%2 == 0 {
			userCohortNames = []string{utils.RSB_STOCKIST_USER_COHORT}
		} else {
			userCohortNames = []string{}
		}
		sanketCount++
	}

	if req.UserID == "7iKxueICXmUVXAOoZaIH0Y6AANI3" {
		if nakshataCount%2 == 0 {
			userCohortNames = []string{utils.RSB_STOCKIST_USER_COHORT}
		} else {
			userCohortNames = []string{}
		}
		nakshataCount++
	}

	if req.UserID == "XkOExufEktP25tJlsZ1Cr5jFpD93" {
		if gauravCount%2 == 0 {
			userCohortNames = []string{utils.RSB_STOCKIST_USER_COHORT}
		} else {
			userCohortNames = []string{}
		}
		gauravCount++
	}

	if req.UserID == "ukE01Qv0dMhYarpHNUqjDGffiep1" {
		if sumitCount%2 == 0 {
			userCohortNames = []string{utils.RSB_STOCKIST_USER_COHORT}
		} else {
			userCohortNames = []string{}
		}
		sumitCount++
	}

	if req.UserID == "k3nIvqWQmQXHKz0gI9QET8Dn3973" {
		if hrithikCount%2 == 0 {
			userCohortNames = []string{utils.RSB_STOCKIST_USER_COHORT}
		} else {
			userCohortNames = []string{}
		}
	}

	userOverallConfirmedOrderCount, err := ordervalue.GetUserLevelConfirmedOrderCount(req.UserID)
	if err != nil {
		return
	}
	screenName, err = resolveScreenNameAccordingToHierarchy(&resolveScreenName{cohortName: userCohortNames, userOverallConfirmedOrderCount: userOverallConfirmedOrderCount, UserId: req.UserID, MixPanelRepo: s.Mixpanel})
	if err != nil {
		return
	}

	screenDetails, err := s.GetScreenDetails(ctx, screenName, req.UserID)
	if err != nil {
		return
	}
	if screenDetails == nil {
		err = fmt.Errorf("screen details not found for screen: %s", screenName)
		return
	}

	userGeoLocationData, err := utils.GetUserGeolocationData(req.UserID)
	if err != nil {
		return
	}

	// pincode := userGeoLocationData.GeoPincode

	widgetsIndexesData := screenDetails.Widgets

	// Convert int64 to []float64
	widgetIndexes := make([]int64, 0)
	for _, widget := range widgetsIndexesData {
		if widget == 1438 && includes(userCohortNames, "RSB Rs.100 Coupon Cohort") {
			widget = 1518
		}
		if widget == 1479 && includes(userCohortNames, "RSB Rs.100 Coupon Cohort") {
			widget = 1519
		}
		widgetIndexes = append(widgetIndexes, widget)
	}

	headerConfigs := screenDetails.HeaderConfig.DynamicConfigs
	screenHeaderConfig, err := GetScreenHeaderConfigByVersion(headerConfigs, appVersion)
	if err != nil {
		return
	}

	header := screenHeaderConfig

	// if widgets, exists := utils.STATE_WIDGETS_MAP[userGeoLocationData.GeoState]; exists {
	// 	widgetIndexes = widgets
	// } else {
	// 	widgetIndexes = []float64{809, 832, 819, 820, 821, 710} // Default widgets
	// }

	// if userGeoLocationData.GeoState == "" {
	// 	widgetIndexes = []int64{1012,
	// 		1013,
	// 		1016,
	// 		875,
	// 		863,
	// 		1008,
	// 		1014,
	// 		873,
	// 	} // Default widgets
	// }

	apkVersionWithProductChanges, _ := semver.NewVersion("6.5.3")
	if userAppVersion.GreaterThan(apkVersionWithProductChanges) || userAppVersion.Equal(apkVersionWithProductChanges) {
		// widgetIndexes = append(widgetIndexes, []int64{1131, 1132, 1133}...)
	}

	// userOverallConfirmedOrderCount, err := ordervalue.GetUserLevelConfirmedOrderCount(req.UserID)
	// if err != nil {
	// 	return
	// }
	// loyaltyAppVersion, _ := semver.NewVersion("6.4.0")
	// if LOYALTY_LARGE_ORDERS_USERS_COHORT[req.UserID] && (userAppVersion.GreaterThan(loyaltyAppVersion) || userAppVersion.Equal(loyaltyAppVersion)) {
	// 	widgetIndexes[0] = 1032
	// } else if userOverallConfirmedOrderCount > 0 && (userAppVersion.GreaterThan(loyaltyAppVersion) || userAppVersion.Equal(loyaltyAppVersion)) {
	// 	widgetIndexes[0] = 1012
	// }

	// if req.UserID == "Y98QKSttXzTjtjoSyl7jPO63KnT2" ||
	// 	len(widgetIndexes) > 0 &&
	// 		!LOYALTY_DATA_7_MAY[req.UserID] &&
	// 		screenName != "Seller_New_To_Ordering" &&
	// 		screenName != "Seller_RSB_New_To_Ordering" &&
	// 		screenName != "Seller_RSB_New_To_Ordering_B" &&
	// 		!LOYALTY_LARGE_ORDERS_USERS_COHORT[req.UserID] {
	// 	widgetIndexes[0] = 1081
	// }

	if includes(userCohortNames, RSB_LOTS_SKU_COHORT) {
		temp := []int64{}
		for i := 0; i < len(widgetIndexes); i++ {
			if widgetIndexes[i] == 1183 {
				temp = append(temp, 1203, 1204)
			} else {
				temp = append(temp, widgetIndexes[i])
			}
		}
		widgetIndexes = temp
	}

	// if includes(userCohortNames, "RSB - B (Flash sale)") {
	// 	widgetIndexes[1] = 1168
	// }

	if widgetIndexes[0] == 1032 && !LOYALTY_LARGE_ORDERS_USERS_COHORT[req.UserID] {
		slack.SendSlackMessage(fmt.Sprintf("Added By Sitaram, Please Ignore\nUser %s is not in loyalty large orders cohort but widget index is 1032", req.UserID))
	}
	widgetViewsCount := <-widgetViewCountChannel

	if utils.UNDEFINED_SELLER_FIX_USERS[req.UserID] && getWidgetViewCountFromId("1236", widgetViewsCount) < 1 {
		widgetIndexes = []int64{1236}
	}

	// add hardcoded widget if request from kirana bazar website and flow id is free_cash_100 or samplebox_100
	if req.Meta.Platform == "website" {
		var websiteFlowWidget int64
		switch req.Meta.FlowID {
		case "free_cash_100":
			websiteFlowWidget = 1505
		case "sample_box":
			websiteFlowWidget = 1506
		}
		if websiteFlowWidget != 0 {
			if len(widgetIndexes) > 3 {
				// Insert websiteFlowWidget at index 2 (third position)
				widgetIndexes = append(widgetIndexes[:3], append([]int64{websiteFlowWidget}, widgetIndexes[3:]...)...)
			} else {
				// If widgetIndexes has 0, 1 or 2 elements, just append websiteFlowWidget at the end
				widgetIndexes = append(widgetIndexes, websiteFlowWidget)
			}
		}
	}

	// if includes([]string{"qvFtKgxk8YQrGBi9LEORJduW62l1", "XkOExufEktP25tJlsZ1Cr5jFpD93", "7iKxueICXmUVXAOoZaIH0Y6AANI3", "k3nIvqWQmQXHKz0gI9QET8Dn3973"}, req.UserID) {
	// 	widgetIndexes = append(widgetIndexes, 1133)
	// }

	// widgetIndexes = append(widgetIndexes, 1473, 1481)

	reqObject := dto.GetScreenDetailsRequest{
		Uid:             req.UserID,
		WidgetIndexes:   widgetIndexes,
		State:           userGeoLocationData.GeoState,
		District:        userGeoLocationData.GeoDistrict,
		GeoCodedCluster: userGeoLocationData.GeoCodedCluster,
		AppVersion:      appVersion,
	}

	apiResponse, statusCode, err := utils.CallExternalAPI(utils.WIDGET_RESOLVER_API, "POST", reqObject, nil) // TODO: add cache
	if err != nil {
		return
	}
	if statusCode == nil || *statusCode != 200 {
		err = fmt.Errorf("error in fetching screen details: %s", string(apiResponse))
		return
	}

	apiResponseData := dto.GetScreenDetailsResponse{}
	err = json.Unmarshal(apiResponse, &apiResponseData)
	if err != nil {
		return
	}

	widgets := []dto.Widget{}

	progressWidgetResponse := <-progressWidgetImageChannel

	// widgets = append(widgets, orderingProgressWidget[0])
	if req.UserID == "b8JVr0PvfsNgy9YuKahDseEN4kn1" {
		err = json.Unmarshal([]byte(utils.BrandsMetaWidgetsData), &widgets)
		if err != nil {
			return nil, err
		}
	}

	// get the all non nil widget from the api response
	filteredWidgets := []dto.Widget{}
	floatingWidgets := []map[string]interface{}{}

	var cartWidgetSeller *string
	if userCohortNames != nil && includes(userCohortNames, utils.RSB_STOCKIST_USER_COHORT) {
		rsbSeller := utils.RSB_SUPER_STOCKIST
		cartWidgetSeller = &rsbSeller
	}
	userCartWidgetItems := s.GetUserCartWidgetData(req.UserID, cartWidgetSeller)

	sellerLevelConfirmOrderCount, err := ordervalue.GetSellerLevelConfirmedOrderCount(req.UserID)
	if err != nil {
		return nil, err
	}

	for _, widget := range apiResponseData.Result {
		if widget != nil {
			widgetData, ok := widget.(map[string]interface{})
			if !ok {
				// Handle the error
				continue
			}
			// Check if the widget has a valid type
			widgetType, exists := widgetData["type"]
			if exists && widgetType != nil {
				widgetTypeValue, valid := widgetType.(float64)
				if valid && utils.FLOATING_WIDGET_TYPES[widgetTypeValue] {
					// If the widget type is a floating video widget, add it to the list
					floatingWidgets = append(floatingWidgets, widgetData)
				}
			}

			widgetId, exists := widgetData["id"]
			if !exists || widgetId == nil {
				fmt.Println("Widget ID is nil or does not exist, skipping this widget")
				continue
			}

			widgetIdValue, ok := widgetId.(float64)
			if !ok {
				fmt.Println("Widget ID is not a float64, skipping this widget")
				continue
			}
			if widgetIdValue == 1436 {
				if progressWidgetResponse != nil && progressWidgetResponse.WidgetUrl != nil && *progressWidgetResponse.WidgetUrl != "" {
					widgetData["image_url"] = *progressWidgetResponse.WidgetUrl
					widgetData["image_url"] = strings.Replace(fmt.Sprintf("%v", widgetData["image_url"]), "target_scheme", "target_scheme_v2", 1)
					if userCohortNames != nil && includes(userCohortNames, utils.RSB_STOCKIST_USER_COHORT) {
						widgetData["CTA"] = shared.Nav{
							Name:    "OrderingModule",
							NavType: "Redirect to Screen",
							Params: map[string]interface{}{
								"screen": "Products",
								"params": map[string]interface{}{
									"seller": utils.RSB_SUPER_STOCKIST,
									"source": fmt.Sprintf("third_party_%s", utils.RSB_SUPER_STOCKIST),
								},
							},
						}
					}
					filteredWidgets = append(filteredWidgets, widgetData)
				}
			} else if widgetIdValue == 1447 {
				if userCohortNames != nil && includes(userCohortNames, utils.RSB_STOCKIST_USER_COHORT) && userCartWidgetItems != nil {
					userCartWidget := utils.DeepCloneMap(TYPE_46_CART_WIDGET_RSB)
					userCartWidget["products"] = userCartWidgetItems
					userCartWidget["header"] = map[string]interface{}{
						"text":  fmt.Sprintf("%d आइटम आपके कार्ट में है", len(userCartWidgetItems)),
						"color": "#333333",
					}
					filteredWidgets = append(filteredWidgets, userCartWidget)
				}
			} else if widgetIdValue == 1502 && includes(userCohortNames, utils.RSB_STOCKIST_USER_COHORT) {
				orderCount := sellerLevelConfirmOrderCount[utils.RSB_SUPER_STOCKIST]
				if orderCount == 0 {
					widgetData["image_url"] = "https://d2rstorage2.blob.core.windows.net/widget/July/17/5da9adcd-a4ff-481d-9162-6d1ecd79771b/1752759254045.webp"
				} else if orderCount == 1 {
					widgetData["image_url"] = "https://d2rstorage2.blob.core.windows.net/widget/July/17/ea39e145-c4e6-4832-98d1-bd070b45e401/1752759086792.webp"
				} else {
					widgetData["image_url"] = "https://d2rstorage2.blob.core.windows.net/widget/July/17/c35b149c-0632-458f-8e7e-5176e1ca044d/1752759062100.webp"
				}
				filteredWidgets = append(filteredWidgets, widgetData)
			} else {
				filteredWidgets = append(filteredWidgets, widget)
			}
		}
	}

	if len(filteredWidgets) > 0 {
		searchWidget := filteredWidgets[0]
		widgets = filteredWidgets[1:]
		header.Widget = &searchWidget
	}

	// widgets = append(widgets, REVIEW_TEST_WIDGET)
	// if len(widgets) > 2 {
	// 	widgets = append(widgets, widgets[2])
	// }

	sellersMeta := utils.DeepCloneMap(brands.GetBrandsMeta())

	var reactivationCohortBannerWidget *shared.BannerImageUrls
	activationCohortUserData := <-activationCohortEligibilityChannel
	if activationCohortUserData != nil && activationCohortUserData.Eligible {
		// get the respective coupon banner widget from map first check
		value, ok := ACTIVATION_COHORT_COUPON_BANNER_MAP[activationCohortUserData.CouponId]
		if ok {
			reactivationCohortBannerWidget = &value
		}
	}

	activationCohortBannerWidget := shared.BannerImageUrls{}
	err = json.Unmarshal([]byte(utils.ACTIVATION_COHORT_BANNER_WIDGET), &activationCohortBannerWidget)
	if err != nil {
		return nil, err
	}

	userOverallOrderCount, err := ordervalue.GetUserLevelOrderCount(req.UserID)
	if err != nil {
		return nil, err
	}

	showOrderingOnboarding := userOverallOrderCount == 0

	for key, seller := range sellersMeta {
		data := []dao.KiranaBazarCategory{}

		sllr, exists := brands.GetSellerBySource(seller.Source)

		if !exists {
			err = fmt.Errorf("seller not found for source: %s", seller.Source)
			return nil, err
		}

		kcSeller := sllr

		cacheKey := fmt.Sprintf("kiranabazar_categories:%s", seller.Source)
		cacheResult, cacheErr := s.GenericCache.GetCachedData(
			ctx,
			cacheKey,
			func() (interface{}, error) {
				query := fmt.Sprintf(`
					SELECT DISTINCT kc.*
					FROM kiranabazar_categories kc 
					JOIN kiranabazar_entities_mapping kem ON kc.id = kem.entity_id
					JOIN kiranabazar_products kp ON kem.target_id = kp.id
					WHERE kc.source = '%s' 
					AND kp.is_active = true and kc.is_active = true and kem.is_active = true
					ORDER BY `+"`rank`"+` ASC;
				`, seller.Source)
				var data []dao.KiranaBazarCategory
				_, dbErr := s.repository.CustomQuery(&data, query)
				if dbErr != nil {
					return nil, dbErr
				}
				return data, nil
			},
			10*time.Minute, // Local cache for 10 minutes
			0,              // not storing Redis cache
		)
		if cacheErr != nil {
			return nil, cacheErr
		}
		data, ok := cacheResult.Data.([]dao.KiranaBazarCategory)
		if !ok {
			return nil, fmt.Errorf("invalid cache data type for kiranabazar_categories")
		}
		orderCount := sellerLevelConfirmOrderCount[kcSeller]
		minOrderValueSellerData, _ := ordervalue.GetMinOrderValueData(kcSeller, orderCount+1, userCohortNames)
		topProductCategory := dto.SearchCategory{
			ImageURL: "https://d2rstorage2.blob.core.windows.net/widget/May/12/5f7889a7-8e6f-477c-89d4-1152db2a00ca/1747056128281.webp",
			Label:    "टॉप प्रोडक्ट्स",
			Value:    "टॉप प्रोडक्ट्स",
			BgColor:  "#E6E6E6",
		}
		if kcSeller == utils.UNDEFINED {
			seller.Categories = append(seller.Categories, dto.SearchCategory{
				ImageURL: "https://d2rstorage2.blob.core.windows.net/widget/May/12/5f7889a7-8e6f-477c-89d4-1152db2a00ca/1747056128281.webp",
				Label:    "बिजली ऑफर्स",
				Value:    "बिजली ऑफर्स",
				BgColor:  "#E6E6E6",
			})
		} else {
			seller.Categories = append(seller.Categories, topProductCategory)
		}
		for _, v := range data {
			categoryID := fmt.Sprint(v.ID)
			seller.Categories = append(seller.Categories, dto.SearchCategory{
				ImageURL: v.ImageURL,
				Label:    v.Category,
				Value:    v.Category,
				ID:       &categoryID,
				BgColor:  "#FFECE5",
			})
		}

		if reactivationCohortBannerWidget != nil && kcSeller == utils.ZOFF_FOODS {
			// prepend the banner widget
			seller.BannerImageUrls = append([]shared.BannerImageUrls{*reactivationCohortBannerWidget}, seller.BannerImageUrls...)
		}

		seller.BannerImageUrls = replaceBannerForOrderCount(kcSeller, orderCount+1, minOrderValueSellerData, seller.BannerImageUrls)

		// april scheme banner along with version check and A/B for specific sellers
		seller.BannerImageUrls = additionalBannersUpdater(kcSeller, req.UserID, seller.BannerImageUrls, userAppVersion, userOverallOrderCount, userOverallConfirmedOrderCount, userCohortNames, screenName)
		height := 140
		seller.CarouselHeight = &height

		sideDrawerData, err := GetSideDrawerData(ctx, kcSeller, seller.Source, seller.BannerImageUrls)
		if err != nil {
			return nil, err
		}
		seller.SideDrawer = &sideDrawerData

		bannersCarousel := make(map[string]interface{})
		err = json.Unmarshal([]byte(CAROUSEL_WIDGET), &bannersCarousel)
		if err == nil {
			seller.BannersCarousel = bannersCarousel
		}

		if true || (req.UserID == "XkOExufEktP25tJlsZ1Cr5jFpD93" && sllr == utils.ZOFF_FOODS) {
			seller.BannersCarousel = nil
			seller.CarouselHeight = nil
		}

		sellersMeta[key] = seller

		searchAppVersion, _ := semver.NewVersion("6.4.0")
		if kcSeller == utils.LOTS && (userAppVersion.GreaterThan(searchAppVersion) || userAppVersion.Equal(searchAppVersion)) {
			seller.BannerImageUrls[0].Nav = &shared.Nav{
				Name:    "OrderingSearch",
				NavType: "Redirect to Screen",
				Params: map[string]interface{}{
					"seller": utils.LOTS,
					"source": "third_party_lots",
					"screen": "Products",
					"params": map[string]interface{}{
						"seller": utils.LOTS,
						"source": "third_party_lots",
					},
				},
			}
		}

		// topBanner := dto.TopNudge{
		// 	Text:    "1200 किराना दुकानदारों ने अभी तक ख़रीदा",
		// 	BgColor: "#E5EFFF",
		// 	Color:   "#023588",
		// }

		// primaryCta := dto.Cta{
		// 	Text:              "ब्रांड पोस्ट",
		// 	PrimaryColor:      utils.StrPtr("#D45339"),
		// 	SecondaryColor:    utils.StrPtr("#FFFFFF"),
		// 	MixpanelEventName: "widget_type_37_brand_post_cta",
		// 	Nav: &dto.Nav{
		// 		Name:    "BrandProfile",
		// 		NavType: "Redirect to Brand Profile",
		// 		Params: map[string]interface{}{
		// 			"brandId":     seller.BrandID,
		// 			"screen_name": seller.Screen,
		// 		},
		// 	},
		// }

		// secondaryCta := dto.Cta{
		// 	Text:              "ऑर्डर करें",
		// 	MixpanelEventName: "widget_type_37_order_cta",
		// 	PrimaryColor:      utils.StrPtr("#FFFFFF"),
		// 	SecondaryColor:    utils.StrPtr("#D45339"),
		// 	Nav: &dto.Nav{
		// 		Name:    "OrderingModule",
		// 		NavType: "Redirect to Screen",
		// 		Params: map[string]interface{}{
		// 			"screen": "Products",
		// 			"seller": seller.Seller,
		// 			"source": seller.Source,
		// 			"params": map[string]interface{}{
		// 				"seller": seller.Seller,
		// 				"source": seller.Source,
		// 			},
		// 		},
		// 	},
		// }

		// mixpanelEventObject := map[string]interface{}{
		// 	"seller":   seller.Seller,
		// 	"source":   seller.Source,
		// 	"brand_id": seller.BrandID,
		// }

		// followers := utils.BRAND_DETAILS[seller.Seller]["followers"]
		// profileImage := utils.BRAND_DETAILS[seller.Seller]["profile_image"]

		// widgetType37 := dto.WidgetType37{
		// 	PrimaryCta:        &primaryCta,
		// 	SecondaryCta:      &secondaryCta,
		// 	TopNudge:          &topBanner,
		// 	MixPanelEventName: fmt.Sprintf("widget_type_37 %s", seller.Seller),
		// 	ID:                2484,
		// 	CreatedBy:         "BACKEND",
		// 	Expiry:            0,
		// 	ExpiryTime:        0,
		// 	Type:              37,
		// 	WidgetInfo: dto.WidgetInfo{
		// 		WidgetName: "widget_type_37",
		// 	},
		// 	BannerImageUrls: seller.BannerImageUrls,
		// 	IsActive:        1,
		// 	UpdatedAt:       "2021-12-03T12:00:00Z",
		// 	VisibleFrom:     1638528000,
		// 	ProfileImage:    profileImage,
		// 	Heading:         seller.Name,
		// 	Icon: map[string]interface{}{
		// 		"type":  "MaterialIcons",
		// 		"name":  "verified",
		// 		"size":  22,
		// 		"color": "#2C99FF",
		// 	},
		// 	SubHeading:          fmt.Sprintf("%s फॉलोवर्स", followers),
		// 	Description:         "मसाले, मेवे आदि पर 50% तक मार्जिन, मसाले, मेवे आदि पर 50% तक मार्जिन, मसाले, मेवे आदि पर 50% तक मार्जिन",
		// 	MixpanelEventObject: mixpanelEventObject,
		// }

		// if seller.Seller == "zoff_foods" {
		// 	widgetType37.BannerImageUrls = []dto.BannerImageUrls{
		// 		{
		// 			MixpanelEventName: "widget_type_37_banner_image",
		// 			Url:               seller.BannerImageUrls[0].Url,
		// 		},
		// 	}
		// }

		// widgets = append(widgets, widgetType37)
	}

	// TODO: @sitaram remove this and fetch this widget from seller screen
	// playStoreCtaAppVersion, _ := semver.NewVersion("6.4.0")
	// if userAppVersion.LessThan(playStoreCtaAppVersion) {
	// 	err = json.Unmarshal([]byte(utils.BrandsMetaHeaderDataLT6_4_0), &header)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// } else {
	// 	err = json.Unmarshal([]byte(utils.BrandsMetaHeaderDataGTE6_4_0), &header)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// }

	// sideDrawer := dto.SideDrawer{}
	// err = json.Unmarshal([]byte(utils.BrandsMetaSideDrawerData), &sideDrawer)
	// if err != nil {
	// 	return nil, err
	// }

	// bsNavObject := dto.Nav{}
	// err = json.Unmarshal([]byte(utils.BS_NAV_OBJECT), &bsNavObject)
	// if err != nil {
	// 	return nil, err
	// }

	// removing null widgets from an array

	// video := getNewsWidget()
	// if video != nil {
	// 	// adding loyalty kya he video at 3rd position
	// 	widgets = append(widgets, nil)   // Extend slice by one
	// 	copy(widgets[2+1:], widgets[2:]) // Shift elements right
	// 	widgets[2] = video               // Insert new widget
	// }
	// _, exists := rewardUsers[req.UserID]
	// if exists {
	// 	widgets = append(widgets, nil)       // Extend slice by one
	// 	copy(widgets[4+1:], widgets[4:])     // Shift elements right
	// 	widgets[4] = getSchemeRewardUpdate() // Insert new widget
	// }

	if userAppVersion.GreaterThan(paymentsKCApkAppVersion) || userAppVersion.Equal(paymentsKCApkAppVersion) {
		header.CtaSecondary = nil
	}

	userActiveCartCount := <-userCartCountChannel
	orderingBottomTabs := populateOrderingBottomTabs(userActiveCartCount, req.UserID, screenName, userCohortNames)

	if includes(userCohortNames, "Order Never Placed User") && getWidgetViewCountFromId("1134", widgetViewsCount) < 2 {

		widgetOptions := [][]map[string]interface{}{
			SELLER_SCREEN_FLOATING_VIDEO_WIDGET_1,
			SELLER_SCREEN_FLOATING_VIDEO_WIDGET_2,
			SELLER_SCREEN_FLOATING_VIDEO_WIDGET_3,
		}

		randomIndex := rand.Intn(len(widgetOptions))

		floatingWidgets = append(floatingWidgets, widgetOptions[randomIndex]...)
		s.IncrementWidgetViewCount(ctx, req.UserID, "1134", nil)
	}

	type44WidgetResp := map[string]interface{}{}
	type46WidgetResp := map[string]interface{}{}

	// type43Resp := []map[string]interface{}{}
	// err = json.Unmarshal([]byte(HRDCODED_WIDGET), &type43Resp)
	// if err != nil {
	// 	return nil, fmt.Errorf("error unmarshalling HRDCODED_WIDGET: %w", err)
	// }

	err = json.Unmarshal([]byte(utils.TYPE_44_WIDGET), &type44WidgetResp)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling TYPE_44_WIDGET: %w", err)
	}

	err = json.Unmarshal([]byte(utils.TYPE_46_WIDGET), &type46WidgetResp)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling TYPE_46_WIDGET: %w", err)
	}

	eventObject := map[string]interface{}{
		"distinct_id":   req.UserID,
		"screen_type":   screenName,
		"screen_name":   "Seller",
		"api_timestamp": time.Now().UnixMilli(),
	}

	if req.Meta.Platform != "website" {
		s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
			s.Mixpanel.NewEvent("Screen View BE", req.UserID, eventObject),
		})
	}

	// widgets = append(widgets, type44WidgetResp)
	// widgets = append(widgets, type46WidgetResp)
	// widgets = append(widgets, type43Resp[0])

	var syncCartSeller *dto.SyncCartSellerData
	if includes(userCohortNames, utils.RSB_STOCKIST_USER_COHORT) {
		syncCartSeller = &dto.SyncCartSellerData{
			Seller: utils.RSB_SUPER_STOCKIST,
			Source: fmt.Sprintf("third_party_%s", utils.RSB_SUPER_STOCKIST),
		}
	}

	data := dto.AppGetKiranaBazarMetaData{
		Widgets:            widgets,
		Header:             header,
		SellersMeta:        sellersMeta,
		Config:             metaScreenConfig,
		SearchTexts:        searchTexts,
		OrderingBottomTabs: orderingBottomTabs,
		FloatingData:       floatingWidgets,
		SyncCartSeller:     syncCartSeller,
	}
	if showOrderingOnboarding {
		data.ShowOrderingOnboarding = &showOrderingOnboarding
	}
	response = &dto.AppGetKiranaBazarMetaResponse{
		Data: data,
		// NavObj: &bsNavObject,
	}
	return
}

const CAROUSEL_WIDGET string = `
{"MixPanelEventName":"1 takka Winner Carousel","components":[{"MixPanelEventName":"W1","image_url":"https://storage.googleapis.com/op-d2r.appspot.com/widgets/October/12/a3d08905-d673-4764-9303-7654fa2c2df1/Takka%20carousel1.png?GoogleAccessId=firebase-adminsdk-p1h6s%40op-d2r.iam.gserviceaccount.com&Expires=***********&Signature=f47ygJ%2FFEi4vdqFe%2Bk9LWTRzvJLNgRkAN%2FBvBmieG7v539EcYog5c%2BCgwGfGVe6CsngaPzhG88s0vnNf21l1mpdUaHWyJ8IZ%2FrkQeTUWNcBX05%2Fh5L0y1BMtAmrPTipBVfVpoNNTV8U1z8922M0SXVQYfX6wAO%2FdusyWTFw3qwGatcB0LAoQ%2Bz4emg87AZ8gJG4chAdYn0Fm1O3ALvUQED22aMKL2ypFRoFKy26W3gn5kZWDE4vWBCRO2bWaXrPlImLLWEuQF%2FVbSPN8cntd0Ju5YwTCo8i6l8GEJf1fuuwgBbE%2BOTdtOO%2FqdhoSDGLExWZK%2BMMjsuxmemkVBiho3Q%3D%3D","nav":{"name":"WebView","nav_type":"Redirect to Webview","params":{"screenTitle":"1 टक्का स्कीम 🤩","showHeader":true,"uri":"https://webapps.retailpulse.ai/billUpload?campaign=Cash_Reward"}}},{"MixPanelEventName":"W2","image_url":"https://storage.googleapis.com/op-d2r.appspot.com/widgets/October/12/b4dad24e-a497-474a-9c9b-dffe0fe474ee/Takka%20carousel2.png?GoogleAccessId=firebase-adminsdk-p1h6s%40op-d2r.iam.gserviceaccount.com&Expires=***********&Signature=DKiEme4F5WewiMVDBAKvNptd%2B7You08qwdK%2BsLuEkLozuhhyk34U5tOdKj9CoPgE5xZ2nArUv0spYmAZwXe0kZSlavLF0Anl63yY67uB8Vhou%2FqKXIjUmdVjmWZwOHOcRafDsVVY%2BMWNrChPtCS6HDH7poGUaF6SByNWgsszUQmlDftCpSSlKARE9O3t4pkLm0VgXg%2Fgx%2BujIF763Hn2KBtZdfzdiuKqDl3MhTNHmuVJWmH427S3pJg%2FsxSwm3MHk5DlALz0BKDMguI2e5qkOslaeb%2FRrTtrecM1PMsY1oV%2FD0JVyP1saGFyRVgUWMCb5jdQpEdIhjJNmwdjJ7YwuA%3D%3D","nav":{"name":"WebView","nav_type":"Redirect to Webview","params":{"screenTitle":"1 टक्का स्कीम 🤩","showHeader":true,"uri":"https://webapps.retailpulse.ai/billUpload?campaign=Cash_Reward"}}},{"MixPanelEventName":"W3","image_url":"https://storage.googleapis.com/op-d2r.appspot.com/widgets/October/12/84842af6-bb8e-4b55-a7a0-61b928006c39/Takka%20carousel3.png?GoogleAccessId=firebase-adminsdk-p1h6s%40op-d2r.iam.gserviceaccount.com&Expires=***********&Signature=g79mc8Fl%2F2ItVE94VIMI1uqsj98rPKyt2pNniPP57v8z%2ByJbgHSTwgyFTgdBE6FBR1vXlrw0mWy5s6nNpHuAHsSsq4430HDngAY%2BCOMNUDZw1TLTt4pj4jPZTUYyzbX1IsRYVZ%2BJk1pmzCh5mVw9nBMH8Xh%2BRiuoItXs%2BzBAbBTkq%2FVP6BvW%2FSRt9YdNRZW27%2FQ29R3CEMQbcoAB2rOQarYYWViFBwM0qTJVFWwGEPgHshYX1qDyjWm%2FvkAhuJNF%2FQAke4yzuPvYDVZezwJmwr25t8MOUArQseUt9%2FQvD8Fsfz1uMVTUgrWExDy9N%2BlmSGy%2FaPUX3TC0KN%2BBiPwVrg%3D%3D","nav":{"name":"WebView","nav_type":"Redirect to Webview","params":{"screenTitle":"1 टक्का स्कीम 🤩","showHeader":true,"uri":"https://webapps.retailpulse.ai/billUpload?campaign=Cash_Reward"}}},{"MixPanelEventName":"W4","image_url":"https://storage.googleapis.com/op-d2r.appspot.com/widgets/October/12/8a06ab15-2ea4-41c2-84b0-2526f6fab429/Takka%20carousel4.png?GoogleAccessId=firebase-adminsdk-p1h6s%40op-d2r.iam.gserviceaccount.com&Expires=***********&Signature=j8yC6TbWg7B3ObizFO%2BLltp4VXaa8pIDT1g8S3qHc1XxjlLIzeT8vOWxikfj%2BPEyw%2BL83I7b2yHdH%2FuEJZK4w6eyACO45QPFNB7z6WqhNmpy6XxflhNPm9aVzIJ0mkw5%2FRd2wwMD7ha1VPB%2BNyloNgQN%2B6qeD4y8h09Tz%2F0cjx1%2BYSDzUo0h30xA0OhhorXyuRhey2%2Bx3I5f9JL26s8pYWzWdgaemx5dJnvbxn9CVxLzherREbHQWF4sPiKBv5Mcm0kYRts3IbPC%2F94zWPFaHsUJGHM%2FFNsFHmkfIlbn0lWdSHd%2BgpCFK8m832wn1QmVPtSyBi3FIzCxA5GeNYXuNQ%3D%3D","nav":{"name":"WebView","nav_type":"Redirect to Webview","params":{"screenTitle":"1 टक्का स्कीम 🤩","showHeader":true,"uri":"https://webapps.retailpulse.ai/billUpload?campaign=Cash_Reward"}}}],"heading":"1 takka Winner Carousel","id":70,"limit":4,"type":9,"versions":">=3.1.0","widget_info":{"widget_name":"1 takka Winner Carousel"},"sql_id":69,"updated_at":"2023-10-16T14:54:20.000Z","expiry":0,"created_by":"BACKEND","updated_by":"BACKEND","visibility_count":0,"is_active":1}

`

func getNewsWidget() interface{} {
	type News struct {
		Title             string        `json:"title"`
		NoOfShares        int           `json:"no_of_shares"`
		NoOfLikes         int           `json:"no_of_likes"`
		NoOfComments      int           `json:"no_of_comments"`
		CreatedAt         int64         `json:"created_at"`
		Margin            bool          `json:"margin"`
		Media             string        `json:"media"`
		NewsID            string        `json:"news_id"`
		ID                string        `json:"id"`
		Type              int           `json:"type"`
		WidgetType        int           `json:"widget_type"`
		NewsType          string        `json:"news_type"`
		Tag               interface{}   `json:"tag"`
		Liked             interface{}   `json:"liked"`
		ContentTruncated  bool          `json:"content_truncated"`
		SuggestedComments []interface{} `json:"suggestedComments"`
		ShowLikeShareComp bool          `json:"showLikeShareComp"`
		PollCTA           interface{}   `json:"pollCTA"`
		Thumbnail         string        `json:"thumbnail"`
		GifThumbnail      interface{}   `json:"gif_thumbnail"`
		WidgetID          int           `json:"widgetId"`
		ShareImgURL       string        `json:"share_img_url"`
		PlayBackSpeed     []float64     `json:"play_back_speed"`
		MaxBitrate        int           `json:"max_bitrate"`
		MaxHeadlineLength int           `json:"max_headline_length"`
	}
	news := News{
		Title:             "🛍️ एक ही कार्ट में ढेरों ब्रांड! अब ऑर्डर करना और भी आसान!",
		NoOfShares:        0,
		NoOfLikes:         300,
		Margin:            false,
		NoOfComments:      0,
		CreatedAt:         1741269964336,
		Media:             "https://mediacdn.retailpulse.ai/gcp-transcoded/processed_outputs/ams-videos/amsjobs/345c9249-e98b-4142-9018-7813fbfa0b9c/2025-05-10/7cc8d5bd-0566-47d3-8921-025118f0c77d/1746876191Your20paragraph20/manifest.m3u8",
		NewsID:            "3eec9398-0594-47bf-b0e5-b992103bf89f",
		ID:                "3eec9398-0594-47bf-b0e5-b992103bf89f",
		Type:              6,
		WidgetType:        29,
		NewsType:          "PORTRAIT",
		Tag:               nil,
		Liked:             nil,
		ContentTruncated:  true,
		SuggestedComments: []interface{}{},
		ShowLikeShareComp: false,
		PollCTA:           nil,
		Thumbnail:         "https://d2rstorage2.blob.core.windows.net/widget/June/10/4721dcb0-f162-4ba0-8bef-5a359b7c1d33/1749563322566.png",
		GifThumbnail:      "https://d2rstorage2.blob.core.windows.net/widget/June/10/4721dcb0-f162-4ba0-8bef-5a359b7c1d33/1749563322566.png",
		WidgetID:          1255,
		ShareImgURL:       "https://d2rstorage2.blob.core.windows.net/widget/June/10/c952a938-77c4-4e90-aede-278be3e4bd62/1749563723440.webp",
		PlayBackSpeed:     []float64{1, 1.5},
		MaxBitrate:        0,
		MaxHeadlineLength: 60,
	}
	return news
}

func getSchemeRewardUpdate() interface{} {
	type Nav struct {
		Name    string                 `json:"name"`
		NavType string                 `json:"nav_type"`
		Params  map[string]interface{} `json:"params"`
	}

	// CTA represents the call-to-action information
	type CTA struct {
		Nav Nav `json:"nav"`
	}

	// WidgetInfo represents widget metadata
	type WidgetInfo struct {
		WidgetName string `json:"widget_name"`
	}

	// Banner represents the main banner structure
	type Banner struct {
		CTA               CTA        `json:"CTA"`
		MixPanelEventName string     `json:"MixPanelEventName"`
		ComponentTitle    string     `json:"component_title"`
		ExpiryTime        int64      `json:"expiry_time"`
		ID                int        `json:"id"`
		ImageURL          string     `json:"image_url"`
		Type              int        `json:"type"`
		UpdatedBy         string     `json:"updatedBy"`
		Visibility        int        `json:"visibility"`
		VisibleFrom       int64      `json:"visible_from"`
		WidgetInfo        WidgetInfo `json:"widget_info"`
	}
	return Banner{
		CTA: CTA{
			Nav: Nav{
				Name:    "GenericScreen",
				NavType: "Redirect to Screen",
				Params: map[string]interface{}{
					"screenName": "Feb_Scheme",
				},
			},
		},
		MixPanelEventName: "Banner",
		ComponentTitle:    "Rewards Update - Users won Reward under Feb Scheme",
		ExpiryTime:        1743515863444,
		ID:                976,
		ImageURL:          "https://d2rstorage2.blob.core.windows.net/widget/March/13/236d961e-f927-4658-b91c-c14003faaf6f/1741874475122.webp",
		Type:              3,
		UpdatedBy:         "<EMAIL>",
		Visibility:        1,
		VisibleFrom:       1741874263444,
		WidgetInfo: WidgetInfo{
			WidgetName: "Rewards Update - Users won Reward under Feb Scheme",
		},
	}
}

func (s *Service) GetKiranaBazarCartScreenMeta(ctx context.Context, req *dto.AppGetKiranaBazarMetaRequest) (response *dto.AppGetKiranaBazarCartScreenMetaResponse, err error) {
	if req.UserID == "" {
		err = fmt.Errorf("user_id is required")
		return
	}
	widgets := []dto.Widget{}
	//widgets = append(widgets, ZoffGifType3WidgetProductsScreen)
	header := dto.Header{
		Text: "कार्ट में जोड़े गए आइटम",
	}
	return &dto.AppGetKiranaBazarCartScreenMetaResponse{
		Data: dto.CartScreenData{
			Widgets: widgets,
			Header:  header,
		},
	}, nil
}

func (s *Service) GetUserGeolocationData(ctx context.Context, userID string) (*dto.UserGeoLocationData, error) {
	cacheKey := fmt.Sprintf(userGeoLocationCacheKey, userID)

	// Try to get cached geolocation data
	cacheResult, err := s.GenericCache.GetCachedData(
		ctx,
		cacheKey,
		func() (interface{}, error) {
			return utils.GetUserGeolocationData(userID)
		},
		1*time.Hour, // Local cache for 1 hour
		0,           // Redis cache for 1 hour
	)

	if err != nil {
		return nil, err
	}

	geoLocationData, ok := cacheResult.Data.(*dto.UserGeoLocationData)
	if !ok {
		return nil, fmt.Errorf("invalid cache data type for geolocation data")
	}

	return geoLocationData, nil
}

func (s *Service) GetUserLevelOrderCount(ctx context.Context, userID string) (int, error) {
	cacheKey := fmt.Sprintf(userOrderCountCacheKey, userID)

	// Try to get cached order count
	cacheResult, err := s.GenericCache.GetCachedData(
		ctx,
		cacheKey,
		func() (interface{}, error) {
			var orderCount int = 0
			_, err := s.repository.CustomQuery(&orderCount, fmt.Sprintf(`select count(*) from kiranaclubdb.kiranabazar_orders ko where user_id = '%s';`, userID))
			if err != nil {
				return 0, err
			}
			return orderCount, nil
		},
		10*time.Minute, // Local cache for 10 minutes
		0,              // No caching in redis
	)

	if err != nil {
		return 0, err
	}

	orderCount, ok := cacheResult.Data.(int)
	if !ok {
		return 0, fmt.Errorf("invalid cache data type for order count")
	}

	return orderCount, nil
}

func (s *Service) GetUserLevelConfirmedOrderCount(ctx context.Context, userID string) (int, error) {
	cacheKey := fmt.Sprintf(userOrderCountCacheKey, userID)

	// Try to get cached confirmed order count
	cacheResult, err := s.GenericCache.GetCachedData(
		ctx,
		cacheKey,
		func() (interface{}, error) {
			var confirmedCount int = 0
			_, err := s.repository.CustomQuery(&confirmedCount, fmt.Sprintf(`select count(*) from kiranaclubdb.kiranabazar_orders ko where user_id = '%s' and status = 'confirmed';`, userID))
			if err != nil {
				return 0, err
			}
			return confirmedCount, nil
		},
		10*time.Minute, // Local cache for 10 minutes
		0,              // No caching in redis
	)

	if err != nil {
		return 0, err
	}

	confirmedCount, ok := cacheResult.Data.(int)
	if !ok {
		return 0, fmt.Errorf("invalid cache data type for confirmed order count")
	}

	return confirmedCount, nil
}

func (s *Service) GetWidgetResolverResponse(ctx context.Context, req dto.GetScreenDetailsRequest) (*dto.GetScreenDetailsResponse, error) {
	cacheKey := fmt.Sprintf(widgetResolverCacheKey, req.Uid, req.AppVersion)

	// Try to get cached widget resolver response
	cacheResult, err := s.GenericCache.GetCachedData(
		ctx,
		cacheKey,
		func() (interface{}, error) {
			apiResponse, statusCode, err := utils.CallExternalAPI(utils.WIDGET_RESOLVER_API, "POST", req, nil)
			if err != nil {
				return nil, err
			}
			if statusCode == nil || *statusCode != 200 {
				return nil, fmt.Errorf("error in fetching screen details: %s", string(apiResponse))
			}
			var response dto.GetScreenDetailsResponse
			err = json.Unmarshal(apiResponse, &response)
			if err != nil {
				return nil, err
			}
			return &response, nil
		},
		10*time.Minute, // Local cache for 10 minutes
		0,              // No caching in redis
	)

	if err != nil {
		return nil, err
	}

	response, ok := cacheResult.Data.(*dto.GetScreenDetailsResponse)
	if !ok {
		return nil, fmt.Errorf("invalid cache data type for widget resolver response")
	}

	return response, nil
}

func (s *Service) GetActivationCohortUserData(ctx context.Context, userID string, seller string) (*dto.ActivationCohortUserData, error) {
	cacheKey := fmt.Sprintf("activation_cohort_user_data:%s:%s", userID, seller)

	// Try to get cached activation cohort user data
	cacheResult, err := s.GenericCache.GetCachedData(
		ctx,
		cacheKey,
		func() (interface{}, error) {
			// ... existing activation cohort user data fetching logic ...
			return &dto.ActivationCohortUserData{}, nil
		},
		10*time.Minute, // Local cache for 10 minutes
		0,              // No caching in redis
	)

	if err != nil {
		return nil, err
	}

	userData, ok := cacheResult.Data.(*dto.ActivationCohortUserData)
	if !ok {
		return nil, fmt.Errorf("invalid cache data type for activation cohort user data")
	}

	return userData, nil
}

func (s *Service) GetWidgetViewCount(ctx context.Context, userID string) (map[string]int, error) {
	cacheKey := fmt.Sprintf(widgetCountsCacheKey, userID)

	// Try to get cached widget view count
	cacheResult, err := s.GenericCache.GetCachedData(
		ctx,
		cacheKey,
		func() (interface{}, error) {
			counts := make(map[string]int)
			// ... existing widget view count fetching logic ...
			return counts, nil
		},
		10*time.Minute, // Local cache for 10 minutes
		0,              // No caching in redis
	)

	if err != nil {
		return nil, err
	}

	counts, ok := cacheResult.Data.(map[string]int)
	if !ok {
		return nil, fmt.Errorf("invalid cache data type for widget view count")
	}

	return counts, nil
}

func (s *Service) GetUserAppVersion(ctx context.Context, userID string) (string, error) {
	cacheKey := fmt.Sprintf(userAppVersionCacheKey, userID)

	// Try to get cached user app version
	cacheResult, err := s.GenericCache.GetCachedData(
		ctx,
		cacheKey,
		func() (interface{}, error) {
			var appVersion string
			err := s.FirebaseRepository.RtDb.NewRef(fmt.Sprintf(`/users/%s/version`, userID)).Get(ctx, &appVersion)
			if err != nil {
				return "", err
			}
			return appVersion, nil
		},
		10*time.Minute, // Local cache for 10 minutes
		0,              // No caching in redis
	)

	if err != nil {
		return "", err
	}

	appVersion, ok := cacheResult.Data.(string)
	if !ok {
		return "", fmt.Errorf("invalid cache data type for user app version")
	}

	return appVersion, nil
}

func (s *Service) GetKiranaBazarWebMeta(ctx context.Context) (response *dto.AppGetKiranaBazarMetaResponse, err error) {
	req := dto.AppGetKiranaBazarMetaRequest{
		UserID: "En6aH3tFthd2E1ubt4TU5W6yndG2",
		Meta: dto.Meta{
			AppVersion: "6.5.1",
			DeviceId:   "1034fa740a156c61",
			ScreenName: "KiranaChallenge",
			Platform:   "web",
		},
	}
	return s.GetKiranaBazarMeta(ctx, &req)
}
