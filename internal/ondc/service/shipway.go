package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/external/shipway"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/logistics/couriers"
	orderstatus "kc/internal/ondc/service/orderStatus"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	"kc/internal/ondc/utils"
	"net/http"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/mixpanel/mixpanel-go"
)

// this function returns the awb number and courier for the orderid
func (s *Service) GetOrderAwbNumberAndCourier(orderID string) (*string, *string, error) {

	// check in order status table
	orderStatus := dao.KiranaBazarOrderStatus{}
	_, err := s.repository.CustomQuery(&orderStatus, fmt.Sprintf(`select * from kiranabazar_order_status ko where id = %s;`, orderID))
	if err != nil {
		return nil, nil, err
	}

	// if data exists in order status table return
	if orderStatus.AWBNumber != nil && *orderStatus.AWBNumber != "" && orderStatus.Courier != nil && *orderStatus.Courier != "" {
		return orderStatus.AWBNumber, orderStatus.Courier, nil
	}

	// check from api calling easyecom api if order does not exists on easyecom this will return an error
	orderid, err := strconv.ParseInt(orderID, 10, 64)
	orderStatusResponse, err := s.GetEasyEcomOrderDetails(context.Background(), int(orderid))
	if err != nil {
		return nil, nil, err
	}

	// if order exists on easyecom and final staus has awb number and is not empty return
	if len(orderStatusResponse.Data) > 0 && orderStatusResponse.Data[0].AWBNumber != "" {
		return &orderStatusResponse.Data[0].AWBNumber, &orderStatusResponse.Data[0].Courier, nil
	}

	// return error with no data found
	return nil, nil, errors.New("orderstatus info does not exists")
}

func getRetryNumber(s string) int {
	// If the string contains a hyphen
	if strings.Contains(s, "-") {
		// Split the string by hyphen and get the last part
		parts := strings.Split(s, "-")
		if len(parts) > 1 {
			// Convert the last part to integer
			num, err := strconv.Atoi(parts[len(parts)-1])
			if err == nil {
				return num
			}
		}
	}
	return 0 // Return 0 if no number found or string doesn't match pattern
}

func (s *Service) CreateShipwayOrder(ctx context.Context, request *dto.CreateShipwayOrderRequest) (*dto.CreateShipwayOrderResponse, error) {
	shipwayTrackingData := dao.ShipwayOrderTracking{}
	_, err := s.repository.CustomQuery(&shipwayTrackingData, fmt.Sprintf(`select * from kiranaclubdb.shipway_order_tracking sot where order_id = %s and is_active = true order by logs_id desc limit 1`, request.OrderID))
	if err != nil {
		return nil, err
	}
	if !request.ForceCreate {
		if shipwayTrackingData.ID != 0 {
			return &dto.CreateShipwayOrderResponse{
				Message:       "fetched from db",
				OrderID:       fmt.Sprintf("%d", shipwayTrackingData.OrderID),
				AwbNumber:     shipwayTrackingData.AwbNumber,
				CurrentStatus: shipwayTrackingData.CurrentStatus,
				CourierName:   shipwayTrackingData.CourierName,
				LogsID:        shipwayTrackingData.LogsID,
			}, nil
		}
	}

	awbNumber, courier, err := s.GetOrderAwbNumberAndCourier(request.OrderID)
	if err != nil {
		return nil, err
	}

	if shipwayTrackingData.AwbNumber == *awbNumber {
		return nil, errors.New("the order with awb number already exists so cannot create even if forced to update")
	}

	// if awb exists create an order on shipway
	courierDetails, err := couriers.GetCourierByName(courier)
	if err != nil {
		return nil, err
	}
	carrierID := courierDetails.ID
	orderid, err := strconv.ParseInt(request.OrderID, 10, 64)
	orderInfo, err := GetOrderInfo(s.repository, orderid)
	if err != nil {
		return nil, err
	}
	shipwayResponse, statusCode, insertID, err := shipway.CallShipwayAPI(shipway.PUSH_ORDER, shipway.PushOrderDataRequest{
		CarrierID:    carrierID,
		AwbNumber:    *awbNumber,
		OrderID:      fmt.Sprintf("KC_%06d-%d", orderid, getRetryNumber(fmt.Sprintf("KC_%06d", orderid))+1),
		FirstName:    "_",
		LastName:     "_",
		Email:        "_",
		Phone:        "_",
		Products:     "_",
		Company:      "Kirana Club",
		ShipmentType: "1", // 1 for forward shipment
		OrderData:    "_",
	}, make(map[string]interface{}, 0))

	if err != nil {
		return nil, err
	}
	if statusCode != http.StatusOK {
		return &dto.CreateShipwayOrderResponse{
			Message: fmt.Sprintf("statusCode %d", statusCode),
		}, errors.New(fmt.Sprintf("statuscode %d", statusCode))
	}

	pushOrderResponse := shipway.PushOrderDataResponse{}
	err = json.Unmarshal(shipwayResponse, &pushOrderResponse)
	if err != nil {
		return nil, err
	}
	if pushOrderResponse.Status == "failed" {
		return &dto.CreateShipwayOrderResponse{
			Message:       pushOrderResponse.Message,
			CurrentStatus: pushOrderResponse.Status,
			OrderID:       request.OrderID,
			AwbNumber:     *awbNumber,
			CourierName:   *courier,
		}, nil
	}
	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Pushed to Shipway", *orderInfo.UserID, map[string]interface{}{
			"distinct_id":      *orderInfo.UserID,
			"seller":           orderInfo.Seller,
			"order_id":         orderInfo.ID,
			"shipway_order_id": fmt.Sprintf("KC_%06d-%d", orderid, getRetryNumber(fmt.Sprintf("KC_%06d", orderid))+1),
			"source":           "AUTOMATION",
		}),
	})
	s.repository.Create(&dao.ShipwayOrderTracking{
		OrderID:        orderid,
		AwbNumber:      *awbNumber,
		CurrentStatus:  "",
		CourierName:    *courier,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
		IsActive:       true,
		LogsID:         insertID,
		ShipwayOrderID: fmt.Sprintf("KC_%06d-%d", orderid, getRetryNumber(fmt.Sprintf("KC_%06d", orderid))+1),
	})
	return &dto.CreateShipwayOrderResponse{
		Message:     "Order created on shipway",
		OrderID:     request.OrderID,
		AwbNumber:   *awbNumber,
		CourierName: *courier,
	}, nil
}

func getOrderID(orderNumber string) string {
	parts := strings.Split(orderNumber, "_")
	if len(parts) < 2 {
		return ""
	}
	orderID := strings.Split(parts[1], "-")[0]
	return orderID
}

func (s *Service) HandleShipwayWebhook(ctx context.Context, request *dto.ShipwayWebhookRequest) (*dto.ShipwayWebhookResponse, error) {
	if len(request.StatusFeed) <= 0 {
		return nil, errors.New("invalid request object")
	}
	byt, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}
	s.repository.Create(&dao.ShipwayWebhookLogs{
		Request:   byt,
		CreatedAt: time.Now(),
		Type:      fmt.Sprintf("update_order_status_%s", request.StatusFeed[0].OrderID),
	})
	orderID := getOrderID(request.StatusFeed[0].OrderID)
	if orderID == "" {
		slack.SendSlackMessage(fmt.Sprintf("invalid order id ", orderID))
		return nil, errors.New("order id is invalid")
	}
	orderIDInt, err := strconv.ParseInt(orderID, 10, 64)
	if err != nil {
		return nil, errors.New("order id is invalid")
	}
	shipwayOrderTracking := []dao.ShipwayOrderTracking{}
	_, err = s.repository.Find(map[string]interface{}{
		"order_id": orderIDInt,
	}, &shipwayOrderTracking)

	if err != nil {
		return nil, err
	}
	if len(shipwayOrderTracking) == 0 {
		return nil, errors.New("order does not exists on kirana club")
	}

	orderStatus := request.StatusFeed[0].CurrentStatus
	orderInfo, err := GetOrderInfo(s.repository, orderIDInt)
	if err != nil {
		return nil, errors.New("order does not exists")
	}
	if orderStatus == *orderInfo.OrderStatus {
		return &dto.ShipwayWebhookResponse{
			Message: "order status already updated",
		}, nil
	}

	wybillDetails, _ := s.AWBMaster.GetWaybillDetails(context.Background(), request.StatusFeed[0].AWBNumber, 0)
	// stopping the sync for delhivery from shipway
	if (request.CarrierID == 2 && slices.Contains([]string{"zoff_foods", "hugs", "apsara_tea"}, orderInfo.Seller)) || (len(wybillDetails) > 0 && wybillDetails[0].CreatedAt != nil) {
		return nil, nil
	}

	orderDetails, _ := GetOrderDetails(s.repository, fmt.Sprintf("%d", orderIDInt))
	if err != nil {
		return nil, err
	}

	// updating tracking url in db in go routine
	if request.StatusFeed[0].TrackingURL != "" {
		go func(orderIDInt int64, trackingUrl string) {
			_, _, err := s.repository.Update(&dao.KiranaBazarOrder{
				ID: &orderIDInt,
			}, &dao.KiranaBazarOrder{
				TrackingLink: &trackingUrl,
			})
			if err != nil {
				slack.SendSlackMessage(fmt.Sprintf("error updating tracking url in db for %d %s", orderIDInt, err.Error()))
			}
		}(orderIDInt, request.StatusFeed[0].TrackingURL)
	}
	if request.StatusFeed[0].PickupDate != "" {
		go func(orderIDInt int64, pickUpDate, awb, carrier, tracakinglink string, orderDetails dao.KiranaBazarOrderDetails, orderInfo dao.KiranaBazarOrder) {
			ist, err := time.LoadLocation("Asia/Kolkata")
			if err != nil {
				return
			}
			t, err := time.ParseInLocation("2006-01-02 15:04:05", pickUpDate, ist)
			if err != nil {
				return
			}
			epochMillis := t.UnixMilli()
			response, err := s.AddDataForReconciliation(ctx, &dto.AddReconciliationRequest{
				OrderID: orderIDInt,
				Data: []dto.StatusTimeStamp{
					{
						TimeStamp:   epochMillis,
						OrderStatus: "order_dispatched",
					},
				},
				Service: "INTERNAL",
				OMS:     orderInfo.Seller,
			})

			metricsUpdated := response.UpdatedMetrics

			if err == nil && includes(metricsUpdated, "order_dispatched") {
				eventObject := map[string]interface{}{
					"distinct_id":     *orderInfo.UserID,
					"awb_number":      awb,
					"courier_name":    carrier,
					"tracking_link":   tracakinglink,
					"order_id":        *orderInfo.ID,
					"cart_value":      int(orderDetails.GetCartValue()),
					"order_value":     int(orderDetails.GetOrderValue()),
					"seller":          orderInfo.Seller,
					"ordering_module": utils.MakeTitleCase(orderInfo.Seller),
					"time":            epochMillis / 1000,
				}
				s.Mixpanel.Track(ctx, []*mixpanel.Event{
					s.Mixpanel.NewEvent("Order Dispatched", *orderInfo.UserID, eventObject, fmt.Sprintf("%s_%d", "order_dispatched", *orderInfo.ID)),
				})
			}
		}(orderIDInt, request.StatusFeed[0].PickupDate, request.StatusFeed[0].AWBNumber, request.StatusFeed[0].Carrier, request.StatusFeed[0].TrackingURL, *orderDetails, orderInfo)
	}
	_, err = s.UpdateB2BOrderStatus(ctx, &dto.UpdateB2BOrderStatusRequest{
		UpdatedBy: "SHIPWAY",
		Data: dto.UpdateB2BOrderStatusData{
			OrderID:        fmt.Sprintf("%d", *orderInfo.ID),
			UpdatedBy:      "SHIPWAY",
			OrderStatus:    orderStatus,
			UpdatedAt:      time.Now().UnixMilli(),
			OrderingModule: orderInfo.Seller,
			OrderMeta: dto.OrderMeta{
				Courier:   request.StatusFeed[0].Carrier,
				AWBNumber: request.StatusFeed[0].AWBNumber,
				Note:      request.StatusFeed[0].CurrentStatusDesc,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	return nil, nil
}

func getorderid(str string) int {
	re := regexp.MustCompile(`0*(\d+)`)
	match := re.FindStringSubmatch(str)
	if len(match) > 1 {
		num, _ := strconv.Atoi(match[1])
		return num
	}
	return 0
}

// GetShipwayOrderDetails return the shipway response for the particular order also saves the response to api logs
func (s *Service) GetShipwayOrderDetails(ctx context.Context, request *dto.ShipwayOrderFetchRequest) (*dto.ShipwayOrderFetchResponse, error) {
	resp, statusCode, _, err := shipway.CallShipwayAPI(shipway.GET_ORDER_DETAILS, shipway.GetOrderShipmentDetails{
		OrderID: request.OrderID,
	}, make(map[string]interface{}))
	if statusCode != 200 || err != nil {
		if err != nil {
			return nil, errors.New(fmt.Sprintf("err fetching order details for the order ", err))
		}
		return nil, errors.New(fmt.Sprintf("not able to fetch order details, %d", statusCode))
	}

	shipwayOrderFetchResponse := &dto.ShipwayOrderFetchResponse{}
	err = json.Unmarshal(resp, shipwayOrderFetchResponse)
	if err != nil {
		return nil, err
	}

	orderStatues := orderstatus.MapOrderStatus(shipwayOrderFetchResponse.Response.CurrentStatusCode, "", orderstatus.OrderStatusResponse{})
	if orderStatues.DisplayStatus == displaystatus.OTHERS {
		return shipwayOrderFetchResponse, nil
	}

	intOrderID := getorderid(shipwayOrderFetchResponse.Response.OrderID)
	orderInfo, err := GetOrderInfo(s.repository, int64(intOrderID))
	if err != nil {
		return nil, err
	}
	orderDetails, err := GetOrderDetails(s.repository, fmt.Sprintf("%d", intOrderID))
	if err != nil {
		return nil, err
	}
	if orderInfo.DeliveryStatus == orderStatues.ShipmentStatus && orderInfo.DisplayStatus == orderStatues.DisplayStatus && orderInfo.ProcessingStatus == orderStatues.ProcessingStatus {
		return shipwayOrderFetchResponse, nil
	}
	if shipwayOrderFetchResponse.Response.PickUpDate != "" || shipwayOrderFetchResponse.Response.PickupDate != "" {
		pkpDate := shipwayOrderFetchResponse.Response.PickUpDate
		if pkpDate == "" {
			pkpDate = shipwayOrderFetchResponse.Response.PickupDate
		}
		go func(orderIDInt int64, pickUpDate, awb, carrier, tracakinglink string) {
			ist, err := time.LoadLocation("Asia/Kolkata")
			if err != nil {
				return
			}
			t, err := time.ParseInLocation("2006-01-02 15:04:05", pickUpDate, ist)
			if err != nil {
				return
			}

			epochMillis := t.UnixMilli()
			response, err := s.AddDataForReconciliation(ctx, &dto.AddReconciliationRequest{
				OrderID: orderIDInt,
				Data: []dto.StatusTimeStamp{
					dto.StatusTimeStamp{
						TimeStamp:   epochMillis,
						OrderStatus: "order_dispatched",
					},
				},
				Service: "INTERNAL",
				OMS:     orderInfo.Seller,
			})

			metricsUpdated := response.UpdatedMetrics

			if err == nil && includes(metricsUpdated, "order_dispatched") {
				eventObject := map[string]interface{}{
					"distinct_id":     *orderInfo.UserID,
					"awb_number":      awb,
					"courier_name":    carrier,
					"tracking_link":   tracakinglink,
					"order_id":        *orderInfo.ID,
					"cart_value":      int(orderDetails.GetCartValue()),
					"order_value":     int(orderDetails.GetOrderValue()),
					"seller":          orderInfo.Seller,
					"ordering_module": utils.MakeTitleCase(orderInfo.Seller),
					"time":            epochMillis / 1000,
				}
				s.Mixpanel.Track(ctx, []*mixpanel.Event{
					s.Mixpanel.NewEvent("Order Dispatched", *orderInfo.UserID, eventObject, fmt.Sprintf("%s_%d", "order_dispatched", *orderInfo.ID)),
				})
			}
		}(*orderInfo.ID, pkpDate, shipwayOrderFetchResponse.Response.AWBNo, shipwayOrderFetchResponse.Response.Carrier, shipwayOrderFetchResponse.Response.TrackingURL)
	}

	_, err = s.UpdateB2BOrderStatus(ctx, &dto.UpdateB2BOrderStatusRequest{
		UpdatedBy: "SHIPWAY",
		Data: dto.UpdateB2BOrderStatusData{
			OrderID:        fmt.Sprintf("%d", *orderInfo.ID),
			UpdatedBy:      "SHIPWAY_POLLING",
			OrderStatus:    shipwayOrderFetchResponse.Response.CurrentStatusCode,
			UpdatedAt:      time.Now().UnixMilli(),
			OrderingModule: orderInfo.Seller,
			OrderMeta: dto.OrderMeta{
				Courier:   shipwayOrderFetchResponse.Response.Carrier,
				AWBNumber: shipwayOrderFetchResponse.Response.AWBNo,
			},
		},
	})
	if err != nil {
		return nil, err
	}
	return shipwayOrderFetchResponse, nil

}
