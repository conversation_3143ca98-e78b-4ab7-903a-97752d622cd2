package service

import (
	"context"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/service/cart"
	productsService "kc/internal/ondc/service/products"
	"kc/internal/ondc/service/recommendation"
	userdetails "kc/internal/ondc/service/userDetails"
	"kc/internal/ondc/utils"
	"math/rand"
	"strings"
	"time"

	// "encoding/json"
	"errors"
	"kc/internal/ondc/models/dto"
)

// shuffleInterfaceSlice shuffles elements of a slice of any type
func shuffleInterfaceSlice(arr []interface{}) []interface{} {
	rand.Seed(time.Now().UnixNano())
	n := len(arr)
	for i := n - 1; i > 0; i-- {
		j := rand.Intn(i + 1)
		arr[i], arr[j] = arr[j], arr[i]
	}
	return arr
}

func (s *Service) GetRecommendedProducts(ctx context.Context, request dto.GetRecommendedProductsRequest) (response *dto.GetRecommendedProductsResponse, err error) {
	appVersion := request.Meta.AppVersion
	kiranaBazarCart, err := cart.Get(ctx, request.UserID, request.Data.Seller)
	if err != nil {
		return nil, err
	}

	sellerItems, _ := kiranaBazarCart.Cart.Get()

	limit := 6
	if request.Meta.Limit != 0 {
		limit = request.Meta.Limit
	}

	userDetailsChannel := userdetails.AsyncFetchUserDetails(request.UserID, []string{userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS}, 1*time.Minute)

	inCartItems := []string{}
	for _, item := range sellerItems {
		if err != nil {
			fmt.Println("err ", err)
		}
		inCartItems = append(inCartItems, item.ID)
	}

	userDetails := <-userDetailsChannel
	userCohortNames := []string{}
	var userGeography *userdetails.UserGeoData
	if userDetails.Data != nil && userDetails.Data.UserDynamicDetails != nil {
		userCohortNames = userDetails.Data.UserDynamicDetails.UserCohortNames
	}
	if userDetails.Data != nil {
		if userDetails.Data.UserGeography != nil {
			userGeography = userDetails.Data.UserGeography
		}
	}
	userCohortNames = append(userCohortNames, userdetails.GetUserDerivedCohorts(request.UserID, &[]string{request.Data.Seller}, userGeography)...)

	userPricingContext := productsService.PricingContext{
		UserID:     &request.UserID,
		Quantity:   0,
		UserCohort: &userCohortNames,
	}

	if len(inCartItems) == 0 {
		inCartItems = append(inCartItems, "1000000")
	}

	excludedProducts := []string{request.Data.ProductId}
	excludedProducts = append(excludedProducts, inCartItems...)
	var suggestedProducts []shared.SellerItems
	var products []dao.KiranaBazarProduct
	var productsData []*productsService.Product

	recommendations, err := recommendation.GetProductRecommendations(ctx, request.Data.ProductId)

	productIdsToFetch := make([]string, 0)
	for _, rec := range recommendations {
		for _, item := range rec {
			data, _ := productsService.GetProductByID(item)
			fmt.Println("item-----", data.ID, data.Name, data.Code, data.IsActive, data.IsOOS)
			if data.IsActive != nil && *data.IsActive == true && (data.IsOOS == nil || *data.IsOOS == false) {
				if !includes(excludedProducts, fmt.Sprintf("%d", item)) {
					productIdsToFetch = append(productIdsToFetch, fmt.Sprintf("%d", item))
					break
				}
			}
		}
	}
	query := ""
	if len(productIdsToFetch) > 3 {
		query = fmt.Sprintf(`
			SELECT 
				kp.*
			FROM 
				kiranabazar_products kp
			WHERE 
				kp.is_active = true
# 				AND kp.is_default = true
			    AND kp.product_type = "product"
				AND kp.id IN (%s)
				AND kp.seller = "%s"
				AND kp.is_oos = false
				ORDER BY kp.popularity_value DESC, kp.rank 
				LIMIT %d`, strings.Join(productIdsToFetch, ","), request.Data.Seller, limit)
		fmt.Println("query in if")
	} else {
		fmt.Println("in eslee")
		query = fmt.Sprintf(`
			SELECT 
				kp.*
			FROM 
				kiranabazar_products kp
			WHERE 
				kp.is_active = true
				AND kp.is_default = true
			    AND kp.product_type = "product"
				AND kp.id NOT IN (%s)
				AND kp.seller = "%s"
				AND kp.is_oos = false
				ORDER BY kp.popularity_value DESC, kp.rank 
				LIMIT %d`, strings.Join(excludedProducts, ","), request.Data.Seller, limit)
	}

	s.repository.CustomQuery(&products, query)
	description := ""

	for i, product := range products {
		data, exist := productsService.GetProductByID(product.ID)
		if data == nil || exist == false {
			errMsg := fmt.Sprintf("product not found for code: %s", product.ID, err)
			fmt.Println(errMsg)
			continue
		}
		if i < 2 {
			description = description + data.MetaProperties.HindiName + ", "
		}
		productsData = append(productsData, data)
	}
	if len(description) > 40 {
		description = description[:40]
	}
	description = description + fmt.Sprintf("...+%d और", len(productsData)-2)

	if len(productsData) < 3 {
		return nil, errors.New("not enough products to recommend")
	}

	productsSizeVariantMap := s.ProcessAndMakeVariantMap(productsData, "")
	suggestedProducts, err = GetProductsFromMap(productsSizeVariantMap, utils.BoolPtr(true), appVersion, "",
		false, &userPricingContext)
	if err != nil {
		return nil, err
	}

	suggestedProducts = filterProductsByCohort(suggestedProducts, userCohortNames)

	for i, _ := range suggestedProducts {
		// suggestedProducts[i].SizeVariants = nil
		suggestedProducts[i].Nav = nil
		ctaText := ""
		if len(suggestedProducts[i].SizeVariants) > 1 {
			ctaText = "साइज चुने"
		}
		suggestedProducts[i].CTAText = &ctaText
	}

	items := make([]interface{}, 0)
	cnt := 0
	for _, product := range suggestedProducts {
		if product.ID == request.Data.ProductId {
			continue // Skip the product that was used to fetch recommendations
		}
		items = append(items, product)

		cnt++
		if cnt >= 20 {
			break
		}
	}
	type43Widgets := getProductsResponse(request.UserID, items, 20, []interface{}{}, "recommended")

	widgets, ok := type43Widgets.([]interface{})
	if !ok {
		return nil, errors.New("failed to cast widgets to []interface{}")
	}

	for i, w := range widgets {
		widgetData, ok := w.(dto.WidgetType43)
		if !ok {
			continue // Skip if the widget is not a map
		}
		// "aspect_ratio": 0.7,
		// "background_color": "#F1F9FF40",
		// "width_multiplier": 130,
		// widgetData["aspect_ratio"] = 0.7 // Assign a unique ID to each widget
		// widgetData["background_color"] = "#F1F9FF40"
		// widgetData["width_multiplier"] = 130 // Assign a unique ID to each widget
		// Update the widget in the response
		aspectRatio := 0.68
		backgroundColor := "#F1F9FF40"
		widthMultiplier := 140.0
		widgetData.AspectRation = &aspectRatio        // Assign aspect ratio
		widgetData.BackgroundColor = &backgroundColor // Assign background color
		widgetData.WidthMultiplier = &widthMultiplier // Assign width multiplier
		widgetData.OptimisedWidthPercentage = 30

		widgetData.Data = shuffleInterfaceSlice(widgetData.Data) // Shuffle the data in the widget

		widgets[i] = widgetData // Update the widget in the response
	}

	return &dto.GetRecommendedProductsResponse{
		Meta: request.Meta,
		Data: dto.RecommendedProductsData{
			Title:   "साथ ख़रीदे गये प्रॉडक्ट्स 🛒",
			SubText: description,
			HighlightedSubTexts: []dto.HighlightedTextData{
				{
					Text: "+3 और",
					HighlightStyle: &map[string]interface{}{
						"color": "#007C06",
					},
				},
			},
			Styles: map[string]interface{}{
				"bg_color": "#F2F1F6",
			},
			//ImageUrl:            "https://d2rstorage2.blob.core.windows.net/widget/June/19/4d467ae2-733e-4a4f-8731-5ac92a2c9a85/1750320646323.webp",
			RecommendedProducts: widgets, // Assuming the response contains widgets
			// RecommendedProducts: widgets, // Assuming the response contains widgets
		},
	}, nil
}

const HRDCODED_WIDGET string = `
[
  {
    "MixPanelEventName": "ProductList Widget",
    "expiry_time": **********,
    "id": 12345,
    "type": 43,
    "sub_type": "recommended",
    "updatedBy": "backend",
    "visibility": 1,
    "visible_from": **********,
    "widget_info": {
      "widget_name": "ProductList Products Screen Widget"
    },
    "aspect_ratio": 0.9,
    "background_color": "#F1F9FF40",
    "width_multiplier": 130,
    "data": [
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Nisha Hair Color Powder : Black",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/nisha/products/400x400/nisha_1000.webp"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/nisha/products/400x400/nisha_1000.webp",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "230"
        ],
        "bottom_info_badge": {
          "text": "1 पैक = 10 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2392",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक पैक में 10 आइटम होते हैं।",
          "hindi_name": "निशा हेयर कलर पाउडर-10 ग्राम : काला",
          "quantity": "10g",
          "pack_size": 10,
          "case_size": 80,
          "mrp_number": 15,
          "mrp_string": "MRP: ₹15",
          "markup_margin": 56.7,
          "markup_margin_string": "मार्जिन: 56.7%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "56.7%",
          "mrp_string_value": "₹ 15",
          "wholesale_rate": 6.5,
          "wholesale_rate_string": "₹6.5",
          "tax": 18,
          "hsn_code": "33059040",
          "expires_in": 63244800000,
          "max_cap": 70
        },
        "size_variant_code": 2392,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 100
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Tablet Tray(12pc)",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/satmola/products/400x400/satmola_1003.webp"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/satmola/products/400x400/satmola_1003.webp",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "242"
        ],
        "top_badge": {
          "text": "ऑफर: ₹6̶0̶ 45",
          "color": "#6f3b00",
          "bg_color": [
            "#ffdbb2",
            "#ffdbb2"
          ]
        },
        "bottom_info_badge": {
          "text": "1 पैक = 1 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2387",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक पैक में 1 आइटम होते हैं।",
          "hindi_name": "Satmola पाचक गोली",
          "quantity": "12pcs",
          "pack_size": 1,
          "case_size": 1,
          "mrp_number": 72,
          "mrp_string": "MRP: ₹72",
          "markup_margin": 37.5,
          "markup_margin_string": "मार्जिन: 16.7+20.8%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "16.7+20.8%",
          "mrp_string_value": "₹ 72",
          "wholesale_rate": 45,
          "wholesale_rate_string": "₹45",
          "tax": 12,
          "hsn_code": "30049011",
          "expires_in": 94867200000,
          "brand_wholesale_rate": 60,
          "badge_text": "ऑफर: ₹6̶0̶ 45"
        },
        "size_variant_code": 2387,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 100
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Digestive Tablet Pouch Jar (Regular)",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/satmola/products/400x400/satmola_1004.webp"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/satmola/products/400x400/satmola_1004.webp",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "242"
        ],
        "top_badge": {
          "text": "ऑफर: ₹1̶8̶0̶ 165",
          "color": "#6f3b00",
          "bg_color": [
            "#ffdbb2",
            "#ffdbb2"
          ]
        },
        "bottom_info_badge": {
          "text": "1 पैक = 1 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2389",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक पैक में 1 आइटम होते हैं।",
          "hindi_name": "Satmola रेगुलर पाचक गोली",
          "quantity": "200pcs",
          "pack_size": 1,
          "case_size": 1,
          "mrp_number": 200,
          "mrp_string": "MRP: ₹200",
          "markup_margin": 17.5,
          "markup_margin_string": "मार्जिन: 10+7.5%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "10+7.5%",
          "mrp_string_value": "₹ 200",
          "wholesale_rate": 165,
          "wholesale_rate_string": "₹165",
          "tax": 12,
          "hsn_code": "30049011",
          "expires_in": 94867200000,
          "brand_wholesale_rate": 180,
          "badge_text": "ऑफर: ₹1̶8̶0̶ 165"
        },
        "size_variant_code": 2389,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 100
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Patanjali Dant Kanti Natural Toothpaste",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/lots/products/400x400/lots_3001.webp",
          "https://kiranaclub.blob.core.windows.net/brands/lots/products/400x400/lots_3001_1.webp"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/lots/products/400x400/lots_3001.webp",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/lots/products/400x400/lots_3001_1.webp",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "218"
        ],
        "bottom_info_badge": {
          "text": "1 पैक = 12 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2268",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक पैक में 6 आइटम होते हैं।",
          "hindi_name": "पतंजलि दाँत कान्ति ",
          "quantity": "₹10",
          "pack_size": 12,
          "case_size": 1,
          "mrp_number": 10,
          "mrp_string": "MRP: ₹10",
          "markup_margin": 22,
          "markup_margin_string": "मार्जिन: 22%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "22%",
          "mrp_string_value": "₹ 10",
          "wholesale_rate": 7.8,
          "wholesale_rate_string": "₹7.80",
          "tax": 12,
          "hsn_code": "33061020",
          "expires_in": 63244800000,
          "max_cap": 24
        },
        "size_variant_code": 2268,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 93.25
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Soya Chunks",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2145.webp",
          "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2145_1.webp"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2145.webp",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2145_1.webp",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "222"
        ],
        "bottom_info_badge": {
          "text": "1 पैक = 20 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "1116",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "यह ₹ 10 वाला पैकेट है जो की 45g की पैकिंग में आता है। इसके एक पैक में 20 पीस होते है।",
          "hindi_name": "सोया बड़ी",
          "quantity": "₹10",
          "pack_size": 20,
          "case_size": 1,
          "mrp_number": 10,
          "mrp_string": "MRP: ₹10",
          "markup_margin": 31.8,
          "markup_margin_string": "मार्जिन: 32%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "32%",
          "mrp_string_value": "₹ 10",
          "wholesale_rate": 6.82,
          "wholesale_rate_string": "₹6.82",
          "tax": 12,
          "hsn_code": "21061000",
          "expires_in": 28944000000
        },
        "size_variant_code": 1116,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 72.39
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Dabur Amla Hair Oil",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/lots/products/400x400/lots_3000.webp",
          "https://kiranaclub.blob.core.windows.net/brands/lots/products/400x400/lots_3000_1.webp"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/lots/products/400x400/lots_3000.webp",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/lots/products/400x400/lots_3000_1.webp",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "218"
        ],
        "bottom_info_badge": {
          "text": "1 पैक = 6 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2267",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक पैक में 6 आइटम होते हैं।",
          "hindi_name": "डाबर अमला हेयर आयल ",
          "quantity": "45ml",
          "pack_size": 6,
          "case_size": 1,
          "mrp_number": 20,
          "mrp_string": "MRP: ₹20",
          "markup_margin": 15,
          "markup_margin_string": "मार्जिन: 15%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "15%",
          "mrp_string_value": "₹ 20",
          "wholesale_rate": 17,
          "wholesale_rate_string": "₹17",
          "tax": 18,
          "hsn_code": "33059011",
          "expires_in": 94867200000,
          "max_cap": 18
        },
        "size_variant_code": 2267,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 70.55
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Patanjali Dant Kanti Natural Toothpaste",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/B8JZ5SdtU8_3nvqL-jFD2/2251011a6c5c9c0bcf2a60712db7d247250fd974jpg?timestamp=**********",
          "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/8Xd3YXAMQDfrATCkzjSuD/c36805a9b9c75b3b5168851f1be9a6e54c772fcfjpg?timestamp=**********"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/B8JZ5SdtU8_3nvqL-jFD2/2251011a6c5c9c0bcf2a60712db7d247250fd974jpg?timestamp=**********",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/8Xd3YXAMQDfrATCkzjSuD/c36805a9b9c75b3b5168851f1be9a6e54c772fcfjpg?timestamp=**********",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "218"
        ],
        "bottom_info_badge": {
          "text": "1 पैक = 4 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2447",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "",
          "hindi_name": "पतंजलि दाँत कान्ति ",
          "quantity": "100gm",
          "pack_size": 4,
          "case_size": 72,
          "mrp_number": 60,
          "mrp_string": "MRP: ₹60",
          "markup_margin": 13.3,
          "markup_margin_string": "मार्जिन: 13%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "13%",
          "mrp_string_value": "₹ 60",
          "wholesale_rate": 52,
          "wholesale_rate_string": "₹52.0",
          "tax": 12,
          "hsn_code": "33061020",
          "expires_in": -1779498532807000
        },
        "size_variant_code": 2447,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 67.48
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Gillette Guard Razor 1 Unit",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/cRUxIlINMumo4p2uoSVG4/ded996411dfd08dd6cc3dd38593aab19920f8e01jpg?timestamp=**********"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/cRUxIlINMumo4p2uoSVG4/ded996411dfd08dd6cc3dd38593aab19920f8e01jpg?timestamp=**********",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "218"
        ],
        "bottom_info_badge": {
          "text": "1 पैक = 3 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2352",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "",
          "hindi_name": "जिलेट गार्ड रेजर",
          "quantity": "1pc",
          "pack_size": 3,
          "case_size": 1,
          "mrp_number": 25,
          "mrp_string": "MRP: ₹25",
          "markup_margin": 20,
          "markup_margin_string": "मार्जिन: 20%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "20%",
          "mrp_string_value": "₹ 25",
          "wholesale_rate": 20,
          "wholesale_rate_string": "₹20",
          "tax": 18,
          "hsn_code": "82121090",
          "expires_in": 94867200000,
          "max_cap": 9
        },
        "size_variant_code": 2352,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 49.69
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "LYCHI CANDY PKT",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1020.webp",
          "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1020_1.webp"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1020.webp",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1020_1.webp",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "219"
        ],
        "top_badge": {
          "text": "ऑफर: ₹4̶9̶ 43",
          "color": "#6f3b00",
          "bg_color": [
            "#ffdbb2",
            "#ffdbb2"
          ]
        },
        "bottom_info_badge": {
          "text": "1 पैक = 1 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2380",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक पैक में 140 आइटम होते हैं।",
          "hindi_name": "लीची कैंडी",
          "quantity": "140pc",
          "pack_size": 1,
          "case_size": 1,
          "mrp_number": 70,
          "mrp_string": "MRP: ₹70",
          "markup_margin": 38.6,
          "markup_margin_string": "मार्जिन: 30+8.6%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "30+8.6%",
          "mrp_string_value": "₹ 70",
          "wholesale_rate": 43,
          "wholesale_rate_string": "₹43",
          "tax": 12,
          "hsn_code": "17049020",
          "expires_in": 31622400000,
          "brand_wholesale_rate": 49,
          "badge_text": "ऑफर: ₹4̶9̶ 43"
        },
        "size_variant_code": 2380,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 44.17
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Nihar Shanti Amla Badam Hair Oil 140 Ml",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/_S7DO3QGH5HkFvVgqnuar/043d5fc7dc14ada182fcfa8ecedebfce15c4730fjpg?timestamp=**********"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/_S7DO3QGH5HkFvVgqnuar/043d5fc7dc14ada182fcfa8ecedebfce15c4730fjpg?timestamp=**********",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "218"
        ],
        "bottom_info_badge": {
          "text": "1 पैक = 3 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2353",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "",
          "hindi_name": "निहार शांति आंवला बादाम हेयर ऑयल ",
          "quantity": "140ml",
          "pack_size": 3,
          "case_size": 1,
          "mrp_number": 45,
          "mrp_string": "MRP: ₹45",
          "markup_margin": 22.22,
          "markup_margin_string": "मार्जिन: 22%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "22%",
          "mrp_string_value": "₹ 45",
          "wholesale_rate": 35,
          "wholesale_rate_string": "₹35",
          "tax": 18,
          "hsn_code": "33059011",
          "expires_in": 94867200000,
          "max_cap": 9
        },
        "size_variant_code": 2353,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 42.94
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Push Up the Cone",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/BLcT8rxL0dEYsB5uIfqdo/7b43dffb2d1117d8116e8e2e659ebddff69ffeecjpg?timestamp=**********",
          "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/CA7szFw4gv1rrFzzP6E7y/927a026c8d6ac08e59911b13fbf960cee0a66698jpg?timestamp=**********"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/BLcT8rxL0dEYsB5uIfqdo/7b43dffb2d1117d8116e8e2e659ebddff69ffeecjpg?timestamp=**********",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/CA7szFw4gv1rrFzzP6E7y/927a026c8d6ac08e59911b13fbf960cee0a66698jpg?timestamp=**********",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "219"
        ],
        "bottom_info_badge": {
          "text": "1 पैक = 1 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2261",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक पैक में 30 आइटम होते हैं और प्रत्येक आइटम की कीमत ₹5 हैं।",
          "hindi_name": "चॉकलेट वेफर्स कोन",
          "quantity": "30 पीस",
          "pack_size": 1,
          "case_size": 1,
          "mrp_number": 150,
          "mrp_string": "MRP: ₹150",
          "markup_margin": 24,
          "markup_margin_string": "मार्जिन: 24%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "24%",
          "mrp_string_value": "₹ 150",
          "wholesale_rate": 114,
          "wholesale_rate_string": "₹114",
          "tax": 18,
          "hsn_code": "18069010",
          "expires_in": 31535630529
        },
        "size_variant_code": 2261,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 41.72
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "FRUITS POP LOLLIPOP JAR",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1004.webp"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1004.webp",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "219"
        ],
        "top_badge": {
          "text": "ऑफर: ₹1̶4̶6̶ 130",
          "color": "#6f3b00",
          "bg_color": [
            "#ffdbb2",
            "#ffdbb2"
          ]
        },
        "bottom_info_badge": {
          "text": "1 पैक = 1 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2377",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक जार में 100 आइटम होते हैं।",
          "hindi_name": "फ्रूट्स पॉप लॉलीपॉप जार",
          "quantity": "100pc",
          "pack_size": 1,
          "case_size": 1,
          "mrp_number": 200,
          "mrp_string": "MRP: ₹200",
          "markup_margin": 35,
          "markup_margin_string": "मार्जिन: 27+8%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "27+8%",
          "mrp_string_value": "₹ 200",
          "wholesale_rate": 130,
          "wholesale_rate_string": "₹130",
          "tax": 12,
          "hsn_code": "17049020",
          "expires_in": 31622400000,
          "brand_wholesale_rate": 146,
          "badge_text": "ऑफर: ₹1̶4̶6̶ 130"
        },
        "size_variant_code": 2377,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 41.1
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Hugs Double Fun",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/9nX7JGVAXO4iQ2x8b1s0e/d3162c8cc69be2ec17d25e1222333b0320a9c2acjpg?timestamp=**********",
          "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/_IuEmZqXt1zaei1nJ8bFL/58ecd873b3b6f3694521765c12a97715bf86b86djpg?timestamp=**********"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/9nX7JGVAXO4iQ2x8b1s0e/d3162c8cc69be2ec17d25e1222333b0320a9c2acjpg?timestamp=**********",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/_IuEmZqXt1zaei1nJ8bFL/58ecd873b3b6f3694521765c12a97715bf86b86djpg?timestamp=**********",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "219"
        ],
        "bottom_info_badge": {
          "text": "1 पैक = 1 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2262",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "description": "उपलब्ध साइज - 33 पीस, 60 पीस",
        "meta": {
          "description": "एक पैक में 33 आइटम होते हैं और प्रत्येक आइटम की कीमत ₹5 हैं।",
          "hindi_name": "डबल फन",
          "quantity": "33 पीस",
          "pack_size": 1,
          "case_size": 1,
          "mrp_number": 165,
          "mrp_string": "MRP: ₹165",
          "markup_margin": 24.24,
          "markup_margin_string": "मार्जिन: 24%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "24%",
          "mrp_string_value": "₹ 165",
          "wholesale_rate": 125,
          "wholesale_rate_string": "₹125",
          "tax": 18,
          "hsn_code": "18069010",
          "expires_in": 31535782280
        },
        "size_variant_code": 2262,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 33.74,
        "size_variants": [
          {
            "provider_id": "third_party_rsb_super_stockist",
            "seller": "rsb_super_stockist",
            "seller_name": "RSB Super Stockist",
            "provider_ttl": "",
            "provider_locations_ids": null,
            "name": "Hugs Double Fun",
            "image_urls": [
              "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/9nX7JGVAXO4iQ2x8b1s0e/d3162c8cc69be2ec17d25e1222333b0320a9c2acjpg?timestamp=**********",
              "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/_IuEmZqXt1zaei1nJ8bFL/58ecd873b3b6f3694521765c12a97715bf86b86djpg?timestamp=**********"
            ],
            "banner_image_urls": [
              {
                "mixpanel_event_name": "",
                "url": "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/9nX7JGVAXO4iQ2x8b1s0e/d3162c8cc69be2ec17d25e1222333b0320a9c2acjpg?timestamp=**********",
                "type": "",
                "video_url": ""
              },
              {
                "mixpanel_event_name": "",
                "url": "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/_IuEmZqXt1zaei1nJ8bFL/58ecd873b3b6f3694521765c12a97715bf86b86djpg?timestamp=**********",
                "type": "",
                "video_url": ""
              }
            ],
            "location_ids": null,
            "category_ids": [
              "219"
            ],
            "bottom_info_badge": {
              "text": "1 पैक = 1 यूनिट",
              "color": "#074E87",
              "bg_color": [
                "#E3F6FF",
                "#E3F6FF"
              ]
            },
            "fulfilment_ids": null,
            "sku_confirmation_processed": false,
            "id": "2262",
            "parent_item_id": "2262",
            "price": {
              "currency": "rs",
              "value": "10",
              "estimated_value": "10",
              "computed_value": "10",
              "listed_value": "10",
              "offered_value": "10",
              "minimum_value": "10",
              "maximum_value": "10"
            },
            "quantity": 0,
            "description": "उपलब्ध साइज - 33 पीस, 60 पीस",
            "meta": {
              "description": "एक पैक में 33 आइटम होते हैं और प्रत्येक आइटम की कीमत ₹5 हैं।",
              "hindi_name": "डबल फन",
              "quantity": "33 पीस",
              "pack_size": 1,
              "case_size": 1,
              "mrp_number": 165,
              "mrp_string": "MRP: ₹165",
              "markup_margin": 24.24,
              "markup_margin_string": "मार्जिन: 24%",
              "markup_margin_key": "मार्जिन:",
              "markup_margin_value": "24%",
              "mrp_string_value": "₹ 165",
              "wholesale_rate": 125,
              "wholesale_rate_string": "₹125",
              "tax": 18,
              "hsn_code": "18069010",
              "expires_in": 31535782280
            },
            "size_variant_code": 2262,
            "is_default": true,
            "is_oos": null,
            "type": 0,
            "rank": 0,
            "popularity_value": 33.74
          },
          {
            "provider_id": "third_party_rsb_super_stockist",
            "seller": "rsb_super_stockist",
            "seller_name": "RSB Super Stockist",
            "provider_ttl": "",
            "provider_locations_ids": null,
            "name": "Hugs Double Fun",
            "image_urls": [
              "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/_fKljf5e-fMfKH0OHFCKo/e23e951e55ad9110dccfb9f32b04d64158bfcd79jpg?timestamp=**********",
              "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/l2fUIqMOKLIxssik_Udiv/5a90ecd2a6f46cf9c736d6463da07164e407791ajpg?timestamp=**********"
            ],
            "banner_image_urls": [
              {
                "mixpanel_event_name": "",
                "url": "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/_fKljf5e-fMfKH0OHFCKo/e23e951e55ad9110dccfb9f32b04d64158bfcd79jpg?timestamp=**********",
                "type": "",
                "video_url": ""
              },
              {
                "mixpanel_event_name": "",
                "url": "https://kiranaclub.blob.core.windows.net/brands/rsb_super_stockist/products/400x400/l2fUIqMOKLIxssik_Udiv/5a90ecd2a6f46cf9c736d6463da07164e407791ajpg?timestamp=**********",
                "type": "",
                "video_url": ""
              }
            ],
            "location_ids": null,
            "category_ids": [
              "219"
            ],
            "bottom_info_badge": {
              "text": "1 पैक = 1 यूनिट",
              "color": "#074E87",
              "bg_color": [
                "#E3F6FF",
                "#E3F6FF"
              ]
            },
            "fulfilment_ids": null,
            "sku_confirmation_processed": false,
            "id": "2263",
            "parent_item_id": "2262",
            "price": {
              "currency": "rs",
              "value": "10",
              "estimated_value": "10",
              "computed_value": "10",
              "listed_value": "10",
              "offered_value": "10",
              "minimum_value": "10",
              "maximum_value": "10"
            },
            "quantity": 0,
            "description": "उपलब्ध साइज - 33 पीस, 60 पीस",
            "meta": {
              "description": "एक पैक में 60 आइटम होते हैं और प्रत्येक आइटम की कीमत ₹5 हैं।",
              "hindi_name": "डबल फन",
              "quantity": "60 पीस",
              "pack_size": 1,
              "case_size": 1,
              "mrp_number": 300,
              "mrp_string": "MRP: ₹300",
              "markup_margin": 23.33,
              "markup_margin_string": "मार्जिन: 23%",
              "markup_margin_key": "मार्जिन:",
              "markup_margin_value": "23%",
              "mrp_string_value": "₹ 300",
              "wholesale_rate": 230,
              "wholesale_rate_string": "₹230",
              "tax": 18,
              "hsn_code": "18069010",
              "expires_in": 31535891720
            },
            "size_variant_code": 2262,
            "is_default": false,
            "is_oos": null,
            "type": 0,
            "rank": 0,
            "popularity_value": 0
          }
        ]
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Cumin whole | NE Lot",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2004.jpeg?timestamp=**********",
          "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2004_1.jpeg?timestamp=**********"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2004.jpeg?timestamp=**********",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2004_1.jpeg?timestamp=**********",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "239"
        ],
        "top_badge": {
          "text": "Expiry: Oct'25",
          "color": "#6f3b00",
          "bg_color": [
            "#ffdbb2",
            "#ffdbb2"
          ]
        },
        "bottom_info_badge": {
          "text": "1 पैक = 10 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2278",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक पैक में 10 पीस होते है",
          "hindi_name": "जीरा",
          "quantity": "₹10",
          "pack_size": 10,
          "case_size": 420,
          "mrp_number": 10,
          "mrp_string": "MRP: ₹10",
          "markup_margin": 40,
          "markup_margin_string": "मार्जिन: 40%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "40%",
          "mrp_string_value": "₹ 10",
          "wholesale_rate": 6,
          "wholesale_rate_string": "₹6",
          "tax": 5,
          "expires_in": 11942221603,
          "badge_text": "Expiry: Oct'25"
        },
        "size_variant_code": 2278,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 30.06
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "ORANGE PULP CANDY PKT",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1018.webp",
          "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1018_1.webp"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1018.webp",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1018_1.webp",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "219"
        ],
        "top_badge": {
          "text": "ऑफर: ₹4̶9̶ 43",
          "color": "#6f3b00",
          "bg_color": [
            "#ffdbb2",
            "#ffdbb2"
          ]
        },
        "bottom_info_badge": {
          "text": "1 पैक = 1 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2379",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक पैक में 140 आइटम होते हैं।",
          "hindi_name": "ऑरेंज कैंडी",
          "quantity": "140pc",
          "pack_size": 1,
          "case_size": 1,
          "mrp_number": 70,
          "mrp_string": "MRP: ₹70",
          "markup_margin": 38.6,
          "markup_margin_string": "मार्जिन: 30+8.6%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "30+8.6%",
          "mrp_string_value": "₹ 70",
          "wholesale_rate": 43,
          "wholesale_rate_string": "₹43",
          "tax": 12,
          "hsn_code": "17049020",
          "expires_in": 31622400000,
          "brand_wholesale_rate": 49,
          "badge_text": "ऑफर: ₹4̶9̶ 43"
        },
        "size_variant_code": 2379,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 27.61
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Himalaya Purifying Neem Face Wash 50 Ml",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/lots/products/400x400/lots_3004.webp",
          "https://kiranaclub.blob.core.windows.net/brands/lots/products/400x400/lots_3004_1.webp"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/lots/products/400x400/lots_3004.webp",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/lots/products/400x400/lots_3004_1.webp",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "218"
        ],
        "bottom_info_badge": {
          "text": "1 पैक = 3 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2269",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक पैक में 3 आइटम होते हैं।",
          "hindi_name": "हिमालया नीम फेस वाश ",
          "quantity": "50Ml",
          "pack_size": 3,
          "case_size": 1,
          "mrp_number": 89,
          "mrp_string": "MRP: ₹89",
          "markup_margin": 21.3,
          "markup_margin_string": "मार्जिन: 21%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "21%",
          "mrp_string_value": "₹ 89",
          "wholesale_rate": 70,
          "wholesale_rate_string": "₹70",
          "tax": 12,
          "hsn_code": "33049990",
          "expires_in": 63244800000,
          "max_cap": 3
        },
        "size_variant_code": 2269,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 25.77
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Punjabi Papad",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/mothers_kitchen/products/400x400/mothers_kitchen_1001.webp",
          "https://kiranaclub.blob.core.windows.net/brands/mothers_kitchen/products/400x400/mothers_kitchen_1001_1.webp",
          "https://kiranaclub.blob.core.windows.net/brands/mothers_kitchen/products/400x400/mothers_kitchen_1001_2.webp",
          "https://kiranaclub.blob.core.windows.net/brands/mothers_kitchen/products/400x400/mothers_kitchen_1001_3.webp"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/mothers_kitchen/products/400x400/mothers_kitchen_1001.webp",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/mothers_kitchen/products/400x400/mothers_kitchen_1001_1.webp",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/mothers_kitchen/products/400x400/mothers_kitchen_1001_2.webp",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/mothers_kitchen/products/400x400/mothers_kitchen_1001_3.webp",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "216"
        ],
        "bottom_info_badge": {
          "text": "1 पैक = 5 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "1037",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक पैक में 5 आइटम होते हैं। प्रोडक्ट की लंबाई 10 cm, चौड़ाई 5 cm और ऊंचाई 2 cm है।",
          "hindi_name": "पंजाबी चिप्स पापड़",
          "quantity": "40g",
          "pack_size": 5,
          "case_size": 1,
          "mrp_number": 35,
          "mrp_string": "MRP: ₹35",
          "markup_margin": 34,
          "markup_margin_string": "मार्जिन: 34%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "34%",
          "mrp_string_value": "₹ 35",
          "wholesale_rate": 23,
          "wholesale_rate_string": "₹23",
          "tax": 0,
          "hsn_code": "19059030",
          "expires_in": 28857600000
        },
        "size_variant_code": 1037,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 24.54
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "JOLLY DOLLY (JELLY CANDY ) MANGO",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1007.webp"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1007.webp",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "219"
        ],
        "top_badge": {
          "text": "ऑफर: ₹5̶9̶.̶5̶ 52",
          "color": "#6f3b00",
          "bg_color": [
            "#ffdbb2",
            "#ffdbb2"
          ]
        },
        "bottom_info_badge": {
          "text": "1 पैक = 1 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2381",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक पैक में 85 आइटम होते हैं।",
          "hindi_name": "मैंगो - जॉली डॉली",
          "quantity": "85pc",
          "pack_size": 1,
          "case_size": 1,
          "mrp_number": 85,
          "mrp_string": "MRP: ₹85",
          "markup_margin": 38.8,
          "markup_margin_string": "मार्जिन: 30+8.8%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "30+8.8%",
          "mrp_string_value": "₹ 85",
          "wholesale_rate": 52,
          "wholesale_rate_string": "₹52",
          "tax": 12,
          "hsn_code": "17049020",
          "expires_in": 31622400000,
          "brand_wholesale_rate": 59.5,
          "badge_text": "ऑफर: ₹5̶9̶.̶5̶ 52"
        },
        "size_variant_code": 2381,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 23.93
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "COCO ECLAIRS PKT",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1005.webp",
          "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1005_1.webp"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1005.webp",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/milden/products/400x400/milden_1005_1.webp",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "219"
        ],
        "top_badge": {
          "text": "ऑफर: ₹7̶4̶ 65",
          "color": "#6f3b00",
          "bg_color": [
            "#ffdbb2",
            "#ffdbb2"
          ]
        },
        "bottom_info_badge": {
          "text": "1 पैक = 1 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2378",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक पैक में 100 आइटम होते हैं।",
          "hindi_name": "COCO एक्लेयर्स",
          "quantity": "100pc",
          "pack_size": 1,
          "case_size": 1,
          "mrp_number": 100,
          "mrp_string": "MRP: ₹100",
          "markup_margin": 35,
          "markup_margin_string": "मार्जिन: 26+9%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "26+9%",
          "mrp_string_value": "₹ 100",
          "wholesale_rate": 65,
          "wholesale_rate_string": "₹65",
          "tax": 12,
          "hsn_code": "17049020",
          "expires_in": 31622400000,
          "brand_wholesale_rate": 74,
          "badge_text": "ऑफर: ₹7̶4̶ 65"
        },
        "size_variant_code": 2378,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 22.7
      },
      {
        "provider_id": "third_party_rsb_super_stockist",
        "seller": "rsb_super_stockist",
        "seller_name": "RSB Super Stockist",
        "provider_ttl": "",
        "provider_locations_ids": null,
        "name": "Red Chilli Powder | NE Lot",
        "image_urls": [
          "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2001.jpeg?timestamp=**********",
          "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2001_1.jpeg?timestamp=**********"
        ],
        "banner_image_urls": [
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2001.jpeg?timestamp=**********",
            "type": "",
            "video_url": ""
          },
          {
            "mixpanel_event_name": "",
            "url": "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2001_1.jpeg?timestamp=**********",
            "type": "",
            "video_url": ""
          }
        ],
        "location_ids": null,
        "category_ids": [
          "239"
        ],
        "top_badge": {
          "text": "Expiry: Oct'25",
          "color": "#6f3b00",
          "bg_color": [
            "#ffdbb2",
            "#ffdbb2"
          ]
        },
        "bottom_info_badge": {
          "text": "1 पैक = 10 यूनिट",
          "color": "#074E87",
          "bg_color": [
            "#E3F6FF",
            "#E3F6FF"
          ]
        },
        "fulfilment_ids": null,
        "sku_confirmation_processed": false,
        "id": "2284",
        "parent_item_id": "",
        "price": {
          "currency": "rs",
          "value": "10",
          "estimated_value": "10",
          "computed_value": "10",
          "listed_value": "10",
          "offered_value": "10",
          "minimum_value": "10",
          "maximum_value": "10"
        },
        "quantity": 0,
        "meta": {
          "description": "एक पैक में 10 पीस होते है",
          "hindi_name": "लाल मिर्च पाउडर",
          "quantity": "₹10",
          "pack_size": 10,
          "case_size": 360,
          "mrp_number": 10,
          "mrp_string": "MRP: ₹10",
          "markup_margin": 47,
          "markup_margin_string": "मार्जिन: 47%",
          "markup_margin_key": "मार्जिन:",
          "markup_margin_value": "47%",
          "mrp_string_value": "₹ 10",
          "wholesale_rate": 5.3,
          "wholesale_rate_string": "₹5.3",
          "tax": 5,
          "expires_in": 13238221602,
          "expiry_date_string": "Nov 2025",
          "badge_text": "Expiry: Oct'25"
        },
        "size_variant_code": 2284,
        "is_default": true,
        "is_oos": null,
        "type": 33,
        "rank": 0,
        "popularity_value": 22.7
      }
    ]
  }
]
`
