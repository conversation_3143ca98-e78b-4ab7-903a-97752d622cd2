package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dto"
	"log"
	"strings"
	"time"

	"github.com/elastic/go-elasticsearch/v8"
)

// CreateEnhancedSearchIndex creates a new index with n-gram analyzers for better search
func CreateEnhancedSearchIndex(ctx context.Context, client *elasticsearch.Client, indexName string) error {
	// Check if the index already exists
	resp, err := client.Indices.Exists([]string{indexName})
	if err != nil {
		return fmt.Errorf("error checking if index exists: %s", err)
	}
	defer resp.Body.Close()

	// If index exists, delete it (for demonstration purposes - in production you'd want to handle this differently)
	if resp.StatusCode == 200 {
		deleteResp, err := client.Indices.Delete([]string{indexName})
		if err != nil {
			return fmt.Errorf("error deleting existing index: %s", err)
		}
		defer deleteResp.Body.Close()

		if deleteResp.IsError() {
			return fmt.Errorf("error deleting index: %s", deleteResp.String())
		}
	}

	// Define the index settings and mappings with n-gram analyzers
	settings := `{
		"settings": {
			"index": {
				"max_ngram_diff": 10
			},
			"analysis": {
				"analyzer": {
					"ngram_analyzer": {
						"tokenizer": "standard",
						"filter": [
							"lowercase",
							"ngram_filter"
						]
					},
					"edge_ngram_analyzer": {
						"tokenizer": "standard",
						"filter": [
							"lowercase",
							"edge_ngram_filter"
						]
					}
				},
				"filter": {
					"ngram_filter": {
						"type": "ngram",
						"min_gram": 2,
						"max_gram": 4
					},
					"edge_ngram_filter": {
						"type": "edge_ngram",
						"min_gram": 2,
						"max_gram": 10
					}
				}
			}
		},
		"mappings": {
			"properties": {
				"product_id": {
					"type": "integer"
				},
				"category_id": {
					"type": "integer"
				},
				"seller": {
					"type": "keyword"
				},
				"queries": {
					"type": "nested",
					"properties": {
						"value": {
							"type": "text",
							"fields": {
								"keyword": {
									"type": "keyword"
								},
								"ngram": {
									"type": "text",
									"analyzer": "ngram_analyzer",
									"search_analyzer": "standard"
								},
								"edge_ngram": {
									"type": "text",
									"analyzer": "edge_ngram_analyzer",
									"search_analyzer": "standard"
								}
							}
						}
					}
				}
			}
		}
	}`

	// Create the index with the defined settings
	createResp, err := client.Indices.Create(
		indexName,
		client.Indices.Create.WithBody(strings.NewReader(settings)),
		client.Indices.Create.WithContext(ctx),
	)
	if err != nil {
		return fmt.Errorf("error creating index: %s", err)
	}
	defer createResp.Body.Close()

	if createResp.IsError() {
		return fmt.Errorf("error creating index: %s", createResp.String())
	}

	fmt.Printf("Successfully created index '%s' with enhanced search capabilities\n", indexName)
	return nil
}

// ReindexData re-indexes data from source to destination with enhanced mappings
func ReindexData(ctx context.Context, client *elasticsearch.Client, sourceIndex, destIndex string) error {
	// Define the reindex operation
	reindexBody := map[string]interface{}{
		"source": map[string]interface{}{
			"index": sourceIndex,
		},
		"dest": map[string]interface{}{
			"index": destIndex,
		},
	}

	// Convert to JSON
	reindexJSON, err := json.Marshal(reindexBody)
	if err != nil {
		return fmt.Errorf("error marshaling reindex body: %s", err)
	}

	// Perform the reindex operation
	resp, err := client.Reindex(
		bytes.NewReader(reindexJSON),
		client.Reindex.WithContext(ctx),
		client.Reindex.WithWaitForCompletion(true),
	)
	if err != nil {
		return fmt.Errorf("error during reindex: %s", err)
	}
	defer resp.Body.Close()

	if resp.IsError() {
		return fmt.Errorf("error during reindex: %s", resp.String())
	}

	// Parse the response to check results
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return fmt.Errorf("error parsing reindex response: %s", err)
	}

	total, _ := result["total"].(float64)
	fmt.Printf("Successfully reindexed %d documents from '%s' to '%s'\n", int(total), sourceIndex, destIndex)
	return nil
}

// SetupEnhancedSearch creates a new index with n-gram capabilities and reindexes data
func SetupEnhancedSearch(esClient *elasticsearch.Client, currentIndex, enhancedIndex string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Step 1: Create a new index with enhanced analyzers
	err := CreateEnhancedSearchIndex(ctx, esClient, enhancedIndex)
	if err != nil {
		return fmt.Errorf("failed to create enhanced index: %w", err)
	}

	// Step 2: Reindex data from current index to the new enhanced index
	err = ReindexData(ctx, esClient, currentIndex, enhancedIndex)
	if err != nil {
		return fmt.Errorf("failed to reindex data: %w", err)
	}

	return nil
}

func (s *Service) CreateElasticEnhancedIndex(ctx context.Context, request *dto.ElasticCreateIndexRequest) error {
	// Create a new Elasticsearch client
	esClient, err := elasticsearch.NewClient(elasticsearch.Config{
		Addresses: []string{"https://kc-elastic-search.es.centralindia.azure.elastic-cloud.com/"},
		Username:  "elastic",
		Password:  "3bcuPwKIOUsfhWvoEZot7yED",
	})
	if err != nil {
		log.Fatalf("Error creating the client: %s", err)
		return err
	}

	// Set up phonetic search
	err = SetupEnhancedSearch(esClient, request.Data.IndexName, request.Data.EnhancedIndexName)
	if err != nil {
		log.Fatalf("Error setting up phonetic search: %s", err)
		return err
	}

	fmt.Println("Phonetic search setup completed successfully!")
	return nil
}
