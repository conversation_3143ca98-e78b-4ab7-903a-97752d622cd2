package config

import (
	"image/color"
	"image/png"
)

// Config holds the configuration for image generation
type Config struct {
	Width        int
	MinHeight    int
	ItemHeight   int
	FontPaths    FontPaths
	Colors       Colors
	OutputPath   string
	LogoPath     string
	FooterPath   string
	Quality      png.Encoder
	DPI          int
	AntiAliasing bool
}

// FontPaths holds paths to required font files
type FontPaths struct {
	Regular    string
	Bold       string
	Devanagari string
	Rupee      string
}

// Colors defines the color scheme for the image
type Colors struct {
	HeaderBlue color.RGBA
	BorderGray color.RGBA
	RowGray    color.RGBA
	TextBlack  color.RGBA
}

// DefaultConfig returns the default configuration
func DefaultConfig() *Config {
	return &Config{
		Width:      850,
		MinHeight:  600,
		ItemHeight: 30,
		Colors: Colors{
			HeaderBlue: color.RGBA{51, 122, 183, 255},
			BorderGray: color.RGBA{221, 221, 221, 255},
			RowGray:    color.RGBA{249, 249, 249, 255},
			TextBlack:  color.RGBA{51, 51, 51, 255},
		},
		FontPaths: FontPaths{
			Regular:    "./internal/ondc/service/imageGenerator/fonts/Mukta-Regular.ttf",
			Bold:       "./internal/ondc/service/imageGenerator/fonts/Mukta-Bold.ttf",
			Devanagari: "./internal/ondc/service/imageGenerator/fonts/NotoSansDevanagari-VariableFont_wdth,wght.ttf",
			Rupee:      "./internal/ondc/service/imageGenerator/fonts/ITF Rupee.ttf",
		},
		OutputPath:   "./internal/ondc/service/imageGenerator/output/order.png",
		LogoPath:     "./internal/ondc/service/imageGenerator/assets/kiranaclub.png",
		FooterPath:   "./internal/ondc/service/imageGenerator/assets/footer.png",
		DPI:          90, // High DPI for better quality
		AntiAliasing: true,
		Quality: png.Encoder{
			CompressionLevel: png.NoCompression, // Best quality
		},
	}
}
