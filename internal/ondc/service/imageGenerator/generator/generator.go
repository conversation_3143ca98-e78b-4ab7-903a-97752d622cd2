package generator

import (
	"context"
	"fmt"
	"image/png"
	"os"
	"path/filepath"
	"time"

	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/imageGenerator/config"
	"kc/internal/ondc/utils"
)

// ImageGenerator handles the generation of order images
type ImageGenerator struct {
	config *config.Config
	fonts  *Fonts
	canvas *Canvas
	drawer *Drawer
}

// NewImageGenerator creates a new instance of ImageGenerator
func NewImageGenerator(cfg *config.Config) (*ImageGenerator, error) {
	if cfg == nil {
		cfg = config.DefaultConfig()
	}

	fonts, err := loadFonts(cfg.FontPaths)
	if err != nil {
		return nil, fmt.Errorf("failed to load fonts: %w", err)
	}

	return &ImageGenerator{
		config: cfg,
		fonts:  fonts,
	}, nil
}

// GenerateOrderImage generates an image for the given order
func (ig *ImageGenerator) GenerateOrderImage(cart dao.KiranaBazarOrderDetails, paymentDetails dao.KiranaBazarOrderPayment, orderID, sellerParam string) (string, error) {
	// Calculate total height based on items
	height := ig.calculateHeight(cart.TotalItems)

	// Initialize canvas
	ig.canvas = newCanvas(ig.config.Width, height, ig.config.DPI)

	// Initialize drawer
	ig.drawer = newDrawer(ig.canvas, ig.fonts, ig.config)

	// Draw components
	if cart.Seller == "" {
		cart.Seller = sellerParam
	}

	sellerName, err := brands.GetNameMappingBySeller(cart.Seller)

	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("Error getting seller name mapping for %s: %v Handle Confirmed Order", cart.Seller, err))
	}

	if err := ig.drawComponents(cart, paymentDetails, orderID, sellerName); err != nil {
		return "", fmt.Errorf("failed to draw components: %w", err)
	}

	// Save image
	localPath := fmt.Sprintf("%s.png", orderID)
	defer os.Remove(localPath)
	if err := ig.saveImage(localPath); err != nil {
		return "", fmt.Errorf("failed to save image: %w", err)
	}

	url, err := utils.UploadImageToBlob(context.Background(), localPath, make([]byte, 0), fmt.Sprintf("%s/%s/%s.png", cart.Seller, time.Now().Format("2006-01-02"), orderID), "image/png", "kcbazar-bills")
	if err != nil {
		return "", err
	}

	return url, nil
}

func (ig *ImageGenerator) calculateHeight(totalItems int) int {
	return totalItems*ig.config.ItemHeight + ig.config.MinHeight
}

func (ig *ImageGenerator) drawComponents(cart dao.KiranaBazarOrderDetails, paymentDetails dao.KiranaBazarOrderPayment, orderID, seller string) error {
	y := 40

	if err := ig.drawer.drawHeader(y); err != nil {
		return fmt.Errorf("failed to draw header: %w", err)
	}
	y += 40

	if err := ig.drawer.drawAddressAndSummary(cart, y, orderID, seller); err != nil {
		return fmt.Errorf("failed to draw address and summary: %w", err)
	}
	y += 200

	y = ig.drawer.drawItemsTable(cart, y)
	y += 35

	y = ig.drawer.drawPricing(cart, paymentDetails, y)

	if err := ig.drawer.drawFooter(y); err != nil {
		return fmt.Errorf("failed to draw footer: %w", err)
	}

	return nil
}

func (ig *ImageGenerator) saveImage(path string) error {
	if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}

	f, err := os.Create(path)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer f.Close()

	if err := png.Encode(f, ig.canvas.img); err != nil {
		return fmt.Errorf("failed to encode image: %w", err)
	}

	return nil
}
