package generator

import (
	"encoding/json"
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/png"
	"math"
	"os"
	"strconv"

	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/service/imageGenerator/config"
	"kc/internal/ondc/service/offers"
	orderstatus "kc/internal/ondc/service/orderStatus/constants"

	"github.com/golang/freetype"
	"github.com/golang/freetype/truetype"
)

const (
	rupeeSymbol = "R"
)

// Drawer handles drawing operations
type Drawer struct {
	ctx    *freetype.Context
	canvas *Canvas
	fonts  *Fonts
	config *config.Config
}

func newDrawer(canvas *Canvas, fonts *Fonts, cfg *config.Config) *Drawer {
	ctx := freetype.NewContext()
	ctx.SetDPI(float64(cfg.DPI))
	ctx.SetFont(fonts.regular)
	ctx.SetFontSize(12)
	ctx.SetClip(canvas.img.Bounds())
	ctx.SetDst(canvas.img)
	ctx.SetSrc(image.Black)

	return &Drawer{
		ctx:    ctx,
		canvas: canvas,
		fonts:  fonts,
		config: cfg,
	}
}

func (d *Drawer) drawHeader(y int) error {
	// Draw header background
	r := image.Rect(0, 0, d.canvas.width, 50)
	draw.Draw(d.canvas.img, r, &image.Uniform{d.config.Colors.HeaderBlue}, image.Point{}, draw.Src)

	// Load and draw logo
	logo, err := d.loadAndScaleLogo(d.config.LogoPath, 150, 40)
	if err != nil {
		return err
	}

	// Center logo
	logoX := (d.canvas.width - logo.Bounds().Dx()) / 2
	logoY := (50 - logo.Bounds().Dy()) / 2

	logoRect := image.Rect(logoX, logoY, logoX+logo.Bounds().Dx(), logoY+logo.Bounds().Dy())
	draw.Draw(d.canvas.img, logoRect, logo, image.Point{}, draw.Over)

	return nil
}

func (d *Drawer) drawAddressAndSummary(cart dao.KiranaBazarOrderDetails, y int, orderID, seller string) error {
	// Draw borders
	d.drawRect(20, y, d.canvas.width/2-20, y+153, d.config.Colors.BorderGray, false)
	d.drawRect(d.canvas.width/2+20, y, d.canvas.width-20, y+153, d.config.Colors.BorderGray, false)

	// Address section
	d.setFont(d.fonts.bold, 14)
	d.drawText("Shipping Address:", 40, y+20)

	d.setFont(d.fonts.regular, 12)
	addr := cart.ShippingAddress
	y += 45

	lines := []string{
		*addr.Name,
		addr.StoreName,
		addr.Line1,
		addr.Line2,
		fmt.Sprintf("%s - %s", *addr.State, *addr.PostalCode),
		fmt.Sprintf("Phone: %s", *addr.Phone),
	}

	for _, line := range lines {
		d.drawText(line[:int(math.Min(70, float64(len(line))))], 40, y)
		y += 20
	}

	// Summary section
	y -= 140
	d.setFont(d.fonts.bold, 14)
	d.drawText("Order Summary", d.canvas.width/2+40, y)

	d.setFont(d.fonts.regular, 12)
	y += 25

	summaryLines := []struct {
		label string
		value interface{}
	}{
		{"Order ID:", orderID},
		{"Seller:", seller},
		{"Total Items:", cart.TotalItems},
	}

	for _, line := range summaryLines {
		d.drawText(fmt.Sprintf("%s %v", line.label, line.value), d.canvas.width/2+40, y)
		y += 20
	}

	// Total amount with rupee symbol
	d.drawText("Total Amount:", d.canvas.width/2+40, y)
	d.setFont(d.fonts.rupee, 12)
	d.drawText(rupeeSymbol, d.canvas.width/2+130, y)
	d.setFont(d.fonts.regular, 12)
	d.drawText(fmt.Sprintf("%.2f", math.Ceil(cart.TotalAmount)), d.canvas.width/2+137, y)

	return nil
}

// Additional drawing methods would go here...
// drawItemsTable, drawPricing, drawFooter, etc.

func (d *Drawer) setFont(font *truetype.Font, size float64) {
	d.ctx.SetFont(font)
	d.ctx.SetFontSize(size)
}

func (d *Drawer) drawText(text string, x, y int) {
	pt := freetype.Pt(x, y)
	d.ctx.DrawString(text, pt)
}

func (d *Drawer) drawRect(x1, y1, x2, y2 int, col color.Color, fill bool) {
	for x := x1; x <= x2; x++ {
		if fill {
			for y := y1; y <= y2; y++ {
				d.canvas.img.Set(x, y, col)
			}
		} else {
			d.canvas.img.Set(x, y1, col)
			d.canvas.img.Set(x, y2, col)
		}
	}
	for y := y1; y <= y2; y++ {
		d.canvas.img.Set(x1, y, col)
		d.canvas.img.Set(x2, y, col)
	}
}

func (d *Drawer) drawLine(x1, y, x2, y2 int, col color.Color) {
	for x := x1; x <= x2; x++ {
		d.canvas.img.Set(x, y, col)
	}
}

func (d *Drawer) drawItemsTable(cart dao.KiranaBazarOrderDetails, y int) int {
	// Draw table header
	d.drawTableHeader(y)
	y += 40

	cartt := cart.Cart
	// json.Unmarshal(cart.Cart, &cartt)

	for i, item := range cartt {
		if i%2 == 0 {
			d.drawRect(20, y-15, d.canvas.width-20, y+15, d.config.Colors.RowGray, true)
		}
		meta := shared.KiranaBazarProductMeta{}
		json.Unmarshal(item.Meta, &meta)

		// Item name with Hindi text
		nameText := fmt.Sprintf("%s (%s)", meta.HindiName, meta.Quantity)
		d.setFont(d.fonts.devanagari, 12)
		d.drawText(nameText, 40, y+5)

		// Numbers with regular font
		d.setFont(d.fonts.regular, 12)
		d.drawText(fmt.Sprintf("%d", item.Quantity), 350, y+5)
		d.drawText(fmt.Sprintf("%d", meta.PackSize), 450, y+5)

		// Draw amounts with Rupee symbol
		d.drawAmount(meta.MRPNumber, 550, y+5)
		d.drawAmount(meta.WholesaleRate, 650, y+5)
		total := float64(item.Quantity) * meta.WholesaleRate * float64(meta.PackSize)
		d.drawAmount(total, 750, y+5)

		y += 30
	}
	return y
}

func (d *Drawer) drawTableHeader(y int) {
	// Header background
	d.drawRect(20, y-20, d.canvas.width-20, y+12, d.config.Colors.HeaderBlue, true)

	// Header text
	d.setFont(d.fonts.bold, 12)
	d.ctx.SetSrc(image.White)

	headers := []struct {
		text string
		x    int
	}{
		{"Item", 40},
		{"Qty", 350},
		{"Pack", 450},
		{"MRP", 550},
		{"Rate", 650},
		{"Total", 750},
	}

	for _, h := range headers {
		d.drawText(h.text, h.x, y)
	}

	d.ctx.SetSrc(image.Black)
}

func (d *Drawer) drawAmount(amount float64, x, y int) {
	// Draw Rupee symbol
	d.setFont(d.fonts.rupee, 12)
	d.drawText(rupeeSymbol, x, y)

	// Draw amount
	d.setFont(d.fonts.bold, 12)
	d.drawText(fmt.Sprintf("%.2f", amount), x+7, y)
}

func (d *Drawer) drawPricing(breakup dao.KiranaBazarOrderDetails, paymentDetails dao.KiranaBazarOrderPayment, y int) int {
	y -= 10
	rightEdge := d.canvas.width - 50
	labelX := d.canvas.width - 250
	fontSize := 15.0

	d.setFont(d.fonts.regular, fontSize)

	//Helper function for pricing rows
	drawPricingRow := func(label string, value float64, isTotal bool) {
		if isTotal {
			d.setFont(d.fonts.bold, fontSize)
		} else {
			d.setFont(d.fonts.regular, fontSize)
		}

		d.drawText(label, labelX, y)
		d.drawRightAlignedAmount(value, rightEdge, y)
		y += 24
	}

	// Draw pricing components
	drawPricingRow("Total Amount", breakup.GetCartValue(), false)

	if breakup.GetChargeValue() != 0 {
		drawPricingRow("Service Charge", breakup.GetChargeValue(), false)
	}

	if breakup.GetDiscountValue() != 0 {
		drawPricingRow("Discount", -1.0*(breakup.GetDiscountValue()), false)
	}
	drawPricingRow("Delivery", 0.00, false)

	// First divider
	y += 5
	d.drawLine(d.canvas.width-260, y, rightEdge, y, color.Black)
	y += 24

	invoiceAmount := breakup.GetOrderValue()
	drawPricingRow("Invoice Amount", invoiceAmount, false)

	advancePaid := paymentDetails.GetAdvancePaid()
	var codAmount float64
	codAmount = invoiceAmount

	if advancePaid != nil {
		drawPricingRow("Advance Paid", -1.0*(*advancePaid), false)
		codAmount = invoiceAmount - *advancePaid
	}

	y += 5
	d.drawLine(d.canvas.width-260, y, rightEdge, y, color.Black)
	y += 24
	//Final COD amount
	drawPricingRow("COD Amount", math.Ceil(codAmount), true)
	return y
}

func (d *Drawer) drawFooter(y int) error {
	y = d.canvas.height - 82
	y += 10
	d.drawLine(20, y, d.canvas.width-20, y, color.Black)
	y += 5

	// Load and draw footer image
	footer, err := d.loadAndScaleLogo(d.config.FooterPath, d.canvas.width-25, 45)
	if err != nil {
		return fmt.Errorf("failed to load footer image: %w", err)
	}

	footerX := (d.canvas.width - footer.Bounds().Dx()) / 2
	footerRect := image.Rect(footerX, y, footerX+footer.Bounds().Dx(), y+footer.Bounds().Dy())
	draw.Draw(d.canvas.img, footerRect, footer, image.Point{}, draw.Over)

	return nil
}

func (d *Drawer) loadAndScaleLogo(path string, maxWidth, maxHeight int) (image.Image, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, fmt.Errorf("failed to open image file: %w", err)
	}
	defer file.Close()

	img, err := png.Decode(file)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	bounds := img.Bounds()
	widthRatio := float64(maxWidth) / float64(bounds.Dx())
	heightRatio := float64(maxHeight) / float64(bounds.Dy())
	ratio := math.Max(widthRatio, heightRatio)

	newWidth := int(float64(bounds.Dx()) * ratio)
	newHeight := int(float64(bounds.Dy()) * ratio)

	return d.resizeImage(img, newWidth, newHeight), nil
}

func (d *Drawer) resizeImage(src image.Image, newWidth, newHeight int) *image.RGBA {
	dst := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))
	for x := 0; x < newWidth; x++ {
		for y := 0; y < newHeight; y++ {
			srcX := x * src.Bounds().Dx() / newWidth
			srcY := y * src.Bounds().Dy() / newHeight
			dst.Set(x, y, src.At(srcX, srcY))
		}
	}
	return dst
}

func (d *Drawer) drawRightAlignedAmount(amount float64, rightEdge, y int) {
	amountStr := fmt.Sprintf("%.2f", amount)
	textWidth := estimateTextWidth(amountStr, 15.0) // Using fontSize 15.0

	// Draw rupee symbol
	d.setFont(d.fonts.rupee, 15.0)
	d.drawText(rupeeSymbol, rightEdge-textWidth-10, y)

	// Draw amount
	d.setFont(d.fonts.regular, 15.0)
	d.drawText(amountStr, rightEdge-textWidth+5, y)
}

// Helper functions for pricing calculations
func getTotalValue(pricing []shared.ProductPrice) float64 {
	totalvalue := 0.0
	for _, p := range pricing {
		value, _ := strconv.ParseFloat(p.TotalValue, 64)
		totalvalue += value
	}
	return totalvalue
}

func getDiscountValue(pricing []shared.DiscountPricing) float64 {
	total := 0.0
	for _, p := range pricing {
		if p.Type == offers.OFFER_TYPES.CHARGE {
			continue
		}
		total += p.TotalValue
	}
	return total * (-1)
}

func getCharges(pricing []shared.DiscountPricing) float64 {
	total := 0.0
	for _, p := range pricing {
		if p.Type == offers.OFFER_TYPES.CHARGE {
			total += p.TotalValue
		}
	}
	return total
}

func getAdvancePaid(pricing []shared.TotalPricing) float64 {
	for _, p := range pricing {
		if p.Key == "advance_paid" {
			return p.TotalValue
		}
	}
	return 0
}

func getAdvancePaidAmount(paymentDetails dao.KiranaBazarOrderPayment) float64 {
	if paymentDetails.Status != nil &&
		*paymentDetails.Status == orderstatus.PARTIALLY_PAID &&
		paymentDetails.PaidAmount != nil &&
		*paymentDetails.PaidAmount > 0 {
		return *paymentDetails.PaidAmount
	}
	return 0.0
}

func calculateInvoiceAmount(breakup dao.BillDetails, pricing []shared.ProductPrice) float64 {
	total := getTotalValue(pricing)
	discount := getDiscountValue(breakup.DiscountPricing)
	serviceCharges := getCharges(breakup.DiscountPricing)
	return total - discount + serviceCharges
}

func estimateTextWidth(text string, fontSize float64) int {
	return int(float64(len(text)) * fontSize * 0.6)
}
