package generator

import (
	"image"
	"image/color"
	"image/draw"
)

// <PERSON><PERSON> represents the image drawing surface
type Canvas struct {
	img    *image.RGBA
	width  int
	height int
	dpi    int
}

// newCanvas creates a new Canvas with the specified dimensions
func newCanvas(width, height, dpi int) *Canvas {
	img := image.NewRGBA(image.Rect(0, 0, width, height))
	draw.Draw(img, img.Bounds(), &image.Uniform{color.White}, image.Point{}, draw.Src)

	return &Canvas{
		img:    img,
		width:  width,
		height: height,
		dpi:    dpi,
	}
}

// Bounds returns the canvas dimensions
func (c *Canvas) Bounds() image.Rectangle {
	return c.img.Bounds()
}
