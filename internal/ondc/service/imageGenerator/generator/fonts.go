package generator

import (
	"fmt"
	"io/ioutil"

	"kc/internal/ondc/service/imageGenerator/config"

	"github.com/golang/freetype/truetype"
)

// Fonts holds loaded font instances
type Fonts struct {
	regular    *truetype.Font
	bold       *truetype.Font
	devanagari *truetype.Font
	rupee      *truetype.Font
}

// loadFonts loads all required fonts from the filesystem
func loadFonts(paths config.FontPaths) (*Fonts, error) {
	regular, err := loadFont(paths.Regular)
	if err != nil {
		return nil, fmt.Errorf("failed to load regular font: %w", err)
	}

	bold, err := loadFont(paths.Bold)
	if err != nil {
		return nil, fmt.Errorf("failed to load bold font: %w", err)
	}

	devanagari, err := loadFont(paths.Devanagari)
	if err != nil {
		return nil, fmt.Errorf("failed to load devanagari font: %w", err)
	}

	rupee, err := loadFont(paths.Rupee)
	if err != nil {
		return nil, fmt.Errorf("failed to load rupee font: %w", err)
	}

	return &Fonts{
		regular:    regular,
		bold:       bold,
		devanagari: devanagari,
		rupee:      rupee,
	}, nil
}

// loadFont loads a single font file
func loadFont(path string) (*truetype.Font, error) {
	fontBytes, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read font file %s: %w", path, err)
	}

	font, err := truetype.Parse(fontBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse font file %s: %w", path, err)
	}

	return font, nil
}
