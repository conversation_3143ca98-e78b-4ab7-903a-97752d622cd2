package generator

import "fmt"

// ErrFontLoad is returned when a font file cannot be loaded
type ErrFontLoad struct {
	FontPath string
	Err      error
}

func (e *ErrFontLoad) Error() string {
	return fmt.Sprintf("failed to load font %s: %v", e.<PERSON><PERSON><PERSON><PERSON>, e.<PERSON>rr)
}

// ErrImageLoad is returned when an image file cannot be loaded
type ErrImageLoad struct {
	ImagePath string
	Err       error
}

func (e *ErrImageLoad) Error() string {
	return fmt.Sprintf("failed to load image %s: %v", e.ImagePath, e.Err)
}

// ErrImageGeneration is returned when there's an error during image generation
type ErrImageGeneration struct {
	Stage string
	Err   error
}

func (e *ErrImageGeneration) Error() string {
	return fmt.Sprintf("error during image generation at %s: %v", e.Stage, e.Err)
}