package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/sqlRepo"
)

func getInitMessage(req *dto.AppInitReq, repo *sqlRepo.Repository) (*dto.InitMessage, error) {
	userAddress := dao.UserAddress{}
	_, err := repo.Find(map[string]interface{}{
		"id": req.Data.AddressID,
	}, &userAddress)
	if err != nil {
		return nil, err
	}
	providerLocation := []dto.OrderProviderLocationsInner{}
	for _, pl := range req.Data.Provider.ProviderLocation {
		providerLocation = append(providerLocation, dto.OrderProviderLocationsInner{
			ID: pl,
		})
	}
	return &dto.InitMessage{
		Order: &dto.Order{
			Provider: &dto.OrderProvider{
				ID:        req.Data.Provider.ProviderID,
				Locations: providerLocation,
			},
			Items: req.Data.Items,
			Billing: &dto.Billing{
				Name:    *userAddress.Name,
				Address: *userAddress.Line,
				State: dto.City{
					Name: *userAddress.State,
				},
				City: dto.City{
					Name: *userAddress.District,
				},
				Phone:     *userAddress.Phone,
				TaxNumber: userAddress.GST,
			},
			Fulfillments: []dto.Fulfillment{
				{
					ID:   "1",
					Type: "Delivery",
					Stops: []dto.Stops{
						{
							Type: "end",
							Location: dto.Location{
								Address:  *userAddress.Line,
								Gps:      fmt.Sprintf("%f,%f", userAddress.Latitude, userAddress.Longitude),
								AreaCode: *userAddress.PostalCode,
								State:    &dto.City{Name: *userAddress.State},
								City:     &dto.City{Name: *userAddress.District},
								Country:  &dto.Country{Code: "IND"},
							},
							Contact: &dto.Contact{Phone: *userAddress.Phone},
						},
					},
				},
			},
			Payment: []dto.Payment{
				dto.Payment{
					Type: "ON-FULFILLMENT",
				},
			},
			Tags: []dto.TagGroup{
				{
					Descriptor: dto.Descriptor{
						Code: "buyer_id",
					},
					List: []dto.Tag{
						{
							Descriptor: dto.Descriptor{Code: "buyer_id_code"},
							Value:      "gst",
						},
						{
							Descriptor: dto.Descriptor{Code: "buyer_id_no"},
							Value:      "dummy_gst_number",
						},
					},
				},
			},
		},
	}, nil
}
func (s *Service) Init(ctx context.Context, req *dto.AppInitReq) (interface{}, error) {
	transactionId := *req.Meta.Context.TransactionID
	msgId := getMessageID()
	identifier := fmt.Sprintf("%s%s", transactionId, msgId)

	initRequestContext, err := getContext(&req.Meta.Context, INIT, msgId)
	if err != nil {
		return nil, err
	}
	initMessage, err := getInitMessage(req, s.repository)
	if err != nil {
		return nil, err
	}
	initRequest := dto.InitRequest{
		Context: initRequestContext,
		Message: initMessage,
	}
	return s.handleONDCInitRequest(ctx, initRequest, identifier, transactionId)
}

func (s *Service) handleONDCInitRequest(ctx context.Context, initRequest dto.InitRequest, identifier, transcationID string) (*dto.AppInitResp, error) {
	adjustedReqJSON, err := json.Marshal(initRequest)
	if err != nil {
		logger.Error(ctx, "Marshal adjusted request failed: %v", err)
		return nil, err
	}
	fmt.Println("INIT req is", string(adjustedReqJSON))

	resp, err := s.syncingONDCRequest(ctx, adjustedReqJSON, identifier, INIT)
	if err != nil {
		logger.Error(ctx, "Creating request failed: %v", err)
		return nil, err
	}

	// send a request to ONDC network
	redisResp, ok := resp.(string)
	if !ok {
		logger.Error(ctx, "not able to typecast the redis resp")
		return nil, fmt.Errorf("not able to typecast the redis resp")
	}
	appInitResp := &dto.AppInitResp{}
	err = json.Unmarshal([]byte(redisResp), appInitResp)
	if err != nil {
		logger.Error(ctx, "not able to unmarshal the redis resp, err is %s", err.Error())
		return nil, err
	}
	appInitResp.Meta.Provider = initRequest.Message.Order.Provider
	appInitResp.Meta.Fulfillment = initRequest.Message.Order.Fulfillments
	appInitResp.Meta.Billing = initRequest.Message.Order.Billing
	appInitResp.Meta.TransactionID = transcationID
	return appInitResp, nil

}

func (s *Service) OnInitReq(ctx context.Context, req *dto.OnInitRequest) error {
	keyIdentifier := fmt.Sprintf("%s%s", *req.Context.TransactionID, *req.Context.MessageID)

	appInitResp := &dto.AppInitResp{
		ProviderLocation: req.Message.Order.ProviderLocation.ID,
		Meta: dto.Meta{
			BppUrl:      req.Context.BppURI,
			BppID:       req.Context.BppID,
			Provider:    req.Message.Order.Provider,
			Items:       req.Message.Order.Items,
			Tags:        req.Message.Order.Tags,
			Payment:     req.Message.Order.Payment,
			Fulfillment: req.Message.Order.Fulfillments,
			Quote:       req.Message.Order.Quote,
			Context:     *req.Context,
		},
	}
	if req.Error != nil && req.Error.Code != nil {
		appInitResp.Error = dto.AppResponseError{
			Code:        req.Error.Code,
			Message:     &req.Error.Message,
			Description: &req.Error.Path,
			Type:        &req.Error.Type,
		}
	}

	marshalledResponse, _ := json.Marshal(appInitResp)
	fmt.Println(string(marshalledResponse))
	responseAsArray := make([]*dto.AppInitResp, 0)
	responseAsArray = append(responseAsArray, appInitResp)
	_, err := s.Cache.Create(ctx, keyIdentifier, appInitResp)
	if err != nil {
		return err
	}
	return nil
}
