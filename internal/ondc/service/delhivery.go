package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"regexp"
	"strconv"
	"strings"
	"time"

	awbMasterDTO "kc/internal/ondc/service/awbMaster/dto"

	tplModels "github.com/Kirana-Club/3pl-go/pkg/models"
)

func checkDelhiveryServiceAbilityFromJson(pincode string) bool {
	value, exists := utils.DELHIVERY_SERVICEABLE_PINCODES_MAP[pincode]
	if !exists {
		return false
	}

	if value.COD == "Y" {
		return true
	}
	return false
}

func (s *Service) CheckDelhiveryServiceAbility(ctx context.Context, oms string, request *dto.CourierServiceAblityAPIRequest) (dto.AppserviceAbilityAPIResponse, error) {
	response := dto.AppserviceAbilityAPIResponse{
		Data: dto.AppServiceAbilityAPIData{
			Servicable: false,
			Message:    "",
		},
	}

	isServiceable := checkDelhiveryServiceAbilityFromJson(request.DeliveryPostCode)
	if !isServiceable {
		return response, nil
	}
	response.Data.Servicable = true
	return response, nil
}

func (s *Service) TrackDelhiveryCourier(ctx context.Context, request *dto.TrackDelhiveryCourierAPIRequest) (*tplModels.TrackingResponse, error) {
	// res, err := delhivery.GetOrderStatus(context.Background(), "apsara_tea", nil, &orderid)
	session, err := s.TPLService.CreateSession(ctx, "delhivery", request.OMS)
	if err != nil {
		return nil, err
	}

	trackingRequest := &tplModels.TrackingRequest{
		Waybills: request.AWBNumber,
		OrderID:  request.OrderID,
	}

	trackingResponse, err := session.TrackShipment(ctx, trackingRequest)
	if err != nil {
		return nil, err
	}

	// Try with "hugs-heavy" if there was an error with "HUGS"
	if trackingResponse.Error != "" && request.OMS == utils.HUGS {
		request.OMS = "hugs-heavy"
		session, err = s.TPLService.CreateSession(ctx, "delhivery", request.OMS)
		if err != nil {
			return nil, err
		}
		trackingResponse, err = session.TrackShipment(ctx, trackingRequest)
		if err != nil {
			return nil, err
		}
	} else if trackingResponse.Error != "" && request.OMS == utils.ZOFF_FOODS {
		request.OMS = "zoff_foods-heavy"
		session, err = s.TPLService.CreateSession(ctx, "delhivery", request.OMS)
		if err != nil {
			return nil, err
		}
		trackingResponse, err = session.TrackShipment(ctx, trackingRequest)
		if err != nil {
			return nil, err
		}
	}

	return trackingResponse, nil
}

func (s *Service) GetAWBNumberFromOrderIDInDelhivery(oms string, orderID string) (string, error) {
	requestBody := &dto.TrackDelhiveryCourierAPIRequest{
		OrderID: []string{orderID},
		OMS:     oms,
	}

	delhiveryResp, err := s.TrackDelhiveryCourier(context.Background(), requestBody)
	if err != nil {
		time.Sleep(10 * time.Second)
		return "", err
	}

	if delhiveryResp.Error != "" {
		return "", fmt.Errorf("error: %s", delhiveryResp.Error)
	}

	if len(delhiveryResp.ShipmentData) == 0 {
		return "", fmt.Errorf("shipmentData is empty")
	}

	awb := delhiveryResp.ShipmentData[0].Shipment.AWB
	if awb == "" {
		return "", fmt.Errorf("aWB not found")
	}
	return awb, nil
}

var accountNameSellerMapping = map[string]string{
	"zoff_foods_heavy":    "zoff_foods",
	"zoff_foods_small":    "zoff_foods",
	"apsara1_surface":     "apsara_tea",
	"sheelz_surface":      "hugs",
	"sheelzindia_surface": "hugs",
	"kiranaclub":          "kiranaclub",
}

func getSellerAccountNameMapping(accountName string) string {
	value, exists := accountNameSellerMapping[accountName]
	if !exists {
		return ""
	}
	return value
}

func ExtractOrderID(input string) uint64 {
	// handling -2 wala issue on delhivery
	// if length is 10 make it 9 by removing last character --  this is temporary TODO: @sanket remove this after 40 days
	if len(input) == 10 {
		input = input[:len(input)-1]
	}

	// Use regex to extract the numeric part after KC/kc prefix and before any suffix
	re := regexp.MustCompile(`(?i).*KC[_-]?0*(\d+)`)
	matches := re.FindStringSubmatch(input)
	if len(matches) > 1 {
		// Convert the matched string to an integer
		orderID, err := strconv.ParseUint(matches[1], 10, 64)
		if err == nil {
			return orderID
		}
	}
	return 0 // Return 0 if no match or conversion error
}

func ConvertToEpochMillis(timestamp string) (int64, error) {
	// Define IST timezone
	ist, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		return 0, fmt.Errorf("failed to load IST timezone: %v", err)
	}

	// Define supported timestamp formats
	formats := []string{
		"2006-01-02T15:04:05.999999", // Original format
		"2006-01-02 15:04:05",        // New format
		"2006-01-02T15:04:05-07:00",  // RFC3339 format with timezone
		"2006-01-02T15:04:05+05:30",  // Specific IST format
	}

	var t time.Time
	var parseErr error

	// Try parsing with each format
	for _, format := range formats {
		if strings.Contains(format, "-07:00") || strings.Contains(format, "+05:30") {
			// For formats with timezone, use time.Parse instead of time.ParseInLocation
			t, parseErr = time.Parse(format, timestamp)
		} else {
			// For formats without timezone, use ParseInLocation with IST
			t, parseErr = time.ParseInLocation(format, timestamp, ist)
		}
		if parseErr == nil {
			break // Successfully parsed
		}
	}

	// If all formats failed, return the last error
	if parseErr != nil {
		return 0, fmt.Errorf("failed to parse timestamp '%s' with any supported format: %v", timestamp, parseErr)
	}

	// Convert to milliseconds
	return t.UnixMilli(), nil
}

// DelhiveryWebhook handles the webhook call from the delhivery
func (s *Service) DelhiveryWebhook(ctx context.Context, request *tplModels.WebhookRequest, accountName string) (interface{}, error) {
	request.AccountName = accountName
	session, err := s.TPLService.CreateSession(ctx, "delhivery", accountName)
	if err != nil {
		slack.SendSlackDebugMessage(fmt.Sprintf("🚨 Delhivery Webhook Error - TPL Session Creation Failed\nAccount: %s\nError: %s", accountName, err.Error()))
		return nil, err
	}

	_, err = session.HandleWebhookRequest(ctx, request)
	if err != nil {
		slack.SendSlackDebugMessage(fmt.Sprintf("🚨 Delhivery Webhook Error - Handle Webhook Request Failed\nAccount: %s\nAWB: %s\nReference: %s\nError: %s", accountName, request.Shipment.AWB, request.Shipment.ReferenceNo, err.Error()))
		return nil, err
	}
	// not accepting the orders which contains rep or which does not contains kc
	if !strings.Contains(strings.ToLower(request.Shipment.ReferenceNo), "kc") || strings.Contains(strings.ToLower(request.Shipment.ReferenceNo), "rep") {
		return "success - non kc order", nil
	}
	go s.AWBMaster.EnqueueAWBSync(request.Shipment.AWB, "Delhivery")
	orderID := ExtractOrderID(request.Shipment.ReferenceNo)
	courier := "Delhivery"
	updatedBy := "WEBHOOK"
	TRUE := true

	var exists bool
	result := s.repository.Db.Select("1").Limit(1).First(&dao.KiranaBazarOrder{}, "id = ?", orderID)
	exists = result.RowsAffected > 0

	if !exists {
		return "success - order not found", nil
	}

	// adding the log data to the awb scans
	logData, err := json.Marshal(request)
	if err != nil {
		slack.SendSlackDebugMessage(fmt.Sprintf("🚨 Delhivery Webhook Error - JSON Marshal Failed\nAccount: %s\nAWB: %s\nReference: %s\nError: %s", accountName, request.Shipment.AWB, request.Shipment.ReferenceNo, err.Error()))
		return nil, err
	}
	s.AWBMaster.CreateAWBScans(context.Background(), string(logData))

	lastStatusUpdated, timeErr := ConvertToEpochMillis(request.Shipment.Status.StatusDateTime)
	if timeErr != nil {
		slack.SendSlackDebugMessage(fmt.Sprintf("🚨 Delhivery Webhook Error - Time Parse Failed\nAccount: %s\nAWB: %s\nReference: %s\nTimestamp: %s\nError: %s", accountName, request.Shipment.AWB, request.Shipment.ReferenceNo, request.Shipment.Status.StatusDateTime, timeErr.Error()))
		lastStatusUpdated = 0 // Use default value
	}

	_, err = s.AWBMaster.Create(context.Background(), awbMasterDTO.CreateAWBRequest{
		AWBNumber:           &request.Shipment.AWB,
		ReferenceID:         &request.Shipment.ReferenceNo,
		OrderID:             &orderID,
		Courier:             &courier,
		Status:              &request.Shipment.Status.Status,
		IsActive:            &TRUE,
		LastStatusUpdatedAt: &lastStatusUpdated,
		AccountName:         &accountName,
		UpdatedBy:           &updatedBy,
		NSLCode:             &request.Shipment.NSLCode,
		StatusType:          &request.Shipment.Status.StatusType,
		Instructions:        &request.Shipment.Status.Instructions,
		StatusCode:          &request.Shipment.NSLCode,
	})
	if err != nil {
		slack.SendSlackDebugMessage(fmt.Sprintf("🚨 Delhivery Webhook Error - AWB Master Create Failed\nAccount: %s\nAWB: %s\nReference: %s\nOrderID: %d\nError: %s", accountName, request.Shipment.AWB, request.Shipment.ReferenceNo, orderID, err.Error()))
		return nil, err
	}

	// fetchAndUpdate primary webhook for the orderiD
	go s.fetchAndUpdatePrimaryWaybill(orderID, "DELHIVERY_WEBHOOK")

	return "success", nil
}

func (s *Service) FetchAndStoreDelhiveryOrderStatus(ctx context.Context, request *dto.TrackDelhiveryCourierAPIRequest) (*tplModels.TrackingResponse, error) {
	trackingResponse, err := s.TrackDelhiveryCourier(ctx, request)
	if err != nil {
		return nil, err
	}

	requestByte, err := json.Marshal(request)
	if err != nil {
		requestByte = []byte(`{"error": "not able to parse the request"}`)
	}
	responseByte, err := json.Marshal(trackingResponse)
	if err != nil {
		responseByte = []byte(`{"error": "not able to parse the response"}`)
	}
	s.repository.Create(&dao.DelhiveryFetchShipmetLogs{
		Request:   requestByte,
		Response:  responseByte,
		CreatedAt: time.Now(),
	})

	return trackingResponse, nil
}

// PushOrderToDelhivery pushes the order to Delhivery for processing
func (s *Service) PushOrderToDelhivery(ctx context.Context, orderID string) (*tplModels.ManifestResponse, error) {
	// Fetch order details
	orderDetails, err := GetOrderDetails(s.repository, orderID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch order details: %w", err)
	}

	// Fetch order seller details
	sellerDetails, err := GetSellerInfo(s.repository, orderDetails.Seller)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch seller details: %w", err)
	}

	pickupDetails := sellerDetails.GetShippingDetails()
	vendorName := strings.ReplaceAll(pickupDetails.VendorName, "'", "")

	orderIDInt, err := strconv.ParseInt(orderID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid order ID: %w", err)
	}
	packageDetails, err := GetPackageDetails(s.repository, orderIDInt)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch package details: %w", err)
	}

	packageDimensions := packageDetails.GetPackageDetails()
	packageCount := len(packageDimensions.Dimensions)
	if packageCount > 5 {
		return nil, fmt.Errorf("package count cannot exceed 5, got %d", packageCount)
	}

	payableAmount := orderDetails.GetCodValue()
	paymentMethod := "COD"
	if payableAmount < 1.0 {
		paymentMethod = "Prepaid"
	}

	var CODAmount float64
	if paymentMethod == "COD" {
		CODAmount = payableAmount
	}

	// Create ManifestRequest
	manifestRequest := &tplModels.ManifestRequest{
		PickupLocation: tplModels.PickupLocation{
			Name: vendorName,
		},

		Shipments: make([]tplModels.Shipment, 0),
	}

	for i := range packageCount {
		// Create Shipment for each package
		shipment := tplModels.Shipment{
			// Reciever details
			Add:         orderDetails.ShippingAddress.Line1 + " " + orderDetails.ShippingAddress.Line2,
			Phone:       *orderDetails.ShippingAddress.Phone,
			Name:        *orderDetails.ShippingAddress.Name,
			Pin:         *orderDetails.ShippingAddress.PostalCode,
			Order:       fmt.Sprintf("KC_%s", orderID),
			PaymentMode: paymentMethod,
			City:        *orderDetails.ShippingAddress.District,
			State:       *orderDetails.ShippingAddress.State,
			Country:     "India",

			Waybill:        "",
			MasterID:       "",
			ReturnAdd:      pickupDetails.Address1 + " " + pickupDetails.Address2,
			ReturnPin:      pickupDetails.Pincode,
			ReturnCity:     pickupDetails.City,
			ReturnState:    pickupDetails.State,
			ReturnCountry:  "India",
			ReturnPhone:    pickupDetails.Phone,
			CODAmount:      CODAmount,
			MPSAmount:      payableAmount,
			TotalAmount:    orderDetails.GetOrderValue(),
			Weight:         packageDimensions.Dimensions[i].Weight,
			Quantity:       "1",
			ProductsDesc:   fmt.Sprintf("%s %d", vendorName, i),
			ShipmentHeight: packageDimensions.Dimensions[i].Height,
			ShipmentLength: packageDimensions.Dimensions[i].Length,
			ShipmentWidth:  packageDimensions.Dimensions[i].Breadth,
			ShippingMode:   "Surface",

			// Seller details
			SellerName: vendorName,
			SellerAdd:  pickupDetails.Address1 + " " + pickupDetails.Address2,
		}

		// Append the shipment to the manifest request
		manifestRequest.Shipments = append(manifestRequest.Shipments, shipment)
	}

	// Create a session with TPLService
	session, err := s.TPLService.CreateSession(ctx, "delhivery", "kiranaclub")
	if err != nil {
		return nil, fmt.Errorf("failed to create session with TPLService: %w", err)
	}

	// Push the manifest request to TPLService
	manifestResponse, err := session.CreateShipment(ctx, manifestRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to create shipment: %w", err)
	}

	return manifestResponse, nil
}

// CancelShipment cancels a shipment
func (s *Service) CancelShipment(ctx context.Context, orderID string) (bool, error) {
	order := dao.KiranaBazarOrderStatus{}
	query := fmt.Sprintf("select * from kiranabazar_order_status where id = %s", orderID)

	_, err := s.repository.CustomQuery(&order, query)
	if err != nil {
		return false, fmt.Errorf("failed to fetch order status: %w", err)
	}

	var awbNumbers []string
	if order.AWBNumbers != nil {
		err = json.Unmarshal(order.AWBNumbers, &awbNumbers)
		if err != nil {
			return false, fmt.Errorf("failed to parse AWB numbers: %w", err)
		}
	}

	if len(awbNumbers) == 0 {
		return false, fmt.Errorf("no AWB numbers found for order ID %s", orderID)
	}

	var provider string
	if order.Courier != nil && strings.Contains(strings.ToLower(*order.Courier), "delhivery") {
		provider = "delhivery"
	} else {
		return false, fmt.Errorf("unsupported courier provider: %s", *order.Courier)
	}

	// Create a session with TPLService
	session, err := s.TPLService.CreateSession(ctx, provider, "kiranaclub")
	if err != nil {
		return false, fmt.Errorf("failed to create session with TPLService: %w", err)
	}

	for _, awbNumber := range awbNumbers {
		cancelRequest := &tplModels.CancelShipmentRequest{
			Waybill: awbNumber,
		}

		_, err := session.CancelShipment(ctx, cancelRequest)
		if err != nil {
			return false, fmt.Errorf("failed to cancel shipment with AWB %s: %w", awbNumber, err)
		}
	}

	return true, nil
}
