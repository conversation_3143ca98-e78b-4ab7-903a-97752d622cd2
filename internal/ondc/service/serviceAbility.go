package service

import (
	"context"
	"fmt"
	"kc/internal/ondc/external/apsaratea"
	"kc/internal/ondc/external/parimal"

	// "kc/internal/ondc/external/cravitos"

	// "kc/internal/ondc/external/icarry"

	"kc/internal/ondc/external/rsbsuperstockist"
	"kc/internal/ondc/external/soothe"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
)

var NON_SERVICEABLE_BRANDS = []string{utils.LOTS}

func (s *Service) CheckServiceAbility(ctx context.Context, request *dto.AppServiceAblityAPIRequest) (*dto.AppserviceAbilityAPIResponse, error) {

	// non serviceable brands check
	if includes(NON_SERVICEABLE_BRANDS, request.Data.Seller) {
		return &dto.AppserviceAbilityAPIResponse{
			Data: dto.AppServiceAbilityAPIData{
				Servicable: false,
			},
		}, nil
	}

	// NUTRAJ is live PAN india
	if request.Data.Seller == utils.NUTRAJ {
		return &dto.AppserviceAbilityAPIResponse{
			Data: dto.AppServiceAbilityAPIData{
				Servicable: true,
			},
		}, nil
	}

	if request.Data.Seller == utils.KIRANACLUB_LOYALTY_REWARDS {
		return &dto.AppserviceAbilityAPIResponse{
			Data: dto.AppServiceAbilityAPIData{
				Servicable: true,
			},
		}, nil
	}

	// global non serviceable pincode check for all sellers
	if ALL_SELLERS_NON_SERVICEABLE_PINCODES[request.DeliveryPostCode] {
		return &dto.AppserviceAbilityAPIResponse{
			Data: dto.AppServiceAbilityAPIData{
				Servicable: false,
			},
		}, nil
	} else {

		if request.Data.Seller == utils.PARIMAL {
			if parimal.PARIMAL_SERVICEABLE_PINCODES[request.DeliveryPostCode] {
				return &dto.AppserviceAbilityAPIResponse{
					Data: dto.AppServiceAbilityAPIData{
						Servicable: true,
					},
				}, nil
			} else {
				return &dto.AppserviceAbilityAPIResponse{
					Data: dto.AppServiceAbilityAPIData{
						Servicable: false,
					},
				}, nil

			}
		}

		if request.Data.Seller == utils.RSB_SUPER_STOCKIST && !rsbsuperstockist.RSB_SUPERSTOCKIST_SERVICEABLE_PINCODES[request.DeliveryPostCode] {
			return &dto.AppserviceAbilityAPIResponse{
				Data: dto.AppServiceAbilityAPIData{
					Servicable: false,
				},
			}, nil
		}

		// if request.Data.Seller == utils.CRAVITOS && !cravitos.CRAVITOS_SERVICEABLE_PINCODES[request.DeliveryPostCode] {
		// 	return &dto.AppserviceAbilityAPIResponse{
		// 		Data: dto.AppServiceAbilityAPIData{
		// 			Servicable: false,
		// 		},
		// 	}, nil
		// }

		if request.Data.Seller == utils.APSARA_TEA && apsaratea.APSARA_TEA_NON_SERVICEABLE_PINCODES[request.DeliveryPostCode] {
			return &dto.AppserviceAbilityAPIResponse{
				Data: dto.AppServiceAbilityAPIData{
					Servicable: false,
				},
			}, nil
		}

		if request.Data.Seller == utils.SOOTHE && soothe.SOOTHE_NON_SERVICEABLE_PINCODES[request.DeliveryPostCode] {
			return &dto.AppserviceAbilityAPIResponse{
				Data: dto.AppServiceAbilityAPIData{
					Servicable: false,
				},
			}, nil
		}

		if request.Data.Seller == utils.KIRANA_CLUB && COMBO_OFFER_NON_SERVICEABLE_USERS[request.UserID] {
			return &dto.AppserviceAbilityAPIResponse{
				Data: dto.AppServiceAbilityAPIData{
					Servicable: false,
				},
			}, nil
		}
		cod := request.COD
		if cod == "" {
			cod = "y"
		} else if cod == "1" {
			cod = "y"
		} else {
			cod = "n"
		}
		serviceAbility, err := s.CheckDelhiveryServiceAbility(ctx, request.Data.Seller, &dto.CourierServiceAblityAPIRequest{
			COD:              request.COD,
			DeliveryPostCode: request.DeliveryPostCode,
			Weight:           request.Weight,
		})
		if err != nil {
			return nil, err
		}
		fmt.Println("from delhivery ", serviceAbility)
		return &serviceAbility, nil
	}
}
