package charges

import (
	"kc/internal/ondc/models/dao"
	userdetails "kc/internal/ondc/service/userDetails"
)

func GetApplicableCharges(
	userID,
	seller string,
	userDetails *userdetails.AsyncResult,
	shippingAddress *dao.UserAddress,
	totalPricingWithoutDiscount float64,
) []AppliedCharge {
	var result []AppliedCharge

	for _, rule := range SELLER_CHARGES_RULES {
		if rule.Seller != seller {
			continue
		}

		for _, chargeRule := range rule.ChargeRuleConfig {
			if !chargeRule.ApplyToAll && !IsUserEligible(chargeRule, userDetails, shippingAddress) && !isOrderChargeable(chargeRule, totalPricingWithoutDiscount) {
				continue
			}

			var chargeMessage *ChargeMessage = nil
			if chargeRule.ChargeMessage != nil {
				chargeMessage = chargeRule.ChargeMessage
				chargeMessage.ChargeAmount = chargeRule.ChargeAmount
				if chargeRule.MaxCartValue != nil {
					chargeMessage.MaxCartValue = *chargeRule.MaxCartValue
					chargeMessage.DiffAmount = *chargeRule.MaxCartValue - totalPricingWithoutDiscount
				}
			}

			result = append(result, AppliedCharge{
				ChargeType:    chargeRule.ChargeType,
				DisplayName:   chargeRule.ChargeDisplayName,
				Amount:        chargeRule.ChargeAmount,
				ChargeMessage: chargeMessage,
			})

			if chargeRule.IsMasterRule {
				break
			}

		}

	}

	return result
}

func isOrderChargeable(rule ChargeRuleConfig, totalPricingWithoutDiscount float64) bool {
	if rule.MaxCartValue != nil {
		return totalPricingWithoutDiscount >= rule.MinCartValue && totalPricingWithoutDiscount <= *rule.MaxCartValue
	}

	return false
}

func IsUserEligible(rule ChargeRuleConfig, userDetails *userdetails.AsyncResult, shippingAddress *dao.UserAddress) bool {
	if shippingAddress == nil {
		return false
	}
	userCohortNames := make([]string, 0)
	if userDetails != nil && userDetails.Data != nil && userDetails.Data.UserDynamicDetails != nil {
		userCohortNames = userDetails.Data.UserDynamicDetails.UserCohortNames
	}

	return contains(rule.States, *shippingAddress.State) ||
		contains(rule.Cities, *shippingAddress.District) ||
		contains(rule.Pincodes, *shippingAddress.PostalCode) ||
		arrayContains(rule.Cohorts, userCohortNames)
}
