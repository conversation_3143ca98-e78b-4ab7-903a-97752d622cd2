package charges

import "kc/internal/ondc/utils"

var SELLER_CHARGES_RULES = []ChargeRule{
	{
		Seller: utils.KIRANA_CLUB,
		ChargeRuleConfig: []ChargeRuleConfig{
			{
				ChargeType:        "delivery",
				ChargeDisplayName: "डिलीवरी चार्ज",
				ChargeAmount:      0,
				ApplyToAll:        true,
				MaxOrdersToCharge: 0,
				MinCartValue:      1,
				CODEligible:       false,
			},
		},
	},
	{
		Seller: utils.MILDEN,
		ChargeRuleConfig: []ChargeRuleConfig{{
			ChargeType:        "delivery",
			ChargeDisplayName: "डिलीवरी चार्ज",
			ChargeAmount:      200,
			ApplyToAll:        false,
			States: []string{
				"Assam",
				"Manipur",
				"Meghalaya",
				"Mizoram",
				"Nagaland",
				"Tripura",
				"Arunachal Pradesh",
				"Himachal Pradesh",
				"Jammu and Kashmir",
				"Ladakh",
				"Andaman and Nicobar Islands",
				"Lakshadweep",
				"Dadra and Nagar Haveli",
				"<PERSON>an and Diu",
			},
			Pincodes: []string{
				"682559", "682553", "682555", "194101", "194202", "194105", "194103", "194104", "194401", "194302", "194109", "194102", "194201", "194404", "194107", "194301", "194106", "396230", "396215", "396210", "396240", "396235", "396193", "396220", "362570", "362520", "362540", "744105", "744101", "744206", "744211", "744204", "744202", "744102", "744301", "744207", "744302", "744104", "744205", "744201",
			},
			MinCartValue: 499,
			CODEligible:  false,
		},
		},
	},
	{
		Seller: utils.CRAVITOS,

		ChargeRuleConfig: []ChargeRuleConfig{{
			ChargeType:        "delivery",
			ChargeDisplayName: "डिलीवरी चार्ज",
			ChargeAmount:      200,
			ApplyToAll:        false,
			States: []string{
				"Assam",
				"Manipur",
				"Meghalaya",
				"Mizoram",
				"Nagaland",
				"Tripura",
				"Arunachal Pradesh",
				"Himachal Pradesh",
				"Jammu and Kashmir",
				"Ladakh",
				"Andaman and Nicobar Islands",
				"Lakshadweep",
				"Dadra and Nagar Haveli",
				"Daman and Diu",
			},
			Pincodes: []string{
				"682559", "682553", "682555", "194101", "194202", "194105", "194103", "194104", "194401", "194302", "194109", "194102", "194201", "194404", "194107", "194301", "194106", "396230", "396215", "396210", "396240", "396235", "396193", "396220", "362570", "362520", "362540", "744105", "744101", "744206", "744211", "744204", "744202", "744102", "744301", "744207", "744302", "744104", "744205", "744201",
			},
			MinCartValue: 499,
			CODEligible:  false,
		},
		},
	},
	{
		Seller: utils.MICHIS,

		ChargeRuleConfig: []ChargeRuleConfig{
			{
				ChargeType:        "delivery",
				ChargeDisplayName: "डिलीवरी चार्ज",
				ChargeAmount:      200,
				ApplyToAll:        false,
				States: []string{
					"Assam",
					"Manipur",
					"Meghalaya",
					"Mizoram",
					"Nagaland",
					"Tripura",
					"Arunachal Pradesh",
					"Himachal Pradesh",
					"Jammu and Kashmir",
					"Ladakh",
					"Andaman and Nicobar Islands",
					"Lakshadweep",
					"Dadra and Nagar Haveli",
					"Daman and Diu",
				},
				Pincodes: []string{
					"682559", "682553", "682555", "194101", "194202", "194105", "194103", "194104", "194401", "194302", "194109", "194102", "194201", "194404", "194107", "194301", "194106", "396230", "396215", "396210", "396240", "396235", "396193", "396220", "362570", "362520", "362540", "744105", "744101", "744206", "744211", "744204", "744202", "744102", "744301", "744207", "744302", "744104", "744205", "744201",
				},
				MinCartValue: 499,
				CODEligible:  false,
			},
		},
	},
	{
		Seller: utils.SOOTHE,

		ChargeRuleConfig: []ChargeRuleConfig{
			{
				ChargeType:        "delivery",
				ChargeDisplayName: "डिलीवरी चार्ज",

				ChargeAmount: 200,
				ApplyToAll:   false,
				States: []string{
					"Assam",
					"Manipur",
					"Meghalaya",
					"Mizoram",
					"Nagaland",
					"Tripura",
					"Arunachal Pradesh",
					"Himachal Pradesh",
					"Jammu and Kashmir",
					"Ladakh",
					"Andaman and Nicobar Islands",
					"Lakshadweep",
					"Dadra and Nagar Haveli",
					"Daman and Diu",
				},
				Pincodes: []string{
					"682559", "682553", "682555", "194101", "194202", "194105", "194103", "194104", "194401", "194302", "194109", "194102", "194201", "194404", "194107", "194301", "194106", "396230", "396215", "396210", "396240", "396235", "396193", "396220", "362570", "362520", "362540", "744105", "744101", "744206", "744211", "744204", "744202", "744102", "744301", "744207", "744302", "744104", "744205", "744201",
				},
				MinCartValue: 499,
				CODEligible:  false,
			},
		},
	},
	{
		Seller: utils.CHUK_DE,
		ChargeRuleConfig: []ChargeRuleConfig{
			{
				ChargeType:        "delivery",
				ChargeDisplayName: "डिलीवरी चार्ज",
				ChargeAmount:      200,
				ApplyToAll:        false,
				States: []string{
					"Assam",
					"Manipur",
					"Meghalaya",
					"Mizoram",
					"Nagaland",
					"Tripura",
					"Arunachal Pradesh",
					"Himachal Pradesh",
					"Jammu and Kashmir",
					"Ladakh",
					"Andaman and Nicobar Islands",
					"Lakshadweep",
					"Dadra and Nagar Haveli",
					"Daman and Diu",
				},
				Pincodes: []string{
					"682559", "682553", "682555", "194101", "194202", "194105", "194103", "194104", "194401", "194302", "194109", "194102", "194201", "194404", "194107", "194301", "194106", "396230", "396215", "396210", "396240", "396235", "396193", "396220", "362570", "362520", "362540", "744105", "744101", "744206", "744211", "744204", "744202", "744102", "744301", "744207", "744302", "744104", "744205", "744201",
				},
				MinCartValue: 0,
				CODEligible:  false,
				IsMasterRule: true,
			},
			// {
			// 	ChargeType:        "delivery",
			// 	ChargeDisplayName: "डिलीवरी चार्ज",
			// 	ChargeAmount:      100,
			// 	ApplyToAll:        false,
			// 	MaxOrdersToCharge: 0,
			// 	MinCartValue:      0,
			// 	CODEligible:       false,
			// 	MaxCartValue:      FloatPtr(1000.00),
			// 	ChargeMessage: &ChargeMessage{
			// 		Message:          "फ्री डिलीवरी पाने के लिए ₹{{amount}} का उत्पाद और जोड़ें।",
			// 		HighlightedTexts: []string{"फ्री डिलीवरी", "₹{{amount}}"},
			// 		IconType: "warning",
			// 	},
			// },
		},
	},
	{
		Seller: utils.BOLAS,

		ChargeRuleConfig: []ChargeRuleConfig{
			{
				ChargeType:        "delivery",
				ChargeDisplayName: "डिलीवरी चार्ज",
				ChargeAmount:      200,
				ApplyToAll:        false,
				States: []string{
					"Assam",
					"Manipur",
					"Meghalaya",
					"Mizoram",
					"Nagaland",
					"Tripura",
					"Arunachal Pradesh",
					"Himachal Pradesh",
					"Jammu and Kashmir",
					"Ladakh",
					"Andaman and Nicobar Islands",
					"Lakshadweep",
					"Dadra and Nagar Haveli",
					"Daman and Diu",
				},
				Pincodes: []string{
					"682559", "682553", "682555", "194101", "194202", "194105", "194103", "194104", "194401", "194302", "194109", "194102", "194201", "194404", "194107", "194301", "194106", "396230", "396215", "396210", "396240", "396235", "396193", "396220", "362570", "362520", "362540", "744105", "744101", "744206", "744211", "744204", "744202", "744102", "744301", "744207", "744302", "744104", "744205", "744201",
				},
				MinCartValue: 0,
				CODEligible:  false,
			},
		},
	},
	{
		Seller: utils.RSB_SUPER_STOCKIST,

		ChargeRuleConfig: []ChargeRuleConfig{
			{
				ChargeType:        "delivery",
				ChargeDisplayName: "डिलीवरी चार्ज",
				ChargeAmount:      200,
				ApplyToAll:        false,
				States: []string{
					"Assam",
					"Manipur",
					"Meghalaya",
					"Mizoram",
					"Nagaland",
					"Tripura",
					"Arunachal Pradesh",
					"Himachal Pradesh",
					"Jammu and Kashmir",
					"Ladakh",
					"Andaman and Nicobar Islands",
					"Lakshadweep",
					"Dadra and Nagar Haveli",
					"Daman and Diu",
				},
				Pincodes: []string{
					"682559", "682553", "682555", "194101", "194202", "194105", "194103", "194104", "194401", "194302", "194109", "194102", "194201", "194404", "194107", "194301", "194106", "396230", "396215", "396210", "396240", "396235", "396193", "396220", "362570", "362520", "362540", "744105", "744101", "744206", "744211", "744204", "744202", "744102", "744301", "744207", "744302", "744104", "744205", "744201",
				},
				MinCartValue: 0,
				CODEligible:  false,
			},
		},
	},
}
