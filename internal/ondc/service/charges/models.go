package charges

type ChargeRuleConfig struct {
	ChargeType        string         `json:"charge_type"`
	ChargeDisplayName string         `json:"charge_display_name"`
	ApplyToAll        bool           `json:"apply_to_all"`
	States            []string       `json:"states,omitempty"`
	Cities            []string       `json:"cities,omitempty"`
	Pincodes          []string       `json:"pincodes,omitempty"`
	Cohorts           []string       `json:"cohorts,omitempty"`
	MaxOrdersToCharge int            `json:"max_orders_to_charge,omitempty"`
	MinCartValue      float64        `json:"min_cart_value,omitempty"`
	MaxCartValue      *float64       `json:"max_cart_value,omitempty"`
	CODEligible       bool           `json:"cod_eligible,omitempty"`
	IsMasterRule      bool           `json:"is_club_master,omitempty"`
	ChargeAmount      float64        `json:"charge_amount"`
	ChargeMessage     *ChargeMessage `json:"charge_message,omitempty"`
}

type ChargeRule struct {
	Seller           string             `json:"seller"`
	ChargeRuleConfig []ChargeRuleConfig `json:"charge_rule_config"`
}

type ChargeMessage struct {
	Message          string                 `json:"message"`
	HighlightedTexts []string               `json:"highlighted_texts"`
	Styles           map[string]interface{} `json:"styles"`
	HighlightStyle   map[string]interface{} `json:"highlight_style"`
	IconType         string                 `json:"icon_type"`
	ChargeAmount     float64                `json:"charge_amount,omitempty"`
	DiffAmount 	  float64                `json:"diff_amount,omitempty"`
	MaxCartValue     float64               `json:"max_cart_value,omitempty"`
}

type AppliedCharge struct {
	ChargeType    string         `json:"charge_type"`
	DisplayName   string         `json:"display_name"`
	Amount        float64        `json:"amount"`
	ChargeMessage *ChargeMessage `json:"charge_message,omitempty"`
}
