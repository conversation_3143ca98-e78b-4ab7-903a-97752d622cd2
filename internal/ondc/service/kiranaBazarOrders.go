package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/external/exotel/ivr"
	ivrexotelintegration "kc/internal/ondc/external/exotel/ivrExotelIntegration"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/infrastructure/payments"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/queue"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/inventory"
	inventoryDao "kc/internal/ondc/service/inventory/models/dao"
	"kc/internal/ondc/service/products"
	"math"
	"slices"
	"sort"
	"strings"

	"kc/internal/ondc/service/cart"
	"kc/internal/ondc/service/charges"
	"kc/internal/ondc/service/coupons"
	"kc/internal/ondc/service/ivrStatus"
	"kc/internal/ondc/service/offers"
	ordervalue "kc/internal/ondc/service/orderBill/orderValue"
	cancelreason "kc/internal/ondc/service/orderReason/cancelReason"
	orderS "kc/internal/ondc/service/orderStatus"
	orderstatus "kc/internal/ondc/service/orderStatus"
	"kc/internal/ondc/service/orderStatus/constants"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	paymentstatus "kc/internal/ondc/service/orderStatus/paymentStatus"
	processingstatus "kc/internal/ondc/service/orderStatus/processingStatus"
	ordertags "kc/internal/ondc/service/orderTags"
	userdetails "kc/internal/ondc/service/userDetails"
	"kc/internal/ondc/utils"
	"strconv"
	"time"

	"math/rand"

	"github.com/Masterminds/semver"
	"github.com/mixpanel/mixpanel-go"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm/clause"
)

var statusMappings = map[string][]string{
	STATUS_ACTIVE: {
		displaystatus.IN_TRANSIT,
		displaystatus.NDR,
		displaystatus.PENDING_CONFIRMATION,
		displaystatus.PLACED,
		displaystatus.SHIPMENT_CREATED,
		displaystatus.CONFIRMED,
	},
	STATUS_DELIVERED: {
		displaystatus.DELIVERED,
	},
	STATUS_CANCELLED: {
		displaystatus.CANCELLED,
		displaystatus.OTHERS,
		displaystatus.RETURNED,
	},
}

var kcfullfillmentSellersNotUsingB2b = []string{"zoff_foods", "go_desi"}

func must(data []byte, err error) []byte {
	if err != nil {
		return []byte("{}")
	}
	return data
}

// Helper function to build status filter
func buildStatusFilter(statusType string) string {
	if statuses, exists := statusMappings[statusType]; exists {
		return " AND ko.display_status IN ('" + strings.Join(statuses, "', '") + "') "
	}
	return ""
}

func getOrderActiveTags(repo *sqlRepo.Repository, orderID string, orderTagIdsChannel chan string) {
	query := fmt.Sprintf(`SELECT GROUP_CONCAT(tag_id) as tag_ids FROM kiranabazar_order_tag_mapping WHERE order_id = %s AND (is_active = 1 or is_active is null) GROUP BY order_id `, orderID)
	var tagIds string
	_, err := repo.CustomQuery(&tagIds, query)
	if err != nil {
		orderTagIdsChannel <- ""
		return
	}
	if tagIds == "" {
		orderTagIdsChannel <- ""
		return
	}
	orderTagIdsChannel <- tagIds
}

func (s *Service) HandleRewardOrder(rewardResponseChannel chan []dto.OrderDetails, userID string, orderID string, limit, offset int) {
	const (
		rewardPrefix   = "KC-REWARD-"
		rewardCategory = "122"
		rewardSeller   = "kiranaclub_rewards"
	)

	// Clean order ID if present
	oid := strings.TrimPrefix(orderID, rewardPrefix)

	// Get rewards data
	rewardResponse, err := s.GetUserRewards(context.Background(), &dto.GetKiranaBazarRewardsRequest{
		UserID: userID,
		Meta: dto.Meta{
			Limit:  limit,
			Offset: offset,
		},
		Data: dto.KiranaBazarRewardsData{
			RewardID: oid,
		},
	})
	if err != nil || len(rewardResponse.Data) == 0 {
		rewardResponseChannel <- []dto.OrderDetails{}
		return
	}

	response := make([]dto.OrderDetails, 0, len(rewardResponse.Data))
	for _, reward := range rewardResponse.Data {
		// Parse shipping address
		var shippingAddress dao.UserAddress
		if addressBytes, err := json.Marshal(reward.Address); err == nil {
			json.Unmarshal(addressBytes, &shippingAddress)
		}

		// Format dates
		hindiDate := fmt.Sprintf("%s %d, %d", hindiMonthsMap[int(reward.CreatedAt.Month())], reward.CreatedAt.Day(), reward.CreatedAt.Year())
		timeString := reward.CreatedAt.Format("3:04 PM 02-01-2006")

		// Parse image URLs
		var imageUrls []string
		if imgBytes, err := json.Marshal(reward.ImageUrls); err == nil {
			json.Unmarshal(imgBytes, &imageUrls)
		}

		// Create seller items
		sellerItem := []shared.SellerItems{{
			SellerItemsData: shared.SellerItemsData{
				ParentItemID: fmt.Sprintf("%d", reward.ProductID),
				ImageUrls:    imageUrls,
				Seller:       rewardSeller,
				ID:           fmt.Sprintf("%d", reward.ProductID),
				Name:         reward.Name,
				CategoryIds:  []string{rewardCategory},
				Meta:         reward.Meta,
				Type:         33,
				Price:        shared.Price{},
				ProviderID:   rewardSeller,
			},
		}}

		// Create order details
		orderDetails := dao.KiranaBazarOrderDetails{
			Cart:            sellerItem,
			ShippingAddress: shippingAddress,
			BillBreakUp: dao.BillDetails{
				ProductsPricing: []shared.ProductPrice{{
					ProductName: reward.Name,
					Key:         reward.Name,
					Value:       "x1",
				}},
				TotalPricing: []shared.TotalPricing{{
					Key:   "टोटल",
					Value: "FREE",
				}},
			},
		}

		tentativeDeliveryDate := reward.TentativeDeliveryDate.Format("2006-01-02")
		response = append(response, dto.OrderDetails{
			ID:                fmt.Sprintf("%s%d", rewardPrefix, reward.ID),
			OrderStatus:       reward.OrderStatus,
			OrderDate:         reward.CreatedAt,
			OrderDateString:   hindiDate,
			OrderTimeString:   timeString,
			OrderDetails:      must(json.Marshal(orderDetails)),
			TotalAmountString: "FREE",
			Seller:            rewardSeller,
			Style:             getOrderStyleFromStatus(reward.DisplayStatus),
			PaymentStatus:     "PAID",
			DisplayStatus:     "displayStatus",
			ProcessingStatus:  "processing status",
			DeliveryStatus:    "delivery Status",
			OrderPayment: dto.OrderPayment{
				PaymentID:     "1",
				PaymentMethod: "CASH",
				PaymentStatus: "PAID",
			},
			OrderStatusDetails: &dto.KiranaBazarOrderStatus{
				TentativeDeliveryDate: &tentativeDeliveryDate,
			},
			TrackingLink: "https://trackinglink.com",
		})
	}

	rewardResponseChannel <- response
}

func (s *Service) GetDisplayStatusCountForUserId(userId string, resultChan chan<- dto.DisplayStatusResult) {
	defer close(resultChan)

	query := fmt.Sprintf(`
						select
							ko.display_status,
							COUNT(*) as order_count
						from
							kiranabazar_orders ko
						join kiranabazar_order_payments kop on
							ko.id = kop.order_id
						join kiranabazar_order_details kod on
							kod.order_id = ko.id
						where
							1 = 1
							and ko.user_id = '%s'
						group by
							ko.display_status
						`, userId)

	var displayStatusCount []dto.DisplayOrderStatusCount
	_, err := s.repository.CustomQuery(&displayStatusCount, query)
	if err != nil {
		resultChan <- dto.DisplayStatusResult{Data: nil, Err: err}
		return
	}

	var activeOrderCount int = 0
	var deliveredOrderCount int = 0
	var cancelledOrderCount int = 0

	for _, status := range displayStatusCount {
		if status.DisplayStatus == displaystatus.DELIVERED {
			deliveredOrderCount += status.DisplayCount
		} else if status.DisplayStatus == displaystatus.CANCELLED || status.DisplayStatus == displaystatus.RETURNED || status.DisplayStatus == displaystatus.OTHERS {
			cancelledOrderCount += status.DisplayCount
		} else if status.DisplayStatus == displaystatus.IN_TRANSIT ||
			status.DisplayStatus == displaystatus.NDR ||
			status.DisplayStatus == displaystatus.PENDING_CONFIRMATION ||
			status.DisplayStatus == displaystatus.PLACED ||
			status.DisplayStatus == displaystatus.SHIPMENT_CREATED ||
			status.DisplayStatus == displaystatus.CONFIRMED {
			activeOrderCount += status.DisplayCount
		}
	}

	var result []dto.DisplayStatusCount

	if activeOrderCount > 0 {
		result = append(result, dto.DisplayStatusCount{Label: "ऐक्टिव", Value: "ACTIVE", Count: activeOrderCount})
	}
	if deliveredOrderCount > 0 {
		result = append(result, dto.DisplayStatusCount{Label: "डिलीवर्ड", Value: "DELIVERED", Count: deliveredOrderCount})
	}
	if cancelledOrderCount > 0 {
		result = append(result, dto.DisplayStatusCount{Label: "कैंसल्ड", Value: "CANCELLED", Count: cancelledOrderCount})
	}

	resultChan <- dto.DisplayStatusResult{Data: result, Err: nil}
}

func (s *Service) GetKiranaBazarOrder(ctx context.Context, request dto.AppGetKiranaBazarOrderRequest) (response dto.AppKiranaBazarOrderResponse, err error) {
	rewardInfoChannel := make(chan []dto.OrderDetails, 1)
	orderTagIdsChannel := make(chan string, 1)

	displayStatusResultChannel := make(chan dto.DisplayStatusResult, 1)
	if request.UserID != "" {
		go s.GetDisplayStatusCountForUserId(request.UserID, displayStatusResultChannel)
	}

	requestSource := "APP"
	if request.Meta.AppVersion == "" {
		requestSource = "B2B"
	}

	if strings.Contains(request.Data.OrderID, "KC-REWARD") {
		go s.HandleRewardOrder(rewardInfoChannel, request.UserID, request.Data.OrderID, request.Meta.Limit, request.Meta.Offset)
		request.Data.OrderID = "0"
	} else if request.Data.OrderID == "" {
		go s.HandleRewardOrder(rewardInfoChannel, request.UserID, request.Data.OrderID, request.Meta.Limit, request.Meta.Offset)
	} else {
		rewardInfoChannel <- []dto.OrderDetails{}
	}

	if request.UserID == "" && request.Data.OrderID != "" {
		// request from b2b dashboard, get order tags
		getOrderActiveTags(s.repository, request.Data.OrderID, orderTagIdsChannel)
	} else {
		orderTagIdsChannel <- ""
	}

	type kiranaBazarOrder struct {
		dao.KiranaBazarOrder
		PaymentID            string          `json:"payment_id"`
		PaymentMethod        string          `json:"payment_method"`
		PaymentAmount        float64         `json:"payment_amount"`
		PaymentStatus        string          `json:"payment_status"`
		PaymentMeta          datatypes.JSON  `json:"payment_meta"`
		PaidAmount           *float64        `json:"paid_amount"`
		OrderDetails         datatypes.JSON  `json:"order_details"`
		TrackingLink         string          `json:"tracking_link"`
		AwbNumber            *string         `json:"awb_number"`
		Courier              *string         `json:"courier"`
		OrderStatusDetails   *datatypes.JSON `json:"order_status_details"`
		ExpectedDeliveryDate *time.Time      `json:"expected_delivery_date"`
		CallStatus           string          `json:"call_status"`
		Note                 string          `json:"note"`
		CancelReason         string          `json:"cancel_reason"`
		ReturnedReason       string          `json:"returned_reason"`
		TagIds               string          `json:"tag_ids"`
		IvrStatus            string          `json:"ivr_status"`
		OrderActivityID      string          `json:"order_activity_id"`
		AssignedTo           string          `json:"assigned_to"`
		GatewayTransactionId string          `json:"gateway_transaction_id"`
		GatewayPaymentAmount float64         `json:"gateway_payment_amount"`
		GatewayPaidAmount    float64         `json:"gateway_paid_amount"`
		GatewayStatus        string          `json:"gateway_status"`
		OrderAmount          float64         `json:"gateway_order_amount"`
		PaymentUpdatedAt     time.Time       `json:"payment_updated_at"`
		Picklist             string          `json:"picklist"`
		PrintingLabel        string          `json:"printing_label"`
		ExtInvoice           string          `json:"ext_invoice"`
		ExtInvoiceNumber     string          `json:"ext_invoice_number"`
		PushToOMS            bool            `json:"push_to_oms,omitempty"`
		OrderConfirmedTs     int64           `json:"order_confirmed_ts"`
		OrderDelivered       int64           `json:"order_delivered,omitempty"`
		TicketStatus         string          `json:"ticket_status"`
		RefundAmount         float64         `json:"refund_amount"`
		RefundStatus         string          `json:"refund_status"`
	}
	kbo := []kiranaBazarOrder{}
	isExternal := request.UserID == ""

	if isExternal && request.Data.OrderID == "" {
		err = fmt.Errorf("user_id or order_id is required")
		return
	}
	query := `
        select
			distinct ko.id,
			ko.*,
			kop.id as payment_id,
			kop.payment_method as payment_method,
			kop.amount as payment_amount,
			kop.status as payment_status,
			kop.payment_meta as payment_meta,
			kod.order_details as order_details,
			kos.courier as courier,
			kos.awb_number as awb_number,
			kos.expected_delivery_date as expected_delivery_date,
			kno.order_activity_id as order_activity_id,
			kno.assigned_to as assigned_to,
			kpg.gateway_transaction_id as gateway_transaction_id,
			kpg.payment_amount as gateway_payment_amount,
			kpg.paid_amount as gateway_paid_amount,
			kpg.gateway_status as gateway_status,
			kpg.order_amount as order_amount,
			kpg.updated_at as payment_updated_at,
			kbr.order_confirmed as order_confirmed_ts, 
			kbr.order_delivered as order_delivered,
			ct.status as ticket_status,
			kor.amount as refund_amount,
			kor.status as refund_status
			`
	// if api request from b2b dashboard
	if isExternal {
		query += `,
			kop.paid_amount as paid_amount,
			kcs.call_status as call_status,
			kcs.note as note,
			kcs.cancel_reason as cancel_reason,
			kod.picklist,
			kod.printing_label,
			kop.ext_invoice,
			kop.ext_invoice_number,
			kcs.returned_reason as returned_reason  `
		if request.Data.OrderID != "" {
			query += ` , kois.ivr_status `
		}
	}

	query += `
		from
			kiranabazar_orders ko
		join kiranabazar_order_payments kop on
			ko.id = kop.order_id
		left join kiranabazar_order_status kos on
			kos.id = ko.id
		join kiranabazar_order_details kod on
			kod.order_id = ko.id 
		left join kiranabazar_ndr_orders kno on
			kno.order_id = ko.id
		left join kiranabazar_payment_gateway_orders kpg on kpg.kc_order_id = ko.id
		left join kc_bazar_reconciliation kbr ON ko.id = kbr.order_id 
		left join cs_tickets ct ON ko.id = ct.order_id
		left join kiranabazar_order_refunds kor on ko.id = kor.order_id
		`
	if isExternal {
		query += `
		left join kiranabazar_call_status kcs on kcs.order_id = ko.id `
		if request.Data.OrderID != "" {
			query += ` LEFT JOIN kiranabazar_orders_ivr_status kois ON ko.id = kois.order_id `
		}
	}

	query += ` where 1=1`

	if !isExternal {
		query += " AND ko.user_id = '" + request.UserID + "' "
	}
	// if isExternal {
	// 	query += " AND (otm.is_active = 1 OR  otm.is_active is null) "
	// }
	if request.RequestSource == "B2B_EXTERNAL" {
		query += " AND  kbr.order_confirmed is not null "
	}
	if request.Data.Seller != "" {
		query += " AND ko.seller = '" + request.Data.Seller + "' "
	}
	if request.Meta.Limit == 0 {
		request.Meta.Limit = 10
	}

	var targetStatus string
	userAppVersion, _ := semver.NewVersion(request.Meta.AppVersion)
	if request.UserID != "" {
		orderMetaChangeAppVersion, _ := semver.NewVersion("6.5.5")

		displayStatuscount := <-displayStatusResultChannel

		if displayStatuscount.Err != nil {
			return response, displayStatuscount.Err
		}

		response.OrderStatusMeta = displayStatuscount.Data

		if request.Meta.DisplayStatus != "" {
			targetStatus = request.Meta.DisplayStatus
		} else if request.Meta.AppVersion != "" && len(displayStatuscount.Data) > 0 && request.Data.OrderID == "" {
			targetStatus = displayStatuscount.Data[0].Value
		}

		if targetStatus != "" && userAppVersion != nil && userAppVersion.GreaterThan(orderMetaChangeAppVersion) {
			query += buildStatusFilter(targetStatus)
		}
	}

	if request.Data.OrderID != "" {
		// if isExternal {
		// 	// add here all the select columns
		// 	query += " AND ko.id = " + request.Data.OrderID + ` GROUP BY
		//    ko.id, kop.id, kop.payment_method, kop.amount, kop.status, kop.payment_meta,
		//    kod.order_details, kos.expected_delivery_date, kop.paid_amount,
		//    kcs.call_status, kcs.note, kcs.cancel_reason, kois.ivr_status,
		//    kpg.gateway_transaction_id, kpg.payment_amount, kpg.paid_amount, kpg.gateway_status, kpg.order_amount, kpg.updated_at `
		// } else {
		// 	query += " AND ko.id = " + request.Data.OrderID
		// }

		query += fmt.Sprintf(" AND ko.id = %s order by created_at desc ", request.Data.OrderID)

	} else {
		query += " order by created_at desc LIMIT " + strconv.Itoa(request.Meta.Limit) + " OFFSET " + strconv.Itoa(request.Meta.Offset)
	}
	_, err = s.repository.CustomQuery(&kbo, query)
	if err != nil {
		return
	}

	orderTagIds := <-orderTagIdsChannel

	if len(kbo) > 0 {
		for _, j := range kbo {
			orderDetails := dao.KiranaBazarOrderDetails{}
			err = json.Unmarshal(j.OrderDetails, &orderDetails)
			if err != nil {
				return
			}
			// actualExpectedDeliveryDate := j.ExpectedDeliveryDate
			// easyEcomOrderDetails := dto.EasyEcomGetOrderDetailsResponse{}
			var orderStatusDetails *dto.KiranaBazarOrderStatus = nil
			if (j.OrderStatusDetails != nil) || (j.AwbNumber != nil) {
				orderStatusDetails = &dto.KiranaBazarOrderStatus{}
				// if j.OrderStatusDetails != nil {
				// 	err = json.Unmarshal(*j.OrderStatusDetails, &easyEcomOrderDetails)
				// 	if err == nil {
				// 		if len(easyEcomOrderDetails.Data) > 0 && !compareStatus(*j.OrderStatus, "CANCELLED") {
				// 			if easyEcomOrderDetails.Data[0].Documents.Label != "" {
				// 				orderStatusDetails.Documents = &easyEcomOrderDetails.Data[0].Documents
				// 			}

				// 			emptyExpectedDeliveryDate := "-"
				// 			orderStatusDetails.TentativeDeliveryDate = &emptyExpectedDeliveryDate
				// 			if actualExpectedDeliveryDate != nil {
				// 				expectedDeliveryDate, err := formatDateTime(actualExpectedDeliveryDate.Format("2006-01-02 15:04:05"))
				// 				if err == nil {
				// 					orderStatusDetails.TentativeDeliveryDate = &expectedDeliveryDate
				// 				}
				// 			}

				// 			orderStatusDetails.ShippingHistory = GetShippingOrOrderHistory(easyEcomOrderDetails.Data[0])
				// 			orderStatusDetails.EasyEcomOrderHistory = easyEcomOrderDetails.Data[0].EasyecomOrderHistory
				// 			orderStatusDetails.OrderID = j.ID
				// 			orderStatusDetails.Courier = easyEcomOrderDetails.Data[0].Courier
				// 			orderStatusDetails.AWBNumber = easyEcomOrderDetails.Data[0].AWBNumber
				// 		}
				// 	}
				// }
				orderStatusDetails.Courier = *j.Courier
				orderStatusDetails.AWBNumber = *j.AwbNumber

			}

			// @sanket here is the logic to control the cancel
			isCancellable := false
			var cancelOrderMetaDetails *dto.CancelOrderMeta = nil
			orderstatues := orderS.MapOrderStatus(*j.OrderStatus, "", orderS.OrderStatusResponse{})
			if orderstatues.ProcessingStatus == processingstatus.PLACED || orderstatues.ProcessingStatus == displaystatus.PENDING_CONFIRMATION {
				var cancelOrderMeta dto.CancelOrderMeta
				err := json.Unmarshal([]byte(utils.CancelOrderMetaData), &cancelOrderMeta)
				if err != nil {
					return response, err
				}
				cancelOrderMetaDetails = &cancelOrderMeta
				isCancellable = true
			}

			var paidAmountProof []string = []string{}
			var advanceTaken bool
			var orderTags []string

			if j.TicketStatus != "" {
				orderTags = append(orderTags, fmt.Sprintf("TICKET-%s", j.TicketStatus))
			}

			if isExternal {
				if j.PaymentMeta != nil {
					var paymentMeta map[string]interface{}
					if err := json.Unmarshal([]byte(j.PaymentMeta), &paymentMeta); err == nil {
						if proof, ok := paymentMeta["paid_amount_proof"].([]interface{}); ok {
							for _, p := range proof {
								if proofMap, ok := p.(map[string]interface{}); ok {
									// Extract the string value from the map, assuming the key is "proof"
									if proofValue, ok := proofMap["url"].(string); ok {
										paidAmountProof = append(paidAmountProof, proofValue)
									}
								} else if proofValue, ok := p.(string); ok {
									// Handle the case where p is directly a string
									paidAmountProof = append(paidAmountProof, proofValue)
								}
							}
						}
						if taken, ok := paymentMeta["advance_taken"].(bool); ok {
							advanceTaken = taken
						}
					}
				}
				if orderTagIds != "" {
					j.TagIds = orderTagIds
					orderTagIds := strings.Split(j.TagIds, ",")
					for _, tagId := range orderTagIds {
						tagId = strings.TrimSpace(tagId)
						intTagId, err := strconv.Atoi(tagId)
						if err != nil {
							return response, err
						}
						tag := ordertags.GetTagFromTagId(intTagId)
						orderTags = append(orderTags, strings.ToUpper(tag.Tag))
					}
				}
			}

			hindiDateString := fmt.Sprintf("%s %d, %d", hindiMonthsMap[int(j.CreatedAt.Month())], j.CreatedAt.Day(), j.CreatedAt.Year())
			timeString := j.CreatedAt.Format("3:04 PM 02-01-2006")
			totalAmountString := fmt.Sprintf("₹%.2f", orderDetails.TotalAmount)
			orderStatus := *j.OrderStatus
			if request.UserID != "" {
				orderStatus = j.DisplayStatus
			}

			orderPaymentData := dto.OrderPayment{
				PaymentID:            j.PaymentID,
				PaymentMethod:        j.PaymentMethod,
				PaymentTransactionID: *j.TransactionID,
				Amount:               j.PaymentAmount,
				PaymentStatus:        j.PaymentStatus,
			}
			allowPayment := false
			if (j.AwbNumber != nil && len(*j.AwbNumber) > 0) || j.DisplayStatus == "CANCELLED" {
				isCancellable = false
				allowPayment = false
			}
			var paymentMethodMeta dto.WaysToPayDataV2
			if j.GatewayTransactionId != "" && (j.DisplayStatus == constants.PLACED || j.DisplayStatus == constants.PENDING_CONFIRMATION) {
				updatedPaymentStatus := s.MapPaymentStatus(j.GatewayStatus, &j.PaymentUpdatedAt)
				style := s.GetPaymentStatusStyle(updatedPaymentStatus, request.Meta.Platform, request.Data.OrderID)
				orderPaymentData.PaymentStatus = updatedPaymentStatus
				orderPaymentData.StatusMeta = &style
				orderPaymentData.PaymentOrderID = &j.GatewayTransactionId
				orderPaymentData.PaymentAmount = &j.GatewayPaymentAmount
				orderPaymentData.PaymentPaidAmount = &j.GatewayPaidAmount

				if updatedPaymentStatus == "failed" {
					allowPayment = true
				} else {
					allowPayment = false
				}

				if j.GatewayPaymentAmount != j.OrderAmount {
					paymentMethodMeta = utils.PaymentMethodsMap[constants.PARTIALLY_PAID]
				} else {
					paymentMethodMeta = utils.PaymentMethodsMap[constants.FULLY_PAID]
				}
			}

			if j.GatewayTransactionId == "" {
				allowPayment = false
				paymentMethodMeta = utils.PaymentMethodsMap[constants.COD]
			} else {
				if orderPaymentData.PaymentPaidAmount != nil && *orderPaymentData.PaymentPaidAmount > 0 {
					isCancellable = false
				}
			}

			if (request.Data.OrderID != "") && (j.DisplayStatus == displaystatus.CONFIRMED) && (j.OrderConfirmedTs != 0 && ((time.Now().UnixMilli() - j.OrderConfirmedTs) > 172800000)) {
				style := s.GetPaymentStatusStyle(payments.StatusDelayedDispatch, request.Meta.Platform, request.Data.OrderID)
				orderPaymentData.StatusMeta = &style
				orderPaymentData.PaymentStatus = payments.StatusDelayedDispatch
			}

			orderPaymentData.PaymentMethodMeta = &paymentMethodMeta
			pushToOMS := false
			var packageDetails *shared.PackageDetails
			if request.Data.Seller == utils.KIRANACLUB_LOYALTY_REWARDS && (includes([]string{"<EMAIL>", "<EMAIL>"}, request.Data.Email)) {
				pushToOMS, packageDetails, err = parseOrderForLoyaltyRewards(j.OrderDetails)
				if err != nil {
					return
				}
			} else if request.Data.Seller == utils.KIRANACLUB_LOYALTY_REWARDS {
				pushToOMS, packageDetails, err = parseOrderForLoyaltyRewards(j.OrderDetails)
				if err != nil {
					return
				}
			} else if slices.Contains(brands.GetKcFullFilledBrands(), j.Seller) {
				pushToOMS = true
			}

			var brandName string = ""
			var brandLogo string = ""

			brandData, exists := brands.GetBrandMetaBySeller(j.Seller)

			if exists {
				brandName = brandData.Name
				brandLogo = brandData.Logo
			}

			showUpdatedProductStatus := (request.RequestSource == "B2B_INTERNAL" || request.RequestSource == "B2B_EXTERNAL") && includes([]string{displaystatus.PLACED, displaystatus.CONFIRMED, displaystatus.PENDING_CONFIRMATION}, j.DisplayStatus)
			if showUpdatedProductStatus {
				for ind, cartItem := range orderDetails.Cart {
					productID := cartItem.ID
					productIDInt, err := strconv.ParseUint(productID, 10, 64)
					if err == nil {
						product, exists := products.GetProduct(uint(productIDInt))
						if exists {
							orderDetails.Cart[ind].IsOos = product.IsOOS
							orderDetails.Cart[ind].IsActive = *product.IsActive
						}
					}
				}
				orderDetailsJson, err := json.Marshal(orderDetails)
				if err == nil {
					j.OrderDetails = orderDetailsJson
				}
			}

			var allowedActions []string
			if request.RequestSource == "B2B_INTERNAL" {
				courier := ""
				if j.Courier != nil {
					courier = *j.Courier
				}
				allowedActions = s.GetOrderActions(context.Background(), j.DisplayStatus, j.Seller, courier, request.Email)
			}

			response.Data = append(response.Data, dto.OrderDetails{
				ID:                 fmt.Sprint(*j.ID),
				TransactionID:      *j.TransactionID,
				MessageID:          *j.MessageID,
				OrderStatus:        orderStatus,
				PaymentStatus:      j.PaymentStatus,
				ConfirmCod:         s.getConfirmCodValue(j.UserID, j.Seller, orderDetails, j.DisplayStatus),
				DisplayStatus:      j.DisplayStatus,
				ProcessingStatus:   j.ProcessingStatus,
				DeliveryStatus:     j.DeliveryStatus,
				OrderPayment:       orderPaymentData,
				AllowPayment:       allowPayment,
				OrderStatusDetails: orderStatusDetails,
				OrderDate:          j.CreatedAt,
				OrderDateString:    hindiDateString,
				OrderTimeString:    timeString,
				OrderDetails:       j.OrderDetails,
				TotalItems:         orderDetails.TotalItems,
				TotalAmount:        orderDetails.TotalAmount,
				TotalAmountString:  totalAmountString,
				TrackingLink:       j.TrackingLink,
				IsCancellable:      isCancellable,
				CancelOrderMeta:    cancelOrderMetaDetails,
				Seller:             j.Seller,
				Style:              getOrderStyleFromStatus(j.DisplayStatus),
				PaidAmount:         j.PaidAmount,
				AdvanceTaken:       advanceTaken,
				PaidAmountProof:    paidAmountProof,
				Note:               j.Note,
				CallStatus:         j.CallStatus,
				CancelReason:       j.CancelReason,
				ReturnedReason:     j.ReturnedReason,
				OrderTags:          orderTags,
				IvrStatus:          j.IvrStatus,
				OrderActivityID:    j.OrderActivityID,
				AssignedTo:         j.AssignedTo,
				PrintingLabel:      &j.PrintingLabel,
				Picklist:           &j.Picklist,
				ExtInvoice:         &j.ExtInvoice,
				PushToOMS:          &pushToOMS,
				Info:               getOrderInfoForCancelledOrders(targetStatus, j.RefundAmount, j.RefundStatus),
				PackgeDetails:      packageDetails,
				BrandLogoUrl:       brandLogo,
				BrandName:          brandName,
				StatusDescription:  orderstatus.GetStatusDescription(j.DisplayStatus),
				Cta:                getDynamicCTAFromStatus(targetStatus, *j.ID, j.Seller, request.Data.OrderID),
				DeliveryStatusInfo: getDeliveryStatusInfo(j.OrderDelivered),
				AllowedActions:     allowedActions,
			})
		}
		response.Meta = dto.Meta{
			Limit:  request.Meta.Limit,
			Offset: request.Meta.Offset + request.Meta.Limit,
		}
	} else {
		response.Data = make([]dto.OrderDetails, 0)
	}

	rewards := <-rewardInfoChannel
	if len(rewards) > 0 {
		response.Data = append(response.Data, rewards...)
	}

	// TODO: @sanket this is temporary please write it properly
	if len(response.Data) == 1 && response.Data[0].OrderStatusDetails != nil && response.Data[0].OrderStatusDetails.AWBNumber != "" {
		awbData, _ := s.AWBMaster.GetAWBScans(context.Background(), response.Data[0].OrderStatusDetails.AWBNumber)
		if awbData != nil {
			tentativeDeliveryDate, _ := convertToIST(awbData.PromisedDeliveryDate, request.Data.OrderID, userAppVersion)
			shippingHistory := []dto.ShippingHistory{}
			returned := false
			for _, scan := range awbData.Scans {
				if scan.Type == "RT" || strings.Contains(strings.ToUpper(scan.Type), "RETURNED") || strings.Contains(strings.ToUpper(scan.Type), "RTO") {
					returned = true
					break
				}
				if slices.Contains([]string{"ST-102", "ST-114", "EOD-111", "EOD-6", "X-SC", "EOD-11", "EOD-43", "DLYDC-101", "DLYRG-125", "DLYLH-152", "EOD-37", "EOD-38", "DLYB2B-101", "EOD-3", "DLYHD-007", "EOD-74", "ST-108", "ST-120", "DLYDC-102", "DLYDC-107", "DLYLH-126", "X-DDD3FD", "ST-105", "ST-107", "DLYRG-130", "DLYMPS-101", "X-PPOM", "DOFF-128", "X-ILL2F", "DLYLH-151", "DLYLH-105", "shipment_pickup_complete", "received", "shipment_out_for_delivery", "received_at_dh", "shipment_delivered", "delivered", "in_transit", "shipment_created", "out_for_delivery"}, scan.Code) {
					status, color := mapCodeToStatus(scan.Code)
					if status != "" {
						time, timeString := convertToIST(scan.Time, request.Data.OrderID, userAppVersion)
						shippingHistory = append(shippingHistory, dto.ShippingHistory{
							Status:      status,
							Time:        time,
							DisplayTime: timeString,
							Location:    scan.Location,
							Color:       &color,
						})
					}
				}
			}
			ReverseInPlace(shippingHistory)
			response.Data[0].OrderStatusDetails.ShippingHistory = shippingHistory
			if tentativeDeliveryDate != "" {
				response.Data[0].OrderStatusDetails.TentativeDeliveryDate = &tentativeDeliveryDate
			}
			if returned {
				response.Data[0].OrderStatusDetails.ShippingHistory = []dto.ShippingHistory{}
			}

		}
	}

	sort.Slice(response.Data, func(i, j int) bool {
		return response.Data[i].OrderDate.After(response.Data[j].OrderDate)
	})

	structureOrderObjectForApp(&response.Data, requestSource)
	return
}

func (s *Service) getConfirmCodValue(userId *string, seller string, orderDetails dao.KiranaBazarOrderDetails, displayStatus string) bool {

	if userId == nil || *userId == "" || seller == "" {
		return false
	}

	cartValue := orderDetails.GetCartValue()
	paymentSection, _, _, err := s.GetPaymentMethods(*userId, seller, cartValue, "6.6.0")

	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("Error getting payment methods for user %s and seller %s: %v", *userId, seller, err))
		return false
	}

	isCod := false

	for _, paymentMethod := range paymentSection.Section1 {
		if paymentMethod.Type == "COD" {
			isCod = true
		}
	}

	if !isCod {
		for _, paymentMethod := range paymentSection.Section2 {
			if paymentMethod.Type == "COD" {
				isCod = true
				break
			}
		}
	}

	if includes([]string{displaystatus.PENDING_CONFIRMATION, displaystatus.PLACED}, displayStatus) && isCod && cartValue <= 4000 {
		return true
	}

	return false
}

func getOrderInfoForCancelledOrders(targetstatus string, refundAmount float64, refundStatus string) *dto.OrderDetailInfo {
	if targetstatus == displaystatus.CANCELLED && refundAmount > 0 && refundStatus == REFUND_STATUS_SUCCESS {
		return &dto.OrderDetailInfo{
			Text:    fmt.Sprintf("₹%.2f का पेमेंट रिफ़ंड - कर दिया गया है ✅", refundAmount),
			BgColor: "#006400",
			Color:   "#FFFFFF",
		}
	}
	return nil
}

func getDeliveryStatusInfo(orderDelivered int64) string {
	if orderDelivered <= 0 {
		return ""
	}

	deliveryTime := time.UnixMilli(orderDelivered).UTC()

	monthStr := hindiMonthsMap[int(deliveryTime.Month())]

	// Format as mm-dd-yyyy with month as string
	return fmt.Sprintf("डिलीवरी डेट - %s %d,%d", monthStr, deliveryTime.Day(), deliveryTime.Year())
}

func getOrderStyleFromStatus(status string) map[string]any {
	switch status {
	case displaystatus.CONFIRMED, displaystatus.IN_TRANSIT, displaystatus.NDR, displaystatus.SHIPMENT_CREATED, displaystatus.PLACED:
		return ACTIVE_STATUS_STYLE
	case displaystatus.CANCELLED, displaystatus.OTHERS, displaystatus.RETURNED:
		return CANCELLED_STATUS_STYLE
	case displaystatus.DELIVERED:
		return DELIVERED_STATUS_STYLE
	}

	return DEFAULT_STATUS_STYLE
}

func getDynamicCTAFromStatus(status string, orderId int64, seller, reqOrderId string) *dto.Cta {
	if reqOrderId != "" {
		return &dto.Cta{
			Nav: &shared.Nav{
				Name: "WebViewOld",
				Params: map[string]interface{}{
					"uri":         "https://webapps.retailpulse.ai/customer-support/home",
					"showHeader":  false,
					"screenTitle": "",
				},
				NavType: "Redirect to WebviewOld",
			},
			Text: "सहायता केंद्र",
		}
	}

	source, exists := brands.GetSourceBySeller(seller)
	if !exists {
		return nil
	}

	// Define common navigation params
	baseParams := map[string]interface{}{
		"seller": seller,
		"source": source,
	}

	// Status-specific configurations
	statusConfig := map[string]struct {
		text   string
		screen string
		params map[string]interface{}
	}{
		STATUS_ACTIVE: {
			text:   "स्टेटस देखें",
			screen: "OrderDetails",
			params: map[string]interface{}{
				"order_id":      orderId,
				"retry_payment": false,
			},
		},
		STATUS_DELIVERED: {
			text:   "दोबारा ऑर्डर करें",
			screen: "Products",
			params: nil,
		},
		STATUS_CANCELLED: {
			text:   "ऑर्डर करें",
			screen: "Products",
			params: nil,
		},
	}

	// Get configuration for current status or use default
	config, exists := statusConfig[status]
	if !exists {
		config = statusConfig[STATUS_CANCELLED] // Default to cancelled behavior
	}

	// Merge base params with status-specific params
	finalParams := baseParams
	if config.params != nil {
		for k, v := range config.params {
			finalParams[k] = v
		}
	}

	return &dto.Cta{
		Text:              config.text,
		MixpanelEventName: "Clicked on Track Kare on My Orders",
		Nav: &shared.Nav{
			Name:    "OrderingModule",
			NavType: "Redirect to Screen",
			Params: map[string]interface{}{
				"screen": config.screen,
				"params": finalParams,
			},
		},
	}
}

func structureOrderObjectForApp(data *[]dto.OrderDetails, requestSource string) {
	if requestSource != "APP" {
		return
	}

	statusTranslations := map[string]string{
		displaystatus.PLACED:           "कन्फर्म होना बाकी",
		displaystatus.CONFIRMED:        "कन्फर्म हो चुका है",
		displaystatus.IN_TRANSIT:       "रवाना हो चुका है",
		displaystatus.DELIVERED:        "डिलीवर्ड",
		displaystatus.CANCELLED:        "कैंसल्ड",
		displaystatus.RETURNED:         "रिटर्न्ड",
		displaystatus.OTHERS:           "अन्य",
		displaystatus.SHIPMENT_CREATED: "पैकिंग शुरू",
	}

	for idx, order := range *data {
		if translatedStatus, exists := statusTranslations[order.DisplayStatus]; exists {
			(*data)[idx].DisplayStatus = translatedStatus
			(*data)[idx].OrderStatus = translatedStatus
		}
	}
}

// temp @sanket TODO:
func ReverseInPlace[T any](arr []T) {
	for i, j := 0, len(arr)-1; i < j; i, j = i+1, j-1 {
		arr[i], arr[j] = arr[j], arr[i]
	}
}

func convertToIST(timeStr, requestOrderID string, userAppVersion *semver.Version) (string, string) {
	// Try different time formats
	var t time.Time
	var err error

	formats := []string{
		"2006-01-02T15:04:05.000-07:00", // RFC3339 with milliseconds
		"2006-01-02T15:04:05-07:00",     // RFC3339
		"2006-01-02T15:04:05.000+07:00", // RFC3339 with milliseconds (positive offset)
		"2006-01-02T15:04:05+07:00",     // RFC3339 (positive offset)
		"2006-01-02T15:04:05.000",       // No timezone (assumes UTC)
		"2006-01-02T15:04:05",           // No timezone (assumes UTC)
	}

	for _, format := range formats {
		t, err = time.Parse(format, timeStr)
		if err == nil {
			break
		}
	}

	if err != nil {
		return "", ""
	}

	// Define IST location (UTC+5:30)
	ist, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		return "", ""
	}

	// Convert to IST
	istTime := t.In(ist)

	if requestOrderID != "" && userAppVersion != nil && userAppVersion.GreaterThan(semver.MustParse("6.5.5")) {
		if timeStr != "" {
			return "", istTime.Format("02 January 2006, 03:04 PM")
		}
		return "", ""
	}

	// Format and return
	return strings.Replace(istTime.Format("2006-01-02 15:04:05 MST"), " IST", "", 1), ""
}

func mapCodeToStatus(code string) (string, string) {
	var NSLStatusMap = map[string]struct {
		Status string
		Color  string // Hex color
	}{
		"ST-102":                    {"Address Issue", "#dc3545"},
		"ST-114":                    {"Contacted Customer", "#6c757d"},
		"EOD-111":                   {"Rejected by Customer", "#dc3545"},
		"EOD-6":                     {"Rejected by Customer", "#dc3545"},
		"X-SC":                      {"Customer to Collect", "#6c757d"},
		"EOD-11":                    {"Customer Unavailable", "#dc3545"},
		"EOD-43":                    {"Customer to Collect", "#6c757d"},
		"DLYDC-101":                 {"Delayed", "#6c757d"},
		"DLYRG-125":                 {"Delayed", "#6c757d"},
		"DLYLH-152":                 {"Delayed", "#6c757d"},
		"EOD-37":                    {"Delivered", "#28a745"},
		"EOD-38":                    {"Delivered", "#28a745"},
		"DLYB2B-101":                {"Rescheduled", "#ffc107"},
		"EOD-3":                     {"Rescheduled", "#ffc107"},
		"DLYHD-007":                 {"On Hold", "#6c757d"},
		"EOD-74":                    {"Address/Contact Issue", "#dc3545"},
		"ST-108":                    {"Delivery Failed", "#dc3545"},
		"ST-120":                    {"Delivery Failed", "#dc3545"},
		"DLYDC-102":                 {"Delayed", "#6c757d"},
		"DLYDC-107":                 {"Destination Closed", "#6c757d"},
		"DLYLH-126":                 {"Destination Not Ready", "#6c757d"},
		"X-DDD3FD":                  {"Out for Delivery", "#ffc107"},
		"ST-105":                    {"Reattempt", "#dc3545"},
		"ST-107":                    {"Reattempt", "#dc3545"},
		"DLYRG-130":                 {"Delayed", "#6c757d"},
		"DLYMPS-101":                {"Delayed", "#6c757d"},
		"X-PPOM":                    {"Picked Up", "#151B54"},
		"DOFF-128":                  {"Delayed", "#6c757d"},
		"X-ILL2F":                   {"In-Transit", "#ffc107"},
		"DLYLH-151":                 {"Delayed", "#6c757d"},
		"DLYLH-105":                 {"Delayed", "#6c757d"},
		"shipment_pickup_complete":  {"Picked Up", "#151B54"},
		"received":                  {"Received at Facility", "#151B54"},
		"shipment_out_for_delivery": {"Out for Delivery", "#ffc107"},
		"received_at_dh":            {"Received at Facility", "#151B54"},
		"shipment_delivered":        {"Delivered", "#28a745"},
		"delivered":                 {"Delivered", "#28a745"},
		"in_transit":                {"In Transit", "#ffc107"},
		"shipment_created":          {"Shipment Created", "#6c757d"},
		"out_for_delivery":          {"Out for Delivery", "#ffc107"},
	}

	val, exists := NSLStatusMap[code]
	if exists {
		return val.Status, val.Color
	}
	return "", ""
}

func (s *Service) placeONDCOrder(ctx context.Context, request dto.AppCreateKiranaBazarOrderRequest) (dto.AppKiranaBazarOrderResponse, error) {
	response := dto.AppKiranaBazarOrderResponse{}

	initResponse := dto.AppInitResp{}
	cartDetails, err := cart.Get(ctx, request.UserID, request.Data.Seller)
	if err != nil {
		return response, err
	}

	byt, err := json.Marshal(cartDetails.OnInit)
	if err != nil {
		return response, err
	}

	err = json.Unmarshal(byt, &initResponse)
	if err != nil {
		return response, err
	}

	ondcPlaceOrderResponse, err := s.Confirm(ctx, &dto.AppConfirmRequest{
		Data: dto.AppConfirmRequestData{
			Provider:    initResponse.Meta.Provider,
			Items:       initResponse.Meta.Items,
			Tags:        initResponse.Meta.Tags,
			Payment:     initResponse.Meta.Payment,
			Fulfillment: initResponse.Meta.Fulfillment,
			Quote:       initResponse.Meta.Quote,
			Billing:     initResponse.Meta.Billing,
			AddressID:   fmt.Sprint(request.Data.AddressID),
		},
		UserID: request.UserID,
		Meta:   initResponse.Meta,
	})
	if err != nil {
		return response, err
	}
	orderAmount, err := strconv.ParseFloat(ondcPlaceOrderResponse.Order.Quote.Price.Value, 32)
	if err != nil {
		return response, err
	}

	orderStatuses := orderS.MapOrderStatus(ondcPlaceOrderResponse.OrderState, "", orderS.OrderStatusResponse{})
	order := dao.KiranaBazarOrder{
		TransactionID:    initResponse.Meta.Context.TransactionID,
		MessageID:        initResponse.Meta.Context.MessageID,
		OrderStatus:      &ondcPlaceOrderResponse.OrderState,
		UserID:           &request.UserID,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
		Seller:           request.Data.Seller,
		DeliveryStatus:   orderStatuses.ShipmentStatus,
		DisplayStatus:    orderStatuses.DisplayStatus,
		ProcessingStatus: orderStatuses.ProcessingStatus,
	}

	// Saving Order data
	_, err = s.repository.Create(&order)
	if err != nil {
		return response, err
	}

	cartDetailss, _ := cartDetails.Cart.Get()
	totalProductPricing := 0.0

	totalProductPrice := 0.0
	totalDiscount := 0.0
	totalPayableAmount := 0.0
	productPricing := []shared.ProductPrice{}

	for _, pric := range initResponse.Meta.Quote.Breakup {
		offeredValue := "0"
		if pric.Item != nil {
			offeredValue = pric.Item.Price.Value
		}
		productPricing = append(productPricing, shared.ProductPrice{
			ID:           pric.ONDCOrgItemID,
			OfferedValue: offeredValue,
			Quantity:     pric.ONDCOrgItemQuantity.Count,
			TotalValue:   pric.Price.Value,
			ProductName:  pric.Title,
			PackSize:     0,
		})

	}

	productPricingDAO := []shared.ProductPrice{}

	for _, j := range productPricing {
		productPricingDAO = append(productPricingDAO, shared.ProductPrice{
			ID:           j.ID,
			OfferedValue: j.OfferedValue,
			Quantity:     j.Quantity,
			TotalValue:   j.TotalValue,
			ProductName:  j.ProductName,
			PackSize:     j.PackSize,
			Key:          j.Key,
			Value:        j.Value,
		})
		tval, _ := strconv.ParseFloat(j.TotalValue, 32)
		totalProductPrice += tval
		totalProductPricing += tval
	}

	totalPricingMap := []shared.TotalPricing{}
	totalPricingMap = append(totalPricingMap, shared.TotalPricing{
		Key:   "टोटल",
		Value: fmt.Sprintf("%.2f", totalProductPricing),
	})

	for _, pric := range totalPricingMap {
		if pric.Key != "टोटल" {
			tval, _ := strconv.ParseFloat(pric.Value, 32)
			totalDiscount += tval
		}
	}

	totalDiscount = -1 * totalDiscount

	totalPayableAmount = totalProductPrice - totalDiscount

	paymentStatus := "PENDING"
	paymentMethod := "COD"

	paymentID := uuid.New().String()
	payment := dao.KiranaBazarOrderPayment{
		ID:            &paymentID,
		OrderID:       order.ID,
		PaymentMethod: &paymentMethod,
		TransactionID: initResponse.Meta.Context.TransactionID,
		Amount:        &totalPayableAmount,
		Status:        &paymentStatus,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// saving payment details in sql
	_, err = s.repository.Create(&payment)
	if err != nil {
		return response, err
	}
	shippingAddress, billingAddress, err := getUserAddress(request.UserID, false, fmt.Sprint(request.Data.AddressID), s.repository)
	if err != nil {
		return response, err
	}

	cartItems, _ := cartDetails.Cart.Get()

	orderDetail := dao.KiranaBazarOrderDetails{
		Cart:            cartItems,
		ShippingAddress: shippingAddress,
		BillingAddress:  &billingAddress,
		TotalItems:      len(cartDetailss),
		TotalAmount:     totalPayableAmount,
		BillBreakUp: dao.BillDetails{
			ProductsPricing: productPricingDAO,
			TotalPricing:    totalPricingMap,
			EnableOrdering:  true,
		},
		ONDCOrderID: ondcPlaceOrderResponse.Order.ID,
		Seller:      request.Data.Seller,
	}
	orderDetailsByte, err := json.Marshal(orderDetail)
	if err != nil {
		return response, err
	}
	orderDetails := dao.KiranaBazarOrderDetail{
		OrderID:      order.ID,
		OrderDetails: orderDetailsByte,
		UpdatedAt:    time.Now(),
	}

	// creating orderDetails in sql
	_, err = s.repository.Create(&orderDetails)
	if err != nil {
		return response, err
	}

	response = dto.AppKiranaBazarOrderResponse{
		Meta: dto.Meta{},
		Data: []dto.OrderDetails{
			{
				ID:            fmt.Sprint(order.ID),
				TransactionID: *initResponse.Meta.Context.TransactionID,
				MessageID:     *ondcPlaceOrderResponse.MessageID,
				OrderStatus:   ondcPlaceOrderResponse.OrderState,
				OrderPayment: dto.OrderPayment{
					PaymentID:            "1",
					PaymentMethod:        "a",
					PaymentTransactionID: "1",
					Amount:               orderAmount,
					PaymentStatus:        ondcPlaceOrderResponse.PaymentStatus,
				},
				OrderDate:          time.Now(),
				TotalItems:         len(ondcPlaceOrderResponse.Order.Items),
				OrderStatusDetails: nil,
				TotalAmount:        orderAmount,
			},
		},
	}
	return response, nil
}

func (s *Service) CreateKiranaBazarOrder(ctx context.Context, request dto.AppCreateKiranaBazarOrderRequest) (response dto.AppKiranaBazarOrderResponse, err error) {
	isBlocked, err := checkIsUserBlocked(s.FirebaseRepository.MetaDb, request.UserID, request.Data.Seller)
	if err != nil {
		return response, err
	}

	if isBlocked {
		return response, errors.New("user is blocked from placing order")
	}

	allBrands := brands.GetAllBrands()

	if includes(allBrands, request.Data.Seller) || includes(allBrands, request.Data.Seller) {
		tid := getTransactionID()
		request.Meta.Context.TransactionID = &tid
		return s.placeThirdPartySellerOrder(ctx, request)
	} else if request.Data.Seller != "" {
		return s.placeONDCOrder(ctx, request)
	}
	return response, errors.New("invalid request")
}

func (s *Service) CreateKiranaBazarOrderV2(ctx context.Context, request dto.AppCreateKiranaBazarOrderRequest) (response dto.AppKiranaBazarOrderResponse, err error) {
	isBlocked, err := checkIsUserBlocked(s.FirebaseRepository.MetaDb, request.UserID, request.Data.Seller)
	if err != nil {
		return response, err
	}

	if isBlocked {
		return response, errors.New("user is blocked from placing order")
	}

	allBrands := brands.GetAllBrands()

	if includes(allBrands, request.Data.Seller) || includes(allBrands, request.Data.Seller) {
		tid := getTransactionID()
		request.Meta.Context.TransactionID = &tid
		return s.placeThirdPartySellerOrderV2(ctx, request)
	} else if request.Data.Seller != "" {
		return s.placeONDCOrder(ctx, request)
	}
	return response, errors.New("invalid request")
}

func (s *Service) placeThirdPartySellerOrder(ctx context.Context, request dto.AppCreateKiranaBazarOrderRequest) (response dto.AppKiranaBazarOrderResponse, err error) {
	seller := request.Data.BillBreakUp.Seller
	return s.placeThirdPartySellerOrderV2(ctx, request)

	appVersion := request.Meta.AppVersion

	userDetailsChannel := userdetails.AsyncFetchUserDetails(request.UserID, []string{userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS, userdetails.USER_DETAILS_TYPES.USER_GEOGRAPHY}, 1*time.Second)

	// min order value for the seller
	var MIN_ORDER_VALUE float64 = userdetails.GetUserSellerMinOrderValue(request.UserID, seller, false, "place_third_party_order")
	if MIN_ORDER_VALUE < 0 {
		err = errors.New("failed to get min order value")
		return
	}

	if seller == utils.KIRANA_CLUB {
		sellerLevelCount, err := ordervalue.GetSellerLevelOrderCount(request.UserID)
		if err != nil {
			return response, err
		}

		placedOrderCount := sellerLevelCount[fmt.Sprintf("%s::placed", seller)]
		confirmedOrderCount := sellerLevelCount[fmt.Sprintf("%s::confirmed", seller)]

		if placedOrderCount > 0 || confirmedOrderCount > 0 {
			err = errors.New("माफ़ करें आप ये प्रोडक्ट 1 से ज्यादा बार आर्डर नहीं कर सकते")
			return response, err
		}
	}
	couponID := request.Data.BillBreakUp.Coupon
	couponIDInt, err := strconv.Atoi(couponID)
	coupon := dto.Coupon{}
	if err == nil {
		couponResponse, errr := s.GetCoupons(ctx, dto.GetCouponsRequest{
			UserID: request.UserID,
			Data: dto.GetCouponData{
				CouponID: couponIDInt,
				Seller:   request.Data.Seller,
			},
		})
		if errr != nil {
			return
		}
		coupon = couponResponse.Data.Coupons[0]
	}

	mid := getMessageID()
	if request.Meta.Context.MessageID != nil {
		mid = *request.Meta.Context.MessageID
	}

	shippingAddress, billingAddress, err := getUserAddress(request.UserID, false, fmt.Sprintf("%d", request.Data.AddressID), s.repository)
	if err != nil {
		return
	}

	activationCohortEligibilityChannel := make(chan *dto.ActivationCohortUserData, 1)

	go getActivationCohortUserData(ctx, request.UserID, utils.ZOFF_FOODS, s.GcpRedis, activationCohortEligibilityChannel)

	userCashbackBalanceChannel := make(chan *float64, 1)
	go s.getUserCashbackBalanceFromService(ctx, request.UserID, request.Data.Seller, request.Meta, userCashbackBalanceChannel)

	orderStatus := processingstatus.PLACED
	paymentStatus := paymentstatus.UNPAID
	paymentMethod := constants.COD
	cartObject, err := cart.Get(ctx, request.UserID, seller)
	if err != nil {
		return
	}

	cartDetails, _ := cartObject.Cart.Get()

	productNames := []string{}
	productIds := []string{}
	for _, product := range cartDetails {
		meta := shared.KiranaBazarProductMeta{}
		err = json.Unmarshal(product.Meta, &meta)
		if err != nil {
			meta.HindiName = product.Name
		}
		productNames = append(productNames, string(meta.HindiName))
		productIds = append(productIds, product.ID)
	}

	inStockProductIds, err := s.CheckProductsOutOfStock(productIds)
	if err != nil {
		return
	}

	if len(inStockProductIds) != len(productIds) || len(inStockProductIds) == 0 {
		err = errors.New(exceptions.OosErrorMessage)
		return
	}

	pproductsPricing := []shared.ProductPrice{}
	ddiscountPricing := []shared.DiscountPricing{}

	ttotalProductPricing := 0.0
	ttotalRewardCoins := 0

	for _, item := range cartDetails {
		if err != nil {
			fmt.Println("err ", err)
		}
		if !includes(inStockProductIds, item.ID) {
			continue
		}
		meta := shared.KiranaBazarProductMeta{}
		err = json.Unmarshal(item.Meta, &meta)
		if err != nil {
			return
		}
		pproductsPricing = append(pproductsPricing, shared.ProductPrice{
			ID:           item.ID,
			OfferedValue: fmt.Sprintf("%.2f", meta.WholesaleRate),
			Quantity:     int(item.Quantity),
			TotalValue:   fmt.Sprintf("%.2f", meta.WholesaleRate*float64(item.Quantity)*float64(meta.PackSize)),
			ProductName:  meta.HindiName,
			PackSize:     meta.PackSize,
			Size:         meta.Quantity,
			Key:          fmt.Sprintf("%s %s (%d पैकेट्स) x %d", meta.HindiName, meta.Quantity, meta.PackSize, item.Quantity),
			Value:        fmt.Sprintf("₹%.2f", meta.WholesaleRate*float64(item.Quantity)*float64(meta.PackSize)),
			Manufacturer: item.Manufacturer,
		})
		ttotalProductPricing += meta.WholesaleRate * float64(item.Quantity) * float64(meta.PackSize)
		if meta.RewardCoins != nil {
			ttotalRewardCoins += *meta.RewardCoins
		}
	}

	ttotalPricingWithoutDiscount := ttotalProductPricing

	ttotalPricingMap := []shared.TotalPricing{}

	discountAmount := 0.0
	discountReason := ""

	activationCohortUserData := <-activationCohortEligibilityChannel
	userDetails := <-userDetailsChannel

	// get user order stats
	userOrderStatsResp, err := s.GetUserOrderStats(ctx, dto.GetUserOrderStatsRequest{
		UserID: request.UserID,
	})
	if err != nil {
		fmt.Println("error in getting user order stats", err)
	}

	userGeoPincode := ""
	isPincodeBlockedOrder := false
	isAutoConfirmOrder := false
	selectedPaymentMode := ""
	if ttotalPricingWithoutDiscount < ordertags.BULK_ORDER_MINIMUM_AMOUNT {
		selectedPaymentMode = constants.COD
	}
	createOrderChecks := ordertags.CreateOrderCheck(userOrderStatsResp.Data, userGeoPincode, *shippingAddress.PostalCode, seller, selectedPaymentMode, ttotalPricingWithoutDiscount)
	if createOrderChecks != nil {
		isPincodeBlockedOrder = *createOrderChecks == ordertags.ORDER_CHECK_TYPES.SERVICEABILITY_BLOCKED
		isAutoConfirmOrder = *createOrderChecks == ordertags.ORDER_CHECK_TYPES.AUTO_CONFIRM_ORDER
	}

	applicabelCharges := charges.GetApplicableCharges(request.UserID, request.Data.Seller, &userDetails, &shippingAddress, ttotalPricingWithoutDiscount)
	styles := map[string]interface{}{
		"color":      "#009E7F",
		"fontWeight": 700,
	}
	if applicabelCharges != nil && len(applicabelCharges) > 0 {
		for _, chrg := range applicabelCharges {
			chrgAmount := chrg.Amount
			chrgDisplayName := chrg.DisplayName
			ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
				Key:        chrgDisplayName,
				Value:      fmt.Sprintf("₹%0.2f", chrgAmount),
				TotalValue: chrgAmount,
				Styles:     &styles,
				Name:       "Service Charge",
				Type:       offers.OFFER_TYPES.CHARGE,
			})

			ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
				Key:        chrgDisplayName,
				Value:      fmt.Sprintf("₹%0.2f", chrgAmount),
				TotalValue: chrgAmount,
				Styles:     &styles,
				Name:       "Service Charge",
				Type:       offers.OFFER_TYPES.CHARGE,
			})
			ttotalProductPricing += chrgAmount
			ttotalPricingWithoutDiscount += chrgAmount
		}
	}

	tpm, disc, err := s.GetBackendDiscount(ttotalPricingWithoutDiscount, ttotalProductPricing, request.Data.Seller, request.UserID, activationCohortUserData)
	if err == nil {
		for _, tpmDiscount := range tpm {
			discValue := tpmDiscount.TotalValue
			value := tpmDiscount.Value
			if discValue > 0 {
				discValue = discValue * -1
			}
			ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
				Key:        tpmDiscount.Key,
				Value:      value,
				TotalValue: math.Floor(discValue*100) / 100, // TotalValue is negative here
				Styles:     tpmDiscount.Styles,
			})
			ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
				Key:        tpmDiscount.Key,
				Value:      value,
				TotalValue: math.Floor(discValue*100) / 100, // TotalValue is negative here
				Styles:     tpmDiscount.Styles,
				IsInternal: tpmDiscount.IsInternal,
			})

			discountReason = discountReason + fmt.Sprintf("%s %s | ", tpmDiscount.Key, tpmDiscount.Value)
			discountAmount += (-1 * discValue)
		}
		ttotalProductPricing -= disc
	}

	if coupon.Valid && coupon.MinimumAmount <= ttotalProductPricing {
		discountInt, err := strconv.Atoi(coupon.Discount)
		if err != nil {
			fmt.Println("err ", err)
		}
		discount := float64(discountInt)
		if coupon.PercentageDiscount > 0.0 {
			discount = ttotalProductPricing * coupon.PercentageDiscount * 0.01
		}

		styles := map[string]interface{}{
			"color":      "#009E7F",
			"fontWeight": 700,
		}

		ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
			Key:        coupon.Description,
			Value:      "-₹" + fmt.Sprintf("%0.2f", discount),
			TotalValue: -1 * discount,
			Styles:     &styles,
		})

		ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
			Key:        coupon.Description,
			Value:      "-₹" + fmt.Sprintf("%0.2f", discount),
			TotalValue: -1 * discount,
			Styles:     &styles,
			IsInternal: coupon.IsInternal,
		})

		ttotalProductPricing -= float64(discount)
		discountReason = discountReason + fmt.Sprintf("%s %0.2f | ", coupon.Code, discount)
		discountAmount += discount
	}

	if applicabelCharges == nil || len(applicabelCharges) == 0 {
		styles := map[string]interface{}{
			"color":      "#009E7F",
			"fontWeight": 700,
		}

		freeDeliveryString := fmt.Sprintf("₹̶%s फ्री डिलीवरी", utils.GenerateStrikethrough(fmt.Sprintf("%d", 100)))
		ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      freeDeliveryString,
			TotalValue: 0,
			Styles:     &styles,
		})

		ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      freeDeliveryString,
			TotalValue: 0,
			Styles:     &styles,
			IsInternal: true,
		})
	}

	ttotalRewardCoins = 100
	rewardCoins := dto.RewardCoinsData{}
	err = json.Unmarshal([]byte(utils.RewardsCoinsData), &rewardCoins)
	if err != nil {
		return
	}
	rewardCoins.Amount = ttotalRewardCoins
	rewardCoins.Text = fmt.Sprintf("%d किराना कॉइन्स", ttotalRewardCoins)

	// var userCashbackOfferComponent *dto.CashbackComponent
	userCashbackBalance := <-userCashbackBalanceChannel
	if userCashbackBalance != nil && *userCashbackBalance > 0 {
		// userCashbackOfferComponent = generateLoyaltyCashbackComponent(*userCashbackBalance)
		if request.Data.BillBreakUp.RequestedCashback > 0 {
			styles := map[string]interface{}{
				"color":      "#009E7F",
				"fontWeight": 700,
			}
			if request.Data.BillBreakUp.RequestedCashback > *userCashbackBalance {
				request.Data.BillBreakUp.RequestedCashback = *userCashbackBalance
			}

			if request.Data.BillBreakUp.RequestedCashback <= ttotalProductPricing {
				ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
					Key:        "कैशबैक",
					Value:      fmt.Sprintf("₹%0.2f", request.Data.BillBreakUp.RequestedCashback),
					TotalValue: -1 * float64(request.Data.BillBreakUp.RequestedCashback),
					Styles:     &styles,
					Type:       offers.OFFER_TYPES.CASHBACK,
				})

				discountAmount += float64(request.Data.BillBreakUp.RequestedCashback)
				discountReason = discountReason + fmt.Sprintf("कैशबैक %0.2f | ", float64(request.Data.BillBreakUp.RequestedCashback))
				ttotalProductPricing -= request.Data.BillBreakUp.RequestedCashback
			} else if request.Data.BillBreakUp.RequestedCashback > ttotalProductPricing {
				ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
					Key:        "कैशबैक",
					Value:      fmt.Sprintf("₹%0.2f", ttotalProductPricing),
					TotalValue: -1 * ttotalProductPricing,
					Styles:     &styles,
					Type:       offers.OFFER_TYPES.CASHBACK,
				})

				discountAmount += ttotalProductPricing
				discountReason = discountReason + fmt.Sprintf("कैशबैक %0.2f | ", ttotalProductPricing)
				ttotalProductPricing = 0
			}
		}
	}
	style := map[string]interface{}{
		"fontWeight": 700,
	}
	ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
		Key:        "टोटल",
		Value:      fmt.Sprintf("₹%.2f", ttotalProductPricing),
		Styles:     &style,
		TotalValue: ttotalProductPricing,
	})

	zproductPricing := []shared.ProductPrice{}
	ztotalProductPricing := []shared.TotalPricing{}
	zdiscountPricing := []shared.DiscountPricing{}

	totalProductPrice := 0.0
	totalDiscount := 0.0
	totalPayableAmount := 0.0
	totalCashbackApplied := 0.0
	totalCharges := 0.0
	for _, pric := range pproductsPricing {
		productDao := shared.ProductPrice{
			ID:           pric.ID,
			OfferedValue: pric.OfferedValue,
			Quantity:     pric.Quantity,
			TotalValue:   pric.TotalValue,
			ProductName:  pric.ProductName,
			PackSize:     pric.PackSize,
			Key:          pric.Key,
			Value:        pric.Value,
			Manufacturer: pric.Manufacturer,
		}
		if pric.Styles != nil {
			productDao.Styles = pric.Styles
		}
		zproductPricing = append(zproductPricing, productDao)
		tval, _ := strconv.ParseFloat(pric.TotalValue, 32)
		totalProductPrice += tval
	}

	for _, pric := range ddiscountPricing {
		discountDao := shared.DiscountPricing{
			Key:        pric.Key,
			Value:      pric.Value,
			TotalValue: pric.TotalValue,
			IsInternal: pric.IsInternal,
			Type:       pric.Type,
		}
		if pric.Styles != nil {
			discountDao.Styles = pric.Styles
		}
		zdiscountPricing = append(zdiscountPricing, discountDao)
		if pric.Type == offers.OFFER_TYPES.CASHBACK {
			cval := pric.TotalValue
			if cval < 0 {
				cval = cval * -1.0
			}
			totalCashbackApplied += cval
		}
		if pric.Type == offers.OFFER_TYPES.CHARGE {
			cval := pric.TotalValue
			if cval < 0 {
				cval = cval * -1.0
			}
			totalCharges += cval
		}
	}

	for _, pric := range ttotalPricingMap {
		totalPricingDao := shared.TotalPricing{
			Key:        pric.Key,
			Value:      pric.Value,
			TotalValue: pric.TotalValue,
		}
		if pric.Styles != nil {
			totalPricingDao.Styles = pric.Styles
		}
		ztotalProductPricing = append(ztotalProductPricing, totalPricingDao)
		if pric.Key != "टोटल" && pric.Type != offers.OFFER_TYPES.CHARGE {
			tval, err := strconv.ParseFloat(pric.Value, 32)
			if err != nil {
				tval = pric.TotalValue
			}
			if tval < 0 {
				tval = tval * -1.0
			}
			totalDiscount += tval
		}
	}

	if totalDiscount < 0 {
		totalDiscount = totalDiscount * -1.0
	}

	totalPayableAmount = totalProductPrice - totalDiscount - totalCashbackApplied + totalCharges

	if totalProductPrice < userdetails.GetUserSellerMinOrderValue(request.UserID, seller, false, "create_order_2") {
		err = fmt.Errorf("order value less than minimum order value %s", "create_order_1")
		return
	}

	eventObject := map[string]interface{}{
		"distinct_id":        request.UserID,
		"ordering_module":    utils.MakeTitleCase(seller),
		"seller":             seller,
		"source":             "PLACE_ORDER",
		"latitude":           shippingAddress.Latitude,
		"longitude":          shippingAddress.Longitude,
		"order_value":        int(totalPayableAmount),
		"product_name":       productNames,
		"product_id":         productIds,
		"total_discount":     totalDiscount,
		"promo_code":         request.Data.BillBreakUp.Coupon,
		"cart_value":         totalProductPrice,
		"charges":            totalCharges,
		"cashback_applied":   totalCashbackApplied,
		"pincode_blocked":    isPincodeBlockedOrder,
		"platform":           request.Meta.Platform,
		"auto_confirm_order": isAutoConfirmOrder,
	}

	intPincode, err := strconv.Atoi(*shippingAddress.PostalCode)
	eventObject["pincode"] = intPincode
	if err != nil {
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Non Serviceable Location", request.UserID, eventObject),
		})
		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{request.UserID},
			EventName:   "Non Serviceable Location",
			EventObject: eventObject,
		})
		err = errors.New("not able to parse pincode")
		return
	}
	serviceablityResponse := &dto.AppserviceAbilityAPIResponse{}
	serviceablityResponse, err = s.CheckServiceAbility(ctx, &dto.AppServiceAblityAPIRequest{
		UserID:           request.UserID,
		DeliveryPostCode: *shippingAddress.PostalCode,
		Weight:           fmt.Sprintf("%d", utils.GetOrderMass(cartDetails)),
		COD:              "y",
		Data: dto.AppServiceAbilityAPIRequestData{
			Seller: seller,
		},
	})

	if err != nil {
		return
	}
	if !serviceablityResponse.Data.Servicable {
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Non Servicable Location", request.UserID, eventObject),
		})
		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{request.UserID},
			EventName:   "Non Servicable Location",
			EventObject: eventObject,
		})
		err = errors.New("not servicable")
		return
	}

	if userCashbackBalance != nil {
		if totalCashbackApplied > *userCashbackBalance {
			err = errors.New("क्षमा करें, आपके पास इस ऑर्डर के लिए पर्याप्त कैशबैक नहीं है।")
			return
		}
	}

	orderStatuses := orderS.MapOrderStatus(orderStatus, "", orderS.OrderStatusResponse{})
	order := dao.KiranaBazarOrder{
		TransactionID:    request.Meta.Context.TransactionID,
		MessageID:        &mid,
		OrderStatus:      &orderStatus,
		UserID:           &request.UserID,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
		Seller:           seller,
		DeliveryStatus:   orderStatuses.ShipmentStatus,
		DisplayStatus:    orderStatuses.DisplayStatus,
		ProcessingStatus: orderStatuses.ProcessingStatus,
	}
	_, err = s.repository.Create(&order)
	if err != nil {
		return
	}

	paymentID := uuid.New().String()
	payment := dao.KiranaBazarOrderPayment{
		ID:            &paymentID,
		OrderID:       order.ID,
		PaymentMethod: &paymentMethod,
		TransactionID: request.Meta.Context.TransactionID,
		Amount:        &totalPayableAmount,
		Status:        &paymentStatus,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
	_, err = s.repository.Create(&payment)
	if err != nil {
		return
	}

	if userCashbackBalance != nil && totalCashbackApplied > 0 {
		// redeem cashback for user
		sourceId, exists := brands.GetCashbackActionsBySource(seller)

		if !exists || exists && (sourceId.ADD_CASHBACK == 0 || sourceId.REDEEM_CASHBACK == 0) {
			slack.SendSlackMessage(fmt.Sprintf("invalid cashback source id for seller %s, sourceId: %v in placeThirdPartySellerOrder", seller, sourceId))
			return response, errors.New("invalid cashback source id")
		}

		_, err := s.RedeemUserCashback(ctx, dto.RedeemUserCashbackRequest{
			UserID: request.UserID,
			Meta:   request.Meta,
			Data: dto.RedeemUserCashbackData{
				OrderAmount:          totalPayableAmount,
				SourceId:             sourceId.REDEEM_CASHBACK,
				OrderId:              *order.ID,
				RedeemCashbackAmount: totalCashbackApplied,
			},
		})
		if err != nil {
			return response, err
		}
	}

	userOrderCount, err := ordervalue.GetUserLevelOrderCount(request.UserID)
	if err != nil {
		fmt.Println("error in getting user order count", err)
	}

	userAcurateConfirmedOrderCount, err := ordervalue.GetUserLevelConfirmedOrderCount(request.UserID)
	if err != nil {
		fmt.Println("error in getting user order count", err)
	}

	userCohortNames := s.getUserCohortNames(userDetails, request.UserID, seller)
	tagIds, tagNames, _ := ordertags.GetOrderTags(totalPayableAmount, userOrderStatsResp.Data, 0, userOrderCount, userAcurateConfirmedOrderCount, appVersion, seller, userCohortNames)

	// insert order tag in db, TODO: move this to a go routine
	if len(tagIds) > 0 {
		err := s.addOrderTags(*order.ID, tagIds)
		if err != nil {
			return response, err
		}
	}

	isSpamUser := ordertags.IsUserSpam(userOrderStatsResp.Data)

	cartItems, _ := cartObject.Cart.Get()
	orderDetail := dao.KiranaBazarOrderDetails{
		Cart:            cartItems,
		ShippingAddress: shippingAddress,
		BillingAddress:  &billingAddress,
		TotalItems:      len(cartDetails),
		TotalAmount:     totalPayableAmount,
		BillBreakUp: dao.BillDetails{
			ProductsPricing: zproductPricing,
			TotalPricing:    ztotalProductPricing,
			DiscountPricing: zdiscountPricing,
			EnableOrdering:  true,
			Message:         request.Data.BillBreakUp.Message,
			Coupon:          couponID,
		},
		Seller: seller,
	}
	orderDetailsByte, err := json.Marshal(orderDetail)
	if err != nil {
		return
	}
	orderDetails := dao.KiranaBazarOrderDetail{
		OrderID:      order.ID,
		OrderDetails: orderDetailsByte,
		UpdatedAt:    time.Now(),
	}
	_, err = s.repository.Create(&orderDetails)
	if err != nil {
		return
	}

	// err = func(userID string) error {
	// 	_, err := s.repository.CustomQuery(nil, fmt.Sprintf(`delete from kiranabazar_cart where user_id = '%s';`, userID))
	// 	return err
	// }(request.UserID)

	source := "APP"
	if request.Data.Email != "" {
		source = "D2R"
	}

	eventObject = map[string]interface{}{
		"distinct_id":         request.UserID,
		"order_value":         int(totalPayableAmount),
		"product_name":        productNames,
		"product_id":          productIds,
		"ordering_module":     utils.MakeTitleCase(seller),
		"seller":              seller,
		"order_id":            fmt.Sprint(*order.ID),
		"total_discount":      totalDiscount,
		"promo_code":          request.Data.BillBreakUp.Coupon,
		"cart_value":          totalProductPrice,
		"source":              source,
		"tag_ids":             tagIds,
		"tags":                tagNames,
		"cashback_applied":    totalCashbackApplied,
		"pincode_blocked":     isPincodeBlockedOrder,
		"platform":            request.Meta.Platform,
		"is_spam_user":        isSpamUser,
		"request_app_version": appVersion,
		"auto_confirm_order":  isAutoConfirmOrder,
	}

	if request.Data.Email != "" {
		eventObject["email"] = request.Data.Email
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Placed", request.UserID, eventObject,
			fmt.Sprintf("%s_%s", "order_placed", fmt.Sprint(*order.ID))),
	})

	activeCohortUserData := activationCohortUserData

	cartValue := orderDetail.GetCartValue()
	gtv := GetGTVValue(&orderDetail)
	go func() {
		_, err := s.AddDataForReconciliation(ctx, &dto.AddReconciliationRequest{
			OrderID: int64(*order.ID),
			Data: []dto.StatusTimeStamp{
				dto.StatusTimeStamp{
					OrderStatus: "order_placed",
					TimeStamp:   time.Now().UnixMilli(),
				},
				dto.StatusTimeStamp{
					OrderStatus: "expected_delivery_timestamp",
					TimeStamp:   time.Now().UnixMilli() + 604800000,
				},
			},
			Service:       "INTERNAL",
			OMS:           seller,
			DiscountValue: &totalDiscount,
			GrossValue:    &totalProductPrice,
			OrderValue:    &totalPayableAmount,
			CartValue:     &cartValue,
			GTV:           &gtv,
		})
		if err != nil {
			fmt.Println("err in ", err)
			slack.SendSlackMessage(fmt.Sprintf("error in add data for reeconciliation %v", err))
		}

		if activeCohortUserData != nil && activeCohortUserData.Eligible && (seller == utils.ZOFF_FOODS || true) {
			// set coupon as redeemed in redis cache
			setActivationCohortCouponRedeemed(ctx, request.UserID, utils.ZOFF_FOODS, activeCohortUserData.CouponId, s.GcpRedis)
		}

		sourceId, exists := brands.GetCashbackActionsBySource(seller)

		if !exists || exists && (sourceId.ADD_CASHBACK == 0 || sourceId.REDEEM_CASHBACK == 0) {
			slack.SendSlackMessage(fmt.Sprintf("invalid cashback source id for seller %s, sourceId: %v in placeThirdPartySellerOrder", seller, sourceId))
			err = errors.New("invalid cashback source id")
			return
		}

		// call here to add loyalty points for this order
		_, err = s.AddUserLoyaltyPoints(ctx, dto.AddUserLoyaltyPointsRequest{
			UserID: request.UserID,
			Meta:   request.Meta,
			Data: dto.AddUserLoyaltyPointsRequestData{
				OrderId:     *order.ID,
				OrderAmount: totalPayableAmount,
				SourceId:    sourceId.ADD_CASHBACK,
			},
		})
		if err != nil {
			fmt.Println("error in adding user loyalty points", err)
		}
	}()

	go func(orderID int64, s *Service) {
		_, err := s.CreatePickListForOrder(ctx, dto.CreatePickListForOrderRequest{
			Data: struct {
				OrderID string `json:"order_id"`
			}{OrderID: fmt.Sprint(orderID)},
		})
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error creating picklist for order: %d %v", orderID, err))
		}
	}(*order.ID, s)

	err = webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{request.UserID},
		EventName:   "Order Placed",
		EventObject: eventObject,
	})
	if err != nil {
		fmt.Println("failed to send webengage event")
	}

	currentDateTime := time.Now()
	hindiDateString := fmt.Sprintf("%s %d, %d", hindiMonthsMap[int(currentDateTime.Month())], currentDateTime.Day(), currentDateTime.Year())
	timeString := currentDateTime.Format("3:04 PM 02-01-2006")

	// add Data to IVR queue
	// if order value is greater than 4k, MAX_ORDER_FOR_AUTOMATED_IVR not inserting
	// Adding IVR call 3 times in a queue (in difference of 8 hours)
	if totalPayableAmount < utils.MAX_ORDER_FOR_AUTOMATED_IVR && !utils.NON_IVR_USERS[request.UserID] && !isSpamUser && userAcurateConfirmedOrderCount > 0 {
		appID := 0
		if data, ok := ivr.SellerIVRData[order.Seller][ivr.ORDER_CONFIRMATION_FLOW]; ok {
			if id, ok := data.(int); ok {
				appID = id
			}
		}
		if appID != 0 {
			// After one hour
			// add here for IVR 1 status update
			ivrStatus := ivrStatus.IVR_STATUS_MAP["ivr_1"]
			ivrStatusRequest := &dto.IVRStatusUpdateRequest{
				OrderId:    *order.ID,
				Status:     ivrStatus,
				IvrStatus:  ivrStatus,
				CallCount:  1,
				RetryCount: 0,
			}

			_, err := s.UpdateIVRCallStatus(ctx, ivrStatusRequest)
			if err != nil {
				fmt.Println("failed updating ivr 1 status", err)
			}

			triggerAt, _ := s.addDataToIVRQueue(*order.ID, order.Seller, totalPayableAmount, totalProductPrice, 1, request.UserID, *shippingAddress.Phone, appID, time.Now().Add(time.Hour*1), ivr.IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW)
			// 2nd call after 8 hour
			rand.Seed(time.Now().UnixNano())
			randomNumber := rand.Intn(61)
			triggerAt, _ = s.addDataToIVRQueue(*order.ID, order.Seller, totalPayableAmount, totalProductPrice, 2, request.UserID, *shippingAddress.Phone, appID, triggerAt.Add(time.Hour*3+time.Minute*30+time.Minute*time.Duration(randomNumber)), ivr.IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW)
			// 3rd call after 8 hours of second call i.e 16 hrs after 2nd call
			triggerAt, _ = s.addDataToIVRQueue(*order.ID, order.Seller, totalPayableAmount, totalProductPrice, 3, request.UserID, *shippingAddress.Phone, appID, triggerAt.Add(time.Hour*3+time.Minute*30+time.Minute*time.Duration(randomNumber)), ivr.IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW)
			// 4th call to check if user has still not receiving the call so that we can cancel the order
			triggerAt, _ = s.addDataToIVRQueue(*order.ID, order.Seller, totalPayableAmount, totalProductPrice, 4, request.UserID, *shippingAddress.Phone, appID, triggerAt.Add(time.Hour*3+time.Minute*30+time.Minute*time.Duration(randomNumber)), ivr.IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW)
		}
	}
	if err != nil {
	}

	err = cart.Empty(ctx, request.UserID, seller)
	if err != nil {
		return
	}

	successBanner := make(map[string]interface{})
	// if !LOYALTY_DATA_7_MAY[request.UserID] {
	// 	successBanner = utils.OrderSuccessBannerWidget
	// }

	response = dto.AppKiranaBazarOrderResponse{
		Data: []dto.OrderDetails{
			{
				ID:                 fmt.Sprint(*order.ID),
				TotalItems:         len(cartDetails),
				OrderDate:          currentDateTime,
				OrderDateString:    hindiDateString,
				OrderTimeString:    timeString,
				TotalAmount:        totalPayableAmount,
				TransactionID:      *request.Meta.Context.TransactionID,
				MessageID:          mid,
				OrderStatusDetails: nil,
				OrderStatus:        orderStatus,
				Banner:             successBanner,
				OrderPayment: dto.OrderPayment{
					PaymentID:            fmt.Sprint(*payment.ID),
					PaymentMethod:        paymentMethod,
					PaymentTransactionID: *request.Meta.Context.TransactionID,
					Amount:               totalPayableAmount,
					PaymentStatus:        "PENDING",
				},
				OrderDetails: orderDetailsByte,
			},
		},
	}

	go s.CreateOrderInvoice(context.Background(), dto.CreateInvoiceRequest{
		OrderID: fmt.Sprint(*order.ID),
	})

	if isPincodeBlockedOrder {
		// cancle order
		_, err = s.CancelKiranaBazarOrder(ctx, dto.AppCancelKiranaBazarOrderRequest{
			UserID: request.UserID,
			Data: dto.AppCancelKiranaBazarOrderData{
				OrderID:     fmt.Sprint(*order.ID),
				Email:       "<EMAIL>",
				Reason:      cancelreason.PincodeNotServiceable,
				Explanation: cancelreason.PincodeNotServiceable,
				Message:     cancelreason.PincodeNotServiceable,
				Source:      "BACKEND",
				StatusType:  "",
			},
		})
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("1 Error cancelling pincode not serviceable order %d: %v", *order.ID, err))
			return
		}
	}

	if isAutoConfirmOrder {
		advanceTaken := false
		s.UpdateB2BOrderStatus(ctx, &dto.UpdateB2BOrderStatusRequest{
			UpdatedBy: "system",
			Data: dto.UpdateB2BOrderStatusData{
				OrderID:        fmt.Sprint(*order.ID),
				UpdatedBy:      "<EMAIL>",
				OrderStatus:    constants.CONFIRMED,
				Source:         "BACKEND",
				UpdatedAt:      time.Now().UnixMilli(),
				OrderingModule: seller,
				OrderMeta: dto.OrderMeta{
					AdvanceTaken:    &advanceTaken,
					PaymentId:       fmt.Sprint(*payment.ID),
					PaymentDiscount: 0,
					Note:            "ORDER AUTO CONFIRMED",
				},
			},
		})
	}

	return
}

func (s *Service) placeThirdPartySellerOrderV2(ctx context.Context, request dto.AppCreateKiranaBazarOrderRequest) (response dto.AppKiranaBazarOrderResponse, err error) {
	seller := request.Data.BillBreakUp.Seller
	couponID := request.Data.BillBreakUp.Coupon
	paymentData := request.PaymentData
	appVersion := request.Meta.AppVersion

	if seller == utils.KIRANA_CLUB {
		sellerLevelCount, err := ordervalue.GetSellerLevelOrderCount(request.UserID)
		if err != nil {
			return response, err
		}
		placedOrderCount := sellerLevelCount[fmt.Sprintf("%s::placed", seller)]
		confirmedOrderCount := sellerLevelCount[fmt.Sprintf("%s::confirmed", seller)]

		if placedOrderCount > 0 || confirmedOrderCount > 0 {
			err = errors.New("माफ़ करें आप ये प्रोडक्ट 1 से ज्यादा बार आर्डर नहीं कर सकते")
			return response, err
		}
	}

	userDetailsChannel := userdetails.AsyncFetchUserDetails(request.UserID, []string{userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS, userdetails.USER_DETAILS_TYPES.USER_GEOGRAPHY}, 1*time.Second)

	// min order value for the seller
	var MIN_ORDER_VALUE float64 = userdetails.GetUserSellerMinOrderValue(request.UserID, seller, false, "place_third_party_order")

	mid := getMessageID()
	if request.Meta.Context.MessageID != nil {
		mid = *request.Meta.Context.MessageID
	}

	shippingAddress, billingAddress, err := getUserAddress(request.UserID, false, fmt.Sprintf("%d", request.Data.AddressID), s.repository)
	if err != nil {
		return
	}

	shippingAddress.Latitude = utils.DefaultFloat64IfNil(request.Data.Latitude, 0.0)
	shippingAddress.Longitude = utils.DefaultFloat64IfNil(request.Data.Longitude, 0.0)

	billingAddress.Latitude = utils.DefaultFloat64IfNil(request.Data.Latitude, 0.0)
	billingAddress.Longitude = utils.DefaultFloat64IfNil(request.Data.Longitude, 0.0)

	activationCohortEligibilityChannel := make(chan *dto.ActivationCohortUserData, 1)
	userCashbackBalanceChannel := make(chan *float64, 1)

	if request.Data.Seller == utils.ZOFF_FOODS {
		go getActivationCohortUserData(ctx, request.UserID, utils.ZOFF_FOODS, s.GcpRedis, activationCohortEligibilityChannel)
	} else {
		// insert nil in channel to avoid deadlock
		activationCohortEligibilityChannel <- nil
	}
	go s.getUserCashbackBalanceFromService(ctx, request.UserID, request.Data.Seller, request.Meta, userCashbackBalanceChannel)

	orderStatus := processingstatus.PLACED
	paymentStatus := paymentstatus.UNPAID
	paymentMethod := constants.COD

	kiranaBazarCart, err := cart.Get(ctx, request.UserID, seller)
	if err != nil {
		return
	}
	cartDetails, _ := kiranaBazarCart.Cart.Get()

	sellerItems, _ := kiranaBazarCart.Cart.Get()

	productNames := []string{}
	productIds := []string{}
	productsOrderData := make([]map[string]interface{}, 0)
	for _, items := range sellerItems {
		meta := shared.KiranaBazarProductMeta{}
		err = json.Unmarshal(items.Meta, &meta)
		if err != nil {
			meta.HindiName = items.Name
		}
		productNames = append(productNames, string(meta.HindiName))
		productIds = append(productIds, items.ID)
		productsOrderData = append(productsOrderData, map[string]interface{}{
			"product_id":     items.ID,
			"product_name":   items.Name,
			"quantity":       items.Quantity,
			"product_type":   items.ProductType,
			"wholesale_rate": meta.WholesaleRate,
		})
	}

	sudoRequest := dto.GetBillDetailsRequest{
		UserID: request.UserID,
		Data: dto.GetBillDetailsData{
			Seller:   seller,
			CouponID: couponID,
		},
		Meta: request.Meta,
	}
	userDetails := <-userDetailsChannel

	// get user order stats
	userOrderStatsResp, err := s.GetUserOrderStats(ctx, dto.GetUserOrderStatsRequest{
		UserID: request.UserID,
	})
	if err != nil {
		fmt.Println("error in getting user order stats", err)
	}

	isSpamUser := ordertags.IsUserSpam(userOrderStatsResp.Data)

	userGeoPincode := ""

	selectedPaymentMode := constants.COD
	if request.PaymentData != nil && request.PaymentData.Mode != nil {
		selectedPaymentMode = request.PaymentData.Mode.Type
	}

	// Process cart and calculate discounts
	productsPricing, discountPricing, totalPricingMap, _, discountAmount, discountReason, totalPayableAmount, _, totalPricingWithoutDiscount, activationCohortUserData, _, _, promoItems, virtualSkus, err :=
		s.ProcessCart(ctx, kiranaBazarCart, activationCohortEligibilityChannel, sudoRequest, MIN_ORDER_VALUE, userDetails, &shippingAddress, true)
	if err != nil {
		return
	}

	isAutoConfirmOrder := false
	isPincodeBlockedOrder := false
	createOrderChecks := ordertags.CreateOrderCheck(userOrderStatsResp.Data, userGeoPincode, *shippingAddress.PostalCode, seller, selectedPaymentMode, totalPricingWithoutDiscount)
	if createOrderChecks != nil {
		isPincodeBlockedOrder = *createOrderChecks == ordertags.ORDER_CHECK_TYPES.SERVICEABILITY_BLOCKED
		isAutoConfirmOrder = *createOrderChecks == ordertags.ORDER_CHECK_TYPES.AUTO_CONFIRM_ORDER
	}

	userCashbackBalance := <-userCashbackBalanceChannel
	_, appliedCashback, totalPayableAmount, _, err :=
		s.processCashbackApplication(userCashbackBalance, request.Data.BillBreakUp.RequestedCashback, totalPayableAmount, &discountPricing, &discountReason, &discountAmount, userDetails, request.Data.Seller, nil)
	if err != nil {
		// Log error but continue
		fmt.Println("Error processing cashback:", err)
	}

	style := map[string]interface{}{
		"fontWeight": 700,
	}
	totalPricingMap = append(totalPricingMap, shared.TotalPricing{
		Key:        "टोटल",
		Value:      fmt.Sprintf("₹%.2f", totalPayableAmount),
		Styles:     &style,
		TotalValue: totalPayableAmount,
	})

	if discountAmount < 0 {
		discountAmount = discountAmount * -1.0
	}

	if totalPricingWithoutDiscount < userdetails.GetUserSellerMinOrderValue(request.UserID, seller, false, "create_order_2") {
		err = fmt.Errorf("order value less than minimum order value %s with value = %0.2f and userId = %s", "create_order_2", totalPricingWithoutDiscount, request.UserID)
		return
	}

	eventObject := map[string]interface{}{
		"distinct_id":         request.UserID,
		"ordering_module":     utils.MakeTitleCase(seller),
		"seller":              seller,
		"source":              "PLACE_ORDER",
		"latitude":            utils.DefaultFloat64IfNil(request.Data.Latitude, 0.0),
		"longitude":           utils.DefaultFloat64IfNil(request.Data.Longitude, 0.0),
		"platform":            request.Meta.Platform,
		"order_value":         int(totalPayableAmount),
		"product_name":        productNames,
		"product_id":          productIds,
		"total_discount":      discountAmount,
		"promo_code":          request.Data.BillBreakUp.Coupon,
		"cart_value":          totalPricingWithoutDiscount,
		"cashback_applied":    appliedCashback,
		"pincode_blocked":     isPincodeBlockedOrder,
		"is_spam_user":        isSpamUser,
		"request_app_version": appVersion,
		"auto_confirm_order":  isAutoConfirmOrder,
	}

	intPincode, err := strconv.Atoi(*shippingAddress.PostalCode)
	eventObject["pincode"] = intPincode
	if err != nil {
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Non Serviceable Location", request.UserID, eventObject),
		})
		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{request.UserID},
			EventName:   "Non Serviceable Location",
			EventObject: eventObject,
		})
		err = errors.New("not able to parse pincode")
		return
	}
	serviceablityResponse := &dto.AppserviceAbilityAPIResponse{}
	serviceablityResponse, err = s.CheckServiceAbility(ctx, &dto.AppServiceAblityAPIRequest{
		UserID:           request.UserID,
		DeliveryPostCode: *shippingAddress.PostalCode,
		Weight:           fmt.Sprintf("%d", utils.GetOrderMass(cartDetails)),
		COD:              "y",
		Data: dto.AppServiceAbilityAPIRequestData{
			Seller: seller,
		},
	})

	if err != nil {
		return
	}
	if !serviceablityResponse.Data.Servicable {
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Non Servicable Location", request.UserID, eventObject),
		})
		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{request.UserID},
			EventName:   "Non Servicable Location",
			EventObject: eventObject,
		})
		err = errors.New("not servicable")
		return
	}

	if userCashbackBalance != nil {
		if appliedCashback > *userCashbackBalance {
			err = errors.New("क्षमा करें, आपके पास इस ऑर्डर के लिए पर्याप्त कैशबैक नहीं है।")
			return
		}
	}

	orderStatuses := orderS.MapOrderStatus(orderStatus, "", orderS.OrderStatusResponse{})
	order := dao.KiranaBazarOrder{
		TransactionID:    request.Meta.Context.TransactionID,
		MessageID:        &mid,
		OrderStatus:      &orderStatus,
		UserID:           &request.UserID,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
		Seller:           seller,
		DeliveryStatus:   orderStatuses.ShipmentStatus,
		DisplayStatus:    orderStatuses.DisplayStatus,
		ProcessingStatus: orderStatuses.ProcessingStatus,
	}
	_, err = s.repository.Create(&order)
	if err != nil {
		return
	}

	paymentID := uuid.New().String()
	payment := dao.KiranaBazarOrderPayment{
		ID:            &paymentID,
		OrderID:       order.ID,
		PaymentMethod: &paymentMethod,
		TransactionID: request.Meta.Context.TransactionID,
		Amount:        &totalPayableAmount,
		Status:        &paymentStatus,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
	_, err = s.repository.Create(&payment)
	if err != nil {
		return
	}

	sourceId, exists := brands.GetCashbackActionsBySource(seller)

	if !exists || exists && (sourceId.ADD_CASHBACK == 0 || sourceId.REDEEM_CASHBACK == 0) {
		slack.SendSlackMessage(fmt.Sprintf("invalid cashback source id for seller %s, sourceId: %v in placeThirdPartySellerOrderV2", seller, sourceId))
		err = errors.New("invalid cashback source id")
		return
	}

	if userCashbackBalance != nil && appliedCashback > 0 {
		// redeem cashback for user
		_, err := s.RedeemUserCashback(ctx, dto.RedeemUserCashbackRequest{
			UserID: request.UserID,
			Meta:   request.Meta,
			Data: dto.RedeemUserCashbackData{
				OrderAmount:          totalPayableAmount,
				SourceId:             sourceId.REDEEM_CASHBACK,
				OrderId:              *order.ID,
				RedeemCashbackAmount: appliedCashback,
			},
		})
		if err != nil {
			slack.SendSlackDebugMessage(fmt.Sprintf("error in redeeming user cashback\nerror: %v\nuser: %s\napplied_cashback: %f\nuser_cashback_balane: %f\norder_id: %d", err, request.UserID, appliedCashback, *userCashbackBalance, *order.ID))
			return response, err
		}
	}

	userOrderCount, err := ordervalue.GetUserLevelOrderCount(request.UserID)
	if err != nil {
		fmt.Println("error in getting user order count", err)
	}

	paymentAmount := 0.0
	if paymentData != nil {
		paymentAmount = paymentData.PaymentAmount
	}

	userAcurateConfirmedOrderCount, err := ordervalue.GetUserLevelConfirmedOrderCount(request.UserID)
	if err != nil {
		fmt.Println("error in getting user order count", err)
	}

	userCohortNames := s.getUserCohortNames(userDetails, request.UserID, seller)
	tagIds, tagNames, _ := ordertags.GetOrderTags(totalPayableAmount, userOrderStatsResp.Data, paymentAmount, userOrderCount, userAcurateConfirmedOrderCount, appVersion, seller, userCohortNames)
	if err != nil {
		return response, err
	}

	// insert order tag in db, TODO: move this to a go routine
	if len(tagIds) > 0 {
		err := s.addOrderTags(*order.ID, tagIds)
		if err != nil {
			return response, err
		}
	}

	virtualSkuIds := make([]string, 0)
	if len(virtualSkus) > 0 {
		for _, virtualSku := range virtualSkus {
			virtualSkuIds = append(virtualSkuIds, virtualSku.ID)
		}
	}

	cartItems, _ := kiranaBazarCart.Cart.Get()
	cartItems = append(cartItems, promoItems...)
	orderDetail := dao.KiranaBazarOrderDetails{
		Cart:            cartItems,
		ShippingAddress: shippingAddress,
		VirtualSkusCart: virtualSkus,
		BillingAddress:  &billingAddress,
		TotalItems:      len(cartDetails),
		TotalAmount:     totalPayableAmount,
		BillBreakUp: dao.BillDetails{
			ProductsPricing: productsPricing,
			TotalPricing:    totalPricingMap,
			DiscountPricing: discountPricing,
			EnableOrdering:  true,
			Message:         request.Data.BillBreakUp.Message,
			Coupon:          couponID,
		},
		Seller: seller,
	}
	orderDetailsByte, err := json.Marshal(orderDetail)
	if err != nil {
		return
	}
	orderDetails := dao.KiranaBazarOrderDetail{
		OrderID:      order.ID,
		OrderDetails: orderDetailsByte,
		UpdatedAt:    time.Now(),
	}
	_, err = s.repository.Create(&orderDetails)
	if err != nil {
		return
	}

	source := "APP"
	if request.Data.Email != "" {
		source = "D2R"
	}

	eventObject = map[string]interface{}{
		"distinct_id":         request.UserID,
		"order_value":         int(totalPayableAmount),
		"product_name":        productNames,
		"product_id":          productIds,
		"order_data":          productsOrderData,
		"ordering_module":     utils.MakeTitleCase(seller),
		"seller":              seller,
		"order_id":            fmt.Sprint(*order.ID),
		"total_discount":      discountAmount,
		"promo_code":          request.Data.BillBreakUp.Coupon,
		"cart_value":          totalPricingWithoutDiscount,
		"source":              source,
		"tag_ids":             tagIds,
		"tags":                tagNames,
		"cashback_applied":    appliedCashback,
		"pincode_blocked":     isPincodeBlockedOrder,
		"is_spam_user":        isSpamUser,
		"request_app_version": appVersion,
		"auto_confirm_order":  isAutoConfirmOrder,
		"virtual_sku_ids":     virtualSkuIds,
	}

	if paymentData != nil {
		eventObject["selected_payment"] = paymentData.Mode.Type
		eventObject["payment_amount"] = paymentData.PaymentAmount
		eventObject["payment_discount"] = paymentData.Mode.PaymentDiscount
	}

	if request.Data.Email != "" {
		eventObject["email"] = request.Data.Email
	}

	queueModalData := s.createQueueModalDataforNonConfirmedOrder(order.ID)

	if queueModalData != nil {
		queue.AddDataToQueue(context.Background(), s.repository, s.Mixpanel, *queueModalData)
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Placed", request.UserID, eventObject,
			fmt.Sprintf("%s_%s", "order_placed", fmt.Sprint(*order.ID))),
	})

	err = webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{request.UserID},
		EventName:   "Order Placed",
		EventObject: eventObject,
	})
	if err != nil {
		fmt.Println("failed to send webengage event")
	}

	// add in redis the order id for kirana club seller
	go func(userId, seller, orderId string) {
		if seller == utils.KIRANA_CLUB {
			err := s.udpateUserKiranaClubOrder(ctx, fmt.Sprintf("KIRANA_CLUB_ORDER::%s", userId), orderId, time.Hour*24*30)
			if err != nil {
				fmt.Println("error in adding order id to redis", err)
			}
		}
	}(request.UserID, seller, fmt.Sprint(*order.ID))

	activeCohortUserData := activationCohortUserData

	cartValue := orderDetail.GetCartValue()
	gtv := GetGTVValue(&orderDetail)
	go func() {
		_, err := s.AddDataForReconciliation(ctx, &dto.AddReconciliationRequest{
			OrderID: int64(*order.ID),
			Data: []dto.StatusTimeStamp{
				dto.StatusTimeStamp{
					OrderStatus: "order_placed",
					TimeStamp:   time.Now().UnixMilli(),
				},
				dto.StatusTimeStamp{
					OrderStatus: "expected_delivery_timestamp",
					TimeStamp:   time.Now().UnixMilli() + 604800000,
				},
			},
			Service:       "INTERNAL",
			OMS:           seller,
			DiscountValue: &discountAmount,
			GrossValue:    &totalPricingWithoutDiscount,
			OrderValue:    &totalPayableAmount,
			CartValue:     &cartValue,
			GTV:           &gtv,
		})
		if err != nil {
			fmt.Println("err in ", err)
			slack.SendSlackMessage(fmt.Sprintf("error in add data for reeconciliation %v", err))
		}

		if activeCohortUserData != nil && activeCohortUserData.Eligible && seller == utils.ZOFF_FOODS {
			// set coupon as redeemed in redis cache
			setActivationCohortCouponRedeemed(ctx, request.UserID, utils.ZOFF_FOODS, activeCohortUserData.CouponId, s.GcpRedis)
		}

		sourceId, exists := brands.GetCashbackActionsBySource(seller)

		if !exists || exists && (sourceId.ADD_CASHBACK == 0 || sourceId.REDEEM_CASHBACK == 0) {
			slack.SendSlackMessage(fmt.Sprintf("invalid cashback source id for seller %s, sourceId: %v in placeThirdPartySellerOrderV2", seller, sourceId))
			err = errors.New("invalid cashback source id")
			return
		}

		loyaltyOrderAmount := totalPayableAmount
		if request.Data.Seller == utils.RSB_SUPER_STOCKIST {
			zoffOrderAmount := utils.GetManufacturerCartValue(productsPricing, []string{utils.ZOFF_FOODS})
			excludingZoffOrderAmount := math.Max(0, totalPayableAmount-zoffOrderAmount)
			loyaltyOrderAmount = excludingZoffOrderAmount
		}
		// call here to add loyalty points for this order
		if request.Data.Seller != utils.ZOFF_FOODS {
			_, err = s.AddUserLoyaltyPoints(ctx, dto.AddUserLoyaltyPointsRequest{
				UserID: request.UserID,
				Meta:   request.Meta,
				Data: dto.AddUserLoyaltyPointsRequestData{
					OrderId:     *order.ID,
					OrderAmount: loyaltyOrderAmount,
					SourceId:    sourceId.ADD_CASHBACK,
				},
			})
			if err != nil {
				fmt.Println("error in adding user loyalty points", err)
			}
		}
	}()

	go func(orderID int64, s *Service) {
		_, err := s.CreatePickListForOrder(ctx, dto.CreatePickListForOrderRequest{
			Data: struct {
				OrderID string `json:"order_id"`
			}{OrderID: fmt.Sprint(orderID)},
		})
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error creating picklist for order: %d %v", orderID, err))
		}
	}(*order.ID, s)

	currentDateTime := time.Now()
	hindiDateString := fmt.Sprintf("%s %d, %d", hindiMonthsMap[int(currentDateTime.Month())], currentDateTime.Day(), currentDateTime.Year())
	timeString := currentDateTime.Format("3:04 PM 02-01-2006")

	// add Data to IVR queue
	// if order value is greater than 4k, MAX_ORDER_FOR_AUTOMATED_IVR not inserting
	// Adding IVR call 3 times in a queue (in difference of 8 hours)

	if totalPayableAmount < utils.MAX_ORDER_FOR_AUTOMATED_IVR &&
		!utils.NON_IVR_USERS[request.UserID] &&
		!isSpamUser &&
		(paymentData == nil || (paymentData != nil && paymentData.PaymentAmount == 0)) &&
		!isAutoConfirmOrder {
		appID := 0
		if data, ok := ivr.SellerIVRData[order.Seller][ivr.ORDER_CONFIRMATION_FLOW]; ok {
			if id, ok := data.(int); ok {
				appID = id
			}
		}
		if appID != 0 {
			// After one hour
			// add here for IVR 1 status update
			ivrStatus := ivrStatus.IVR_STATUS_MAP["ivr_1"]
			ivrStatusRequest := &dto.IVRStatusUpdateRequest{
				OrderId:    *order.ID,
				Status:     ivrStatus,
				IvrStatus:  ivrStatus,
				CallCount:  1,
				RetryCount: 0,
			}

			_, err := s.UpdateIVRCallStatus(ctx, ivrStatusRequest)
			if err != nil {
				fmt.Println("failed updating ivr 1 status", err)
			}

			triggerAt, _ := s.addDataToIVRQueue(*order.ID, order.Seller, totalPayableAmount, totalPricingWithoutDiscount, 1, request.UserID, *shippingAddress.Phone, appID, time.Now().Add(time.Hour*1), ivr.IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW)
			// 2nd call after 8 hour
			rand.Seed(time.Now().UnixNano())
			randomNumber := rand.Intn(61)
			triggerAt, _ = s.addDataToIVRQueue(*order.ID, order.Seller, totalPayableAmount, totalPricingWithoutDiscount, 2, request.UserID, *shippingAddress.Phone, appID, triggerAt.Add(time.Hour*3+time.Minute*30+time.Minute*time.Duration(randomNumber)), ivr.IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW)
			// 3rd call after 8 hours of second call i.e 16 hrs after 2nd call
			triggerAt, _ = s.addDataToIVRQueue(*order.ID, order.Seller, totalPayableAmount, totalPricingWithoutDiscount, 3, request.UserID, *shippingAddress.Phone, appID, triggerAt.Add(time.Hour*3+time.Minute*30+time.Minute*time.Duration(randomNumber)), ivr.IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW)
			// 4th call to check if user has still not receiving the call so that we can cancel the order
			triggerAt, _ = s.addDataToIVRQueue(*order.ID, order.Seller, totalPayableAmount, totalPricingWithoutDiscount, 4, request.UserID, *shippingAddress.Phone, appID, triggerAt.Add(time.Hour*3+time.Minute*30+time.Minute*time.Duration(randomNumber)), ivr.IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW)
		}
	}
	if err != nil {
	}

	err = cart.Empty(ctx, request.UserID, seller)
	if err != nil {
		return
	}

	successBanner := make(map[string]interface{})
	// if !LOYALTY_DATA_7_MAY[request.UserID] {
	// 	successBanner = utils.OrderSuccessBannerWidget
	// }

	if isAutoConfirmOrder {
		successBanner = utils.AUTO_CONFIRM_BANNER_WIDGET
	}

	response = dto.AppKiranaBazarOrderResponse{
		Data: []dto.OrderDetails{
			{
				ID:                 fmt.Sprint(*order.ID),
				TotalItems:         len(cartDetails),
				OrderDate:          currentDateTime,
				OrderDateString:    hindiDateString,
				OrderTimeString:    timeString,
				TotalAmount:        totalPayableAmount,
				TransactionID:      *request.Meta.Context.TransactionID,
				MessageID:          mid,
				OrderStatusDetails: nil,
				OrderStatus:        orderStatus,
				Banner:             successBanner,
				UserPhoneNumber:    *shippingAddress.Phone,
				UserName:           *shippingAddress.Name,
				IsAutoConfirmed:    isAutoConfirmOrder,
				OrderPayment: dto.OrderPayment{
					PaymentID:            fmt.Sprint(*payment.ID),
					PaymentMethod:        paymentMethod,
					PaymentTransactionID: *request.Meta.Context.TransactionID,
					Amount:               totalPayableAmount,
					PaymentStatus:        "PENDING",
				},
				OrderDetails: orderDetailsByte,
			},
		},
	}

	orderIdString := fmt.Sprint(*order.ID)

	go s.CreateOrderInvoice(context.Background(), dto.CreateInvoiceRequest{
		OrderID: fmt.Sprint(*order.ID),
	})

	if isPincodeBlockedOrder {
		// cancle order
		_, err = s.CancelKiranaBazarOrder(ctx, dto.AppCancelKiranaBazarOrderRequest{
			UserID: request.UserID,
			Data: dto.AppCancelKiranaBazarOrderData{
				OrderID:     fmt.Sprint(*order.ID),
				Email:       "<EMAIL>",
				Reason:      cancelreason.PincodeNotServiceable,
				Explanation: cancelreason.PincodeNotServiceable,
				Message:     cancelreason.PincodeNotServiceable,
				Source:      "BACKEND",
				StatusType:  "",
			},
		})
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("2 Error cancelling pincode not serviceable order %d: %v", *order.ID, err))
			return
		}
		err = errors.New("order cancelled pincode blocked by delhivery")
	}

	if isAutoConfirmOrder {
		advanceTaken := false
		s.UpdateB2BOrderStatus(ctx, &dto.UpdateB2BOrderStatusRequest{
			UpdatedBy: "system",
			Data: dto.UpdateB2BOrderStatusData{
				OrderID:        fmt.Sprint(*order.ID),
				UpdatedBy:      "<EMAIL>",
				OrderStatus:    constants.CONFIRMED,
				Source:         "BACKEND",
				UpdatedAt:      time.Now().UnixMilli(),
				OrderingModule: seller,
				OrderMeta: dto.OrderMeta{
					AdvanceTaken:    &advanceTaken,
					PaymentId:       fmt.Sprint(*payment.ID),
					PaymentDiscount: 0,
					Note:            "ORDER AUTO CONFIRMED",
				},
			},
		})
	}

	if !isPincodeBlockedOrder {
		oosProducts, _, err := s.Inventory.UpdateProductsInventory(ctx, cartItems, inventory.INVENTORY_OPERATION_TYPES.DEDUCT_INVENTORY)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error updating inventory for order %d: %v", *order.ID, err))
			return response, nil
		}

		go func(ctx context.Context, orderId int64, oosProducts []inventoryDao.KiranaBazarProductsInventory) {
			if len(oosProducts) > 0 {
				_, err := s.UpdateProductsStockStatus(ctx, oosProducts, true, "<EMAIL>")
				if err != nil {
					slack.SendSlackMessage(fmt.Sprintf("Error marking products as OOS for order %d: %v", orderId, err))
				}
			}
		}(ctx, *order.ID, oosProducts)
	}

	s.Coupons.RedeemCouponAsyncWithCb(ctx, request.UserID, orderIdString, func(response *coupons.RedeemCouponResponse, err error) {
		if err != nil {
			// Handle error if needed
			logger.Error(ctx, "coupon redemption failed", "error", err)
			return
		}
		// Handle success if needed
		logger.Info(ctx, "coupon redeemed", "success", response.Success)
		err = s.Coupons.InvalidateUserCouponUsageCache(ctx, request.UserID)
		if err != nil {
			logger.Error(ctx, "failed to invalidate user coupon usage cache", "error", err)
		}
	})

	return
}

// addDataToIVRQUeue is used to insert data to queue for triggereing the iVR call for the user whi has placed the order
func (s *Service) addDataToIVRQueue(orderID int64, seller string, totalPayableAmount, totalProductPrice float64, IVRCallCount int, userID string, userShippingPhone string, appID int, triggerAt time.Time, requestType string) (time.Time, error) {
	triggerat, err := ivrexotelintegration.AddDataToExotelIVRQueue(context.Background(), dto.ExotelIVROrderInfo{
		OrderID:      orderID,
		Seller:       seller,
		OrderValue:   totalPayableAmount,
		CartValue:    totalProductPrice,
		IVRCallCount: IVRCallCount,
		UserID:       userID,
		RequestType:  requestType,
	}, fmt.Sprintf("+91%s", userShippingPhone), ivr.IVR_TO_PHONE, appID, triggerAt, s.repository, s.Mixpanel, requestType, true, nil)
	return triggerat, err
}

func (s *Service) addOrderTags(orderId int64, tags []int) error {
	for _, tag := range tags {
		err := s.repository.Db.Clauses(clause.OnConflict{UpdateAll: true}).Save(&dao.KiranaBazarOrderTagMapping{
			OrderID:  orderId,
			TagID:    tag,
			IsActive: true,
		}).Error
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *Service) getUserCohortNames(userDetails userdetails.AsyncResult, userId, seller string) []string {
	userCohortNames := make([]string, 0)
	var userGeography *userdetails.UserGeoData
	if userDetails.Data != nil {
		if userDetails.Data.UserDynamicDetails != nil {
			userCohortNames = userDetails.Data.UserDynamicDetails.UserCohortNames
		}
		if userDetails.Data.UserGeography != nil {
			userGeography = userDetails.Data.UserGeography
		}
		userCohortNames = append(userCohortNames, userdetails.GetUserDerivedCohorts(userId, &[]string{seller}, userGeography)...)
	}
	return userCohortNames
}

func (s *Service) removeOrderTags(orderId int64) error {
	_, err := s.repository.CustomQuery(nil, fmt.Sprintf(`update kiranabazar_order_tag_mapping set is_active = false where order_id = %d;`, orderId))
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) removeOrderTag(orderId int64, tagId int) error {
	_, err := s.repository.CustomQuery(nil, fmt.Sprintf(`update kiranabazar_order_tag_mapping set is_active = false where order_id = %d and tag_id = %d;`, orderId, tagId))
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) GetCartDetailsForOrderDetails(ctx context.Context, request dto.AppCartDetailsForOrderDetailsRequest) (response dto.AppGetKiranaBazarCartResponse, err error) {
	orderID := request.Data.OrderID
	if orderID == "" {
		err = errors.New("orderID cannot be empty")
		return
	}

	order := dao.KiranaBazarOrderDetail{}

	orderid, err := strconv.Atoi(orderID)
	if err != nil {
		err = errors.New("orderID is not defined")
		return
	}
	orderid64 := uint64(orderid)
	_, err = s.repository.Find(map[string]interface{}{
		"order_id": orderid64,
	}, &order)

	if err != nil {
		err = errors.New("order with given orderid doest not exists")
		return
	}

	ods := dao.KiranaBazarOrderDetails{}
	err = json.Unmarshal(order.OrderDetails, &ods)
	if err != nil {
		return
	}

	sellerItems := ods.Cart

	response = dto.AppGetKiranaBazarCartResponse{
		Data: dto.AppGetKiranaBazarCartData{
			Products: sellerItems,
			OrderID:  orderID,
		},
	}

	return
}

func (s *Service) UpdateZoffOrder(ctx context.Context, request dto.AppCreateKiranaBazarOrderRequest) (response dto.AppKiranaBazarOrderResponse, err error) {

	seller := request.Data.BillBreakUp.Seller
	if includes(brands.GetAllBrands(), seller) {
		tid := getTransactionID()
		request.Meta.Context.TransactionID = &tid
	}

	shippingAddress, billingAddress, err := getUserAddress(request.UserID, false, fmt.Sprintf("%d", request.Data.AddressID), s.repository)
	if err != nil {
		return
	}

	mid := getMessageID()
	orderStatus := "INITIATED"
	paymentStatus := "PENDING"
	paymentMethod := "COD"
	cart, err := cart.Get(ctx, request.UserID, request.Data.Seller)
	if err != nil {
		return
	}

	cartDetails, _ := cart.Cart.Get()

	serviceablityResponse := &dto.AppserviceAbilityAPIResponse{}
	serviceablityResponse, err = s.CheckServiceAbility(ctx, &dto.AppServiceAblityAPIRequest{
		UserID:           request.UserID,
		DeliveryPostCode: *shippingAddress.PostalCode,
		Weight:           fmt.Sprintf("%d", utils.GetOrderMass(cartDetails)),
		COD:              "y",
		Data: dto.AppServiceAbilityAPIRequestData{
			Seller: seller,
		},
	})

	if err != nil {
		return
	}
	if !serviceablityResponse.Data.Servicable {
		err = errors.New("not servicable")
		return
	}

	initialAmount := 0.0

	orderID := request.Data.OrderID
	if orderID == "" {
		err = errors.New("orderID cannot be empty")
		return
	}

	orderid, err := strconv.Atoi(orderID)
	if err != nil {
		err = errors.New("orderID is not defined")
		return
	}
	orderid64 := int64(orderid)
	orderPayment := dao.KiranaBazarOrderPayment{}
	_, err = s.repository.Find(map[string]interface{}{
		"order_id": orderid64,
	}, &orderPayment)

	if err != nil {
		return
	}

	initialAmount = *orderPayment.Amount

	orderInfo := dao.KiranaBazarOrder{}
	_, err = s.repository.Find(map[string]interface{}{
		"id": orderid64,
	}, &orderInfo)

	if err != nil {
		err = errors.New("order with given orderid doest not exists")
		return
	}
	if request.Data.Email == "" {
		if *orderInfo.OrderStatus != "INITIATED" {
			err = fmt.Errorf("order status is %s not INITIATED", *orderInfo.OrderStatus)
			return
		}
	} else {
		if !(*orderInfo.OrderStatus == "INITIATED" || *orderInfo.OrderStatus == "NOTCONFIRMED" || *orderInfo.OrderStatus == "CANCELLED" || *orderInfo.OrderStatus == "UNCONTACTABLE") {
			err = fmt.Errorf("order status is %s not INITIATED or NOTCONFIRMED or CANCELLED or UNCONTACTABLE", *orderInfo.OrderStatus)
			return
		}
	}

	_, _, err = s.repository.Update(dao.KiranaBazarOrder{
		ID: &orderid64,
	}, dao.KiranaBazarOrder{
		UpdatedAt: time.Now(),
	})
	if err != nil {
		return
	}

	productPricing := []shared.ProductPrice{}
	totalProductPricing := []shared.TotalPricing{}
	discountPricing := []shared.DiscountPricing{}

	totalProductPrice := 0.0
	totalDiscount := 0.0
	totalPayableAmount := 0.0
	totalCharges := 0.0
	for _, pric := range request.Data.BillBreakUp.ProductsPricing {
		productDao := shared.ProductPrice{
			ID:           pric.ID,
			OfferedValue: pric.OfferedValue,
			Quantity:     pric.Quantity,
			TotalValue:   pric.TotalValue,
			ProductName:  pric.ProductName,
			PackSize:     pric.PackSize,
			Key:          pric.Key,
			Value:        pric.Value,
			Manufacturer: pric.Manufacturer,
		}
		if pric.Styles != nil {
			productDao.Styles = pric.Styles
		}
		productPricing = append(productPricing, productDao)
		tval, _ := strconv.ParseFloat(pric.TotalValue, 32)
		totalProductPrice += tval
	}

	for _, pric := range request.Data.BillBreakUp.DiscountPricing {
		discountDao := shared.DiscountPricing{
			Key:        pric.Key,
			Value:      pric.Value,
			TotalValue: pric.TotalValue,
			IsInternal: pric.IsInternal,
		}
		if pric.Styles != nil {
			discountDao.Styles = pric.Styles
		}
		discountPricing = append(discountPricing, discountDao)

		if pric.Type == offers.OFFER_TYPES.CHARGE {
			totalCharges += pric.TotalValue
		}
	}

	for _, pric := range request.Data.BillBreakUp.TotalPricing {
		discountDao := shared.TotalPricing{
			Key:        pric.Key,
			Value:      pric.Value,
			TotalValue: pric.TotalValue,
		}
		if pric.Styles != nil {
			discountDao.Styles = pric.Styles
		}
		totalProductPricing = append(totalProductPricing, discountDao)
		if pric.Key != "टोटल" && pric.Type != offers.OFFER_TYPES.CHARGE {
			tval := pric.TotalValue
			totalDiscount += tval
		}
	}

	totalDiscount = -1 * totalDiscount

	if totalCharges < 0 {
		totalCharges = totalCharges * -1
	}

	totalPayableAmount = totalProductPrice - totalDiscount + totalCharges

	paymentID := uuid.New().String()
	payment := dao.KiranaBazarOrderPayment{
		ID:            &paymentID,
		OrderID:       &orderid64,
		PaymentMethod: &paymentMethod,
		TransactionID: request.Meta.Context.TransactionID,
		Amount:        &totalPayableAmount,
		Status:        &paymentStatus,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
	_, _, err = s.repository.Update(&dao.KiranaBazarOrderPayment{
		OrderID: &orderid64,
	}, &payment)
	if err != nil {
		return
	}

	cartItems, _ := cart.Cart.Get()
	orderDetail := dao.KiranaBazarOrderDetails{
		Cart:            cartItems,
		ShippingAddress: shippingAddress,
		BillingAddress:  &billingAddress,
		TotalItems:      len(cartDetails),
		TotalAmount:     totalPayableAmount,
		BillBreakUp: dao.BillDetails{
			ProductsPricing: productPricing,
			TotalPricing:    totalProductPricing,
			DiscountPricing: discountPricing,
			EnableOrdering:  request.Data.BillBreakUp.EnableOrdering,
			Message:         request.Data.BillBreakUp.Message,
			Coupon:          request.Data.BillBreakUp.Coupon,
		},
	}
	orderDetailsByte, err := json.Marshal(orderDetail)
	if err != nil {
		return
	}
	orderDetails := dao.KiranaBazarOrderDetail{
		OrderID:      &orderid64,
		OrderDetails: orderDetailsByte,
		UpdatedAt:    time.Now(),
	}
	_, _, err = s.repository.Update(&dao.KiranaBazarOrderDetail{
		OrderID: &orderid64,
	}, &orderDetails)
	if err != nil {
		return
	}

	productNames := []string{}
	productIds := []string{}
	for _, product := range cartDetails {
		meta := shared.KiranaBazarProductMeta{}
		err = json.Unmarshal(product.Meta, &meta)
		if err != nil {
			meta.HindiName = product.Name
		}
		productNames = append(productNames, string(meta.HindiName))
		productIds = append(productIds, product.ID)
	}

	source := "APP"
	if request.Data.Email != "" {
		source = "D2R"
	}
	amountDifference := totalPayableAmount - initialAmount

	eventObject := map[string]interface{}{
		"distinct_id":       request.UserID,
		"order_value":       int(totalPayableAmount),
		"product_name":      productNames,
		"product_id":        productIds,
		"seller":            request.Data.BillBreakUp.Seller,
		"ordering_module":   utils.MakeTitleCase(request.Data.BillBreakUp.Seller),
		"order_id":          fmt.Sprint(orderid64),
		"total_discount":    totalDiscount,
		"promo_code":        request.Data.BillBreakUp.Coupon,
		"cart_value":        totalProductPrice,
		"source":            source,
		"difference_amount": amountDifference,
	}

	if request.Data.Email != "" {
		eventObject["email"] = request.Data.Email
	}

	cartValue := orderDetail.GetCartValue()
	gtv := GetGTVValue(&orderDetail)
	_, err = s.AddDataForReconciliation(ctx, &dto.AddReconciliationRequest{
		OrderID: int64(orderid64),
		Data: []dto.StatusTimeStamp{
			dto.StatusTimeStamp{
				OrderStatus: "order_placed",
				TimeStamp:   time.Now().UnixMilli(),
			},
			dto.StatusTimeStamp{
				OrderStatus: "expected_delivery_timestamp",
				TimeStamp:   time.Now().UnixMilli() + 604800000,
			},
		},
		Service:       "INTERNAL",
		OMS:           request.Data.BillBreakUp.Seller,
		DiscountValue: &totalDiscount,
		GrossValue:    &totalProductPrice,
		OrderValue:    &totalPayableAmount,
		CartValue:     &cartValue,
		GTV:           &gtv,
	})
	if err != nil {
		fmt.Println("err in ", err)
		slack.SendSlackMessage(fmt.Sprintf("error in add data for reeconciliation %v", err))
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Updated", request.UserID, eventObject),
	})

	err = webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{request.UserID},
		EventName:   "Order Updated",
		EventObject: eventObject,
	})
	if err != nil {
		fmt.Println("failed to send webengage event")
	}

	response = dto.AppKiranaBazarOrderResponse{
		Data: []dto.OrderDetails{
			{
				ID:                 fmt.Sprint(orderid64),
				TotalItems:         len(cartDetails),
				OrderDate:          time.Now(),
				TotalAmount:        totalPayableAmount,
				TransactionID:      *request.Meta.Context.TransactionID,
				MessageID:          mid,
				OrderStatusDetails: nil,
				OrderStatus:        orderStatus,
				OrderPayment: dto.OrderPayment{
					PaymentID:            fmt.Sprint(*payment.ID),
					PaymentMethod:        paymentMethod,
					PaymentTransactionID: *request.Meta.Context.TransactionID,
					Amount:               totalPayableAmount,
					PaymentStatus:        "PENDING",
				},
				OrderDetails: orderDetailsByte,
			},
		},
	}

	go s.CreateOrderInvoice(context.Background(), dto.CreateInvoiceRequest{
		OrderID: fmt.Sprint("%d", orderid64),
	})

	go func(orderID int64, s *Service) {
		_, err := s.CreatePickListForOrder(ctx, dto.CreatePickListForOrderRequest{
			Data: struct {
				OrderID string `json:"order_id"`
			}{OrderID: fmt.Sprint(orderID)},
		})
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error creating picklist for order: %d %v", orderID, err))
		}
	}(orderid64, s)

	return
}

// populateFreebiesInCart adds freebies and bonus products to the cart based on applicable promotions
func (s *Service) populateFreebiesInCart(ctx context.Context, request *dto.ConfirmOrderRequest, orderDetails *dao.KiranaBazarOrderDetails) (*dao.KiranaBazarOrderDetails, error) {
	if orderDetails == nil {
		return nil, fmt.Errorf("order details cannot be nil")
	}

	seller := orderDetails.Seller
	if seller == "" {
		return orderDetails, nil
	}
	source, exists := brands.GetSourceBySeller(seller)

	if !exists {
		slack.SendSlackMessage(fmt.Sprintf("source not found for seller: %s populate freebies in cart", seller))
		return orderDetails, fmt.Errorf("source not found for seller: %s", seller)
	}

	originalGtv := orderDetails.GetGtvValue()

	// Build maps of current cart products for lookup
	currentCartProductsIds := make(map[string]bool)
	currentCartProductQuantities := make(map[string]BonusSkuCartMapItem)

	for _, product := range orderDetails.Cart {
		if product.ID != "" {
			currentCartProductsIds[product.ID] = true
			currentCartProductQuantities[product.ID] = BonusSkuCartMapItem{
				Quantity:     int(product.Quantity),
				Manufacturer: product.Manufacturer,
			}
		}
	}

	// Initialize maps for tracking freebies and bonus SKUs
	freebieMap := make(map[string]FreebieDetails)
	bonusSkuMap := make(map[string]BonusSkuDetails)

	// Get all coupon descriptions for freebie lookup
	allCouponsKeyMap := getAllCouponsDesMap()

	// Collect freebie IDs to fetch
	var freebieProductIDs []string

	// Process each discount to identify freebies and bonus SKUs
	for _, discount := range orderDetails.BillBreakUp.DiscountPricing {
		// Handle direct coupon ID
		couponId := discount.ID
		if couponId == 0 {
			// Try to find by key in the coupon descriptions map
			if value, exists := allCouponsKeyMap[discount.Key]; exists {
				// Handle legacy format where freebie info comes from description map
				if value.Type == "FreeBie" && !currentCartProductsIds[value.Freebie.ProductID] {
					// only add if it does not already exisits in user's cart
					freebieProductIDs = append(freebieProductIDs, value.Freebie.ProductID)
					freebieMap[value.Freebie.ProductID] = FreebieDetails{
						Quantity: value.Freebie.Quantity,
						Value:    value.Freebie.Value,
					}
				}
			}
			continue
		}

		// Modern approach - fetch coupon by ID
		coupon, err := s.Coupons.GetCouponByID(ctx, couponId)
		if err != nil {
			logger.Error(ctx, "error getting coupon by ID %d: %v", couponId, err)
			continue
		}

		if coupon == nil {
			logger.Warn(ctx, "coupon not found for ID: %d", couponId)
			continue
		}

		// Only process freebie-type discounts
		if coupon.DiscountType != coupons.DiscountTypeFreebie {
			continue
		}

		if len(coupon.Rules) == 0 {
			logger.Warn(ctx, "coupon %d has no rules defined", couponId)
			continue
		}

		for _, rule := range coupon.Rules {
			// Extract rules
			rules, err := rule.GetCouponRules()
			if err != nil {
				logger.Error(ctx, "error parsing coupon rules for coupon %d: %v", couponId, err)
				continue
			}

			if rules == nil {
				logger.Warn(ctx, "coupon %d has nil rules", couponId)
				continue
			}

			// Handle direct product freebie
			if rules.ProductId != nil && rules.Quantity != nil && *rules.Quantity > 0 {
				// Only add if it doesn't already exist in the cart
				if !currentCartProductsIds[*rules.ProductId] {
					freebieValue := 0.0
					if rules.Value != nil {
						freebieValue = *rules.Value
					}

					freebieProductIDs = append(freebieProductIDs, *rules.ProductId)
					freebieMap[*rules.ProductId] = FreebieDetails{
						Quantity: *rules.Quantity,
						Value:    freebieValue,
						CouponID: couponId,
					}
					logger.Info(ctx, "found direct freebie from coupon %d: %s, qty: %d",
						couponId, *rules.ProductId, *rules.Quantity)
				}
			}
			if rules.BonusSkusConditions != nil && len(rules.BonusSkusConditions.EligibleSkus) > 0 {
				// Handle bonus SKUs conditions
				logger.Info(ctx, "processing bonus SKU conditions for coupon %d with %d eligible skus",
					couponId, len(rules.BonusSkusConditions.EligibleSkus))

				// Check if any eligible SKUs are in the cart
				for _, sku := range rules.BonusSkusConditions.EligibleSkus {
					if sku.ProductID == "" || sku.BonusProductID == "" {
						logger.Warn(ctx, "invalid bonus SKU configuration in coupon %d", couponId)
						continue
					}

					// If the eligible product is in the cart, record the bonus product details
					if currentCartProductItem, exists := currentCartProductQuantities[sku.ProductID]; exists && currentCartProductItem.Quantity > 0 && currentCartProductItem.Quantity >= int(sku.MinQuantity) {
						bonusSkuMap[sku.BonusProductID] = BonusSkuDetails{
							CartProductID:       sku.ProductID,
							CartProductQuantity: currentCartProductItem.Quantity,
							ProductID:           sku.BonusProductID,
							Quantity:            sku.BonusQuantity,
							Type:                sku.Type,
							StepConfig:          sku.StepConfig,
							Tiers:               sku.Tiers,
							CouponID:            couponId,
							Manufacturer:        currentCartProductItem.Manufacturer,
						}

						logger.Info(ctx, "found bonus SKU from coupon %d: cart product %s triggers bonus product %s",
							couponId, sku.ProductID, sku.BonusProductID)
					}
				}
			}
		}
	}

	// Get current product pricing and cart data
	productPricingArr := orderDetails.BillBreakUp.ProductsPricing
	discountPricingArr := orderDetails.BillBreakUp.DiscountPricing
	orderDetailsCart := orderDetails.Cart

	// If there are freebies to process
	if len(freebieProductIDs) > 0 {
		logger.Info(ctx, "found %d freebies to fetch: %v", len(freebieProductIDs), freebieProductIDs)

		// Set conditions for product fetch
		condition := &dto.TopProductsConditions{FetchInactiveProducts: true}

		// Create fetch request
		exclusive := true
		appSearchRequest := &dto.AppSearchRequest{
			UserID: request.Data.UserID,
			Data: dto.AppSearchRequestData{
				Exclusive:     &exclusive,
				Source:        &source,
				Seller:        seller,
				TopProductIds: freebieProductIDs,
				Conditions:    condition,
			},
			Meta: request.Meta,
		}

		// Fetch freebie product details
		productsResponse, err := s.GetProductsV2(ctx, appSearchRequest)
		if err != nil {
			logger.Error(ctx, "failed to fetch freebie products: %v", err)
			return orderDetails, nil // Return original order details rather than failing
		}

		// Parse product response
		parsedProductsResponse, ok := productsResponse.Data.Items.([]shared.SellerItems)
		if !ok {
			logger.Warn(ctx, "failed to parse freebie products response")
			parsedProductsResponse = make([]shared.SellerItems, 0)
		}

		if len(parsedProductsResponse) == 0 {
			logger.Warn(ctx, "no freebie products found in response")
			return orderDetails, nil
		}

		// Process fetched freebie products
		for i, product := range parsedProductsResponse {
			// Skip if product ID is empty
			if product.ID == "" {
				continue
			}

			var productMeta shared.KiranaBazarProductMeta
			if err := json.Unmarshal(product.Meta, &productMeta); err != nil {
				logger.Error(ctx, "failed to unmarshal meta for freebie product %s: %v", product.ID, err)
				continue
			}

			// Get freebie details and update with product info
			freebie := freebieMap[product.ID]
			freebie.HindiName = productMeta.HindiName
			freebie.PackSize = productMeta.PackSize
			freebie.ProductMetaWholesaleRate = productMeta.WholesaleRate
			freebie.ProductMetaQuantity = productMeta.Quantity
			freebie.IsProcessed = true

			// Update product quantity
			product.Quantity = int32(freebie.Quantity)

			// Update in response and map
			parsedProductsResponse[i] = product
			freebieMap[product.ID] = freebie

			logger.Info(ctx, "processed freebie product %s: %s, qty: %d",
				product.ID, freebie.HindiName, freebie.Quantity)
		}

		// Add all properly processed freebies to product pricing and cart
		for productID, val := range freebieMap {
			if !val.IsProcessed {
				logger.Warn(ctx, "skipping unprocessed freebie: %s", productID)
				continue
			}

			// Add to product pricing array
			productPricingArr = append(productPricingArr, shared.ProductPrice{
				ID:           productID,
				Key:          fmt.Sprintf("%s %s (%d पैकेट्स) x %d", val.HindiName, val.ProductMetaQuantity, val.PackSize, val.Quantity),
				Size:         val.ProductMetaQuantity,
				Value:        fmt.Sprintf("₹%.2f", val.Value),
				Quantity:     val.Quantity,
				PackSize:     val.PackSize,
				TotalValue:   fmt.Sprintf("%.2f", val.Value),
				ProductName:  val.HindiName,
				OfferedValue: fmt.Sprintf("%.2f", val.ProductMetaWholesaleRate),
				Type:         fmt.Sprintf("freebie:%d", val.CouponID),
				Manufacturer: val.Manufacturer,
			})
		}

		// Add all valid freebie products to cart
		orderDetailsCart = append(orderDetailsCart, parsedProductsResponse...)
	}

	// Process bonus SKUs
	if len(bonusSkuMap) > 0 {
		logger.Info(ctx, "processing %d bonus SKUs", len(bonusSkuMap))

		// Track discount totals per coupon
		couponDiscountTotals := make(map[int64]float64)

		// First handle bonus SKUs already in the cart
		for productID, bonusDetails := range bonusSkuMap {
			// Find existing product in cart
			found := false
			for i, cartItem := range orderDetailsCart {
				if cartItem.ID == productID {
					found = true
					if cartItem.SkuConfirmationProcessed {
						logger.Warn(ctx, "bonus product %s already processed in cart, skipping", productID)
						break
					}

					additionalQuantity := calculateAdditionalQuantity(bonusDetails)

					// Skip if nothing to add
					if additionalQuantity <= 0 {
						continue
					}

					// Update cart item
					orderDetailsCart[i].Quantity += additionalQuantity
					orderDetailsCart[i].SkuConfirmationProcessed = true

					// Extract product metadata
					var productMeta shared.KiranaBazarProductMeta
					err := json.Unmarshal(cartItem.Meta, &productMeta)
					if err != nil {
						logger.Error(ctx, "failed to unmarshal meta for bonus product %s: %v", productID, err)
						continue
					}

					// Calculate total value for bonus product
					productTotal := float64(additionalQuantity) * productMeta.WholesaleRate * float64(productMeta.PackSize)

					// Add to product pricing array
					productPricingArr = append(productPricingArr, shared.ProductPrice{
						ID:           productID,
						Key:          fmt.Sprintf("%s %s (%d पैकेट्स) x %d", productMeta.HindiName, productMeta.Quantity, productMeta.PackSize, additionalQuantity),
						Size:         productMeta.Quantity,
						Value:        fmt.Sprintf("₹%0.2f", productTotal),
						Quantity:     int(additionalQuantity),
						PackSize:     productMeta.PackSize,
						TotalValue:   fmt.Sprintf("%0.2f", productTotal),
						ProductName:  productMeta.HindiName,
						OfferedValue: fmt.Sprint(productMeta.WholesaleRate),
						Type:         fmt.Sprintf("bonus:%d", bonusDetails.CouponID),
						Manufacturer: cartItem.Manufacturer,
					})

					// Track discount total for this specific coupon
					couponDiscountTotals[bonusDetails.CouponID] += productTotal

					logger.Info(ctx, "added bonus quantity %d to existing cart product %s for coupon %d", additionalQuantity, productID, bonusDetails.CouponID)
					break
				}
			}

			// If not found in cart, fetch product details and add as new item
			if !found {
				productInfo, exists := products.GetProductByID(productID)
				if !exists {
					logger.Warn(ctx, "bonus product %s not found in product catalog", productID)
					continue
				}

				// Calculate bonus quantity
				bonusQuantity := calculateAdditionalQuantity(bonusDetails)

				// Skip if nothing to add
				if bonusQuantity <= 0 {
					continue
				}

				// Create cart item
				productCartItem := productInfo.ToSellerItems(products.ToSellerItemsCondition{
					IncludeVariant: true,
				}, "", nil)
				//TODO: To use conditional pricing we need to pass userContext

				productCartItem.Quantity = bonusQuantity
				productCartItem.SkuConfirmationProcessed = true

				// Add to cart
				orderDetailsCart = append(orderDetailsCart, productCartItem)

				// Add to product pricing
				productMeta := productInfo.MetaProperties
				productTotal := float64(bonusQuantity) * productMeta.WholesaleRate * float64(productMeta.PackSize)

				productPricingArr = append(productPricingArr, shared.ProductPrice{
					ID:           productID,
					Key:          fmt.Sprintf("%s %s (%d पैकेट्स) x %d", productMeta.HindiName, productMeta.Quantity, productMeta.PackSize, bonusQuantity),
					Size:         productMeta.Quantity,
					Value:        fmt.Sprintf("₹%0.2f", productTotal),
					Quantity:     int(bonusQuantity),
					PackSize:     productMeta.PackSize,
					TotalValue:   fmt.Sprintf("%0.2f", productTotal),
					ProductName:  productMeta.HindiName,
					OfferedValue: fmt.Sprint(productMeta.WholesaleRate),
					Type:         fmt.Sprintf("bonus:%d", bonusDetails.CouponID),
					Manufacturer: productCartItem.Manufacturer,
				})

				// Track discount total for this specific coupon
				couponDiscountTotals[bonusDetails.CouponID] += productTotal

				logger.Info(ctx, "added new bonus product %s with quantity %d to cart for coupon %d", productID, bonusQuantity, bonusDetails.CouponID)
			}
		}

		// Update discount pricing array for all applicable coupons
		for couponID, discountTotal := range couponDiscountTotals {
			if discountTotal > 0 {
				// Update the discount value for each coupon that provided bonus SKUs
				for i, discount := range discountPricingArr {
					if discount.ID == couponID {
						// Update the total value for this discount
						discountPricingArr[i].TotalValue += discountTotal
						discountPricingArr[i].Value = fmt.Sprintf("-₹%.2f", discountPricingArr[i].TotalValue)
						logger.Info(ctx, "updated bonus discount for coupon %d: new total value %.2f", couponID, discountPricingArr[i].TotalValue)
						break
					}
				}
			}
		}
	}

	// Update order details with new product pricing and cart
	orderDetails.BillBreakUp.ProductsPricing = productPricingArr
	orderDetails.BillBreakUp.DiscountPricing = discountPricingArr
	orderDetails.Cart = orderDetailsCart

	// Persist updated order details
	jsonData, err := json.Marshal(orderDetails)
	if err != nil {
		return nil, fmt.Errorf("error marshalling updated order details: %w", err)
	}

	// Parse order ID
	orderid, err := strconv.Atoi(request.Data.OrderID)
	if err != nil || orderid == 0 {
		return nil, fmt.Errorf("invalid order ID: %s", request.Data.OrderID)
	}

	orderid64 := int64(orderid)
	_, _, err = s.repository.Update(dao.KiranaBazarOrderDetail{OrderID: &orderid64}, dao.KiranaBazarOrderDetail{OrderDetails: jsonData})
	if err != nil {
		return nil, fmt.Errorf("failed to update order details: %w", err)
	}

	updatedGtvValue := orderDetails.GetGtvValue()

	if originalGtv != updatedGtvValue {
		// update in reconcilation table
		_, _, err := s.repository.Update(dao.KiranaBazarReconciliation{
			OrderID: orderid64,
		}, dao.KiranaBazarReconciliation{
			GrossValue: updatedGtvValue,
		})
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("failed to update gross value in reconciliation for order %d: %v", orderid64, err))
			logger.Error(ctx, "failed to update gross value in reconciliation for order %d: %v", orderid64, err)
		}
	}

	return orderDetails, nil
}

func (s *Service) UpdateThirdPartySellerOrder(ctx context.Context, request dto.BillDifferenceRequest) (response dto.AppKiranaBazarOrderResponse, err error) {
	message := fmt.Sprintf("Edit Order Not Supported at the moment for order %s", request.Data.OrderID)
	slack.SendSlackMessage(message)
	err = fmt.Errorf(message)
	return

	seller := request.Data.Seller
	if seller == "" {
		err = errors.New("no seller in the request")
		return
	}

	appVersion := request.Meta.AppVersion
	orderID := request.Data.OrderID

	if orderID == "" {
		err = errors.New("no orderid in the request")
		return
	}

	intOrderID, _ := strconv.Atoi(orderID)
	orderInfoDB, err := GetOrderInfo(s.repository, int64(intOrderID))
	if err != nil {
		return
	}
	orderDetailsDB, err := GetOrderDetails(s.repository, orderID)
	if err != nil {
		return
	}

	// min order value for the seller
	var MIN_ORDER_VALUE float64 = userdetails.GetUserSellerMinOrderValue(request.UserID, seller, true, "update_third_party_seller_order")
	if MIN_ORDER_VALUE < 0 {
		err = errors.New("not able to get min order value")
		return
	}
	if slices.Contains(ordervalue.MIN_ORDER_VALUE_EXCEPTION_EMAILS, request.Email) {
		MIN_ORDER_VALUE = 0
	}
	couponID := request.Data.CouponID
	couponIDInt, err := strconv.Atoi(couponID)
	coupon := dto.Coupon{}
	if err == nil {
		couponResponse, errr := s.GetCoupons(ctx, dto.GetCouponsRequest{
			UserID: request.UserID,
			Data: dto.GetCouponData{
				CouponID: couponIDInt,
				Seller:   request.Data.Seller,
			},
		})
		if errr != nil {
			return
		}
		coupon = couponResponse.Data.Coupons[0]
	}

	mid := getMessageID()
	if request.Meta.Context.MessageID != nil {
		mid = *request.Meta.Context.MessageID
	}

	shippingAddress, billingAddress, err := getUserAddress(request.UserID, false, fmt.Sprintf("%d", *orderDetailsDB.ShippingAddress.ID), s.repository)
	if err != nil {
		return
	}

	activationCohortEligibilityChannel := make(chan *dto.ActivationCohortUserData, 1)

	go getActivationCohortUserData(ctx, request.UserID, utils.ZOFF_FOODS, s.GcpRedis, activationCohortEligibilityChannel)

	userCashbackBalanceChannel := make(chan *float64, 1)
	go s.getUserCashbackBalanceFromService(ctx, request.UserID, request.Data.Seller, request.Meta, userCashbackBalanceChannel)

	orderStatus := processingstatus.PLACED
	paymentStatus := paymentstatus.UNPAID
	paymentMethod := constants.COD
	// cartObject, err := cart.Get(ctx, request.UserID, seller)
	// if err != nil {
	// 	return
	// }

	cartDetails := request.Data.Cart

	productNames := []string{}
	productIds := []string{}
	for _, product := range cartDetails {
		meta := shared.KiranaBazarProductMeta{}
		err = json.Unmarshal(product.Meta, &meta)
		if err != nil {
			meta.HindiName = product.Name
		}
		productNames = append(productNames, string(meta.HindiName))
		productIds = append(productIds, product.ID)
	}

	inStockProductIds, err := s.CheckProductsOutOfStock(productIds)
	if err != nil {
		return
	}

	if (len(inStockProductIds) != len(productIds) || len(inStockProductIds) == 0) && !slices.Contains(cart.OOSExceptionEmails, request.Email) {
		err = errors.New(exceptions.OosErrorMessage)
		return
	}

	pproductsPricing := []shared.ProductPrice{}
	ddiscountPricing := []shared.DiscountPricing{}

	ttotalProductPricing := 0.0
	ttotalRewardCoins := 0

	for _, item := range cartDetails {
		if err != nil {
			fmt.Println("err ", err)
		}
		if !includes(inStockProductIds, item.ID) {
			continue
		}
		meta := shared.KiranaBazarProductMeta{}
		err = json.Unmarshal(item.Meta, &meta)
		if err != nil {
			return
		}
		pproductsPricing = append(pproductsPricing, shared.ProductPrice{
			ID:           item.ID,
			OfferedValue: fmt.Sprintf("%.2f", meta.WholesaleRate),
			Quantity:     int(item.Quantity),
			TotalValue:   fmt.Sprintf("%.2f", meta.WholesaleRate*float64(item.Quantity)*float64(meta.PackSize)),
			ProductName:  meta.HindiName,
			PackSize:     meta.PackSize,
			Size:         meta.Quantity,
			Key:          fmt.Sprintf("%s %s (%d पैकेट्स) x %d", meta.HindiName, meta.Quantity, meta.PackSize, item.Quantity),
			Value:        fmt.Sprintf("₹%.2f", meta.WholesaleRate*float64(item.Quantity)*float64(meta.PackSize)),
			Manufacturer: item.Manufacturer,
		})
		ttotalProductPricing += meta.WholesaleRate * float64(item.Quantity) * float64(meta.PackSize)
		if meta.RewardCoins != nil {
			ttotalRewardCoins += *meta.RewardCoins
		}
	}

	ttotalPricingWithoutDiscount := ttotalProductPricing

	ttotalPricingMap := []shared.TotalPricing{}

	discountAmount := 0.0
	discountReason := ""

	activationCohortUserData := <-activationCohortEligibilityChannel
	if request.Data.Seller == utils.KIRANA_CLUB {
		styles := map[string]interface{}{
			"color":      "#009E7F",
			"fontWeight": 700,
		}

		ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      fmt.Sprintf("₹%d", KC_OFFER_DELIVER_CHARGE),
			TotalValue: KC_OFFER_DELIVER_CHARGE,
			Styles:     &styles,
			Name:       "Service Charge",
			Type:       offers.OFFER_TYPES.CHARGE,
		})

		ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      fmt.Sprintf("₹%d", KC_OFFER_DELIVER_CHARGE),
			TotalValue: KC_OFFER_DELIVER_CHARGE,
			Styles:     &styles,
			Name:       "Service Charge",
			Type:       offers.OFFER_TYPES.CHARGE,
		})
		ttotalProductPricing += KC_OFFER_DELIVER_CHARGE
		ttotalPricingWithoutDiscount += KC_OFFER_DELIVER_CHARGE
	}

	tpm, disc, err := s.GetBackendDiscount(ttotalPricingWithoutDiscount, ttotalProductPricing, request.Data.Seller, request.UserID, activationCohortUserData)
	if err == nil {
		for _, tpmDiscount := range tpm {
			discValue := tpmDiscount.TotalValue
			value := tpmDiscount.Value
			if discValue > 0 {
				discValue = discValue * -1
			}
			ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
				Key:        tpmDiscount.Key,
				Value:      value,
				TotalValue: math.Floor(discValue*100) / 100, // TotalValue is negative here
				Styles:     tpmDiscount.Styles,
			})
			ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
				Key:        tpmDiscount.Key,
				Value:      value,
				TotalValue: math.Floor(discValue*100) / 100, // TotalValue is negative here
				Styles:     tpmDiscount.Styles,
				IsInternal: tpmDiscount.IsInternal,
			})

			discountReason = discountReason + fmt.Sprintf("%s %s | ", tpmDiscount.Key, tpmDiscount.Value)
			discountAmount += (-1 * discValue)
		}
		ttotalProductPricing -= disc
	}

	if coupon.Valid && coupon.MinimumAmount <= ttotalProductPricing {
		discountInt, err := strconv.Atoi(coupon.Discount)
		if err != nil {
			fmt.Println("err ", err)
		}
		discount := float64(discountInt)
		if coupon.PercentageDiscount > 0.0 {
			discount = ttotalProductPricing * coupon.PercentageDiscount * 0.01
		}

		styles := map[string]interface{}{
			"color":      "#009E7F",
			"fontWeight": 700,
		}

		ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
			Key:        coupon.Description,
			Value:      "-₹" + fmt.Sprintf("%0.2f", discount),
			TotalValue: -1 * discount,
			Styles:     &styles,
		})

		ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
			Key:        coupon.Description,
			Value:      "-₹" + fmt.Sprintf("%0.2f", discount),
			TotalValue: -1 * discount,
			Styles:     &styles,
			IsInternal: coupon.IsInternal,
		})

		ttotalProductPricing -= float64(discount)
		discountReason = discountReason + fmt.Sprintf("%s %0.2f | ", coupon.Code, discount)
		discountAmount += discount
	}

	if ttotalProductPricing >= MIN_ORDER_VALUE && request.Data.Seller != utils.KIRANA_CLUB {
		styles := map[string]interface{}{
			"color":      "#009E7F",
			"fontWeight": 700,
		}

		freeDeliveryString := fmt.Sprintf("₹̶%s फ्री डिलीवरी", utils.GenerateStrikethrough(fmt.Sprintf("%d", 100)))
		ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      freeDeliveryString,
			TotalValue: 0,
			Styles:     &styles,
		})

		ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      freeDeliveryString,
			TotalValue: 0,
			Styles:     &styles,
			IsInternal: true,
		})
	}

	ttotalRewardCoins = 100
	rewardCoins := dto.RewardCoinsData{}
	err = json.Unmarshal([]byte(utils.RewardsCoinsData), &rewardCoins)
	if err != nil {
		return
	}
	rewardCoins.Amount = ttotalRewardCoins
	rewardCoins.Text = fmt.Sprintf("%d किराना कॉइन्स", ttotalRewardCoins)

	waysToPayData := make([]dto.WaysToPayDataV1, 0)
	err = json.Unmarshal([]byte(utils.WaysToPayData), &waysToPayData)
	if err != nil {
		return
	}

	// var userCashbackOfferComponent *dto.CashbackComponent
	userCashbackBalance := <-userCashbackBalanceChannel
	if userCashbackBalance != nil && *userCashbackBalance > 0 {
		// userCashbackOfferComponent = generateLoyaltyCashbackComponent(*userCashbackBalance)
		if request.Data.RequestedCashback > 0 {
			styles := map[string]interface{}{
				"color":      "#009E7F",
				"fontWeight": 700,
			}
			if request.Data.RequestedCashback > *userCashbackBalance {
				request.Data.RequestedCashback = *userCashbackBalance
			}

			if request.Data.RequestedCashback <= ttotalProductPricing {
				ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
					Key:        "कैशबैक",
					Value:      fmt.Sprintf("₹%0.2f", request.Data.RequestedCashback),
					TotalValue: -1 * float64(request.Data.RequestedCashback),
					Styles:     &styles,
					Type:       offers.OFFER_TYPES.CASHBACK,
				})

				discountAmount += float64(request.Data.RequestedCashback)
				discountReason = discountReason + fmt.Sprintf("कैशबैक %0.2f | ", float64(request.Data.RequestedCashback))
				ttotalProductPricing -= request.Data.RequestedCashback
			} else if request.Data.RequestedCashback > ttotalProductPricing {
				ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
					Key:        "कैशबैक",
					Value:      fmt.Sprintf("₹%0.2f", ttotalProductPricing),
					TotalValue: -1 * ttotalProductPricing,
					Styles:     &styles,
					Type:       offers.OFFER_TYPES.CASHBACK,
				})

				discountAmount += ttotalProductPricing
				discountReason = discountReason + fmt.Sprintf("कैशबैक %0.2f | ", ttotalProductPricing)
				ttotalProductPricing = 0
			}
		}
	}
	style := map[string]interface{}{
		"fontWeight": 700,
	}
	ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
		Key:        "टोटल",
		Value:      fmt.Sprintf("₹%.2f", ttotalProductPricing),
		Styles:     &style,
		TotalValue: ttotalProductPricing,
	})

	zproductPricing := []shared.ProductPrice{}
	ztotalProductPricing := []shared.TotalPricing{}
	zdiscountPricing := []shared.DiscountPricing{}

	totalProductPrice := 0.0
	totalDiscount := 0.0
	totalPayableAmount := 0.0
	totalCharges := 0.0
	totalCashbackApplied := 0.0
	for _, pric := range pproductsPricing {
		productDao := shared.ProductPrice{
			ID:           pric.ID,
			OfferedValue: pric.OfferedValue,
			Quantity:     pric.Quantity,
			TotalValue:   pric.TotalValue,
			ProductName:  pric.ProductName,
			PackSize:     pric.PackSize,
			Key:          pric.Key,
			Value:        pric.Value,
			Manufacturer: pric.Manufacturer,
		}
		if pric.Styles != nil {
			productDao.Styles = pric.Styles
		}
		zproductPricing = append(zproductPricing, productDao)
		tval, _ := strconv.ParseFloat(pric.TotalValue, 32)
		totalProductPrice += tval
	}

	for _, pric := range ddiscountPricing {
		discountDao := shared.DiscountPricing{
			Key:        pric.Key,
			Value:      pric.Value,
			TotalValue: pric.TotalValue,
			IsInternal: pric.IsInternal,
			Type:       pric.Type,
		}
		if pric.Styles != nil {
			discountDao.Styles = pric.Styles
		}
		zdiscountPricing = append(zdiscountPricing, discountDao)
		if pric.Type == offers.OFFER_TYPES.CASHBACK {
			cval := pric.TotalValue
			if cval < 0 {
				cval = cval * -1.0
			}
			totalCashbackApplied += cval
		}
		if pric.Type == offers.OFFER_TYPES.CHARGE {
			cval := pric.TotalValue
			if cval < 0 {
				cval = cval * -1.0
			}
			totalCharges += cval
		}
	}

	for _, pric := range ttotalPricingMap {
		totalPricingDao := shared.TotalPricing{
			Key:        pric.Key,
			Value:      pric.Value,
			TotalValue: pric.TotalValue,
		}
		if pric.Styles != nil {
			totalPricingDao.Styles = pric.Styles
		}
		ztotalProductPricing = append(ztotalProductPricing, totalPricingDao)
		if pric.Key != "टोटल" && pric.Type != offers.OFFER_TYPES.CHARGE {
			tval, err := strconv.ParseFloat(pric.Value, 32)
			if err != nil {
				tval = pric.TotalValue
			}
			if tval < 0 {
				tval = tval * -1.0
			}
			totalDiscount += tval
		}
	}

	if totalDiscount < 0 {
		totalDiscount = totalDiscount * -1.0
	}

	if totalCharges < 0 {
		totalCharges = totalCharges * -1.0
	}

	totalPayableAmount = totalProductPrice - totalDiscount - totalCashbackApplied + totalCharges

	if totalProductPrice < MIN_ORDER_VALUE {
		err = fmt.Errorf("order value less than minimum order value %s", "update_third_party_seller_order_1")
		return
	}

	eventObject := map[string]interface{}{
		"distinct_id":       request.UserID,
		"ordering_module":   utils.MakeTitleCase(seller),
		"seller":            seller,
		"source":            "D2R",
		"order_id":          orderID,
		"order_value":       int(totalPayableAmount),
		"product_name":      productNames,
		"product_id":        productIds,
		"total_discount":    totalDiscount,
		"promo_code":        request.Data.CouponID,
		"cart_value":        totalProductPrice,
		"cashback_applied":  totalCashbackApplied,
		"difference_amount": orderDetailsDB.GetOrderValue() - totalPayableAmount,
	}

	intPincode, err := strconv.Atoi(*shippingAddress.PostalCode)
	eventObject["pincode"] = intPincode
	if err != nil {
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Non Serviceable Location", request.UserID, eventObject),
		})
		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{request.UserID},
			EventName:   "Non Serviceable Location",
			EventObject: eventObject,
		})
		err = errors.New("not able to parse pincode")
		return
	}
	serviceablityResponse := &dto.AppserviceAbilityAPIResponse{}
	serviceablityResponse, err = s.CheckServiceAbility(ctx, &dto.AppServiceAblityAPIRequest{
		UserID:           request.UserID,
		DeliveryPostCode: *shippingAddress.PostalCode,
		Weight:           fmt.Sprintf("%d", utils.GetOrderMass(cartDetails)),
		COD:              "y",
		Data: dto.AppServiceAbilityAPIRequestData{
			Seller: seller,
		},
	})

	if err != nil {
		return
	}
	if !serviceablityResponse.Data.Servicable {
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Non Servicable Location", request.UserID, eventObject),
		})
		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{request.UserID},
			EventName:   "Non Servicable Location",
			EventObject: eventObject,
		})
		err = errors.New("not servicable")
		return
	}

	if userCashbackBalance != nil {
		if totalCashbackApplied > *userCashbackBalance {
			err = errors.New("क्षमा करें, आपके पास इस ऑर्डर के लिए पर्याप्त कैशबैक नहीं है।")
			return
		}
	}

	orderStatuses := orderS.MapOrderStatus(orderStatus, "", orderS.OrderStatusResponse{})
	order := dao.KiranaBazarOrder{
		OrderStatus:      &orderStatus,
		UpdatedAt:        time.Now(),
		Seller:           seller,
		DeliveryStatus:   orderStatuses.ShipmentStatus,
		DisplayStatus:    orderStatuses.DisplayStatus,
		ProcessingStatus: orderStatuses.ProcessingStatus,
	}
	_, _, err = s.repository.Update(&dao.KiranaBazarOrder{
		ID: orderInfoDB.ID,
	}, &order)
	if err != nil {
		return
	}

	payment := dao.KiranaBazarOrderPayment{
		PaymentMethod: &paymentMethod,
		Amount:        &totalPayableAmount,
		Status:        &paymentStatus,
		CreatedAt:     time.Now(),
	}
	_, _, err = s.repository.Update(&dao.KiranaBazarOrderPayment{
		OrderID: orderInfoDB.ID,
	}, &payment)
	if err != nil {
		return
	}

	if userCashbackBalance != nil && totalCashbackApplied > 0 {
		// redeem cashback for user

		sourceId, exists := brands.GetCashbackActionsBySource(seller)

		if !exists || exists && (sourceId.ADD_CASHBACK == 0 || sourceId.REDEEM_CASHBACK == 0) {
			slack.SendSlackMessage(fmt.Sprintf("invalid cashback source id for seller %s, sourceId: %v in UpdateThirdPartySellerOrder", seller, sourceId))
			err = errors.New("invalid cashback source id")
			return
		}

		_, err := s.RedeemUserCashback(ctx, dto.RedeemUserCashbackRequest{
			UserID: request.UserID,
			Meta:   request.Meta,
			Data: dto.RedeemUserCashbackData{
				OrderAmount:          totalPayableAmount,
				SourceId:             sourceId.REDEEM_CASHBACK,
				OrderId:              *orderInfoDB.ID,
				RedeemCashbackAmount: totalCashbackApplied,
			},
		})
		if err != nil {
			return response, err
		}
	}

	// get user order stats
	userOrderStatsResp, err := s.GetUserOrderStats(ctx, dto.GetUserOrderStatsRequest{
		UserID: request.UserID,
	})
	if err != nil {
		fmt.Println("error in getting user order stats", err)
	}

	userOrderCount, err := ordervalue.GetUserLevelOrderCount(request.UserID)
	if err != nil {
		fmt.Println("error in getting user order count", err)
	}

	userAcurateConfirmedOrderCount, err := ordervalue.GetUserLevelConfirmedOrderCount(request.UserID)
	if err != nil {
		fmt.Println("error in getting user order count", err)
	}

	userCohortNames := []string{}
	tagIds, tagNames, _ := ordertags.GetOrderTags(totalPayableAmount, userOrderStatsResp.Data, 0, userOrderCount, userAcurateConfirmedOrderCount, appVersion, seller, userCohortNames)

	// insert order tag in db, TODO: move this to a go routine
	// removing all the tags before adding new tags
	err = s.removeOrderTags(*orderInfoDB.ID)
	if err != nil {
		return response, err
	}
	if len(tagIds) > 0 {
		err := s.addOrderTags(*orderInfoDB.ID, tagIds)
		if err != nil {
			return response, err
		}
	}

	cartItems := cartDetails
	orderDetail := dao.KiranaBazarOrderDetails{
		Cart:            cartItems,
		ShippingAddress: shippingAddress,
		BillingAddress:  &billingAddress,
		TotalItems:      len(cartDetails),
		TotalAmount:     totalPayableAmount,
		BillBreakUp: dao.BillDetails{
			ProductsPricing: zproductPricing,
			TotalPricing:    ztotalProductPricing,
			DiscountPricing: zdiscountPricing,
			EnableOrdering:  true,
			Message:         "",
			Coupon:          couponID,
		},
		Seller: seller,
	}
	orderDetailsByte, err := json.Marshal(orderDetail)
	if err != nil {
		return
	}
	orderDetails := dao.KiranaBazarOrderDetail{
		OrderDetails: orderDetailsByte,
		UpdatedAt:    time.Now(),
	}
	_, _, err = s.repository.Update(dao.KiranaBazarOrderDetail{
		OrderID: orderInfoDB.ID,
	}, &orderDetails)
	if err != nil {
		return
	}

	source := "APP"
	if request.Email != "" {
		source = "D2R"
	}

	eventObject = map[string]interface{}{
		"distinct_id":         request.UserID,
		"order_value":         int(totalPayableAmount),
		"product_name":        productNames,
		"product_id":          productIds,
		"ordering_module":     utils.MakeTitleCase(seller),
		"seller":              seller,
		"order_id":            fmt.Sprintf("%d", orderInfoDB.ID),
		"total_discount":      totalDiscount,
		"promo_code":          request.Data.CouponID,
		"cart_value":          totalProductPrice,
		"source":              source,
		"tag_ids":             tagIds,
		"tags":                tagNames,
		"cashback_applied":    totalCashbackApplied,
		"request_app_version": appVersion,
	}

	if request.Email != "" {
		eventObject["email"] = request.Email
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Updated", request.UserID, eventObject,
			fmt.Sprintf("%s_%s", "order_updated", fmt.Sprintf("%d", *orderInfoDB.ID))),
	})

	activeCohortUserData := activationCohortUserData

	cartValue := orderDetail.GetCartValue()
	gtv := GetGTVValue(&orderDetail)
	go func() {
		_, err := s.AddDataForReconciliation(ctx, &dto.AddReconciliationRequest{
			OrderID: int64(*orderInfoDB.ID),
			Data: []dto.StatusTimeStamp{
				dto.StatusTimeStamp{
					OrderStatus: "order_updated",
					TimeStamp:   time.Now().UnixMilli(),
				},
				dto.StatusTimeStamp{
					OrderStatus: "expected_delivery_timestamp",
					TimeStamp:   time.Now().UnixMilli() + 604800000,
				},
			},
			Service:       "INTERNAL",
			OMS:           seller,
			DiscountValue: &totalDiscount,
			GrossValue:    &totalProductPrice,
			OrderValue:    &totalPayableAmount,
			CartValue:     &cartValue,
			GTV:           &gtv,
		})
		if err != nil {
			fmt.Println("err in ", err)
			slack.SendSlackMessage(fmt.Sprintf("error in add data for reeconciliation %v", err))
		}

		if activeCohortUserData != nil && activeCohortUserData.Eligible && seller == utils.ZOFF_FOODS {
			// set coupon as redeemed in redis cache
			setActivationCohortCouponRedeemed(ctx, request.UserID, utils.ZOFF_FOODS, activeCohortUserData.CouponId, s.GcpRedis)
		}

		sourceId, exists := brands.GetCashbackActionsBySource(seller)

		if !exists || exists && (sourceId.ADD_CASHBACK == 0 || sourceId.REDEEM_CASHBACK == 0) {
			slack.SendSlackMessage(fmt.Sprintf("invalid cashback source id for seller %s, sourceId: %v in UpdateThirdPartySellerOrder", seller, sourceId))
			err = errors.New("invalid cashback source id")
			return
		}

		loyaltyOrderAmount := totalPayableAmount
		if request.Data.Seller == utils.RSB_SUPER_STOCKIST {
			zoffOrderAmount := utils.GetManufacturerCartValue(zproductPricing, []string{utils.ZOFF_FOODS})
			excludingZoffOrderAmount := math.Max(0, totalPayableAmount-zoffOrderAmount)
			loyaltyOrderAmount = excludingZoffOrderAmount
		}
		// call here to add loyalty points for this order
		if request.Data.Seller != utils.ZOFF_FOODS {
			_, err = s.AddUserLoyaltyPoints(ctx, dto.AddUserLoyaltyPointsRequest{
				UserID: request.UserID,
				Meta:   request.Meta,
				Data: dto.AddUserLoyaltyPointsRequestData{
					OrderId:     *order.ID,
					OrderAmount: loyaltyOrderAmount,
					SourceId:    sourceId.ADD_CASHBACK,
				},
			})
			if err != nil {
				fmt.Println("error in adding user loyalty points", err)
			}
		}
	}()

	err = webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{request.UserID},
		EventName:   "Order Updated",
		EventObject: eventObject,
	})
	if err != nil {
		fmt.Println("failed to send webengage event")
	}

	currentDateTime := time.Now()
	hindiDateString := fmt.Sprintf("%s %d, %d", hindiMonthsMap[int(currentDateTime.Month())], currentDateTime.Day(), currentDateTime.Year())
	timeString := currentDateTime.Format("3:04 PM 02-01-2006")

	response = dto.AppKiranaBazarOrderResponse{
		Data: []dto.OrderDetails{
			{
				ID:                 fmt.Sprint(*orderInfoDB.ID),
				TotalItems:         len(cartDetails),
				OrderDate:          currentDateTime,
				OrderDateString:    hindiDateString,
				OrderTimeString:    timeString,
				TotalAmount:        totalPayableAmount,
				MessageID:          mid,
				OrderStatusDetails: nil,
				OrderStatus:        orderStatus,
				OrderPayment: dto.OrderPayment{
					PaymentMethod: paymentMethod,
					Amount:        totalPayableAmount,
					PaymentStatus: "PENDING",
				},
				OrderDetails: orderDetailsByte,
			},
		},
	}

	go s.CreateOrderInvoice(context.Background(), dto.CreateInvoiceRequest{
		OrderID: fmt.Sprint(*orderInfoDB.ID),
	})

	go func(orderID int64, s *Service) {
		_, err := s.CreatePickListForOrder(ctx, dto.CreatePickListForOrderRequest{
			Data: struct {
				OrderID string `json:"order_id"`
			}{OrderID: fmt.Sprint(orderID)},
		})
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error creating picklist for order: %d %v", orderID, err))
		}
	}(*orderInfoDB.ID, s)

	return
}

func (s *Service) UpdateOrderDetailsForPayments(ctx context.Context, orderId int64, paymentDiscount float64, prepaidAmount float64, orderAmount float64) error {
	orderDetailsData := dao.KiranaBazarOrderDetail{}
	whereCondition := dao.KiranaBazarOrderDetail{
		OrderID: &orderId,
	}

	logger.Warn(context.Background(), "Updating order details for payments", map[string]interface{}{
		"order_id":         orderId,
		"payment_discount": paymentDiscount,
		"prepaid_amount":   prepaidAmount,
		"order_amount":     orderAmount,
	})

	_, err := s.repository.Find(map[string]interface{}{
		"order_id": orderId,
	}, &orderDetailsData)

	orderDetails := dao.KiranaBazarOrderDetails{}
	err = json.Unmarshal(orderDetailsData.OrderDetails, &orderDetails)
	if err != nil {
		logger.Error(context.Background(), "error in unmarshalling order details", err)
		return err
	}
	updatedTotalAmount := utils.Round(orderDetails.TotalAmount)
	updatedTotalPricing := orderDetails.BillBreakUp.TotalPricing
	updatedDiscountPricing := orderDetails.BillBreakUp.DiscountPricing

	exists := false
	for _, discount := range updatedDiscountPricing {
		if discount.Name == "PrePaid Amount" {
			exists = true
			break
		}
	}

	if exists {
		logger.Error(context.Background(), "payment key already present", map[string]interface{}{
			"order_id":         orderId,
			"payment_discount": paymentDiscount,
			"prepaid_amount":   prepaidAmount,
			"order_amount":     orderAmount,
		})
		return nil
	}

	style := map[string]interface{}{
		"color":      "#009E7F",
		"fontWeight": 700,
	}

	if paymentDiscount > 0 {
		value := fmt.Sprintf("-₹%.2f", paymentDiscount)
		roundedDiscValue := math.Floor(paymentDiscount*100) / 100
		updatedTotalPricing = append(updatedTotalPricing, shared.TotalPricing{
			Key:        "पेमेंट डिस्काउंट",
			Value:      value,
			TotalValue: roundedDiscValue,
			Styles:     &style,
			Name:       "Payment Discount",
		})

		updatedDiscountPricing = append(updatedDiscountPricing, shared.DiscountPricing{
			Key:        "पेमेंट डिस्काउंट",
			Value:      value,
			TotalValue: roundedDiscValue,
			Styles:     &style,
			IsInternal: false,
			Name:       "Payment Discount",
		})
		updatedTotalAmount -= paymentDiscount
	}

	if prepaidAmount > 0 {
		value := fmt.Sprintf("-₹%.2f", prepaidAmount)
		roundedDiscValue := math.Floor(prepaidAmount*100) / 100
		updatedTotalPricing = append(updatedTotalPricing, shared.TotalPricing{
			Key:        "पेमेंट",
			Value:      value,
			TotalValue: roundedDiscValue,
			Styles:     &style,
			Name:       "PrePaid Amount",
			Type:       offers.OFFER_TYPES.PREPAID_AMOUNT,
		})

		updatedDiscountPricing = append(updatedDiscountPricing, shared.DiscountPricing{
			Key:        "पेमेंट",
			Value:      value,
			TotalValue: roundedDiscValue,
			Styles:     &style,
			IsInternal: false,
			Name:       "PrePaid Amount",
			Type:       offers.OFFER_TYPES.PREPAID_AMOUNT,
		})
		updatedTotalAmount -= prepaidAmount
	} else {
		return fmt.Errorf("prepaid amount is 0")
	}

	updatedTotalAmount = utils.Round(updatedTotalAmount)
	var totalObj shared.TotalPricing
	var totalObjIndex int
	found := false
	for i, obj := range updatedTotalPricing {
		if obj.Key == "टोटल" {
			totalObj = obj
			totalObjIndex = i
			found = true

			totalObj.TotalValue = updatedTotalAmount
			totalObj.Value = fmt.Sprintf("₹%.2f", updatedTotalAmount)
			break
		}
	}

	if found {
		updatedTotalPricing = append(updatedTotalPricing[:totalObjIndex], updatedTotalPricing[totalObjIndex+1:]...)
		updatedTotalPricing = append(updatedTotalPricing, totalObj)
	}

	updatedOrderDetail := dao.KiranaBazarOrderDetails{
		Cart:            orderDetails.Cart,
		ShippingAddress: orderDetails.ShippingAddress,
		BillingAddress:  orderDetails.BillingAddress,
		TotalItems:      len(orderDetails.Cart),
		TotalAmount:     updatedTotalAmount,
		BillBreakUp: dao.BillDetails{
			ProductsPricing: orderDetails.BillBreakUp.ProductsPricing,
			TotalPricing:    updatedTotalPricing,
			DiscountPricing: updatedDiscountPricing,
			EnableOrdering:  orderDetails.BillBreakUp.EnableOrdering,
			Message:         orderDetails.BillBreakUp.Message,
			Coupon:          orderDetails.BillBreakUp.Coupon,
		},
		Seller: orderDetails.Seller,
	}
	orderDetailsByte, err := json.Marshal(updatedOrderDetail)
	if err != nil {
		return err
	}
	updatedOrderDetails := dao.KiranaBazarOrderDetail{
		OrderDetails: orderDetailsByte,
		UpdatedAt:    time.Now(),
	}
	_, _, err = s.repository.Update(&whereCondition, &updatedOrderDetails)
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) CreateThirdPartySellerOrder(ctx context.Context, request dto.BillDifferenceRequest) (response dto.AppKiranaBazarOrderResponse, err error) {

	seller := request.Data.Seller
	if seller == "" {
		err = errors.New("no seller in the request")
		return
	}

	appVersion := request.Meta.AppVersion

	// min order value for the seller
	var MIN_ORDER_VALUE float64 = userdetails.GetUserSellerMinOrderValue(request.UserID, seller, true, "create_third_party_seller_order")
	if MIN_ORDER_VALUE < 0 {
		err = errors.New("min order value issue")
		return
	}
	tid := getTransactionID()
	request.Meta.Context.TransactionID = &tid
	couponID := request.Data.CouponID
	couponIDInt, err := strconv.Atoi(couponID)
	coupon := dto.Coupon{}
	if err == nil {
		couponResponse, errr := s.GetCoupons(ctx, dto.GetCouponsRequest{
			UserID: request.UserID,
			Data: dto.GetCouponData{
				CouponID: couponIDInt,
				Seller:   request.Data.Seller,
			},
		})
		if errr != nil {
			return
		}
		coupon = couponResponse.Data.Coupons[0]
	}

	mid := getMessageID()
	if request.Meta.Context.MessageID != nil {
		mid = *request.Meta.Context.MessageID
	}

	shippingAddress, billingAddress, err := getUserAddress(request.UserID, false, fmt.Sprintf("%s", request.Data.AddressID), s.repository)
	if err != nil {
		return
	}

	userDetailsChannel := userdetails.AsyncFetchUserDetails(request.UserID, []string{userdetails.USER_DETAILS_TYPES.USER_DYNAMIC_DETAILS, userdetails.USER_DETAILS_TYPES.USER_GEOGRAPHY}, 1*time.Second)

	activationCohortEligibilityChannel := make(chan *dto.ActivationCohortUserData, 1)

	go getActivationCohortUserData(ctx, request.UserID, utils.ZOFF_FOODS, s.GcpRedis, activationCohortEligibilityChannel)

	userCashbackBalanceChannel := make(chan *float64, 1)
	go s.getUserCashbackBalanceFromService(ctx, request.UserID, request.Data.Seller, request.Meta, userCashbackBalanceChannel)

	orderStatus := processingstatus.PLACED
	paymentStatus := paymentstatus.UNPAID
	paymentMethod := constants.COD
	cartDetails := request.Data.Cart

	productNames := []string{}
	productIds := []string{}
	for _, product := range cartDetails {
		meta := shared.KiranaBazarProductMeta{}
		err = json.Unmarshal(product.Meta, &meta)
		if err != nil {
			meta.HindiName = product.Name
		}
		productNames = append(productNames, string(meta.HindiName))
		productIds = append(productIds, product.ID)
	}

	inStockProductIds, err := s.CheckProductsOutOfStock(productIds)
	if err != nil {
		return
	}

	if (len(inStockProductIds) != len(productIds) || len(inStockProductIds) == 0) && seller != utils.KIRANACLUB_LOYALTY_REWARDS {
		err = errors.New(exceptions.OosErrorMessage)
		return
	}

	pproductsPricing := []shared.ProductPrice{}
	ddiscountPricing := []shared.DiscountPricing{}

	ttotalProductPricing := 0.0
	ttotalRewardCoins := 0

	var packageDetails *shared.PackageDetails = nil
	dimensions := make([]shared.Dimensions, 0)

	for _, item := range cartDetails {
		if err != nil {
			fmt.Println("err ", err)
		}
		if !includes(inStockProductIds, item.ID) && seller != utils.KIRANACLUB_LOYALTY_REWARDS {
			continue
		}
		meta := shared.KiranaBazarProductMeta{}
		err = json.Unmarshal(item.Meta, &meta)
		if err != nil {
			return
		}
		pproductsPricing = append(pproductsPricing, shared.ProductPrice{
			ID:           item.ID,
			OfferedValue: fmt.Sprintf("%.2f", meta.WholesaleRate),
			Quantity:     int(item.Quantity),
			TotalValue:   fmt.Sprintf("%.2f", meta.WholesaleRate*float64(item.Quantity)*float64(meta.PackSize)),
			ProductName:  meta.HindiName,
			PackSize:     meta.PackSize,
			Size:         meta.Quantity,
			Key:          fmt.Sprintf("%s %s (%d पैकेट्स) x %d", meta.HindiName, meta.Quantity, meta.PackSize, item.Quantity),
			Value:        fmt.Sprintf("₹%.2f", meta.WholesaleRate*float64(item.Quantity)*float64(meta.PackSize)),
			Manufacturer: item.Manufacturer,
		})
		ttotalProductPricing += meta.WholesaleRate * float64(item.Quantity) * float64(meta.PackSize)
		if meta.RewardCoins != nil {
			ttotalRewardCoins += *meta.RewardCoins
		}

		if seller == utils.KIRANACLUB_LOYALTY_REWARDS {
			dim := shared.Dimensions{}
			if meta.Length != nil {
				dim.Length = *meta.Length
			}
			if meta.Breadth != nil {
				dim.Breadth = *meta.Breadth
			}
			if meta.Height != nil {
				dim.Height = *meta.Height
			}
			if meta.Weight != nil {
				dim.Weight = *meta.Weight
			}
			dimensions = append(dimensions, dim)
		}
	}

	if seller == utils.KIRANACLUB_LOYALTY_REWARDS && len(dimensions) > 0 {
		packageDetails = &shared.PackageDetails{
			Dimensions: dimensions,
		}
	}

	ttotalPricingWithoutDiscount := ttotalProductPricing

	ttotalPricingMap := []shared.TotalPricing{}

	discountAmount := 0.0
	discountReason := ""

	activationCohortUserData := <-activationCohortEligibilityChannel
	if request.Data.Seller == utils.KIRANA_CLUB {
		styles := map[string]interface{}{
			"color":      "#009E7F",
			"fontWeight": 700,
		}

		ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      fmt.Sprintf("₹%d", KC_OFFER_DELIVER_CHARGE),
			TotalValue: KC_OFFER_DELIVER_CHARGE,
			Styles:     &styles,
			Name:       "Service Charge",
			Type:       offers.OFFER_TYPES.CHARGE,
		})

		ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      fmt.Sprintf("₹%d", KC_OFFER_DELIVER_CHARGE),
			TotalValue: KC_OFFER_DELIVER_CHARGE,
			Styles:     &styles,
			Name:       "Service Charge",
			Type:       offers.OFFER_TYPES.CHARGE,
		})
		ttotalProductPricing += KC_OFFER_DELIVER_CHARGE
		ttotalPricingWithoutDiscount += KC_OFFER_DELIVER_CHARGE
	}

	tpm, disc, err := s.GetBackendDiscount(ttotalPricingWithoutDiscount, ttotalProductPricing, request.Data.Seller, request.UserID, activationCohortUserData)
	if err == nil {
		for _, tpmDiscount := range tpm {
			discValue := tpmDiscount.TotalValue
			value := tpmDiscount.Value
			if discValue > 0 {
				discValue = discValue * -1
			}
			ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
				Key:        tpmDiscount.Key,
				Value:      value,
				TotalValue: math.Floor(discValue*100) / 100, // TotalValue is negative here
				Styles:     tpmDiscount.Styles,
			})
			ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
				Key:        tpmDiscount.Key,
				Value:      value,
				TotalValue: math.Floor(discValue*100) / 100, // TotalValue is negative here
				Styles:     tpmDiscount.Styles,
				IsInternal: tpmDiscount.IsInternal,
			})

			discountReason = discountReason + fmt.Sprintf("%s %s | ", tpmDiscount.Key, tpmDiscount.Value)
			discountAmount += (-1 * discValue)
		}
		ttotalProductPricing -= disc
	}

	if coupon.Valid && coupon.MinimumAmount <= ttotalProductPricing {
		discountInt, err := strconv.Atoi(coupon.Discount)
		if err != nil {
			fmt.Println("err ", err)
		}
		discount := float64(discountInt)
		if coupon.PercentageDiscount > 0.0 {
			discount = ttotalProductPricing * coupon.PercentageDiscount * 0.01
		}

		styles := map[string]interface{}{
			"color":      "#009E7F",
			"fontWeight": 700,
		}

		ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
			Key:        coupon.Description,
			Value:      "-₹" + fmt.Sprintf("%0.2f", discount),
			TotalValue: -1 * discount,
			Styles:     &styles,
		})

		ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
			Key:        coupon.Description,
			Value:      "-₹" + fmt.Sprintf("%0.2f", discount),
			TotalValue: -1 * discount,
			Styles:     &styles,
			IsInternal: coupon.IsInternal,
		})

		ttotalProductPricing -= float64(discount)
		discountReason = discountReason + fmt.Sprintf("%s %0.2f | ", coupon.Code, discount)
		discountAmount += discount
	}
	fmt.Println("here 11")

	if ttotalProductPricing >= MIN_ORDER_VALUE && request.Data.Seller != utils.KIRANA_CLUB {
		styles := map[string]interface{}{
			"color":      "#009E7F",
			"fontWeight": 700,
		}

		freeDeliveryString := fmt.Sprintf("₹̶%s फ्री डिलीवरी", utils.GenerateStrikethrough(fmt.Sprintf("%d", 100)))
		ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      freeDeliveryString,
			TotalValue: 0,
			Styles:     &styles,
		})

		ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
			Key:        "डिलीवरी चार्ज",
			Value:      freeDeliveryString,
			TotalValue: 0,
			Styles:     &styles,
			IsInternal: true,
		})
	}

	ttotalRewardCoins = 100
	rewardCoins := dto.RewardCoinsData{}
	err = json.Unmarshal([]byte(utils.RewardsCoinsData), &rewardCoins)
	if err != nil {
		return
	}
	rewardCoins.Amount = ttotalRewardCoins
	rewardCoins.Text = fmt.Sprintf("%d किराना कॉइन्स", ttotalRewardCoins)

	waysToPayData := make([]dto.WaysToPayDataV1, 0)
	err = json.Unmarshal([]byte(utils.WaysToPayData), &waysToPayData)
	if err != nil {
		return
	}

	// var userCashbackOfferComponent *dto.CashbackComponent
	userCashbackBalance := <-userCashbackBalanceChannel
	if userCashbackBalance != nil && *userCashbackBalance > 0 {
		// userCashbackOfferComponent = generateLoyaltyCashbackComponent(*userCashbackBalance)
		if request.Data.RequestedCashback > 0 {
			styles := map[string]interface{}{
				"color":      "#009E7F",
				"fontWeight": 700,
			}
			if request.Data.RequestedCashback > *userCashbackBalance {
				request.Data.RequestedCashback = *userCashbackBalance
			}

			if request.Data.RequestedCashback <= ttotalProductPricing {
				ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
					Key:        "कैशबैक",
					Value:      fmt.Sprintf("₹%0.2f", request.Data.RequestedCashback),
					TotalValue: -1 * float64(request.Data.RequestedCashback),
					Styles:     &styles,
					Type:       offers.OFFER_TYPES.CASHBACK,
				})

				discountAmount += float64(request.Data.RequestedCashback)
				discountReason = discountReason + fmt.Sprintf("कैशबैक %0.2f | ", float64(request.Data.RequestedCashback))
				ttotalProductPricing -= request.Data.RequestedCashback
			} else if request.Data.RequestedCashback > ttotalProductPricing {
				ddiscountPricing = append(ddiscountPricing, shared.DiscountPricing{
					Key:        "कैशबैक",
					Value:      fmt.Sprintf("₹%0.2f", ttotalProductPricing),
					TotalValue: -1 * ttotalProductPricing,
					Styles:     &styles,
					Type:       offers.OFFER_TYPES.CASHBACK,
				})

				discountAmount += ttotalProductPricing
				discountReason = discountReason + fmt.Sprintf("कैशबैक %0.2f | ", ttotalProductPricing)
				ttotalProductPricing = 0
			}
		}
	}
	style := map[string]interface{}{
		"fontWeight": 700,
	}
	ttotalPricingMap = append(ttotalPricingMap, shared.TotalPricing{
		Key:        "टोटल",
		Value:      fmt.Sprintf("₹%.2f", ttotalProductPricing),
		Styles:     &style,
		TotalValue: ttotalProductPricing,
	})

	zproductPricing := []shared.ProductPrice{}
	ztotalProductPricing := []shared.TotalPricing{}
	zdiscountPricing := []shared.DiscountPricing{}

	totalProductPrice := 0.0
	totalDiscount := 0.0
	totalPayableAmount := 0.0
	totalCashbackApplied := 0.0
	totalCharges := 0.0
	for _, pric := range pproductsPricing {
		productDao := shared.ProductPrice{
			ID:           pric.ID,
			OfferedValue: pric.OfferedValue,
			Quantity:     pric.Quantity,
			TotalValue:   pric.TotalValue,
			ProductName:  pric.ProductName,
			PackSize:     pric.PackSize,
			Key:          pric.Key,
			Value:        pric.Value,
			Manufacturer: pric.Manufacturer,
		}
		if pric.Styles != nil {
			productDao.Styles = pric.Styles
		}
		zproductPricing = append(zproductPricing, productDao)
		tval, _ := strconv.ParseFloat(pric.TotalValue, 32)
		totalProductPrice += tval
	}

	for _, pric := range ddiscountPricing {
		discountDao := shared.DiscountPricing{
			Key:        pric.Key,
			Value:      pric.Value,
			TotalValue: pric.TotalValue,
			IsInternal: pric.IsInternal,
			Type:       pric.Type,
		}
		if pric.Styles != nil {
			discountDao.Styles = pric.Styles
		}
		zdiscountPricing = append(zdiscountPricing, discountDao)
		if pric.Type == offers.OFFER_TYPES.CASHBACK {
			cval := pric.TotalValue
			if cval < 0 {
				cval = cval * -1.0
			}
			totalCashbackApplied += cval
		}
		if pric.Type == offers.OFFER_TYPES.CHARGE {
			cval := pric.TotalValue
			if cval < 0 {
				cval = cval * -1.0
			}
			totalCharges += cval
		}
	}
	fmt.Println("here8")

	for _, pric := range ttotalPricingMap {
		totalPricingDao := shared.TotalPricing{
			Key:        pric.Key,
			Value:      pric.Value,
			TotalValue: pric.TotalValue,
		}
		if pric.Styles != nil {
			totalPricingDao.Styles = pric.Styles
		}
		ztotalProductPricing = append(ztotalProductPricing, totalPricingDao)
		if pric.Key != "टोटल" && pric.Type != offers.OFFER_TYPES.CHARGE {
			tval, err := strconv.ParseFloat(pric.Value, 32)
			if err != nil {
				tval = pric.TotalValue
			}
			if tval < 0 {
				tval = tval * -1.0
			}
			totalDiscount += tval
		}
	}

	if totalDiscount < 0 {
		totalDiscount = totalDiscount * -1.0
	}

	totalPayableAmount = totalProductPrice - totalDiscount - totalCashbackApplied + totalCharges

	if totalProductPrice < userdetails.GetUserSellerMinOrderValue(request.UserID, seller, true, "update_third_party_seller_order_2") {
		err = fmt.Errorf("order value less than minimum order value %s", "create_third_party_seller_order")
		return
	}

	eventObject := map[string]interface{}{
		"distinct_id":      request.UserID,
		"ordering_module":  utils.MakeTitleCase(seller),
		"seller":           seller,
		"source":           "D2R",
		"latitude":         shippingAddress.Latitude,
		"longitude":        shippingAddress.Longitude,
		"order_value":      int(totalPayableAmount),
		"product_name":     productNames,
		"product_id":       productIds,
		"total_discount":   totalDiscount,
		"promo_code":       request.Data.CouponID,
		"cart_value":       totalProductPrice,
		"cashback_applied": totalCashbackApplied,
	}

	intPincode, err := strconv.Atoi(*shippingAddress.PostalCode)
	eventObject["pincode"] = intPincode
	if err != nil {
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Non Serviceable Location", request.UserID, eventObject),
		})
		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{request.UserID},
			EventName:   "Non Serviceable Location",
			EventObject: eventObject,
		})
		err = errors.New("not able to parse pincode")
		return
	}
	serviceablityResponse := &dto.AppserviceAbilityAPIResponse{}
	serviceablityResponse, err = s.CheckServiceAbility(ctx, &dto.AppServiceAblityAPIRequest{
		UserID:           request.UserID,
		DeliveryPostCode: *shippingAddress.PostalCode,
		Weight:           fmt.Sprintf("%d", utils.GetOrderMass(cartDetails)),
		COD:              "y",
		Data: dto.AppServiceAbilityAPIRequestData{
			Seller: seller,
		},
	})

	if err != nil {
		return
	}
	if !serviceablityResponse.Data.Servicable {
		s.Mixpanel.Track(ctx, []*mixpanel.Event{
			s.Mixpanel.NewEvent("Non Servicable Location", request.UserID, eventObject),
		})
		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{request.UserID},
			EventName:   "Non Servicable Location",
			EventObject: eventObject,
		})
		err = errors.New("not servicable")
		return
	}

	if userCashbackBalance != nil {
		if totalCashbackApplied > *userCashbackBalance {
			err = errors.New("क्षमा करें, आपके पास इस ऑर्डर के लिए पर्याप्त कैशबैक नहीं है।")
			return
		}
	}

	orderStatuses := orderS.MapOrderStatus(orderStatus, "", orderS.OrderStatusResponse{})
	order := dao.KiranaBazarOrder{
		TransactionID:    request.Meta.Context.TransactionID,
		MessageID:        &mid,
		OrderStatus:      &orderStatus,
		UserID:           &request.UserID,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
		Seller:           seller,
		DeliveryStatus:   orderStatuses.ShipmentStatus,
		DisplayStatus:    orderStatuses.DisplayStatus,
		ProcessingStatus: orderStatuses.ProcessingStatus,
	}
	_, err = s.repository.Create(&order)
	if err != nil {
		return
	}

	paymentID := uuid.New().String()
	payment := dao.KiranaBazarOrderPayment{
		ID:            &paymentID,
		OrderID:       order.ID,
		PaymentMethod: &paymentMethod,
		TransactionID: request.Meta.Context.TransactionID,
		Amount:        &totalPayableAmount,
		Status:        &paymentStatus,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
	_, err = s.repository.Create(&payment)
	if err != nil {
		return
	}

	if userCashbackBalance != nil && totalCashbackApplied > 0 {
		// redeem cashback for user
		sourceId, exists := brands.GetCashbackActionsBySource(seller)

		if !exists || exists && (sourceId.ADD_CASHBACK == 0 || sourceId.REDEEM_CASHBACK == 0) {
			slack.SendSlackMessage(fmt.Sprintf("invalid cashback source id for seller %s, sourceId: %v in CreateThirdPartySellerOrder", seller, sourceId))
			err = errors.New("invalid cashback source id")
			return
		}
		_, err := s.RedeemUserCashback(ctx, dto.RedeemUserCashbackRequest{
			UserID: request.UserID,
			Meta:   request.Meta,
			Data: dto.RedeemUserCashbackData{
				OrderAmount:          totalPayableAmount,
				SourceId:             sourceId.REDEEM_CASHBACK,
				OrderId:              *order.ID,
				RedeemCashbackAmount: totalCashbackApplied,
			},
		})
		if err != nil {
			return response, err
		}
	}

	// get user order stats
	userOrderStatsResp, err := s.GetUserOrderStats(ctx, dto.GetUserOrderStatsRequest{
		UserID: request.UserID,
	})
	if err != nil {
		fmt.Println("error in getting user order stats", err)
	}

	isSpamUser := ordertags.IsUserSpam(userOrderStatsResp.Data)

	userOrderCount, err := ordervalue.GetUserLevelOrderCount(request.UserID)
	if err != nil {
		fmt.Println("error in getting user order count", err)
	}

	userAcurateConfirmedOrderCount, err := ordervalue.GetUserLevelConfirmedOrderCount(request.UserID)
	if err != nil {
		fmt.Println("error in getting user order count", err)
	}

	userDetails := <-userDetailsChannel
	userCohortNames := s.getUserCohortNames(userDetails, request.UserID, seller)
	tagIds, tagNames, _ := ordertags.GetOrderTags(totalPayableAmount, userOrderStatsResp.Data, 0, userOrderCount, userAcurateConfirmedOrderCount, appVersion, seller, userCohortNames)

	// insert order tag in db, TODO: move this to a go routine
	if len(tagIds) > 0 {
		err := s.addOrderTags(*order.ID, tagIds)
		if err != nil {
			return response, err
		}
	}

	cartItems := cartDetails
	orderDetail := dao.KiranaBazarOrderDetails{
		Cart:            cartItems,
		ShippingAddress: shippingAddress,
		BillingAddress:  &billingAddress,
		TotalItems:      len(cartDetails),
		TotalAmount:     totalPayableAmount,
		BillBreakUp: dao.BillDetails{
			ProductsPricing: zproductPricing,
			TotalPricing:    ztotalProductPricing,
			DiscountPricing: zdiscountPricing,
			EnableOrdering:  true,
			Message:         "",
			Coupon:          couponID,
		},
		Seller:         seller,
		PackageDetails: packageDetails,
	}
	orderDetailsByte, err := json.Marshal(orderDetail)
	if err != nil {
		return
	}
	orderDetails := dao.KiranaBazarOrderDetail{
		OrderID:      order.ID,
		OrderDetails: orderDetailsByte,
		UpdatedAt:    time.Now(),
	}
	_, err = s.repository.Create(&orderDetails)
	if err != nil {
		return
	}

	source := "APP"
	if request.Email != "" {
		source = "D2R"
	}

	eventObject = map[string]interface{}{
		"distinct_id":         request.UserID,
		"order_value":         int(totalPayableAmount),
		"product_name":        productNames,
		"product_id":          productIds,
		"ordering_module":     utils.MakeTitleCase(seller),
		"seller":              seller,
		"order_id":            fmt.Sprint(*order.ID),
		"total_discount":      totalDiscount,
		"promo_code":          request.Data.CouponID,
		"cart_value":          totalProductPrice,
		"source":              source,
		"tag_ids":             tagIds,
		"tags":                tagNames,
		"cashback_applied":    totalCashbackApplied,
		"is_spam_user":        isSpamUser,
		"request_app_version": appVersion,
	}

	if request.Email != "" {
		eventObject["email"] = request.Email
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Placed", request.UserID, eventObject,
			fmt.Sprintf("%s_%s", "order_placed", fmt.Sprint(*order.ID))),
	})

	activeCohortUserData := activationCohortUserData

	cartValue := orderDetail.GetCartValue()
	gtv := GetGTVValue(&orderDetail)
	go func() {
		_, err := s.AddDataForReconciliation(ctx, &dto.AddReconciliationRequest{
			OrderID: int64(*order.ID),
			Data: []dto.StatusTimeStamp{
				dto.StatusTimeStamp{
					OrderStatus: "order_placed",
					TimeStamp:   time.Now().UnixMilli(),
				},
				dto.StatusTimeStamp{
					OrderStatus: "expected_delivery_timestamp",
					TimeStamp:   time.Now().UnixMilli() + 604800000,
				},
			},
			Service:       "INTERNAL",
			OMS:           seller,
			DiscountValue: &totalDiscount,
			GrossValue:    &totalProductPrice,
			OrderValue:    &totalPayableAmount,
			CartValue:     &cartValue,
			GTV:           &gtv,
		})
		if err != nil {
			fmt.Println("err in ", err)
			slack.SendSlackMessage(fmt.Sprintf("error in add data for reeconciliation %v", err))
		}

		if activeCohortUserData != nil && activeCohortUserData.Eligible && seller == utils.ZOFF_FOODS {
			// set coupon as redeemed in redis cache
			setActivationCohortCouponRedeemed(ctx, request.UserID, utils.ZOFF_FOODS, activeCohortUserData.CouponId, s.GcpRedis)
		}

		sourceId, exists := brands.GetCashbackActionsBySource(seller)

		if !exists || exists && (sourceId.ADD_CASHBACK == 0 || sourceId.REDEEM_CASHBACK == 0) {
			slack.SendSlackMessage(fmt.Sprintf("invalid cashback source id for seller %s, sourceId: %v in CreateThirdPartySellerOrder", seller, sourceId))
			err = errors.New("invalid cashback source id")
			return
		}

		loyaltyOrderAmount := totalPayableAmount
		if request.Data.Seller == utils.RSB_SUPER_STOCKIST {
			zoffOrderAmount := utils.GetManufacturerCartValue(zproductPricing, []string{utils.ZOFF_FOODS})
			excludingZoffOrderAmount := math.Max(0, totalPayableAmount-zoffOrderAmount)
			loyaltyOrderAmount = excludingZoffOrderAmount
		}
		// call here to add loyalty points for this order
		if request.Data.Seller != utils.ZOFF_FOODS {
			_, err = s.AddUserLoyaltyPoints(ctx, dto.AddUserLoyaltyPointsRequest{
				UserID: request.UserID,
				Meta:   request.Meta,
				Data: dto.AddUserLoyaltyPointsRequestData{
					OrderId:     *order.ID,
					OrderAmount: loyaltyOrderAmount,
					SourceId:    sourceId.ADD_CASHBACK,
				},
			})
			if err != nil {
				fmt.Println("error in adding user loyalty points", err)
			}
		}
	}()

	go func(orderID int64, s *Service) {
		_, err := s.CreatePickListForOrder(ctx, dto.CreatePickListForOrderRequest{
			Data: struct {
				OrderID string `json:"order_id"`
			}{OrderID: fmt.Sprint(orderID)},
		})
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error creating picklist for order: %d %v", orderID, err))
		}
	}(*order.ID, s)

	err = webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{request.UserID},
		EventName:   "Order Placed",
		EventObject: eventObject,
	})
	if err != nil {
		fmt.Println("failed to send webengage event")
	}

	currentDateTime := time.Now()
	hindiDateString := fmt.Sprintf("%s %d, %d", hindiMonthsMap[int(currentDateTime.Month())], currentDateTime.Day(), currentDateTime.Year())
	timeString := currentDateTime.Format("3:04 PM 02-01-2006")

	// add Data to IVR queue
	// if order value is greater than 4k, MAX_ORDER_FOR_AUTOMATED_IVR not inserting
	// Adding IVR call 3 times in a queue (in difference of 8 hours)
	if totalPayableAmount < utils.MAX_ORDER_FOR_AUTOMATED_IVR && !isSpamUser && !utils.NON_IVR_USERS[request.UserID] && userAcurateConfirmedOrderCount > 0 {
		appID := 0
		if data, ok := ivr.SellerIVRData[order.Seller][ivr.ORDER_CONFIRMATION_FLOW]; ok {
			if id, ok := data.(int); ok {
				appID = id
			}
		}
		if appID != 0 {
			// After one hour
			// add here for IVR 1 status update
			ivrStatus := ivrStatus.IVR_STATUS_MAP["ivr_1"]
			ivrStatusRequest := &dto.IVRStatusUpdateRequest{
				OrderId:    *order.ID,
				Status:     ivrStatus,
				IvrStatus:  ivrStatus,
				CallCount:  1,
				RetryCount: 0,
			}

			_, err := s.UpdateIVRCallStatus(ctx, ivrStatusRequest)
			if err != nil {
				fmt.Println("failed updating ivr 1 status", err)
			}

			triggerAt, _ := s.addDataToIVRQueue(*order.ID, order.Seller, totalPayableAmount, totalProductPrice, 1, request.UserID, *shippingAddress.Phone, appID, time.Now().Add(time.Hour*1), ivr.IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW)
			// 2nd call after 8 hour
			rand.Seed(time.Now().UnixNano())
			randomNumber := rand.Intn(61)
			triggerAt, _ = s.addDataToIVRQueue(*order.ID, order.Seller, totalPayableAmount, totalProductPrice, 2, request.UserID, *shippingAddress.Phone, appID, triggerAt.Add(time.Hour*3+time.Minute*30+time.Minute*time.Duration(randomNumber)), ivr.IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW)
			// 3rd call after 8 hours of second call i.e 16 hrs after 2nd call
			triggerAt, _ = s.addDataToIVRQueue(*order.ID, order.Seller, totalPayableAmount, totalProductPrice, 3, request.UserID, *shippingAddress.Phone, appID, triggerAt.Add(time.Hour*3+time.Minute*30+time.Minute*time.Duration(randomNumber)), ivr.IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW)
			// 4th call to check if user has still not receiving the call so that we can cancel the order
			triggerAt, _ = s.addDataToIVRQueue(*order.ID, order.Seller, totalPayableAmount, totalProductPrice, 4, request.UserID, *shippingAddress.Phone, appID, triggerAt.Add(time.Hour*3+time.Minute*30+time.Minute*time.Duration(randomNumber)), ivr.IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW)
		}
	}
	if err != nil {
	}

	successBanner := make(map[string]interface{})
	// if !LOYALTY_DATA_7_MAY[request.UserID] {
	// 	successBanner = utils.OrderSuccessBannerWidget
	// }

	response = dto.AppKiranaBazarOrderResponse{
		Data: []dto.OrderDetails{
			{
				ID:                 fmt.Sprint(*order.ID),
				TotalItems:         len(cartDetails),
				OrderDate:          currentDateTime,
				OrderDateString:    hindiDateString,
				OrderTimeString:    timeString,
				TotalAmount:        totalPayableAmount,
				TransactionID:      *request.Meta.Context.TransactionID,
				MessageID:          mid,
				OrderStatusDetails: nil,
				OrderStatus:        orderStatus,
				Banner:             successBanner,
				OrderPayment: dto.OrderPayment{
					PaymentID:            fmt.Sprint(*payment.ID),
					PaymentMethod:        paymentMethod,
					PaymentTransactionID: *request.Meta.Context.TransactionID,
					Amount:               totalPayableAmount,
					PaymentStatus:        "PENDING",
				},
				OrderDetails: orderDetailsByte,
			},
		},
	}

	go s.CreateOrderInvoice(context.Background(), dto.CreateInvoiceRequest{
		OrderID: fmt.Sprint(*order.ID),
	})

	return
}

func getPromiseDeliveryDate(promisedDeliveryDate time.Time, dispatchAt *int64) (string, string) {

	if !promisedDeliveryDate.IsZero() {
		pdd := formatDateInHindi(promisedDeliveryDate)
		displayedDate := promisedDeliveryDate.AddDate(0, 0, 2)
		displayDateInHindi := formatDateInHindi(displayedDate)
		return pdd, displayDateInHindi
	}

	if dispatchAt != nil {

		dispatchedTimeMillis := *dispatchAt
		currentTime := time.Now()

		// Convert dispatch time from milliseconds to time.Time
		dispatchedTime := time.UnixMilli(dispatchedTimeMillis)

		standardDeliveryDate := dispatchedTime.AddDate(0, 0, 7)

		var baseTimeForDisplay time.Time

		// If standard delivery date is already past, use it
		// Otherwise use current time + 2 days as fallback
		if standardDeliveryDate.After(currentTime) {
			baseTimeForDisplay = standardDeliveryDate
		} else {
			baseTimeForDisplay = currentTime.AddDate(0, 0, 2)
		}

		pdd := formatDateInHindi(baseTimeForDisplay)
		displayedPromisedDate := baseTimeForDisplay.AddDate(0, 0, 2)
		displayDateInHindi := formatDateInHindi(displayedPromisedDate)

		return pdd, displayDateInHindi

	}
	return "", ""
}

func (s *Service) UpdateProgressWidget(ctx context.Context, req *dto.UpdateProgressWidgetRequest) error {
	if req == nil {
		return errors.New("request cannot be nil")
	}

	orderId, err := strconv.Atoi(req.Data.OrderID)
	orderInfo, err := GetOrderInfo(s.repository, int64(orderId))
	if err != nil {
		return fmt.Errorf("failed to get order info for order %s: %w", req.Data.OrderID, err)
	}

	seller := req.Data.Seller
	if includes([]string{utils.ZOFF_FOODS, utils.RSB_SUPER_STOCKIST}, seller) {
		// call for thailand scheme as well
		amount := 0.0
		if seller == utils.ZOFF_FOODS {
			amount = req.Data.Amount
		} else if seller == utils.RSB_SUPER_STOCKIST {
			var orderDetails *dao.KiranaBazarOrderDetails
			var err error
			if req.Data.OrderDetails != nil {
				orderDetails = req.Data.OrderDetails
			} else {
				orderDetails, err = GetOrderDetails(s.repository, req.Data.OrderID)
				if err != nil {
					return fmt.Errorf("failed to get order details for order %s: %w", req.Data.OrderID, err)
				}
			}
			amount = orderDetails.GetManufacturerCartValue([]string{utils.ZOFF_FOODS})
		}

		if orderInfo.CreatedAt.Before(utils.THAILAND_SCHEME_CAMPAIGN_EXPIRY_DATE) || orderInfo.CreatedAt.Equal(utils.THAILAND_SCHEME_CAMPAIGN_EXPIRY_DATE) {
			reqObject := map[string]interface{}{
				"order_id": req.Data.OrderID,
				"amount":   amount,
				"user_id":  req.UserID,
				"status":   req.Data.Status,
				"scheme":   "thiland_scheme",
			}
			utils.CallExternalAPIAsync(utils.PROGRESS_WIDGET_RESOLVER_API, "POST", reqObject, nil)
		}
	}

	if orderInfo.CreatedAt.Before(utils.TARGET_SCHEME_CAMPAIGN_EXPIRY_DATE) || orderInfo.CreatedAt.Equal(utils.TARGET_SCHEME_CAMPAIGN_EXPIRY_DATE) {
		reqObject := map[string]interface{}{
			"user_id":  req.UserID,
			"order_id": req.Data.OrderID,
			"amount":   req.Data.Amount,
			"status":   req.Data.Status,
		}
		utils.CallExternalAPIAsync(utils.PROGRESS_WIDGET_RESOLVER_API, "POST", reqObject, nil)
	}

	return nil
}

// GetGTVValue returns the total GTV value for the order
// TODO: @sanket migrate this to orderDetails
func GetGTVValue(orderDetail *dao.KiranaBazarOrderDetails) float64 {
	totalGTV := 0.0
	for _, cartItem := range orderDetail.Cart {
		meta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(cartItem.Meta, &meta)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error unmarshalling product meta in GetGtvValue: %v", err))
			continue
		}
		brandWholesaleRate := meta.WholesaleRate
		if meta.BrandWholesaleRate != nil {
			brandWholesaleRate = *meta.BrandWholesaleRate
		}
		productID := cartItem.ID
		product, ok := products.GetProductByID(productID)
		if !ok {
			continue
		}
		gtvCalculationRate := brandWholesaleRate
		if product.GTVCalculationRate != nil {
			gtvCalculationRate = *product.GTVCalculationRate
		}
		totalGTV += gtvCalculationRate * float64(cartItem.Quantity) * float64(meta.PackSize)
	}
	return totalGTV

}
