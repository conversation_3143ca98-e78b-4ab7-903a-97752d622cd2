package returnreason

const (
	ReasonRetailerDeniedLateDelivery   = "Retailer Denied - Late Delivery"
	ReasonRetailerDeniedNotAvailable   = "Retailer Denied - Not Available"
	ReasonRetailerDeniedDamaged        = "Retailer Denied - Damaged Package"
	ReasonRetailerDeniedNoLongerNeeded = "Retailer Denied - No Longer Needed"
	ReasonRetailerDeniedOthers         = "Retailer Denied - Others"
	ReasonDeliveryIssueNotContact      = "Delivery Issue - Retailer Not Contacted"
	ReasonDeliveryIssueRetailerPickup  = "Delivery Issue - Asked the Retailer to Pickup"
	ReasonDeliveryIssueNoDelivery      = "Delivery Issue - Called but didn't Deliver"
	ReasonNotAvailable                 = "Reason Not Available"
)

const (
	RRDLD = "RRDLD"
	RRDNA = "RRDNA"
	RRDDP = "RRDDP"
	RRDNN = "RRDNN"
	RRDO  = "RRDO"
	RDINC = "RDINC"
	RDIRP = "RDIRP"
	RDIND = "RDIND"
	RNA   = "RNA"
)

var ReturnReasonCodeMap = map[string]string{
	ReasonRetailerDeniedLateDelivery:   RRDLD,
	ReasonRetailerDeniedNotAvailable:   RRDNA,
	ReasonRetailerDeniedDamaged:        RRDDP,
	ReasonRetailerDeniedNoLongerNeeded: RRDNN,
	ReasonRetailerDeniedOthers:         RRDO,
	ReasonDeliveryIssueNotContact:      RDINC,
	ReasonDeliveryIssueRetailerPickup:  RDIRP,
	ReasonDeliveryIssueNoDelivery:      RDIND,
	ReasonNotAvailable:                 RNA,
}

var ReturnCodeReasonMap = map[string]string{}
