package orderreason

import (
	cancelReason "kc/internal/ondc/service/orderReason/cancelReason"
	returnReason "kc/internal/ondc/service/orderReason/returnReason"
)

type OrderReasonResponse struct {
	ReasonString string `json:"reason_string"`
	ReasonCode   string `json:"reason_code"`
}

func GetOrderCancelCodeFromReason(currentReason string) OrderReasonResponse {
	response := OrderReasonResponse{
		ReasonString: cancelReason.ReasonNotAvailable,
		ReasonCode:   cancelReason.CancelReasonCodeMap[cancelReason.ReasonNotAvailable],
	}

	if code, exists := cancelReason.CancelReasonCodeMap[currentReason]; exists {
		response.ReasonString = currentReason
		response.ReasonCode = code
		return response
	}

	switch currentReason {
	case "गलती से आर्डर हो गया था", "गलती से ऑर्डर हो गया था":
		response.ReasonString = cancelReason.ReasonAccidentalOrder
		response.ReasonCode = cancelReason.CancelReasonCodeMap[cancelReason.ReasonAccidentalOrder]
	case "आर्डर में बदलाव करना है", "ऑर्डर में बदलाव करना है":
		response.ReasonString = cancelReason.ReasonDuplicateOrder
		response.ReasonCode = cancelReason.CancelReasonCodeMap[cancelReason.ReasonDuplicateOrder]
	case "लोकल में ज्यादा मार्जिन मिल रहा", "लोकल में ज़्यादा मार्जिन मिल रहा":
		response.ReasonString = cancelReason.ReasonInsufficientMargin
		response.ReasonCode = cancelReason.CancelReasonCodeMap[cancelReason.ReasonInsufficientMargin]
	case "अन्य कारण":
		response.ReasonString = cancelReason.ReasonOther
		response.ReasonCode = cancelReason.CancelReasonCodeMap[cancelReason.ReasonOther]
	default:
		// slack.SendSlackMessage(fmt.Sprintf("Error mapping cancel reason code: %s", currentReason))
		return response
	}
	return response
}

func GetOrderCancelReasonFromCode(currentCode string) OrderReasonResponse {
	if len(cancelReason.CancelCodeReasonMap) == 0 {
		for reason, code := range cancelReason.CancelReasonCodeMap {
			cancelReason.CancelCodeReasonMap[code] = reason
		}
	}

	response := OrderReasonResponse{
		ReasonString: cancelReason.ReasonNotAvailable,
		ReasonCode:   cancelReason.CancelReasonCodeMap[cancelReason.ReasonNotAvailable],
	}

	if reason, exists := cancelReason.CancelCodeReasonMap[currentCode]; exists {
		response.ReasonString = reason
		response.ReasonCode = currentCode
		return response
	}
	// slack.SendSlackMessage(fmt.Sprintf("Error mapping cancel reason code: %s", currentCode))
	return response
}

func GetOrderReturnCodeFromReason(currentReason string) OrderReasonResponse {
	response := OrderReasonResponse{
		ReasonString: returnReason.ReasonNotAvailable,
		ReasonCode:   returnReason.ReturnReasonCodeMap[returnReason.ReasonNotAvailable],
	}

	if code, exists := returnReason.ReturnReasonCodeMap[currentReason]; exists {
		response.ReasonString = currentReason
		response.ReasonCode = code
		return response
	}
	// slack.SendSlackMessage(fmt.Sprintf("Error mapping return reason code: %s", currentReason))
	return response
}

func GetOrderReturnReasonFromCode(currentCode string) OrderReasonResponse {
	if len(returnReason.ReturnCodeReasonMap) == 0 {
		for reason, code := range returnReason.ReturnReasonCodeMap {
			returnReason.ReturnCodeReasonMap[reason] = code
		}
	}

	response := OrderReasonResponse{
		ReasonString: returnReason.ReasonNotAvailable,
		ReasonCode:   returnReason.ReturnReasonCodeMap[returnReason.ReasonNotAvailable],
	}

	if reason, exists := returnReason.ReturnCodeReasonMap[currentCode]; exists {
		response.ReasonString = reason
		response.ReasonCode = currentCode
		return response
	}
	// slack.SendSlackMessage(fmt.Sprintf("Error mapping return reason code: %s", currentCode))
	return response
}

func GetCancelReasonOptionsAsMap() []map[string]interface{} {
	options := make([]map[string]interface{}, 0, len(cancelReason.CancelCodeReasonMap))
	id := 1
	for reason := range cancelReason.CancelReasonCodeMap {
		if reason == cancelReason.ReasonNotAvailable {
			continue
		}
		opt := map[string]interface{}{
			"label": reason,
			"value": reason,
			"id":    id,
		}
		options = append(options, opt)
		id++
	}
	return options
}
