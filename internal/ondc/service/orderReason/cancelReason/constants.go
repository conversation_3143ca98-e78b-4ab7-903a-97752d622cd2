package cancelreason

const (
	ReasonAccidentalOrder    = "Accidental Order (Was Trying | Ordered by <PERSON><PERSON><PERSON>)"
	ReasonInsufficientMargin = "Insufficient Margin"
	ReasonDuplicateOrder     = "Duplicate Order | Wants to Modify Order"
	ReasonLongDelivery       = "Delivery Time Too Long"
	ReasonPastOrderIssues    = "Issues with Past Order"
	ReasonRetailerContact    = "Unable to Contact Retailer"
	ReasonAdvancePayment     = "Unable to Pay Advance"
	ReasonOther              = "Other Issue"
	ReasonNotAvailable       = "Reason Not Available"
	PincodeNotServiceable    = "Pincode Not Serviceable"
	StockIssues              = "Stock Issues"
)

const (
	CAO  = "CAO"
	CIM  = "CIM"
	CDO  = "CDO"
	CDTL = "CDTL"
	CIPO = "CIPO"
	CUCR = "CUCR"
	CUPA = "CUPA"
	COI  = "COI"
	RNA  = "RNA"
	PNS  = "PNS"
	SIS  = "SIS"
)

var CancelReasonCodeMap = map[string]string{
	ReasonAccidentalOrder:    CAO,
	ReasonInsufficientMargin: CIM,
	ReasonDuplicateOrder:     CDO,
	ReasonLongDelivery:       CDTL,
	ReasonPastOrderIssues:    CIPO,
	ReasonRetailerContact:    CUCR,
	ReasonAdvancePayment:     CUPA,
	ReasonOther:              COI,
	ReasonNotAvailable:       RNA,
	PincodeNotServiceable:    PNS,
	StockIssues:              SIS,
}

var CancelCodeReasonMap = map[string]string{
	CAO:  ReasonAccidentalOrder,
	CIM:  ReasonInsufficientMargin,
	CDO:  ReasonDuplicateOrder,
	CDTL: ReasonLongDelivery,
	CIPO: ReasonPastOrderIssues,
	CUCR: ReasonRetailerContact,
	CUPA: ReasonAdvancePayment,
	COI:  ReasonOther,
	PNS:  PincodeNotServiceable,
	SIS:  StockIssues,
}
