package brands

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"sync/atomic"
	"time"
	"unsafe"

	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/brands/models"
)

var bc *BrandCache

// cacheData holds all the cache data in a single struct
type cacheData struct {
	brands             map[string]*models.Brands
	allBrands          []string
	brandMeta          map[string]dto.Seller
	sourceSellerMap    map[string]string
	sellerSourceMap    map[string]string
	lastUpdate         time.Time
	kcFullfilledBrands []string
	loyaltySourceIds   map[string]models.CashbackActions
	brandDetails       map[string]map[string]string // Additional details for brands
	sellerNameMapping  map[string]string            // Maps seller codes to their names
}

type BrandCache struct {
	data *cacheData // Atomic pointer swap
	db   *sqlRepo.Repository
}

// BrandFilters defines filtering options for brands
type BrandFilters struct {
	Sources   []string
	SortBy    string // Name, CreatedAt, UpdatedAt
	SortOrder string // asc, desc
	IsActive  *bool  // If brand has active flag in metadata
}

func NewBrandCache(db *sqlRepo.Repository, config string) *BrandCache {
	initialData := &cacheData{
		brands:             make(map[string]*models.Brands),
		allBrands:          make([]string, 0),
		brandMeta:          make(map[string]dto.Seller),
		sourceSellerMap:    make(map[string]string),
		sellerSourceMap:    make(map[string]string),
		lastUpdate:         time.Now(),
		kcFullfilledBrands: make([]string, 0),
		loyaltySourceIds:   make(map[string]models.CashbackActions),
		sellerNameMapping:  make(map[string]string),
	}

	bce := &BrandCache{
		data: initialData,
		db:   db,
	}
	bc = bce

	// Initial load
	bc.RefreshCache()

	// Start background refresh
	go bc.backgroundRefresh(config)

	return bc
}

func (bc *BrandCache) backgroundRefresh(config string) {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	consecutiveFailures := 0
	maxFailures := 3

	for range ticker.C {
		if err := bc.RefreshCache(); err != nil {
			consecutiveFailures++
			log.Printf("Error refreshing brand cache (attempt %d/%d): %v",
				consecutiveFailures, maxFailures, err)

			if consecutiveFailures >= maxFailures {
				slack.SendSlackMessage(fmt.Sprintf(
					"CRITICAL: Brand cache refresh has failed %d consecutive times. Last error: %v",
					consecutiveFailures, err))
			}
		} else {
			consecutiveFailures = 0 // Reset on success
			if config == "prod" {
				slack.SendSlackMessage("Brand Cache refreshed successfully at " + time.Now().Format(time.RFC3339))
			}
		}
	}
}

// getData safely gets the current cache data
func (bc *BrandCache) getData() *cacheData {
	return (*cacheData)(atomic.LoadPointer((*unsafe.Pointer)(unsafe.Pointer(&bc.data))))
}

// setData safely updates the cache data
func (bc *BrandCache) setData(newData *cacheData) {
	atomic.StorePointer((*unsafe.Pointer)(unsafe.Pointer(&bc.data)), unsafe.Pointer(newData))
}

func (bc *BrandCache) RefreshCache() error {
	// Fetch brands from DB
	brands, err := bc.fetchDataFromDB()
	if err != nil {
		return err
	}

	newBrands := make(map[string]*models.Brands)
	newSourcesBrands := make(map[string][]*models.Brands)
	newAllBrands := make([]string, 0, len(brands))
	newBrandMeta := make(map[string]dto.Seller)
	newSellerSourceMap := make(map[string]string)
	newSourceSellerMap := make(map[string]string)
	newKcFullfilledBrands := make([]string, 0)
	newSellerCashbackSourceMap := make(map[string]models.CashbackActions)
	newBrandDetailsMap := make(map[string]map[string]string)
	newSellerNameMapping := make(map[string]string)

	for _, b := range brands {
		newBrands[b.Code] = b
		newSourcesBrands[b.Source] = append(newSourcesBrands[b.Source], b)
		newAllBrands = append(newAllBrands, b.Code)
		newSellerSourceMap[b.Code] = b.Source
		newSourceSellerMap[b.Source] = b.Code

		var temporarySeller dto.Seller
		if err := json.Unmarshal(b.Meta, &temporarySeller); err != nil {
			log.Printf("ERROR UNMARSHALLING META for brand %s: %v", b.Code, err)
			continue
		}

		if temporarySeller.KcFullFilled {
			newKcFullfilledBrands = append(newKcFullfilledBrands, temporarySeller.Seller)
		}

		newSellerCashbackSourceMap[temporarySeller.Seller] = models.CashbackActions{
			ADD_CASHBACK:    temporarySeller.AddCashbackSourceId,
			REDEEM_CASHBACK: temporarySeller.RedeemCashbackSourceId,
		}
		newBrandDetailsMap[b.Code] = map[string]string{
			"followers":        "1700",
			"bulk_order_form":  temporarySeller.BulkOrderForm,
			"help_center_form": "https://webapps.retailpulse.ai/customer-support/home",
			"profile_image":    temporarySeller.Logo,
		}

		newSellerNameMapping[b.Code] = b.Name

		newBrandMeta[b.Code] = temporarySeller
	}

	// Create new cache data struct
	newData := &cacheData{
		brands:             newBrands,
		allBrands:          newAllBrands,
		brandMeta:          newBrandMeta,
		sellerSourceMap:    newSellerSourceMap,
		sourceSellerMap:    newSourceSellerMap,
		lastUpdate:         time.Now(),
		kcFullfilledBrands: newKcFullfilledBrands,
		loyaltySourceIds:   newSellerCashbackSourceMap,
		brandDetails:       newBrandDetailsMap,
		sellerNameMapping:  newSellerNameMapping,
	}

	// Atomic swap - this is the only synchronization point!
	bc.setData(newData)

	fmt.Println("Brand cache refreshed at", newData.lastUpdate)
	return nil
}

func GetCashbackActionsBySource(source string) (models.CashbackActions, bool) {
	if bc == nil {
		return models.CashbackActions{}, false
	}

	data := bc.getData() // Atomic read

	actions, exists := data.loyaltySourceIds[source]

	if !exists {
		slack.SendSlackMessage(fmt.Sprintf("get cashback actions by source failed for source: %s and current state of loyalty is %+v\n", source, data.loyaltySourceIds))
	}

	return actions, exists
}

// GetBrandByCode returns a brand by its code - NO LOCKING!
func GetBrandByCode(code string) (*models.Brands, bool) {
	if bc == nil {
		return nil, false
	}

	data := bc.getData() // Atomic read
	brand, exists := data.brands[code]
	return brand, exists
}

func GetBrandsMeta() map[string]dto.Seller {
	if bc == nil {
		return make(map[string]dto.Seller)
	}

	data := bc.getData() // Atomic read

	// Return a copy to prevent external modifications
	result := make(map[string]dto.Seller, len(data.brandMeta))
	for k, v := range data.brandMeta {
		result[k] = v
	}

	if len(result) == 0 {
		slack.SendSlackMessage("BrandMeta is empty, ensure the cache is initialized and populated correctly.")
	}

	return result
}

func GetSourceSellerMap() map[string]string {
	if bc == nil {
		return make(map[string]string)
	}

	data := bc.getData() // Atomic read

	// Return a copy to prevent external modifications
	result := make(map[string]string, len(data.sourceSellerMap))
	for k, v := range data.sourceSellerMap {
		result[k] = v
	}

	if len(result) == 0 {
		slack.SendSlackMessage("SourceSellerMap is empty, ensure the cache is initialized and populated correctly.")
	}

	return result
}

func GetSellerSourceMap() map[string]string {
	if bc == nil {
		return make(map[string]string)
	}

	data := bc.getData() // Atomic read

	// Return a copy to prevent external modifications
	result := make(map[string]string, len(data.sellerSourceMap))
	for k, v := range data.sellerSourceMap {
		result[k] = v
	}

	if len(result) == 0 {
		slack.SendSlackMessage("SellerSourceMap is empty, ensure the cache is initialized and populated correctly.")
	}

	return result
}

func GetAllCapsBrandMap() map[string]string {
	if bc == nil {
		return make(map[string]string)
	}
	data := bc.getData() // Atomic read

	allCapMap := make(map[string]string, len(data.brands))

	for _, brand := range data.brands {
		if brand.Code != "" {
			allCapMap[brand.Code] = strings.ToUpper(strings.ReplaceAll(brand.Code, "_", " "))
		}
	}

	if len(allCapMap) == 0 {
		slack.SendSlackMessage("AllCapsBrand is empty, ensure the cache is initialized and populated correctly.")
	}

	return allCapMap
}

func GetSellerNameMapping() map[string]string {
	if bc == nil {
		return map[string]string{}
	}

	data := bc.getData() // Atomic read

	return data.sellerNameMapping
}

func GetNameMappingBySeller(seller string) (string, error) {
	if bc == nil {
		return "", fmt.Errorf("brand cache is not initialized")
	}

	data := bc.getData()

	name, exists := data.sellerNameMapping[seller]

	if !exists {
		slack.SendSlackMessage("GetNameMappingBySeller failed for seller: " + seller)
		return "", fmt.Errorf("seller %s not found in name mapping", seller)
	}

	return name, nil
}

func GetSourceBySeller(seller string) (string, bool) {
	if bc == nil {
		return "", false
	}

	data := bc.getData() // Atomic read

	source, exists := data.sellerSourceMap[seller]

	return source, exists
}

func GetSellerBySource(source string) (string, bool) {
	if bc == nil {
		return "", false
	}

	data := bc.getData() // Atomic read

	seller, exists := data.sourceSellerMap[source]

	if !exists {
		slack.SendSlackMessage(fmt.Sprintf("GetSellerBySource failed for source: %s and current state of sourceSellerMap is %+v\n", source, data.sourceSellerMap))
	}

	return seller, exists
}

func GetAllBrands() []string {
	if bc == nil {
		return []string{}
	}

	data := bc.getData()

	if len(data.allBrands) == 0 {
		slack.SendSlackMessage("AllBrands is empty, ensure the cache is initialized and populated correctly.")
	}

	return data.allBrands
}

func GetKcFullFilledBrands() []string {
	if bc == nil {
		return []string{}
	}

	data := bc.getData()

	if len(data.kcFullfilledBrands) == 0 {
		slack.SendSlackMessage("KcFullFilledBrands is empty, ensure the cache is initialized and populated correctly.")
	}

	return data.kcFullfilledBrands
}

func GetBrandMetaBySeller(seller string) (dto.Seller, bool) {
	if bc == nil {
		return dto.Seller{}, false
	}

	data := bc.getData() // Atomic read

	sellerData, exists := data.brandMeta[seller]

	return sellerData, exists
}

// GetLastUpdateTime returns when the cache was last updated - NO LOCKING!
func GetLastUpdateTime() time.Time {
	if bc == nil {
		return time.Time{}
	}

	data := bc.getData() // Atomic read
	return data.lastUpdate
}

func GetBrandDetailsForSeller(seller string) (map[string]string, bool) {

	if bc == nil {
		return make(map[string]string), false
	}

	data := bc.getData()

	brandDetails, exists := data.brandDetails[seller]

	return brandDetails, exists

}

func (bc *BrandCache) fetchDataFromDB() ([]*models.Brands, error) {
	query := `
		SELECT 
			id, code, name, source, created_at, updated_at, updated_by, meta
		FROM 
			kiranaclubdb.kiranabazar_sellers ks 
	`

	var brands []*models.Brands
	_, err := bc.db.CustomQuery(&brands, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching brands: %v", err)
	}

	return brands, nil
}
