package models

import "gorm.io/datatypes"

type Brands struct {
	ID        string         `json:"id" db:"id"`
	Code      string         `json:"code" db:"code"`
	Name      string         `json:"name" db:"name"`
	Source    string         `json:"source" db:"source"`
	CreatedAt int64          `json:"created_at" db:"created_at"`
	UpdatedAt int64          `json:"updated_at" db:"updated_at"`
	UpdatedBy string         `json:"updated_by" db:"updated_by"`
	Meta      datatypes.JSON `json:"meta,omitempty" db:"meta"`
}

type CashbackActions struct {
	ADD_CASHBACK    int
	REDEEM_CASHBACK int
}
