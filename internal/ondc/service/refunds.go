package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"kc/internal/ondc/external/whatsapp"
	"kc/internal/ondc/infrastructure/payments"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/kcFinances"
	"kc/internal/ondc/service/orderStatus/constants"
	shipmentstatus "kc/internal/ondc/service/orderStatus/shipmentStatus"
	"kc/internal/ondc/service/products"
	"kc/internal/ondc/utils"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/mixpanel/mixpanel-go"
	"gorm.io/datatypes"
)

func (s *Service) getProductsItemsForOrders(ctx context.Context, orderId int64) ([]dto.RefundItemInfo, error) {
	// Fetch order information from repository
	orderInfo, err := GetOrderInfo(s.repository, orderId)
	if err != nil {
		return nil, err
	}

	orderDetails, err := GetOrderDetails(s.repository, fmt.Sprintf("%d", orderId))
	if err != nil {
		return nil, err
	}

	discount := utils.RoundToTwoDecimals(orderDetails.GetDiscountValue())
	cashback := orderDetails.GetAppliedCashbackValue()
	discount = discount - cashback

	// Prepare data for discount allocation
	itemPrices := make([]float64, 0, len(orderDetails.Cart))
	for _, item := range orderDetails.Cart {
		var meta shared.KiranaBazarProductMeta
		if err := json.Unmarshal(item.Meta, &meta); err != nil {
			return nil, err
		}
		invoiceWholesale := meta.WholesaleRate
		if orderInfo.Seller == "apsara_tea" {
			invoiceWholesale = meta.WholesaleRate * float64(meta.PackSize)
		}

		invoicePackSize := meta.PackSize
		if orderInfo.Seller == "apsara_tea" {
			invoicePackSize = 1
		}
		quantity := float64(int(item.Quantity) * invoicePackSize)
		price := invoiceWholesale * quantity
		itemPrices = append(itemPrices, price)
	}

	// Allocate discounts proportionally with two-decimal precision
	allocatedDiscounts := allocateDiscount(itemPrices, discount)
	//allocatedCashback := allocateDiscount(itemPrices, cashback)

	var items []dto.RefundItemInfo

	// Process each item in the cart
	for idx, item := range orderDetails.Cart {
		var meta shared.KiranaBazarProductMeta
		if err := json.Unmarshal(item.Meta, &meta); err != nil {
			return []dto.RefundItemInfo{}, err
		}

		product, ok := products.GetProductByID(item.ID)
		if !ok {
			return nil, errors.New("product not found")
		}

		invoiceWholesale := meta.WholesaleRate
		if orderInfo.Seller == "apsara_tea" {
			invoiceWholesale = meta.WholesaleRate * float64(meta.PackSize)
		}

		invoicePackSize := meta.PackSize
		if orderInfo.Seller == "apsara_tea" {
			invoicePackSize = 1
		}

		// Calculate item quantities and values with two-decimal precision
		quantity := float64(int(item.Quantity) * invoicePackSize)
		price := utils.RoundToTwoDecimals(invoiceWholesale * quantity)
		itemDiscount := allocatedDiscounts[idx]

		// Calculate price after discount with two-decimal precision
		priceAfterDiscount := utils.RoundToTwoDecimals(price - itemDiscount)

		unitPriceAfterDiscount := utils.RoundToTwoDecimals(priceAfterDiscount / quantity)

		items = append(items, dto.RefundItemInfo{
			Description:            item.Name,
			HindiName:              meta.HindiName,
			SKU:                    product.Code,
			Quantity:               int(quantity),
			UnitPrice:              utils.RoundToTwoDecimals(invoiceWholesale),
			UnitPriceAfterDiscount: unitPriceAfterDiscount,
			ItemValue:              utils.RoundToTwoDecimals(price),
			ItemValueAfterDiscount: priceAfterDiscount,
			ItemDiscount:           itemDiscount,
			ImageUrl:               item.ImageUrls[0],
		})

	}

	return items, nil
}

func GetOrderPaymentDataOrderId(ctx context.Context, OrderId int64, repository *sqlRepo.Repository) (dao.KiranaBazarOrderPayment, error) {
	paymentTransaction := dao.KiranaBazarOrderPayment{}
	// TODO: handle RQ rnn mapping
	_, err := repository.Find(map[string]interface{}{
		"order_id": OrderId,
	}, &paymentTransaction)

	if err != nil {
		logger.Error(ctx, "Failed to fetch the payment details", err, OrderId)
		return dao.KiranaBazarOrderPayment{}, err
	}

	return paymentTransaction, nil
}

// GetOrderProducts retrieves the products of an order for refund
func (s *Service) GetOrderProducts(ctx *gin.Context, request dto.GetOrderProductsRequest) (dto.GetOrderProductsResponse, error) {
	// Check if order exists
	// TODO: handle past refunds

	orderStatus, err := s.GetOrderStatusFromDB(context.Background(), &dto.OrderStatusRequest{
		OrderID: request.OrderID,
	})
	if err != nil {
		return dto.GetOrderProductsResponse{}, fmt.Errorf("failed to get order status: %v", err)
	}

	//Check if order is delivered
	if orderStatus.DisplayStatus != constants.DELIVERED {
		return dto.GetOrderProductsResponse{}, errors.New("order is not delivered yet, cannot initiate refund")
	}

	transactionData, err := GetOrderPaymentDataOrderId(ctx, request.OrderID, s.repository)
	if err != nil {
		return dto.GetOrderProductsResponse{}, fmt.Errorf("failed to get payment transaction: %v", err)
	}

	if transactionData.RefundAmount != nil && *transactionData.RefundAmount > 0 {
		return dto.GetOrderProductsResponse{}, errors.New("order is already refunded")
	}

	items, err := s.getProductsItemsForOrders(ctx, request.OrderID)
	if err != nil {
		return dto.GetOrderProductsResponse{}, fmt.Errorf("failed to get order products: %v", err)
	}

	return dto.GetOrderProductsResponse{
		OrderID:  request.OrderID,
		Products: items,
	}, nil
}

// GetRefundOptions retrieves the available refund options for an order
func (s *Service) GetRefundOptions(ctx *gin.Context, request dto.GetRefundOptionsRequest) (dto.GetRefundOptionsResponse, error) {
	// Check if order exists
	orderStatus, err := s.GetOrderStatusFromDB(context.Background(), &dto.OrderStatusRequest{
		OrderID: request.OrderID,
	})
	if err != nil {
		return dto.GetRefundOptionsResponse{}, fmt.Errorf("failed to get order status: %v", err)
	}

	// Check if order is delivered
	if orderStatus.ShipmentStatus != shipmentstatus.DELIVERED {
		return dto.GetRefundOptionsResponse{}, errors.New("order is not delivered yet, cannot initiate refund")
	}

	if request.RefundAmount == nil && request.RefundedProducts == nil {
		return dto.GetRefundOptionsResponse{}, fmt.Errorf("refund not defined")
	}

	refundAmount := 0.0
	if request.RefundAmount != nil {
		refundAmount = *request.RefundAmount
	}

	if request.RefundedProducts != nil {
		for _, product := range *request.RefundedProducts {
			if product.RefundedQuantity > product.Quantity {
				return dto.GetRefundOptionsResponse{}, fmt.Errorf("refund quantity %d can't be more than quantity %d for %s", product.RefundedQuantity, product.Quantity, product.Description)
			}
			if product.RefundedQuantity == product.Quantity {
				refundAmount += product.ItemValueAfterDiscount
			} else {
				refundAmount += float64(product.RefundedQuantity) * product.UnitPriceAfterDiscount
			}
		}
	}

	// Get payment transaction for the order
	transactionData, err := GetOrderPaymentDataOrderId(ctx, request.OrderID, s.repository)
	if err != nil {
		return dto.GetRefundOptionsResponse{}, fmt.Errorf("failed to get payment transaction: %v", err)
	}

	if transactionData.Amount == nil {
		return dto.GetRefundOptionsResponse{}, errors.New("transaction amount is not set")
	}

	alreadyRefundAmount := 0.0
	paidAmount := 0.0
	if transactionData.RefundAmount != nil {
		alreadyRefundAmount = *transactionData.RefundAmount
	}
	if transactionData.PaidAmount != nil {
		paidAmount = *transactionData.PaidAmount
	}

	if refundAmount <= 0 || refundAmount+alreadyRefundAmount > *transactionData.Amount {
		return dto.GetRefundOptionsResponse{}, fmt.Errorf("invalid refund amount: %v", request.RefundAmount)
	}

	var options []dto.RefundOption
	options = append(options, dto.RefundOption{
		ID:          "wallet",
		Name:        "Added in Wallet",
		Description: "Money will be added to wallet",
		Amount:      refundAmount,
	})

	//options = append(options, dto.RefundOption{
	//	ID:          "upi",
	//	Name:        "Return to UPI",
	//	Description: "Money will reverted to this UPI ID",
	//	Amount:      refundAmount,
	//})

	if refundAmount < paidAmount && transactionData.PaymentId != nil {
		// Add original payment method option
		options = append(options, dto.RefundOption{
			ID:          "source",
			Name:        "Return to Original Payment Method",
			Description: "Money will be Refund to the original payment method",
			Amount:      refundAmount,
		})
	}

	return dto.GetRefundOptionsResponse{
		Options:               options,
		OrderID:               request.OrderID,
		AlreadyRefundedAmount: alreadyRefundAmount,
	}, nil
}

// createCreditWalletTransaction makes an API call to credit the wallet
func (s *Service) createCreditWalletTransaction(orderId int64, amount float64, source int) (*string, error) {
	headers := map[string]string{
		"Content-Type": "application/json",
	}

	uid, err := s.getUserIdFromOrderID(orderId)
	if err != nil {
		return nil, fmt.Errorf("error getting user id from order id: %v", err)
	}

	payload := map[string]interface{}{
		"user_id":                    uid,
		"transaction_type":           "CREDIT",
		"amount":                     amount,
		"status":                     "SUCCESS",
		"source_id":                  source,
		"increase_transaction_limit": true,
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("error marshaling wallet transaction payload: %v", err)
	}

	req, err := http.NewRequest(
		"POST",
		"https://darkarts.retailpulse.ai/api/v1/wallet/transaction",
		strings.NewReader(string(payloadBytes)),
	)
	if err != nil {
		return nil, fmt.Errorf("error creating wallet transaction request: %v", err)
	}

	// Set headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending wallet transaction request: %v", err)
	}
	defer resp.Body.Close()

	// Read the response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading wallet transaction response: %v", err)
	}

	// Check if the request was successful
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("wallet API error: %s, code: %d", string(body), resp.StatusCode)
	}

	// Parse the response
	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("error unmarshaling wallet transaction response: %v", err)
	}

	// Check if status is OK
	status, ok := response["status"].(string)
	if !ok || status != "OK" {
		return nil, fmt.Errorf("invalid response status: %v", response["status"])
	}

	// Get data from response
	data, ok := response["data"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid response data format")
	}

	// Check if transaction was successful
	if txStatus, ok := data["status"].(string); !ok || txStatus != "SUCCESS" {
		return nil, fmt.Errorf("transaction failed with status: %v", data["status"])
	}

	// Get transaction ID
	transactionID, ok := data["transaction_id"].(string)
	if !ok {
		return nil, fmt.Errorf("transaction_id not found in response")
	}

	return &transactionID, nil
}

// updateFirebasePaymentRefundKYC updates the user's paymentRefundKYC status in Firebase
func (s *Service) updateFirebasePaymentRefundKYC(uid string) error {
	if s.FirebaseRepository == nil || s.FirebaseRepository.RtDb == nil {
		return fmt.Errorf("firebase client is not initialized")
	}

	ref := s.FirebaseRepository.RtDb.NewRef(fmt.Sprintf("/users/%s", uid))
	err := ref.Update(context.Background(), map[string]interface{}{
		"paymentRefundKYC": "APPROVED",
	})
	if err != nil {
		return fmt.Errorf("error updating firebase: %v", err)
	}

	return nil
}

func (s *Service) ProcessPaymentRefundKYCUpdate(ctx *gin.Context, request dto.UpdateKYCRequest) (dto.UpdateKYCResponse, error) {
	if request.UserID == "" {
		return dto.UpdateKYCResponse{
			RefundKYCUpdated: false,
			Error: dto.AppResponseError{
				Message: utils.StrPtr("user_id cannot be empty"),
			},
		}, fmt.Errorf("user_id cannot be empty")
	}

	err := s.updateFirebasePaymentRefundKYC(request.UserID)
	if err != nil {
		return dto.UpdateKYCResponse{
			RefundKYCUpdated: false,
			Error: dto.AppResponseError{
				Message: utils.StrPtr(err.Error()),
			},
		}, err
	}

	return dto.UpdateKYCResponse{
		RefundKYCUpdated: true,
	}, nil
}

// InitiateRefund initiates a refund for an order
func (s *Service) InitiateRefund(ctx *gin.Context, request dto.InitiateRefundRequest) (dto.InitiateRefundResponse, error) {
	// TODO: need to adjust coins and cashback also
	// TODO: need to handle duplicate refunds
	// TODO: need to log and handle refunded quantity -- doing in meta for now
	// Check if order exists
	orderStatus, err := s.GetOrderStatusFromDB(context.Background(), &dto.OrderStatusRequest{
		OrderID: request.OrderID,
	})
	if err != nil {
		return dto.InitiateRefundResponse{
			RefundInitiated: false,
			Error: dto.AppResponseError{
				Message: utils.StrPtr(fmt.Sprintf("failed to get order status")),
			},
		}, fmt.Errorf("failed to get order status: %v", err)
	}

	// Check if order is delivered
	if orderStatus.ShipmentStatus != shipmentstatus.DELIVERED && (request.Force == nil || !*request.Force) {
		return dto.InitiateRefundResponse{
			RefundInitiated: false,
			Error: dto.AppResponseError{
				Message: utils.StrPtr(fmt.Sprintf("order is not delivered yet, cannot initiate refund")),
			},
		}, errors.New("order is not delivered yet, cannot initiate refund")
	}

	if request.RefundAmount == nil && request.RefundedProducts == nil {
		return dto.InitiateRefundResponse{
			RefundInitiated: false,
			Error: dto.AppResponseError{
				Message: utils.StrPtr(fmt.Sprintf("refund not defined")),
			},
		}, fmt.Errorf("refund not defined")
	}

	refundAmount := 0.0
	if request.RefundAmount != nil {
		refundAmount = *request.RefundAmount
		_, err = s.ProcessCreditNote(context.Background(), &dto.CreditNoteRequest{
			CreditNote: &dto.CreditNote{
				OrderID:        fmt.Sprintf("%d", request.OrderID),
				CreditNoteType: kcFinances.CREDIT_NOTE_TYPE.CUSTOM,
				//PaymentID:      138534,
				Amount: *request.RefundAmount,
			},
		})
	}

	creditNoteItems := make([]*dto.CreditNoteItem, 0)
	if request.RefundedProducts != nil {
		for _, product := range *request.RefundedProducts {
			if product.RefundedQuantity > product.Quantity {
				return dto.InitiateRefundResponse{
					RefundInitiated: false,
					Error: dto.AppResponseError{
						Message: utils.StrPtr(fmt.Sprintf("refund quantity %d can't be more than quantity %d for %s", product.RefundedQuantity, product.Quantity, product.Description)),
					},
				}, fmt.Errorf("refund quantity %d can't be more than quantity %d for %s", product.RefundedQuantity, product.Quantity, product.Description)
			}
			if product.RefundedQuantity == product.Quantity {
				refundAmount += product.ItemValueAfterDiscount
			} else {
				refundAmount += float64(product.RefundedQuantity) * product.UnitPriceAfterDiscount
			}
			creditNoteItems = append(creditNoteItems, &dto.CreditNoteItem{
				SKUID:    product.SKU,
				Quantity: product.RefundedQuantity,
			})
		}
		_, err = s.ProcessCreditNote(context.Background(), &dto.CreditNoteRequest{
			CreditNote: &dto.CreditNote{
				OrderID:        fmt.Sprintf("%d", request.OrderID),
				CreditNoteType: kcFinances.CREDIT_NOTE_TYPE.DAMAGED_ITEMS,
			},
			CreditNoteItems: creditNoteItems,
		})
	}

	// Get payment transaction for the order
	transactionData, err := GetOrderPaymentDataOrderId(ctx, request.OrderID, s.repository)
	if err != nil {
		return dto.InitiateRefundResponse{
			RefundInitiated: false,
			Error: dto.AppResponseError{
				Message: utils.StrPtr(fmt.Sprintf("failed to get payment transaction")),
			},
		}, fmt.Errorf("failed to get payment transaction: %v", err)
	}

	if transactionData.Amount == nil {
		return dto.InitiateRefundResponse{
			RefundInitiated: false,
			Error: dto.AppResponseError{
				Message: utils.StrPtr(fmt.Sprintf("transaction amount is not set")),
			},
		}, errors.New("transaction amount is not set")
	}

	alreadyRefundAmount := 0.0
	paidAmount := 0.0
	if transactionData.RefundAmount != nil {
		alreadyRefundAmount = *transactionData.RefundAmount
	}
	if transactionData.PaidAmount != nil {
		paidAmount = *transactionData.PaidAmount
	}

	if refundAmount <= 0 || refundAmount+alreadyRefundAmount > *transactionData.Amount {
		return dto.InitiateRefundResponse{
			RefundInitiated: false,
			Error: dto.AppResponseError{
				Message: utils.StrPtr(fmt.Sprintf("invalid refund amount: %v", request.RefundAmount)),
			},
		}, fmt.Errorf("invalid refund amount: %v", request.RefundAmount)
	}

	phoneNumber, err := s.getPhoneFromOrderID(request.OrderID)
	if err != nil {
		return dto.InitiateRefundResponse{
			RefundInitiated: false,
			Error: dto.AppResponseError{
				Message: utils.StrPtr(fmt.Sprintf("failed to get phone number")),
			},
		}, fmt.Errorf("failed to get phone number: %v", err)
	}

	missingItemsStr := ""
	appendFlag := false
	if request.RefundedProducts != nil {
		for _, product := range *request.RefundedProducts {
			if appendFlag {
				missingItemsStr += ", "
				appendFlag = false
			}
			if product.RefundedQuantity > 0 {
				missingItemsStr += fmt.Sprintf("%s", product.HindiName)
				appendFlag = true
			}
		}
	}

	var refundId string
	var refundStatus string
	var refundGateway string
	var refundMethod string
	var paymentID *string

	if request.RefundMode.ID == "wallet" {
		// Return to wallet
		transactionId, err := s.createCreditWalletTransaction(request.OrderID, refundAmount, 94)
		if err != nil {
			return dto.InitiateRefundResponse{
				RefundInitiated: false,
				Error: dto.AppResponseError{
					Message: utils.StrPtr(fmt.Sprintf("Failed to credit wallet: %v", err)),
				},
			}, fmt.Errorf("failed to credit wallet: %v", err)
		}

		// Update Firebase
		err = s.updateFirebasePaymentRefundKYC(request.UserID)
		if err != nil {
			// Log the error but don't fail the refund
			fmt.Printf("Error updating Firebase: %v\n", err)
		}

		refundId = *transactionId
		refundStatus = "SUCCESS"
		refundGateway = "wallet"
		refundMethod = "wallet"

	} else if request.RefundMode.ID == "source" {
		if !(refundAmount < paidAmount && transactionData.PaymentId != nil) {
			return dto.InitiateRefundResponse{
				RefundInitiated: false,
				Error: dto.AppResponseError{
					Message: utils.StrPtr(fmt.Sprintf("invalid refund option: %v", request.RefundAmount)),
				},
			}, fmt.Errorf("invalid refund option: %v", request.RefundAmount)
		}

		// initiate gateway refund
		refundResponse, err := s.RefundAdvancePayment(ctx, dto.RefundPaymentApiRequest{
			UserID:         request.UserID,
			OrderID:        request.OrderID,
			Amount:         refundAmount,
			InstantPayment: utils.BoolPtr(true),
			Force:          utils.BoolPtr(true),
			Reason:         utils.StrPtr("Refund requested by user"),
		})
		if err != nil {
			return dto.InitiateRefundResponse{
				RefundInitiated: false,
				Error: dto.AppResponseError{
					Message: utils.StrPtr("Failed to initiate gateway refund"),
				},
			}, fmt.Errorf("failed to initiate gateway refund: %v", err)
		}

		refundId = refundResponse.RefundOrderId
		refundStatus = refundResponse.RefundStatus
		refundGateway = refundResponse.RefundGateway
		refundMethod = "source"
		paymentID = transactionData.PaymentId

	} else {
		return dto.InitiateRefundResponse{
			RefundInitiated: false,
			Error: dto.AppResponseError{
				Message: utils.StrPtr("Something went wrong"),
			},
		}, nil
	}

	if request.Reason == nil {
		request.Reason = utils.StrPtr("Refund requested by user")
	}

	// Create refund entry
	refundEntry := dao.KiranaBazarOrderRefund{
		OrderID:        request.OrderID,
		RefundMethod:   refundMethod,
		PaymentID:      paymentID,
		SourceRefundID: refundId,
		Source:         refundMethod,
		Status:         refundStatus,
		Amount:         refundAmount,
		PaymentMethod:  refundMethod,
		Reason:         request.Reason,
		Cohort:         request.RefundSource,
	}

	metaData := map[string]interface{}{
		"refunded_products": request.RefundedProducts,
	}
	metaJSON, err := json.Marshal(metaData)
	if err != nil {
		logger.Error(ctx, "Failed to marshal refunded products to JSON", err)
	} else {
		j := datatypes.JSON(metaJSON)
		refundEntry.Meta = &j
	}

	_, err = s.repository.Create(&refundEntry)
	if err != nil {
		// Log the error but don't fail the refund
		fmt.Printf("Error creating refund entry: %v\n", err)
	}

	// Update payment
	updatePayment := dao.KiranaBazarOrderPayment{}
	updatePayment.RefundAmount = &refundAmount
	updatePayment.RefundID = &refundId

	wherePayment := dao.KiranaBazarOrderPayment{
		OrderID: utils.Int64Ptr(request.OrderID),
	}

	if _, _, err := s.repository.Update(wherePayment, updatePayment); err != nil {
		logger.Error(ctx, "Failed to update refund in orders table for: ", err, request.OrderID)
	}

	// Track event
	uid, err := s.getUserIdFromOrderID(request.OrderID)
	if err != nil {
		logger.Error(ctx, "Failed to get user id from order id", err, request.OrderID)
	}
	seller, err := s.getSellerFromOrderID(request.OrderID)
	if err != nil {
		logger.Error(ctx, "Failed to get seller from order id", err, request.OrderID)
	}

	refundMode := "original"
	if request.RefundMode.ID == "wallet" {
		refundMode = "wallet"
	}

	eventObject := map[string]interface{}{
		"distinct_id":   uid,
		"order_id":      request.OrderID,
		"order_value":   transactionData.Amount,
		"seller":        seller,
		"refund_mode":   refundMode,
		"refund_amount": refundAmount,
		"email":         request.UpdatedBy,
		"refund_id":     refundId,
		"comment":       request.Reason,
		"missing_items": missingItemsStr,
		"cohort":        request.RefundSource,
	}
	s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Amount Refunded", uid, eventObject),
	})

	err = s.addOrderTags(request.OrderID, []int{11}) // 10 - Any refund has been made after delivery
	if err != nil {
		logger.Error(ctx, "Failed to add order tags", err, request.OrderID)
	}

	// Send WhatsApp message
	templateID := "t5_upi_id_missing_item"
	if request.RefundMode.ID == "source" {
		templateID = "missing_item_refund_t4"
	}

	err = whatsapp.SendGenericWhatsAppMessage(templateID, phoneNumber, []string{
		fmt.Sprintf("%d", request.OrderID),
		missingItemsStr,
		fmt.Sprintf("%.2f", refundAmount),
	}, []string{})

	return dto.InitiateRefundResponse{
		RefundInitiated: true,
		RefundOrderId:   refundId,
		RefundAmount:    refundAmount,
		RefundStatus:    refundStatus,
		RefundGateway:   refundGateway,
	}, nil
}

func (s *Service) refundAPPInitiatedAdvancePayment(ctx context.Context, request dto.RefundPaymentApiRequest, paymentId string) (dto.RefundPaymentApiResponse, error) {
	transactionData, err := GetPaymentTransactionFromKCOrderId(ctx, request.OrderID, s.repository)
	if err != nil {
		// no gateway payment transaction found for this order
		// this is a COD order
		logger.Error(ctx, "Failed to get the payment transaction", err, request.OrderID)
	}

	if transactionData.GatewayStatus == payments.StatusRefunded {
		// already refunded
		return dto.RefundPaymentApiResponse{}, payments.ErrAlreadyRefunded
	}

	if transactionData.GatewayStatus != payments.StatusCompleted {
		return dto.RefundPaymentApiResponse{}, payments.ErrPaymentNotCompleted
	}

	if request.Amount == -1 {
		request.Amount = transactionData.PaidAmount
	}

	if request.Amount > transactionData.PaidAmount {
		return dto.RefundPaymentApiResponse{}, payments.ErrInvalidAmount
	}
	//
	//if paymentId != transactionData.PaymentID

	refundResponse, err := s.Payments.RefundPayment(ctx, dto.RefundPaymentRequest{
		KCOrderId:      request.OrderID,
		PaymentOrderId: transactionData.GatewayTransactionID,
		RefundAmount:   int(request.Amount * 100),
		PaymentId:      transactionData.PaymentID,
		InstantPayment: request.InstantPayment,
	}, "")
	if err != nil {
		logger.Error(ctx, "Failed to refund the payment", err, request.OrderID)
		return dto.RefundPaymentApiResponse{}, err
	}

	// update the payment transaction in the database
	sqlUpdateData := dao.PaymentGatewayTransaction{
		GatewayStatus: payments.StatusRefunded,
		KcStatus:      payments.StatusRefunded,
	}
	sqlWhereData := dao.PaymentGatewayTransaction{
		GatewayTransactionID: transactionData.GatewayTransactionID,
		KcOrderID:            transactionData.KcOrderID,
	}

	var rowsEffected int64
	if _, rowsEffected, err = s.repository.Update(sqlWhereData, sqlUpdateData); err != nil {
		logger.Error(ctx, "Failed to update refund in orders table for: ", err, request.OrderID)
		return dto.RefundPaymentApiResponse{}, err
	}

	if rowsEffected != 1 {
		logger.Error(ctx, "Failed to update the payment status")
		return dto.RefundPaymentApiResponse{}, errors.New("failed to update the payment status")
	}

	return dto.RefundPaymentApiResponse{
		RefundInitiated: true,
		RefundOrderId:   refundResponse.ID,
		RefundAmount:    float64(refundResponse.Amount) / 100,
		RefundStatus:    refundResponse.Status,
	}, nil
}

func (s *Service) refundQRInitiatedAdvancePayment(ctx context.Context, request dto.RefundPaymentApiRequest, paymentId string) (dto.RefundPaymentApiResponse, error) {
	refundResponse, err := s.Payments.RefundPayment(ctx, dto.RefundPaymentRequest{
		KCOrderId:      request.OrderID,
		RefundAmount:   int(request.Amount * 100),
		PaymentId:      paymentId,
		InstantPayment: request.InstantPayment,
	}, "")

	if err != nil {
		logger.Error(ctx, "Failed to refund the QR payment", err, request.OrderID)
		return dto.RefundPaymentApiResponse{}, err
	}

	return dto.RefundPaymentApiResponse{
		RefundInitiated: true,
		RefundOrderId:   refundResponse.ID,
		RefundAmount:    float64(refundResponse.Amount) / 100,
		RefundStatus:    refundResponse.Status,
	}, nil
}

func (s *Service) RefundAdvancePayment(ctx context.Context, request dto.RefundPaymentApiRequest) (dto.RefundPaymentApiResponse, error) {
	if request.Amount != -1 && request.Amount <= 0 {
		return dto.RefundPaymentApiResponse{}, payments.ErrInvalidAmount
	}

	orderPaymentData, err := GetOrderPaymentDataOrderId(ctx, request.OrderID, s.repository)
	if err != nil {
		return dto.RefundPaymentApiResponse{}, fmt.Errorf("failed to get order payment data: %v", err)
	}

	if orderPaymentData.PaidAmount == nil {
		return dto.RefundPaymentApiResponse{}, payments.ErrNoAdvancePayment
	}

	if request.Amount == -1 {
		request.Amount = *orderPaymentData.PaidAmount
	}

	if orderPaymentData.PaymentId == nil { // TODO: if payment is late then this will always be nil
		return dto.RefundPaymentApiResponse{}, payments.ErrNoAdvancePayment
	}

	if orderPaymentData.PaidAmount == nil || *orderPaymentData.PaidAmount < 0 {
		return dto.RefundPaymentApiResponse{}, payments.ErrNoAdvancePayment
	}

	if request.Amount > *orderPaymentData.PaidAmount {
		return dto.RefundPaymentApiResponse{}, payments.ErrInvalidAmount
	}

	if orderPaymentData.RefundAmount != nil && *orderPaymentData.PaidAmount-*orderPaymentData.RefundAmount-request.Amount < 0 {
		return dto.RefundPaymentApiResponse{}, payments.ErrAlreadyRefunded
	}

	orderStatus, err := s.GetOrderStatusFromDB(context.Background(), &dto.OrderStatusRequest{
		OrderID: request.OrderID,
	})
	if err != nil {
		return dto.RefundPaymentApiResponse{}, fmt.Errorf("failed to get order status: %v", err)
	}

	if !(request.Force != nil && *request.Force) && (!(orderStatus.ShipmentStatus == shipmentstatus.RTO_DELIVERED ||
		orderStatus.DisplayStatus == constants.CANCELLED)) {
		return dto.RefundPaymentApiResponse{}, fmt.Errorf("order Status %s is not elegible for refund", orderStatus.DisplayStatus)
	}

	var response dto.RefundPaymentApiResponse

	if orderPaymentData.Source == "Gateway" {
		response, err = s.refundAPPInitiatedAdvancePayment(ctx, request, *orderPaymentData.PaymentId)
		if err != nil {
			response, err = s.refundQRInitiatedAdvancePayment(ctx, request, *orderPaymentData.PaymentId)
		}
	} else {
		response, err = s.refundQRInitiatedAdvancePayment(ctx, request, *orderPaymentData.PaymentId)
	}

	if err != nil {
		return dto.RefundPaymentApiResponse{}, err
	}

	if response.RefundInitiated && response.RefundAmount > 0 {
		updatePayment := dao.KiranaBazarOrderPayment{}
		updatePayment.RefundAmount = &request.Amount
		updatePayment.RefundID = &response.RefundOrderId

		wherePayment := dao.KiranaBazarOrderPayment{
			OrderID: utils.Int64Ptr(request.OrderID),
		}

		if _, rowsAffected, err := s.repository.Update(wherePayment, updatePayment); err != nil {
			return dto.RefundPaymentApiResponse{}, fmt.Errorf("failed to update order payment: %v", err)
		} else if rowsAffected != 1 {
			return dto.RefundPaymentApiResponse{}, fmt.Errorf("failed to update order payment, rows affected: %d", rowsAffected)
		}

		// Create entry in kiranabazar_order_refunds table
		refundEntry := dao.KiranaBazarOrderRefund{
			OrderID:        request.OrderID,
			RefundMethod:   "original",
			PaymentID:      orderPaymentData.PaymentId,
			SourceRefundID: response.RefundOrderId,
			Source:         orderPaymentData.Source,
			Status:         response.RefundStatus,
			Amount:         response.RefundAmount,
			PaymentMethod:  *orderPaymentData.PaymentMethod,
			Reason:         utils.StrPtr("Advance payment refund"),
		}

		_, err = s.repository.Create(&refundEntry)
		if err != nil {
			// Log the error but don't fail the refund
			fmt.Printf("Error creating refund entry: %v\n", err)
		}

		uid, err := s.getUserIdFromOrderID(request.OrderID)
		if err != nil {
			logger.Error(ctx, "Failed to get user id from order id", err, request.OrderID)
		}
		seller, err := s.getSellerFromOrderID(request.OrderID)
		if err != nil {
			logger.Error(ctx, "Failed to get seller from order id", err, request.OrderID)
		}
		eventObject := map[string]interface{}{
			"distinct_id":   uid,
			"order_id":      request.OrderID,
			"order_value":   *orderPaymentData.Amount,
			"seller":        seller,
			"refund_mode":   "original",
			"refund_amount": response.RefundAmount,
			"refund_id":     response.RefundOrderId,
			"reason":        request.Reason,
		}

		if !(request.Reason != nil && *request.Reason == "Refund requested by user") {
			s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
				s.Mixpanel.NewEvent("Order Amount Refunded", uid, eventObject),
			})
		}

		err = s.addOrderTags(request.OrderID, []int{10}) // 10 - Advance Refunded
		if err != nil {
			logger.Error(ctx, "Failed to add order tags", err, request.OrderID)
		}

		if request.Reason != nil {
			templateID := ""
			if *request.Reason == shipmentstatus.RTO_DELIVERED {
				templateID = "rto_refund_initiated"
				_, err = s.ProcessCreditNote(context.Background(), &dto.CreditNoteRequest{
					CreditNote: &dto.CreditNote{
						OrderID:        fmt.Sprintf("%d", request.OrderID),
						CreditNoteType: kcFinances.CREDIT_NOTE_TYPE.FULL_RETURN,
						//PaymentID:      orderPaymentData.PaymentId,
					},
				})
				if err != nil {
					logger.Error(ctx, "Failed to process credit note for RTO refund", err, request.OrderID)
				}
			} else if *request.Reason == constants.CANCELLED {
				templateID = "cancelled_order_refund_initiated"
			}

			if templateID != "" {
				phoneNumber, err := s.getPhoneFromOrderID(request.OrderID)
				if err != nil {
					return response, nil
				}

				sellerName, err := brands.GetNameMappingBySeller(seller)

				if err != nil {
					return response, nil
				}

				err = whatsapp.SendGenericWhatsAppMessage(templateID, phoneNumber, []string{
					fmt.Sprintf("%d", request.OrderID),
					sellerName,
				}, []string{})
			}
		}

		return response, nil
	}
	return dto.RefundPaymentApiResponse{}, fmt.Errorf("failed to initiate refund")
}
