package service

import (
	"context"
	"encoding/base64"
	"fmt"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
)

func (s *Service) OnSubscribeHandler(ctx context.Context, req *dto.OnSubscribeRequest) (*dto.OnSubscribeResponse, error) {
	fmt.Println("received challenge is", req.Challenge, req.SubscriberID)
	decryptedMessage, err := s.decryptChallenge(ctx, req.Challenge)
	if err != nil {
		logger.Error(ctx, "Decrypt challenge failed: %s", err)
		return nil, err
	}
	response := &dto.OnSubscribeResponse{
		Answer: decryptedMessage,
	}
	fmt.Println("decrypted message is ", decryptedMessage)
	return response, nil
}

func (s *Service) decryptChallenge(ctx context.Context, message string) (string, error) {
	privateKey, err := s.KeyClient.ServiceEncryptionPrivateKey(ctx)
	if err != nil {
		return "", err
	}
	decryptedMsg, err := utils.DecryptMessage(message, string(privateKey), "MCowBQYDK2VuAyEAduMuZgmtpjdCuxv+Nc49K0cB6tL/Dj3HZetvVN7ZekM=")
	if err != nil {
		return "", err
	}
	return decryptedMsg, nil

}

func (s *Service) SiteVerificationHandler(ctx context.Context) (error, string) {

	signingKeyset, err := s.KeyClient.ServiceSigningPrivateKeyset(ctx)
	if err != nil {
		logger.Error(ctx, "Failed to fetch signing private key: %s", err)
		return err, ""
	}

	signedRequestID, err := utils.Sign([]byte("-uni-"), signingKeyset)
	if err != nil {
		logger.Error(ctx, "Failed to sign request ID: %s", err)
		return err, ""
	}

	signedRequestIDB64 := base64.StdEncoding.EncodeToString(signedRequestID)
	return nil, signedRequestIDB64
}
