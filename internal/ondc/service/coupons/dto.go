package coupons

import (
	"encoding/json"
)

// GetCouponsByBrandAndUserRequest represents the request for getting coupons by brand and user
type GetCouponsByBrandAndUserRequest struct {
	Brand  string `json:"brand" validate:"required"`
	UserID string `json:"user_id" validate:"required"`
}

// GetCouponsByBrandCartAndUserRequest represents the request for getting coupons by brand, cart and user
type GetCouponsByBrandCartAndUserRequest struct {
	Brand  string `json:"brand" validate:"required"`
	UserID string `json:"user_id" validate:"required"`
	Cart   Cart   `json:"cart" validate:"required"`
}

// ValidateCouponRequest represents the request for validating a coupon
type ValidateCouponRequest struct {
	Code   string `json:"code" validate:"required"`
	Brand  string `json:"brand" validate:"required"`
	UserID string `json:"user_id" validate:"required"`
	Cart   *Cart  `json:"cart,omitempty"`
}

// RedeemCouponRequest represents the request for redeeming a coupon
type RedeemCouponRequest struct {
	Code           string  `json:"code" validate:"required"`
	UserID         string  `json:"user_id" validate:"required"`
	OrderID        string  `json:"order_id" validate:"required"`
	DiscountAmount float64 `json:"discount_amount" validate:"required"`
}

// Cart represents a user's shopping cart
type Cart struct {
	Items       []CartItem `json:"items" validate:"required"`
	TotalAmount float64    `json:"total_amount" validate:"required"`
}

// CartItem represents an item in the shopping cart
type CartItem struct {
	ProductID   string   `json:"product_id" validate:"required"`
	Quantity    int      `json:"quantity" validate:"required"`
	Price       float64  `json:"price" validate:"required"`
	BrandID     string   `json:"brand_id" validate:"required"`
	CategoryIDs []string `json:"category_ids,omitempty"`
}

// Response DTOs

// CouponResponse represents a coupon in API responses
type CouponResponse struct {
	ID              int64            `json:"id"`
	Code            string           `json:"code"`
	Name            string           `json:"name"`
	Description     string           `json:"description,omitempty"`
	IsActive        bool             `json:"is_active"`
	IsInternal      bool             `json:"is_internal"`
	MaxUsageCount   *int64           `json:"max_usage_count,omitempty"`
	CurrentUsage    int64            `json:"current_usage"`
	MaxUsagePerUser *int64           `json:"max_usage_per_user,omitempty"`
	MinOrderAmount  float64          `json:"min_order_amount"`
	MaxOrderAmount  *float64         `json:"max_order_amount,omitempty"`
	Source          string           `json:"source"`
	CouponType      string           `json:"coupon_type"`
	DiscountType    string           `json:"discount_type"`
	DiscountReason  string           `json:"discount_reason"`
	DiscountMethod  string           `json:"discount_method"`
	DiscountValue   float64          `json:"discount_value"`
	CanClub         bool             `json:"can_club"`
	Priority        int              `json:"priority"`
	Terms           json.RawMessage  `json:"terms,omitempty"`
	StartTimestamp  *int64           `json:"start_timestamp,omitempty"`
	EndTimestamp    *int64           `json:"end_timestamp,omitempty"`
	ParentCode      string           `json:"parent_code,omitempty"`
	Brands          []string         `json:"brands,omitempty"`
	ChildCoupons    []CouponResponse `json:"child_coupons,omitempty"`
	Rules           []RuleResponse   `json:"rules,omitempty"`
}

// RuleResponse represents a coupon rule in API responses
type RuleResponse struct {
	RuleType string          `json:"rule_type"`
	Value    json.RawMessage `json:"value"`
}

// GetCouponsResponse represents the response for get coupons endpoints
type GetCouponsResponse struct {
	Coupons []CouponResponse `json:"coupons"`
}

// ValidateCouponResponse represents the response for validating a coupon
type ValidateCouponResponse struct {
	IsValid        bool                   `json:"is_valid"`
	Message        string                 `json:"message,omitempty"`
	DiscountAmount float64                `json:"discount_amount,omitempty"`
	DiscountType   string                 `json:"discount_type,omitempty"`
	DiscountData   map[string]interface{} `json:"discount_data,omitempty"`
	Coupon         *Coupon                `json:"coupon,omitempty"`
}

// RedeemCouponResponse represents the response for redeeming a coupon
type RedeemCouponResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
}

// Add this new struct to represent the next applicable coupon
type NextApplicableCoupon struct {
	Coupon            *Coupon `json:"coupon"`
	AmountNeeded      float64 `json:"amount_needed"`
	PotentialDiscount float64 `json:"potential_discount"`
	Message           string  `json:"message"`
	CartValue         float64 `json:"cart_value"`
}
