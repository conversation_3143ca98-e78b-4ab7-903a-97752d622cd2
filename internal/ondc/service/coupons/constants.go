package coupons

import "regexp"

type couponProvider struct {
	PLATFORM          string
	SELLER            string
	PLATFORM_CASHBACK string
	PAYMENT_DISCOUNT  string
}

var Provider = couponProvider{
	PLATFORM:          "PLATFORM",          // discount given by platform
	SELLER:            "SELLER",            // discount given by seller
	PLATFORM_CASHBACK: "PLATFORM_CASHBACK", // cashback/discount given by platform regarding loyalty
	PAYMENT_DISCOUNT:  "PAYMENT_DISCOUNT",  // discount given by platform on payment amount
}

var PATTERNS = map[string]*regexp.Regexp{
	"eq":  regexp.MustCompile(`^==\s*(-?\d+)$`),
	"neq": regexp.MustCompile(`^!=\s*(-?\d+)$`),
	"gt":  regexp.MustCompile(`^>\s*(-?\d+)$`),
	"gte": regexp.MustCompile(`^>=\s*(-?\d+)$`),
	"lt":  regexp.MustCompile(`^<\s*(-?\d+)$`),
	"lte": regexp.MustCompile(`^<=\s*(-?\d+)$`),
}
