package coupons

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/infrastructure/logging"
	"kc/internal/ondc/service/cart"
	userdetails "kc/internal/ondc/service/userDetails"
	"time"
)

var logger = logging.New("coupons")

// GetCouponsByBrandAndUser retrieves all valid coupons for a brand and user
func (s *Repository) GetCouponsByBrandAndUser(ctx context.Context, seller, userID string, userDetails *userdetails.AsyncResult) ([]*Coupon, error) {
	coupons, err := s.GetCouponsByBrand(ctx, seller, true)
	if err != nil {
		return nil, fmt.Errorf("error getting coupons from repository: %w", err)
	}

	//Filter coupons by user eligibility
	var result []*Coupon
	for _, coupon := range coupons {
		if s.IsUserEligible(ctx, coupon, userID, userDetails, seller) && s.isCouponValid(coupon) {
			result = append(result, coupon)
		}
	}
	return result, nil
}

// GetCouponsByBrandCartAndUser retrieves valid coupons for a brand, cart, and user
func (s *Repository) GetCouponsByBrandCartAndUser(ctx context.Context, seller, userID string, userDetails *userdetails.AsyncResult) ([]*Coupon, error) {
	// Get all applicable coupons for the brand and user
	coupons, err := s.GetCouponsByBrandAndUser(ctx, seller, userID, userDetails)
	if err != nil {
		return nil, err
	}

	// Filter by cart eligibility
	cart, err := cart.Get(ctx, userID, seller)
	if err != nil {
		return nil, fmt.Errorf("error getting cart from repository: %w", err)
	}
	for _, coupon := range coupons {
		coupon.IsApplicable = s.isCouponApplicable(ctx, coupon, cart)
	}

	return coupons, nil
}

// calculatePotentialDiscount calculates what the discount would be if the cart value reached the threshold
func (s *Repository) GetNextApplicableCouponByBrandCartAndUser(ctx context.Context, seller, userID string, userDetails *userdetails.AsyncResult) (*NextApplicableCoupon, error) {
	// Get all applicable coupons for the brand and user
	coupons, err := s.GetCouponsByBrandAndUser(ctx, seller, userID, userDetails)
	if err != nil {
		return nil, err
	}

	// Filter by cart eligibility
	cart, err := cart.Get(ctx, userID, seller)
	if err != nil {
		return nil, fmt.Errorf("error getting cart from repository: %w", err)
	}

	cartValue, err := cart.CartValue()
	if err != nil {
		logger.Error(ctx, "error getting cart value", err)
	}

	nextEligibleCoupons := []*Coupon{}
	for _, couponData := range coupons {
		if couponData.CouponType != CouponTypeBackend {
			continue
		}

		coupon, err := s.ValidateCoupon(ctx, couponData.Code, seller, userID, userDetails, false)
		if err != nil {
			logger.Error(ctx, "error getting coupon: %w", err)
			continue
		}
		if !coupon.IsValid {
			continue
		}
		isApplicable := false
		if couponData.MinOrderAmount.Valid && cartValue < couponData.MinOrderAmount.Float64 {
			isApplicable = true
		}
		if couponData.MaxOrderAmount.Valid && cartValue > couponData.MaxOrderAmount.Float64 {
			isApplicable = false
		}
		if isApplicable {
			couponData.IsApplicable = true
			nextEligibleCoupons = append(nextEligibleCoupons, couponData)
		}
	}

	// get the just next applicable coupon
	var minThreshold float64 = -1
	nextApplicableCoupon := NextApplicableCoupon{
		CartValue: cartValue,
	}
	for _, coupon := range nextEligibleCoupons {
		if coupon.MinOrderAmount.Valid && coupon.MinOrderAmount.Float64 > 0 {
			amountNeeded := coupon.MinOrderAmount.Float64 - cartValue
			if minThreshold == -1 || amountNeeded < minThreshold {
				minThreshold = amountNeeded
				// nextOfferMessage := s.calculatePotentialDiscount(coupon, cartValue)
				nextApplicableCoupon.Coupon = coupon
				nextApplicableCoupon.AmountNeeded = amountNeeded
			}
		}
	}

	return &nextApplicableCoupon, nil
}

// ValidateCoupon validates a coupon for a specific user, brand, and optionally a cart
func (s *Repository) ValidateCoupon(ctx context.Context, code string, brand string, userID string, userDetails *userdetails.AsyncResult, checkCartEligibility bool) (*ValidateCouponResponse, error) {
	// Get from database (with caching handled by repository)
	if code == "" {
		return &ValidateCouponResponse{
			IsValid: false,
			Message: "Coupon code is required",
		}, nil
	}

	coupon, err := s.GetCouponByCode(ctx, code, true)
	if err != nil {
		return nil, fmt.Errorf("error getting coupon from repository: %w", err)
	}

	if coupon == nil {
		return &ValidateCouponResponse{
			IsValid: false,
			Message: "Coupon not found",
		}, nil
	}

	// Check if coupon is for the brand
	if !s.isCouponForBrand(coupon, brand) {
		return &ValidateCouponResponse{
			IsValid: false,
			Message: "Coupon not valid for this brand",
		}, nil
	}

	// Check if coupon is valid
	if !s.isCouponValid(coupon) {
		return &ValidateCouponResponse{
			IsValid: false,
			Message: "Coupon is not valid at this time",
		}, nil
	}

	// Check if user is eligible
	if !s.IsUserEligible(ctx, coupon, userID, userDetails, brand) {
		return &ValidateCouponResponse{
			IsValid: false,
			Message: "User is not eligible for this coupon",
		}, nil
	}

	// Evaluate coupon rules
	if !s.EvaluateCouponRulesObject(ctx, coupon, userID, brand, true) {
		return &ValidateCouponResponse{
			IsValid: false,
			Message: "Coupon rules does not meet the requirements",
		}, nil
	}

	if checkCartEligibility {
		return s.calculateCoupon(ctx, code, brand, userID, coupon, checkCartEligibility)
	}

	return &ValidateCouponResponse{
		IsValid:      true,
		Message:      "Coupon is valid",
		DiscountData: map[string]interface{}{},
		DiscountType: "",
		Coupon:       coupon,
	}, nil
}

func (s *Repository) calculateCoupon(ctx context.Context, code string, brand string, userID string, coupon *Coupon, checkCartEligibility bool) (*ValidateCouponResponse, error) {
	var err error
	if coupon == nil {
		coupon, err = s.GetCouponByCode(ctx, code, true)
		if err != nil {
			return nil, fmt.Errorf("error getting coupon from repository: %w", err)
		}
	}
	cart, err := cart.Get(ctx, userID, brand)
	if err != nil {
		return nil, fmt.Errorf("error getting cart from repository: %w", err)
	}
	// Check cart eligibility if provided
	if checkCartEligibility {
		if !s.isCouponApplicable(ctx, coupon, cart) {
			return &ValidateCouponResponse{
				IsValid: false,
				Message: fmt.Sprintf("Cart does not meet requirements."),
			}, nil
		}
	}

	discountData := s.calculateDiscount(ctx, coupon, cart)

	discountAmount, ok := discountData["amount"].(float64)
	if !ok {
		discountAmount = 0
	}

	DiscountType, ok := discountData["DiscountType"].(string)
	if !ok {
		DiscountType = ""
	}

	return &ValidateCouponResponse{
		IsValid:        true,
		Message:        "Coupon is valid",
		DiscountData:   discountData,
		DiscountType:   DiscountType,
		DiscountAmount: discountAmount,
		Coupon:         coupon,
	}, nil
}

func (s *Repository) CalculateBackendDiscount(ctx context.Context, brand string, userID string, activationCoupon *int64,
	appliedCoupon *string, userDetails *userdetails.AsyncResult) ([]*ValidateCouponResponse, error) {
	couponsData, err := s.GetCouponsByBrandCartAndUser(ctx, brand, userID, userDetails)
	if err != nil {
		return nil, fmt.Errorf("error getting coupon: %w", err)
	}

	var canClubCoupons []*ValidateCouponResponse
	var cannotClubCoupons []*ValidateCouponResponse
	var cannotClubChoosenCoupons *ValidateCouponResponse
	var activationCouponProcessed bool = false
	var activationCouponData *Coupon
	checkCartEligibility := true
	for _, couponData := range couponsData {
		if couponData.CouponType != CouponTypeBackend {
			continue
		}
		if couponData.Code == *appliedCoupon {
			continue
		}
		if couponData.ID == *activationCoupon {
			activationCouponProcessed = true
		}
		coupon, err := s.ValidateCoupon(ctx, couponData.Code, brand, userID, userDetails, checkCartEligibility)
		if err != nil {
			logger.Error(ctx, "error getting coupon: %w", err)
			continue
		}
		if coupon.IsValid == false {
			continue
		}
		if coupon.Coupon.CanClub || couponData.ID == *activationCoupon {
			canClubCoupons = append(canClubCoupons, coupon)
		} else {
			cannotClubCoupons = append(cannotClubCoupons, coupon)
		}
	}

	if !activationCouponProcessed {
		activationCouponData, err = s.GetCouponByID(ctx, *activationCoupon)
		if err == nil {
			activationCoupon, err := s.ValidateCoupon(ctx, activationCouponData.Code, brand, userID, userDetails, checkCartEligibility)
			if err != nil {
				logger.Error(ctx, "error getting coupon: %w", err)
			} else {
				if activationCoupon.IsValid == true {
					canClubCoupons = append(canClubCoupons, activationCoupon)
				}
			}
		}
	}

	if len(cannotClubCoupons) > 0 {
		// choose coupons with maximum discount
		var maxDiscount float64 = 0
		for _, coupon := range cannotClubCoupons {
			if coupon.DiscountAmount >= maxDiscount {
				maxDiscount = coupon.DiscountAmount
				cannotClubChoosenCoupons = coupon
			}
		}
	}

	canClubCoupons = append(canClubCoupons, cannotClubChoosenCoupons)

	childCoupons := []*ValidateCouponResponse{}
	for _, coupon := range canClubCoupons {
		if coupon != nil && coupon.Coupon != nil && coupon.Coupon.ChildCoupons != nil {
			for _, childCoupon := range coupon.Coupon.ChildCoupons {
				childCouponValidated, err := s.calculateCoupon(ctx, childCoupon.Code, brand, userID, childCoupon, checkCartEligibility)
				if err != nil {
					logger.Error(ctx, "error getting coupon: %w", err)
					continue
				}
				childCoupons = append(childCoupons, childCouponValidated)
			}
		}
	}

	canClubCoupons = append(canClubCoupons, childCoupons...)

	return canClubCoupons, nil
}

// Helper methods
// isCouponValid checks if the coupon is active and within its valid date range
func (s *Repository) isCouponValid(coupon *Coupon) bool {
	if !coupon.IsActive {
		return false
	}
	if coupon.MaxUsageCount.Valid && coupon.MaxUsageCount.Int64 > 0 && coupon.CurrentUsage >= coupon.MaxUsageCount.Int64 {
		return false
	}
	if coupon.StartTimestamp > time.Now().Unix() || coupon.EndTimestamp < time.Now().Unix() {
		return false
	}
	if coupon.IsInternal {
		return false
	}
	return true
}

func (s *Repository) isCouponForBrand(coupon *Coupon, brand string) bool {
	if includes(coupon.Brands, "ALL") {
		return true
	}
	for _, b := range coupon.Brands {
		if b == brand {
			return true
		}
	}
	return false
}

func (s *Repository) IsUserEligible(ctx context.Context, coupon *Coupon, userID string, userDetails *userdetails.AsyncResult, seller string) bool {
	//Check if coupon has usage limits per user
	if coupon.MaxUsagePerUser.Valid && coupon.MaxUsagePerUser.Int64 > 0 {
		usage, err := s.GetUserCouponUsage(ctx, coupon.ID, userID)
		if err != nil {
			// Log the error and assume not eligible
			fmt.Printf("error checking user coupon usage: %v\n", err)
			return false
		}
		if int64(usage) >= coupon.MaxUsagePerUser.Int64 {
			return false
		}
	}

	userCohortNames := make([]string, 0)
	var userGeography *userdetails.UserGeoData
	if userDetails != nil && userDetails.Data != nil {
		if userDetails.Data.UserDynamicDetails != nil {
			userCohortNames = userDetails.Data.UserDynamicDetails.UserCohortNames
		}
		if userDetails.Data.UserGeography != nil {
			userGeography = userDetails.Data.UserGeography
		}
	}
	userCohortNames = append(userCohortNames, userdetails.GetUserDerivedCohorts(userID, &[]string{seller}, userGeography)...)

	if includes(coupon.Cohorts, "USER_LEVEL_COUPON") {
		// check from user_coupons table if user is eligible for this coupon
		existing, _ := s.CheckUserCouponAssignment(ctx, userID, coupon.ID)
		// User must have an active assignment for this coupon
		if existing {
			return true
		}
	}

	if len(coupon.Cohorts) > 0 {
		for _, cohort := range coupon.Cohorts {
			if userCohortNames != nil && includes(userCohortNames, cohort) {
				return true
			}
			if cohort == "ALL" || cohort == "all" {
				return true
			}
			// add user cohort check after fetching cohorts,
			//user geography can also be cohort so compared
		}
		return false
	}

	return true
}

// isCartEligible checks if the cart is eligible for the coupon
func (s *Repository) isCouponApplicable(ctx context.Context, coupon *Coupon, cart *cart.KiranaBazarCart) bool {
	// Check min order amount
	cartValue, err := cart.CartValue()
	if err != nil {
		logger.Error(ctx, "error getting cart value", err)
		return false
	}

	if coupon.MinOrderAmount.Valid && cartValue < coupon.MinOrderAmount.Float64 {
		return false
	}
	if coupon.MaxOrderAmount.Valid && cartValue > coupon.MaxOrderAmount.Float64 {
		return false
	}

	return true
}

// calculateDiscount calculates the discount amount for a coupon and cart
func (s *Repository) calculateDiscount(ctx context.Context, coupon *Coupon, cart *cart.KiranaBazarCart) map[string]interface{} {
	var valueToBeConsideredForDiscount float64 = 0
	var err error

	switch coupon.DiscountReason {
	case DiscountReasonOrderTotal:
		valueToBeConsideredForDiscount, err = cart.CartValue()
		if err != nil {
			logger.Error(ctx, "error getting cart value", err)
			valueToBeConsideredForDiscount = 0
		}
	}

	switch coupon.DiscountType {
	case DiscountTypeAmount:
		if coupon.DiscountMethod != DiscountMethodCash {
			logger.Error(ctx, "invalid discount method for amount coupon", coupon.DiscountMethod, coupon.ID)
			return map[string]interface{}{
				"amount": 0,
				"type":   coupon.DiscountMethod,
			}
		}
		return map[string]interface{}{
			"amount": coupon.DiscountValue,
			"type":   coupon.DiscountMethod,
		}

	case DiscountTypePercentage:
		discount := valueToBeConsideredForDiscount * (coupon.DiscountValue / 100)

		for _, rule := range coupon.Rules {
			if rule.RuleType == "percentage" {
				var percentageRule PercentageRule
				if err := json.Unmarshal(rule.Value, &percentageRule); err == nil {
					if percentageRule.MaxDiscount != nil && *percentageRule.MaxDiscount > 0 {
						discount = min(discount, *percentageRule.MaxDiscount)
					}
				}
			}
		}
		if coupon.DiscountMethod != DiscountMethodCash {
			logger.Error(ctx, "invalid discount method for amount coupon", coupon.DiscountMethod, coupon.ID)
			return map[string]interface{}{
				"amount": 0,
				"type":   coupon.DiscountMethod,
			}
		}
		return map[string]interface{}{
			"amount": discount,
			"type":   coupon.DiscountMethod,
		}

	case DiscountTypeShipping:
		return map[string]interface{}{
			"type": coupon.DiscountMethod,
		}

	case DiscountTypeFreebie:
		// Calculate based on the rules
		for _, rule := range coupon.Rules {
			switch rule.RuleType {
			case "freebie":
				var freebieRule FreebieRule
				if err := json.Unmarshal(rule.Value, &freebieRule); err == nil {
					return map[string]interface{}{
						"product_id": freebieRule.ProductID,
						"quantity":   freebieRule.Quantity,
						"value":      freebieRule.Value,
						"type":       coupon.DiscountMethod,
					}
				}
			case "free_item":
				var freeItemRule FreeItemRule
				if err := json.Unmarshal(rule.Value, &freeItemRule); err == nil {
					return map[string]interface{}{
						"Name":     freeItemRule.Name,
						"quantity": freeItemRule.Quantity,
						"value":    freeItemRule.Value,
						"type":     coupon.DiscountMethod,
					}
				}
			}

		}
	}
	return map[string]interface{}{}
}
