package coupons

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/cart"
	ordervalue "kc/internal/ondc/service/orderBill/orderValue"
	"strings"
	"time"

	"gorm.io/datatypes"
)

type Source string
type CouponType string
type DiscountType string
type DiscountReason string
type DiscountMethod string

const (
	// Source types -- no check based on this on code
	SourceKC    Source = "kc"
	SourceBrand Source = "brand"

	// Coupon types -- will be used to filter out coupons to be shown on app
	CouponTypeApp     CouponType = "app"
	CouponTypeBackend CouponType = "backend"
	CouponTypeAgent   CouponType = "agent"

	// Discount types -- amount, percentage, freebie, are handled as of now
	DiscountTypeAmount     DiscountType = "amount"
	DiscountTypePercentage DiscountType = "percentage"
	DiscountTypeFreebie    DiscountType = "freebie"
	DiscountTypeShipping   DiscountType = "shipping"
	//DiscountTypeTiered     DiscountType = "tiered"
	//DiscountTypeBundle     DiscountType = "bundle"

	// Discount reasons -- what part of the order is being discounted/considered -- order_total is handled as of now
	DiscountReasonOrderTotal DiscountReason = "order_total"
	//DiscountReasonSpecificProducts   DiscountReason = "specific_products"
	//DiscountReasonSpecificCategories DiscountReason = "specific_categories"
	//DiscountReasonPartBundle         DiscountReason = "part_bundle"

	// Discount methods -- what user will get as discount -- cash, freebie are handled as of now
	DiscountMethodCash DiscountMethod = "cash"
	//DiscountMethodCashback DiscountMethod = "cashback"
	//DiscountMethodPoints   DiscountMethod = "points"
	//DiscountMethodPunji    DiscountMethod = "punji"
	DiscountMethodFreebie  DiscountMethod = "freebie"
	DiscountMethodFreeItem DiscountMethod = "free_item"
	DiscountMethodShipping DiscountMethod = "shipping"
)

const (
	TableCoupons       = "kiranabazar_coupons"
	TableCouponBrands  = "kiranabazar_coupon_brands"
	TableCouponCohorts = "kiranabazar_coupon_cohorts"
	TableCouponRules   = "kiranabazar_coupons_rule"
	TableCouponUsages  = "kiranabazar_coupon_usages"
	TableUserCoupons   = "kiranabazar_coupon_users"
)

// Coupon represents the main coupon entity from the database
type CouponDao struct {
	ID              int64           `db:"id" json:"id"`
	Code            string          `db:"code" json:"code"`
	Name            string          `db:"name" json:"name"`
	Description     sql.NullString  `db:"description" json:"description"`
	IsActive        bool            `db:"is_active" json:"is_active"`
	IsInternal      bool            `db:"is_internal" json:"is_internal"`
	MaxUsageCount   sql.NullInt64   `db:"max_usage_count" json:"max_usage_count"`
	CurrentUsage    int64           `db:"current_usage" json:"current_usage"`
	MaxUsagePerUser sql.NullInt64   `db:"max_usage_per_user" json:"max_usage_per_user"`
	MinOrderAmount  sql.NullFloat64 `db:"min_order_amount" json:"min_order_amount"`
	MaxOrderAmount  sql.NullFloat64 `db:"max_order_amount" json:"max_order_amount"`
	Source          Source          `db:"source" json:"source"`
	CouponType      CouponType      `db:"coupon_type" json:"coupon_type"`
	DiscountType    DiscountType    `db:"discount_type" json:"discount_type"`
	DiscountReason  DiscountReason  `db:"discount_reason" json:"discount_reason"`
	DiscountMethod  DiscountMethod  `db:"discount_method" json:"discount_method"`
	DiscountValue   float64         `db:"discount_value" json:"discount_value"`
	CanClub         bool            `db:"can_club" json:"can_club"`
	Priority        int             `db:"priority" json:"priority"`
	CreatedBy       string          `db:"created_by" json:"created_by"`
	CreatedAt       time.Time       `db:"created_at" json:"created_at"`
	UpdatedAt       time.Time       `db:"updated_at" json:"updated_at"`
	Meta            json.RawMessage `db:"meta" json:"meta,omitempty"`
	Terms           json.RawMessage `db:"terms" json:"terms,omitempty"`
	StartTimestamp  int64           `db:"start_timestamp" json:"start_timestamp"`
	EndTimestamp    int64           `db:"end_timestamp" json:"end_timestamp"`
	ParentCode      string          `db:"parent_code" json:"parent_code"`
	Key             string          `db:"key" json:"key"`
	Hidden          bool            `db:"hidden" json:"hidden"`
}

type Coupon struct {
	ID              int64           `json:"id"`
	Code            string          `json:"code"`
	Name            string          `json:"name"`
	Description     sql.NullString  `json:"description"`
	IsActive        bool            `json:"is_active"`
	IsInternal      bool            `json:"is_internal"`
	MaxUsageCount   sql.NullInt64   `json:"max_usage_count"`
	CurrentUsage    int64           `json:"current_usage"`
	MaxUsagePerUser sql.NullInt64   `json:"max_usage_per_user"`
	MinOrderAmount  sql.NullFloat64 `json:"min_order_amount"`
	MaxOrderAmount  sql.NullFloat64 `json:"max_order_amount"`
	Source          Source          `json:"source"`
	CouponType      CouponType      `json:"coupon_type"`
	DiscountType    DiscountType    `json:"discount_type"`
	DiscountReason  DiscountReason  `json:"discount_reason"`
	DiscountMethod  DiscountMethod  `json:"discount_method"`
	DiscountValue   float64         `json:"discount_value"`
	CanClub         bool            `json:"can_club"`
	Priority        int             `json:"priority"`
	CreatedBy       string          `json:"created_by"`
	CreatedAt       time.Time       `json:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at"`
	Meta            json.RawMessage `json:"meta,omitempty"`
	Terms           json.RawMessage `json:"terms,omitempty"`
	StartTimestamp  int64           `json:"start_timestamp"`
	EndTimestamp    int64           `json:"end_timestamp"`
	ParentCode      string          `json:"parent_code"`
	Key             string          `json:"key"`
	Hidden          bool            `json:"hidden"`

	//Additional fields
	Brands       []string      `json:"brands,omitempty"`
	Cohorts      []string      `json:"cohorts,omitempty"`
	Rules        []*CouponRule `json:"rules,omitempty"`
	ChildCoupons []*Coupon     `json:"child_coupons,omitempty"`

	IsApplicable bool `json:"is_applicable"`
}

// CouponBrand represents the brand association for a coupon
type CouponBrand struct {
	ID       int64  `db:"id" json:"id"`
	CouponID int64  `db:"coupon_id" json:"coupon_id"`
	Brand    string `db:"brand" json:"brand"`
	IsActive bool   `db:"is_active" json:"is_active"`
}

// CouponCohort represents the cohort association for a coupon
type CouponCohort struct {
	ID             int64  `db:"id" json:"id"`
	CouponID       int64  `db:"coupon_id" json:"coupon_id"`
	Cohort         string `db:"cohort" json:"cohort"`
	IsActive       bool   `db:"is_active" json:"is_active"`
	StartTimestamp int64  `db:"start_timestamp" json:"start_timestamp"`
	EndTimestamp   int64  `db:"end_timestamp" json:"end_timestamp"`
	Type           string `db:"type" json:"type"`
}

type OfferProgressBar struct {
	Styles map[string]interface{} `json:"styles"`
}

type OfferMessageData struct {
	TextTemplate    string                 `json:"text_template"`
	HighlightedText []string               `json:"highlighted_text"`
	Styles          map[string]interface{} `json:"styles"`
	HighlightStyle  map[string]interface{} `json:"highlight_style"`
	IconType        string                 `json:"icon_type"`
	ProgressBar     *OfferProgressBar      `json:"progress_bar,omitempty"`
	RightIconUrl    *string                `json:"right_icon_url,omitempty"`
	LeftIconUrl     *string                `json:"left_icon_url,omitempty"`
}

type CouponMeta struct {
	OfferMessageData *OfferMessageData `json:"offer_message_data,omitempty"`
	PopupData        *PopupData        `json:"popup_data,omitempty"`
}

type OrderNumberConditions struct {
	Operator                *string  `json:"operator,omitempty"`
	ConfirmedOrderCondition *string  `json:"confirmed_order_condition,omitempty"`
	Seller                  []string `json:"seller,omitempty"` // Optional, list of sellers to apply the condition to
}

type BonusSkusConditions struct {
	EligibleSkus []EligibleBonusSkus `json:"eligible_skus,omitempty"`
}

type StepConfig struct {
	StepQuantity     int  `json:"step_quantity"`                // Number of items to buy to get the bonus
	BonusPerStep     int  `json:"bonus_per_step"`               // Number of bonus items received per step
	MaxBonusQuantity *int `json:"max_bonus_quantity,omitempty"` // Maximum number of bonus items that can be received
}

type TierConfig struct {
	MinQuantity   int  `json:"min_quantity"`           // Minimum quantity to qualify for this tier
	MaxQuantity   *int `json:"max_quantity,omitempty"` // Optional, maximum quantity for this tier
	BonusQuantity int  `json:"bonus_quantity"`         // Number of bonus items received at this tier
}

type EligibleBonusSkus struct {
	ProductID      string       `json:"product_id"`
	MinQuantity    int32        `json:"min_quantity"`
	BonusProductID string       `json:"bonus_product_id"`
	BonusQuantity  *int         `json:"bonus_quantity,omitempty"`
	Type           string       `json:"type,omitempty"`        // e.g., "step", "tier"
	StepConfig     *StepConfig  `json:"step_config,omitempty"` // Optional, used for step-based bonus calculations
	Tiers          []TierConfig `json:"tiers,omitempty"`       // Optional, used for tier-based bonus calculations
}

type CouponRuleValue struct {
	Value                 *float64               `json:"value,omitempty"`
	Quantity              *int                   `json:"quantity,omitempty"`
	ProductId             *string                `json:"product_id,omitempty"`
	MaxDiscount           *float64               `json:"max_discount,omitempty"`
	Name                  *string                `json:"name,omitempty"`
	OrderNumberConditions *OrderNumberConditions `json:"order_number_conditions,omitempty"`
	BonusSkusConditions   *BonusSkusConditions   `json:"bonus_skus_conditions,omitempty"`
}

type PopupData struct {
	Heading            string   `json:"heading"`
	SubHeading         string   `json:"sub_heading"`
	ImageUrl           string   `json:"image_url"`
	DiscountPercentage *float64 `json:"discount_percentage,omitempty"`
}

// CouponRule represents a specific rule for a coupon
type CouponRule struct {
	ID       int64          `db:"id" json:"id"`
	CouponID int64          `db:"coupon_id" json:"coupon_id"`
	RuleType string         `db:"rule_type" json:"rule_type"`
	Value    datatypes.JSON `db:"value" json:"value"`
}

// CouponUsage represents a usage record for a coupon
type CouponUsage struct {
	ID       int64  `db:"id" json:"id"`
	CouponID int64  `db:"coupon_id" json:"coupon_id"`
	UserID   string `db:"user_id" json:"user_id"`
	OrderID  string `db:"order_id" json:"order_id"`
	//UsedAt         time.Time `db:"used_at" json:"used_at"`
	DiscountAmount float64 `db:"discount_amount" json:"discount_amount"`
}

// UserCoupon represents a user-specific coupon assignment
type UserCoupon struct {
	ID         int64     `db:"id" json:"id"`
	CouponID   int64     `db:"coupon_id" json:"coupon_id"`
	UserID     string    `db:"user_id" json:"user_id"`
	AssignedAt time.Time `db:"assigned_at" json:"assigned_at"`
	ExpiresAt  time.Time `db:"expires_at" json:"expires_at"`
	IsActive   bool      `db:"is_active" json:"is_active"`
	Campaign   string    `db:"campaign" json:"campaign"`
	CreatedBy  string    `db:"created_by" json:"created_by"`
}

// Rule Type Definitions
// These structs represent the JSON structure for different rule types

// FreebieRule represents a rule for a freebie product
type FreebieRule struct {
	ProductID string  `json:"product_id"`
	Quantity  int     `json:"quantity"`
	Value     float64 `json:"value"`
}

// FreeItemRule represents a rule for a free item not in catalog
type FreeItemRule struct {
	Name     string  `json:"name"`
	Quantity int     `json:"quantity"`
	Value    float64 `json:"value"`
}

// PercentageRule represents additional rules for percentage discounts
type PercentageRule struct {
	MaxDiscount *float64 `json:"max_discount,omitempty"`
}

// ProductListRule represents a list of products eligible for the coupon
type ProductListRule struct {
	ProductIDs []string `json:"product_ids"`
}

// CategoryListRule represents a list of categories eligible for the coupon
type CategoryListRule struct {
	CategoryIDs []string `json:"category_ids"`
}

// BuyXGetYRule represents a rule for tiered "buy X get Y" promotions
type BuyXGetYRule struct {
	BuyQuantity   int      `json:"buy_quantity"`
	GetQuantity   int      `json:"get_quantity"`
	ProductIDs    []string `json:"product_ids,omitempty"`
	GetProductIDs []string `json:"get_product_ids,omitempty"`
	Discount      float64  `json:"discount_percentage"`
}

type GetApplicableCouponsResponse struct {
	Meta  dto.Meta             `json:"meta"`
	Data  dto.GetCouponsData   `json:"data"`
	Error dto.AppResponseError `json:"error,omitempty"`
}

type GetCouponsBottomSheetResponse struct {
	Meta  dto.Meta              `json:"meta"`
	Data  CouponBottomSheetData `json:"data"`
	Error dto.AppResponseError  `json:"error,omitempty"`
}

type CouponBottomSheetData struct {
	Title       string                        `json:"title"`
	Duration    *int64                        `json:"duration,omitempty"`
	CouponsData CouponData                    `json:"coupons_data"`
	CouponsName []string                      `json:"coupons_name"`
	Benefits    []dto.LoyaltyRewardsComponent `json:"benifits"`
	Count       int                           `json:"count"`
	ErrorText   string                        `json:"error_text"`
}

type CouponData struct {
	Coupons []dto.Coupon         `json:"coupons"`
	Header  *dto.ComponentFooter `json:"header,omitempty"`
}

type GetCouponsDataxx struct {
	Coupons   []*Coupon `json:"coupons"`
	Count     int       `json:"count"`
	ErrorText string    `json:"error_text"`
}

func (kcc *CouponDao) TableName() string    { return TableCoupons }
func (kcb *CouponBrand) TableName() string  { return TableCouponBrands }
func (kcc *CouponCohort) TableName() string { return TableCouponCohorts }
func (kcr *CouponRule) TableName() string   { return TableCouponRules }
func (kcu *CouponUsage) TableName() string  { return TableCouponUsages }
func (kuc *UserCoupon) TableName() string   { return TableUserCoupons }

func (c *Coupon) GetCouponMeta() (*CouponMeta, error) {
	couponMeta := CouponMeta{}
	if c.Meta != nil {
		if err := json.Unmarshal(c.Meta, &couponMeta); err != nil {
			return nil, err
		}
		return &couponMeta, nil
	}
	return nil, nil
}

func (c *CouponRule) GetCouponRules() (*CouponRuleValue, error) {
	couponRuleValue := CouponRuleValue{}
	if c.Value != nil {
		if err := json.Unmarshal(c.Value, &couponRuleValue); err != nil {
			return nil, err
		}
		return &couponRuleValue, nil
	}
	return nil, nil
}

func (crv *CouponRuleValue) EvaluateOrderNumberConditions(sellerLevelCount ordervalue.SellerOrderCount) (bool, error) {
	if crv.OrderNumberConditions == nil {
		return true, nil // No conditions means rule passes
	}

	conditions := crv.OrderNumberConditions

	// Collect all condition results
	var results []bool
	var hasConditions bool

	// Evaluate ConfirmedOrderCondition
	if conditions.ConfirmedOrderCondition != nil {
		hasConditions = true
		confirmedOrderCount := 0
		if len(conditions.Seller) > 0 {
			// If specific sellers are provided, check only those sellers
			for _, seller := range conditions.Seller {
				sellerKey := fmt.Sprintf("%s::confirmed", seller)
				if cnt, exists := sellerLevelCount[sellerKey]; exists {
					confirmedOrderCount += cnt
				}
			}
		} else {
			// If no specific sellers are provided, check all sellers
			for key, cnt := range sellerLevelCount {
				// if key contains ::confirmed
				if strings.Contains(key, "::confirmed") {
					confirmedOrderCount += cnt
				}
			}
		}

		result, err := EvaluateCondition(*conditions.ConfirmedOrderCondition, confirmedOrderCount)
		if err != nil {
			return false, fmt.Errorf("error evaluating ConfirmedOrderCondition: %w", err)
		}
		results = append(results, result)
	}

	// If no conditions were provided, rule passes
	if !hasConditions {
		return true, nil
	}

	// Determine operator (default is AND)
	operator := "OR"
	if conditions.Operator != nil {
		operator = strings.ToUpper(strings.TrimSpace(*conditions.Operator))
	}

	// Apply logical operator
	switch operator {
	case "AND":
		// All conditions must be true
		for _, result := range results {
			if !result {
				return false, nil
			}
		}
		return true, nil

	case "OR":
		// At least one condition must be true
		for _, result := range results {
			if result {
				return true, nil
			}
		}
		return false, nil

	default:
		return false, fmt.Errorf("invalid operator: %s. Supported operators: AND, OR", operator)
	}
}

func (crv *CouponRuleValue) EvaluateCartBonusSkuCondtions(cart *cart.KiranaBazarCart) (bool, error) {
	if cart == nil || crv.BonusSkusConditions == nil {
		return true, nil // No conditions means rule passes
	}

	conditonSkus := crv.BonusSkusConditions.EligibleSkus

	conditionSkusQuantityMap := make(map[string]int32)
	// Create a map of product IDs to their required quantities
	for _, sku := range conditonSkus {
		conditionSkusQuantityMap[sku.ProductID] = sku.MinQuantity
	}

	cartProducts, err := cart.GetProducts()
	if err != nil {
		return false, fmt.Errorf("error getting products from cart: %w", err)
	}
	// Check if the product exists in the cart
	for _, item := range cartProducts {
		if minQuantity, exists := conditionSkusQuantityMap[item.ID]; exists {
			// If the product is found, check if the quantity in the cart meets the requirement
			if item.Quantity >= minQuantity {
				return true, nil // Condition met
			}
		}
	}

	return false, nil // Condition not met
}

type UserCouponUsageCount struct {
	UserID           string `json:"user_id"`
	CouponID         int64  `json:"coupon_id"`
	CouponUsageCount int64  `json:"coupon_usage_count"`
}
