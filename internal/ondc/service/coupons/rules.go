package coupons

import (
	"context"
	cartService "kc/internal/ondc/service/cart"
	ordervalue "kc/internal/ondc/service/orderBill/orderValue"
)

// evaluateCouponRules determines whether a coupon is applicable to a user based on all defined rules.
// This function acts as the main entry point for coupon rule validation and ensures that ALL rules
// associated with a coupon must pass for the coupon to be considered valid for the user.
//
// Parameters:
//   - ctx: Context for the request, used for cancellation and timeouts
//   - coupon: The coupon object containing all rules to be evaluated. If nil, the function returns true.
//   - userOrderStats: User's order statistics (e.g., total orders, confirmed orders) used for rule evaluation
//
// Returns:
//   - bool: true if all rules pass or if no rules are defined, false if any rule fails
//
// Rule Evaluation Logic:
//   - If coupon is nil or has no rules, returns true (no restrictions)
//   - ALL rules must pass for the coupon to be applicable (AND logic)
//   - If ANY single rule fails, the entire evaluation returns false
//   - Rules are evaluated in the order they appear in the coupon.Rules slice
//
// Example Usage:
//
//	isApplicable := s.evaluateCouponRules(ctx, coupon, &dao.UserOrderStats{
//	    UserID: "user123",
//	    ConfirmedOrder: 5,
//	})
//
// Common Rule Types:
//   - Order count rules (minimum/maximum order requirements)
//
// Note: This function delegates individual rule evaluation to evaluateSingleRule()
func (s *Repository) EvaluateCouponRulesObject(ctx context.Context, coupon *Coupon, userID, seller string, evaluateCart bool) bool {
	if coupon == nil || coupon.Rules == nil {
		return true
	}

	sellerLevelCount, err := ordervalue.GetSellerLevelOrderCount(userID)
	if err != nil {
		return false
	}

	var cart *cartService.KiranaBazarCart
	if evaluateCart {
		cart, err = cartService.Get(ctx, userID, seller)
		if err != nil {
			return false
		}
	}

	// Evaluate all rules for the coupon
	return s.evaluateAllRules(ctx, coupon.Rules, sellerLevelCount, cart)
}

func (s *Repository) evaluateAllRules(ctx context.Context, rules []*CouponRule, sellerLevelCount ordervalue.SellerOrderCount, cart *cartService.KiranaBazarCart) bool {
	if rules == nil {
		return true // No rules to evaluate, consider it valid
	}

	// All rules must pass for the coupon to be applicable
	for _, rule := range rules {
		if !s.evaluateSingleRule(rule, sellerLevelCount, cart) {
			return false // If any rule fails, the coupon is not applicable
		}
	}

	return true // All rules passed
}

// evaluateSingleRule evaluates a single coupon rule
func (s *Repository) evaluateSingleRule(rule *CouponRule, sellerLevelCount ordervalue.SellerOrderCount, cart *cartService.KiranaBazarCart) bool {
	// Get the rule value
	ruleValue, err := rule.GetCouponRules()
	if err != nil {
		return false
	}

	// If rule value is nil, skip this rule
	if ruleValue == nil {
		return true // or return false based on your business logic
	}

	switch rule.RuleType {
	case "order_count":
		result, err := ruleValue.EvaluateOrderNumberConditions(sellerLevelCount)
		if err != nil {
			return false // Rule evaluation failed
		}
		return result // Return true if the order count conditions are met, false otherwise
	case "bonus_sku":
		result, err := ruleValue.EvaluateCartBonusSkuCondtions(cart)
		if err != nil {
			return false // Rule evaluation failed
		}
		return result // Return true if the cart SKUs conditions are met, false otherwise
	}

	return true
}
