package coupons

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/cache"
	"kc/internal/ondc/external/slack"
	orderdata "kc/internal/ondc/service/orderData"
	"time"
)

// RedeemCouponAsyncWithCallback redeems a coupon asynchronously with a callback
func (s *Repository) RedeemCouponAsyncWithCb(ctx context.Context, userID, orderID string, callback func(*RedeemCouponResponse, error)) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Error(context.Background(), "panic in RedeemCouponAsyncWithCallback",
					"userID", userID,
					"orderID", orderID,
					"panic", r)
				if callback != nil {
					callback(nil, fmt.Errorf("panic occurred: %v", r))
				}
			}
		}()

		timeoutCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		response, err := s.<PERSON>eem<PERSON>oupon(timeoutCtx, userID, orderID)

		if callback != nil {
			callback(response, err)
		}
	}()
}

// RedeemCoupon redeems a coupon for a specific order
func (s *Repository) RedeemCoupon(ctx context.Context, userID, orderID string) (*RedeemCouponResponse, error) {

	orderDetails, err := orderdata.GetOrderDetails(s.db, orderID)
	if err != nil {
		return nil, fmt.Errorf("error getting order details: %w", err)
	}

	if orderDetails == nil {
		return nil, fmt.Errorf("order not found")
	}

	discountPricingData := orderDetails.BillBreakUp.DiscountPricing
	if discountPricingData == nil {
		return nil, nil
	}

	for _, discount := range discountPricingData {
		if discount.ID <= 0 {
			continue
		}
		// Record the usage
		usage := &CouponUsage{
			CouponID:       discount.ID,
			UserID:         userID,
			OrderID:        orderID,
			DiscountAmount: discount.TotalValue,
		}

		if err := s.RecordCouponUsage(ctx, usage); err != nil {
			return nil, fmt.Errorf("error recording coupon usage: %w", err)
		}
	}

	return &RedeemCouponResponse{
		Success: true,
		Message: "Coupon redeemed successfully",
	}, nil
}

// RecordCouponUsage records a usage of a coupon
func (s *Repository) RecordCouponUsage(ctx context.Context, usage *CouponUsage) error {
	// Begin a transaction
	tx, err := s.db.BeginTx(ctx)
	if err != nil {
		return fmt.Errorf("error beginning transaction: %v", err)
	}
	defer tx.Rollback()

	// Create usage record
	_, err = s.db.CreateTx(tx, usage)
	if err != nil {
		return fmt.Errorf("error recording coupon usage: %v", err)
	}

	// Update usage count
	query := fmt.Sprintf(`UPDATE kiranabazar_coupons SET current_usage = current_usage + 1 WHERE id = %d`,
		usage.CouponID)

	_, err = s.db.CustomQueryTx(tx, nil, query)
	if err != nil {
		return fmt.Errorf("error incrementing coupon usage: %v", err)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		slack.SendSlackDebugMessage(fmt.Sprintf("error committing transaction for coupon usage: %v", err))
		return fmt.Errorf("error committing transaction: %v", err)
	}

	coupon, err := s.GetCouponByID(ctx, usage.CouponID)
	if err != nil {
		logger.Error(ctx, "error getting coupon by ID", err, "couponID", usage.CouponID)
		return fmt.Errorf("error getting coupon by ID: %w", err)
	}

	// Invalidate related cache entries
	s.cache.Delete(BuildKey(CacheKeyCouponByCode, coupon.Code))
	s.cache.Delete(BuildKey(CacheKeyCouponByID, coupon.ID))
	// TODO: update instead of invalidating cache
	return nil
}

func (s *Repository) GetUserCouponUsage(ctx context.Context, couponID int64, userID string) (int, error) {
	userCouponUsageCount, err := s.GetUserCouponUsageCount(ctx, userID)
	if err != nil {
		logger.Error(ctx, "error getting user coupon usage count", err)
		return 0, fmt.Errorf("error getting user coupon usage count: %w", err)
	}
	if userCouponUsageCount == nil {
		return 0, nil
	}
	usage, ok := userCouponUsageCount[couponID]
	if !ok {
		return 0, nil
	}
	return int(usage.CouponUsageCount), nil
}

func (s *Repository) GetUserCouponUsageCount(ctx context.Context, userID string) (map[int64]UserCouponUsageCount, error) {
	query := fmt.Sprintf(`SELECT 
			user_id, 
			coupon_id, 
			COUNT(*) AS coupon_usage_count
		FROM 
			kiranabazar_coupon_usages kcu
		WHERE 
			user_id = '%s'
		GROUP BY 
			user_id, coupon_id`, userID)
	cacheKey := BuildKey(CacheKeyUserCouponUsage, userID)
	cacheResult, err := cache.GetInstance().GetCachedData(
		ctx,
		cacheKey,
		func() (interface{}, error) {
			data := make([]UserCouponUsageCount, 0)
			_, err := s.db.CustomQuery(&data, query)
			if err != nil {
				return nil, err
			}
			userCouponUsageData := make(map[int64]UserCouponUsageCount)
			for _, usage := range data {
				userCouponUsageData[usage.CouponID] = usage
			}
			return userCouponUsageData, nil
		},
		1*time.Hour,
		5*time.Hour,
	)
	if err != nil {
		return nil, err
	}

	jsonData, err := json.Marshal(cacheResult.Data)
	if err != nil {
		return nil, fmt.Errorf("error marshalling cache data: %w", err)
	}
	var userCouponUsageCountData map[int64]UserCouponUsageCount
	if err := json.Unmarshal(jsonData, &userCouponUsageCountData); err != nil {
		return nil, fmt.Errorf("error unmarshalling cache data: %w", err)
	}
	return userCouponUsageCountData, nil
}

func (s *Repository) InvalidateUserCouponUsageCache(ctx context.Context, userID string) error {
	cacheKey := BuildKey(CacheKeyUserCouponUsage, userID)
	if err := cache.GetInstance().Clear(ctx, cacheKey); err != nil {
		return fmt.Errorf("error deleting user coupon usage cache: %v", err)
	}
	return nil
}
