package coupons

import (
	"fmt"
	"strconv"
	"strings"
)

func includes(slice []string, str string) bool {
	for _, item := range slice {
		if item == str {
			return true
		}
	}
	return false
}

// EvaluateCondition is a generic function that evaluates a condition string against a count value
func EvaluateCondition(conditionStr string, countValue int) (bool, error) {
	// Trim whitespace and validate input
	conditionStr = strings.TrimSpace(conditionStr)
	if conditionStr == "" {
		return false, fmt.Errorf("condition cannot be empty")
	}

	// Check each pattern and evaluate
	for operator, pattern := range PATTERNS {
		if matches := pattern.FindStringSubmatch(conditionStr); matches != nil {
			threshold, err := strconv.Atoi(matches[1])
			if err != nil {
				return false, fmt.Errorf("invalid threshold value: %s", matches[1])
			}

			switch operator {
			case "eq":
				return countValue == threshold, nil
			case "neq":
				return countValue != threshold, nil
			case "gt":
				return countValue > threshold, nil
			case "gte":
				return countValue >= threshold, nil
			case "lt":
				return countValue < threshold, nil
			case "lte":
				return countValue <= threshold, nil
			}
		}
	}

	return false, fmt.Errorf("invalid condition format: %s. Supported formats: ==N, !=N, >N, >=N, <N, <=N", conditionStr)
}