package coupons

import (
	"fmt"
	"sync"
	"time"
)

// CacheItem represents an item in the cache with expiration
type CacheItem struct {
	Value      interface{}
	Expiration int64
}

// Expired checks if the cache item has expired
func (i *CacheItem) Expired() bool {
	if i.Expiration == 0 {
		return false
	}
	return time.Now().UnixNano() > i.Expiration
}

// MemoryCache implements an in-memory cache
type MemoryCache struct {
	items   map[string]CacheItem
	mu      sync.RWMutex
	ttl     time.Duration
	janitor *time.Ticker
}

// NewMemoryCache creates a new in-memory cache with the specified TTL
func NewMemoryCache(ttl time.Duration) *MemoryCache {
	cache := &MemoryCache{
		items:   make(map[string]CacheItem),
		ttl:     ttl,
		janitor: time.NewTicker(time.Minute),
	}

	// Start a goroutine to periodically clean up expired items
	go func() {
		for range cache.janitor.C {
			cache.DeleteExpired()
		}
	}()

	return cache
}

// Set adds an item to the cache with the specified key and TTL
func (c *MemoryCache) Set(key string, value interface{}, ttl time.Duration) {
	c.mu.Lock()
	defer c.mu.Unlock()

	var exp int64
	if ttl > 0 {
		exp = time.Now().Add(ttl).UnixNano()
	} else if c.ttl > 0 {
		exp = time.Now().Add(c.ttl).UnixNano()
	}

	c.items[key] = CacheItem{
		Value:      value,
		Expiration: exp,
	}
}

// Get retrieves an item from the cache by key
func (c *MemoryCache) Get(key string) (interface{}, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	item, found := c.items[key]
	if !found {
		return nil, false
	}

	if item.Expired() {
		return nil, false
	}

	return item.Value, true
}

// Delete removes an item from the cache by key
func (c *MemoryCache) Delete(key string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	delete(c.items, key)
}

// DeleteWithPrefix removes all items with a key prefix
func (c *MemoryCache) DeleteWithPrefix(prefix string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	for k := range c.items {
		if len(k) >= len(prefix) && k[:len(prefix)] == prefix {
			delete(c.items, k)
		}
	}
}

// DeleteExpired removes all expired items from the cache
func (c *MemoryCache) DeleteExpired() {
	c.mu.Lock()
	defer c.mu.Unlock()

	now := time.Now().UnixNano()
	for k, v := range c.items {
		if v.Expiration > 0 && now > v.Expiration {
			delete(c.items, k)
		}
	}
}

// BuildKey creates a cache key with a prefix and parameters
func BuildKey(prefix string, params ...interface{}) string {
	if len(params) == 0 {
		return prefix
	}
	return fmt.Sprintf("%s:%s", prefix, fmt.Sprint(params...))
}

// Close stops the janitor ticker
func (c *MemoryCache) Close() {
	if c.janitor != nil {
		c.janitor.Stop()
	}
}
