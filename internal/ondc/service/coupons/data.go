package coupons

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/brands"
	"strings"
	"sync"
	"time"

	"github.com/go-playground/validator/v10"
	"gorm.io/datatypes"
)

// CacheKeys for different cache entries
const (
	CacheKeyCouponByCode         = "coupon:code"
	CacheKeyCouponByID           = "coupon:id"
	CacheKeyCouponsByParentCode  = "coupons:parent"
	CacheKeyCouponsByBrand       = "coupons:brand"
	CacheKeyCouponBrands         = "coupon:brands"
	CacheKeyCouponRules          = "coupon:rules"
	CacheKeyCouponCohorts        = "coupon:cohorts"
	CacheKeyUserCouponUsage      = "usage:coupon:user"
	CacheKeyCouponNameMap        = "coupon:nameMap"
	CacheKeyUserCouponAssignment = "user:coupon:assignment"
)

// Repository handles database operations with caching
type Repository struct {
	db            *sqlRepo.Repository
	cache         *MemoryCache
	refreshTicker *time.Ticker
	stopRefresh   chan struct{}
	isLoading     sync.RWMutex // Prevents concurrent cache refreshes
}

// NewRepository creates a new repository with the provided database and cache
func NewRepository(db *sqlRepo.Repository, cache *MemoryCache) *Repository {
	repo := &Repository{
		db:          db,
		cache:       cache,
		stopRefresh: make(chan struct{}),
	}

	// Initialize cache on startup
	go func() {
		logger.Info(context.Background(), "Loading coupon data on startup...")
		repo.loadCouponDataSafe()
		logger.Info(context.Background(), "Coupon data loaded successfully")
	}()

	// Start background refresh ticker
	repo.startCacheRefreshTicker()

	return repo
}

// FetchOrCache is a generic function that tries to fetch data from cache first,
// then queries the database if not found, and caches the result
func (s *Repository) FetchOrCache(ctx context.Context, cacheKey string, queryFn func() (interface{}, error)) (interface{}, error) {
	// Try to get from cache first
	if data, found := s.cache.Get(cacheKey); found {
		return data, nil
	}

	// Not in cache, query the database
	data, err := queryFn()
	if err != nil {
		return nil, err
	}

	// Cache the result (if not nil)
	if data != nil {
		s.cache.Set(cacheKey, data, 0) // Use default TTL
	}

	return data, nil
}

// convertCouponDaoToCoupon converts a CouponDao to a Coupon
func convertCouponDaoToCoupon(couponDao CouponDao) *Coupon {
	return &Coupon{
		ID:              couponDao.ID,
		Code:            couponDao.Code,
		Name:            couponDao.Name,
		Key:             couponDao.Key,
		Description:     couponDao.Description,
		IsActive:        couponDao.IsActive,
		IsInternal:      couponDao.IsInternal,
		MaxUsageCount:   couponDao.MaxUsageCount,
		MaxUsagePerUser: couponDao.MaxUsagePerUser,
		CurrentUsage:    couponDao.CurrentUsage,
		MinOrderAmount:  couponDao.MinOrderAmount,
		MaxOrderAmount:  couponDao.MaxOrderAmount,
		Source:          couponDao.Source,
		CouponType:      couponDao.CouponType,
		DiscountType:    couponDao.DiscountType,
		DiscountReason:  couponDao.DiscountReason,
		DiscountMethod:  couponDao.DiscountMethod,
		DiscountValue:   couponDao.DiscountValue,
		CanClub:         couponDao.CanClub,
		Priority:        couponDao.Priority,
		Meta:            couponDao.Meta,
		Terms:           couponDao.Terms,
		StartTimestamp:  couponDao.StartTimestamp,
		EndTimestamp:    couponDao.EndTimestamp,
		ParentCode:      couponDao.ParentCode,
		Hidden:          couponDao.Hidden,
	}
}

func (s *Repository) loadCouponData() {
	// Load coupon data from database
	var couponsDao []CouponDao
	var coupons []*Coupon
	var couponsMap = make(map[int64]*Coupon)
	var couponCodeIDMap = make(map[string][]int64)
	var CouponBrandMap = make(map[int64][]CouponBrand)
	var BrandCouponMap = make(map[string][]*Coupon)

	_, err := s.db.Find(map[string]interface{}{
		"is_active": 1,
	}, &couponsDao)
	if err != nil {
		fmt.Println("error finding coupon by code", err)
	}
	for _, couponDao := range couponsDao {
		coupon := convertCouponDaoToCoupon(couponDao)
		coupons = append(coupons, coupon)
		couponsMap[coupon.ID] = coupon
		couponCodeIDMap[coupon.Code] = append(couponCodeIDMap[coupon.Code], coupon.ID)
	}

	// Load coupon brands from database
	var brands []CouponBrand
	query := fmt.Sprintf(`SELECT kcb.* FROM kiranaclubdb.kiranabazar_coupon_brands kcb join kiranaclubdb.kiranabazar_coupons c 
    				on kcb.coupon_id=c.id WHERE c.is_active = 1 and kcb.is_active = 1`)
	_, err = s.db.CustomQuery(&brands, query)
	if err != nil {
		fmt.Println("error finding coupon brands", err)
	}

	// Load coupon rules from database
	query = fmt.Sprintf(`SELECT cr.* FROM kiranaclubdb.kiranabazar_coupons_rule cr join kiranaclubdb.kiranabazar_coupons c 
    on cr.coupon_id = c.id WHERE c.is_active = 1`)
	var rules []*CouponRule
	_, err = s.db.CustomQuery(&rules, query)
	if err != nil {
		fmt.Println("error finding coupon rules", err)
	}

	// Load coupon cohorts from database
	var cohorts []CouponCohort
	query = fmt.Sprintf(`SELECT cc.* FROM kiranaclubdb.kiranabazar_coupon_cohorts cc join kiranaclubdb.kiranabazar_coupons c
	on cc.coupon_id = c.id WHERE c.is_active = 1`)
	_, err = s.db.CustomQuery(&cohorts, query)
	if err != nil {
		fmt.Println("error finding coupon cohorts", err)
	}

	for _, brandData := range brands {
		couponsMap[brandData.CouponID].Brands = append(couponsMap[brandData.CouponID].Brands, brandData.Brand)
		CouponBrandMap[brandData.CouponID] = append(CouponBrandMap[brandData.CouponID], brandData)
		BrandCouponMap[brandData.Brand] = append(BrandCouponMap[brandData.Brand], couponsMap[brandData.CouponID])
	}

	if coupons, ok := BrandCouponMap["ALL"]; ok && len(coupons) > 0 {
		for brand := range BrandCouponMap {
			BrandCouponMap[brand] = append(BrandCouponMap[brand], coupons...)
		}
	}

	for _, ruleData := range rules {
		couponsMap[ruleData.CouponID].Rules = append(couponsMap[ruleData.CouponID].Rules, ruleData)
	}

	for _, cohortData := range cohorts {
		couponsMap[cohortData.CouponID].Cohorts = append(couponsMap[cohortData.CouponID].Cohorts, cohortData.Cohort)
	}

	for _, coupon := range coupons {
		if coupon.Code == coupon.ParentCode && coupon.IsInternal == false {
			childCouponsIds := couponCodeIDMap[coupon.Code]
			if len(childCouponsIds) > 0 {
				var childCoupons []*Coupon
				for _, id := range childCouponsIds {
					childCoupons = append(childCoupons, couponsMap[id])
				}
				coupon.ChildCoupons = childCoupons
			}
		}
	}

	// Coupon by code
	for _, coupon := range coupons {
		cacheKey := BuildKey(CacheKeyCouponByCode, coupon.Code)
		s.cache.Set(cacheKey, coupon, 0)
	}

	// Coupon by ID
	for _, coupon := range coupons {
		cacheKey := BuildKey(CacheKeyCouponByID, coupon.ID)
		s.cache.Set(cacheKey, coupon, 0)
	}

	// all coupons by brand
	for brand, coupons := range BrandCouponMap {
		cacheKey := BuildKey(CacheKeyCouponsByBrand, brand)
		s.cache.Set(cacheKey, coupons, 0)
	}

	// Coupon brands from coupon id
	for _, coupon := range coupons {
		cacheKey := BuildKey(CacheKeyCouponBrands, coupon.ID)
		s.cache.Set(cacheKey, CouponBrandMap[coupon.ID], 0)
	}

	// coupon rules
	for _, coupon := range coupons {
		cacheKey := BuildKey(CacheKeyCouponRules, coupon.ID)
		s.cache.Set(cacheKey, coupon.Rules, 0)
	}

	// coupon cohorts
	for _, coupon := range coupons {
		cacheKey := BuildKey(CacheKeyCouponCohorts, coupon.ID)
		s.cache.Set(cacheKey, coupon.Cohorts, 0)

	}
}

func (s *Repository) GetCouponNameMap(ctx context.Context) map[string]*Coupon {
	cacheKey := BuildKey(CacheKeyCouponNameMap, "all")
	data, err := s.FetchOrCache(ctx, cacheKey, func() (interface{}, error) {
		allCoupons := make([]*Coupon, 0)
		for _, sd := range brands.GetAllBrands() {
			seller := sd
			coupons, err := s.GetCouponsByBrand(ctx, seller, false)
			if err != nil {
				logger.Error(ctx, "Error fetching coupons for seller %s: %v", seller, err)
				continue
			}
			if len(coupons) == 0 {
				logger.Info(ctx, "No coupons found for seller %s", seller)
				continue
			}
			allCoupons = append(allCoupons, coupons...)
		}

		allCouponsMap := make(map[string]*Coupon)
		for _, coupon := range allCoupons {
			if coupon == nil {
				continue
			}

			for _, b := range coupon.Brands {
				allCouponsMap[fmt.Sprintf("%s:%s", coupon.Name, b)] = coupon
			}

			if len(coupon.Brands) == 0 {
				allCouponsMap[coupon.Name] = coupon
			}
		}
		return allCouponsMap, nil
	})
	if err != nil || data == nil {
		return nil
	}

	couponMap, ok := data.(map[string]*Coupon)
	if !ok {
		return nil
	}
	return couponMap
}

// GetCouponByCode retrieves a coupon by its code
func (s *Repository) GetCouponByCode(ctx context.Context, code string, fetchActive bool) (*Coupon, error) {
	cacheKey := BuildKey(CacheKeyCouponByCode, code)

	data, err := s.FetchOrCache(ctx, cacheKey, func() (interface{}, error) {
		condition := map[string]interface{}{
			"code": code,
		}
		if fetchActive {
			condition["is_active"] = true
		}

		var couponsDao []CouponDao
		_, err := s.db.Find(condition, &couponsDao)
		if err != nil {
			return nil, fmt.Errorf("error finding coupon by code: %w", err)
		}

		if len(couponsDao) != 1 {
			return nil, fmt.Errorf("unexpected number of coupons found: %d", len(couponsDao))
		}

		coupon := convertCouponDaoToCoupon(couponsDao[0])

		// Get associated data
		if err := s.populateCouponData(ctx, coupon); err != nil {
			return nil, err
		}

		return coupon, nil
	})

	if err != nil {
		return nil, err
	}

	if data == nil {
		return nil, nil
	}

	coupon, ok := data.(*Coupon)
	if !ok {
		return nil, fmt.Errorf("invalid cache value type")
	}

	return coupon, nil
}

// GetCouponByID retrieves a coupon by its ID
func (s *Repository) GetCouponByID(ctx context.Context, id int64) (*Coupon, error) {
	cacheKey := BuildKey(CacheKeyCouponByID, id)

	data, err := s.FetchOrCache(ctx, cacheKey, func() (interface{}, error) {
		condition := map[string]interface{}{
			"id": id,
		}

		var couponsDao []CouponDao
		_, err := s.db.Find(condition, &couponsDao)
		if err != nil {
			return nil, fmt.Errorf("error finding coupon by id: %w", err)
		}

		if len(couponsDao) != 1 {
			return nil, fmt.Errorf("unexpected number of coupons found: %d", len(couponsDao))
		}

		coupon := convertCouponDaoToCoupon(couponsDao[0])

		// Get associated data
		if err := s.populateCouponData(ctx, coupon); err != nil {
			return nil, err
		}

		return coupon, nil
	})

	if err != nil {
		return nil, err
	}

	if data == nil {
		return nil, nil
	}

	coupon, ok := data.(*Coupon)
	if !ok {
		return nil, fmt.Errorf("invalid cache value type")
	}

	return coupon, nil
}

// GetCouponsByParentCode retrieves coupons by parent code
func (s *Repository) GetCouponsByParentCode(ctx context.Context, parentCode string) ([]*Coupon, error) {
	cacheKey := BuildKey(CacheKeyCouponsByParentCode, parentCode)

	data, err := s.FetchOrCache(ctx, cacheKey, func() (interface{}, error) {
		query := fmt.Sprintf(`
			SELECT * FROM kiranaclubdb.kiranabazar_coupons 
			WHERE parent_code = '%s' AND is_active = 1 AND is_internal=1
		`, parentCode)

		var couponsDao []CouponDao
		_, err := s.db.CustomQuery(&couponsDao, query)
		if err != nil {
			return nil, fmt.Errorf("error finding coupons by parent code: %w", err)
		}

		var coupons []*Coupon
		for _, couponDao := range couponsDao {
			coupon := convertCouponDaoToCoupon(couponDao)
			coupons = append(coupons, coupon)
		}

		// Get associated data for each coupon
		for _, coupon := range coupons {
			if err := s.populateCouponData(ctx, coupon); err != nil {
				return nil, err
			}
		}

		return coupons, nil
	})

	if err != nil {
		return nil, err
	}

	if data == nil {
		return nil, nil
	}

	coupons, ok := data.([]*Coupon)
	if !ok {
		return nil, fmt.Errorf("invalid cache value type in getCouponsByParentCode")
	}

	return coupons, nil
}

// GetCouponsByBrand retrieves coupons for a specific brand
func (s *Repository) GetCouponsByBrand(ctx context.Context, brand string, active bool) ([]*Coupon, error) {
	cacheKey := BuildKey(CacheKeyCouponsByBrand, brand)

	data, err := s.FetchOrCache(ctx, cacheKey, func() (interface{}, error) {
		query := ""
		if active {
			query = fmt.Sprintf(`
			SELECT c.* FROM kiranaclubdb.kiranabazar_coupons c
			JOIN kiranaclubdb.kiranabazar_coupon_brands cb ON c.id = cb.coupon_id
			WHERE (cb.brand = '%s' or cb.brand = 'ALL')
			AND cb.is_active = %t
			AND c.is_active = %t
			AND c.start_timestamp <= unix_timestamp() AND c.end_timestamp >= unix_timestamp() order by c.min_order_amount
		`, brand, active, active)
		} else {
			query = fmt.Sprintf(`
			SELECT c.* FROM kiranaclubdb.kiranabazar_coupons c
			JOIN kiranaclubdb.kiranabazar_coupon_brands cb ON c.id = cb.coupon_id
			WHERE (cb.brand = '%s' or cb.brand = 'ALL') order by c.min_order_amount
		`, brand)
		}

		var couponsDao []CouponDao
		_, err := s.db.CustomQuery(&couponsDao, query)
		if err != nil {
			return nil, fmt.Errorf("error finding coupons by brand: %w", err)
		}

		var coupons []*Coupon
		for _, couponDao := range couponsDao {
			coupon := convertCouponDaoToCoupon(couponDao)
			coupons = append(coupons, coupon)
		}

		for _, coupon := range coupons {
			if err := s.populateCouponData(ctx, coupon); err != nil {
				return nil, err
			}
		}

		return coupons, nil
	})

	if err != nil {
		return nil, err
	}

	if data == nil {
		return nil, nil
	}

	coupons, ok := data.([]*Coupon)
	if !ok {
		return nil, fmt.Errorf("invalid cache value type in getCouponsByBrand")
	}

	return coupons, nil
}

// GetCouponBrands retrieves brands associated with a coupon
func (s *Repository) GetCouponBrands(ctx context.Context, couponID int64) ([]CouponBrand, error) {
	cacheKey := BuildKey(CacheKeyCouponBrands, couponID)

	data, err := s.FetchOrCache(ctx, cacheKey, func() (interface{}, error) {
		condition := map[string]interface{}{
			"coupon_id": couponID,
			"is_active": true,
		}

		var brands []CouponBrand
		_, err := s.db.Find(condition, &brands)
		if err != nil {
			return nil, fmt.Errorf("error finding coupon brands: %w", err)
		}
		return brands, nil
	})

	if err != nil {
		return nil, err
	}

	if data == nil {
		return []CouponBrand{}, nil
	}

	brands, ok := data.([]CouponBrand)
	if !ok {
		return nil, fmt.Errorf("invalid cache value type in getCouponBrands")
	}

	return brands, nil
}

// GetCouponRules retrieves rules associated with a coupon
func (s *Repository) GetCouponRules(ctx context.Context, couponID int64) ([]*CouponRule, error) {
	fmt.Println("cacheKey", couponID)

	cacheKey := BuildKey(CacheKeyCouponRules, couponID)

	data, err := s.FetchOrCache(ctx, cacheKey, func() (interface{}, error) {
		query := fmt.Sprintf(`
			SELECT cr.* FROM kiranabazar_coupons_rule cr
			WHERE cr.coupon_id = %d`, couponID)

		var rules []*CouponRule
		_, err := s.db.CustomQuery(&rules, query)
		if err != nil {
			// no rule for this coupon
			return []*CouponRule{}, nil
		}

		return rules, nil
	})

	if err != nil {
		return nil, err
	}

	if data == nil {
		return []*CouponRule{}, nil
	}

	rules, ok := data.([]*CouponRule)
	if !ok {
		return nil, fmt.Errorf("invalid cache value type in getCouponRules")
	}

	return rules, nil
}

// GetCouponCohorts retrieves cohorts associated with a coupon
func (s *Repository) GetCouponCohorts(ctx context.Context, couponID int64) ([]string, error) {
	cacheKey := BuildKey(CacheKeyCouponCohorts, couponID)

	data, err := s.FetchOrCache(ctx, cacheKey, func() (interface{}, error) {
		condition := map[string]interface{}{
			"coupon_id": couponID,
			"is_active": 1,
		}

		var cohorts []CouponCohort
		_, err := s.db.Find(condition, &cohorts)
		if err != nil {
			return nil, fmt.Errorf("error finding coupon cohorts: %w", err)
		}

		var cohortNames []string
		for _, cohort := range cohorts {
			cohortNames = append(cohortNames, cohort.Cohort)
		}

		return cohortNames, nil
	})

	if err != nil {
		return nil, err
	}

	if data == nil {
		return []string{}, nil
	}

	cohorts, ok := data.([]string)
	if !ok {
		return nil, fmt.Errorf("invalid cache value type in getCouponCohorts")
	}

	return cohorts, nil
}

// populateCouponData populates associated data for a coupon
func (s *Repository) populateCouponData(ctx context.Context, coupon *Coupon) error {
	var wg sync.WaitGroup
	errChan := make(chan error, 4) // Buffer for up to 4 errors (one from each goroutine)

	// Get brands
	wg.Add(1)
	var brands []CouponBrand
	go func() {
		defer wg.Done()
		var err error
		brands, err = s.GetCouponBrands(ctx, coupon.ID)
		if err != nil {
			errChan <- fmt.Errorf("error getting coupon brands: %w", err)
		}
	}()

	// Get rules
	wg.Add(1)
	var rules []*CouponRule
	go func() {
		defer wg.Done()
		var err error
		rules, err = s.GetCouponRules(ctx, coupon.ID)
		if err != nil {
			errChan <- fmt.Errorf("error getting coupon rules: %w", err)
		}
	}()

	// Get cohorts
	wg.Add(1)
	var cohorts []string
	go func() {
		defer wg.Done()
		var err error
		cohorts, err = s.GetCouponCohorts(ctx, coupon.ID)
		if err != nil {
			errChan <- fmt.Errorf("error getting coupon cohorts: %w", err)
		}
	}()

	// Get child coupons if this is a parent coupon
	var childCoupons []*Coupon
	if coupon.Code == coupon.ParentCode && !coupon.IsInternal {
		wg.Add(1)
		go func() {
			defer wg.Done()
			var err error
			childCoupons, err = s.GetCouponsByParentCode(ctx, coupon.Code)
			if err != nil {
				errChan <- fmt.Errorf("error getting child coupons: %w", err)
			}
		}()
	}

	// Wait for all goroutines to complete
	go func() {
		wg.Wait()
		close(errChan)
	}()

	// Check for any errors
	for err := range errChan {
		return err // Return the first error encountered
	}

	// Process results
	coupon.Brands = make([]string, 0, len(brands))
	for _, brand := range brands {
		if brand.IsActive {
			coupon.Brands = append(coupon.Brands, brand.Brand)
		}
	}

	coupon.Rules = rules
	coupon.Cohorts = cohorts

	if coupon.Code == coupon.ParentCode && coupon.IsInternal == false {
		coupon.ChildCoupons = childCoupons
	}

	return nil
}

// invalidateCouponCache invalidates all cache entries related to a coupon
func (s *Repository) invalidateCouponCache(couponID int64) {
	coupon, err := s.GetCouponByID(context.Background(), couponID)
	if err != nil {
		fmt.Printf("Error getting coupon for cache invalidation: %v\n", err)
		return
	}

	if coupon == nil {
		return
	}

	// Delete the coupon by code
	s.cache.Delete(BuildKey(CacheKeyCouponByCode, coupon.Code))

	// Delete the coupon by ID
	s.cache.Delete(BuildKey(CacheKeyCouponByID, couponID))

	// Delete brands, rules, and cohorts
	s.cache.Delete(BuildKey(CacheKeyCouponBrands, couponID))
	s.cache.Delete(BuildKey(CacheKeyCouponRules, couponID))
	s.cache.Delete(BuildKey(CacheKeyCouponCohorts, couponID))

	// Delete parent/child relationships
	if coupon.ParentCode != "" {
		s.cache.Delete(BuildKey(CacheKeyCouponsByParentCode, coupon.ParentCode))
	}
	// Also delete coupons by this code as parent
	s.cache.Delete(BuildKey(CacheKeyCouponsByParentCode, coupon.Code))

	// Delete brand-related entries
	for _, brand := range coupon.Brands {
		s.cache.Delete(BuildKey(CacheKeyCouponsByBrand, brand))
	}
}

// UpdateCouponCache updates the coupon in cache instead of invalidating
func (s *Repository) UpdateCouponCache(ctx context.Context, coupon *Coupon) {
	// Update coupon by code
	s.cache.Set(BuildKey(CacheKeyCouponByCode, coupon.Code), coupon, 0)

	// Update coupon by ID
	s.cache.Set(BuildKey(CacheKeyCouponByID, coupon.ID), coupon, 0)
}

// AddCoupon inserts a new coupon using the dto request struct
func (s *Repository) AddCoupon(ctx context.Context, req *dto.CouponAddRequest) (uint64, error) {
	q := req.Data
	validate := validator.New()
	err := validate.Struct(req.Data)
	if err != nil {
		return 0, err
	}
	coupon := &CouponDao{
		Code:           q.Code,
		Name:           q.Name,
		Description:    sql.NullString{String: q.Description, Valid: q.Description != ""},
		IsActive:       true,
		IsInternal:     *q.IsInternal,
		CurrentUsage:   0,
		Priority:       0,
		CreatedBy:      *q.CreatedBy,
		Key:            q.Code,
		Source:         Source(*q.Source),
		CouponType:     CouponType(*q.CouponType),
		DiscountType:   DiscountType(*q.DiscountType),
		DiscountReason: DiscountReason(*q.DiscountReason),
		DiscountMethod: DiscountMethod(*q.DiscountMethod),
		StartTimestamp: *q.StartTimestamp,
		EndTimestamp:   *q.EndTimestamp,
	}

	if q.Hidden != nil {
		coupon.Hidden = *q.Hidden
	} else {
		coupon.Hidden = false
	}

	if q.CanClub != nil {
		coupon.CanClub = *q.CanClub
	} else {
		coupon.CanClub = false
	}

	var couponRule *[]CouponRule
	var discountValue float64
	if DiscountType(*q.DiscountType) == DiscountTypePercentage || DiscountType(*q.DiscountType) == DiscountTypeAmount {
		if q.DiscountValue == nil {
			return 0, fmt.Errorf("invalid value for discount type: %v", q.DiscountType)
		}
		discountValue = *q.DiscountValue
	} else {
		if q.Rules == nil {
			return 0, fmt.Errorf("rules are required for discount type: %v", q.DiscountType)
		}
		var rulesData interface{}
		rulesBytes, err := json.Marshal(q.Rules)
		if err != nil {
			return 0, fmt.Errorf("error marshaling rules: %w", err)
		}
		if err := json.Unmarshal(rulesBytes, &rulesData); err != nil {
			return 0, fmt.Errorf("error unmarshaling rules: %w", err)
		}
		value, exists := rulesData.(map[string]interface{})["value"]
		if !exists {
			return 0, fmt.Errorf("value field is required for discount type: %v", q.DiscountType)
		}
		if floatValue, ok := value.(float64); ok {
			discountValue = floatValue
		} else {
			return 0, fmt.Errorf("invalid value type for discount type: %v", q.DiscountType)
		}
		quantity, exists := rulesData.(map[string]interface{})["quantity"]
		if !exists {
			return 0, fmt.Errorf("quantity field is required for discount type: %v", q.DiscountType)
		}
		if DiscountMethod(*q.DiscountMethod) == DiscountMethodFreebie {
			productId, exists := rulesData.(map[string]interface{})["product_id"]
			if !exists {
				return 0, fmt.Errorf("product_id field is required for discount method: %v", q.DiscountMethod)
			}
			couponRule = &[]CouponRule{
				{
					CouponID: 0, // Will be set later
					RuleType: string(DiscountMethodFreebie),
					Value: func() datatypes.JSON {
						jsonValue, _ := json.Marshal(map[string]interface{}{
							"product_id": productId,
							"quantity":   quantity,
							"value":      value,
						})
						return datatypes.JSON(jsonValue)
					}(),
				},
			}
		}
		if DiscountMethod(*q.DiscountMethod) == DiscountMethodFreeItem {
			productName, exists := rulesData.(map[string]interface{})["product_name"]
			if !exists {
				return 0, fmt.Errorf("product_name field is required for discount method: %v", q.DiscountMethod)
			}
			couponRule = &[]CouponRule{
				{
					CouponID: 0, // Will be set later
					RuleType: string(DiscountMethodFreebie),
					Value: func() datatypes.JSON {
						jsonValue, _ := json.Marshal(map[string]interface{}{
							"product_name": productName,
							"quantity":     quantity,
							"value":        value,
						})
						return datatypes.JSON(jsonValue)
					}(),
				},
			}
		}
	}

	coupon.DiscountValue = discountValue

	termsBytes, err := json.Marshal(q.Terms)
	if err != nil {
		return 0, fmt.Errorf("error marshaling terms: %w", err)
	}
	coupon.Terms = json.RawMessage(termsBytes)

	// Handle nullable fields
	if q.MaxUsageCount != nil {
		coupon.MaxUsageCount = sql.NullInt64{Int64: int64(*q.MaxUsageCount), Valid: true}
	}
	if q.MaxUsagePerUser != nil {
		coupon.MaxUsagePerUser = sql.NullInt64{Int64: int64(*q.MaxUsagePerUser), Valid: true}
	}
	if q.MinOrderAmount != nil {
		coupon.MinOrderAmount = sql.NullFloat64{Float64: *q.MinOrderAmount, Valid: true}
	}
	if q.MaxOrderAmount != nil {
		coupon.MaxOrderAmount = sql.NullFloat64{Float64: *q.MaxOrderAmount, Valid: true}
	}

	// Handle JSON fields
	if q.Meta != nil {
		metaBytes, err := json.Marshal(q.Meta)
		if err != nil {
			return 0, fmt.Errorf("error marshaling meta: %w", err)
		}
		coupon.Meta = json.RawMessage(metaBytes)
	}

	tx, err := s.db.BeginTx(ctx)
	if err != nil {
		return 0, fmt.Errorf("error beginning transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	_, err = s.db.CreateTx(tx, coupon)
	if err != nil {
		tx.Rollback()
		return 0, err
	}

	var insertedCoupon CouponDao
	_, err = s.db.FindTx(tx, map[string]interface{}{"code": q.Code}, &insertedCoupon)
	if err != nil {
		tx.Rollback()
		return 0, fmt.Errorf("error fetching inserted coupon: %w", err)
	}
	couponID := uint64(insertedCoupon.ID)

	// Insert brands if provided
	if q.Brands != nil {
		for _, brand := range *q.Brands {
			newBrand := &CouponBrand{CouponID: int64(couponID), Brand: brand, IsActive: true}
			_, err := s.db.CreateTx(tx, newBrand)
			if err != nil {
				tx.Rollback()
				return 0, fmt.Errorf("error adding coupon brand: %w", err)
			}
		}
	}
	// Insert cohorts if provided
	if q.Cohorts != nil {
		for _, cohort := range *q.Cohorts {
			newCohort := &CouponCohort{CouponID: int64(couponID), Cohort: cohort, IsActive: true}
			_, err := s.db.CreateTx(tx, newCohort)
			if err != nil {
				tx.Rollback()
				return 0, fmt.Errorf("error adding coupon cohort: %w", err)
			}
		}
	}

	if couponRule != nil {
		(*couponRule)[0].CouponID = int64(couponID) // Set the coupon ID after creation
		_, err := s.db.CreateTx(tx, couponRule)
		if err != nil {
			tx.Rollback()
			return 0, fmt.Errorf("error adding coupon rule: %w", err)
		}
	}

	if err := tx.Commit().Error; err != nil {
		return 0, fmt.Errorf("error committing transaction: %v", err)
	}
	return couponID, nil
}

// EditCoupon updates an existing coupon using the dto request struct
func (s *Repository) EditCoupon(ctx context.Context, req *dto.CouponEditRequest) error {
	q := req.Data

	if q.ID == 0 {
		return fmt.Errorf("coupon ID is required for update")
	}

	updates := make(map[string]interface{})

	if q.Description != nil {
		updates["description"] = q.Description
	}

	if q.Name != nil {
		updates["name"] = q.Name
	}

	if q.MinOrderAmount != nil && *q.MinOrderAmount > 0 {
		updates["min_order_amount"] = *q.MinOrderAmount
	}

	if q.MaxOrderAmount != nil && *q.MaxOrderAmount > 0 {
		updates["max_order_amount"] = *q.MaxOrderAmount
	}

	if q.Hidden != nil {
		updates["hidden"] = q.Hidden
	}

	if q.IsActive != nil {
		updates["is_active"] = q.IsActive
	}

	if q.IsInternal != nil {
		updates["is_internal"] = q.IsInternal

	}

	if q.Terms != nil {
		termsBytes, err := json.Marshal(q.Terms)
		if err != nil {
			return fmt.Errorf("error marshaling terms: %w", err)
		}
		updates["terms"] = json.RawMessage(termsBytes)
	}
	if q.CouponType != nil {
		updates["coupon_type"] = CouponType(*q.CouponType)
	}
	if q.StartTimestamp != nil {
		updates["start_timestamp"] = *q.StartTimestamp
	}
	if q.EndTimestamp != nil {
		updates["end_timestamp"] = *q.EndTimestamp
	}

	tx, err := s.db.BeginTx(ctx)
	if err != nil {
		return fmt.Errorf("error beginning transaction: %w", err)
	}
	hasErr := false
	defer func() {
		if hasErr {
			tx.Rollback()
		}
	}()
	if len(updates) > 0 {
		fmt.Println("Updating coupon with ID:", q.ID, "updates:", updates)
		_, _, err = s.db.UpdateTx(tx, &CouponDao{ID: q.ID}, updates)
		if err != nil {
			hasErr = true
			return fmt.Errorf("error updating coupon: %w", err)
		}
	}

	if q.Brands != nil || q.Cohorts != nil {
		if q.Brands != nil {
			// Brand update logic
			var dbBrands []CouponBrand
			condition := map[string]interface{}{"coupon_id": q.ID}
			_, err = s.db.FindTx(tx, condition, &dbBrands)
			if err != nil {
				hasErr = true
				return fmt.Errorf("error fetching coupon brands from db: %w", err)
			}
			dbBrandMap := make(map[string]CouponBrand)
			for _, b := range dbBrands {
				dbBrandMap[b.Brand] = b
			}
			reqBrandSet := make(map[string]struct{})
			for _, b := range *q.Brands {
				reqBrandSet[b] = struct{}{}
			}
			for brand, dbBrand := range dbBrandMap {
				if _, ok := reqBrandSet[brand]; ok {
					if !dbBrand.IsActive {
						updateData := map[string]interface{}{"is_active": true}
						_, _, err := s.db.UpdateTx(tx, &dbBrand, updateData)
						if err != nil {
							hasErr = true
							return fmt.Errorf("error activating coupon brand: %w", err)
						}
					}
				}
			}
			for brand, dbBrand := range dbBrandMap {
				if _, ok := reqBrandSet[brand]; !ok && dbBrand.IsActive {
					updateData := map[string]interface{}{"is_active": false}
					_, _, err := s.db.UpdateTx(tx, &dbBrand, updateData)
					if err != nil {
						hasErr = true
						return fmt.Errorf("error deactivating coupon brand: %w", err)
					}
				}
			}
			for brand := range reqBrandSet {
				if _, ok := dbBrandMap[brand]; !ok {
					newBrand := &CouponBrand{CouponID: q.ID, Brand: brand, IsActive: true}
					_, err := s.db.CreateTx(tx, newBrand)
					if err != nil {
						hasErr = true
						return fmt.Errorf("error adding new coupon brand: %w", err)
					}
				}
			}
		}

		if q.Cohorts != nil {
			// Cohort update logic
			var dbCohorts []CouponCohort
			condition := map[string]interface{}{"coupon_id": q.ID}
			_, err = s.db.FindTx(tx, condition, &dbCohorts)
			if err != nil {
				hasErr = true
				return fmt.Errorf("error fetching coupon cohorts from db: %w", err)
			}
			fmt.Println("dbCohorts", dbCohorts)
			dbCohortMap := make(map[string]CouponCohort)
			for _, c := range dbCohorts {
				dbCohortMap[c.Cohort] = c
			}
			reqCohortSet := make(map[string]struct{})
			for _, c := range *q.Cohorts {
				reqCohortSet[c] = struct{}{}
			}
			for cohort, dbCohort := range dbCohortMap {
				if _, ok := reqCohortSet[cohort]; ok {
					if !dbCohort.IsActive {
						updateData := map[string]interface{}{"is_active": true}
						_, _, err := s.db.UpdateTx(tx, &dbCohort, updateData)
						if err != nil {
							hasErr = true
							return fmt.Errorf("error activating coupon cohort: %w", err)
						}
					}
				}
			}
			fmt.Println("dbCohorts", dbCohortMap)
			for cohort, dbCohort := range dbCohortMap {
				if _, ok := reqCohortSet[cohort]; !ok && dbCohort.IsActive {
					updateData := map[string]interface{}{"is_active": false}
					_, _, err := s.db.UpdateTx(tx, &dbCohort, updateData)
					if err != nil {
						hasErr = true
						return fmt.Errorf("error deactivating coupon cohort: %w", err)
					}
				}
			}
			fmt.Println("reqCohortSet", reqCohortSet)
			for cohort := range reqCohortSet {
				if _, ok := dbCohortMap[cohort]; !ok {
					newCohort := &CouponCohort{CouponID: q.ID, Cohort: cohort, IsActive: true}
					_, err := s.db.CreateTx(tx, newCohort)
					if err != nil {
						hasErr = true
						return fmt.Errorf("error adding new coupon cohort: %w", err)
					}
				}
			}
		}
	}
	if err := tx.Commit().Error; err != nil {
		hasErr = true
		return fmt.Errorf("error committing transaction: %v", err)
	}
	s.cache.Delete(BuildKey(CacheKeyCouponByID, req.Data.ID))
	s.cache.Delete(BuildKey(CacheKeyCouponBrands, req.Data.ID))
	s.cache.Delete(BuildKey(CacheKeyCouponRules, req.Data.ID))
	s.cache.Delete(BuildKey(CacheKeyCouponCohorts, req.Data.ID))
	return nil
}

// AddCouponBrand adds a new brand to a coupon
func (s *Repository) AddCouponBrand(ctx context.Context, brand *CouponBrand) (uint64, error) {
	_, err := s.db.Create(brand)
	if err != nil {
		return 0, fmt.Errorf("error adding coupon brand: %w", err)
	}

	// Invalidate cache for this coupon's brands
	s.cache.Delete(BuildKey(CacheKeyCouponBrands, brand.CouponID))
	s.cache.Delete(BuildKey(CacheKeyCouponsByBrand, brand.Brand))

	// Get the ID from the created brand
	return uint64(brand.ID), nil
}

// GetCouponBrandsByCouponID retrieves all brands for a coupon
func (s *Repository) GetCouponBrandsByCouponID(ctx context.Context, couponID int64) ([]CouponBrand, error) {
	cacheKey := BuildKey(CacheKeyCouponBrands, couponID)

	data, err := s.FetchOrCache(ctx, cacheKey, func() (interface{}, error) {
		var brands []CouponBrand
		condition := map[string]interface{}{
			"coupon_id": couponID,
			"is_active": true,
		}
		_, err := s.db.Find(condition, &brands)
		if err != nil {
			return nil, fmt.Errorf("error getting coupon brands: %w", err)
		}
		return brands, nil
	})

	if err != nil {
		return nil, err
	}

	if data == nil {
		return []CouponBrand{}, nil
	}

	brands, ok := data.([]CouponBrand)
	if !ok {
		return nil, fmt.Errorf("invalid cache value type in getCouponBrandsByCouponID")
	}

	return brands, nil
}

// DeleteCouponBrand deletes a brand from a coupon
func (s *Repository) DeleteCouponBrand(ctx context.Context, id uint64) error {
	// First get the brand to know which coupon and brand to invalidate cache for
	var brand CouponBrand
	condition := map[string]interface{}{
		"id": id,
	}
	_, err := s.db.Find(condition, &brand)
	if err != nil {
		return fmt.Errorf("error getting coupon brand: %w", err)
	}

	// Delete the brand using Update to set is_active to false
	updateData := map[string]interface{}{
		"is_active": false,
	}
	_, _, err = s.db.Update(&brand, updateData)
	if err != nil {
		return fmt.Errorf("error deleting coupon brand: %w", err)
	}

	// Invalidate cache for this coupon's brands
	s.cache.Delete(BuildKey(CacheKeyCouponBrands, brand.CouponID))
	s.cache.Delete(BuildKey(CacheKeyCouponsByBrand, brand.Brand))

	return nil
}

// AddCouponCohort adds a new cohort to a coupon
func (s *Repository) AddCouponCohort(ctx context.Context, cohort *CouponCohort) (uint64, error) {
	_, err := s.db.Create(cohort)
	if err != nil {
		return 0, fmt.Errorf("error adding coupon cohort: %w", err)
	}

	// Invalidate cache for this coupon's cohorts
	s.cache.Delete(BuildKey(CacheKeyCouponCohorts, cohort.CouponID))

	// Get the ID from the created cohort
	return uint64(cohort.ID), nil
}

// DeleteCouponCohort deletes a cohort from a coupon
func (s *Repository) DeleteCouponCohort(ctx context.Context, id uint64) error {
	// First get the cohort to know which coupon to invalidate cache for
	var cohort CouponCohort
	condition := map[string]interface{}{
		"id": id,
	}
	_, err := s.db.Find(condition, &cohort)
	if err != nil {
		return fmt.Errorf("error getting coupon cohort: %w", err)
	}

	// Delete the cohort using Update to set is_active to false
	updateData := map[string]interface{}{
		"is_active": false,
	}
	_, _, err = s.db.Update(&cohort, updateData)
	if err != nil {
		return fmt.Errorf("error deleting coupon cohort: %w", err)
	}

	// Invalidate cache for this coupon's cohorts
	s.cache.Delete(BuildKey(CacheKeyCouponCohorts, cohort.CouponID))

	return nil
}

// GetAllCoupons retrieves all coupons with pagination and search filters
func (s *Repository) GetAllCoupons(ctx context.Context, req dto.GetAllCouponsRequest) (dto.GetCouponsResponse, error) {
	var page, pageSize int
	if req.Data.Page <= 0 {
		page = 1
	} else {
		page = req.Data.Page
	}
	if req.Data.PageSize <= 0 {
		pageSize = 10
	} else {
		pageSize = req.Data.PageSize
	}

	offset := (page - 1) * pageSize

	// Build dynamic WHERE clause
	whereClauses := []string{"1=1"}
	if req.Data.Valid != nil && *req.Data.Valid {
		whereClauses = append(whereClauses, fmt.Sprintf("c.is_active = 1 AND c.start_timestamp <= %d AND c.end_timestamp >= %d", time.Now().Unix(), time.Now().Unix()))
	}
	if req.Data.Code != nil {
		whereClauses = append(whereClauses, fmt.Sprintf("c.code LIKE '%%%s%%'", *req.Data.Code))
	}
	if req.Data.Name != nil {
		whereClauses = append(whereClauses, fmt.Sprintf("c.name LIKE '%%%s%%'", *req.Data.Name))
	}

	joinBrand := false
	if req.Data.Brands != nil && len(*req.Data.Brands) > 0 {
		joinBrand = true
		whereClauses = append(whereClauses, fmt.Sprintf("cb.brand IN ('%s')", strings.Join(*req.Data.Brands, "','")))
	}

	joinCohort := false
	if req.Data.Cohorts != nil && len(*req.Data.Cohorts) > 0 {
		joinCohort = true
		whereClauses = append(whereClauses, fmt.Sprintf("cc.cohort IN ('%s')", strings.Join(*req.Data.Cohorts, "','")))
	}

	fromClause := "kiranaclubdb.kiranabazar_coupons c"
	if joinBrand {
		fromClause += " JOIN kiranaclubdb.kiranabazar_coupon_brands cb ON c.id = cb.coupon_id AND cb.is_active = 1"
	}
	if joinCohort {
		fromClause += " JOIN kiranaclubdb.kiranabazar_coupon_cohorts cc ON c.id = cc.coupon_id AND cc.is_active = 1"
	}

	whereSQL := ""
	if len(whereClauses) > 0 {
		whereSQL = "WHERE " + (whereClauses[0])
		for i := 1; i < len(whereClauses); i++ {
			whereSQL += " AND " + whereClauses[i]
		}
	}

	// Get total count
	countQuery := fmt.Sprintf("SELECT COUNT(DISTINCT c.id) FROM %s %s", fromClause, whereSQL)

	fmt.Println("countQuery", countQuery)
	var totalCount int64
	_, err := s.db.CustomQuery(&totalCount, countQuery)
	if err != nil {
		return dto.GetCouponsResponse{}, fmt.Errorf("error getting total count: %w", err)
	}

	// Get paginated coupons
	query := fmt.Sprintf(`
		SELECT DISTINCT c.* FROM %s %s
		ORDER BY c.is_active, c.id DESC
		LIMIT %d OFFSET %d
	`, fromClause, whereSQL, pageSize, offset)

	var couponsDao []CouponDao
	_, err = s.db.CustomQuery(&couponsDao, query)
	if err != nil {
		return dto.GetCouponsResponse{}, fmt.Errorf("error getting coupons: %w", err)
	}

	// Convert to Coupon objects and process for response
	var couponDTOs []dto.Coupon
	for _, couponDao := range couponsDao {
		coupon := convertCouponDaoToCoupon(couponDao)
		validFrom := time.Unix(coupon.StartTimestamp, 0).Format(time.RFC3339)
		validTill := time.Unix(coupon.EndTimestamp, 0).Format(time.RFC3339)

		var discount string
		if coupon.DiscountType == "PERCENTAGE" {
			discount = fmt.Sprintf("%.0f%%", coupon.DiscountValue)
		} else {
			discount = fmt.Sprintf("₹%.2f", coupon.DiscountValue)
		}

		couponDTO := dto.Coupon{
			ID:                 int(coupon.ID),
			Code:               coupon.Code,
			Name:               coupon.Name,
			Description:        coupon.Description.String,
			Discount:           discount,
			PercentageDiscount: coupon.DiscountValue,
			ValidFrom:          validFrom,
			ValidTill:          validTill,
			Type:               string(coupon.CouponType),
			Valid:              coupon.IsActive && time.Now().Unix() >= coupon.StartTimestamp && time.Now().Unix() <= coupon.EndTimestamp,
			IsActive:           coupon.IsActive,
		}
		couponDTOs = append(couponDTOs, couponDTO)
	}

	resp := dto.GetCouponsResponse{
		Meta: dto.Meta{},
		Data: dto.GetCouponsData{
			Coupons:   couponDTOs,
			Count:     int(totalCount),
			ErrorText: "",
		},
	}
	return resp, nil
}

// GetCouponRulesByCouponID retrieves all rules for a coupon
func (s *Repository) GetCouponRulesByCouponID(ctx context.Context, couponID int64) ([]dto.CouponRuleResponse, error) {
	rules, err := s.GetCouponRules(ctx, couponID)
	if err != nil {
		return nil, fmt.Errorf("error getting coupon rules: %w", err)
	}

	var resp []dto.CouponRuleResponse
	for _, rule := range rules {
		var value interface{}
		_ = json.Unmarshal([]byte(rule.Value), &value)
		resp = append(resp, dto.CouponRuleResponse{
			RuleType: rule.RuleType,
			Value:    value,
		})
	}
	return resp, nil
}

// GetCouponByName retrieves a coupon by its name
func (s *Repository) GetCouponByName(ctx context.Context, name string, fetchActive bool) (*Coupon, error) {
	condition := map[string]interface{}{
		"name": name,
	}
	if fetchActive {
		condition["is_active"] = true
	}

	var couponsDao []CouponDao
	_, err := s.db.Find(condition, &couponsDao)
	if err != nil {
		return nil, fmt.Errorf("error finding coupon by name: %w", err)
	}

	if len(couponsDao) == 0 {
		return nil, fmt.Errorf("no coupon found with name: %s", name)
	}
	coupon := convertCouponDaoToCoupon(couponsDao[0])
	return coupon, nil
}

// loadCouponDataSafe is a thread-safe wrapper around loadCouponData
func (s *Repository) loadCouponDataSafe() {
	s.isLoading.Lock()
	defer s.isLoading.Unlock()

	ctx := context.Background()
	logger.Info(ctx, "Starting cache refresh...")

	s.loadCouponData()
	logger.Info(ctx, "Cache refresh completed successfully")
}

// startCacheRefreshTicker starts a background ticker to refresh cache every 30 minutes
func (s *Repository) startCacheRefreshTicker() {
	s.refreshTicker = time.NewTicker(20 * time.Minute)

	go func() {
		for {
			select {
			case <-s.refreshTicker.C:
				s.loadCouponDataSafe()
			case <-s.stopRefresh:
				s.refreshTicker.Stop()
				return
			}
		}
	}()
}

// StopCacheRefresh stops the background cache refresh
func (s *Repository) StopCacheRefresh() {
	if s.stopRefresh != nil {
		close(s.stopRefresh)
	}
	if s.refreshTicker != nil {
		s.refreshTicker.Stop()
	}
}

// RefreshCacheNow manually triggers a cache refresh
func (s *Repository) RefreshCacheNow(ctx context.Context) {
	s.isLoading.Lock()
	defer s.isLoading.Unlock()

	logger.Info(ctx, "Manual cache refresh triggered")
	s.loadCouponData()
	logger.Info(ctx, "Manual cache refresh completed")
}

// GetUserCouponsAssignments retrieves all active coupon assignments for a user
func (s *Repository) GetUserCouponsAssignments(ctx context.Context, userID string) ([]UserCoupon, error) {
	cacheKey := BuildKey(CacheKeyUserCouponAssignment, userID, "all")

	data, err := s.FetchOrCache(ctx, cacheKey, func() (interface{}, error) {
		condition := map[string]interface{}{
			"user_id":   userID,
			"is_active": true,
		}

		var userCoupons []UserCoupon
		_, err := s.db.Find(condition, &userCoupons)
		if err != nil {
			return nil, fmt.Errorf("error finding user coupon assignments: %w", err)
		}

		// Filter out expired assignments
		activeAssignments := make([]UserCoupon, 0)
		now := time.Now()
		for _, assignment := range userCoupons {
			if now.Before(assignment.ExpiresAt) {
				activeAssignments = append(activeAssignments, assignment)
			}
		}

		return activeAssignments, nil
	})

	if err != nil {
		return nil, err
	}

	if data == nil {
		return []UserCoupon{}, nil
	}

	userCoupons, ok := data.([]UserCoupon)
	if !ok {
		return nil, fmt.Errorf("invalid cache data type for user coupon assignments")
	}

	return userCoupons, nil
}

func (s *Repository) CheckUserCouponAssignment(ctx context.Context, userID string, couponID int64) (bool, *UserCoupon) {
	userCoupons, err := s.GetUserCouponsAssignments(ctx, userID)
	if err != nil {
		return false, nil
	}
	for _, assignment := range userCoupons {
		fmt.Println(assignment.CouponID, couponID, assignment.IsActive, time.Now().Before(assignment.ExpiresAt))
		if assignment.CouponID == couponID && assignment.IsActive && time.Now().Before(assignment.ExpiresAt) {
			return true, &assignment
		}
	}
	return false, nil
}

// AssignUserCoupon assigns a coupon to a user
func (s *Repository) AddUserCoupon(ctx context.Context, request *dto.AddUserCouponRequest) error {
	if request.Data.UserID == "" || request.Data.CouponID <= 0 {
		err := fmt.Errorf("user_id and coupon_id are required")
		return err
	}

	if request.Data.Validity < 0 {
		return fmt.Errorf("validity must be a positive integer")
	}

	if request.Data.Campaign == "" {
		return fmt.Errorf("campaign is required")
	}

	if request.Data.Validity == 0 {
		request.Data.Validity = 30 // Default to 30 days if not specified
	}

	// Check if assignment already exists
	existing, _ := s.CheckUserCouponAssignment(ctx, request.Data.UserID, request.Data.CouponID)
	if existing {
		return fmt.Errorf("coupon already assigned to user %s for coupon %d", request.Data.UserID, request.Data.CouponID)
	}

	// Create new assignment
	userCoupon := &UserCoupon{
		CouponID:   request.Data.CouponID,
		UserID:     request.Data.UserID,
		AssignedAt: time.Now(),
		ExpiresAt:  time.Now().Add(time.Duration(request.Data.Validity) * 24 * time.Hour),
		IsActive:   true,
		CreatedBy:  request.Data.UserID,
		Campaign:   request.Data.Campaign,
	}

	_, err := s.db.Create(userCoupon)
	if err != nil {
		return fmt.Errorf("error creating user coupon assignment: %w", err)
	}

	// Invalidate cache
	s.cache.Delete(BuildKey(CacheKeyUserCouponAssignment, request.Data.UserID, "all"))

	// Get coupon details to get the coupon code
	coupon, err := s.GetCouponByID(ctx, request.Data.CouponID)
	if err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Error getting coupon details for webengage event: %v\n", err)
	} else {
		// Send WebEngage event
		eventObject := map[string]interface{}{
			"coupon_id":   request.Data.CouponID,
			"coupon_code": coupon.Code,
			"created_by":  request.Data.UserID,
			"campaign":    request.Data.Campaign,
			"validity":    request.Data.Validity,
		}

		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{request.Data.UserID},
			EventName:   "Ordering Coupon Added",
			EventObject: eventObject,
		})
	}

	return nil
}

// DeactivateUserCoupon deactivates a user's coupon assignment
func (s *Repository) DeactivateUserCoupon(ctx context.Context, request *dto.DeactivateUserCouponRequest) error {
	if request.Data.UserID == "" || request.Data.CouponID <= 0 {
		return fmt.Errorf("user_id and coupon_id are required")
	}

	// Check if assignment exists and is active
	existing, assignment := s.CheckUserCouponAssignment(ctx, request.Data.UserID, request.Data.CouponID)
	if !existing {
		return fmt.Errorf("no active coupon assignment found for user %s and coupon %d", request.Data.UserID, request.Data.CouponID)
	}

	// Update the assignment to set is_active to false
	updateData := map[string]interface{}{
		"is_active": false,
	}
	_, _, err := s.db.Update(assignment, updateData)
	if err != nil {
		return fmt.Errorf("error deactivating user coupon assignment: %w", err)
	}

	// Invalidate cache
	s.cache.Delete(BuildKey(CacheKeyUserCouponAssignment, request.Data.UserID, "all"))
	return nil
}
