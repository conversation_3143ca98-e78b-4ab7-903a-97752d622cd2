package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/repositories/sqlRepo"
	awbMasterDTO "kc/internal/ondc/service/awbMaster/dto"
	"kc/internal/ondc/service/brands"
	inventoryDao "kc/internal/ondc/service/inventory/models/dao"
	"kc/internal/ondc/service/logistics/couriers"
	ordercomm "kc/internal/ondc/service/orderComm"
	orderreason "kc/internal/ondc/service/orderReason"
	orderstatus "kc/internal/ondc/service/orderStatus"
	"kc/internal/ondc/service/orderStatus/constants"
	delhiverystatus "kc/internal/ondc/service/orderStatus/delhiveryStatus"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	ekartstatus "kc/internal/ondc/service/orderStatus/ekart"
	omsstatus "kc/internal/ondc/service/orderStatus/omsStatus"
	processingstatus "kc/internal/ondc/service/orderStatus/processingStatus"
	shipdelightstatus "kc/internal/ondc/service/orderStatus/shipdelightStatus"
	shipmentstatus "kc/internal/ondc/service/orderStatus/shipmentStatus"
	"kc/internal/ondc/service/products"
	"kc/internal/ondc/utils"
	"log"
	"math"
	"reflect"
	"slices"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	libRedis "github.com/go-redis/redis/v8"
	"golang.org/x/sync/errgroup"

	"go.uber.org/zap"

	OMSModels "github.com/Kirana-Club/oms-go/pkg/models"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/mixpanel/mixpanel-go"
)

func UpdateCallStatus(
	sqlRepo *sqlRepo.Repository,
	orderID, callStatus, note, updatedBy string, updatedAt int64, cancelReason string, returnedReason string, returnReceivedNote string,
) error {

	// Helper function to escape SQL strings
	escapeSQL := func(s string) string {
		// Replace single quotes with escaped single quotes
		return strings.ReplaceAll(s, "'", "\\'")
	}

	// Build columns and values arrays for dynamic query construction
	columns := []string{"order_id"}
	values := []string{fmt.Sprintf("'%s'", escapeSQL(orderID))}
	updateParts := []string{}

	// Helper function to add field if not empty
	addField := func(column string, value interface{}) {
		if str, ok := value.(string); ok && str != "" {
			columns = append(columns, column)
			values = append(values, fmt.Sprintf("'%s'", escapeSQL(str)))
			updateParts = append(updateParts, fmt.Sprintf("%s = VALUES(%s)", column, column))
		} else if num, ok := value.(int64); ok {
			columns = append(columns, column)
			values = append(values, fmt.Sprintf("'%d'", num))
			updateParts = append(updateParts, fmt.Sprintf("%s = VALUES(%s)", column, column))
		}
	}

	if callStatus != "" {
		addField("call_status", callStatus)
	}
	if note != "" {
		addField("note", note)
	}

	if returnReceivedNote != "" {
		addField("returned_received_note", returnReceivedNote)
	}

	addField("updated_by", updatedBy)
	addField("updated_at", updatedAt)

	if cancelReason != "" {
		cancelReasonMap := orderreason.GetOrderCancelCodeFromReason(cancelReason)
		addField("cancel_reason", cancelReasonMap.ReasonCode)
	}

	if returnedReason != "" {
		returnedReasonMap := orderreason.GetOrderReturnCodeFromReason(returnedReason)
		addField("returned_reason", returnedReasonMap.ReasonCode)
	}

	columnsStr := strings.Join(columns, ", ")
	valuesStr := strings.Join(values, ", ")
	updateStr := strings.Join(updateParts, ", ")

	query := fmt.Sprintf(`
		INSERT INTO kiranabazar_call_status (%s)
		VALUES (%s)
		ON DUPLICATE KEY UPDATE %s`,
		columnsStr,
		valuesStr,
		updateStr,
	)

	_, err := sqlRepo.CustomQuery(nil, query)
	return err
}

func UpdateOrderStatus(sqlRepo *sqlRepo.Repository, orderID int64, status string, statusType string) error {
	orderStatuses := orderstatus.MapOrderStatus(status, statusType, orderstatus.OrderStatusResponse{})
	_, _, err := sqlRepo.Update(&dao.KiranaBazarOrder{
		ID: &orderID,
	}, &dao.KiranaBazarOrder{
		OrderStatus:      &status,
		DeliveryStatus:   orderStatuses.ShipmentStatus,
		DisplayStatus:    orderStatuses.DisplayStatus,
		ProcessingStatus: orderStatuses.ProcessingStatus,
		UpdatedAt:        time.Now(),
	})
	if err != nil {
		return err
	}
	return nil
}

func GetOrderDetails(sqlRepo *sqlRepo.Repository, orderId string) (*dao.KiranaBazarOrderDetails, error) {
	data := []dao.KiranaBazarOrderDetail{}
	query := fmt.Sprintf("select kod.order_details from kiranabazar_order_details kod where kod.order_id = '%s'", orderId)
	_, err := sqlRepo.CustomQuery(&data, query)
	if err != nil {
		return nil, err
	}

	if len(data) > 0 {
		orderDetails := dao.KiranaBazarOrderDetails{}
		err = json.Unmarshal(data[0].OrderDetails, &orderDetails)
		if err != nil {
			return nil, err
		}
		return &orderDetails, nil
	}
	return nil, nil
}

func GetOrderInfo(repo *sqlRepo.Repository, orderId int64) (orderInfo dao.KiranaBazarOrder, err error) {
	_, err = repo.Find(map[string]interface{}{
		"id": orderId,
	}, &orderInfo)
	return
}

func GetOrderReconciliation(repo *sqlRepo.Repository, orderId int64) (orderReconciliation dao.KiranaBazarReconciliation, err error) {
	_, err = repo.Find(map[string]interface{}{
		"order_id": orderId,
	}, &orderReconciliation)
	return
}

func GetSellerInfo(repo *sqlRepo.Repository, seller string) (sellerInfo dao.KiranaBazarSeller, err error) {
	_, err = repo.Find(map[string]interface{}{
		"code": seller,
	}, &sellerInfo)
	return
}

func GetOrderStatus(repo *sqlRepo.Repository, orderId int64) (orderStatus dao.KiranaBazarOrderStatus, err error) {
	_, err = repo.Find(map[string]interface{}{
		"id": orderId,
	}, &orderStatus)
	return
}

// GetPackageDetails provides the package details for the order
func GetPackageDetails(repo *sqlRepo.Repository, orderId int64) (packageDetails dao.KiranabazarOrderPackageDetails, err error) {
	_, err = repo.Find(map[string]interface{}{
		"order_id": orderId,
	}, &packageDetails)
	return
}

func GetOrderApiStatus(repo *sqlRepo.Repository, orderId int64) (orderStatus dao.KiranaBazarOrderStatus, err error) {
	_, err = repo.Find(map[string]interface{}{
		"id": orderId,
	}, &orderStatus)
	return
}

func GetSource(updatedBy string, requestSource string) string {
	source := "D2R"
	if updatedBy == "IVR" {
		source = "AUTOMATION"
	} else if updatedBy == "SHIPWAY" {
		source = "AUTOMATION"
	} else if updatedBy == "SHIPDELIGHT" {
		source = "AUTOMATION"
	} else if updatedBy == "DELHIVERY" {
		source = "AUTOMATION"
	}
	if requestSource != "" {
		source = requestSource
	}
	return source
}

func UpdateAddressInOrderDetails(sqlRepo *sqlRepo.Repository, orderDetails *dao.KiranaBazarOrderDetails, data dto.UpdateOrderAddressData) error {
	line1 := ""
	line2 := ""

	storeHouseNumber := ""
	addressTag := utils.ADDRESS_TAG[data.Tag]
	if addressTag == "HOME" {
		storeHouseNumber = data.HouseNumber
	} else if addressTag == "STORE" {
		storeHouseNumber = data.StoreName
	}
	if storeHouseNumber != "" && data.Neighbourhood != "" {
		line1 = storeHouseNumber + ", " + data.Neighbourhood
	}

	if data.Village != "" && data.Landmark != "" {
		line2 = data.Village + ", " + data.Landmark
	}
	line := line1 + ", " + line2

	orderDetails.ShippingAddress.District = &data.City
	orderDetails.ShippingAddress.Line = &line
	orderDetails.ShippingAddress.Name = &data.Name
	orderDetails.ShippingAddress.Neighbourhood = data.Neighbourhood
	orderDetails.ShippingAddress.Village = data.Village
	orderDetails.ShippingAddress.HouseNumber = data.HouseNumber
	orderDetails.ShippingAddress.Landmark = data.Landmark
	orderDetails.ShippingAddress.StoreName = data.StoreName
	orderDetails.ShippingAddress.Tag = &addressTag
	orderDetails.ShippingAddress.State = &data.State
	orderDetails.ShippingAddress.PostalCode = &data.PostalCode
	orderDetails.ShippingAddress.Line1 = line1
	orderDetails.ShippingAddress.Line2 = line2
	orderDetails.ShippingAddress.Phone = &data.Phone
	if data.AlternatePhone != "" {
		orderDetails.ShippingAddress.AlternatePhone = &data.AlternatePhone
	}

	orderDetailsJson, err := json.Marshal(orderDetails)
	if err != nil {
		return err
	}

	query := fmt.Sprintf("update kiranabazar_order_details set order_details = '%s' where order_id = '%s'", string(orderDetailsJson), data.OrderID)
	_, err = sqlRepo.CustomQuery(nil, query)
	if err != nil {
		return err
	}
	return nil
}

func UpdatePaymentMeta(
	sqlRepo *sqlRepo.Repository,
	orderID string,
	advanceTaken *bool,
	paidAmount *float64,
	orderStatus *string,
	updatedBy *string,
	orderingModule *string,
	userId *string,
	totalAmount float64,
	paidAmountProof *[]dto.PaidAmountProof,
	paymentId *string,
	paymentDiscount *float64,
	rnn *string,
) error {
	if paidAmount == nil {
		return nil
	}

	int64OrderId, err := strconv.ParseInt(orderID, 10, 64)
	if err != nil {
		return err
	}

	paymentMeta := make(map[string]interface{})
	sqlWhereData := &dao.KiranaBazarOrderPayment{
		OrderID:     &int64OrderId,
		PaymentMeta: nil,
	}
	var sqlUpdateData dao.KiranaBazarOrderPayment
	advanceProofUpdate := false

	if rnn != nil && len(*rnn) > 0 && *rnn != "" {
		sqlUpdateData.RNN = rnn
		sqlUpdateData.Source = "QR"
	}

	if paidAmountProof != nil && len(*paidAmountProof) > 0 {
		paymentMeta["advance_taken"] = true
		paymentMeta["paid_amount_proof"] = paidAmountProof
		advanceProofUpdate = true
		if paymentId != nil && *paymentId != "" {
			sqlUpdateData.PaymentId = paymentId
		}
	} else if paymentId != nil && *paymentId != "" {
		paymentMeta["advance_taken"] = true
		paymentMeta["paid_amount_proof"] = []dto.PaidAmountProof{}
		sqlUpdateData.PaymentId = paymentId
		sqlUpdateData.Source = "Gateway"
	} else {
		paymentMeta["advance_taken"] = false
		paymentMeta["paid_amount_proof"] = []dto.PaidAmountProof{}
	}

	paymentMetaJson, err := json.Marshal(paymentMeta)
	if err != nil {
		return err
	}

	if paymentMeta["advance_taken"] == true {
		orderAmount := totalAmount
		if paymentDiscount != nil && *paymentDiscount > 0 {
			orderAmount = math.Max(0, totalAmount-*paymentDiscount)
		}
		orderAmount = utils.Round(orderAmount)
		paidAmount = utils.Float64Ptr(utils.Round(*paidAmount))
		totalAmount = utils.Round(totalAmount)

		sqlUpdateData.PaidAmount = paidAmount
		sqlUpdateData.PaymentMeta = paymentMetaJson
		sqlUpdateData.Amount = &orderAmount

		if totalAmount == *paidAmount {
			sqlUpdateData.Status = utils.StrPtr("FULLY_PAID")
		} else {
			sqlUpdateData.Status = utils.StrPtr("PARTIALLY_PAID")
		}
	}

	_, noOfRowsAffected, err := sqlRepo.Update(sqlWhereData, sqlUpdateData)
	if err != nil {
		return err
	}
	if noOfRowsAffected == 0 {
		slackMsg := fmt.Sprintf("Trying to update payment meta for order status %s: %s", *orderStatus, orderID)
		if updatedBy != nil {
			slackMsg = fmt.Sprintf("%s %s", slackMsg, *updatedBy)
			if *updatedBy != "Delhivery" {
				// slack.SendSlackMessage(slackMsg)
			}
		}
	}

	// call advance payment tracker API for zoff foods
	if noOfRowsAffected > 0 && advanceProofUpdate {
		proofUrlsArray := make([]string, len(*paidAmountProof))
		for _, proof := range *paidAmountProof {
			proofUrlsArray = append(proofUrlsArray, proof.Url)
		}

		proofUrls := strings.Join(proofUrlsArray, ", ")

		requestObject := map[string]interface{}{
			"user_id":        userId,
			"order_id":       orderID,
			"confirmed_date": time.Now().Format("2006-01-02 15:04:05"),
			"total_amount":   totalAmount,
			"paid_amount":    paidAmount,
			"proof_urls":     proofUrls,
		}

		_, ok := utils.ADVANCE_PAYMENT_SHEET_APIS[*orderingModule]
		if ok {
			_, statusCode, err := utils.CallExternalAPI(utils.ADVANCE_PAYMENT_SHEET_APIS[*orderingModule], "POST", requestObject, nil)
			if err != nil {
				slack.SendSlackMessage(fmt.Sprintf("Error inserting data in advance payment sheet for: %s, %d %v", orderID, statusCode, err))
				return err
			}
		}

	}

	return nil
}

func UpdateOrderTrackingDetails(sqlRepo *sqlRepo.Repository, orderID int64, courier string, awbNumber string, updatedBy string) error {
	if updatedBy != "SHIPWAY" && (courier == "" || awbNumber == "" || orderID == 0) {
		err := errors.New("invalid orderID")
		return err
	}
	upsertQuery := fmt.Sprintf(`INSERT INTO kiranabazar_order_status
								(status, created_at, updated_at, expected_delivery_date,
								 error, courier, awb_number, id, awb_numbers) VALUES (NULL, NULL,'%s',
								  NULL, NULL, '%s', '%s', %d, JSON_ARRAY('%s')) ON DUPLICATE KEY
								   UPDATE updated_at=VALUES(updated_at), courier=VALUES(courier), awb_number=VALUES(awb_number), awb_numbers=JSON_ARRAY(VALUES(awb_number))`, time.Now().Format("2006-01-02 15:04:05"), courier, awbNumber, orderID, awbNumber)

	_, err := sqlRepo.CustomQuery(nil, upsertQuery)

	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("Tracking Details Not Updated %d: %s %s", orderID, courier, awbNumber))
		return err
	}
	return nil

}

func (s *Service) AddUpdateNDROrder(ctx context.Context, orderID string, explaination string, updatedBy string, orderStatus string, displayStatus string, attemptCount int, source string, agentReason string) error {
	intOrderID, err := strconv.Atoi(orderID)
	if err != nil {
		return err
	}
	orderNDRInfo, _ := s.GetNDROrder(intOrderID)

	orderDetails, err := GetOrderDetails(s.repository, orderID)
	if err != nil {
		return err
	}

	var updatedAttempCount int
	if orderNDRInfo != nil {
		updatedAttempCount = *orderNDRInfo.AttemptCount
	}
	if attemptCount == 0 {
		updatedAttempCount += 1
	} else {
		updatedAttempCount = attemptCount
	}
	updatedNDRTag := fmt.Sprintf("%s%d", constants.NDR, updatedAttempCount)

	if orderNDRInfo != nil {
		updateActivity := false
		if !((source == "CSV") && (orderNDRInfo != nil) && (*orderNDRInfo.AttemptCount == attemptCount)) {
			updateActivity = true
		}
		_, err = s.UpdateNDROrder(ctx, &dto.UpdateNDROrderRequest{OrderID: orderID,
			AssignedTo:      orderNDRInfo.AssignedTo,
			NDRAgentReason:  &agentReason,
			Note:            &explaination,
			UpdatedBy:       updatedBy,
			OrderStatus:     orderStatus,
			UpdatedAt:       time.Now().UnixMilli(),
			OrderActivityID: orderNDRInfo.OrderActivityID,
			NDRTag:          &updatedNDRTag,
			AttemptCount:    &updatedAttempCount,
			Source:          &source,
			UpdateActivity:  &updateActivity})
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("error in updating NDR order %v %s, %s, %s, %s, %s, %s", err, orderID, agentReason, explaination, orderNDRInfo.OrderActivityID, updatedNDRTag, source))
		}
	} else if displayStatus == displaystatus.NDR {
		_, err = s.AddNDROrder(ctx, &dto.AddNDROrderRequest{OrderID: orderID,
			NDRAgentReason: &agentReason,
			Note:           &explaination,
			UpdatedBy:      updatedBy,
			OrderStatus:    orderStatus,
			OrderValue:     int(orderDetails.GetOrderValue()),
			NDRTag:         &updatedNDRTag,
			AttemptCount:   &updatedAttempCount,
			Source:         &source})
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("error in add NDR order %v", err))
		}
	}
	return nil
}

func (s *Service) AddUpdateNDROrderV2(ctx context.Context, orderID string, updatedBy string, agentReason string, orderStatus string, orderValue float64, source string) error {
	intOrderID, err := strconv.Atoi(orderID)
	if err != nil {
		return err
	}
	orderNDRInfo, _ := s.GetNDROrderV2(intOrderID)
	if orderNDRInfo == nil {
		isOrderKcFulfilled, err := s.IsOrderKcFulfilled(intOrderID)
		if err != nil {
			return err
		}
		if isOrderKcFulfilled {
			_, err := s.AddNDRV2(ctx, fmt.Sprintf(`%d`, intOrderID), orderStatus, updatedBy, agentReason, source, false)
			if err != nil {
				return err
			}
		} else {
			log.Println("Order is not KC Fulfilled so not adding to NDR, orderID:", orderID)
			return nil
		}
	} else {
		attemptCount := int(*orderNDRInfo.NDRAttemptCount + 1)
		_, err = s.UpdateNDROrderV2(ctx, &dto.UpdateNDROrderRequestV2{
			Data: dto.UpdateNDROrderRequestV2Data{
				OrderID:        orderID,
				ActionID:       orderNDRInfo.OrderActionID,
				UpdatedBy:      updatedBy,
				NDRAgentReason: &agentReason,
				AttemptCount:   &attemptCount,
				OrderStatus:    orderStatus,
				NDRStage:       "3PL",
				Source:         &source,
			},
		})
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *Service) UpdateB2BOrderStatus(ctx context.Context, request *dto.UpdateB2BOrderStatusRequest) (*dto.UpdateB2BOrderStatusResponse, error) {
	source := GetSource(request.Data.UpdatedBy, request.Data.Source)

	int64OrderId, err := strconv.ParseInt(request.Data.OrderID, 10, 64)
	if err != nil {
		return nil, err
	}

	// get order details json from db
	orderDetails, err := GetOrderDetails(s.repository, request.Data.OrderID)
	if err != nil {
		return nil, err
	}

	orderInfo, err := GetOrderInfo(s.repository, int64OrderId)
	if err != nil {
		return nil, err
	}

	if orderInfo.DeliveryStatus == shipmentstatus.RTO_RECEIVED {
		return nil, errors.New("order return reached the desitination")
	}

	orderNDRInfo, _ := s.GetNDROrder(int(int64OrderId))

	orderStatus := strings.ToUpper(request.Data.OrderStatus)
	orderStatuses := orderstatus.MapOrderStatus(orderStatus, request.Data.OrderStatusType, orderstatus.OrderStatusResponse{})

	if orderStatus == "NOTCONFIRMED" && !includes([]string{"Others"}, request.Data.OrderMeta.CallStatus) {
		_ = sendWhatsAppForNonConfirmedOrders(orderInfo, orderDetails)
	}

	// This is to handle the case when order shipment is created -- this is comprehensive
	if orderStatuses.DisplayStatus == displaystatus.SHIPMENT_CREATED && orderStatuses.ShipmentStatus == "" && orderStatuses.ProcessingStatus == processingstatus.SHIPMENT_CREATED {
		pushOmsLogs := dao.KiranaBazarPushOrderToOMSLogs{}
		s.repository.CustomQuery(&pushOmsLogs, fmt.Sprintf(`select * from kiranabazar_push_order_to_oms_logs where order_id = %d limit 1`, int64OrderId))
		if pushOmsLogs.ID != 0 {
			return &dto.UpdateB2BOrderStatusResponse{
				Message: "Order status already updated successfully to shipment created",
			}, nil
		}
		orderPayment, err := GetOrderPayment(s.repository, request.Data.OrderID)
		if err != nil {
			return nil, err
		}
		vendor, err := GetSellerInfo(s.repository, orderInfo.Seller)
		if err != nil {
			return nil, err
		}
		vendorInvoiceDetails := vendor.GetInvoicingDetails()
		vendorShippingDetails := vendor.GetShippingDetails()

		IGST := !strings.EqualFold(strings.ToUpper(vendorShippingDetails.State), strings.ToUpper(*orderDetails.ShippingAddress.State))

		invoiceRequest, itemQuantity, err := s.CreateInvoiceRequest(orderInfo, orderDetails, orderPayment, vendorShippingDetails, request.Data.OrderMeta.InvoiceNumber, vendorInvoiceDetails, request.Data.OrderMeta.Courier, request.Data.OrderMeta.AWBNumber, IGST)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("err while creating shipment in b2b update status in UpdateB2BOrderStatus err = %v", err))
			return nil, err
		}
		CGSTValue := 0.0
		SGSTValue := 0.0
		IGSTValue := 0.0
		if !IGST {
			CGSTValue = utils.RoundToTwoDecimals(invoiceRequest.Data.TaxSummary.TotalItemTax / 2.0)
			SGSTValue = utils.RoundToTwoDecimals(CGSTValue)
		} else {
			IGSTValue = invoiceRequest.Data.TaxSummary.TotalItemTax
		}
		_, err = s.HandleShipmentCreatedOrder(context.Background(), dto.ShipmentCreatedRequest{
			Data: dto.ShipmentCreatedRequestData{
				OrderID:           request.Data.OrderID,
				OMSOrderID:        fmt.Sprintf("KC_%06d", int64OrderId),
				OrderStatus:       processingstatus.SHIPMENT_CREATED,
				Courier:           request.Data.OrderMeta.Courier,
				AWBNumber:         request.Data.OrderMeta.AWBNumber,
				CSGT:              CGSTValue,
				IGST:              IGSTValue,
				SGST:              SGSTValue,
				BuyerGST:          invoiceRequest.Data.BilledTo.GSTIN,
				NoOfSkus:          len(invoiceRequest.Data.Items),
				ItemQuantity:      itemQuantity,
				Discount:          invoiceRequest.Data.TaxSummary.TotalDiscount,
				InvoiceURL:        orderPayment.ExtInvoice,
				InvoiceID:         orderPayment.ExtInvoiceNumber,
				InvoiceAmount:     math.Round(invoiceRequest.Data.TaxSummary.TotalValue),
				UpdatedBy:         request.Data.UpdatedBy,
				Source:            source,
				OMS:               "",
				ShipmentCreatedAt: time.Now().UnixMilli(),
				KCShip:            true,
			},
		})
		if err != nil {
			return nil, err
		}
		return &dto.UpdateB2BOrderStatusResponse{
			Message: "Order status updated successfully to return received",
		}, nil
	}

	if (orderStatuses.DisplayStatus == constants.NDR) && (source == "CSV") {
		orderStatuses.ShipmentStatus = shipmentstatus.MapShipmentStatus(request.Data.OrderMeta.Note)
	}

	if (request.Data.OrderStatus == *orderInfo.OrderStatus) && (orderStatuses.DisplayStatus != constants.NDR) {
		fmt.Printf("order status aready synced %d \n", orderInfo.ID)
		return nil, errors.New("order status alredt same")
	}

	if (orderStatuses.DisplayStatus == constants.NDR) && (request.Data.UpdatedBy == "SHIPWAY_POLLING") {
		fmt.Printf("order status aready synced %d \n", orderInfo.ID)
		return nil, errors.New("order status alredt same")
	}

	errChan := make(chan error)
	wg := sync.WaitGroup{} // WaitGroup to manage goroutines

	// Goroutine function wrapper to handle errors
	runTask := func(task func() error) {
		defer wg.Done()
		if err := task(); err != nil {
			errChan <- err
		}
	}

	// update order status and call status in db
	if (!includes([]string{displaystatus.RETURNED, displaystatus.DELIVERED, displaystatus.CONFIRMED, displaystatus.CANCELLED, displaystatus.NDR}, orderStatuses.DisplayStatus)) && (request.Data.OrderStatus != shipmentstatus.RTO_RECEIVED) {
		wg.Add(1)
		go runTask(func() error {
			return UpdateOrderStatus(s.repository, int64OrderId, orderStatus, request.Data.OrderStatusType)
		})
	}

	if orderStatuses.DisplayStatus == constants.IN_TRANSIT {
		err := s.pushOrdersToKCCACHE(ctx, *orderInfo.UserID, request.Data.OrderID, request.Data.OrderMeta.AWBNumber, orderInfo.Seller, constants.IN_TRANSIT)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("error in pushing order to kccache %v", err))
		}
	}

	wg.Add(1)
	go runTask(func() error {
		return UpdateCallStatus(s.repository, request.Data.OrderID, request.Data.OrderMeta.CallStatus, request.Data.OrderMeta.Note, request.Data.UpdatedBy, request.Data.UpdatedAt, request.Data.OrderMeta.CancelReason, request.Data.OrderMeta.ReturnedReason, request.Data.OrderMeta.ReturnReceivedNote)
	})

	// update advance taken data in db and insert data in advance payment sheet
	wg.Add(1)
	go runTask(func() error {
		return UpdatePaymentMeta(s.repository, request.Data.OrderID, request.Data.OrderMeta.AdvanceTaken, request.Data.OrderMeta.PaidAmount,
			&orderStatus, &request.Data.UpdatedBy, &request.Data.OrderingModule, orderInfo.UserID,
			orderDetails.TotalAmount, &request.Data.OrderMeta.PaidAmountProof, &request.Data.OrderMeta.PaymentId, nil, request.Data.OrderMeta.RNN)
	})

	// update NDR order status in db
	if orderStatuses.DisplayStatus == displaystatus.NDR {
		wg.Add(1)
		go runTask(func() error {
			return s.AddUpdateNDROrderV2(ctx, request.Data.OrderID, request.Data.UpdatedBy, request.Data.OrderMeta.Note, request.Data.OrderStatus, orderDetails.GetOrderValue(), request.Data.Source)
		})
	}

	if orderStatuses.ShipmentStatus == shipmentstatus.OUT_FOR_DELIVERY {
		wg.Add(1)
		go runTask(func() error {
			ofdTimeStamp := time.Now().UnixMilli()
			if (request.Data.OrderMeta.OutForDeliveryTimestamp != nil) && (*request.Data.OrderMeta.OutForDeliveryTimestamp != 0) {
				ofdTimeStamp = *request.Data.OrderMeta.OutForDeliveryTimestamp
			}

			err := s.pushOrdersToKCCACHE(ctx, *orderInfo.UserID, request.Data.OrderID, request.Data.OrderMeta.AWBNumber, orderInfo.Seller, shipmentstatus.OUT_FOR_DELIVERY)
			if err != nil {
				slack.SendSlackMessage(fmt.Sprintf("error in pushing order to kccache %v", err))
			}

			s.AddDataForReconciliation(ctx, &dto.AddReconciliationRequest{
				OrderID: int64OrderId,
				Data: []dto.StatusTimeStamp{
					{
						TimeStamp:   ofdTimeStamp,
						OrderStatus: "order_ofd",
					},
				},
			})
			return nil
		})
	}

	// Close the error channel after all goroutines finish
	go func() {
		wg.Wait()
		close(errChan)
	}()

	// @developers this is logic for dispatched order, currently only added mixpanel event, this is fired in goroutine because no need to wait for the same
	// added status for shipdelight as well on 6th may 2025 -- <EMAIL>
	go func(orderID, status string, updatedAt int64) {
		// this is for dehivery (for delhivery we know that when the NSL code is X-PPOM or X-PPOM then the status is order picked up)
		// if STATUS 100 for shipdelight then its pickup added on on 6th may 2025
		// if status is EKT_SHIPMENT_PICKUP_COMPLETE for ekart then its pickup added on on 16th june 2025
		if !slices.Contains([]string{strings.ToUpper(delhiverystatus.XPPOM), strings.ToUpper(delhiverystatus.XPROM), strings.ToUpper(shipdelightstatus.STATUS_100), strings.ToUpper(ekartstatus.EKT_SHIPMENT_PICKUP_COMPLETE)}, strings.ToUpper(status)) {
			return
		}
		// update this updated by for specific 3pl
		UpdatedBy := request.UpdatedBy
		err := s.HandleDispatchedOrder(context.Background(), dto.DispatchedOrderRequest{
			Data: dto.DispatchedOrderData{
				OrderID:        orderID,
				DispatchedTime: updatedAt,
				UpdatedBy:      UpdatedBy,
			},
		})
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("error in dispatched order %v", err))
		}
	}(request.Data.OrderID, request.Data.OrderStatus, request.Data.UpdatedAt)

	// Check for errors from goroutines
	for err := range errChan {
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("🆘 Error updating order by %s from source %s during parallel operations %s : %v", request.Data.UpdatedBy, source, request.Data.OrderID, err))
			return nil, fmt.Errorf("error occurred during parallel operations: %v", err)
		}
	}

	if orderStatuses.ShipmentStatus == shipmentstatus.RTO_RECEIVED {
		_, err = s.HandleReturnReceivedOrder(ctx, &dto.OrderReturnReceivedRequest{
			Data: dto.OrderReturnReceivedRequestData{
				OrderID:     request.Data.OrderID,
				Email:       request.Data.UpdatedBy,
				Source:      request.Data.Source,
				OrderStatus: request.Data.OrderStatus,
				OrderMeta: dto.OrderMeta{
					ReturnReceivedNote: request.Data.OrderMeta.ReturnReceivedNote,
				},
			},
		})
		if err != nil {
			return nil, err
		}

		err := s.removeOrderFromOutForDeliveryCache(ctx, *orderInfo.UserID, request.Data.OrderID)

		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("error in removing order from out for delivery cache %v", err))
		}

		return &dto.UpdateB2BOrderStatusResponse{
			Message: "Order status updated successfully to return received",
		}, nil
	} else if orderStatuses.ShipmentStatus == shipmentstatus.RTO_DELIVERED {
		_, err = s.RefundAdvancePayment(ctx, dto.RefundPaymentApiRequest{
			UserID:  *orderInfo.UserID,
			OrderID: int64OrderId,
			Amount:  -1,
			Reason:  utils.StrPtr(shipmentstatus.RTO_DELIVERED),
		})
		if err != nil {
			logger.Warn(ctx, "failed to refund payment", zap.Error(err))
		}

		_, err = s.RevertUserCashback(ctx, dto.RevertUserCashbackRequest{
			UserID: *orderInfo.UserID,
			Data: dto.RevertUserUserCashbackData{
				OrderId: int64OrderId,
			},
		})
		if err != nil {
			logger.Error(ctx, "failed to revert user cashback", zap.Error(err))
		}
		_, err = s.HandleReturnedOrder(ctx, dto.OrderReturnedRequest{
			UserID: *orderInfo.UserID,
			Data: dto.OrderReturnedData{
				OrderID:     request.Data.OrderID,
				Email:       request.Data.UpdatedBy,
				Reason:      request.Data.OrderMeta.ReturnedReason,
				Message:     request.Data.OrderMeta.Note,
				Explanation: request.Data.OrderMeta.Note,
				Source:      source,
				Status:      orderStatus,
				StatusType:  request.Data.OrderStatusType,
			},
		})
		if err != nil {
			return nil, err
		}
		return &dto.UpdateB2BOrderStatusResponse{
			Message: "Order status updated successfully to returned",
		}, nil
	} else if orderStatuses.DisplayStatus == constants.RETURNED {
		_, err = s.HandleReturnedOrder(ctx, dto.OrderReturnedRequest{
			UserID: *orderInfo.UserID,
			Data: dto.OrderReturnedData{
				OrderID:     request.Data.OrderID,
				Email:       request.Data.UpdatedBy,
				Reason:      request.Data.OrderMeta.ReturnedReason,
				Message:     request.Data.OrderMeta.Note,
				Explanation: request.Data.OrderMeta.Note,
				Source:      source,
				Status:      orderStatus,
				StatusType:  request.Data.OrderStatusType,
			},
		})
		if err != nil {
			return nil, err
		}
		return &dto.UpdateB2BOrderStatusResponse{
			Message: "Order status updated successfully to returned",
		}, nil
	} else if orderStatuses.DisplayStatus == constants.CANCELLED {
		_, err := s.CancelKiranaBazarOrder(ctx, dto.AppCancelKiranaBazarOrderRequest{
			UserID: *orderInfo.UserID,
			Data: dto.AppCancelKiranaBazarOrderData{
				OrderID:     request.Data.OrderID,
				Email:       request.Data.UpdatedBy,
				Reason:      request.Data.OrderMeta.CancelReason,
				Explanation: request.Data.OrderMeta.Note,
				Message:     request.Data.OrderMeta.Note,
				Source:      request.Data.Source,
				StatusType:  request.Data.OrderStatusType,
			},
		})
		if err != nil {
			return nil, err
		}
		return &dto.UpdateB2BOrderStatusResponse{
			Message: "Order status updated successfully to cancelled",
		}, nil
	} else if orderStatuses.DisplayStatus == constants.DELIVERED {
		var deliveredAt int64
		if request.Data.OrderMeta.DeliveredTimestamp != nil {
			deliveredAt = *request.Data.OrderMeta.DeliveredTimestamp
		} else if request.Data.UpdatedAt != 0 {
			deliveredAt = request.Data.UpdatedAt
		} else {
			deliveredAt = time.Now().UnixMilli()
		}
		_, err := s.HandleDeliveredOrder(ctx, dto.OrderDeliveredRequest{
			UserID: *orderInfo.UserID,
			Data: dto.OrderDeliveredData{
				OrderID:     request.Data.OrderID,
				Email:       request.Data.UpdatedBy,
				Message:     request.Data.OrderMeta.Note,
				Explanation: request.Data.OrderMeta.Note,
				Source:      source,
				DeliveredAt: deliveredAt,
				OrderStatus: orderStatus,
				StatusType:  request.Data.OrderStatusType,
			},
		})
		if err != nil {
			return nil, err
		}
		return &dto.UpdateB2BOrderStatusResponse{
			Message: "Order status updated successfully to delivered",
		}, nil
	} else if orderStatuses.DisplayStatus == displaystatus.NDR {
		if !((source == "CSV") && (orderNDRInfo != nil) && (*orderNDRInfo.AttemptCount == request.Data.OrderMeta.NDRAttempCount)) {
			_, err := s.HandleFailedDeliveryOrder(ctx, dto.OrderFailedDeliveryRequest{
				UserID: *orderInfo.UserID,
				Data: dto.OrderFailedDeliveryData{
					OrderID:     request.Data.OrderID,
					Email:       request.Data.UpdatedBy,
					Message:     request.Data.OrderMeta.Note,
					Explanation: orderStatuses.ShipmentStatus,
					Source:      source,
					OrderStatus: orderStatus,
					StatusType:  request.Data.OrderStatusType,
				},
			})
			if err != nil {
				return nil, err
			}
			return &dto.UpdateB2BOrderStatusResponse{
				Message: "Order status updated successfully to failed delivery attempt",
			}, nil
		}
	}

	paidAmountProofUrls := []string{}
	for _, proof := range request.Data.OrderMeta.PaidAmountProof {
		paidAmountProofUrls = append(paidAmountProofUrls, proof.Url)
	}
	// if order CONFIRMED, create on easeyecom and update progress widget
	if orderStatus == "CONFIRMED" {

		_, err := s.HandleConfirmedOrder(ctx, dto.ConfirmOrderRequest{
			Data: dto.ConfirmOrderData{
				UserID:      *orderInfo.UserID,
				OrderID:     request.Data.OrderID,
				Email:       request.Data.UpdatedBy,
				Source:      source,
				OrderStatus: orderStatus,
				Message:     request.Data.OrderMeta.Note,
				OrderMeta: dto.OrderMeta{
					PaidAmount:      request.Data.OrderMeta.PaidAmount,
					PaidAmountProof: request.Data.OrderMeta.PaidAmountProof,
					AdvanceTaken:    request.Data.OrderMeta.AdvanceTaken,
					PaymentId:       request.Data.OrderMeta.PaymentId,
					PaymentDiscount: request.Data.OrderMeta.PaymentDiscount,
					CallStatus:      request.Data.OrderMeta.CallStatus,
					Note:            request.Data.OrderMeta.Note,
				},
			},
		})
		if err != nil {
			return nil, err
		}
	}
	eventObject := map[string]interface{}{
		"distinct_id":             *orderInfo.UserID,
		"order_id":                request.Data.OrderID,
		"order_value":             int(orderDetails.GetOrderValue()),
		"status":                  orderStatus,
		"call_status":             request.Data.OrderMeta.CallStatus,
		"notes":                   request.Data.OrderMeta.Note,
		"ordering_module":         utils.MakeTitleCase(orderInfo.Seller),
		"seller":                  orderInfo.Seller,
		"email":                   request.Data.UpdatedBy,
		"source":                  source,
		"paid_amount":             request.Data.OrderMeta.PaidAmount,
		"paid_amount_proof":       paidAmountProofUrls,
		"shipment_status":         orderStatuses.ShipmentStatus,
		"processing_status":       orderStatuses.ProcessingStatus,
		"display_status":          orderStatuses.DisplayStatus,
		"previous_display_status": orderInfo.DisplayStatus,
		"event_trigger":           "order_status_updated",
	}

	if request.Data.OrderMeta.DeliveredTimestamp != nil {
		eventObject["delivered_timestamp"] = *request.Data.OrderMeta.DeliveredTimestamp
	}
	if request.Data.UpdatedAt != 0 {
		eventObject["time"] = request.Data.UpdatedAt
	}

	s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Status Updated", *orderInfo.UserID, eventObject),
	})

	webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{*orderInfo.UserID},
		EventName:   "Order Status Updated",
		EventObject: eventObject,
	})

	go ordercomm.SendOrderCommunicationToUser(request.Data.OrderID, *orderInfo.UserID, orderStatus, request.Data.OrderStatusType)
	return &dto.UpdateB2BOrderStatusResponse{
		Message: "Order status updated successfully",
	}, nil
}

func (s *Service) UpdateB2BOrderAddress(ctx context.Context, request *dto.UpdateOrderAddressRequest) (*dto.UpdateB2BOrderStatusResponse, error) {
	// get order details json from db
	source := GetSource(request.Data.UpdatedBy, "")
	int64OrderId, err := strconv.ParseInt(request.Data.OrderID, 10, 64)
	if err != nil {
		return nil, err
	}
	orderInfo, _ := GetOrderInfo(s.repository, int64OrderId)
	orderStatus := strings.ToUpper(request.Data.OrderStatus)
	orderStatuses := orderstatus.MapOrderStatus(orderStatus, "", orderstatus.OrderStatusResponse{})
	orderDetails, err := GetOrderDetails(s.repository, request.Data.OrderID)
	if err != nil {
		return nil, err
	}

	err = UpdateAddressInOrderDetails(s.repository, orderDetails, request.Data)
	if err != nil {
		return nil, err
	}

	s.UpdateUserAddress(ctx, dto.UpdateUserAddressRequest{
		UserID: *orderInfo.UserID,
		Address: dto.UserAddress{
			ID:             request.Data.AddressId,
			Name:           request.Data.Name,
			District:       request.Data.City,
			Phone:          request.Data.Phone,
			Neighbourhood:  request.Data.Neighbourhood,
			Village:        request.Data.Village,
			HouseNumber:    request.Data.HouseNumber,
			Landmark:       request.Data.Landmark,
			StoreName:      request.Data.StoreName,
			Tag:            request.Data.Tag,
			State:          request.Data.State,
			PostalCode:     request.Data.PostalCode,
			Line:           request.Data.Line,
			GST:            request.Data.GST,
			AlternatePhone: request.Data.AlternatePhone,
		},
	})

	eventObject := map[string]interface{}{
		"distinct_id":             orderInfo.UserID,
		"order_id":                request.Data.OrderID,
		"order_value":             int(orderDetails.GetOrderValue()),
		"status":                  orderStatus,
		"ordering_module":         utils.MakeTitleCase(orderInfo.Seller),
		"seller":                  orderInfo.Seller,
		"email":                   request.Data.UpdatedBy,
		"source":                  source,
		"shipment_status":         orderStatuses.ShipmentStatus,
		"processing_status":       orderStatuses.ProcessingStatus,
		"display_status":          orderStatuses.DisplayStatus,
		"previous_display_status": orderInfo.DisplayStatus,
		"event_trigger":           "order_address_updated",
	}

	s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Status Updated", *orderInfo.UserID, eventObject),
	})

	webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{*orderInfo.UserID},
		EventName:   "Order Status Updated",
		EventObject: eventObject,
	})

	return &dto.UpdateB2BOrderStatusResponse{
		Message: "Order address updated successfully",
	}, nil
}

func (s *Service) GetPendingShipments(ctx context.Context, request *dto.GetPendingShipmentsRequest) (*[]dto.GetPendingShipmentsResponse, error) {
	query := fmt.Sprintf(`
						SELECT
							DATE_FORMAT(FROM_UNIXTIME(kbr.order_confirmed/1000), '%%Y-%%m-%%d') as date,
							SUM(CASE WHEN ko.display_status = 'CONFIRMED' THEN 1 ELSE 0 END) as pending_count,
							SUM(CASE WHEN ko.display_status NOT IN ('CONFIRMED', 'PLACED', 'PENDING_CONFIRMATION', 'CANCELLED') THEN 1 ELSE 0 END) as processing_count,
							SUM(CASE WHEN ko.display_status = 'CONFIRMED' THEN 1 ELSE 0 END) +
							SUM(CASE WHEN ko.display_status NOT IN ('CONFIRMED', 'PLACED', 'PENDING_CONFIRMATION', 'CANCELLED') THEN 1 ELSE 0 END) as total_count,
							SUM(kop.amount) as total_orders_value
						FROM kiranaclubdb.kiranabazar_orders ko
						JOIN
							kiranaclubdb.kc_bazar_reconciliation kbr
							ON ko.id = kbr.order_id
						JOIN
							kiranaclubdb.kiranabazar_order_payments kop ON ko.id = kop.order_id
						WHERE seller = '%s'
							AND FROM_UNIXTIME(kbr.order_confirmed/1000) >= (CURRENT_DATE - INTERVAL 30 DAY)
							AND (
								ko.display_status = 'CONFIRMED'
								OR ko.display_status NOT IN ('CONFIRMED', 'PLACED', 'PENDING_CONFIRMATION', 'CANCELLED')
							)
						GROUP BY date
						ORDER BY date DESC;
						`, request.Data.Seller)
	var result []dto.GetPendingShipmentsResponse
	_, err := s.repository.CustomQuery(&result, query)
	if err != nil {
		return nil, err
	}
	// Filter out invalid entries
	filteredResult := make([]dto.GetPendingShipmentsResponse, 0)
	for _, r := range result {
		if r.Date != "" && r.TotalCount != 0 {
			filteredResult = append(filteredResult, r)
		}
	}
	return &filteredResult, nil
}

// Add this new function to get seller-wise report
func (s *Service) getSellerWiseReport(ctx context.Context) (map[string]interface{}, error) {
	type SellerReportData struct {
		ConfirmedDate   string  `gorm:"column:confirmed_date"`
		Seller          string  `gorm:"column:seller"`
		RecordCount     int     `gorm:"column:record_count"`
		TotalOrderValue float64 `gorm:"column:total_order_value"`
	}

	// Query to get seller-wise data for last 15 days
	sellerQuery := `
		SELECT
			DATE(FROM_UNIXTIME(kbr.order_confirmed / 1000)) AS confirmed_date,
			kbr.oms AS seller,
			COUNT(*) AS record_count,
			SUM(kbr.gross_value) AS total_order_value
		FROM
			kiranaclubdb.kc_bazar_reconciliation kbr USE INDEX (kc_bazar_reconciliation_order_confirmed_IDX)
		LEFT JOIN kiranabazar_orders ko ON ko.id = kbr.order_id
		WHERE
			DATE(FROM_UNIXTIME(kbr.order_confirmed / 1000)) >= DATE_SUB(CURDATE(), INTERVAL 15 DAY)
			AND kbr.order_confirmed IS NOT NULL
			AND ko.display_status != 'ARCHIVED'
			AND kbr.oms != 'kiranaclub_loyalty_rewards'
		GROUP BY
			confirmed_date,
			kbr.oms
		ORDER BY
			confirmed_date DESC, total_order_value DESC;
	`

	var sellerResults []SellerReportData
	_, err := s.repository.CustomQuery(&sellerResults, sellerQuery)
	if err != nil {
		return nil, err
	}

	// Calculate total order value per seller across all 15 days
	sellerTotals := make(map[string]float64)
	for _, row := range sellerResults {
		sellerTotals[row.Seller] += row.TotalOrderValue
	}

	// Sort sellers by total order value and get top 5
	type SellerTotal struct {
		Seller string
		Total  float64
	}

	var sortedSellers []SellerTotal
	for seller, total := range sellerTotals {
		sortedSellers = append(sortedSellers, SellerTotal{Seller: seller, Total: total})
	}

	sort.Slice(sortedSellers, func(i, j int) bool {
		return sortedSellers[i].Total > sortedSellers[j].Total
	})

	// Get top 5 sellers
	topSellers := make(map[string]bool)
	for i := 0; i < len(sortedSellers) && i < 7; i++ {
		topSellers[sortedSellers[i].Seller] = true
	}

	// Structure the data similar to daily order report
	type SellerOrderData struct {
		Orders map[string]int     `json:"orders"`
		Amount map[string]float64 `json:"amount"`
	}

	sellerReportData := make(map[string]SellerOrderData)

	for _, row := range sellerResults {
		// Format the date to remove the timestamp
		formattedDate := strings.Split(row.ConfirmedDate, "T")[0]

		// Round the value to 2 decimal places
		value := math.Round(row.TotalOrderValue*100) / 100

		// Determine if this seller should be in top 5 or "others"
		sellerKey := row.Seller
		if !topSellers[row.Seller] {
			sellerKey = "others"
		}

		// Initialize the entry for this date if it doesn't exist
		if _, exists := sellerReportData[formattedDate]; !exists {
			sellerReportData[formattedDate] = SellerOrderData{
				Orders: make(map[string]int),
				Amount: make(map[string]float64),
			}
		}

		// Add the data to the map (accumulate for "others")
		sellerReportData[formattedDate].Orders[sellerKey] += row.RecordCount
		sellerReportData[formattedDate].Amount[sellerKey] += value
	}

	// Round the accumulated "others" amounts
	for date, data := range sellerReportData {
		if amount, exists := data.Amount["others"]; exists {
			data.Amount["others"] = math.Round(amount*100) / 100
			sellerReportData[date] = data
		}
	}

	// Generate colors for top sellers
	// colors := []string{"#7367F0", "#E91E63", "#28C76F", "#FF9F43", "#00BAD1", "#FF4C51", "#FFEB78"} // Add more colors if needed
	colors := []string{"#59A14F", "#F28E2B", "#4E79A7", "#E15759", "#76B7B2", "#FF9DA7", "#EDC948"}
	topSellersWithColors := make([]map[string]interface{}, 0)

	for i, seller := range sortedSellers {
		if i < 7 {
			topSellersWithColors = append(topSellersWithColors, map[string]interface{}{
				"seller": seller.Seller,
				"color":  colors[i],
			})
		}
	}

	// Add "others" with grey color
	if len(sortedSellers) > 7 {
		topSellersWithColors = append(topSellersWithColors, map[string]interface{}{
			"seller": "others",
			"color":  "#BDBDBD", // Grey
		})
	}

	return map[string]interface{}{
		"seller_report": map[string]interface{}{
			"data":        sellerReportData,
			"top_sellers": topSellersWithColors,
		},
	}, nil
}

func (s *Service) GetBrandInsights(ctx context.Context, request *dto.GetBrandInsightsRequest) (*map[string]interface{}, error) {
	now := time.Now()
	startDate := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	endDate := startDate.AddDate(0, 0, -1)
	if request.Data.TimeRange == "last_month" {
		startDate = startDate.AddDate(0, -1, 0)
	}
	startDateStr := startDate.Format("2006-01-02")
	endDateStr := endDate.Format("2006-01-02")
	yesterdayDate := now.AddDate(0, 0, -1).Format("2006-01-02")

	type BrandData struct {
		Id     int    `json:"id"`
		Seller string `json:"seller"`
		Source string `json:"source"`
		Label  string `json:"label"`
	}
	type DispatchData struct {
		Duration string  `json:"duration"`
		Orders   int64   `json:"orders"`
		Sales    float64 `json:"sales"`
		Share    float64 `json:"share"`
	}
	type SalesData struct {
		Sales  float64 `json:"sales"`
		Orders int64   `json:"orders"`
		Share  float64 `json:"share"`
	}
	type UniqueMetrics struct {
		Retailers int64 `json:"retailers"`
		Pincodes  int64 `json:"pincodes"`
	}
	type InsightsDataResponse struct {
		ThisMonth         SalesData      `json:"this_month,omitempty"`
		Today             SalesData      `json:"today,omitempty"`
		Yesterday         SalesData      `json:"yesterday,omitempty"`
		AverageOrderValue float64        `json:"average_order_value,omitempty"`
		Dispatch          []DispatchData `json:"dispatch,omitempty"`
		Pending           SalesData      `json:"pending,omitempty"`
		Delivered         SalesData      `json:"delivered,omitempty"`
		Returned          SalesData      `json:"returned,omitempty"`
		OverAllPending    SalesData      `json:"overall_pending,omitempty"`
	}

	calculateOrdersShare := func(pendingOrders int64, totalOrders int64) float64 {
		if totalOrders == 0 {
			return 0
		}
		return math.Round((float64(pendingOrders)/float64(totalOrders))*100*100) / 100
	}

	insightsData := InsightsDataResponse{}
	orderSalesQuery := `
						select
							sum(kbr.gross_value) as sales, count(kbr.order_id) as orders
						from
							kiranaclubdb.kc_bazar_reconciliation kbr USE INDEX (kc_bazar_reconciliation_order_confirmed_IDX)
							left join kiranabazar_orders ko on ko.id = kbr.order_id
						where
						`
	uniqueMetricsQuery := `
						select
							count(distinct(ko.user_id)) as retailers, count(distinct(ug.geo_pincode)) as pincodes
						from
							kiranaclubdb.kc_bazar_reconciliation kbr USE INDEX (kc_bazar_reconciliation_order_confirmed_IDX)
						left join kiranabazar_orders ko on ko.id = kbr.order_id
						left join users_geography ug on ko.user_id = ug.user_id
						where
						`
	dispatchQuery := `
						SELECT 
							CASE 
								WHEN (kbr.order_shipment_created - kbr.order_confirmed) <= 86400000 THEN '24 Hours'
								WHEN (kbr.order_shipment_created - kbr.order_confirmed) > 86400000 AND (kbr.order_shipment_created - kbr.order_confirmed) <= 172800000 THEN '24 - 48 Hours'
								ELSE 'More than 48 Hours'
							END AS duration,
							COUNT(*) AS orders,
							SUM(kbr.gross_value) AS sales
						FROM kc_bazar_reconciliation kbr
						WHERE kbr.order_confirmed IS NOT NULL 
						AND kbr.order_shipment_created IS NOT NULL
						AND kbr.order_shipment_created >= kbr.order_confirmed 
					`

	// group and get data (confirmed, shipment created, dispatched) for last 14 days
	groupOrderQuery := `
			SELECT
			DATE(FROM_UNIXTIME(kbr.order_confirmed / 1000)) AS confirmed_date,
			CASE
				WHEN ko.display_status = 'CONFIRMED' THEN 'Confirmed Only'
				WHEN ko.display_status = 'SHIPMENT_CREATED' THEN 'Shipment Created'
				WHEN ko.display_status in ('DELIVERED', 'RETURNED', 'IN_TRANSIT', 'NDR') THEN 'Dispatched'
				ELSE 'Other'
			END AS order_status,
			COUNT(*) AS record_count,
			SUM(kbr.gross_value) AS total_order_value
		FROM
			kiranaclubdb.kc_bazar_reconciliation kbr USE INDEX (kc_bazar_reconciliation_order_confirmed_IDX)
		LEFT JOIN kiranabazar_orders ko ON ko.id = kbr.order_id
		WHERE
			DATE(FROM_UNIXTIME(kbr.order_confirmed / 1000)) >= DATE_SUB(CURDATE(), INTERVAL 14 DAY)
			AND kbr.order_confirmed IS NOT NULL
	`

	if len(request.Data.Seller) > 0 {
		orderSalesQuery += fmt.Sprintf(` kbr.oms in ('%s') and`, strings.Join(request.Data.Seller, "','"))
		uniqueMetricsQuery += fmt.Sprintf(` kbr.oms in ('%s') and`, strings.Join(request.Data.Seller, "','"))
		dispatchQuery += fmt.Sprintf(` AND kbr.oms in ('%s')`, strings.Join(request.Data.Seller, "','"))
		groupOrderQuery += fmt.Sprintf(` AND kbr.oms in ('%s')`, strings.Join(request.Data.Seller, "','"))
	} else {
		orderSalesQuery += ` kbr.oms != 'kiranaclub_loyalty_rewards' and`
		uniqueMetricsQuery += ` kbr.oms != 'kiranaclub_loyalty_rewards' and`
		dispatchQuery += ` AND kbr.oms != 'kiranaclub_loyalty_rewards'`
		groupOrderQuery += ` AND kbr.oms != 'kiranaclub_loyalty_rewards'`
	}

	yesterdayQuery := orderSalesQuery
	todayQuery := orderSalesQuery
	pendingQuery := orderSalesQuery
	returnedQuery := orderSalesQuery
	deliveredQuery := orderSalesQuery
	orderSalesQuery += fmt.Sprintf(` Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) >= '%s' and ko.display_status != 'ARCHIVED'`, startDateStr)
	yesterdayQuery += fmt.Sprintf(` Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) = '%s' and ko.display_status != 'ARCHIVED';`, yesterdayDate)
	todayQuery += fmt.Sprintf(` Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) = '%s' and ko.display_status != 'ARCHIVED';`, now.Format("2006-01-02"))
	uniqueMetricsQuery += fmt.Sprintf(` Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) >= '%s' and ko.display_status != 'ARCHIVED'`, startDateStr)
	returnedQuery += fmt.Sprintf(` Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) >= '%s'`, startDateStr)
	deliveredQuery += fmt.Sprintf(` Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) >= '%s'`, startDateStr)
	dispatchQuery += fmt.Sprintf(`AND Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) >= '%s'`, startDateStr)
	pendingQuery += `		kbr.order_shipment_created is null 
							and kbr.order_delivered is null
							and kbr.order_cancelled is null
							and ko.display_status != 'ARCHIVED'`
	overallPendingQuery := pendingQuery + ` and Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) >= '2025-02-01' and ko.display_status != 'ARCHIVED';`
	pendingQuery += fmt.Sprintf(` and Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) >= '%s'`, startDateStr)
	if request.Data.TimeRange == "last_month" {
		orderSalesQuery += fmt.Sprintf(` and Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) <= '%s';`, endDateStr)
		uniqueMetricsQuery += fmt.Sprintf(` and Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) <= '%s';`, endDateStr)
		returnedQuery += fmt.Sprintf(` and Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) <= '%s'`, endDateStr)
		deliveredQuery += fmt.Sprintf(` and Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) <= '%s'`, endDateStr)
		dispatchQuery += fmt.Sprintf(` AND Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) <= '%s'`, endDateStr)
		pendingQuery += fmt.Sprintf(` and Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) <= '%s';`, endDateStr)
	}
	returnedQuery += fmt.Sprintf(` and ko.display_status = '%s';`, displaystatus.RETURNED)
	deliveredQuery += fmt.Sprintf(` and ko.display_status = '%s';`, displaystatus.DELIVERED)
	dispatchQuery += `
						GROUP BY duration
						ORDER BY
								CASE
									WHEN duration = '24 Hours' THEN 1
									WHEN duration = '24 - 48 Hours' THEN 2
									ELSE 3
								END;
					`
	groupOrderQuery += `
			GROUP BY
				confirmed_date,
				CASE
					WHEN ko.display_status = 'CONFIRMED' THEN 'Confirmed Only'
					WHEN ko.display_status = 'SHIPMENT_CREATED' THEN 'Shipment Created'
					WHEN ko.display_status IN ('DELIVERED', 'RETURNED', 'IN_TRANSIT', 'NDR') THEN 'Dispatched'
					ELSE 'Other'
				END
			ORDER BY
				confirmed_date DESC;
	`

	_, err := s.repository.CustomQuery(&insightsData.ThisMonth, orderSalesQuery)
	if err != nil {
		return nil, err
	}
	_, err = s.repository.CustomQuery(&insightsData.Yesterday, yesterdayQuery)
	if err != nil {
		return nil, err
	}
	_, err = s.repository.CustomQuery(&insightsData.Today, todayQuery)
	if err != nil {
		return nil, err
	}

	_, err = s.repository.CustomQuery(&insightsData.Dispatch, dispatchQuery)
	if err != nil {
		return nil, err
	}
	_, err = s.repository.CustomQuery(&insightsData.Pending, pendingQuery)
	if err != nil {
		return nil, err
	}
	_, err = s.repository.CustomQuery(&insightsData.OverAllPending, overallPendingQuery)
	if err != nil {
		return nil, err
	}
	_, err = s.repository.CustomQuery(&insightsData.Returned, returnedQuery)
	if err != nil {
		return nil, err
	}
	_, err = s.repository.CustomQuery(&insightsData.Delivered, deliveredQuery)
	if err != nil {
		return nil, err
	}

	type OrderStatusGrouping struct {
		Orders map[string]int     `json:"orders"`
		Amount map[string]float64 `json:"amount"`
	}

	type DailyReport map[string]OrderStatusGrouping

	var groupOrderResults []struct {
		ConfirmedDate   string  `gorm:"column:confirmed_date"`
		OrderStatus     string  `gorm:"column:order_status"`
		RecordCount     int     `gorm:"column:record_count"`
		TotalOrderValue float64 `gorm:"column:total_order_value"`
	}
	_, err = s.repository.CustomQuery(&groupOrderResults, groupOrderQuery)

	if err != nil {
		return nil, err
	}

	dailyReportData := make(DailyReport)
	for _, row := range groupOrderResults {
		// Format the date to remove the timestamp
		formattedDate := strings.Split(row.ConfirmedDate, "T")[0]

		// Round the value to 2 decimal places
		value := math.Round(row.TotalOrderValue*100) / 100

		// Convert status to the desired format
		statusKey := ""
		switch row.OrderStatus {
		case "Confirmed Only":
			statusKey = "confirmed"
		case "Shipment Created":
			statusKey = "shipment_created"
		case "Dispatched":
			statusKey = "dispatched"
		default:
			statusKey = "other"
		}

		// Initialize the entry for this date if it doesn't exist
		if _, exists := dailyReportData[formattedDate]; !exists {
			dailyReportData[formattedDate] = OrderStatusGrouping{
				Orders: make(map[string]int),
				Amount: make(map[string]float64),
			}
		}

		// Add the data to the map
		dailyReportData[formattedDate].Orders[statusKey] = row.RecordCount
		dailyReportData[formattedDate].Amount[statusKey] = value
	}

	if insightsData.ThisMonth.Orders > 0 {
		insightsData.AverageOrderValue = math.Round((float64(insightsData.ThisMonth.Sales/float64(insightsData.ThisMonth.Orders)))*100) / 100
	}

	expectedDurations := []string{"24 Hours", "24 - 48 Hours", "More than 48 Hours"}
	durationMap := make(map[string]DispatchData)
	for _, stat := range insightsData.Dispatch {
		durationMap[stat.Duration] = stat
	}

	filteredDispatch := []DispatchData{}
	for _, duration := range expectedDurations {
		if stat, exists := durationMap[duration]; exists {
			stat.Share = calculateOrdersShare(stat.Orders, insightsData.ThisMonth.Orders)
			filteredDispatch = append(filteredDispatch, stat)
		} else {
			filteredDispatch = append(filteredDispatch, DispatchData{
				Duration: duration,
				Orders:   0,
				Sales:    0.0,
				Share:    0.0,
			})
		}
	}
	filteredDispatch = append(filteredDispatch, DispatchData{
		Duration: "Pending Dispatches",
		Orders:   insightsData.Pending.Orders,
		Sales:    insightsData.Pending.Sales,
		Share:    calculateOrdersShare(insightsData.Pending.Orders, insightsData.ThisMonth.Orders),
	})

	insightsData.Delivered.Share = calculateOrdersShare(insightsData.Delivered.Orders, insightsData.ThisMonth.Orders)
	insightsData.Returned.Share = calculateOrdersShare(insightsData.Returned.Orders, insightsData.ThisMonth.Orders)

	insightsData.Dispatch = filteredDispatch

	brandsData := make([]BrandData, 0)

	for source, seller := range brands.GetSourceSellerMap() {
		if strings.Contains(source, "third_party") {
			bmeta, _ := brands.GetBrandMetaBySeller(seller)

			brandData := BrandData{
				Seller: seller,
				Source: source,
				Label:  bmeta.Name,
			}
			brandsData = append(brandsData, brandData)
		}
	}

	// sort array alphabetically
	sort.Slice(brandsData, func(i, j int) bool {
		return strings.ToLower(brandsData[i].Label) < strings.ToLower(brandsData[j].Label)
	})
	tempBrandId := 1
	for i := range brandsData {
		brandsData[i].Id = tempBrandId
		tempBrandId++
	}

	response := map[string]interface{}{
		"data":         insightsData,
		"brands":       brandsData,
		"order_report": dailyReportData,
	}

	// Add seller report only if:
	// 1. No seller filter is applied
	// 2. Time range is "this_month" (not "last_month")
	if len(request.Data.Seller) == 0 && request.Data.TimeRange != "last_month" {
		sellerReportData, err := s.getSellerWiseReport(ctx)
		if err != nil {
			return nil, err
		}

		// Add seller_report to response at same level as order_report
		for key, value := range sellerReportData {
			response[key] = value
		}
	}

	return &response, nil
}

func (s *Service) GetCarriers(ctx context.Context) (response dto.GetCarrierResponse, err error) {
	for _, courier := range couriers.GetAllCouriers() {
		response.Carriers = append(response.Carriers, dto.Carrier{
			ID:          courier.ID,
			DisplayName: courier.CourierName,
			CourierName: courier.CourierName,
			Image:       courier.Image,
		})
	}
	return
}

func (s *Service) UpdateOrderMetaData(ctx context.Context, request *dto.UpdateOrderReasonsRequest) (response *dto.UpdateOrderReasonsResponse, err error) {
	if request.Data.OrderID == "" || request.UpdatedBy == "" {
		err = errors.New("invalid orderID or updatedBy")
		return
	}
	source := GetSource(request.Data.UpdatedBy, "")
	int64OrderId, err := strconv.ParseInt(request.Data.OrderID, 10, 64)
	if err != nil {
		return nil, err
	}
	orderInfo, _ := GetOrderInfo(s.repository, int64OrderId)
	orderDetails, _ := GetOrderDetails(s.repository, request.Data.OrderID)
	orderStatus := strings.ToUpper(request.Data.OrderStatus)
	orderStatuses := orderstatus.MapOrderStatus(orderStatus, "", orderstatus.OrderStatusResponse{})

	updatedAt := time.Now().UnixMilli()
	if request.Data.UpdatedAt != nil {
		updatedAt = *request.Data.UpdatedAt
	}
	err = UpdateCallStatus(s.repository, request.Data.OrderID, request.Data.CallStatus, request.Data.Note, request.Data.UpdatedBy, updatedAt, request.Data.CancelReason, request.Data.ReturnedReason, request.Data.ReturnReceivedNote)
	if err != nil {
		return
	}

	if request.Data.OrderStatus == *orderInfo.OrderStatus {
		eventObject := map[string]interface{}{
			"distinct_id":             orderInfo.UserID,
			"order_id":                request.Data.OrderID,
			"order_value":             int(orderDetails.GetOrderValue()),
			"status":                  orderStatus,
			"call_status":             request.Data.CallStatus,
			"notes":                   request.Data.Note,
			"ordering_module":         utils.MakeTitleCase(orderInfo.Seller),
			"seller":                  orderInfo.Seller,
			"email":                   request.Data.UpdatedBy,
			"source":                  source,
			"shipment_status":         orderStatuses.ShipmentStatus,
			"processing_status":       orderStatuses.ProcessingStatus,
			"display_status":          orderStatuses.DisplayStatus,
			"previous_display_status": orderInfo.DisplayStatus,
			"event_trigger":           "order_notes_updated",
		}
		if request.Data.CancelReason != "" {
			eventObject["cancel_reason"] = request.Data.CancelReason
		}

		if request.Data.ReturnedReason != "" {
			eventObject["return_reason"] = request.Data.ReturnedReason
		}

		if request.Data.UpdatedAt != nil && *request.Data.UpdatedAt != 0 {
			// eventObject["time"] = request.Data.UpdatedAt
		}

		s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
			s.Mixpanel.NewEvent("Order Status Updated", *orderInfo.UserID, eventObject),
		})
		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:     []string{*orderInfo.UserID},
			EventName:   "Order Status Updated",
			EventObject: eventObject,
		})

	}

	return &dto.UpdateOrderReasonsResponse{
		Message: "Order notes updated successfully",
	}, nil
}

func (s *Service) ArchiveOrder(ctx context.Context, request *dto.ArchiveOrderRequest) (response *dto.ArchiveOrderResponse, err error) {
	source := "D2R"
	if request.Data.UpdatedBy == "IVR" {
		source = "AUTOMATION"
	} else if request.Data.UpdatedBy == "SHIPWAY" {
		source = "AUTOMATION"
	}
	int64OrderId, err := strconv.ParseInt(request.Data.OrderID, 10, 64)
	if err != nil {
		return nil, err
	}
	orderInfo, err := GetOrderInfo(s.repository, int64OrderId)
	if err != nil {
		return nil, err
	}
	orderDetails, err := GetOrderDetails(s.repository, request.Data.OrderID)
	if err != nil {
		return nil, err
	}
	orderStatus := strings.ToUpper(constants.ARCHIVED)
	orderStatuses := orderstatus.MapOrderStatus(orderStatus, "",
		orderstatus.OrderStatusResponse{
			DisplayStatus:    orderInfo.DisplayStatus,
			ShipmentStatus:   orderInfo.DeliveryStatus,
			ProcessingStatus: orderInfo.ProcessingStatus,
			PaymentStatus:    orderInfo.PaymentStatus,
		})

	kbOrdersQuery := fmt.Sprintf("UPDATE kiranaclubdb.kiranabazar_orders SET delivery_status='%s', processing_status='%s', display_status='%s', is_archived = 1 WHERE id = %d",
		orderStatuses.ShipmentStatus, orderStatuses.ProcessingStatus, orderStatuses.DisplayStatus, int64OrderId)
	_, err = s.repository.CustomQuery(nil, kbOrdersQuery)
	if err != nil {
		return nil, err
	}
	kbrOrdersQuery := fmt.Sprintf("UPDATE kiranaclubdb.kc_bazar_reconciliation SET is_archived = 1 WHERE order_id = %d", int64OrderId)
	_, err = s.repository.CustomQuery(nil, kbrOrdersQuery)
	if err != nil {
		return nil, err
	}

	s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Archived", *orderInfo.UserID, map[string]interface{}{
			"distinct_id":       orderInfo.UserID,
			"order_id":          request.Data.OrderID,
			"seller":            orderInfo.Seller,
			"email":             request.Data.UpdatedBy,
			"source":            source,
			"order_value":       int(orderDetails.GetOrderValue()),
			"status":            orderInfo.OrderStatus,
			"shipment_status":   orderStatuses.ShipmentStatus,
			"processing_status": orderStatuses.ProcessingStatus,
			"display_status":    orderStatuses.DisplayStatus,
			"call_status":       request.Data.CallStatus,
			"notes":             request.Data.Note,
		}, fmt.Sprintf("%s_%s", "order_archived", request.Data.OrderID)),
	})

	return &dto.ArchiveOrderResponse{
		Message: "Order archived successfully",
	}, nil
}

func (s *Service) GetB2BCategories(ctx context.Context, request *dto.GetB2BCategoriesRequest) (interface{}, error) {
	var categories []dao.KiranaBazarCategory

	// Build the WHERE clause and collect args
	whereClause, args := buildWhereClause(request.Data.Filters)

	// Build the main query
	query := strings.Builder{}
	query.WriteString("SELECT * FROM kiranabazar_categories WHERE 1=1")
	query.WriteString(whereClause)

	// Apply ordering
	if len(request.Data.OrderBy) > 0 {
		query.WriteString(" ORDER BY")
		for i, order := range request.Data.OrderBy {
			if i > 0 {
				query.WriteString(",")
			}
			query.WriteString(fmt.Sprintf(" %s %s", order.Key, order.Value))
		}
	}

	// Apply pagination
	if request.Data.Meta.Limit > 0 {
		query.WriteString(" LIMIT ? OFFSET ?")
		args = append(args, request.Data.Meta.Limit, request.Data.Meta.Offset)
	}

	// Build count query using the same where clause
	countQuery := strings.Builder{}
	countQuery.WriteString("SELECT COUNT(*) FROM kiranabazar_categories WHERE 1=1")
	countQuery.WriteString(whereClause)

	// Execute count query
	var total int64
	if err := s.repository.Db.Raw(countQuery.String(), args[:len(args)-2]...).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("error getting total count: %w", err)
	}

	// Execute main query
	fmt.Println("query.String() = ", query.String())
	if err := s.repository.Db.Raw(query.String(), args...).Scan(&categories).Error; err != nil {
		return nil, fmt.Errorf("error fetching categories: %w", err)
	}

	// Prepare response
	response := map[string]interface{}{
		"categories": categories,
		"meta": map[string]interface{}{
			"total":  total,
			"limit":  request.Data.Meta.Limit,
			"offset": request.Data.Meta.Offset,
		},
	}
	return response, nil
}

// buildWhereClause creates the WHERE clause and returns it along with the args
func buildWhereClause(filters dto.Filters) (string, []interface{}) {
	var conditions []string
	var args []interface{}

	if len(filters.Sellers) > 0 {
		placeholders := make([]string, len(filters.Sellers))
		for i := range filters.Sellers {
			placeholders[i] = "?"
			source, exists := brands.GetSourceBySeller(filters.Sellers[i])
			if !exists {
				slack.SendSlackMessage(fmt.Sprintf("Source not found for seller: %s from build where clause", filters.Sellers[i]))
				continue
			}
			args = append(args, source)
		}
		conditions = append(conditions, fmt.Sprintf("source IN (%s)", strings.Join(placeholders, ",")))
	}

	if len(filters.CategoryIds) > 0 {
		placeholders := make([]string, len(filters.CategoryIds))
		for i := range filters.CategoryIds {
			placeholders[i] = "?"
			args = append(args, filters.CategoryIds[i])
		}
		conditions = append(conditions, fmt.Sprintf("id IN (%s)", strings.Join(placeholders, ",")))
	}

	if filters.IsActive != nil {
		conditions = append(conditions, "is_active = ?")
		args = append(args, *filters.IsActive)
	}

	if len(filters.CategoryCodes) > 0 {
		placeholders := make([]string, len(filters.CategoryCodes))
		for i := range filters.CategoryCodes {
			placeholders[i] = "?"
			args = append(args, filters.CategoryCodes[i])
		}
		conditions = append(conditions, fmt.Sprintf("code IN (%s)", strings.Join(placeholders, ",")))
	}

	if len(filters.CategoryNames) > 0 {
		placeholders := make([]string, len(filters.CategoryNames))
		for i := range filters.CategoryNames {
			placeholders[i] = "?"
			args = append(args, filters.CategoryNames[i])
		}
		conditions = append(conditions, fmt.Sprintf("category IN (%s)", strings.Join(placeholders, ",")))
	}

	if len(conditions) > 0 {
		return " AND " + strings.Join(conditions, " AND "), args
	}
	return "", args
}

func (s *Service) GetB2BProducts(ctx context.Context, request *dto.GetB2BProductsRequest) (interface{}, error) {
	sellers := request.Data.Filters.Sellers
	productIds := request.Data.Filters.ProductIds
	var requestedProducts []*products.Product
	if len(productIds) > 0 {
		for _, productIdStr := range productIds {
			productId, err := strconv.Atoi(productIdStr)
			if err != nil {
				continue // Skip invalid product IDs
			}
			product, exists := products.GetProductByID(productId)
			if exists {
				requestedProducts = append(requestedProducts, product)
				if !slices.Contains(sellers, product.Seller) {
					sellers = append(sellers, product.Seller)
				}
			}
		}
		requestedProducts = products.Products(requestedProducts).RemoveSelfVariantReferences()
	} else {
		if len(sellers) == 0 && request.Source == "B2B_INTERNAL" {
			for _, seller := range brands.GetSourceSellerMap() {
				if !slices.Contains([]string{"undefined", "lots"}, request.Source) {
					sellers = append(sellers, seller)
				}
			}
		}
		for _, seller := range sellers {
			source, exists := brands.GetSourceBySeller(seller)

			if !exists {
				slack.SendSlackMessage(fmt.Sprintf("Source not found for seller: %s from GetB2BProducts -- 1", seller))
			}

			if exists {
				// Fetch products for each seller
				sellerProducts := products.Products(products.GetProductsBySource(source)).RemoveSelfVariantReferences()
				requestedProducts = append(requestedProducts, sellerProducts...)
			}
		}
	}
	allProducts := []shared.SellerItemsB2B{}
	for _, j := range requestedProducts {
		productInventory, err := products.GetProductInventory(int(j.ID))
		if err != nil {
			return nil, err
		}
		if productInventory != nil {
			j.InventoryAnalysis = *productInventory
		}
		isActive := j.IsActive != nil && *j.IsActive
		if slices.Contains([]string{"B2B_EXTERNAL", "B2B_CREATE_ORDER", "B2B_EDIT_ORDER"}, request.Source) {
			if isActive {
				j1 := j.ToSellerItemsB2B(products.ToSellerItemsCondition{
					IncludeVariant: false,
				})
				allProducts = append(allProducts, j1)
			}
		} else if request.Source == "B2B_INTERNAL" {
			if request.Data.Filters.ShowMerged != nil && *request.Data.Filters.ShowMerged {
				j1 := j.ToSellerItemsB2B(products.ToSellerItemsCondition{
					IncludeVariant: true,
				})
				// if get products by product ids, then we need to show all products
				if len(productIds) == 0 {
					if j.IsDefault != nil && *j.IsDefault {
						allProducts = append(allProducts, j1)
					}
				} else {
					allProducts = append(allProducts, j1)
				}
			} else {
				j1 := j.ToSellerItemsB2B(products.ToSellerItemsCondition{
					IncludeVariant: false,
				})
				allProducts = append(allProducts, j1)
			}
		} else {
			if isActive {
				j1 := j.ToSellerItemsB2B(products.ToSellerItemsCondition{
					IncludeVariant: false,
				})
				allProducts = append(allProducts, j1)
			}
		}
	}

	var sellerCategories []dto.SearchCategory
	for _, seller := range sellers {
		source, exists := brands.GetSourceBySeller(seller)

		if !exists {
			slack.SendSlackMessage(fmt.Sprintf("Source not found for seller: %s from GetB2BProducts -- 2", seller))
		}

		if exists {
			// Fetch products for each seller
			categories, err := s.GetCategories(ctx, &dto.GetCategoriesRequest{
				Domain:    "Retail",
				SubDomain: "Grocery",
				Source:    source,
			})
			if err != nil {
				return nil, err
			}
			sellerCategories = append(sellerCategories, categories.Categories...)
		}
	}

	return map[string]interface{}{
		"products":   allProducts,
		"categories": sellerCategories,
	}, nil
}

func (s *Service) GetActiveVirtualSkus(productId int) ([]int, error) {
	var activeSkus []int
	query := fmt.Sprintf(`SELECT virtual_sku_id FROM kiranabazar_virtual_sku_mapping WHERE is_active = 1 AND product_id = %d`, productId)
	_, err := s.repository.CustomQuery(&activeSkus, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching active virtual SKUs: %w", err)
	}
	return activeSkus, nil
}

func (s *Service) AddProduct(ctx context.Context, request *dto.AddProductRequest) (interface{}, error) {
	imageUrlsJSON, err := json.Marshal(request.Data.ImageUrls)
	if err != nil {
		return nil, err
	}

	mediaUrls := make([]dao.KiranaBazarProductMediaUrl, 0)
	if request.Data.ImageUrls != nil {
		for _, url := range request.Data.ImageUrls {
			if strings.TrimSpace(url) != "" {
				mediaUrls = append(mediaUrls, dao.KiranaBazarProductMediaUrl{
					Url: url,
				})
			}
		}
	}

	mediaUrlsJson, err := json.Marshal(mediaUrls)
	if err != nil {
		return nil, err
	}

	markupMarginFloat, markupMarginString, brandWholesaleRate, wholesaleRate, err := products.GetRateAndMargin(request.Data.WholesaleRate, request.Data.BrandWholesaleRate, request.Data.MRPNumber)
	if err != nil {
		return nil, err
	}
	categoryData, exists := products.GetCategoryByID(request.Data.CategoryID)
	if !exists {
		return nil, errors.New("category not found")
	}
	if (strings.TrimSpace(request.Data.Name) == "") || (strings.TrimSpace(request.Data.Code) == "") || (strings.TrimSpace(request.Data.HindiName) == "") {
		return nil, errors.New("product name, code and hindi name cannot be empty")
	}
	expiresIn := *request.Data.ExpiresAt - time.Now().UnixMilli()
	var badgeText *string
	if request.Data.BadgeText != nil {
		spaceTrimmedText := strings.TrimSpace(*request.Data.BadgeText)
		if spaceTrimmedText != "" {
			badgeText = &spaceTrimmedText
		}
	}
	if categoryData.Source == nil {
		return nil, errors.New("category source is not defined")
	}
	seller := strings.TrimPrefix(*categoryData.Source, "third_party_")
	productMeta := shared.KiranaBazarProductMeta{
		Description:         request.Data.Description,
		HindiName:           request.Data.HindiName,
		Quantity:            request.Data.Quantity,
		PackSize:            request.Data.PackSize,
		CaseSize:            request.Data.CaseSize,
		MRPNumber:           request.Data.MRPNumber,
		MRPString:           fmt.Sprintf(`MRP: ₹%d`, int(request.Data.MRPNumber)),
		MarkupMargin:        markupMarginFloat,
		MarkupMarginString:  markupMarginString,
		WholesaleRate:       wholesaleRate,
		WholesaleRateString: fmt.Sprintf(`₹%.1f`, wholesaleRate),
		BrandWholesaleRate:  brandWholesaleRate,
		Tax:                 request.Data.Tax,
		HSNCode:             request.Data.HSNCode,
		ExpiresIn:           &expiresIn,
		BadgeText:           badgeText,
	}
	productMetaJSON, err := json.Marshal(productMeta)
	if err != nil {
		return nil, err
	}

	var topBadgeStyles *dao.TopBadgeStyles
	if request.Data.TopBadgeStyles != nil {
		if request.Data.TopBadgeStyles.Color != nil && request.Data.TopBadgeStyles.BgColor != nil {
			topBadgeStyles = &dao.TopBadgeStyles{
				Color:   *request.Data.TopBadgeStyles.Color,
				BgColor: request.Data.TopBadgeStyles.BgColor,
			}
		}
	}

	productExtendedMeta := dao.KiranaBazarProductExtendedMeta{
		TopBadgeStyles: topBadgeStyles,
	}
	productExtendedMetaJSON, err := json.Marshal(productExtendedMeta)
	if err != nil {
		return nil, err
	}
	productType := "product"
	if request.Data.ProductType != nil {
		productType = *request.Data.ProductType
	}
	newProduct := false
	if request.Data.SizeVariantCode == nil {
		newProduct = true
	}
	nameLabel := fmt.Sprintf("%s Pack of %d", request.Data.Name, request.Data.PackSize)
	productData := dao.KiranaBazarProduct{
		CategoryID:      request.Data.CategoryID,
		Name:            request.Data.Name,
		Code:            request.Data.Code,
		Rank:            0,
		CreatedAt:       time.Now().UTC(),
		UpdatedAt:       time.Now().UTC(),
		Meta:            productMetaJSON,
		ExtendedMeta:    productExtendedMetaJSON,
		IsActive:        false,
		IsOos:           false,
		IsDefault:       newProduct,
		ImageUrls:       imageUrlsJSON,
		NameLabel:       nameLabel,
		CodeLabel:       request.Data.Code,
		RatingsSum:      0,
		RatingsCount:    0,
		PopularityValue: 100,
		Seller:          seller,
		MediaUrls:       mediaUrlsJson,
		ProductType:     productType,
	}
	newProductData, err := s.repository.Create(&productData)
	if err != nil {
		return nil, err
	}

	productID := reflect.ValueOf(newProductData).Elem().Field(0).Int()

	var sizeVariantCode int64
	if newProduct {
		sizeVariantCode = productID
	} else {
		sizeVariantCode = *request.Data.SizeVariantCode
	}
	_, err = s.repository.CustomQuery(nil, fmt.Sprintf("Update kiranabazar_products SET size_variant_code = %d WHERE id = %d;", sizeVariantCode, productID))
	if err != nil {
		return nil, err
	}

	screenTag := "screen:products"
	if request.Data.ProductScreenTag != nil && *request.Data.ProductScreenTag != "" {
		screenTag = strings.TrimSpace(*request.Data.ProductScreenTag)
	}

	newProductEntityMapping := dao.KiranaBazarEntitiesMapping{
		EntityId: uint(request.Data.CategoryID),
		TargetId: fmt.Sprint(productID),
		Type:     "product",
		Tag:      screenTag,
		IsActive: true,
	}
	_, err = s.repository.Create(&newProductEntityMapping)
	if err != nil {
		return nil, err
	}

	products.RefreshCacheExternalCall()

	// log product addition
	_, err = s.repository.Create(&dao.ProductDataUpdateHistory{
		ProductID:     int(productID),
		UpdatedBy:     request.UpdatedBy,
		UpdatedAt:     time.Now().UnixMilli(),
		FieldName:     "add_product",
		PreviousValue: "",
		UpdatedValue:  "",
		RequestSource: request.RequestSource,
	})
	if err != nil {
		return nil, err
	}

	updatedProduct, _ := products.GetProductByID(productID)
	updatedProductData := updatedProduct.ToSellerItemsB2B(products.ToSellerItemsCondition{IncludeVariant: true})
	return &dto.UpdateProductDetailsResponse{
		Product: &updatedProductData,
		Message: "Product added successfully",
	}, nil
}

func (s *Service) UpdateProductDetails(ctx context.Context, request *dto.UpdateProductDetailsRequest) (*dto.UpdateProductDetailsResponse, error) {
	productData, exists := products.GetProductByID(request.Data.ProductID)
	if !exists {
		return nil, errors.New("product not found")
	}

	categoryData, exists := products.GetCategoryByID(productData.CategoryID)
	if !exists {
		return nil, errors.New("category not found")
	}
	productSource, exists := brands.GetSourceBySeller(request.Data.Seller)

	if !exists {
		slack.SendSlackMessage(fmt.Sprintf("Source not found for seller: %s from update prod details", request.Data.Seller))
		return nil, errors.New("source not found for seller")
	}

	if productSource != *categoryData.Source {
		return nil, errors.New("product does not belong to the seller")
	}

	updateLog, err := products.UpdateProductData(request, productData)
	if err != nil {
		return nil, err
	}

	for _, updateItem := range request.Data.Updates {
		if updateItem.Key == "is_oos" && updateItem.Value == true {
			// If the product is marked as OOS, we need to update the stock status
			err := s.Inventory.MarkProductOOS(ctx, request.Data.ProductID, request.Data.Seller, request.UpdatedBy)
			if err != nil {
				return nil, fmt.Errorf("failed to mark product as OOS: %w", err)
			}

			// check if the product is present in any of the active virtual skus we will need to mark them as oos
			activeVirtualSkus, err := s.GetActiveVirtualSkus(request.Data.ProductID)
			if err != nil {
				slack.SendSlackMessage(fmt.Sprintf("Error fetching active virtual SKUs for product ID %d: %s", request.Data.ProductID, err.Error()))
			}

			if len(activeVirtualSkus) > 0 {
				for _, vistualSkuId := range activeVirtualSkus {
					err := s.Inventory.MarkProductOOS(ctx, vistualSkuId, request.Data.Seller, request.UpdatedBy)
					if err != nil {
						return nil, fmt.Errorf("failed to mark product as OOS: %w", err)
					}
				}
			}
			break
		}
	}

	// to refresh cache
	products.RefreshCacheExternalCall()

	for _, logItem := range updateLog {
		log := logItem.(map[string]interface{})
		_, err := s.repository.Create(&dao.ProductDataUpdateHistory{
			ProductID:     request.Data.ProductID,
			UpdatedBy:     request.UpdatedBy,
			UpdatedAt:     time.Now().UnixMilli(),
			FieldName:     log["key"].(string),
			PreviousValue: fmt.Sprintf("%v", log["previous_value"]),
			UpdatedValue:  fmt.Sprintf("%v", log["updated_value"]),
			RequestSource: request.RequestSource,
		})
		if err != nil {
			return nil, err
		}
	}
	updatedProduct, _ := products.GetProductByID(request.Data.ProductID)
	updatedProductData := updatedProduct.ToSellerItemsB2B(products.ToSellerItemsCondition{IncludeVariant: true})
	return &dto.UpdateProductDetailsResponse{
		Product: &updatedProductData,
		Message: "Product details updated successfully",
	}, nil
}

func (s *Service) UpdateProductsStockStatus(ctx context.Context, products []inventoryDao.KiranaBazarProductsInventory, isOos bool, updatedBy string) (interface{}, error) {
	if len(products) == 0 {
		return nil, nil
	}
	multipleProductUpdateRequests := make([]dto.UpdateProductDetailsData, 0)
	for _, product := range products {
		req := dto.UpdateProductDetailsData{
			UpdatedBy: product.UpdatedBy,
			Seller:    product.Seller,
			ProductID: int(product.ID),
			Updates: []dto.UpdateProductField{
				{
					Key:   "is_oos",
					Value: isOos,
				},
			},
		}
		multipleProductUpdateRequests = append(multipleProductUpdateRequests, req)
	}

	responses, err := s.UpdateMultipleProductsDetails(ctx, &dto.UpdateMultipleProductDetailsRequest{
		Data:          multipleProductUpdateRequests,
		UpdatedBy:     updatedBy,
		RequestSource: "BACKEND",
	})
	if err != nil {
		return nil, err
	}

	return responses, nil
}

func (s *Service) UpdateMultipleProductsDetails(ctx context.Context, request *dto.UpdateMultipleProductDetailsRequest) ([]*dto.UpdateProductDetailsResponse, error) {
	if request == nil {
		return nil, errors.New("no products to process")
	}

	if len(request.Data) == 0 {
		return nil, errors.New("no products to process")
	}

	var (
		mu        sync.Mutex
		responses []*dto.UpdateProductDetailsResponse
		errs      []error
	)

	g, ctx := errgroup.WithContext(ctx)

	for _, reqData := range request.Data {
		req := reqData // capture loop variable
		requestSource := request.RequestSource

		g.Go(func() error {
			// updatedBy := reqData.UpdatedBy
			// if updatedBy == "" {
			// 	updatedBy = request.UpdatedBy
			// }
			updatedBy := request.UpdatedBy
			resp, err := s.UpdateProductDetails(ctx, &dto.UpdateProductDetailsRequest{
				Data:          req,
				UpdatedBy:     updatedBy,
				RequestSource: requestSource,
			})

			mu.Lock()
			defer mu.Unlock()

			if err != nil {
				errs = append(errs, fmt.Errorf("productID %d: %w", req.ProductID, err))
			} else {
				responses = append(responses, resp)
			}

			return nil // we collect errors manually
		})
	}

	_ = g.Wait() // Wait for all goroutines

	if len(errs) > 0 {
		return responses, &dto.MultiError{Errors: errs}
	}
	return responses, nil
}

func SafeDeref[T any](ptr *T) T {
	var zero T
	if ptr == nil {
		return zero
	}
	return *ptr
}

func (s *Service) FetchWallWaybills(ctx context.Context, req dto.FetchAllWaybillsRequest) (*dto.FetchAllWaybillsResponse, error) {
	orderID, err := strconv.ParseUint(req.OrderID, 10, 64)
	if err != nil {
		return nil, err
	}
	allWaybills, err := s.AWBMaster.GetAllActiveWaybillsForOrderID(context.Background(), orderID)
	if err != nil {
		return nil, err
	}
	response := dto.FetchAllWaybillsResponse{}
	for _, waybill := range allWaybills {
		response.AWBs = append(response.AWBs, dto.AWB{
			AWBNumber:    SafeDeref(waybill.AWBNumber),
			ReferenceID:  SafeDeref(waybill.ReferenceID),
			Status:       SafeDeref(waybill.Status),
			IsPrimary:    SafeDeref(waybill.IsPrimary),
			NSLCode:      SafeDeref(waybill.NSLCode),
			Instructions: SafeDeref(waybill.Instructions),
			Courier:      SafeDeref(waybill.Courier),
		})
	}
	sort.Slice(response.AWBs, func(i, j int) bool {
		return response.AWBs[i].IsPrimary
	})
	return &response, nil
}

func (s *Service) GetWayBillDetails(ctx context.Context, req dto.GetWaybillDetailsRequest) (*awbMasterDTO.AWBDetailsData, error) {
	awbData, err := s.AWBMaster.GetAWBScans(context.Background(), req.AWBNumber)
	if err != nil {
		return nil, err
	}
	return awbData, nil
}

func (s *Service) GetB2BMeta(ctx context.Context, request *dto.GetB2BMetaRequest) (*dto.GetB2BMetaResponse, error) {
	response := dto.GetB2BMetaResponse{}
	brandsData := make([]dto.BrandData, 0)
	if request.Data.Seller != "" {
		source, exists := brands.GetSourceBySeller(request.Data.Seller)
		if !exists {
			slack.SendSlackMessage(fmt.Sprintf("Source not found for seller: %s from get b2b meta", request.Data.Seller))
			return nil, errors.New("source not found for seller: " + request.Data.Seller)
		}

		brandMeta, exists := brands.GetBrandMetaBySeller(request.Data.Seller) // Ensure brand meta is loaded

		if !exists {
			return nil, errors.New("brand meta not found for seller: " + request.Data.Seller)
		}

		label := brandMeta.Name
		fmt.Println("brandMeta", brandMeta)
		bgColor, textColor := utils.GetLabelColor(request.Data.Seller)
		brandData := dto.BrandData{
			Seller:    request.Data.Seller,
			Source:    source,
			Label:     label,
			BgColor:   bgColor,
			TextColor: textColor,
			Url:       brandMeta.Logo,
		}
		brandsData = append(brandsData, brandData)
		response.Data.Brands = brandsData
		return &response, nil
	}

	for source, seller := range brands.GetSourceSellerMap() {
		bgColor, textColor := utils.GetLabelColor(seller)
		brandMeta, exists := brands.GetBrandMetaBySeller(seller) // Ensure brand meta is loaded
		if !exists {
			log.Printf("Warning: Brand meta not found for seller %s in GetB2BMeta loop. Using seller code as label.", seller)
			return nil, errors.New("brand meta not found for seller: " + seller)
		}

		brandData := dto.BrandData{
			Seller:    seller,
			Source:    source,
			Label:     brandMeta.Name,
			BgColor:   bgColor,
			TextColor: textColor,
			Url:       brandMeta.Logo,
		}
		brandsData = append(brandsData, brandData)
	}

	// sort array alphabetically
	sort.Slice(brandsData, func(i, j int) bool {
		return strings.ToLower(brandsData[i].Label) < strings.ToLower(brandsData[j].Label)
	})

	tempBrandId := 1
	for i := range brandsData {
		brandsData[i].Id = tempBrandId
		tempBrandId++
	}
	response.Data.Brands = brandsData
	response.Data.CancellationReasons = orderreason.GetCancelReasonOptionsAsMap()

	return &response, nil
}

func (s *Service) PushOrderToOMSWithLogs(ctx context.Context, request *dto.PushOrderToOMSRequest) (*dto.PushOrderToOMSResponse, error) {
	orderID, err := strconv.ParseInt(request.Data.OrderID, 10, 64)
	if err != nil {
		return nil, err
	}

	tx := s.repository.Db.Begin()
	if tx.Error != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	var kpool dao.KiranaBazarPushOrderToOMSLogs
	err = tx.Set("gorm:query_option", "FOR UPDATE").Where("order_id = ?", orderID).First(&kpool).Error

	recordExists := false
	if err == nil {
		recordExists = true

		// Only block if status is SUCCESS or PENDING (and not forced)
		if (kpool.Status == "SUCCESS" || kpool.Status == "PENDING") && !request.Data.Force {
			tx.Rollback()
			return nil, fmt.Errorf("order already %s", strings.ToLower(kpool.Status))
		}

		// For FAILED status, continue processing (no force required)
		// For any status with force=true, continue processing
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		tx.Rollback()
		return nil, fmt.Errorf("failed to check existing record: %w", err)
	}

	// Create or update log entry
	newKpool := dao.KiranaBazarPushOrderToOMSLogs{
		OrderID:   orderID,
		Email:     request.Data.UpdatedBy,
		Status:    "PENDING",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Reason:    request.Data.Reason,
	}

	if recordExists {
		// Update existing record
		newKpool.ID = kpool.ID
		newKpool.CreatedAt = kpool.CreatedAt // Preserve original creation time
		if err = tx.Save(&newKpool).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to update log entry: %w", err)
		}
	} else {
		// Create new record
		if err = tx.Create(&newKpool).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to create log entry: %w", err)
		}
	}

	if err = tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Push to OMS
	resp, err := s.pushOrderToOMS(ctx, request)

	// Update status based on result
	updateData := map[string]interface{}{
		"updated_at": time.Now(),
	}

	if err != nil || !resp.Success {
		updateData["status"] = "FAILED"
		if updateErr := s.repository.Db.Model(&dao.KiranaBazarPushOrderToOMSLogs{}).Where("order_id = ?", orderID).Updates(updateData).Error; updateErr != nil {
			log.Printf("Failed to update status to FAILED: %v", updateErr)
		}
		return resp, err
	}

	updateData["status"] = "SUCCESS"
	if updateErr := s.repository.Db.Model(&dao.KiranaBazarPushOrderToOMSLogs{}).Where("order_id = ?", orderID).Updates(updateData).Error; updateErr != nil {
		log.Printf("Failed to update status to SUCCESS: %v", updateErr)
	}

	return resp, nil
}

// PushOrderToOMS handles b2b side order confirmation and pushing to OMS
func (s *Service) pushOrderToOMS(ctx context.Context, request *dto.PushOrderToOMSRequest) (*dto.PushOrderToOMSResponse, error) {

	// converting string orderid to int
	orderID, err := strconv.ParseInt(request.Data.OrderID, 10, 64)
	if err != nil {
		return nil, err
	}

	// storing dimensions
	dimensions := []shared.Dimensions{}
	for _, j := range request.Data.Package {
		dimensions = append(dimensions, shared.Dimensions{
			Length:  j.Length,
			Breadth: j.Width,
			Height:  j.Height,
			Weight:  j.Weight,
		})
	}
	packageDetails := shared.PackageDetails{
		Dimensions: dimensions,
	}
	packageDetailsByte, err := json.Marshal(packageDetails)
	if err != nil {
		return nil, err
	}
	packageDetailsS := &dao.KiranabazarOrderPackageDetails{
		OrderID:      orderID,
		Details:      packageDetailsByte,
		NoOfPackages: len(request.Data.Package),
	}

	err = s.repository.Db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "order_id"}}, // Assuming order_id is your unique key
		DoUpdates: clause.AssignmentColumns([]string{"details", "no_of_packages"}),
	}).Create(packageDetailsS).Error
	if err != nil {
		return nil, err
	}

	// generating invoice
	invoiceResponse, err := s.processInvoiceGeneration(ctx, request.Data.OrderID, "", "", orderID)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("sanket check this OrderID: %s, Failed to generate invoice: %v", request.Data.OrderID, err))
		s.reverseInvoiceGeneration(ctx, request.Data.OrderID, orderID)
		return nil, err
	}

	/** Main function here is this -- trying to use this type of commenting to highlight the main function of the file
	what does the function do?
	--> This function is responsible to push the order to OMS
	*/
	// comment below to send request directly to delhivery to skip the shipdelight -- currently integrated for delhivery
	// creating order on Internal OMS

	resp, err := s.PushOrderTo3PL(context.Background(), fmt.Sprintf("%d", orderID), request.Data.UpdatedBy, invoiceResponse)
	if err == nil {
		return resp, nil
	}

	// failed to push to 3pl, try to push to shipdelight
	slack.SendSlackMessage(fmt.Sprintf("OrderID: %s, Failed to push order to 3pl: %v trying via shipdelight", request.Data.OrderID, err))

	createOrderResponse, _ := s.CreateOrderOnOMS(context.Background(), dto.CreateOrderOnOMSRequest{
		OrderID: uint64(orderID),
	})
	if createOrderResponse == nil {
		// omsstatus.FAILED_NON_SERVICEABLE -- as of now any error is non servicable error
		description := omsstatus.GetOMSStatusDescription(omsstatus.FAILED_NON_SERVICEABLE)
		s.UpdateB2BOrderStatus(context.Background(), &dto.UpdateB2BOrderStatusRequest{
			UpdatedBy: "SHIPDELIGHT",
			Data: dto.UpdateB2BOrderStatusData{
				OrderStatus:     omsstatus.FAILED_NON_SERVICEABLE,
				Source:          "SHIPDELIGHT",
				OrderingModule:  "SHIPDELIGHT",
				OrderStatusType: "",
				OrderID:         request.Data.OrderID,
				UpdatedAt:       time.Now().UnixMilli(),
				UpdatedBy:       "SHIPDELIGHT",
				OrderMeta: dto.OrderMeta{
					Note: description,
				},
			},
		})
		return &dto.PushOrderToOMSResponse{
			Success: false,
			Message: omsstatus.GetOMSStatusDescription(omsstatus.FAILED_OTHERS),
			OrderID: request.Data.OrderID,
			OMS:     "SHIPDELIGHT",
		}, nil
	}
	cor := createOrderResponse.(*OMSModels.OrderResponse)
	if cor.Status != omsstatus.SUCCESS_CREATED {
		description := omsstatus.GetOMSStatusDescription(cor.Status)
		s.UpdateB2BOrderStatus(context.Background(), &dto.UpdateB2BOrderStatusRequest{
			UpdatedBy: "SHIPDELIGHT",
			Data: dto.UpdateB2BOrderStatusData{
				OrderStatus:     cor.Status,
				Source:          "SHIPDELIGHT",
				OrderingModule:  "SHIPDELIGHT",
				OrderStatusType: "",
				OrderID:         request.Data.OrderID,
				UpdatedAt:       time.Now().UnixMilli(),
				UpdatedBy:       "SHIPDELIGHT",
				OrderMeta: dto.OrderMeta{
					Note: description,
				},
			},
		})
		// change the order processing status to error here
		return &dto.PushOrderToOMSResponse{
			Success: false,
			Message: description,
			OrderID: request.Data.OrderID,
			OMS:     "SHIPDELIGHT",
		}, nil
	}

	go func(repo *sqlRepo.Repository, orderID int64, labelURL string) {
		_, err := repo.CustomQuery(nil, fmt.Sprintf(`update kiranabazar_order_details set printing_label = "%s" where order_id = %d`, labelURL, orderID))
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("error updating labelURL url in db for %d %s", orderID, err.Error()))
			fmt.Println("error updating label url in db", err)
		}
	}(s.repository, orderID, cor.LabelURL)

	// updating awb numbers in db
	awbs := []string{cor.AWB}
	if len(cor.AWBs) > 0 {
		awbs = cor.AWBs
	}
	go s.UpdateAWBsInDatabase(request.Data.OrderID, awbs, cor.Courier)

	// this handles the Shipment Created Event and all the trigger that is needed after shipment is created
	// go s.HandleShipmentCreatedOrder(orderID, cor.OrderID, cor.Courier, cor.AWB, request.Data.UpdatedBy, invoiceResponse.InvoiceURL, cor.LabelURL, invoiceResponse.InvoiceID, invoiceResponse.InvoiceAmount, invoiceResponse, packageDetails, "SHIPDELIGHT")
	go s.HandleShipmentCreatedOrder(context.Background(), dto.ShipmentCreatedRequest{
		Data: dto.ShipmentCreatedRequestData{
			OrderID:           request.Data.OrderID,
			OMSOrderID:        cor.OrderID,
			OrderStatus:       processingstatus.SHIPMENT_CREATED,
			CourierRequested:  cor.RequestedCourier,
			CourierAssigned:   cor.AssignedCourier,
			Courier:           cor.Courier,
			AWBNumber:         cor.AWB,
			ShippingLabel:     cor.LabelURL,
			PackageDetails:    packageDetails,
			CSGT:              invoiceResponse.CGST,
			IGST:              invoiceResponse.IGST,
			SGST:              invoiceResponse.SGST,
			BuyerGST:          invoiceResponse.BuyerGST,
			NoOfSkus:          invoiceResponse.NoOfSkus,
			ItemQuantity:      invoiceResponse.ItemQuantity,
			Discount:          invoiceResponse.Discount,
			InvoiceURL:        invoiceResponse.InvoiceURL,
			InvoiceID:         invoiceResponse.InvoiceID,
			InvoiceAmount:     invoiceResponse.InvoiceAmount,
			InvoiceCreatedAt:  time.Now().UnixMilli(),
			ShipmentCreatedAt: time.Now().UnixMilli(),
			Source:            "B2B",
			KCShip:            true,
			OMS:               "SHIPDELIGHT",
			UpdatedBy:         request.Data.UpdatedBy,
		},
	})

	// returning invoice and printing label
	return &dto.PushOrderToOMSResponse{
		Success:       true,
		Message:       "Order pushed to OMS successfully",
		OrderID:       request.Data.OrderID,
		Invoice:       invoiceResponse.InvoiceURL,
		PrintingLabel: cor.LabelURL,
		OMS:           "SHIPDELIGHT",
		Courier:       cor.Courier,
		AWBNumber:     cor.AWB,
	}, nil
}

// GetOrderPayment provides the payment details for the order
func GetOrderPayment(repo *sqlRepo.Repository, orderId string) (orderPayment dao.KiranaBazarOrderPayment, err error) {
	_, err = repo.Find(map[string]interface{}{
		"order_id": orderId,
	}, &orderPayment)
	return
}

type DeliveryStatus struct {
	OrderId        string `json:"order_id"`
	AwbNumber      string `json:"awb_number"`
	TimeOfDelivery int64  `json:"time_of_delivery"`
	Seller         string `json:"seller"`
	InsertedAt     int64  `json:"inserted_at"`
}
type DeliveryStatusNewsFeed struct {
	InTransit      []DeliveryStatus `json:"in_transit"`
	OutForDelivery []DeliveryStatus `json:"out_for_delivery"`
}

const (
	cacheKeyTemplate = "delivery_status_news_feed:%s"
	cacheTTL         = 30 * 24 * time.Hour
	sevenDaysMs      = 7 * 24 * 60 * 60 * 1000
	twoDaysMs        = 2 * 24 * 60 * 60 * 1000
)

func (s *Service) pushOrdersToKCCACHE(ctx context.Context, userId string, orderId string, awbNumber string, seller string, currStatus string) error {
	if !includes([]string{displaystatus.IN_TRANSIT, shipmentstatus.OUT_FOR_DELIVERY}, currStatus) {
		return fmt.Errorf("unsupported status: %s", currStatus)
	}

	userData, err := s.getUserDataFromCache(userId, ctx)
	if err != nil {
		return err
	}

	cleanupExpiredOrders(&userData)

	orderIdInInt, err := strconv.Atoi(orderId)
	if err != nil {
		return fmt.Errorf("invalid order ID: %w", err)
	}

	timeOfDelivery, err := s.getDeliveryTime(ctx, currStatus, awbNumber, uint64(orderIdInInt))
	if err != nil {
		return fmt.Errorf("failed to get delivery time: %w", err)
	}

	newStatus := DeliveryStatus{
		OrderId:        orderId,
		AwbNumber:      awbNumber,
		Seller:         seller,
		TimeOfDelivery: timeOfDelivery,
		InsertedAt:     time.Now().UnixMilli(),
	}

	s.updateUserDataWithStatus(&userData, newStatus, currStatus, orderId)

	return s.saveUserDataToCache(userId, userData, ctx)
}

func (s *Service) removeOrderFromOutForDeliveryCache(ctx context.Context, userId string, orderId string) error {
	userData, err := s.getUserDataFromCache(userId, ctx)
	if err == libRedis.Nil {
		return nil // Nothing to remove
	}
	if err != nil {
		return err
	}

	userData.OutForDelivery = removeOrderFromArray(userData.OutForDelivery, orderId)
	return s.saveUserDataToCache(userId, userData, ctx)
}

func (s *Service) getUserDataFromCache(userId string, ctx context.Context) (DeliveryStatusNewsFeed, error) {
	userKey := fmt.Sprintf(cacheKeyTemplate, userId)
	gcpRedis := s.GcpRedis.RedisClient
	result, err := gcpRedis.Get(ctx, userKey).Result()

	if err == libRedis.Nil {
		return DeliveryStatusNewsFeed{
			InTransit:      []DeliveryStatus{},
			OutForDelivery: []DeliveryStatus{},
		}, nil
	}

	if err != nil {
		return DeliveryStatusNewsFeed{}, fmt.Errorf("error fetching user data: %w", err)
	}

	var userData DeliveryStatusNewsFeed
	if err := json.Unmarshal([]byte(result), &userData); err != nil {
		return DeliveryStatusNewsFeed{}, fmt.Errorf("error unmarshaling user data: %w", err)
	}

	return userData, nil
}

func (s *Service) saveUserDataToCache(userId string, userData DeliveryStatusNewsFeed, ctx context.Context) error {
	userKey := fmt.Sprintf(cacheKeyTemplate, userId)
	updatedUserDataJSON, err := json.Marshal(userData)
	if err != nil {
		return fmt.Errorf("error marshaling updated user data: %w", err)
	}

	gcpRedis := s.GcpRedis.RedisClient

	if err := gcpRedis.Set(ctx, userKey, updatedUserDataJSON, cacheTTL).Err(); err != nil {
		return fmt.Errorf("error storing updated user data: %w", err)
	}

	return nil
}

func (s *Service) getDeliveryTime(ctx context.Context, currStatus string, awbNumber string, orderIdInInt uint64) (int64, error) {
	timeOfDelivery := time.Now().Unix()

	switch currStatus {
	case displaystatus.IN_TRANSIT:
		awbDetails, err := s.AWBMaster.GetPrimaryWaybillForOrderID(ctx, []uint64{orderIdInInt})
		if err != nil {
			return timeOfDelivery, err
		}
		if len(awbDetails) > 0 && awbDetails[0].PDD != nil {
			timeOfDelivery = *awbDetails[0].PDD
		}

	case shipmentstatus.OUT_FOR_DELIVERY:
		awbDetails, err := s.AWBMaster.GetWaybillDetails(ctx, awbNumber, orderIdInInt)
		if err != nil {
			return timeOfDelivery, err
		}
		if len(awbDetails) > 0 && awbDetails[0].PDD != nil {
			timeOfDelivery = *awbDetails[0].PDD
		}
	}

	return timeOfDelivery, nil
}

func (s *Service) updateUserDataWithStatus(userData *DeliveryStatusNewsFeed, newStatus DeliveryStatus, currStatus string, orderId string) {
	// Remove order from both arrays to avoid duplicates
	userData.InTransit = removeOrderFromArray(userData.InTransit, orderId)
	userData.OutForDelivery = removeOrderFromArray(userData.OutForDelivery, orderId)

	switch currStatus {
	case displaystatus.IN_TRANSIT:
		userData.InTransit = append(userData.InTransit, newStatus)
	case shipmentstatus.OUT_FOR_DELIVERY:
		userData.OutForDelivery = append(userData.OutForDelivery, newStatus)
	}
}

func removeOrderFromArray(orders []DeliveryStatus, orderId string) []DeliveryStatus {
	result := make([]DeliveryStatus, 0, len(orders))
	for _, order := range orders {
		if order.OrderId != orderId {
			result = append(result, order)
		}
	}
	return result
}

func cleanupExpiredOrders(userData *DeliveryStatusNewsFeed) {
	now := time.Now().UnixMilli()
	sevenDaysAgo := now - sevenDaysMs
	twoDaysAgo := now - twoDaysMs

	userData.InTransit = filterOrdersByAge(userData.InTransit, sevenDaysAgo)
	userData.OutForDelivery = filterOrdersByAge(userData.OutForDelivery, twoDaysAgo)
}

func filterOrdersByAge(orders []DeliveryStatus, cutoffTime int64) []DeliveryStatus {
	filtered := make([]DeliveryStatus, 0, len(orders))
	for _, order := range orders {
		if order.InsertedAt > cutoffTime {
			filtered = append(filtered, order)
		}
	}
	return filtered
}

// GetOrderPaymentDetails retrieves comprehensive payment information for an order
func (s *Service) GetOrderPaymentDetails(ctx context.Context, request *dto.GetOrderPaymentDetailsRequest) (*dto.GetOrderPaymentDetailsResponse, error) {
	if request.Data.OrderID == "" {
		return nil, errors.New("order_id is required")
	}

	orderID, err := strconv.ParseInt(request.Data.OrderID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid order_id format: %w", err)
	}

	// Get payment details
	paymentDetails, err := s.getPaymentDetails(orderID)
	if err != nil {
		return nil, err
	}

	// Get refund details
	refundDetails, err := s.getRefundDetails(orderID)
	if err != nil {
		return nil, err
	}

	// Build response
	response := s.buildPaymentResponse(paymentDetails, refundDetails, request.Data.OrderID)
	return response, nil
}

// getPaymentDetails fetches payment information from joined tables
func (s *Service) getPaymentDetails(orderID int64) (*dto.GetPaymentDetailsPaymentQueryResult, error) {
	query := `
		SELECT
			kop.order_id,
			kop.amount as payable_amount,
			kop.paid_amount,
			kop.payment_id,
			kop.rnn,
			kop.source as kop_source,
			kop.updated_at as kop_updated_at,
			kpgo.gateway_status,
			kpgo.payment_amount as advance_payment,
			kpgo.created_at as kpgo_created_at,
			kpgo.gateway
		FROM
			kiranabazar_order_payments kop
		LEFT JOIN (
			SELECT
				kc_order_id,
				gateway_status,
				payment_amount,
				created_at,
				gateway,
				ROW_NUMBER() OVER (PARTITION BY kc_order_id ORDER BY created_at DESC) as rn
			FROM
				kiranabazar_payment_gateway_orders
			WHERE
				kc_order_id = ?
		) kpgo ON kop.order_id = kpgo.kc_order_id AND kpgo.rn = 1
		WHERE
			kop.order_id = ?
	`

	var result dto.GetPaymentDetailsPaymentQueryResult
	err := s.repository.Db.Raw(query, orderID, orderID).Scan(&result).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("payment details not found for order_id: %d", orderID)
		}
		return nil, fmt.Errorf("failed to fetch payment details: %w", err)
	}

	// Check if order payment record exists
	if result.OrderID == 0 {
		return nil, fmt.Errorf("payment details not found for order_id: %d", orderID)
	}

	return &result, nil
}

// getRefundDetails fetches all refund information for an order
func (s *Service) getRefundDetails(orderID int64) ([]dto.GetPaymentDetailsRefundQueryResult, error) {
	query := `
		SELECT
			kor.refund_method,
			kor.source_refund_id,
			kor.amount,
			kor.reason,
			kor.created_at,
			JSON_UNQUOTE(JSON_EXTRACT(kpgr.meta, '$.payment.entity.vpa')) AS refund_vpa
		FROM
			kiranabazar_order_refunds kor
		LEFT JOIN kiranabazar_payment_gateway_records kpgr ON kor.payment_id = kpgr.id
		WHERE
			kor.order_id = ?
		ORDER BY kor.created_at DESC
	`

	var results []dto.GetPaymentDetailsRefundQueryResult
	err := s.repository.Db.Raw(query, orderID).Scan(&results).Error
	if err != nil {
		return nil, fmt.Errorf("failed to fetch refund details: %w", err)
	}

	return results, nil
}

// buildPaymentResponse constructs the response from payment and refund data
func (s *Service) buildPaymentResponse(paymentResult *dto.GetPaymentDetailsPaymentQueryResult, refundResults []dto.GetPaymentDetailsRefundQueryResult, orderIDStr string) *dto.GetOrderPaymentDetailsResponse {
	response := &dto.GetOrderPaymentDetailsResponse{
		PayableAmount: fmt.Sprintf("%.2f", paymentResult.PayableAmount),
	}

	// Determine source
	if paymentResult.AdvancePayment != nil {
		response.Source = "payment gateway"
	} else if (paymentResult.AdvancePayment == nil) && (paymentResult.PaidAmount != nil) && (*paymentResult.PaidAmount > 0.0) {
		response.Source = "qr"
	} else {
		response.Source = "cod"
	}

	// Determine status
	if paymentResult.GatewayStatus != nil {
		response.Status = strings.ToLower(*paymentResult.GatewayStatus)
	} else if response.Source == "cod" {
		response.Status = "cod"
	} else {
		response.Status = "completed" // Default for QR payments
	}

	// Calculate advance payment
	if paymentResult.AdvancePayment != nil {
		response.AdvancePayment = fmt.Sprintf("%.2f", *paymentResult.AdvancePayment)
	} else {
		response.AdvancePayment = "0.00"
	}

	// Calculate total refund amount
	var totalRefundAmount float64
	for _, refund := range refundResults {
		totalRefundAmount += refund.Amount
	}

	// Calculate final COD amount (considering refunds)
	finalCOD := paymentResult.PayableAmount
	if paymentResult.PaidAmount != nil {
		finalCOD = paymentResult.PayableAmount - *paymentResult.PaidAmount
	}
	// // Subtract refunds from final COD
	// finalCOD -= totalRefundAmount
	// if finalCOD < 0 {
	// 	finalCOD = 0
	// }
	response.FinalCOD = fmt.Sprintf("%.2f", finalCOD)

	// Transaction timestamp
	if paymentResult.KpgoCreatedAt != nil {
		response.TransactionAt = paymentResult.KpgoCreatedAt.Format("January 2, 2006, 3:04 PM")
	} else {
		response.TransactionAt = paymentResult.KopUpdatedAt.Format("January 2, 2006, 3:04 PM")
	}

	// Transaction ID
	if paymentResult.PaymentID != nil && *paymentResult.PaymentID != "" {
		response.TransactionID = *paymentResult.PaymentID
	} else if paymentResult.RNN != nil && *paymentResult.RNN != "" {
		response.TransactionID = *paymentResult.RNN
	}

	// Refund information
	response.Refunded = len(refundResults) > 0
	if response.Refunded {
		response.TotalRefundAmount = fmt.Sprintf("%.2f", totalRefundAmount)

		// Build refunds array
		refunds := make([]dto.RefundDetails, 0, len(refundResults))
		for _, refund := range refundResults {
			refundDetail := dto.RefundDetails{
				RefundMethod: refund.RefundMethod,
				Amount:       fmt.Sprintf("%.2f", refund.Amount),
				RefundedAt:   refund.CreatedAt.Format("January 2, 2006, 3:04 PM"),
				RefundVpa:    refund.RefundVpa,
			}

			if refund.SourceRefundID != nil && *refund.SourceRefundID != "" {
				refundDetail.SourceRefundID = *refund.SourceRefundID
			}

			if refund.Reason != nil && *refund.Reason != "" {
				refundDetail.Reason = *refund.Reason
			}

			refunds = append(refunds, refundDetail)
		}
		response.Refunds = refunds
	}

	return response
}

func (s *Service) CancelOrderB2B(ctx context.Context, request *dto.CancelOrderB2BRequest) (*string, error) {
	orderId := request.Data.OrderID
	orderIdInt, err := strconv.Atoi(orderId)
	if err != nil {
		return nil, fmt.Errorf("invalid order_id format: %w", err)
	}
	orderInfo, err := GetOrderEssentials(s.repository, int64(orderIdInt))
	if err != nil {
		return nil, fmt.Errorf("failed to fetch order: %w", err)
	}

	if orderInfo.DisplayStatus != nil && *orderInfo.DisplayStatus == displaystatus.CANCELLED {
		return nil, fmt.Errorf("order already cancelled")
	}

	courier := ""
	if orderInfo.Courier != nil {
		courier = *orderInfo.Courier
	}
	seller := ""
	if orderInfo.Seller != nil {
		seller = *orderInfo.Seller
	}
	orderStatus := ""
	if orderInfo.OrderStatus != nil {
		orderStatus = *orderInfo.DisplayStatus
	}
	canCancelOrder := canCancelOrder(orderStatus, seller, courier, request.UpdatedBy)
	if !canCancelOrder {
		return nil, fmt.Errorf("order cannot be cancelled")
	}

	updateOrderStatusRequest := &dto.UpdateB2BOrderStatusRequest{
		Data: dto.UpdateB2BOrderStatusData{
			Source:         request.RequestSource,
			OrderStatus:    displaystatus.CANCELLED,
			OrderID:        orderId,
			UpdatedAt:      time.Now().UTC().UnixMilli(),
			UpdatedBy:      request.UpdatedBy,
			OrderingModule: *orderInfo.Seller,
			OrderMeta: dto.OrderMeta{
				CancelReason: request.Data.CancelReason,
				Note:         request.Data.Note,
			},
		},
		UpdatedBy: request.UpdatedBy,
	}

	if includes([]string{displaystatus.PLACED, displaystatus.PENDING_CONFIRMATION, displaystatus.CONFIRMED}, orderStatus) {
		_, err := s.UpdateB2BOrderStatus(ctx, updateOrderStatusRequest)
		if err != nil {
			return nil, fmt.Errorf("failed to cancel order in update order status: %w", err)
		}
	} else if orderStatus == displaystatus.SHIPMENT_CREATED {
		shipmentCancelled, err := s.CancelShipment(ctx, orderId)
		if err != nil {
			return nil, fmt.Errorf("failed to cancel shipment: %w", err)
		}
		if shipmentCancelled {
			_, err := s.UpdateB2BOrderStatus(ctx, updateOrderStatusRequest)
			if err != nil {
				return nil, fmt.Errorf("failed to cancel order in update order status: %w", err)
			}
		}
	} else {
		return nil, fmt.Errorf("order cannot be cancelled")
	}
	message := "Order Cancelled Successfully"

	return &message, nil
}
