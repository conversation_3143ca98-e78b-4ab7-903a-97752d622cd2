package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"kc/internal/ondc/external/exotel/ivr"
	ivrexotelintegration "kc/internal/ondc/external/exotel/ivrExotelIntegration"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/ivrStatus"
	"kc/internal/ondc/utils"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/mixpanel/mixpanel-go"
)

var IVR_TAG_EXCLUDED_REQUEST_TYPES = []string{ivr.IVR_REQUEST_TYPES.ORDER_OUT_FOR_DELIVERY}

func (s *Service) UpdateIVRCallStatus(ctx context.Context, request *dto.IVRStatusUpdateRequest) (*dto.IVRStatusUpdateResponse, error) {
	currentIvrKey := fmt.Sprintf("ivr_%d", request.CallCount)
	updateTimestamp := time.Now().UnixMilli()
	ivrMeta := map[string]interface{}{
		"key":         currentIvrKey,
		"status":      request.Status,
		"retry_count": request.RetryCount,
		"call_count":  request.CallCount,
		"updated_at":  updateTimestamp,
	}

	ivrMetaJSON, err := json.Marshal(ivrMeta)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal ivr meta: %w", err)
	}

	query := fmt.Sprintf(`
		INSERT INTO kiranabazar_orders_ivr_status (order_id, ivr_status, ivr_meta, updated_at)
		VALUES (%d, '%s', JSON_OBJECT('status_history', JSON_ARRAY(CAST('%s' AS JSON))), %d)
		ON DUPLICATE KEY UPDATE
		ivr_status = '%s',
		ivr_meta = JSON_SET(
			ivr_meta,
			'$.status_history',
			JSON_ARRAY_APPEND(
				IF(
					JSON_TYPE(JSON_EXTRACT(ivr_meta, '$.status_history')) = 'ARRAY',
					JSON_EXTRACT(ivr_meta, '$.status_history'),
					JSON_ARRAY()
				),
				'$',
				CAST('%s' AS JSON)
			)
		),
		updated_at = %d;
	`,
		request.OrderId,
		request.IvrStatus,
		string(ivrMetaJSON),
		updateTimestamp,
		request.IvrStatus,
		string(ivrMetaJSON),
		updateTimestamp,
	)

	_, err = s.repository.CustomQuery(nil, query)
	if err != nil {
		return nil, fmt.Errorf("failed to update ivr status: %w", err)
	}
	return &dto.IVRStatusUpdateResponse{
		Success: true,
		Data:    *request,
	}, nil
}

func (s *Service) CreateIVRCall(ctx context.Context, request *dto.IVRRequest) (*dto.IVRResponse, error) {

	url := "https://api.exotel.com/v1/Accounts/kiranaclub1/Calls/connect.json"
	method := "POST"

	payload := strings.NewReader("From=%**************&CallerId=%**************&Url=http%3A%2F%2Fmy.exotel.com%2Fkiranaclub1%2Fexoml%2Fstart_voice%2F877165")

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payload)

	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	req.Header.Add("Authorization", "Basic ODEyZWZkOTNlYTdlZjNjYjRmMThkNzRiYzk3MmIyNjM3YzdjMjI0YjkwYWE4ZDYzOjA3MzUzNzM0ZmE2MGQyNTIzZDhmNTFhMTFjZTlhYjEwYzY2ZGIzMmUwZTFlMGE1ZQ==")
	req.Header.Add("accept", "application/json")
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	fmt.Println(string(body))
	return nil, nil
}

func GetOrderDetailsFromSid(sid string, repo *sqlRepo.Repository) (string, string, string, string, dao.KiranaBazarOrderDetails, uint64, error) {
	query := fmt.Sprintf(`
		select
			*
		from
			kiranaclubdb.kiranabazar_order_details kod
		where
			order_id = (
			select
				kc_bazar_order_id
			from
				kiranaclubdb.exotel_ivr_data eid
			join kiranaclubdb.exotel_ivr_kc_bazar_order_mapping eikbom on
				eid.id = eikbom.exotel_ivr_id
			where
				sid = '%s');`, sid)
	orderDetail := dao.KiranaBazarOrderDetail{}
	orderDetails := dao.KiranaBazarOrderDetails{}
	_, err := repo.CustomQuery(&orderDetail, query)
	if err != nil {
		return "", "", "", "", orderDetails, uint64(*orderDetail.OrderID), err
	}

	byt, err := json.Marshal(orderDetail.OrderDetails)
	if err != nil {
		return "", "", "", "", orderDetails, uint64(*orderDetail.OrderID), err
	}

	err = json.Unmarshal(byt, &orderDetails)
	if err != nil {
		return "", "", "", "", orderDetails, uint64(*orderDetail.OrderID), err
	}
	seller := "zoff_foods"
	if orderDetails.Seller != "" {
		seller = orderDetails.Seller
	}

	sellerName, err := brands.GetNameMappingBySeller(seller)

	if err != nil {
		return "", "", "", "", orderDetails, uint64(*orderDetail.OrderID), err
	}

	return *orderDetails.ShippingAddress.Phone, sellerName, fmt.Sprintf("KC_%06d", *orderDetail.OrderID), fmt.Sprintf("₹%.1f", orderDetails.TotalAmount), orderDetails, uint64(*orderDetail.OrderID), nil
}

func handleNoAnswerIVRCall(request dto.ExotelIVRExomlWebhookRequest, repo *sqlRepo.Repository, mp *mixpanelRepo.Repository) (dao.KiranaBazarOrderDetails, uint64, int, int, string) {
	// send WA
	_, _, _, _, orderDetails, intOrderID, err := GetOrderDetailsFromSid(request.CallSid, repo)
	if err != nil {
		fmt.Println("err = ", err)
	}
	// whatsapp.SendNotAbleToConnectMessage(phone, "order_not_confirmed_call_back", seller, orderID, orderValue)
	// mp.Track(context.Background(), []*mixpanel.Event{
	// 	mp.NewEvent("Order Not Confirmed WhatsApp Triggered", *orderDetails.ShippingAddress.UserID, map[string]interface{}{
	// 		"distinct_id":     *orderDetails.ShippingAddress.UserID,
	// 		"sid":             request.CallSid,
	// 		"status":          request.Status,
	// 		"order_id":        intOrderID,
	// 		"order_value":     int(orderDetails.GetOrderValue()),
	// 		"mobile":          orderDetails.ShippingAddress.Phone,
	// 		"seller":          seller,
	// 		"ordering_module": utils.MakeTitleCase(seller),
	// 	}),
	// })

	query := fmt.Sprintf(`
		select
			*
		from
			kiranaclubdb.exotel_ivr_api_logs
		where
			json_extract(response, "$.Call.Sid") = '%s' limit 1;`, request.CallSid)
	var ivrAPIdetailss dao.ExotelIVRAPILogs
	_, err = repo.CustomQuery(&ivrAPIdetailss, query)
	if err != nil {
		return orderDetails, intOrderID, 0, 0, ""
	}
	ivrAPIdetails := map[string]string{}
	err = json.Unmarshal(ivrAPIdetailss.Request, &ivrAPIdetails)
	if err != nil {
		return orderDetails, intOrderID, 0, 0, ""
	}
	customFields := &dto.ExotelIVROrderInfo{}
	cf := ivrAPIdetails["CustomField"]
	err = json.Unmarshal([]byte(cf), customFields)
	if err != nil {
		return orderDetails, intOrderID, customFields.RetryCount, customFields.IVRCallCount, customFields.RequestType
	}
	return orderDetails, intOrderID, customFields.RetryCount, customFields.IVRCallCount, customFields.RequestType
}

func handleBusyIVRCall(request dto.ExotelIVRExomlWebhookRequest, repo *sqlRepo.Repository, mp *mixpanelRepo.Repository) (dao.KiranaBazarOrderDetails, uint64, int, int, string) {
	// send WA
	_, _, _, _, orderDetails, intOrderID, err := GetOrderDetailsFromSid(request.CallSid, repo)
	if err != nil {
		fmt.Println("err = ", err)
	}
	// whatsapp.SendNotAbleToConnectMessage(phone, "order_not_confirmed_call_back", seller, orderID, orderValue)
	// mp.Track(context.Background(), []*mixpanel.Event{
	// 	mp.NewEvent("Order Not Confirmed WhatsApp Triggered", *orderDetails.ShippingAddress.UserID, map[string]interface{}{
	// 		"distinct_id":     *orderDetails.ShippingAddress.UserID,
	// 		"sid":             request.CallSid,
	// 		"status":          request.Status,
	// 		"order_id":        intOrderID,
	// 		"order_value":     int(orderDetails.GetOrderValue()),
	// 		"mobile":          orderDetails.ShippingAddress.Phone,
	// 		"seller":          seller,
	// 		"ordering_module": utils.MakeTitleCase(seller),
	// 	}),
	// })
	query := fmt.Sprintf(`
		select
			*
		from
			kiranaclubdb.exotel_ivr_api_logs
		where
			json_extract(response, "$.Call.Sid") = '%s' limit 1;`, request.CallSid)
	var ivrAPIdetailss dao.ExotelIVRAPILogs
	_, err = repo.CustomQuery(&ivrAPIdetailss, query)
	if err != nil {
		return orderDetails, intOrderID, 0, 0, ""
	}
	ivrAPIdetails := map[string]string{}
	err = json.Unmarshal(ivrAPIdetailss.Request, &ivrAPIdetails)
	if err != nil {
		return orderDetails, intOrderID, 0, 0, ""
	}
	customFields := &dto.ExotelIVROrderInfo{}
	cf := ivrAPIdetails["CustomField"]
	err = json.Unmarshal([]byte(cf), customFields)
	if err != nil {
		return orderDetails, intOrderID, customFields.RetryCount, customFields.IVRCallCount, customFields.RequestType
	}
	return orderDetails, intOrderID, customFields.RetryCount, customFields.IVRCallCount, customFields.RequestType
}

func handleCompletedIVRCall(request dto.ExotelIVRExomlWebhookRequest, repo *sqlRepo.Repository) (dao.KiranaBazarOrderDetails, uint64, int, int, string) {
	// dont do anything
	_, _, _, _, orderDetails, intOrderID, _ := GetOrderDetailsFromSid(request.CallSid, repo)
	query := fmt.Sprintf(`
		select
			*
		from
			kiranaclubdb.exotel_ivr_api_logs
		where
			json_extract(response, "$.Call.Sid") = '%s' limit 1;`, request.CallSid)
	var ivrAPIdetailss dao.ExotelIVRAPILogs
	_, err := repo.CustomQuery(&ivrAPIdetailss, query)
	if err != nil {
		return orderDetails, intOrderID, 0, 0, ""
	}
	ivrAPIdetails := map[string]string{}
	err = json.Unmarshal(ivrAPIdetailss.Request, &ivrAPIdetails)
	if err != nil {
		return orderDetails, intOrderID, 0, 0, ""
	}
	customFields := &dto.ExotelIVROrderInfo{}
	cf := ivrAPIdetails["CustomField"]
	err = json.Unmarshal([]byte(cf), customFields)
	if err != nil {
		return orderDetails, intOrderID, 0, 0, customFields.RequestType
	}
	return orderDetails, intOrderID, 0, 0, customFields.RequestType
}

func handleFailedIVRCall(request dto.ExotelIVRExomlWebhookRequest, repo *sqlRepo.Repository, mp *mixpanelRepo.Repository) (dao.KiranaBazarOrderDetails, uint64, int, int, string) {
	// retry after 5 min
	// if already retried then retry after 15 mon
	_, _, _, _, orderDetails, intOrderID, _ := GetOrderDetailsFromSid(request.CallSid, repo)
	// trigger IVR call
	query := fmt.Sprintf(`
		select
			*
		from
			exotel_ivr_api_logs
		where
			json_extract(response, "$.Call.Sid") = '%s' limit 1;`, request.CallSid)
	var ivrAPIdetailss dao.ExotelIVRAPILogs
	_, err := repo.CustomQuery(&ivrAPIdetailss, query)
	if err != nil {
		return orderDetails, intOrderID, 0, 0, ""
	}
	ivrAPIdetails := map[string]string{}
	err = json.Unmarshal(ivrAPIdetailss.Request, &ivrAPIdetails)
	phone := ivrAPIdetails["From"]
	customFields := &dto.ExotelIVROrderInfo{}
	cf := ivrAPIdetails["CustomField"]
	err = json.Unmarshal([]byte(cf), customFields)
	if err != nil {
		return orderDetails, intOrderID, customFields.RetryCount, customFields.IVRCallCount, customFields.RequestType
	}

	totalProductPrice := 0.0
	for _, pric := range orderDetails.BillBreakUp.ProductsPricing {
		tval, _ := strconv.ParseFloat(pric.TotalValue, 32)
		totalProductPrice += tval
	}
	retryCount := customFields.RetryCount
	var triggerTime time.Time
	if retryCount == 0 {
		triggerTime = time.Now().Add(time.Minute * 5)
	} else if retryCount == 1 {
		triggerTime = time.Now().Add(time.Minute * 15)
	} else {
		// slack.SendSlackMessageOnOrderingOperations(fmt.Sprintf("3 IVR retry for %s failed, orderid = %d", phone, intOrderID))
		return orderDetails, intOrderID, customFields.RetryCount, customFields.IVRCallCount, customFields.RequestType
	}

	appID := 0
	if data, ok := ivr.SellerIVRData[customFields.Seller][ivr.ORDER_CONFIRMATION_FLOW]; ok {
		if id, ok := data.(int); ok {
			appID = id
		}
	}
	ivrexotelintegration.AddDataToExotelIVRQueue(context.Background(), dto.ExotelIVROrderInfo{
		OrderID:      customFields.OrderID,
		Seller:       customFields.Seller,
		OrderValue:   customFields.OrderValue,
		CartValue:    customFields.CartValue,
		IVRCallCount: customFields.IVRCallCount,
		UserID:       customFields.UserID,
		RetryCount:   retryCount + 1,
		RequestType:  customFields.RequestType,
	}, phone, ivr.IVR_TO_PHONE, appID, triggerTime, repo, mp, ivr.IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW, true, nil)
	return orderDetails, intOrderID, retryCount, customFields.IVRCallCount, customFields.RequestType
}

func (s *Service) ExotelIVRStatusCallback(ctx context.Context, request interface{}) (interface{}, error) {
	bytt, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}
	_, err = s.repository.Create(&dao.ExotelIVRExomlWebhookLogs{
		Request:   bytt,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	})
	if err != nil {
		fmt.Println("not able to insert the webhook data in the db")
		return nil, err
	}

	exomlRequest := dto.ExotelIVRExomlWebhookRequest{}
	err = json.Unmarshal(bytt, &exomlRequest)
	if err != nil {
		return nil, err
	}

	orderDetails := dao.KiranaBazarOrderDetails{}
	var oid uint64
	var retryCount int
	var callCount int
	var requestType string
	switch exomlRequest.Status {
	case "no-answer":
		{
			orderDetails, oid, retryCount, callCount, requestType = handleNoAnswerIVRCall(exomlRequest, s.repository, s.Mixpanel)
			break
		}
	case "busy":
		{
			orderDetails, oid, retryCount, callCount, requestType = handleBusyIVRCall(exomlRequest, s.repository, s.Mixpanel)
			break
		}
	case "completed":
		{
			orderDetails, oid, retryCount, callCount, requestType = handleCompletedIVRCall(exomlRequest, s.repository)
			break
		}
	case "failed":
		{
			orderDetails, oid, retryCount, callCount, requestType = handleFailedIVRCall(exomlRequest, s.repository, s.Mixpanel)
			break
		}
	default:
		{
			fmt.Println("default status in exoml request")
		}
	}

	// update status here for IVR 2, IVR 3, IVR FAILED
	nextIvrStatus := ""
	if exomlRequest.Status == "failed" && retryCount > 1 {
		nextIvrStatus = ivrStatus.IVR_STATUS_MAP[exomlRequest.Status]
	} else {
		nextIvrStatus = ivrStatus.IVR_STATUS_MAP[fmt.Sprintf("ivr_%d", callCount+1)]
	}

	if nextIvrStatus != "" && exomlRequest.Status != "completed" && !includes(IVR_TAG_EXCLUDED_REQUEST_TYPES, requestType) {
		ivrStatusRequest := &dto.IVRStatusUpdateRequest{
			IvrStatus:  nextIvrStatus,
			Status:     exomlRequest.Status,
			RetryCount: retryCount,
			CallCount:  callCount,
			OrderId:    int64(oid),
		}
		_, err = s.UpdateIVRCallStatus(ctx, ivrStatusRequest)
		if err != nil {
			fmt.Println("not able to update ivr status in the db")
			return nil, err
		}
	}

	resp, err := GetIVRCallDetails(exomlRequest.CallSid)
	if err != nil {
		fmt.Println("not able to get exotel call details", err)
	}

	// default CallFrom and From is NA -- whill check the cases where these numbers are not coming and handle those cases
	CallFrom := "NA"
	From := "NA"

	if resp.Call.Sid != "" {
		CallFrom = resp.Call.To
		From = resp.Call.To
	}

	s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
		s.Mixpanel.NewEvent("IVR Call Response Received", *orderDetails.ShippingAddress.UserID, map[string]interface{}{
			"distinct_id":     *orderDetails.ShippingAddress.UserID,
			"sid":             exomlRequest.CallSid,
			"status":          exomlRequest.Status,
			"retry_count":     retryCount,
			"order_id":        oid,
			"order_value":     int(orderDetails.GetOrderValue()),
			"call_from":       CallFrom,
			"from":            From,
			"seller":          orderDetails.Seller,
			"ordering_module": utils.MakeTitleCase(orderDetails.Seller),
			"request_type":    requestType,
		}),
	})
	return "OK", nil
}
