package service

import (
	"context"
	"encoding/json"
	"time"

	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
)

/* ExpressBeesWebhook takes the request body(rb), converts rb into json and stores it in table "express_webhook_logs" */
func (s *Service) ExpressBeesWebhook(ctx context.Context, request *dto.ExpressBeesWebhookRequest, accountName string) (interface{}, error) {

	reqJSonString, err := json.Marshal(request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		return nil, err
	}

	_, err = s.repository.Create(&dao.ExpressBeesWebhookLogs{
		RequestObj:  reqJSonString,
		CreatedAt:   time.Now(),
		AccountName: accountName,
	})

	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		return nil, err
	}

	return "success", nil
}
