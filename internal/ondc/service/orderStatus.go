package service

import (
	"context"
	"kc/internal/ondc/models/dto"
	orderstatus "kc/internal/ondc/service/orderStatus"
)

func (s *Service) GetOrderStatus(ctx context.Context, request *dto.OrderStatusRequest) (*orderstatus.OrderStatusResponse, error) {
	return orderstatus.GetOrderStatusFromOrderID(request.OrderID)
}

func (s *Service) GetOrderStatusFromDB(ctx context.Context, request *dto.OrderStatusRequest) (*orderstatus.OrderStatusResponse, error) {
	return orderstatus.GetOrderStatusFromDB(request.OrderID)
}

func (s *Service) MapOrderStatus(ctx context.Context, request *dto.MapOrderStatusRequest) (*orderstatus.OrderStatusResponse, error) {
	statuses := orderstatus.MapOrderStatus(request.OrderStatus, "", orderstatus.OrderStatusResponse{})
	return &statuses, nil
}
