package auth

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

const (
	AccessTokenCookieName  = "d9e79a03-af12-4ce0-aeaa-320e9f296949" // Prefix for secure cookies
	RefreshTokenCookieName = "65217ced-399c-4b06-842c-8cf15df355fd" // Prefix for secure cookies
	CSRFCookieName         = "62d1f4cb-26d0-48db-827f-0cf4225cf82c" // CSRF token cookie name
	LoginValidationKey     = "8465fb57-bc41-481d-9b6a-af0df24803f1" // Cookie validation key
	LoginValidationValue   = "1SpZDZy9h5jj2XsrSG1Ylq9S9x4OVzOlOlFCYOWIAdArKjIEG4HyMFJbRH1ii4r1"
)

type Service struct {
	jwtService    *JWTService
	repository    *Repository
	fullCache     *UserCache // Changed from userCache to fullCache
	tokenService  *TokenService
	EncryptionKey string
}

func NewService(jwtSecret []byte, repository *Repository) (*Service, error) {
	// Initialize full user cache with 5-minute refresh interval
	fullCache := NewUserCache(repository, 5*time.Minute)

	// Initialize token service for hashing
	tokenService := NewTokenService()

	return &Service{
		jwtService:   NewJWTService(jwtSecret),
		repository:   repository,
		fullCache:    fullCache,
		tokenService: tokenService,
	}, nil
}

// Login authenticates user and returns tokens with enhanced security
func (s *Service) Login(ctx *gin.Context, req *LoginRequest, clientIP, userAgent string) (*AuthResponse, error) {
	// Get user from full cache (no DB call)
	user, err := s.fullCache.GetUser(req.Email)
	if err != nil {
		return nil, fmt.Errorf("authentication failed: %w", err)
	}

	// Check if user is active
	if !user.Active {
		return nil, fmt.Errorf("user account is inactive")
	}

	// Generate session ID for security tracking
	sessionID := s.generateSessionID()

	// Store session info for security validation
	expiresAt := time.Now().Add(7 * 24 * time.Hour).Unix()
	err = s.repository.StoreUserSession(user.Email, sessionID, clientIP, userAgent, expiresAt)
	if err != nil {
		// Log error but don't fail login
	}

	// Generate new refresh token
	refreshToken, err := s.generateAndStoreRefreshToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// Generate access token only
	accessToken, _, err := s.jwtService.GenerateTokens(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	return &AuthResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    60, // 1 minute for testing, change to 3600 for production
		User:         user,
	}, nil
}

// generateSessionID creates a secure random session ID
func (s *Service) generateSessionID() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// generateAndStoreRefreshToken generates new refresh token and stores its hash
func (s *Service) generateAndStoreRefreshToken(user *CSAgent) (string, error) {
	// Generate new refresh token
	_, refreshToken, err := s.jwtService.GenerateTokens(user)
	if err != nil {
		return "", fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// Hash refresh token
	hashedToken, err := s.tokenService.HashToken(refreshToken)
	if err != nil {
		return "", fmt.Errorf("failed to hash refresh token: %w", err)
	}

	// Store hashed token in database with 7-day expiry
	expiresAt := time.Now().Add(7 * 24 * time.Hour).Unix()
	err = s.repository.StoreRefreshToken(user.Email, hashedToken, expiresAt)
	if err != nil {
		return "", fmt.Errorf("failed to store refresh token: %w", err)
	}

	return refreshToken, nil
}

// RefreshToken generates new access token using existing refresh token with enhanced security
func (s *Service) RefreshToken(ctx *gin.Context, req *RefreshRequest) (*AuthResponse, error) {
	// Validate refresh token format
	claims, err := s.jwtService.ValidateToken(req.RefreshToken)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	// Additional security validation
	if req.ClientIP != "" && req.UserAgent != "" {
		valid, err := s.repository.ValidateUserSession(claims.Email, req.ClientIP, req.UserAgent)
		if err != nil || !valid {
			return nil, fmt.Errorf("session validation failed")
		}
	}

	// Get stored token hash and expiry
	storedHash, expiresAt, err := s.repository.GetRefreshTokenData(claims.Email)
	if err != nil {
		return nil, fmt.Errorf("refresh token not found: %w", err)
	}

	// Check if token is expired
	if time.Now().Unix() > expiresAt {
		// Clean up expired token
		s.repository.ClearRefreshToken(claims.Email)
		return nil, fmt.Errorf("refresh token expired")
	}

	// Validate refresh token against stored hash
	if !s.tokenService.ValidateToken(req.RefreshToken, storedHash) {
		return nil, fmt.Errorf("invalid refresh token")
	}

	// Get fresh user data from cache (no DB call)
	user, err := s.fullCache.GetUser(claims.Email)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	// Check if user is still active
	if !user.Active {
		return nil, fmt.Errorf("user account is inactive")
	}

	// Generate new access token only (keep same refresh token)
	accessToken, _, err := s.jwtService.GenerateTokens(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	return &AuthResponse{
		AccessToken:  accessToken,
		RefreshToken: req.RefreshToken, // Return same refresh token
		ExpiresIn:    60,               // 1 minute for testing, change to 3600 for production
		User:         user,
	}, nil
}

// Logout invalidates refresh token
func (s *Service) Logout(ctx *gin.Context, req *RefreshRequest) error {
	// Validate refresh token to get user email
	claims, err := s.jwtService.ValidateToken(req.RefreshToken)
	if err != nil {
		return fmt.Errorf("invalid refresh token: %w", err)
	}

	// Clear refresh token from database
	return s.repository.ClearRefreshToken(claims.Email)
}

// Enhanced methods for middleware interface
func (s *Service) GetUserByEmail(email string) (*CSAgent, error) {
	return s.fullCache.GetUser(email) // Now uses full cache, no DB call
}

func (s *Service) UpdateUserActivity(email string) error {
	return s.repository.UpdateUserActivity(email)
}

func (s *Service) ValidateToken(tokenString string) (*Claims, error) {
	return s.jwtService.ValidateToken(tokenString)
}

// NEW METHOD: ValidateSessionSecurity for enhanced middleware
func (s *Service) ValidateSessionSecurity(email, clientIP, userAgent string) bool {
	// Implement your security logic here
	// For now, we'll do basic validation
	valid, err := s.repository.ValidateUserSession(email, clientIP, userAgent)
	if err != nil {
		return false
	}
	return valid
}

// Cache management methods
func (s *Service) InvalidateUserCache(email string) {
	s.fullCache.InvalidateUser(email)
}

func (s *Service) RefreshUserCache(email string) error {
	// For full cache, we refresh the entire cache
	return s.fullCache.RefreshCache()
}

func (s *Service) GetCacheStats() map[string]interface{} {
	return s.fullCache.GetCacheStats()
}

func (s *Service) Stop() {
	s.fullCache.Stop()
}

// User management methods that now work with full cache
func (s *Service) UpdateUserProfile(email string, updates map[string]interface{}) error {
	err := s.repository.UpdateUserProfile(email, updates)
	if err != nil {
		return err
	}

	// For full cache, we need to refresh the entire cache or update specific user
	// Option 1: Refresh entire cache (simple)
	s.fullCache.RefreshCache()

	// Option 2: Update specific user (more efficient)
	// user, err := s.repository.GetUserByEmail(email)
	// if err == nil {
	//     s.fullCache.UpdateUser(user)
	// }

	return nil
}

func (s *Service) CleanupExpiredTokens() error {
	return s.repository.CleanupExpiredTokens()
}

func (s *Service) ForceLogoutUser(email string) error {
	return s.repository.ClearRefreshToken(email)
}

// NEW METHODS: Bulk operations for better performance
func (s *Service) BulkUpdateUserActivity(emails []string) error {
	return s.repository.BulkUpdateLastActivity(emails)
}

func (s *Service) GetAllActiveUsers() []*CSAgent {
	return s.fullCache.GetAllUsers()
}

func (s *Service) IsUserActive(email string) bool {
	return s.fullCache.IsUserActive(email)
}

// NEW METHOD: Force cache refresh (useful for admin operations)
func (s *Service) ForceCacheRefresh() error {
	return s.fullCache.RefreshCache()
}

func generateRandomString(length int) string {
	bytes := make([]byte, length)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)[:length]
}

func (s *Service) GenerateCSRFToken() string {
	// Generate random bytes
	bytes := make([]byte, 32)
	_, err := rand.Read(bytes)
	if err != nil {
		// Fallback if random generation fails
		return fmt.Sprintf("csrf_%d_%s", time.Now().Unix(), generateRandomString(16))
	}

	// Create timestamp for token expiry validation
	timestamp := time.Now().Unix()

	// Create token with timestamp
	token := fmt.Sprintf("%d:%s", timestamp, hex.EncodeToString(bytes))

	// Sign token with HMAC
	mac := hmac.New(sha256.New, []byte(s.EncryptionKey)) // Use your CSRF secret
	mac.Write([]byte(token))
	signature := hex.EncodeToString(mac.Sum(nil))

	// Return base64 encoded token with signature
	signedToken := fmt.Sprintf("%s:%s", token, signature)
	return base64.URLEncoding.EncodeToString([]byte(signedToken))
}

// ValidateCSRFToken validates a CSRF token
func (s *Service) ValidateCSRFToken(token string) bool {
	// Decode base64
	decoded, err := base64.URLEncoding.DecodeString(token)
	if err != nil {
		return false
	}

	// Split token and signature
	parts := strings.Split(string(decoded), ":")
	if len(parts) != 3 {
		return false
	}

	timestamp := parts[0]
	randomPart := parts[1]
	signature := parts[2]

	// Reconstruct original token
	originalToken := fmt.Sprintf("%s:%s", timestamp, randomPart)

	// Verify signature
	mac := hmac.New(sha256.New, []byte(s.EncryptionKey))
	mac.Write([]byte(originalToken))
	expectedSignature := hex.EncodeToString(mac.Sum(nil))

	if signature != expectedSignature {
		return false
	}

	// Check if token is expired (1 hour expiry)
	var tokenTime int64
	if _, err := fmt.Sscanf(timestamp, "%d", &tokenTime); err != nil {
		return false
	}

	if time.Now().Unix()-tokenTime > 3600 { // 1 hour expiry
		return false
	}

	return true
}
