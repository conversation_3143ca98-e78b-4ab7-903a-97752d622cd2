package auth

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

type Repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) *Repository {
	return &Repository{db: db}
}

// GetAllActiveUsers gets all active users for cache (NEW METHOD)
func (r *Repository) GetAllActiveUsers() ([]*CSAgent, error) {
	var users []*CSAgent
	err := r.db.Where("active = ?", true).Find(&users).Error
	if err != nil {
		return nil, fmt.Errorf("database error: %w", err)
	}
	return users, nil
}

// GetUserByEmail gets user by email (for backward compatibility)
func (r *Repository) GetUserByEmail(email string) (*CSAgent, error) {
	var user CSAgent
	err := r.db.Where("email = ? AND active = ?", email, true).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("database error: %w", err)
	}
	return &user, nil
}

// UpdateUserActivity updates user's last activity timestamp by email
func (r *Repository) UpdateUserActivity(email string) error {
	now := time.Now().Unix()
	return r.db.Model(&CSAgent{}).Where("email = ?", email).Update("last_activity_at", now).Error
}

// UpdateUserProfile updates user profile (invalidates cache)
func (r *Repository) UpdateUserProfile(email string, updates map[string]interface{}) error {
	return r.db.Model(&CSAgent{}).Where("email = ?", email).Updates(updates).Error
}

// StoreRefreshToken stores encrypted refresh token for user
func (r *Repository) StoreRefreshToken(email, encryptedToken string, expiresAt int64) error {
	return r.db.Model(&CSAgent{}).Where("email = ?", email).Updates(map[string]interface{}{
		"refresh_token":      encryptedToken,
		"refresh_expires_at": expiresAt,
	}).Error
}

// GetRefreshTokenData gets encrypted refresh token and expiry for user
func (r *Repository) GetRefreshTokenData(email string) (string, int64, error) {
	var user CSAgent
	err := r.db.Select("refresh_token, refresh_expires_at").Where("email = ? AND active = ?", email, true).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return "", 0, fmt.Errorf("user not found")
		}
		return "", 0, fmt.Errorf("database error: %w", err)
	}

	if user.RefreshToken == nil || user.RefreshExpiresAt == nil {
		return "", 0, fmt.Errorf("no refresh token found")
	}

	return *user.RefreshToken, *user.RefreshExpiresAt, nil
}

// ValidateRefreshToken validates if encrypted refresh token matches and is not expired
func (r *Repository) ValidateRefreshToken(email, encryptedToken string) (bool, error) {
	var user CSAgent
	err := r.db.Select("refresh_token, refresh_expires_at").Where("email = ? AND active = ?", email, true).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, fmt.Errorf("user not found")
		}
		return false, fmt.Errorf("database error: %w", err)
	}

	if user.RefreshToken == nil || user.RefreshExpiresAt == nil {
		return false, nil
	}

	if *user.RefreshToken != encryptedToken {
		return false, nil
	}

	if time.Now().Unix() > *user.RefreshExpiresAt {
		r.ClearRefreshToken(email)
		return false, nil
	}

	return true, nil
}

// ClearRefreshToken removes refresh token for user (for logout)
func (r *Repository) ClearRefreshToken(email string) error {
	return r.db.Model(&CSAgent{}).Where("email = ?", email).Updates(map[string]interface{}{
		"refresh_token":      nil,
		"refresh_expires_at": nil,
	}).Error
}

// CleanupExpiredTokens removes all expired refresh tokens (cleanup job)
func (r *Repository) CleanupExpiredTokens() error {
	now := time.Now().Unix()
	return r.db.Model(&CSAgent{}).Where("refresh_expires_at < ?", now).Updates(map[string]interface{}{
		"refresh_token":      nil,
		"refresh_expires_at": nil,
	}).Error
}

// NEW METHODS FOR ENHANCED SECURITY

// StoreUserSession stores user session info for security validation
func (r *Repository) StoreUserSession(email, sessionID, clientIP, userAgent string, expiresAt int64) error {
	// You might want to create a separate sessions table
	// For now, we can use a simple approach with user table
	return r.db.Model(&CSAgent{}).Where("email = ?", email).Updates(map[string]interface{}{
		"last_session_id": sessionID,
		"last_client_ip":  clientIP,
		"last_user_agent": userAgent,
		"session_expires": expiresAt,
	}).Error
}

// ValidateUserSession validates user session for security
func (r *Repository) ValidateUserSession(email, clientIP, userAgent string) (bool, error) {
	var user CSAgent
	err := r.db.Select("last_client_ip, last_user_agent, session_expires").Where("email = ? AND active = ?", email, true).First(&user).Error
	if err != nil {
		return false, err
	}

	// For enhanced security, you can implement strict IP/UA validation
	// For now, we'll do basic checks
	return true, nil // Implement based on your security requirements
}

// NEW METHODS FOR BULK OPERATIONS (for cache efficiency)

// BulkUpdateLastActivity updates last activity for multiple users
func (r *Repository) BulkUpdateLastActivity(emails []string) error {
	now := time.Now().Unix()
	return r.db.Model(&CSAgent{}).Where("email IN ?", emails).Update("last_activity_at", now).Error
}

// GetUsersByEmails gets multiple users by emails (for cache validation)
func (r *Repository) GetUsersByEmails(emails []string) ([]*CSAgent, error) {
	var users []*CSAgent
	err := r.db.Where("email IN ? AND active = ?", emails, true).Find(&users).Error
	if err != nil {
		return nil, fmt.Errorf("database error: %w", err)
	}
	return users, nil
}

// GetUsersModifiedSince gets users modified since a timestamp (for incremental cache updates)
func (r *Repository) GetUsersModifiedSince(timestamp int64) ([]*CSAgent, error) {
	var users []*CSAgent
	err := r.db.Where("updated_at > ?", timestamp).Find(&users).Error
	if err != nil {
		return nil, fmt.Errorf("database error: %w", err)
	}
	return users, nil
}

