package auth

import (
	"crypto/sha256"
	"fmt"

	"golang.org/x/crypto/bcrypt"
)

// TokenService handles token hashing instead of encryption
type TokenService struct {
	cost int // bcrypt cost
}

func NewTokenService() *TokenService {
	return &TokenService{
		cost: 12, // bcrypt cost (adjust based on your security vs performance needs)
	}
}

// HashToken hashes a token using SHA-256 first, then bcrypt (to handle long JWT tokens)
func (ts *TokenService) HashToken(token string) (string, error) {
	// First hash with SHA-256 to reduce length and ensure consistent input size
	sha256Hash := sha256.Sum256([]byte(token))
	sha256Hex := fmt.Sprintf("%x", sha256Hash)

	// Then hash with bcrypt for security (SHA-256 hex output is always 64 chars, well under 72 byte limit)
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(sha256Hex), ts.cost)
	if err != nil {
		return "", fmt.Errorf("failed to hash token: %w", err)
	}
	return string(hashedBytes), nil
}

// ValidateToken validates a token against its hash
func (ts *TokenService) ValidateToken(token, hashedToken string) bool {
	// First hash the token with SHA-256
	sha256Hash := sha256.Sum256([]byte(token))
	sha256Hex := fmt.Sprintf("%x", sha256Hash)

	// Then compare with bcrypt
	err := bcrypt.CompareHashAndPassword([]byte(hashedToken), []byte(sha256Hex))
	return err == nil
}