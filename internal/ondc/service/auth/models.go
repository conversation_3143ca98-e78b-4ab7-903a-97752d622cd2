package auth

import (
	"time"

	"github.com/golang-jwt/jwt/v4"
)

// CSAgent represents user data structure with enhanced security fields
type CSAgent struct {
	ID               string  `gorm:"column:id" json:"id"`
	Name             string  `gorm:"column:name" json:"name"`
	Role             string  `gorm:"column:role" json:"role"`
	AvatarURL        *string `gorm:"column:avatar_url" json:"avatar_url"`
	Email            string  `gorm:"column:email" json:"email"`
	Seller           *string `gorm:"column:seller" json:"seller"`
	Status           *string `gorm:"column:status" json:"status"`
	CreatedAt        uint64  `gorm:"column:created_at" json:"created_at"`
	UpdatedAt        uint64  `gorm:"column:updated_at;autoUpdateTime:false" json:"updated_at"`
	Team             *string `gorm:"column:team" json:"team"`
	LastActivityAt   *int64  `gorm:"column:last_activity_at" json:"last_activity_at"`
	Active           bool    `gorm:"column:active" json:"active"`
	RefreshToken     *string `gorm:"column:refresh_token" json:"-"`      // Don't expose in JSON
	RefreshExpiresAt *int64  `gorm:"column:refresh_expires_at" json:"-"` // Don't expose in JSON

	// NEW SECURITY FIELDS (add these to your database)
	LastSessionID      *string `gorm:"column:last_session_id" json:"-"`      // Don't expose
	LastClientIP       *string `gorm:"column:last_client_ip" json:"-"`       // Don't expose
	LastUserAgent      *string `gorm:"column:last_user_agent" json:"-"`      // Don't expose
	SessionExpiresAt   *int64  `gorm:"column:session_expires" json:"-"`      // Don't expose
	LoginAttempts      *int    `gorm:"column:login_attempts" json:"-"`       // For rate limiting
	LastLoginAttempt   *int64  `gorm:"column:last_login_attempt" json:"-"`   // For rate limiting
	AccountLockedUntil *int64  `gorm:"column:account_locked_until" json:"-"` // For account locking
}

// Claims represents JWT claims structure
type Claims struct {
	UserID string `json:"user_id"`
	Email  string `json:"email"`
	Name   string `json:"name"`
	Role   string `json:"role"`
	jwt.StandardClaims
}

// Request/Response structs
type LoginRequest struct {
	Email string `json:"email" binding:"required,email"`
}

type AuthResponse struct {
	AccessToken  string   `json:"access_token"`
	RefreshToken string   `json:"refresh_token"`
	ExpiresIn    int64    `json:"expires_in"`
	User         *CSAgent `json:"user"`
}

type RefreshRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
	// NEW SECURITY FIELDS for enhanced validation
	ClientIP  string `json:"-"` // Not from JSON, set by handler
	UserAgent string `json:"-"` // Not from JSON, set by handler
}

// NEW STRUCTS for enhanced security

type SecurityEvent struct {
	UserEmail string `json:"user_email"`
	EventType string `json:"event_type"` // login, logout, token_refresh, suspicious_activity
	ClientIP  string `json:"client_ip"`
	UserAgent string `json:"user_agent"`
	Timestamp int64  `json:"timestamp"`
	Details   string `json:"details"`
}

type SessionInfo struct {
	SessionID  string `json:"session_id"`
	UserEmail  string `json:"user_email"`
	ClientIP   string `json:"client_ip"`
	UserAgent  string `json:"user_agent"`
	CreatedAt  int64  `json:"created_at"`
	ExpiresAt  int64  `json:"expires_at"`
	LastActive int64  `json:"last_active"`
}

// NEW METHODS for CSAgent

// IsAccountLocked checks if user account is temporarily locked
func (u *CSAgent) IsAccountLocked() bool {
	if u.AccountLockedUntil == nil {
		return false
	}
	return time.Now().Unix() < *u.AccountLockedUntil
}

// CanAttemptLogin checks if user can attempt login (not locked and within rate limits)
func (u *CSAgent) CanAttemptLogin() bool {
	if u.IsAccountLocked() {
		return false
	}

	// Check if too many recent attempts
	if u.LoginAttempts != nil && u.LastLoginAttempt != nil {
		// Allow if last attempt was more than 15 minutes ago
		if time.Now().Unix()-*u.LastLoginAttempt > 15*60 {
			return true
		}
		// Allow if less than 5 attempts in last 15 minutes
		return *u.LoginAttempts < 5
	}

	return true
}

// IncrementLoginAttempts increments failed login attempts
func (u *CSAgent) IncrementLoginAttempts() {
	now := time.Now().Unix()
	if u.LoginAttempts == nil {
		attempts := 1
		u.LoginAttempts = &attempts
	} else {
		*u.LoginAttempts++
	}
	u.LastLoginAttempt = &now

	// Lock account after 5 failed attempts for 1 hour
	if *u.LoginAttempts >= 5 {
		lockUntil := now + 3600 // 1 hour
		u.AccountLockedUntil = &lockUntil
	}
}

// ResetLoginAttempts resets login attempts after successful login
func (u *CSAgent) ResetLoginAttempts() {
	u.LoginAttempts = nil
	u.LastLoginAttempt = nil
	u.AccountLockedUntil = nil
}
