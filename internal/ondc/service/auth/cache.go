package auth

import (
	"fmt"
	"log"
	"sync"
	"time"
)

// FullUserCache caches ALL users and refreshes every 5 minutes
type UserCache struct {
	mu              sync.RWMutex
	users           map[string]*CSAgent // map[email]*CSAgent
	repository      *Repository
	refreshInterval time.Duration
	ticker          *time.Ticker
	stopChan        chan bool
	lastRefresh     time.Time
	isInitialized   bool
}

func NewUserCache(repository *Repository, refreshInterval time.Duration) *UserCache {
	cache := &UserCache{
		users:           make(map[string]*CSAgent),
		repository:      repository,
		refreshInterval: refreshInterval,
		ticker:          time.NewTicker(refreshInterval),
		stopChan:        make(chan bool),
	}

	// Initial load of all users
	if err := cache.loadAllUsers(); err != nil {
		log.Printf("Error loading initial users: %v", err)
	}

	// Start background refresh routine
	go cache.refreshRoutine()

	return cache
}

// loadAllUsers loads all active users from database
func (fc *UserCache) loadAllUsers() error {
	users, err := fc.repository.GetAllActiveUsers()
	if err != nil {
		return err
	}

	fc.mu.Lock()
	defer fc.mu.Unlock()

	// Clear existing cache
	fc.users = make(map[string]*CSAgent)

	// Populate cache with all users
	for _, user := range users {
		fc.users[user.Email] = user
	}

	fc.lastRefresh = time.Now()
	fc.isInitialized = true

	log.Printf("Loaded %d users into cache", len(users))
	return nil
}

// GetUser gets user from cache (no DB call)
func (fc *UserCache) GetUser(email string) (*CSAgent, error) {
	fc.mu.RLock()
	defer fc.mu.RUnlock()

	if !fc.isInitialized {
		return nil, fmt.Errorf("cache not initialized")
	}

	user, exists := fc.users[email]
	if !exists {
		return nil, fmt.Errorf("user not found")
	}

	// Return a copy to prevent modifications
	userCopy := *user
	return &userCopy, nil
}

// GetAllUsers returns all cached users (for admin purposes)
func (fc *UserCache) GetAllUsers() []*CSAgent {
	fc.mu.RLock()
	defer fc.mu.RUnlock()

	users := make([]*CSAgent, 0, len(fc.users))
	for _, user := range fc.users {
		userCopy := *user
		users = append(users, &userCopy)
	}

	return users
}

// RefreshCache forces immediate refresh from database
func (fc *UserCache) RefreshCache() error {
	return fc.loadAllUsers()
}

// refreshRoutine runs background cache refresh every 5 minutes
func (fc *UserCache) refreshRoutine() {
	for {
		select {
		case <-fc.ticker.C:
			if err := fc.loadAllUsers(); err != nil {
				log.Printf("Error refreshing user cache: %v", err)
			}
		case <-fc.stopChan:
			return
		}
	}
}

// Stop stops the background refresh routine
func (fc *UserCache) Stop() {
	fc.ticker.Stop()
	fc.stopChan <- true
}

// GetCacheStats returns cache statistics
func (fc *UserCache) GetCacheStats() map[string]interface{} {
	fc.mu.RLock()
	defer fc.mu.RUnlock()

	return map[string]interface{}{
		"total_users":      len(fc.users),
		"refresh_interval": fc.refreshInterval.String(),
		"last_refresh":     fc.lastRefresh,
		"is_initialized":   fc.isInitialized,
	}
}

// InvalidateUser removes user from cache (when user is deactivated)
func (fc *UserCache) InvalidateUser(email string) {
	fc.mu.Lock()
	defer fc.mu.Unlock()
	delete(fc.users, email)
}

// UpdateUser updates a specific user in cache (when user data changes)
func (fc *UserCache) UpdateUser(user *CSAgent) {
	fc.mu.Lock()
	defer fc.mu.Unlock()
	fc.users[user.Email] = user
}

// IsUserActive checks if user exists and is active in cache
func (fc *UserCache) IsUserActive(email string) bool {
	fc.mu.RLock()
	defer fc.mu.RUnlock()

	user, exists := fc.users[email]
	return exists && user.Active
}
