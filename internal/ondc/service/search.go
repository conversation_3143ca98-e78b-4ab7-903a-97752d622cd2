package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/search"
	"kc/internal/ondc/utils"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"sync"

	"github.com/Masterminds/semver"
	"github.com/lithammer/fuzzysearch/fuzzy"
)

type SellerProductsResponse struct {
	Source string
	Items  []shared.SellerItems
	Error  error
}

type ScoredProduct struct {
	Product utils.SearchProduct
	Score   float64
}

// HandleSearchReq is logic layer for ONDC search queries
func (s *Service) HandleSearchReq(ctx context.Context, req *dto.AppSearchRequest) (*dto.AppSearchResponses, error) {
	// Unique TransactionID required for the ONDC request
	transactionId := getTransactionID()
	msgId := getMessageID()
	identifier := fmt.Sprintf("%s%s", transactionId, msgId)
	fmt.Println(identifier)

	// users location data
	locationData, _, err := getUserAddress(req.UserID, true, "", s.repository)
	if locationData.ID == nil || err != nil {
		return handleInvalidAddress()
	}

	// adding postal code as hardcoded
	// TODO @sanket handle this when going to prod
	postalCode := "std:080"
	locationData.PostalCode = &postalCode

	// search request context
	searchRequestContext, err := getContext(getDefaultContext(transactionId, *locationData.PostalCode), SEARCH, msgId)
	if err != nil {
		return nil, err
	}

	// search Intent
	searchIntent, err := getSearchIntent(req.Data.CategoryID, &req.Data.Query, locationData)
	if err != nil {
		return nil, err
	}

	// final search request for ONDC
	searchReq := &dto.SearchRequest{
		Context: searchRequestContext,
		Message: &dto.SearchMessage{
			Intent: searchIntent,
		},
	}

	return s.handleONDCSearchRequest(ctx, *searchReq, identifier, transactionId)

}

// HandleONDCSearchRequest
func (s *Service) handleONDCSearchRequest(ctx context.Context, searchReq dto.SearchRequest, identifier, transactionId string) (*dto.AppSearchResponses, error) {
	adjustedReqJSON, err := json.Marshal(searchReq)
	if err != nil {
		logger.Error(ctx, "Marshal adjusted request failed: %v", err)
		return nil, err
	}
	fmt.Println("req is", string(adjustedReqJSON))

	sellerResp := &dto.AppSearchResponses{}
	resp, err := s.syncingONDCRequest(ctx, adjustedReqJSON, identifier, SEARCH)
	if err != nil {
		return nil, err
	}

	redisResp, ok := resp.([]string)
	if !ok {
		logger.Error(ctx, "not able to typecast the redis resp")
		return nil, fmt.Errorf("not able to typecast the redis resp")
	}

	for i, d := range redisResp {
		fmt.Println(i, d)
		tempResp := &dto.AppSearchResponses{}
		err := json.Unmarshal([]byte(d), tempResp)
		for sellerIdx, seller := range tempResp.Data.Sellers {
			providerTTL := seller.ProviderTTL
			sellerProviderID := seller.ProviderID
			for itemIdx, item := range seller.Items {
				tempResp.Data.Sellers[sellerIdx].Items[itemIdx].ProviderID = sellerProviderID
				tempResp.Data.Sellers[sellerIdx].Items[itemIdx].ProviderLocationIDs = item.LocationIds
				tempResp.Data.Sellers[sellerIdx].Items[itemIdx].ProviderTTL = providerTTL

			}
		}
		if err != nil {
			logger.Error(ctx, "not able to unmarshal the redis resp, err is %s", err.Error())
			return nil, err
		}
		sellerResp.Data.Sellers = append(sellerResp.Data.Sellers, tempResp.Data.Sellers...)
		sellerResp.Meta.Context = tempResp.Meta.Context
	}
	sellerResp.Meta.TransactionID = transactionId
	return sellerResp, nil
}

// returns request object for what to search
func getSearchIntent(categoryID string, query *string, locationData dao.UserAddress) (*dto.Intent, error) {
	var cat *dto.Category
	if categoryID != "" {
		cat = &dto.Category{
			ID: categoryID,
		}
	}
	var item = &dto.Item{}
	if query != nil && *query != "" {
		item = &dto.Item{
			Descriptor: &dto.Descriptor{
				Name: *query,
			},
		}
	} else {
		item = nil
	}
	return &dto.Intent{
		Category: cat,
		Item:     item,
		Fulfillment: &dto.Fulfillment{
			Type: "Delivery",
			Stops: []dto.Stops{
				{
					Type: "end",
					Location: dto.Location{
						Gps:      fmt.Sprintf("%f, %f", locationData.Latitude, locationData.Longitude),
						AreaCode: *locationData.PostalCode,
					},
				},
			},
		},
		Payment: &dto.Payment{
			Type: "ON-FULFILLMENT",
		},
		Offer: nil,
		Tags: []dto.TagGroup{
			{
				Descriptor: dto.Descriptor{
					Code: "buyer_id",
				},
				List: []dto.Tag{
					{
						Descriptor: dto.Descriptor{Code: "buyer_id_code"},
						Value:      "gst",
					},
					{
						Descriptor: dto.Descriptor{Code: "buyer_id_no"},
						Value:      "dummy_gst_number",
					},
				},
			},
			{
				Descriptor: dto.Descriptor{
					Code: "bap_terms",
				},
				List: []dto.Tag{
					{
						Descriptor: dto.Descriptor{Code: "finder_fee_type"},
						Value:      "percent",
					},
					{
						Descriptor: dto.Descriptor{Code: "finder_fee_amount"},
						Value:      "0.0",
					},
				},
			},
		},
	}, nil
}

// Calculate weighted similarity score for a product
func calculateScore(query string, product utils.SearchProduct) float64 {
	query = strings.ToLower(query)

	// Extract relevant fields
	code := strings.ToLower(product.Code)
	hindiName := strings.ToLower(product.HindiName)
	nameLabel := strings.ToLower(product.NameLabel)
	name := strings.ToLower(product.Name)
	seller := strings.ToLower(product.Seller)
	sellerName := strings.ToLower(product.SellerName)
	category := strings.ToLower(product.Category)
	keywords := product.Keywords

	// Calculate scores for each field
	codeScore := matchScore(query, code)
	hindiNameScore := matchScore(query, hindiName)
	nameLabelScore := matchScore(query, nameLabel)
	categoryScore := matchScore(query, category)
	sellerScore := matchScore(query, seller)
	sellerNameScore := matchScore(query, sellerName)
	nameScore := matchScore(query, name)
	var keywordScore float64 = 0
	for _, keyword := range keywords {
		keywordScore = max(keywordScore, matchScore(query, strings.ToLower(keyword)))
	}

	weights := utils.SEARCH_PRODUCTS_WEIGTHS

	// Combine scores with weights
	totalScore := codeScore*weights["code"] +
		hindiNameScore*weights["hindi_name"] +
		nameLabelScore*weights["name_label"] +
		categoryScore*weights["category"] +
		sellerScore*weights["seller"] +
		sellerNameScore*weights["seller_name"] +
		nameScore*weights["name"] +
		keywordScore*weights["keywords"]
	return totalScore
}

// Calculate match score using fuzzysearch
func matchScore(query, field string) float64 {
	if fuzzy.Match(query, field) {
		return float64(len(query)) / float64(len(field))
	}
	return 0
}

func SearchProducts(query string) []ScoredProduct {
	query = strings.ToLower(query)

	// Number of workers
	numWorkers := 4
	productChunks := chunkProducts(utils.SEARCH_PRODUCTS, numWorkers)
	resultsChan := make(chan []ScoredProduct)
	var wg sync.WaitGroup

	// Spawn workers
	for _, chunk := range productChunks {
		wg.Add(1)
		go func(chunk []utils.SearchProduct) {
			defer wg.Done()
			resultsChan <- scoreProducts(chunk, query)
		}(chunk)
	}

	// Close channel when all workers are done
	go func() {
		wg.Wait()
		close(resultsChan)
	}()

	// Collect results
	var scoredResults []ScoredProduct
	for chunkResults := range resultsChan {
		scoredResults = append(scoredResults, chunkResults...)
	}

	// Sort results by score
	sort.Slice(scoredResults, func(i, j int) bool {
		return scoredResults[i].Score > scoredResults[j].Score
	})

	return scoredResults
}

// Helper function to divide products into chunks for parallel processing
func chunkProducts(products []utils.SearchProduct, numChunks int) [][]utils.SearchProduct {
	chunkSize := (len(products) + numChunks - 1) / numChunks // Ceiling division
	var chunks [][]utils.SearchProduct

	for i := 0; i < len(products); i += chunkSize {
		end := i + chunkSize
		if end > len(products) {
			end = len(products)
		}
		chunks = append(chunks, products[i:end])
	}
	return chunks
}

// Helper function to score a chunk of products
func scoreProducts(chunk []utils.SearchProduct, query string) []ScoredProduct {
	var results []ScoredProduct
	for _, product := range chunk {
		score := calculateScore(query, product)
		if score > 0 {
			results = append(results, ScoredProduct{Product: product, Score: score})
		}
	}
	return results
}

func (s *Service) getProductsFromService(ctx context.Context, userId string, source *string, seller string, lat float64, long float64, postalCode string, productIDs []string, reqMeta dto.Meta) ([]shared.SellerItems, error) {
	exclusive := true
	request := &dto.AppSearchRequest{
		UserID: userId,
		Data: dto.AppSearchRequestData{
			Exclusive:     &exclusive,
			Source:        source,
			Seller:        seller,
			Latitude:      lat,
			Longitude:     long,
			PostalCode:    postalCode,
			TopProductIds: productIDs,
		},
		Meta: reqMeta,
	}

	productsResponse, err := s.GetProductsV2(ctx, request)
	if err != nil {
		return nil, err
	}

	if productsResponse.Data.Items == nil {
		return []shared.SellerItems{}, nil
	}

	items, ok := productsResponse.Data.Items.([]shared.SellerItems)
	if !ok {
		return nil, fmt.Errorf("error in typecasting the items")
	}

	return items, nil
}

func (s *Service) fetchSellerData(ctx context.Context, seller string, source string, productIDs []string, req *dto.AppSearchRequest, resultChan chan<- SellerProductsResponse) {
	items, err := s.getProductsFromService(ctx, req.UserID, &source, seller, req.Data.Latitude, req.Data.Longitude, req.Data.PostalCode, productIDs, req.Meta)
	resultChan <- SellerProductsResponse{Source: source, Items: items, Error: err}
}

// HandleProductsSearch is logic layer for ONDC search queries
func (s *Service) HandleProductsSearch(ctx context.Context, req *dto.AppSearchRequest) (*dto.AppProductsSearchResponses, error) {
	if req.Data.Query == "" {
		return &dto.AppProductsSearchResponses{
			Data: dto.AppProductsSearchData{
				Widgets: []dto.Widget{},
			},
		}, nil
	}

	if req.UserID == "jy8mOXgqYLgGKk1rwvBfVVj8vYI3" {
		reqJson, err := json.Marshal(req)
		if err != nil {
			logger.Error(ctx, "error in marshalling the request")
			return nil, err
		}
		slack.SendSlackMessage("NAKSHATA SEARCH REQUEST")
		slack.SendSlackMessage(string(reqJson))
	}

	// TODO: @sitaram remove this after location issue is fixed from FE
	userAppVersion, _ := semver.NewVersion(req.Meta.AppVersion)
	paymentsApkAppVersion, _ := semver.NewVersion("6.5.0")
	if userAppVersion.GreaterThan(paymentsApkAppVersion) || userAppVersion.Equal(paymentsApkAppVersion) {
		req.Data.Latitude = 0
		req.Data.Longitude = 0
	}
	var excludeSellers []string
	if req.Data.Seller == "" {
		excludeSellers = append(excludeSellers, utils.RSB_SUPER_STOCKIST)
	}
	productSearchService := search.NewProductSearchService(s.ElasticSearchClient, utils.SEARCH_PRODUCTS_INDEX, utils.SEARCH_PRODUCTS_ENHANCED_INDEX,
		utils.SEARCH_CATEGORIES_INDEX, utils.SEARCH_CATEGORIES_ENHANCED_INDEX, utils.SEARCH_SELLER_WEIGTHS)
	searchResults, err := productSearchService.SearchProductsWithScores(ctx, req.Data.Query, req.Data.Seller, excludeSellers, utils.SEARCH_PRODUCTS_OFFSET, utils.SEARCH_PRODUCTS_LIMIT, utils.SEARCH_DIVERSIFIED_SELLERS, utils.SEARCH_INNER_HITS_SIZE, utils.SEARCH_ENABLE_PHONETIC, utils.SEARCH_PRODUCTS_INDEX_WEIGHT, utils.SEARCH_CATEGORIES_INDEX_WEIGHT)
	if err != nil {
		logger.Error(ctx, "error in searching products: %v", err)
		return nil, err
	}

	productsToFetch := make([]string, 0)
	for _, result := range searchResults.Products {
		productsToFetch = append(productsToFetch, strconv.Itoa(int(result.ProductID)))
	}

	// results := SearchProducts(req.Data.Query)
	// productsToFetch := make([]string, 0)
	// for _, result := range results {
	// 	if len(productsToFetch) >= 10 {
	// 		break
	// 	}
	// 	productsToFetch = append(productsToFetch, strconv.Itoa(int(result.ProductID)))
	// }

	if len(productsToFetch) == 0 {
		return &dto.AppProductsSearchResponses{
			Data: dto.AppProductsSearchData{
				Widgets: []dto.Widget{},
			},
		}, nil
	}

	widgets := make([]dto.Widget, 0)

	if req.Data.Source != nil {
		productsResponse, err := s.getProductsFromService(ctx, req.UserID, req.Data.Source, req.Data.Seller, req.Data.Latitude, req.Data.Longitude, req.Data.PostalCode, productsToFetch, req.Meta)
		if err != nil {
			return nil, err
		}
		if len(productsResponse) > 0 {
			for _, item := range productsResponse {
				if reflect.ValueOf(item.IsOos).IsNil() {
					widgets = append(widgets, item)
				}
			}
		}

		// type43Widget := map[string]interface{}{}
		// err = json.Unmarshal([]byte(type43WidgetSearch1), &type43Widget)
		// if err != nil {
		// 	logger.Error(ctx, "error in unmarshalling the type 43 widget data")
		// 	return nil, err
		// }

		// widgets = append(widgets, type43Widget)

		return &dto.AppProductsSearchResponses{
			Data: dto.AppProductsSearchData{
				Widgets: widgets,
			},
		}, nil
	}

	query := fmt.Sprintf(`SELECT kc.category,
				kc.source,
				kp.* FROM
			kiranabazar_products kp
			JOIN kiranabazar_categories kc
			ON kp.category_id = kc.id
			WHERE kp.is_active = true
			AND kp.is_oos = false
			AND kp.id IN (%s) `, strings.Join(productsToFetch, ","))

	if req.Data.Source != nil {
		query = query + fmt.Sprintf(` AND kc.source = '%s'`, *req.Data.Source)
	}
	products := []dao.KiranaBazarProductsAndCategories{}
	_, err = s.repository.CustomQuery(&products, query)
	if err != nil {
		logger.Error(ctx, "error in fetching the products from db")
		return nil, err
	}

	products = utils.SortProductsByScore(products, *searchResults)
	sellerProductsSizeVariantCodeMap := make(map[string][]string)
	for _, product := range products {
		if _, ok := sellerProductsSizeVariantCodeMap[product.Source]; !ok {
			sellerProductsSizeVariantCodeMap[product.Source] = make([]string, 0)
		}
		sellerProductsSizeVariantCodeMap[product.Source] = append(sellerProductsSizeVariantCodeMap[product.Source], strconv.FormatInt(product.SizeVariantCode, 10))
	}
	for seller, sizeVariantCodes := range sellerProductsSizeVariantCodeMap {
		sellerProductsSizeVariantCodeMap[seller] = utils.RemoveDuplicates(sizeVariantCodes).([]string)
	}

	resultChan := make(chan SellerProductsResponse)
	var wg sync.WaitGroup

	// Start goroutines for each seller
	for source, productIDs := range sellerProductsSizeVariantCodeMap {
		wg.Add(1)
		go func(source string, productIDs []string) {
			defer wg.Done()
			seller, exists := brands.GetSellerBySource(source)
			if exists {
				s.fetchSellerData(ctx, seller, source, productIDs, req, resultChan)
			} else {
				slack.SendSlackMessage(fmt.Sprintf("Seller %s not found in brands cache while fetching products for search", source))
			}
		}(source, productIDs)
	}

	go func() {
		wg.Wait()
		close(resultChan)
	}()

	for res := range resultChan {

		seller, exists := brands.GetSellerBySource(res.Source)

		if !exists {
			return nil, fmt.Errorf("seller %s not found", seller)
		}

		if res.Error != nil {
			fmt.Printf("Error fetching data for seller %s: %v\n", res.Source, res.Error)
		} else {
			fmt.Println("Items fetched for seller ", res.Source)
			sellerData, exists := utils.SEARCH_WIDGET_META[seller]
			if !exists {
				fmt.Printf("No widget meta found for seller %s\n", res.Source)
				continue
			}
			widget := dto.WidgetType39{
				BrandRating: sellerData.(map[string]interface{})["rating"].(float64),
				Cta: dto.Cta{
					PrimaryColor:      utils.StrPtr("#4A5961"),
					SecondaryColor:    utils.StrPtr("#FFFFFF"),
					Text:              "सारे प्रोडक्ट्स देखें",
					MixpanelEventName: "view_all_products search cta clicked",
					Nav: &shared.Nav{
						Name:    "OrderingModule",
						NavType: "Redirect to Screen",
						Params: map[string]interface{}{
							"params": map[string]interface{}{
								"seller": seller,
								"source": res.Source,
							},
							"screen": "Products",
							"seller": seller,
							"source": res.Source,
						},
					},
				},
				Data:        res.Items,
				Description: sellerData.(map[string]interface{})["description"].(string),
				Id:          sellerData.(map[string]interface{})["widget_id"].(int),
				DescriptionIcon: map[string]interface{}{
					"color":    "#FFD100",
					"icon":     "bag",
					"icon_set": "Ionicons",
				},
				ExpiryTime:  1766829527593,
				Heading:     sellerData.(map[string]interface{})["heading"].(string),
				Type:        39,
				Versions:    ">=6.3.1",
				VisibleFrom: 1734688727593,
				WidgetInfo: dto.WidgetInfo{
					WidgetName: fmt.Sprintf("Search Brand Widget %s", seller),
				},
			}
			widgets = append(widgets, widget)
		}
	}

	// if req.UserID == "b8JVr0PvfsNgy9YuKahDseEN4kn1" || req.UserID == "wMn4a578ChXPCC34Vl2Qn7KZFHV2" || req.UserID == "A0dtcDcX3Te9s2oZXkOhXXVva5m1" {
	// 	data := make([]dto.Widget, 0)
	// 	err = json.Unmarshal([]byte(utils.PRODUCTS_SEARCH_WIDGETS), &data)
	// 	if err != nil {
	// 		logger.Error(ctx, "error in unmarshalling the widget data")
	// 		return nil, err
	// 	}
	// 	widgets = data
	// }

	orderedWidgets := utils.SortItemsBySellerOrder(searchResults.SellerScores, widgets)

	// type43Widget := map[string]interface{}{}
	// err = json.Unmarshal([]byte(type43WidgetSearch2), &type43Widget)
	// if err != nil {
	// 	logger.Error(ctx, "error in unmarshalling the type 43 widget data")
	// 	return nil, err
	// }

	// type33Widget1 := map[string]interface{}{}
	// err = json.Unmarshal([]byte(type33WidgetSearch1), &type33Widget1)
	// if err != nil {
	// 	logger.Error(ctx, "error in unmarshalling the type 33 widget data")
	// 	return nil, err
	// }

	// type33Widget2 := map[string]interface{}{}
	// err = json.Unmarshal([]byte(type33WidgetSearch2), &type33Widget2)
	// if err != nil {
	// 	logger.Error(ctx, "error in unmarshalling the type 33 widget data")
	// 	return nil, err
	// }

	// orderedWidgets = append(orderedWidgets, type33Widget1, type33Widget2)

	// orderedWidgets = append(orderedWidgets, type43Widget)
	respData := dto.AppProductsSearchData{
		Widgets: orderedWidgets,
	}

	resp := &dto.AppProductsSearchResponses{
		Data: respData,
	}
	return resp, nil
}
