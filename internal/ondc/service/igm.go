package service

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/external/zoho"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/utils"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-sql-driver/mysql"
	"github.com/google/uuid"
	"github.com/mixpanel/mixpanel-go"
	"gorm.io/gorm"
)

var (
	ticketStorage      = make(map[int64]dto.Ticket)
	ticketStorageMutex = &sync.Mutex{}
)
var (
	conversationStorage      = make(map[int64][]dto.Conversations)
	conversationStorageMutex sync.Mutex
)

var ISSUE_CATEGORIES = map[string]string{
	"ORDER":       "ORDER",
	"ITEM":        "ITEM",
	"FULFILLMENT": "FULFILLMENT",
	"AGENT":       "AGENT",
	"PAYMENT":     "PAYMENT",
	"TRANSACTION": "TRANSACTION",
}

var ISSUE_SUBCATEGORY = map[string]string{
	"ORD01": "ORD01",
	"ORD02": "ORD02",
	"ORD03": "ORD03",
	"ITM01": "ITM01",
	"ITM02": "ITM02",
	"ITM03": "ITM03",
	"ITM04": "ITM04",
	"FLM01": "FLM01",
	"FLM02": "FLM02",
	"FLM03": "FLM03",
	"FLM04": "FLM04",
	"FLM05": "FLM05",
	"FLM06": "FLM06",
	"FLM07": "FLM07",
	"AGT01": "AGT01",
	"AGT02": "AGT02",
	"PMT01": "PMT01",
	"PMT02": "PMT02",
	"PMT03": "PMT03",
	"PMT04": "PMT04",
}

var ISSUE_TYPE = map[string]string{
	"ISSUE":     "ISSUE",
	"GRIEVANCE": "GRIEVANCE",
	"DISPUTE":   "DISPUTE",
}

var ISSUE_SOURCE = map[string]string{
	"CONSUMER":       "CONSUMER",
	"SELLER":         "SELLER",
	"INTERFACING-NP": "INTERFACING-NP",
}

var ISSUE_STATUS = map[string]string{
	"OPEN":   "OPEN",
	"CLOSED": "CLOSED",
}

var COMPLAINANT_ACTION = map[string]string{
	"OPEN":     "OPEN",
	"ESCALATE": "ESCALATE",
	"CLOSE":    "CLOSE",
}

// the below function returns the details of the complaianant
func getComplianantInfo(orderDetails *dao.KiranaBazarOrderDetails) (dto.ComplainantInfo, error) {
	complianantInfo := dto.ComplainantInfo{}

	complianantInfo.Contact = dto.Contact{
		Phone: *orderDetails.ShippingAddress.Phone,
		Email: "",
	}
	// image of the user currently sharing dummy image
	userProfileAvtr := "https://kiranaclubstorage.blob.core.windows.net/thumbnails/webp/image_entries/profile-pics/wMn4a578ChXPCC34Vl2Qn7KZFHV2.webp"
	userProfileDOB := "31/08/1999"
	gender := "male"
	complianantInfo.Person = dto.Person{
		Name: *orderDetails.ShippingAddress.Name,
		Image: &dto.Image{
			Value: userProfileAvtr,
		},
		Dob:    userProfileDOB,
		Gender: gender,
	}
	return complianantInfo, nil
}

func getAdditionalInfo(issueID string, orderDetails *dao.KiranaBazarOrderDetails) ([]dto.AdditionalInfo, error) {
	additionalInfo := []dto.AdditionalInfo{}
	if issueID == "" {
		return nil, nil
	}
	additionalInfo = append(additionalInfo, dto.AdditionalInfo{
		InfoRequired: dto.IssueUpdateInfo{
			IssueUpdateInfo: dto.Description{
				Name:      "sanket",
				Code:      "sanket",
				Symbol:    "symbol 2",
				ShortDesc: "thisnis the shrt desc for the new ticjet created",
				LongDesc:  "thisiw the ling dexctio fir the new ticket created",
				Images:    []string{"this is the image array.com"},
				Audio:     "this it he new audio string.com",
			},
			UpdatedAt: time.Now(),
			MessageID: "wq",
		},
		InfoProvided: dto.IssueUpdateInfo{
			IssueUpdateInfo: dto.Description{
				Name:      "pop",
				ShortDesc: "this nis the short desc1",
				Symbol:    "symbol",
				LongDesc:  "this is the long desc for the order undelivered",
				Images:    []string{"https://sanket.wable"},
				Audio:     "here is the audiio.com",
				Code:      "jhgj",
			},
			UpdatedAt: time.Now(),
			MessageID: "message id",
		},
	})
	return additionalInfo, nil
}

func getComplaintDescription(req *dto.AppIssueRequest) (dto.Description, error) {
	desc := dto.Description{}
	var err error

	return desc, err

}
func getIssueMessage(req *dto.AppIssueRequest, repo *sqlRepo.Repository, orderDetails *dao.KiranaBazarOrderDetail, orderMetaDetails *dao.KiranaBazarOrderDetails) (*dto.IssueMessage, uint64, error) {

	// handle here what exactly to return from the function with the proper response
	// currently hardcoded the order id which has already been used

	orderID, err := strconv.Atoi(req.Data.OrderID)
	if err != nil {
		return nil, 0, err
	}
	intOrderID := uint64(orderID)

	complainantInfo, err := getComplianantInfo(orderMetaDetails)
	if err != nil {
		return nil, intOrderID, err
	}

	additionalInfo, err := getAdditionalInfo(req.Data.IssueID, orderMetaDetails)
	if err != nil {
		return nil, 0, err
	}

	complaintDesription, err := getComplaintDescription(req)
	if err != nil {
		return nil, 0, err
	}
	return &dto.IssueMessage{
		Issue: dto.Issue{
			ID:              getIssueID(req.Data.IssueID),
			ComplainantInfo: complainantInfo,
			OrderDetails: dto.IssueOrderDetails{
				ID: orderMetaDetails.ONDCOrderID,
			},
			AdditionalInfoRequired: additionalInfo,
			Category:               ISSUE_CATEGORIES["ORDER"],
			SubCategory:            ISSUE_SUBCATEGORY["ORD01"],
			IssueType:              ISSUE_TYPE["ISSUE"],
			CreatedAt:              time.Now(),
			Description:            complaintDesription,
			Status:                 ISSUE_STATUS["OPEN"],
			UpdatedAt:              time.Now(),
			IssueActions: dto.IssueActions{
				ComplainantActions: []dto.ComplainantAction{
					{
						ComplainantAction: COMPLAINANT_ACTION["ESCALATE"],
						UpdatedAt:         time.Now(),
						UpdatedBy: dto.UpdatedBy{
							Org: dto.Organization{
								Name: "Kirana Club",
							},
							Contact: dto.Contact{
								Phone: "9521205830",
								Email: "<EMAIL>",
							},
							Person: dto.Person{
								Name: "Wable Sanket",
								Image: &dto.Image{
									Value: "https://avatars.githubusercontent.com/u/97028034?v=4",
								},
							},
						},
						ShortDesc: "kuch toh description he + 100",
					},
				},
			},
			Source: dto.Source{
				Type: ISSUE_SOURCE["CONSUMER"],
			},
		},
	}, intOrderID, nil
}

func (s *Service) Issues(ctx context.Context, req *dto.AppIssueRequest) (*dto.AppIssueResponse, error) {
	// transactionID := *req.Meta.Context.TransactionID
	// if req.Data.IssueID != "" {
	// 	transactionID = getTransactionID()
	// }
	kcOrderID := req.Data.OrderID

	orderDetail := dao.KiranaBazarOrder{}
	_, err := s.repository.Find(map[string]interface{}{
		"id": kcOrderID,
	}, &orderDetail)

	if err != nil {
		return nil, err
	}

	orderDetails := dao.KiranaBazarOrderDetail{}
	_, err = s.repository.Find(map[string]interface{}{
		"order_id": kcOrderID,
	}, &orderDetails)

	details := dao.KiranaBazarOrderDetails{}
	err = json.Unmarshal(orderDetails.OrderDetails, &details)
	if err != nil {
		return nil, err
	}

	msgId := getMessageID()

	identifier := fmt.Sprintf("%s%s", *orderDetail.TransactionID, msgId)

	issueRequestContext, err := getContext(getDefaultContext(*orderDetail.TransactionID, ""), ISSUE, msgId)
	if err != nil {
		return nil, err
	}

	issueMessage, orderID, err := getIssueMessage(req, s.repository, &orderDetails, &details)
	if err != nil {
		return nil, err
	}

	issueRequest := dto.IssueRequest{
		Context: issueRequestContext,
		Message: issueMessage,
	}

	return s.handleONDCIssueRequest(ctx, orderID, issueRequest, identifier, *orderDetail.TransactionID)
}

func (s *Service) handleONDCIssueRequest(ctx context.Context, orderID uint64, issueRequest dto.IssueRequest, identifier, transactionID string) (*dto.AppIssueResponse, error) {
	adjustedReqJSON, err := json.Marshal(issueRequest)
	if err != nil {
		logger.Error(ctx, "Marshal adjusted request failed: %v", err)
		return nil, err
	}

	fmt.Println("ISSUE req is", string(adjustedReqJSON))

	issue := dao.KiranaBazarIssue{
		TransactionID: transactionID,
		ID:            issueRequest.Message.Issue.ID,
		Type:          issueRequest.Message.Issue.IssueType,
		SubCategory:   issueRequest.Message.Issue.SubCategory,
		Category:      issueRequest.Message.Issue.Category,
		ONDCOrderID:   issueRequest.Message.Issue.OrderDetails.ID,
		Status:        issueRequest.Message.Issue.Status,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
		OrderID:       orderID,
	}

	respondentInfo, err := json.Marshal(issueRequest.Message.Issue.ComplainantInfo)
	if err != nil {
		return nil, err
	}

	issueAction, err := json.Marshal(issueRequest.Message.Issue.IssueActions)
	if err != nil {
		return nil, err
	}

	issueMessage := dao.KiranaBazarIssueMessage{
		RespondentInfo: respondentInfo,
		Action:         issueAction,
		CreatedAt:      time.Now(),
		IssueID:        issueRequest.Message.Issue.ID,
	}

	// Creating Issue datastore
	_, err = s.repository.Save(&issue)
	if err != nil {
		return nil, err
	}

	issueMessage.IssueID = issue.ID

	// Creating Issue Message datastore
	_, err = s.repository.Create(&issueMessage)
	if err != nil {
		return nil, err
	}

	resp, err := s.onlySendingRequest(ctx, adjustedReqJSON, identifier, ISSUE)
	if err != nil {
		logger.Error(ctx, "ISSUE request failed: %v", err)
		return nil, err
	}

	ackResponse := dto.AckResponse{}
	err = json.Unmarshal(resp.([]byte), &ackResponse)
	if err != nil {
		return nil, err
	}

	appIssueResponse := &dto.AppIssueResponse{
		Data: dto.AppIssueResponseData{},
	}
	return appIssueResponse, nil
}

// need to implement issues status TODO: @sanketwable
func (s *Service) IssuesStatus(ctx context.Context, req *dto.AppIssueStatusRequest) (*dto.AppIssueStatusResponse, error) {
	response := &dto.AppIssueStatusResponse{}
	return response, nil

}

func (s *Service) OnIssueRequest(ctx context.Context, req *dto.OnIssueRequest) error {
	// keyIdentifier := fmt.Sprintf("%s%s", *req.Context.TransactionID, *req.Context.MessageID)

	action, err := json.Marshal(req.Message.Issue.IssueActions)
	if err != nil {
		return err
	}
	reqByt, err := json.Marshal(req)
	if err != nil {
		return err
	}
	issueMessage := dao.KiranaBazarIssueMessage{
		IssueID:   req.Message.Issue.ID,
		Action:    action,
		CreatedAt: time.Now(),
		Meta:      reqByt,
	}
	// creating issue message
	s.repository.Create(&issueMessage)

	// update issue status
	s.repository.Update(&dao.KiranaBazarIssue{
		ID: req.Message.Issue.ID,
	}, &dao.KiranaBazarIssue{
		Status:    req.Message.Issue.Status,
		UpdatedAt: time.Now(),
	})

	appIssueResp := &dto.AppIssueResponse{
		Meta: dto.Meta{
			BppUrl:  req.Context.BppURI,
			BppID:   req.Context.BppID,
			Context: *req.Context,
		},
		Data: dto.AppIssueResponseData{
			Issue: req.Message.Issue,
		},
	}

	if req.Error != nil {
		appIssueResp.Error = dto.AppResponseError{
			Code:        req.Error.Code,
			Description: &req.Error.Path,
			Type:        &req.Error.Type,
		}
	}
	// marshalledResponse, err := json.Marshal(appIssueResp)
	// if err != nil {
	// 	return err
	// }
	// fmt.Println(string(marshalledResponse))
	// _, err = s.cache.Create(ctx, keyIdentifier, appIssueResp)
	// if err != nil {
	// 	return err
	// }
	return nil
}

func (s *Service) OnIssueStatusRequest(ctx context.Context, req *dto.OnIssueRequest) error {
	// keyIdentifier := fmt.Sprintf("%s%s", *req.Context.TransactionID, *req.Context.MessageID)

	action, err := json.Marshal(req.Message.Issue.IssueActions)
	if err != nil {
		return err
	}
	reqByt, err := json.Marshal(req)
	if err != nil {
		return err
	}
	issueMessage := dao.KiranaBazarIssueMessage{
		IssueID:   req.Message.Issue.ID,
		Action:    action,
		CreatedAt: time.Now(),
		Meta:      reqByt,
	}
	// creating issue message
	s.repository.Create(&issueMessage)

	// update issue status
	s.repository.Update(&dao.KiranaBazarIssue{
		ID: req.Message.Issue.ID,
	}, &dao.KiranaBazarIssue{
		Status:    req.Message.Issue.Status,
		UpdatedAt: time.Now(),
	})

	appIssueResp := &dto.AppIssueResponse{
		Meta: dto.Meta{
			BppUrl:  req.Context.BppURI,
			BppID:   req.Context.BppID,
			Context: *req.Context,
		},
		Data: dto.AppIssueResponseData{
			Issue: req.Message.Issue,
		},
	}

	if req.Error != nil {
		appIssueResp.Error = dto.AppResponseError{
			Code:        req.Error.Code,
			Description: &req.Error.Path,
			Type:        &req.Error.Type,
		}
	}
	// marshalledResponse, err := json.Marshal(appIssueResp)
	// if err != nil {
	// 	return err
	// }
	// fmt.Println(string(marshalledResponse))
	// _, err = s.cache.Create(ctx, keyIdentifier, appIssueResp)
	// if err != nil {
	// 	return err
	// }
	return nil
}

func (s *Service) GetIGMTickets(ctx context.Context, req *dto.GetTicketsRequest) (dto.GetTicketsResponse, error) {
	limit := 10
	offset := req.Meta.Offset

	if req.Meta.Limit > 0 {
		limit = req.Meta.Limit
	}

	_ = req.Data.Seller
	status := req.Data.Status

	tickets := &[]dao.KcBazarTicket{}

	baseQuery := "SELECT * FROM kc_bazar_tickets"
	condition := fmt.Sprintf(`WHERE 1=1 and user_id = "%s" `, req.UserID)

	if len(status) > 0 {
		formattedStatus := "'" + strings.Join(status, "', '") + "'"
		condition = fmt.Sprintf(" %s AND status IN (%s)", condition, formattedStatus)
	}

	finalQuery := fmt.Sprintf("%s %s ORDER BY created_at DESC LIMIT %d OFFSET %d", baseQuery, condition, limit, offset)
	if _, err := s.repository.CustomQuery(tickets, finalQuery); err != nil {
		errorResponse := dto.GetTicketsResponse{
			Meta: dto.Meta{
				Limit:   limit,
				Offset:  offset,
				Context: dto.Context{},
			},
			Data: dto.TicketsData{
				Tickets: []dto.Ticket{},
			},
			Error: dto.AppResponseError{
				Code:        utils.StrPtr("500"),
				Message:     utils.StrPtr("Failed to retrieve tickets"),
				Description: utils.StrPtr(err.Error()),
				Type:        utils.StrPtr("DatabaseError"),
			},
		}
		return errorResponse, err
	}
	ticketResponses := make([]dto.Ticket, len(*tickets))
	for i, ticket := range *tickets {
		ticketID := ticket.ID
		ticketResponses[i] = dto.Ticket{
			ID:          &ticketID,
			Type:        ticket.Type,
			Category:    ticket.Category,
			SubCategory: ticket.SubCategory,
			Description: ticket.Description,
			Headline:    ticket.Description,
			Status:      ticket.Status,
			Rating: &dto.TicketRating{
				Rating:      ticket.Rating,
				Description: ticket.RatingDescription,
			},
			CreatedAt: ticket.CreatedAt,
			UpdatedAt: ticket.UpdatedAt,
		}
	}

	response := dto.GetTicketsResponse{
		Meta: dto.Meta{
			Limit:   limit,
			Offset:  offset,
			Context: dto.Context{},
		},
		Data: dto.TicketsData{
			Tickets: ticketResponses,
		},
		Error: dto.AppResponseError{
			Code:        nil,
			Message:     nil,
			Description: nil,
			Type:        nil,
		},
	}

	return response, nil
}

func (s *Service) GetIGMTicketDetails(ctx context.Context, ticketId int64) (dto.GetTicketDetailsResponse, error) {

	query := fmt.Sprintf(`	
		SELECT 
			ko.id as order_id, ko.order_status, ko.created_at as placed_at, 
			kcbt.id as ticket_id, kcbt.status as ticket_status, kcbt.description, kcbt.type, kcbt.rating, kcbt.rating_description,
			kcbt.category, kcbt.sub_category, kcbt.created_at, kcbt.updated_at, kcbta.attachment 
		FROM 
			kc_bazar_tickets kcbt 
		LEFT JOIN 
			kiranabazar_orders ko ON ko.id = kcbt.order_id 
		LEFT JOIN 
			kc_bazar_tickets_attachments kcbta ON kcbt.id = kcbta.ticket_id 
		WHERE 
			kcbt.id = %d`,
		ticketId)

	var ticketDetails dao.IGMTicketDetails
	if _, err := s.repository.CustomQuery(&ticketDetails, query); err != nil {
		return dto.GetTicketDetailsResponse{
			Meta: dto.Meta{Context: dto.Context{}},
			Error: dto.AppResponseError{
				Code:        utils.StrPtr("500"),
				Message:     utils.StrPtr("Failed to retrieve ticket details"),
				Description: utils.StrPtr(err.Error()),
				Type:        utils.StrPtr("DatabaseError"),
			},
		}, err
	}

	attachment := []dto.IGMMedia{}
	if err := json.Unmarshal(ticketDetails.Attachment, &attachment); err != nil {
		return dto.GetTicketDetailsResponse{
			Meta: dto.Meta{Context: dto.Context{}},
			Error: dto.AppResponseError{
				Code:        utils.StrPtr("500"),
				Message:     utils.StrPtr("Failed to retrieve ticket details"),
				Description: utils.StrPtr(err.Error()),
				Type:        utils.StrPtr("DatabaseError"),
			},
		}, err

	}

	attachments := []dto.IGMMedia{}
	for _, attach := range attachment {
		attachments = append(attachments, dto.IGMMedia{
			Type: attach.Type,
			URL:  attach.URL,
		})

	}

	var orderPlacedAtUnix *int64
	if !ticketDetails.OrderPlacedAt.IsZero() {
		unixts := ticketDetails.OrderPlacedAt.Unix()
		orderPlacedAtUnix = &unixts
	} else {
		orderPlacedAtUnix = nil
	}

	ticketData := dto.Ticket{
		ID:          ticketDetails.TicketID,
		Type:        ticketDetails.Type,
		Category:    ticketDetails.Category,
		SubCategory: ticketDetails.SubCategory,
		Description: ticketDetails.Description,
		OrderDetails: &dto.IGMOrderDetail{
			ID:       ticketDetails.OrderID,
			Status:   ticketDetails.OrderStatus,
			PlacedAt: orderPlacedAtUnix,
			CTA: dto.CTA{
				Text: "order dekein",
				Nav:  map[string]interface{}{},
			},
		},
		Rating: &dto.TicketRating{
			Rating:      ticketDetails.Rating,
			Description: ticketDetails.RatingDescription,
		},
		Status:    ticketDetails.TicketStatus,
		CreatedAt: ticketDetails.CreatedAt,
		UpdatedAt: ticketDetails.UpdatedAt,
		Media:     &attachments,
	}

	response := dto.GetTicketDetailsResponse{
		Meta: dto.Meta{
			Context: dto.Context{},
		},
		Data: dto.TicketsData{
			Tickets: []dto.Ticket{ticketData},
		},
		Error: dto.AppResponseError{},
	}

	return response, nil
}

func (s *Service) getSellerFromOrderID(orderId int64) (string, error) {
	order := dao.KiranaBazarOrder{}
	_, err := s.repository.Find(map[string]interface{}{
		"id": orderId,
	}, &order)
	if err != nil {
		return "", err
	}
	return order.Seller, nil
}

func (s *Service) getUserIdFromOrderID(orderId int64) (string, error) {
	order := dao.KiranaBazarOrder{}
	_, err := s.repository.Find(map[string]interface{}{
		"id": orderId,
	}, &order)
	if err != nil {
		return "", err
	}
	return *order.UserID, nil
}

func (s *Service) getPhoneFromOrderID(orderId int64) (string, error) {
	var userPhone uint64
	_, err := s.repository.CustomQuery(&userPhone, fmt.Sprintf(`select customer_phone from kiranaclubdb.kiranabazar_order_details kod where order_id = %d;`, orderId))
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%d", userPhone), nil
}

func (s *Service) getOrderDetailsFromOrderPin(orderPin int64) (*dao.OrderDetailsForIVR, error) {
	var orderDetails dao.OrderDetailsForIVR
	query := fmt.Sprintf(`select ko.seller, kod.order_id as order_id, customer_phone, ko.user_id , ko.display_status, kod.order_details->>"$.shipping_address.name" as name from kiranaclubdb.kiranabazar_order_details kod 
						join kiranaclubdb.kiranabazar_orders ko on ko.id=kod.order_id join kiranaclubdb.kiranabazar_order_pin kop on kop.order_id=ko.id where kop.pin = %d and ko.display_status in (
						'IN_TRANSIT', 'CONFIRMED', 'SHIPMENT_CREATED', 'NDR')`, orderPin)

	_, err := s.repository.CustomQuery(&orderDetails, query)
	if err != nil {
		return nil, err
	}
	if orderDetails.CustomerPhone == "" {
		return nil, fmt.Errorf("no order details found for order_id: %d", orderPin)
	}
	return &orderDetails, nil
}

func HandleDbErr(err error) error {
	if err != nil {
		var mysqlErr *mysql.MySQLError
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return exceptions.GenerateNewServerError(exceptions.DBRecordNotFoundError, err, "not able to find results", http.StatusOK)
		} else if errors.As(err, &mysqlErr) && mysqlErr.Number == 1062 {
			return exceptions.GenerateNewServerError(exceptions.DBDuplicateKeyError, err, "key already exists", http.StatusOK)
		}

		return exceptions.GenerateNewServerError(exceptions.DBReadError, err, "not able to read level data", http.StatusInternalServerError)
	}
	return nil
}
func (s *Service) getContactID(userID string) (string, error) {
	user := dao.ZohoContact{}
	_, err := s.repository.Find(map[string]interface{}{
		"kc_user_id": userID,
	}, &user)

	if err != nil && err != HandleDbErr(gorm.ErrRecordNotFound) {
		return "", err
	}
	if err == HandleDbErr(gorm.ErrRecordNotFound) {
		user := &dao.User{}
		if userID != "" {
			condition := map[string]interface{}{
				"user_id": userID,
			}
			_, err := s.repository.Find(condition, user)
			if err != nil && err != HandleDbErr(gorm.ErrRecordNotFound) {
				return "", err
			}
		}
		if user.ID == 0 {
			return "", errors.New("not able to fetch user details from db")
		}
		firstName := ""
		lastName := user.Name
		phone := *user.Phone
		resp, err := s.CreateZohoContact(context.Background(), dto.ZohoCreateContactRequest{
			LastName:  user.Name,
			OwnerID:   "171893000000221405",
			FirstName: "",
			Phone:     *user.Phone,
			CF: struct {
				Email1      string "json:\"cf_email1\""
				UserID      string "json:\"cf_user_id\""
				Decimal1    string "json:\"cf_decimal1\""
				Picklist1   string "json:\"cf_picklist1\""
				SingleLine1 string "json:\"cf_singleline1\""
			}{
				UserID: userID,
			},
		})
		if err != nil {
			return "", errors.New("not able to create contact with api")
		}
		_, err = s.repository.Create(&dao.ZohoContact{
			ContactID: resp.ID,
			KCUserID:  userID,
			FirstName: firstName,
			LastName:  lastName,
			Phone:     phone,
			CreatedAt: uint64(time.Now().Unix()),
			UpdatedAt: uint64(time.Now().Unix()),
		})
		if err != nil {
			return "", err
		}
		return resp.ID, nil
	}

	return user.ContactID, nil
}

func (s *Service) CreateIGMTicket(ctx context.Context, req *dto.CreateTicketRequest) (interface{}, error) {
	ticketId := uuid.NewString()
	timeStamp := time.Now().Unix()

	var orderID int64 = -1
	if req.Data.Ticket.OrderDetails != nil && req.Data.Ticket.OrderDetails.ID != nil {
		orderID = *req.Data.Ticket.OrderDetails.ID
	}

	ticket := &dao.KcBazarTicket{
		TicketID:    ticketId,
		Type:        req.Data.Ticket.Type,
		UserID:      req.UserID,
		Subject:     req.Data.Ticket.Description,
		Description: req.Data.Ticket.Description,
		Category:    req.Data.Ticket.Category,
		SubCategory: req.Data.Ticket.SubCategory,
		IsSpam:      false,
		IsActive:    true,
		Status:      "OPEN",
		CreatedAt:   &timeStamp,
		UpdatedAt:   &timeStamp,
		OrderID:     &orderID,
	}

	// create ticket on zoho
	seller, err := s.getSellerFromOrderID(orderID)
	if err != nil && err != HandleDbErr(gorm.ErrRecordNotFound) {
		return nil, err
	}
	userPhone, err := s.getPhoneFromOrderID(orderID)
	if err != nil {
		userPhone = "9999999999"
	}
	contactID, err := s.getContactID(req.UserID)
	if err != nil {
		return nil, err
	}
	if contactID == "" {
		return nil, errors.New("not able to create contact")
	}
	departmentID := "171893000000010772"
	language := "English"
	channel := "WEB"
	status := "Open"
	priority := "High"
	assigneeID := "171893000000236001"
	resp, err := s.CreateZohoTicket(ctx, dto.ZohoCreateTicketRequest{
		SubCategory: &req.Data.Ticket.SubCategory,
		Category:    &req.Data.Ticket.Category,
		Cf: &dto.CfFields{
			CfOrderId: fmt.Sprintf("%d", orderID),
			CfSeller:  seller,
			CfUserId:  req.UserID,
		},
		ContactId:    &contactID,
		Subject:      &req.Data.Ticket.Description,
		DepartmentId: &departmentID,
		Language:     &language,
		Channel:      &channel,
		Description:  &req.Data.Ticket.Description,
		Status:       &status,
		Priority:     &priority,
		AssigneeId:   &assigneeID,
		Phone:        &userPhone,
		DueDate:      time.Now().Add(48 * time.Hour).Format("2006-01-02T15:04:05.000Z"),
	})
	if err != nil {
		return nil, err
	}

	for _, j := range *req.Data.Ticket.Media {
		s.AddTicketAttachment(ctx, &dto.AddTicketAttachmentRequest{
			URL:       j.URL,
			MediaType: j.Type,
			TicketID:  resp.Id,
		})
	}

	ticket.TicketID = resp.Id
	_, err = s.repository.Create(ticket)
	if err != nil {
		return nil, err
	}

	insertedTicketId := ticket.ID
	ticketAttachments := &dao.KcBazarTicketsAttachments{
		TicketID: insertedTicketId,
		Attachment: func() string {
			mediaJSON, _ := json.Marshal(req.Data.Ticket.Media)
			return string(mediaJSON)
		}(),
	}

	_, err = s.repository.Create(ticketAttachments)
	if err != nil {
		return nil, err
	}

	// stopping storing 1st user message as conversation
	// ticketConversation := &dao.KcBazarTicketConversation{
	// 	ConversationID: "internal-" + uuid.New().String(),
	// 	TicketID:       uint64(insertedTicketId),
	// 	UserType:       "kc_user",
	// 	UserID:         req.UserID,
	// 	Content:        req.Data.Ticket.Description,
	// 	ContentType:    "plainText",
	// 	CreatedAt:      timeStamp,
	// 	UpdatedAt:      timeStamp,
	// }

	// _, err = s.repository.Create(ticketConversation)
	// if err != nil {
	// 	return nil, err
	// }

	orderDetail := &dto.IGMOrderDetail{}
	if req.Data.Ticket.OrderDetails != nil && req.Data.Ticket.OrderDetails.ID != nil {
		condition := map[string]interface{}{
			"id": *req.Data.Ticket.OrderDetails.ID,
		}
		kbOrder := &dao.KiranaBazarOrder{}
		_, err := s.repository.Find(condition, kbOrder)
		if err != nil {
			return nil, err
		}

		var orderPlacedAtUnix *int64
		if !kbOrder.CreatedAt.IsZero() {
			unixts := kbOrder.CreatedAt.Unix()
			orderPlacedAtUnix = &unixts
		} else {
			orderPlacedAtUnix = nil
		}

		orderDetail = &dto.IGMOrderDetail{
			ID:       kbOrder.ID,
			PlacedAt: orderPlacedAtUnix,
			Status:   *kbOrder.OrderStatus,
			CTA: dto.CTA{
				Text: "order dekein",
				Nav:  map[string]interface{}{},
			},
		}
	}

	response := dto.CreateTicketResponse{
		Meta: dto.Meta{
			Context: dto.Context{},
		},
		Data: dto.TicketsData{
			Tickets: []dto.Ticket{
				{
					ID:           &insertedTicketId,
					Type:         req.Data.Ticket.Type,
					Headline:     req.Data.Ticket.Description,
					Category:     req.Data.Ticket.Category,
					SubCategory:  req.Data.Ticket.SubCategory,
					Description:  req.Data.Ticket.Description,
					OrderDetails: orderDetail,
					CreatedAt:    &timeStamp,
					UpdatedAt:    &timeStamp,
					Status:       "OPEN",
					Media:        req.Data.Ticket.Media,
					Rating:       nil,
				},
			},
		},
		Error: dto.AppResponseError{
			Code:        nil,
			Message:     nil,
			Description: nil,
			Type:        nil,
		},
	}

	eventObject := map[string]interface{}{
		"ticket_id":       insertedTicketId,
		"order_id":        orderID,
		"status":          "OPEN",
		"category":        req.Data.Ticket.Category,
		"sub_category":    req.Data.Ticket.SubCategory,
		"created_at":      timeStamp,
		"updated_at":      timeStamp,
		"media":           req.Data.Ticket.Media,
		"description":     req.Data.Ticket.Description,
		"seller":          seller,
		"ordering_module": utils.MakeTitleCase(seller),
		"order_details": map[string]interface{}{
			"id":        orderDetail.ID,
			"status":    orderDetail.Status,
			"placed_at": orderDetail.PlacedAt,
		},
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Support Ticket Created", req.UserID, eventObject),
	})

	webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{req.UserID},
		EventName:   "Support Ticket Created",
		EventObject: eventObject,
	})

	return response, nil
}

func (s *Service) UpdateIGMTicket(ctx context.Context, request *dto.UpdateTicketRequest) (interface{}, error) {
	rating := request.Data.Ticket.Rating.Rating
	ratingDescription := request.Data.Ticket.Rating.Description
	_, err := s.repository.CustomQuery(nil, fmt.Sprintf(`update kc_bazar_tickets set rating = %d, rating_description = "%s" where id = %d;`, *rating, *ratingDescription, *request.Data.Ticket.ID))
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (s *Service) GetIGMFAQ(ctx context.Context, request interface{}) (interface{}, error) {
	faqs := []dto.FAQData{
		{
			ID:        1,
			Type:      "general_question",
			Question:  "मेरा ऑर्डर कब तक डिलीवर होगा?",
			Answer:    "ऑर्डर आमतौर पर 3-7 दिनों में डिलीवर होता है। डिलीवरी की स्थिति ट्रैक करने के लिए ऐप में \"ऑर्डर देखें\" पर क्लिक करके देखें।",
			CreatedAt: 1731570149,
			UpdatedAt: 1731570149,
		},
		{
			ID:        2,
			Type:      "general_question",
			Question:  "मैं अपने ऑर्डर का स्टेटस कैसे देख सकता हूँ?",
			Answer:    "ऐप में \"ऑर्डर देखें\" पर क्लिक करें। वहाँ आपको अपने सभी ऑर्डर्स का स्टेटस देख सकते है।",
			CreatedAt: 1731570149,
			UpdatedAt: 1731570149,
		},
		{
			ID:        3,
			Type:      "general_question",
			Question:  "क्या मैं ऑर्डर कैंसल कर सकता हूँ?",
			Answer:    "आर्डर कैंसिल करने के लिए कृपया \"समस्या टिकट\" बनाये, किराना क्लब टीम आपकी मदद करेगी।",
			CreatedAt: 1731570149,
			UpdatedAt: 1731570149,
		},
		{
			ID:        4,
			Type:      "general_question",
			Question:  "ऑर्डर की पेमेंट कैसे करें?",
			Answer:    "ज्यादा आर्डर की पेमेंट COD (कैश ऑन डिलीवरी) पर होती है, लेकिन बड़े आर्डर करने पर आपसे थोड़ी राशि एडवांस ले ली जाती है।",
			CreatedAt: 1731570149,
			UpdatedAt: 1731570149,
		},
		{
			ID:        5,
			Type:      "general_question",
			Question:  "मुझे गलत या डैमेज्ड प्रोडक्ट मिला है, क्या करूँ?",
			Answer:    "अगर आपको गलत या डैमेज्ड प्रोडक्ट मिला है, तो आप अपनी समस्या दर्ज करें। हम आपकी समस्या का समाधान जल्द से जल्द करेंगे।",
			CreatedAt: 1731570149,
			UpdatedAt: 1731570149,
		},
		{
			ID:        6,
			Type:      "general_question",
			Question:  "क्या मैं किसी ऑर्डर में बदलाव कर सकता हूँ?",
			Answer:    "आर्डर में बदलाव करने के लिए कृपया \"समस्या टिकट\" बनाये, किराना क्लब टीम आपकी मदद करेगी।",
			CreatedAt: 1731570149,
			UpdatedAt: 1731570149,
		},
	}

	response := dto.FAQResponse{
		Meta: dto.Meta{
			Limit:   10,
			Offset:  0,
			Context: dto.Context{},
		},
		Data: dto.Faqs{
			FAQData: faqs,
		},
		Error: dto.AppResponseError{
			Code:        nil,
			Message:     nil,
			Description: nil,
			Type:        nil,
		},
	}

	return response, nil
}

func (s *Service) GetIGMOrders(ctx context.Context, req *dto.GetIGMOrdersRequest) (dto.GetIGMOrdersResponse, error) {
	limit := 10
	offset := req.Meta.Offset
	seller := req.Data.Seller
	// status := req.Data.Status

	if req.Meta.Limit > 0 {
		limit = req.Meta.Limit
	}

	baseQuery := "SELECT ko.id as order_id, ko.order_status, ko.display_status, ko.created_at, kbop.amount, kcbt.id as ticket_id, kcbt.status as ticket_status FROM kiranabazar_orders ko LEFT JOIN kc_bazar_tickets kcbt ON ko.id = kcbt.order_id LEFT JOIN kiranabazar_order_payments kbop ON ko.id = kbop.order_id"
	condition := fmt.Sprintf("WHERE 1=1 and ko.user_id = '%s' and ko.is_archived = false ", req.UserID)

	if seller != "" && seller != "kirana_club" {
		condition += fmt.Sprintf(" AND ko.seller = '%s'", seller)
	}
	// if len(status) > 0 {
	// 	formattedStatus := "'" + strings.Join(status, "', '") + "'"
	// 	condition += fmt.Sprintf(" AND ko.order_status IN (%s)", formattedStatus)
	// }

	igmOrderData := &[]dao.IGMOrderData{}
	finalQuery := fmt.Sprintf("%s %s ORDER BY ko.created_at DESC LIMIT %d OFFSET %d", baseQuery, condition, limit, offset)
	if _, err := s.repository.CustomQuery(&igmOrderData, finalQuery); err != nil {
		fmt.Println("error is", err)
		errorResponse := dto.GetIGMOrdersResponse{
			Meta: dto.Meta{
				Limit:   limit,
				Offset:  offset,
				Context: dto.Context{},
			},
			Data: dto.IGMOrdersData{
				Orders: []dto.IGMOrder{},
			},
			Error: dto.AppResponseError{
				Code:        utils.StrPtr("500"),
				Message:     utils.StrPtr("Failed to retrieve orders"),
				Description: utils.StrPtr(err.Error()),
				Type:        utils.StrPtr("DatabaseError"),
			},
		}
		return errorResponse, err
	}

	orders := make([]dto.IGMOrder, len(*igmOrderData))
	for i, order := range *igmOrderData {
		orders[i] = dto.IGMOrder{
			ID:       *order.OrderID,
			Status:   *order.DisplayStatus,
			Amount:   int64(order.Amount),
			PlacedAt: order.CreatedAt.Unix(),
		}
		if order.TicketID != nil {
			orders[i].Ticket = &dto.Ticket{
				ID:     order.TicketID,
				Status: order.TicketStatus,
			}
		}
	}

	response := dto.GetIGMOrdersResponse{
		Meta: dto.Meta{
			Limit:   10,
			Offset:  0,
			Context: dto.Context{},
		},
		Data: dto.IGMOrdersData{
			Orders: orders,
		},
		Error: dto.AppResponseError{
			Code:        nil,
			Message:     nil,
			Description: nil,
			Type:        nil,
		},
	}

	return response, nil
}

func (s *Service) GetIssuesCategories(ctx context.Context, queryType string) (interface{}, error) {
	var categories []dto.IssueCategory

	if queryType == "GENERAL_QUESTION" {
		categories = []dto.IssueCategory{
			{Value: "APP_ISSUE", Label: "ऐप से जुड़ी समस्या"},
			{Value: "OFFER_RELATED", Label: "ऑफर/स्कीम सम्बंधित"},
			{Value: "ORDER_NOT_PLACING", Label: "आर्डर नहीं लग रहा"},
			{Value: "REWARD_RELATED", Label: "इनाम सम्बंधित"},
			{Value: "OTHER", Label: "अन्य"},
		}
	} else if queryType == "ORDER_RELATED_QUESTION" {
		categories = []dto.IssueCategory{
			{Value: "ORDER_STATUS", Label: "ऑर्डर की स्थिति"},
			{Value: "DELIVERY_DELAY", Label: "डिलीवरी में देरी"},
			{Value: "WRONG_DAMAGED_PRODUCT", Label: "गलत/डैमेज प्रोडक्ट"},
			{Value: "RETURN_REFUND", Label: "रिटर्न और रिफंड"},
			{Value: "INCOMPLETE_ORDER", Label: "आर्डर पूरा नहीं आया"},
			{Value: "ORDER_CHANGE", Label: "आर्डर में बदलाव"},
			{Value: "ORDER_CANCEL", Label: "आर्डर कैंसिल"},
			{Value: "OFFER_RELATED", Label: "ऑफर के सम्बंधित"},
			{Value: "OTHER", Label: "अन्य"},
		}
	} else {
		return nil, errors.New("invalid type provided")
	}

	response := dto.GetIssuesCategoriesResponse{
		Meta: dto.Meta{
			Context: dto.Context{},
		},
		Data: dto.CategoriesData{
			Categories: categories,
		},
		Error: dto.AppResponseError{
			Code:        nil,
			Message:     nil,
			Description: nil,
			Type:        nil,
		},
	}

	return response, nil
}

func (s *Service) GetIGMConversations(ctx context.Context, ticketId int64) (interface{}, error) {

	conversations := []dao.KcBazarTicketConversationsData{}
	query := fmt.Sprintf(`
		SELECT * 
		FROM kiranaclubdb.kc_bazar_tickets_conversations kbtc
		LEFT JOIN kiranaclubdb.kc_bazar_conversation_attachments kcbca 
		ON kbtc.conversation_id = kcbca.conversation_id 
		WHERE kbtc.ticket_id = %d ORDER BY kbtc.created_at DESC`, ticketId)
	if _, err := s.repository.CustomQuery(&conversations, query); err != nil {
		return nil, err
	}

	compliantActions := []dto.Action{}
	respondentActions := []dto.Action{}
	var userID string

	for _, conversation := range conversations {
		attachment := conversation.Attachment
		var media []dto.IGMMedia
		if attachment != nil {
			err := json.Unmarshal(attachment, &media)
			if err != nil {
				return nil, err
			}
		}

		if conversation.UserType == "kc_user" {
			if conversation.UserID != "" {
				userID = conversation.UserID
			}
			compliantActions = append(compliantActions, dto.Action{
				Text:      conversation.Content,
				Media:     media,
				CreatedAt: conversation.CreatedAt * 1000,
			})
		} else {
			respondentActions = append(respondentActions, dto.Action{
				Text:      conversation.Content,
				Media:     media,
				CreatedAt: conversation.CreatedAt,
			})
		}
	}

	user := &dao.User{}
	if userID != "" {
		condition := map[string]interface{}{
			"user_id": userID,
		}
		_, err := s.repository.Find(condition, user)
		if err != nil && err != HandleDbErr(gorm.ErrRecordNotFound) {
			return nil, err
		}
	}

	response := dto.GetIGMConversationsResponse{
		Meta: dto.Meta{},
		Data: dto.ConversationsData{
			ComplainantInfo: dto.IGMComplainantInfo{
				Name:            user.Name,
				ProfileImageURL: user.ProfileImageURL,
			},
			RespondentInfo: dto.IGMRespondentInfo{
				Name:            "KC-Agent",
				ProfileImageURL: "https://cdn2.vectorstock.com/i/1000x1000/62/36/call-center-agent-operator-avatar-vector-24606236.jpg",
			},
			Conversations: dto.Conversations{
				IssueActions: dto.IGMIssueActions{
					ComplainantActions: compliantActions,
					RespondentActions:  respondentActions,
				},
			},
		},
		Error: dto.AppResponseError{},
	}
	return &response, nil
}

func (s *Service) AddIGMConversations(ctx context.Context, request *dto.IGMConversationsRequest) (*dto.IGMConversationsResponse, error) {
	timeStamp := time.Now().Unix()

	ticketID := request.Data.TicketID
	newConversation := request.Data.Conversations
	userID := request.UserID

	ticketDetails := &dao.KcBazarTicket{}
	_, err := s.repository.Find(map[string]interface{}{
		"id": ticketID,
	}, ticketDetails)
	if err != nil {
		return nil, err
	}

	attachmentIds := []string{}
	if len(newConversation.IssueActions.ComplainantActions[0].Media) > 0 {
		for _, j := range newConversation.IssueActions.ComplainantActions[0].Media {
			mediaType := j.Type
			mediaURl := j.URL
			re, err := s.AddTicketAttachment(ctx, &dto.AddTicketAttachmentRequest{
				URL:       mediaURl,
				TicketID:  ticketDetails.TicketID,
				MediaType: mediaType,
			})
			if err != nil {
				return nil, err
			}
			attachmentIds = append(attachmentIds, re.ID)
		}
	}
	falseString := "false"
	plaintText := "plainText"
	createZohoCommentResponse, err := s.CreateComment(&ctx, dto.ZohoCreateCommentRequest{
		IsPublic:      &falseString,
		ContentType:   &plaintText,
		Content:       &request.Data.Conversations.IssueActions.ComplainantActions[0].Text,
		AttachmentIds: &attachmentIds,
	}, ticketDetails.TicketID)

	if err != nil {
		return nil, err
	}

	conversationId := createZohoCommentResponse.ID
	conv := dao.KcBazarTicketConversation{
		ConversationID: createZohoCommentResponse.ID,
		TicketID:       uint64(*ticketID),
		UserType:       "kc_user",
		Content:        newConversation.IssueActions.ComplainantActions[0].Text,
		ContentType:    "plainText",
		CreatedAt:      timeStamp,
		UpdatedAt:      timeStamp,
		UserID:         userID,
	}

	_, err = s.repository.Create(&conv)
	if err != nil {
		return nil, err
	}
	_, err = s.repository.CustomQuery(nil, fmt.Sprintf(`update kc_bazar_tickets set status = 'OPEN' where id = %d; `, *request.Data.TicketID))
	if err != nil {
		return nil, err
	}

	if len(newConversation.IssueActions.ComplainantActions[0].Media) > 0 {
		attachment := dao.KCBazarConversationAttachment{
			ConversationID: conversationId,
			Attachment: func() []byte {
				mediaJSON, _ := json.Marshal(newConversation.IssueActions.ComplainantActions[0].Media)
				return mediaJSON
			}(),
			CreatedAt: &timeStamp,
			UpdatedAt: &timeStamp,
			IsActive:  true,
		}
		_, err := s.repository.Create(&attachment)
		if err != nil {
			return nil, err
		}
	}

	response := &dto.IGMConversationsResponse{
		Meta: dto.Meta{
			Context: dto.Context{},
		},
		Data: dto.IGMConversationData{
			TicketID:        ticketID,
			Conversations:   newConversation,
			ConversationsID: &conversationId,
		},
		Error: dto.AppResponseError{
			Code:        nil,
			Message:     nil,
			Description: nil,
			Type:        nil,
		},
	}

	return response, nil
}

func (s *Service) AddTicketAttachment(ctx context.Context, request *dto.AddTicketAttachmentRequest) (*dto.ZohoTicketAttachmentResponse, error) {
	resp, err := s.downloadAndUploadToZohoDesk(request.URL, request.TicketID)
	if err != nil {
		return nil, err
	}
	response := dto.ZohoTicketAttachmentResponse{}

	err = json.Unmarshal(resp, &response)
	if err != nil {
		return nil, err
	}
	return &response, nil
}

func (s *Service) downloadAndUploadToZohoDesk(downloadURL, ticketID string) ([]byte, error) {
	// Create a temporary file
	accessToken, err := s.ZohoClient.TempGetToken(zoho.ZOHO_USER)
	if err != nil {
		return nil, fmt.Errorf("error getting access token: %v", err)
	}

	tempFile, err := os.CreateTemp("", "downloaded-*.jpg")
	if err != nil {
		return nil, fmt.Errorf("error creating temp file: %v", err)
	}
	tempFilePath := tempFile.Name()
	defer os.Remove(tempFilePath) // Ensure file is deleted after use
	defer tempFile.Close()

	// Download file from URL
	resp, err := http.Get(downloadURL)
	if err != nil {
		return nil, fmt.Errorf("error downloading file: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("download failed with status: %d", resp.StatusCode)
	}

	// Save downloaded content to temp file
	_, err = io.Copy(tempFile, resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error saving file: %v", err)
	}

	// Prepare multipart upload
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Open temp file for reading
	file, err := os.Open(tempFilePath)
	if err != nil {
		return nil, fmt.Errorf("error opening temp file: %v", err)
	}
	defer file.Close()

	// Create form file
	part, err := writer.CreateFormFile("file", filepath.Base(tempFilePath))
	if err != nil {
		return nil, fmt.Errorf("error creating form file: %v", err)
	}

	// Copy file content
	_, err = io.Copy(part, file)
	if err != nil {
		return nil, fmt.Errorf("error copying file content: %v", err)
	}

	// Close multipart writer
	err = writer.Close()
	if err != nil {
		return nil, fmt.Errorf("error closing multipart writer: %v", err)
	}

	// Prepare Zoho Desk upload request
	uploadURL := fmt.Sprintf("https://desk.zoho.in/api/v1/tickets/%s/attachments", ticketID)
	req, err := http.NewRequest("POST", uploadURL, body)
	if err != nil {
		return nil, fmt.Errorf("error creating upload request: %v", err)
	}

	// Set headers
	req.Header.Set("Authorization", fmt.Sprintf("Zoho-oauthtoken %s", *accessToken))
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// Send upload request
	client := &http.Client{}
	uploadResp, err := client.Do(req)

	if err != nil {
		return nil, fmt.Errorf("error uploading to Zoho Desk: %v", err)
	}
	defer uploadResp.Body.Close()

	// Check upload response status

	bodyr, err := io.ReadAll(uploadResp.Body)

	if uploadResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("upload failed with status: %d", uploadResp.StatusCode)
	}

	return bodyr, nil
}
