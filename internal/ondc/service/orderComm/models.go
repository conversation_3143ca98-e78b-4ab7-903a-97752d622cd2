package ordercomm

import (
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"

	"github.com/go-redis/redis/v8"
)

var ocs *OrderCommService

type OrderCommService struct {
	redisClient *redis.Client
	repository  *sqlRepo.Repository
	mixpanel    *mixpanelRepo.Repository
}

type CommEvent struct {
	Timestamp        int64  `json:"timestamp"`
	OrderID          string `json:"order_id"`
	DisplayStatus    string `json:"display_status"`
	ShipmentStatus   string `json:"shipment_status"`
	ProcessingStatus string `json:"processing_status"`
	PaymentStatus    string `json:"payment_status"`
	EventType        string `json:"event_type"`
}

func NewOrderCommService(redisClient *redis.Client, repository *sqlRepo.Repository, mixpanel *mixpanelRepo.Repository) *OrderCommService {
	orderCommService := &OrderCommService{
		redisClient: redisClient,
		repository:  repository,
		mixpanel:    mixpanel,
	}
	ocs = orderCommService
	return orderCommService
}
