package ordercomm

import (
	"context"
	"encoding/json"
	"fmt"
	orderstatus "kc/internal/ondc/service/orderStatus"
	"time"

	"github.com/go-redis/redis/v8"
)

func getOrderCommunicationRedisKey(orderID string) string {
	return fmt.Sprintf("order::%s::comm_events", orderID)
}

func AddOrderCommunicationEvent(ctx context.Context, orderID string, orderStatuses orderstatus.OrderStatusResponse, eventType string) error {
	event := CommEvent{
		Timestamp:        time.Now().UnixMilli(),
		OrderID:          orderID,
		DisplayStatus:    orderStatuses.DisplayStatus,
		ShipmentStatus:   orderStatuses.ShipmentStatus,
		ProcessingStatus: orderStatuses.ProcessingStatus,
		PaymentStatus:    orderStatuses.PaymentStatus,
		EventType:        eventType,
	}

	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal event: %v", err)
	}

	key := getOrderCommunicationRedisKey(orderID)
	// LPUSH to add to start of list (most recent first)
	err = ocs.redisClient.LPush(ctx, key, data).Err()
	if err != nil {
		return fmt.Errorf("failed to add event to Redis: %v", err)
	}
	ttl, err := ocs.redisClient.TTL(ctx, key).Result()
	if err != nil {
		return fmt.Errorf("failed to get TTL: %v", err)
	}
	if ttl < 0 {
		// Key has no expiry, set it
		if err := ocs.redisClient.Expire(ctx, key, ORDER_REDIS_TTL).Err(); err != nil {
			return fmt.Errorf("failed to set expiry: %v", err)
		}
	}

	return nil
}

func GetLastOrderCommunicationEvent(ctx context.Context, orderID string) (*CommEvent, error) {
	key := fmt.Sprintf("order:%s:events", orderID)

	// Get first item from list
	result, err := ocs.redisClient.LIndex(ctx, key, 0).Result()
	if err == redis.Nil {
		return nil, nil // No events yet
	} else if err != nil {
		return nil, fmt.Errorf("error getting last event: %v", err)
	}

	var event CommEvent
	err = json.Unmarshal([]byte(result), &event)
	if err != nil {
		return nil, fmt.Errorf("error parsing event: %v", err)
	}

	return &event, nil
}

func GetAllOrderCommunicationEvents(ctx context.Context, orderID string) ([]CommEvent, error) {
	key := getOrderCommunicationRedisKey(orderID)

	// Get all items
	results, err := ocs.redisClient.LRange(ctx, key, 0, -1).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get events: %v", err)
	}

	var events []CommEvent
	for _, item := range results {
		var event CommEvent
		if err := json.Unmarshal([]byte(item), &event); err != nil {
			return nil, fmt.Errorf("failed to parse event: %v", err)
		}
		events = append(events, event)
	}

	return events, nil
}
