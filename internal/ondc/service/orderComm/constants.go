package ordercomm

import "time"

const ORDER_REDIS_TTL = 30 * 24 * time.Hour

type OrderCommEventTypes struct {
	ORDER_PLACED                   string
	ORDER_CONFIRMATION             string
	ORDER_CANCELLATION             string
	ORDER_DELIVERED                string
	ORDER_OUT_FOR_DELIVERY         string
	ORDER_REACHED_DESTINATION_CITY string
	ORDER_DISPATCHED               string
	ORDER_PROCESSING               string
	ORDER_IN_TRANSIT               string
	ORDER_BAD_DELIVERY             string
}

var ORDER_COMM_EVENT_TYPES = OrderCommEventTypes{
	ORDER_PLACED:                   "ORDER_PLACED",
	ORDER_CONFIRMATION:             "ORDER_CONFIRMATION",
	ORDER_CANCELLATION:             "ORDER_CANCELLATION",
	ORDER_DELIVERED:                "ORDER_DELIVERED",
	ORDER_OUT_FOR_DELIVERY:         "ORDER_OUT_FOR_DELIVERY",
	ORDER_BAD_DELIVERY:             "ORDER_BAD_DELIVERY",
	ORDER_REACHED_DESTINATION_CITY: "ORDER_REACHED_DESTINATION_CITY",
	ORDER_DISPATCHED:               "ORDER_DISPATCHED",
	ORDER_PROCESSING:               "ORDER_PROCESSING",
	ORDER_IN_TRANSIT:               "ORDER_IN_TRANSIT",
}
