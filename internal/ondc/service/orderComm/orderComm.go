package ordercomm

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/exotel/ivr"
	ivrexotelintegration "kc/internal/ondc/external/exotel/ivrExotelIntegration"
	"kc/internal/ondc/external/whatsapp"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/sqlRepo"
	orderstatus "kc/internal/ondc/service/orderStatus"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	shipmentstatus "kc/internal/ondc/service/orderStatus/shipmentStatus"
	"kc/internal/ondc/utils"
	"strconv"
	"strings"
	"time"
)

// function to schedule IVR for order status
func addDataToIVRQueue(orderID int64, seller string, totalPayableAmount, totalProductPrice float64, IVRCallCount int, userID string, userShippingPhone string, appID int, triggerAt time.Time, requestType string, adjustTimeValue bool, timeAdjustConfig *utils.TimeAdjustConfig) (time.Time, error) {
	triggerat, err := ivrexotelintegration.AddDataToExotelIVRQueue(context.Background(), dto.ExotelIVROrderInfo{
		OrderID:      orderID,
		Seller:       seller,
		OrderValue:   totalPayableAmount,
		CartValue:    totalProductPrice,
		IVRCallCount: IVRCallCount,
		UserID:       userID,
		RequestType:  requestType,
	}, fmt.Sprintf("+91%s", userShippingPhone), ivr.IVR_TO_PHONE, appID, triggerAt, ocs.repository, ocs.mixpanel, requestType, adjustTimeValue, timeAdjustConfig)
	return triggerat, err
}

func GetOrderDetails(sqlRepo *sqlRepo.Repository, orderId string) (*dao.KiranaBazarOrderDetails, error) {
	data := []dao.KiranaBazarOrderDetail{}
	query := fmt.Sprintf("select kod.order_details from kiranabazar_order_details kod where kod.order_id = '%s'", orderId)
	_, err := sqlRepo.CustomQuery(&data, query)
	if err != nil {
		return nil, err
	}

	if len(data) > 0 {
		orderDetails := dao.KiranaBazarOrderDetails{}
		err = json.Unmarshal(data[0].OrderDetails, &orderDetails)
		if err != nil {
			return nil, err
		}
		return &orderDetails, nil
	}
	return nil, nil
}

func SendOrderCommunicationToUser(orderID, userID, orderStatus, orderStatusType string) error {
	orderStatus = strings.ToUpper(orderStatus)
	orderStatuses := orderstatus.MapOrderStatus(orderStatus, orderStatusType, orderstatus.OrderStatusResponse{})
	orderDetails, err := GetOrderDetails(ocs.repository, orderID)
	if err != nil {
		return err
	}

	// TODO: store status and event history in redis

	// schedule events and IVR as per order status
	handleOrderStatusUpdateCommunication(orderID, userID, orderStatuses, orderDetails)
	return nil
}

// function to take final decision for timed events before sending
func handleOrderStatusUpdateCommunication(orderID, userID string, orderStatuses orderstatus.OrderStatusResponse, orderDetails *dao.KiranaBazarOrderDetails) {

	switch orderStatuses.DisplayStatus {
	case displaystatus.PLACED:
		handleOrderPlacedComm(orderID, orderStatuses)
		return
	case displaystatus.CONFIRMED:
		handleOrderConfirmedComm(orderID, userID, orderDetails, orderStatuses)
		return

	}
	switch orderStatuses.ShipmentStatus {
	case shipmentstatus.OUT_FOR_DELIVERY:
		handleOutForDeliveryComm(orderID, userID, orderDetails, orderStatuses)
		return
	default:
	}
}

func handleOutForDeliveryComm(orderID, userID string, orderDetails *dao.KiranaBazarOrderDetails, orderStatuses orderstatus.OrderStatusResponse) error {
	// TODO: here check for past events and communication from redis

	cartValue := orderDetails.GetCartValue()
	codValue := orderDetails.GetCodValue()
	shippingAddress := orderDetails.ShippingAddress

	intOrderID, err := strconv.Atoi(orderID)
	if err != nil {
		return err
	}

	timeLowerBound := 8
	timeAdjustConfig := utils.TimeAdjustConfig{
		LowerBoundHour: &timeLowerBound,
	}
	appId := 932341
	addDataToIVRQueue(int64(intOrderID), orderDetails.Seller, codValue, cartValue, 1, userID, *shippingAddress.Phone, appId, time.Now().Add(time.Minute*1), ivr.IVR_REQUEST_TYPES.ORDER_OUT_FOR_DELIVERY, true, &timeAdjustConfig)

	eventObject := map[string]any{
		"distinct_id":     userID,
		"tracking_link":   "",
		"order_id":        orderID,
		"cart_value":      int(orderDetails.GetCartValue()),
		"order_value":     int(orderDetails.GetOrderValue()),
		"seller":          orderDetails.Seller,
		"ordering_module": utils.MakeTitleCase(orderDetails.Seller),
		"updated_by":      "<EMAIL>",
		"$insert_id":      fmt.Sprintf("%s_%d", "order_out_for_delivery", intOrderID),
	}

	webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{userID},
		EventName:   "Order Out For Delivery",
		EventObject: eventObject,
	})

	whatsapp.SendOrderOurForDeliveryWhatsapp(*shippingAddress.Phone, *shippingAddress.Name, orderID, fmt.Sprintf("%0.2f", codValue))
	// add event info in redis
	AddOrderCommunicationEvent(context.Background(), orderID, orderStatuses, ORDER_COMM_EVENT_TYPES.ORDER_OUT_FOR_DELIVERY)
	return nil
}

func handleOrderConfirmedComm(orderID, userID string, orderDetails *dao.KiranaBazarOrderDetails, orderStatuses orderstatus.OrderStatusResponse) {
	// sleep for 1 minute so that order confirmation is completed
	AddOrderCommunicationEvent(context.Background(), orderID, orderStatuses, ORDER_COMM_EVENT_TYPES.ORDER_CONFIRMATION)

	// schedule a trigger after 16 hours which will check if order is in confirmed state
	// and if yes then send the event to user and add in redis

	// TODO: add trigger in queue for order processing event to be sent after 16 hours if order is in confirmed state

}

func handleOrderPlacedComm(orderID string, orderStatuses orderstatus.OrderStatusResponse) {
	AddOrderCommunicationEvent(context.Background(), orderID, orderStatuses, ORDER_COMM_EVENT_TYPES.ORDER_PLACED)
}

// function to send event to mixpanel and webengage
