package service

import (
	"context"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
)

func (s *Service) Categories(ctx context.Context) dto.CategoriesResp {
	data := []dto.SearchCategory{
		{
			ImageURL: "https://picsum.photos/50",
			Label:    "Bags",
			Value:    "Bag",
		},
		{
			ImageURL: "https://picsum.photos/50",
			Label:    "Coffee",
			Value:    "Coffee",
		},
	}
	carousel := []string{
		"https://imgs.search.brave.com/Wuj2l3NpBqs-3A_GUckbbojO8YVlvFcS19qM9Z0zOZw/rs:fit:860:0:0/g:ce/aHR0cHM6Ly90NC5m/dGNkbi5uZXQvanBn/LzAyLzQ5LzUwLzE1/LzM2MF9GXzI0OTUw/MTU0MV9YbVdkZkFm/VWJXQXZHeEJ3QU0w/YmEyYVlUMzZudGxw/SC5qcGc",
		"https://imgs.search.brave.com/yGdQcVrhmIZBfkOvAgiU0J4U-0Jz0hWfT53pBkLuyQg/rs:fit:860:0:0/g:ce/aHR0cHM6Ly90My5m/dGNkbi5uZXQvanBn/LzA1LzA3Lzc5LzY4/LzM2MF9GXzUwNzc5/Njg2M19YT2N0amZO/NlZJaUhhNzliRmo3/R0NnOTJQOVRwRUxJ/ZS5qcGc",
	}
	return dto.CategoriesResp{
		Carousel:   carousel,
		Categories: data,
	}
}

func (s *Service) GetCategories(ctx context.Context, req *dto.GetCategoriesRequest) (*dto.GetCategoriesResponse, error) {
	resp := dto.GetCategoriesResponse{
		Domain:    req.Domain,
		SubDomain: req.SubDomain,
	}
	data := []dao.KiranaBazarCategory{}
	query := fmt.Sprintf(`SELECT * FROM kiranabazar_categories kc WHERE kc.source = '%s' AND kc.is_active = %t ORDER BY kc.rank ASC;`, req.Source, true)
	_, err := s.repository.CustomQuery(&data, query)
	if err != nil {
		return &resp, err
	}
	resp.Categories = append(resp.Categories, dto.SearchCategory{
		ImageURL: "https://picsum.photos/50",
		Label:    "टॉप प्रोडक्ट्स",
		Value:    "टॉप प्रोडक्ट्स",
		BgColor:  "#E6E6E6",
	})
	for _, v := range data {
		categoryID := fmt.Sprint(v.ID)
		resp.Categories = append(resp.Categories, dto.SearchCategory{
			ImageURL: v.ImageURL,
			Label:    v.Category,
			Value:    v.Category,
			ID:       &categoryID,
			BgColor:  "#E6E6E6",
		})
	}
	return &resp, nil
}

func (s *Service) GetCategoryWidgets(ctx context.Context, req *dto.GetCategoryWidgetsRequest) (*dto.GetCategoriesWidgetsResponse, error) {
	if req.Data.ScreenTag == nil {
		return nil, fmt.Errorf("screen_tag is not supported in this request")
	}
	if req.Data.ScreenType == "" {
		req.Data.ScreenType = "products"
	}
	if req.Data.ScreenType == "products" {
		req.Meta.AppVersion = "6.5.2"
		limit := 10
		getProductsRequest := &dto.AppSearchRequest{
			Meta:   req.Meta,
			UserID: req.UserId,
			Data: dto.AppSearchRequestData{
				CategoryID: req.Data.CategoryID,
				Seller:     req.Data.Seller,
				ScreenTag:  req.Data.ScreenTag,
				// Latitude:   req.Data.Latitude,
				// Longitude:  req.Data.Longitude,
				// PostalCode: req.Data.PostalCode,
				Internal:   true,
				Limit:      &limit,
			},
		}

		resp, err := s.GetProductsV2(ctx, getProductsRequest)
		if err != nil {
			return nil, err
		}

		productsData := resp.Data.Items

		return &dto.GetCategoriesWidgetsResponse{
			Meta: req.Meta,
			Data: dto.GetCategoriesWidgetsResponseData{
				SubType: req.Data.ScreenType,
				Data:    productsData,
			},
		}, nil
	} else if req.Data.ScreenType == "widgets" {
		screenName := *req.Data.ScreenTag
		appVersion := req.Meta.AppVersion
		widgets, err := s.GetScreenWidgets(ctx, &dto.WidgetsRequest{
			ScreenName: screenName,
			UserID:     req.UserId,
			AppVersion: appVersion,
		})
		if err != nil {
			return nil, fmt.Errorf("error in fetching screen widgets: %w", err)
		}

		return &dto.GetCategoriesWidgetsResponse{
			Meta: req.Meta,
			Data: dto.GetCategoriesWidgetsResponseData{
				SubType: req.Data.ScreenType,
				Data:    widgets,
			},
		}, nil
	} else if req.Data.ScreenType == "top_products" {
		req.Meta.AppVersion = "6.5.2"
		limit := 10
		seller := "zoff_foods"
		if req.Data.ScreenTag != nil && *req.Data.ScreenTag != "" {
			seller = *req.Data.ScreenTag
		}
		getProductsRequest := &dto.AppSearchRequest{
			Meta:   req.Meta,
			UserID: req.UserId,
			Data: dto.AppSearchRequestData{
				Seller:     seller,
				// Latitude:   req.Data.Latitude,
				// Longitude:  req.Data.Longitude,
				// PostalCode: req.Data.PostalCode,
				Internal:   true,
				Limit:      &limit,
			},
		}

		resp, err := s.GetProductsV2(ctx, getProductsRequest)
		if err != nil {
			return nil, err
		}

		productsData := resp.Data.Items

		return &dto.GetCategoriesWidgetsResponse{
			Meta: req.Meta,
			Data: dto.GetCategoriesWidgetsResponseData{
				SubType: "products",
				Data:    productsData,
			},
		}, nil
	}
	return nil, nil
}
