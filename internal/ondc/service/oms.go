package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"log"

	"github.com/Kirana-Club/oms-go/pkg/models"
	OMSDao "github.com/Kirana-Club/oms-go/storage/dao"
	"github.com/google/uuid"
)

var sellerOMSMapping = map[string]string{
	"michis":                     "shipdelight",
	"soothe":                     "shipdelight",
	"cravitos":                   "shipdelight",
	"milden":                     "shipdelight",
	"hugs":                       "shipdelight",
	"apsara_tea":                 "shipdelight",
	"mothers_kitchen":            "shipdelight",
	"panchvati":                  "shipdelight",
	"rsb_super_stockist":         "shipdelight",
	"bolas":                      "shipdelight",
	"kirana_club":                "shipdelight",
	"zoff_foods":                 "shipdelight",
	"go_desi":                    "shipdelight",
	"chuk_de":                    "shipdelight",
	"kiranaclub_loyalty_rewards": "shipdelight",
	"candylake":                  "shipdelight",
	"sugandh":                    "shipdelight",
	"somnath":                    "shipdelight",
}

func (s *Service) CreateOrderOnOMS(ctx context.Context, request dto.CreateOrderOnOMSRequest) (any, error) {
	if request.OrderID <= 0 {
		return nil, errors.New("order id invalid")
	}
	orderInfo, err := GetOrderInfo(s.repository, int64(request.OrderID))
	if err != nil {
		return nil, err
	}

	orderDetails, err := GetOrderDetails(s.repository, fmt.Sprintf("%d", request.OrderID))
	if err != nil {
		return nil, err
	}

	packageDetails, err := GetPackageDetails(s.repository, int64(request.OrderID))
	if err != nil {
		return nil, err
	}
	packageDetailsObj := shared.PackageDetails{}
	err = json.Unmarshal(packageDetails.Details, &packageDetailsObj)
	if err != nil {
		return nil, err
	}

	dimentions := packageDetailsObj.Dimensions

	oms, exists := sellerOMSMapping[orderInfo.Seller]
	if exists {
		session, err := s.OmsService.CreateSession(context.Background(), orderInfo.Seller, oms)
		if err != nil {
			return nil, err
		}

		vendors, err := session.GetVendors(context.Background())
		if err != nil {
			return nil, err
		}
		if len(vendors) == 0 {
			return nil, errors.New("no vendor found")
		}
		vendor := vendors[0]

		orderPayment, err := GetOrderPayment(s.repository, fmt.Sprintf("%d", request.OrderID))
		if err != nil {
			return nil, err
		}

		// calculate below numbers
		requestObject, err := s.GetOMSRequestObject(ctx, orderInfo, orderDetails, orderPayment, vendor, dimentions)
		if err != nil {
			return nil, err
		}

		lsps := s.LSPAllocationService.GetServiceableLSPs(*orderDetails.ShippingAddress.PostalCode, orderInfo.Seller)
		priorityCourier := "NA"
		if len(lsps) >= 0 {
			priorityCourier = string(lsps[0])
		}

		for idx, lsp := range lsps {
			manualCourier := string(lsp)
			requestObject.ManualCourier = &manualCourier
			orderCreationResponse, err := session.PushOrderToOMS(context.Background(), requestObject)
			if (err != nil && idx != len(lsps)-1) || (orderCreationResponse != nil && orderCreationResponse.AWB == "" && idx != len(lsps)-1) {
				continue
			} else {
				orderCreationResponse.RequestedCourier = priorityCourier
				orderCreationResponse.AssignedCourier = manualCourier
				return orderCreationResponse, err
			}
		}

		// if no priority courier found then send request without manual courier
		orderCreationResponse, err := session.PushOrderToOMS(context.Background(), requestObject)
		if err != nil {
			return orderCreationResponse, err
		}
		return orderCreationResponse, nil
	} else {
		return nil, errors.New("no seller oms mapping found")
	}

}

func (s *Service) GetOMSRequestObject(ctx context.Context, orderInfo dao.KiranaBazarOrder, orderDetails *dao.KiranaBazarOrderDetails, orderPayment dao.KiranaBazarOrderPayment, vendor OMSDao.OMSVendor, dimentions []shared.Dimensions) (*models.CreateOrderRequest, error) {
	referenceCode := fmt.Sprintf("KC_%06d", *orderInfo.ID)
	invoiceID := uuid.NewString()

	mdls := []models.Item{}
	for _, product := range orderDetails.GetOrderCart() {
		meta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(product.Meta, &meta)
		if err != nil {
			log.Fatal("error while unmarshalling the json", dto.ErrInvalidDateRange)
		}
		skuCode := getItemSkuCodeLabel(product.ID, s.repository)
		tax := 0.0
		if meta.Tax != nil {
			tax = *meta.Tax
		}
		mdls = append(mdls, models.Item{
			ID:        product.ID,
			SKUCode:   getItemSkuCodeLabel(product.ID, s.repository),
			VariantID: skuCode,
			Name:      product.Name,
			Quantity:  int(product.Quantity * int32(meta.PackSize)),
			Price: models.ItemPrice{
				Currency:       "Rs",
				Price:          meta.WholesaleRate * float64(meta.PackSize) * float64(product.Quantity),
				WholeSalePrice: meta.WholesaleRate,
				Tax:            tax,
			},
		})
	}

	paidAmount := 0.0
	if orderPayment.PaidAmount != nil && *orderPayment.PaidAmount > 0 {
		paidAmount = *orderPayment.PaidAmount
	}

	orderValue := orderDetails.GetOrderValue()

	var collectableAmount *float64 = &orderValue
	if paidAmount > 0 && orderValue > 0 {
		collAmnt := orderValue - paidAmount
		collectableAmount = &collAmnt
	}
	if *collectableAmount < 1.0 {
		zeroValue := 0.0
		collectableAmount = &zeroValue
	}

	discount := orderDetails.GetDiscountValue()
	invoiceAmount := orderDetails.GetOrderValue()
	payableAmount := *collectableAmount
	gstNumber := orderDetails.ShippingAddress.GST
	// above all numbers need to be calculated

	var gstDetails *models.GSTDetails
	if gstNumber != "" {
		gstDetails = &models.GSTDetails{
			GSTNumber: gstNumber,
		}
	}

	var shippingAddress = models.Address{
		Name:         *orderDetails.ShippingAddress.Name,
		AddressLine1: orderDetails.ShippingAddress.Line1,
		AddressLine2: orderDetails.ShippingAddress.Line2,
		City:         *orderDetails.ShippingAddress.District,
		State:        *orderDetails.ShippingAddress.State,
		Pincode:      *orderDetails.ShippingAddress.PostalCode,
		Country:      "India",
		Phone:        *orderDetails.ShippingAddress.Phone,
	}

	var billingAddress = models.Address{}

	if orderDetails.BillingAddress == nil {
		billingAddress = shippingAddress
	} else {
		billingAddress = models.Address{
			Name:         *orderDetails.BillingAddress.Name,
			AddressLine1: orderDetails.BillingAddress.Line1,
			AddressLine2: orderDetails.BillingAddress.Line2,
			City:         *orderDetails.BillingAddress.District,
			State:        *orderDetails.BillingAddress.State,
			Pincode:      *orderDetails.BillingAddress.PostalCode,
			Country:      "India",
			Phone:        *orderDetails.BillingAddress.Phone,
		}
	}

	paymentMethod := "COD"
	if payableAmount == 0 {
		paymentMethod = "PPD"
	}

	// creating multiple packages
	packages := []models.Package{}
	for _, dimention := range dimentions {
		packages = append(packages, models.Package{
			BoxLength: dimention.Length,
			BoxWidth:  dimention.Breadth,
			BoxHeight: dimention.Height,
			Weight:    dimention.Weight,
		})
	}
	mps := false
	if len(packages) > 1 {
		mps = true
	}

	return &models.CreateOrderRequest{
		OrderID:     uint64(*orderInfo.ID),
		Packages:    packages,
		InvoiceID:   invoiceID,
		OMSOrderID:  referenceCode,
		AutoApprove: true, // auto approve is true -- this generates AWB on the go
		VendorInfo: models.VendorInfo{
			VendorCode: vendor.VendorCode,
			VendorName: vendor.VendorName,
			Address: models.Address{
				Name:         vendor.VendorName,
				AddressLine1: vendor.Address1,
				AddressLine2: vendor.Address2,
				City:         vendor.City,
				State:        vendor.State,
				Pincode:      vendor.Pincode,
				Country:      "India",
				Phone:        vendor.Phone,
				Email:        vendor.Email,
				Latitude:     -1,
				Longitude:    -1,
			},
		},
		ServiceType: "f",
		Discount: models.Discount{
			Total: discount,
		},
		ShippingCharges: 0.0,
		InvoiceAmount:   invoiceAmount,
		PayableAmount:   payableAmount,
		PickUpLocation: models.Address{
			Name:         vendor.VendorName,
			AddressLine1: vendor.Address1,
			AddressLine2: vendor.Address2,
			City:         vendor.City,
			State:        vendor.State,
			Pincode:      vendor.Pincode,
			Country:      "India",
			Phone:        vendor.Phone,
			Email:        vendor.Email,
			Latitude:     -1,
			Longitude:    -1,
		},
		RTOLocation: models.Address{
			Name:         vendor.VendorName,
			AddressLine1: vendor.Address1,
			AddressLine2: vendor.Address2,
			City:         vendor.City,
			State:        vendor.State,
			Pincode:      vendor.Pincode,
			Country:      "India",
			Phone:        vendor.Phone,
			Email:        vendor.Email,
			Latitude:     -1,
			Longitude:    -1,
		},
		GSTDetails:    gstDetails,
		Items:         mdls,
		PaymentMethod: paymentMethod,
		Customer: models.Customer{
			ShippingAddress: shippingAddress,
			BillingAddress:  billingAddress,
		},
		ShippingMethod: "",
		MPS:            mps,
	}, nil
}
