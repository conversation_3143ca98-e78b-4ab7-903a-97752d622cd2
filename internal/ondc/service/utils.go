package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/redis"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/coupons"
	"kc/internal/ondc/utils"
	"math"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"firebase.google.com/go/v4/db"

	"github.com/Masterminds/semver"
	"github.com/google/uuid"
	"golang.org/x/net/html"
)

var ACTIVATION_COHORT_COUPON_BANNER_MAP = map[string]shared.BannerImageUrls{
	"104": {
		MixpanelEventName: "clicked ek_rupye_me_do_darjan_haldi_ordering_reactivation",
		Url:               "https://d2rstorage2.blob.core.windows.net/widget/February/7/7cae2bc0-91ba-4ae2-b015-7c8d35ae5d50/1738888016776.webp",
		Nav: &shared.Nav{
			Name:    "GenericScreen",
			NavType: "Redirect to Screen",
			Params: map[string]interface{}{
				"screenName": "ek_rupye_me_do_darjan_haldi_ordering_reactivation",
			},
		},
	},
}

type BonusSkuCartMapItem struct {
	Quantity     int
	Manufacturer string
}

// Define structs for tracking freebies and bonus SKUs
type FreebieDetails struct {
	HindiName                string
	Quantity                 int
	PackSize                 int
	Value                    float64
	ProductMetaWholesaleRate float64
	ProductMetaQuantity      string
	IsProcessed              bool
	CouponID                 int64
	Manufacturer             string
}

type BonusSkuDetails struct {
	CartProductID       string
	CartProductQuantity int
	ProductID           string
	Quantity            *int
	IsProcessed         bool
	CouponID            int64
	Type                string               // e.g., "step", "tier"
	StepConfig          *coupons.StepConfig  // Optional, used for step-based bonus calculations
	Tiers               []coupons.TierConfig // Optional, used for tier-based bonus calculations
	Manufacturer        string
}

// GetMessageID returns unique id for every ondc request
func getMessageID() string {
	// return "da42ab2a-aa07-4280-b419-56d4396df93c"
	return uuid.NewString()
}

func getIssueID(issueID string) string {
	if issueID != "" {
		return issueID
	}
	return fmt.Sprint("KC-", time.Now().Unix())
}

// GetTransactionID returns unique id for every new transaction initiated in ONDC
func getTransactionID() string {
	// return "30766c72-664b-4a7e-9b84-57150759ce98"
	return uuid.NewString()
}

const (
	TRANSITION_STATUS = "transit"
)

// getDefaultContext returns default context required for ondc request
func getDefaultContext(transactionID string, postalCode string) *dto.Context {
	return &dto.Context{
		Domain: ONDC_DOMAIN,
		Location: &dto.Location{
			City: &dto.City{
				Code: postalCode,
			},
			Country: &dto.Country{
				Code: utils.COUNTRY_CODE,
			},
		},
		CoreVersion:   &ONDC_CORE_VERSION,
		BapID:         &ONDC_BAP_ID,
		BppURI:        BPP_URL,
		BppID:         BPP_ID,
		BapURI:        &ONDC_BAP_URI,
		TransactionID: &transactionID,
		Timestamp:     time.Now(),
		TTL:           PT30S,
	}
}

// getContext returns the context needed for the ondc request
func getContext(ctx *dto.Context, reqType string, messageID string) (*dto.Context, error) {
	return &dto.Context{
		Domain:        ctx.Domain,
		Location:      ctx.Location,
		Action:        reqType,
		CoreVersion:   ctx.CoreVersion,
		BapID:         ctx.BapID,
		BapURI:        ctx.BapURI,
		BppURI:        ctx.BppURI,
		BppID:         ctx.BppID,
		TransactionID: ctx.TransactionID,
		MessageID:     &messageID,
		Timestamp:     time.Now(),
		TTL:           ctx.TTL,
	}, nil
}

// Function  returns the address of the user <-- returning userShipment address, billing address, error if any -->
func getUserAddress(uid string, isDefault bool, addressID string, repo *sqlRepo.Repository) (dao.UserAddress, dao.UserAddress, error) {
	userAddress := []dao.UserAddress{}
	condition := map[string]interface{}{
		"user_id": uid,
	}

	if addressID != "" {
		condition["id"] = addressID
	} else if isDefault {
		condition["is_default"] = true
	}

	repo.Find(condition, &userAddress)
	if len(userAddress) > 0 {
		billingAddressID := userAddress[0].BillingAddress
		if billingAddressID == nil {
			return userAddress[0], userAddress[0], nil
		}
		billingAddress := []dao.UserAddress{}

		// fetching billing address
		repo.Find(map[string]interface{}{
			"id": *billingAddressID,
		}, &billingAddress)

		if len(billingAddress) > 0 {
			return userAddress[0], billingAddress[0], nil
		}
		return userAddress[0], userAddress[0], nil
	}
	return dao.UserAddress{}, dao.UserAddress{}, nil
}

func handleInvalidAddress() (*dto.AppSearchResponses, error) {
	errorCode := "12345"
	errorMessage := "Invalid Address"
	errorDescription := "User address not present"
	errorType := "INTERNAL"
	return &dto.AppSearchResponses{
		Error: dto.AppResponseError{
			Code:        &errorCode,
			Message:     &errorMessage,
			Description: &errorDescription,
			Type:        &errorType,
		},
	}, nil
}

// createONDCRequest create a HTTP request for ONDC network with a Authorization header.
func (s *Service) createONDCRequest(ctx context.Context, action, url string, body []byte) (*http.Request, error) {
	keyset, err := s.KeyClient.ServiceSigningPrivateKeyset(ctx)
	if err != nil {
		return nil, err
	}

	currentTime := time.Now()
	// Use outer bound of request ttl which is 30 seconds.
	expiredTime := currentTime.Add(30 * time.Second)
	authHeader, err := utils.CreateAuthSignature(body, keyset, currentTime.Unix(), expiredTime.Unix(), "ondc.retailpulse.ai", "s.conf.KeyID")
	if err != nil {
		return nil, err
	}
	fmt.Println("auth header is", authHeader)
	requestURL := fmt.Sprintf("%s/%s", url, action)
	fmt.Println("requestURL = ", requestURL)
	request, err := http.NewRequest(http.MethodPost, requestURL, bytes.NewReader(body))
	if err != nil {
		return nil, err
	}
	request.Header.Set("Authorization", authHeader)
	request.Header.Set("Content-Type", "application/json")

	return request, nil
}

// syncingONDCRequest is an adapter to send the request and wait for response from the redis
func (s *Service) syncingONDCRequest(ctx context.Context, requestObject []byte, identifier, requestType string) (interface{}, error) {
	request, err := s.createONDCRequest(ctx, requestType, BPP_URL, requestObject)
	if err != nil {
		logger.Error(ctx, "Creating request failed: %v", err)
		return nil, err
	}

	// send a request to ONDC network
	response, err := s.httpClient.Do(request)
	if err != nil {
		logger.Error(ctx, "Sending request to ONDC network failed: %v", err)
		return nil, err
	}

	body, err := io.ReadAll(response.Body)
	if err != nil {
		logger.Error(ctx, "Error reading response body: %v", err)
		return nil, err
	}

	fmt.Printf("%s Response Body: %s\n", requestType, body)
	defer response.Body.Close()

	resp, err := s.syncifyClient.WaitForAsyncReq(ctx, identifier)
	if err != nil {
		logger.Error(ctx, "error in ONDC search api, error is %s", err.Error())
		return nil, err
	}
	return resp, nil

}

// onlySendingRequest eliminates the syncing the data from the redis
func (s *Service) onlySendingRequest(ctx context.Context, requestObject []byte, identifier, requestType string) (interface{}, error) {
	request, err := s.createONDCRequest(ctx, requestType, BPP_URL, requestObject)
	if err != nil {
		logger.Error(ctx, "Creating request failed: %v", err)
		return nil, err
	}

	response, err := s.httpClient.Do(request)
	if err != nil {
		logger.Error(ctx, "Error reading response body: %v", err)
		return nil, err
	}

	body, err := io.ReadAll(response.Body)
	if err != nil {
		logger.Error(ctx, "Error reading response body: %v", err)
		return nil, err
	}

	fmt.Printf("%s Response Body: %s\n", requestType, body)
	defer response.Body.Close()
	return body, nil
}

// getFloatFromString converts float to string
func getFloatFromString(s string) float64 {
	f, _ := strconv.ParseFloat(s, 64)
	return f
}

// the offer text with required value to add in cart for go_desi
func getFreeOfferText(diffValue float64, offerType string, seller string) (*string, []string) {
	highlightedText := make([]string, 0)
	if seller == utils.ZOFF_FOODS {
		if offerType == "free_haldi1" {
			text := fmt.Sprintf("फ्री 10 पैकेट हल्दी (25g) पाने के लिए %.2f रुपये का उत्पाद और जोड़ें।", diffValue)
			return &text, highlightedText
		}
		if offerType == "free_haldi2" {
			text := fmt.Sprintf("फ्री 20 पैकेट हल्दी (25g) पाने के लिए %.2f रुपये का उत्पाद और जोड़ें।", diffValue)
			return &text, highlightedText
		}
		if offerType == "free_haldi3" {
			text := fmt.Sprintf("फ्री 30 पैकेट हल्दी (25g) पाने के लिए %.2f रुपये का उत्पाद और जोड़ें।", diffValue)
			return &text, highlightedText
		}
		if offerType == "zoff_free_bag" {
			text := fmt.Sprintf("फ्री बैग पाने के लिए %.2f रुपये का उत्पाद और जोड़ें।", diffValue)
			return &text, highlightedText
		}
		if offerType == "zoff_30packet_haldi" {
			text := fmt.Sprintf("फ्री 30 पैकेट हल्दी (25g) पाने के लिए ₹%.2f रुपये का उत्पाद और जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "फ्री 30 पैकेट हल्दी")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f रुपये", diffValue))
			return &text, highlightedText
		}
		if offerType == "zoff_20packet_haldi" {
			text := fmt.Sprintf("फ्री 20 पैकेट हल्दी (25g) पाने के लिए ₹%.2f रुपये का उत्पाद और जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "फ्री 20 पैकेट हल्दी")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f रुपये", diffValue))
			return &text, highlightedText
		}
		if offerType == "zoff_10packet_haldi" {
			text := fmt.Sprintf("फ्री 10 पैकेट हल्दी (25g) पाने के लिए ₹%.2f रुपये का उत्पाद और जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "फ्री 10 पैकेट हल्दी")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f रुपये", diffValue))
			return &text, highlightedText
		}
	} else if seller == utils.GO_DESI {
		if offerType == "free_10_imli_pops" {
			text := fmt.Sprintf("फ्री 10 इमली पॉप्स पाने के लिए ₹%.2f का उत्पाद जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "फ्री 10 इमली पॉप्स")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f", diffValue))
			return &text, highlightedText
		} else if offerType == "free_30_imli_pops" {
			text := fmt.Sprintf("फ्री 30 इमली पॉप्स पाने के लिए ₹%.2f का उत्पाद जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "फ्री 30 इमली पॉप्स")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f", diffValue))
			return &text, highlightedText
		} else if offerType == "free_60_imli_pops" {
			text := fmt.Sprintf("फ्री 60 इमली पॉप्स पाने के लिए ₹%.2f का उत्पाद जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "फ्री 60 इमली पॉप्स")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f", diffValue))
			return &text, highlightedText
		}
		// if offerType == "3_percent_mobile_holder" {
		// 	text := fmt.Sprintf("फ्री मोबाइल होल्डर + 3%% एक्स्ट्रा छूट के लिए ₹%.2f का उत्पाद जोड़ें।", diffValue)
		// 	return &text
		// }
	} else if seller == utils.APSARA_TEA {
		if offerType == "apsara_tea_1_tea_packet" {
			text := fmt.Sprintf("फ्री 10 पैकेट इलाइची चाय (12 ग्राम वाली) पाने के लिए ₹%.2f का उत्पाद और जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "फ्री 10 पैकेट इलाइची चाय")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f", diffValue))
			return &text, highlightedText
		} else if offerType == "apsara_tea_2_tea_packet" {
			text := fmt.Sprintf("फ्री 20 पैकेट इलाइची चाय (12 ग्राम वाली) पाने के लिए ₹%.2f का उत्पाद और जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "फ्री 20 पैकेट इलाइची चाय")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f", diffValue))
			return &text, highlightedText
		} else if offerType == "apsara_tea_4_tea_packet" {
			text := fmt.Sprintf("फ्री 40 पैकेट इलाइची चाय (12 ग्राम वाली) पाने के लिए ₹%.2f का उत्पाद और जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "फ्री 40 पैकेट इलाइची चाय")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f", diffValue))
			return &text, highlightedText
		}
	} else if seller == utils.KIRANA_CLUB {
		text := "किराना क्लब कॉम्बो ऑफर से 399 की छूट"
		highlightedText = append(highlightedText, "किराना क्लब कॉम्बो ऑफर से 399 की छूट")
		return &text, highlightedText
	} else if seller == utils.MILDEN {
		if offerType == "milden_2_free_candy_packets" {
			text := fmt.Sprintf("2 पैकेट ऑरेंज कैंडी फ्री पाने के लिए ₹%.2f का उत्पाद जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "2 पैकेट ऑरेंज कैंडी फ्री")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f", diffValue))
			return &text, highlightedText
		} else if offerType == "milden_3_free_candy_packets" {
			text := fmt.Sprintf("3 पैकेट ऑरेंज कैंडी फ्री पाने के लिए ₹%.2f का उत्पाद जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "3 पैकेट ऑरेंज कैंडी फ्री")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f", diffValue))
			return &text, highlightedText
		} else if offerType == "milden_5_free_candy_packets" {
			text := fmt.Sprintf("5 पैकेट ऑरेंज कैंडी फ्री पाने के लिए ₹%.2f का उत्पाद जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "5 पैकेट ऑरेंज कैंडी फ्री")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f", diffValue))

			return &text, highlightedText
		}

	} else if seller == utils.CHUK_DE {
		if offerType == "chuk_de_free_delivery" {
			text := fmt.Sprintf("फ्री डिलीवरी पाने के लिए ₹%.2f का उत्पाद और जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "फ्री डिलीवरी")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f", diffValue))
			return &text, highlightedText
		}

	} else if seller == utils.RSB_SUPER_STOCKIST {
		if offerType == "rsb_free_bag_offer" {
			text := fmt.Sprintf("फ्री बैग पाने के लिए ₹%.2f का उत्पाद और जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "फ्री बैग")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f", diffValue))
			return &text, highlightedText
		} else if offerType == "rsb_free_mehndi_5" {
			text := fmt.Sprintf("फ्री 5 पैकेट निशा हेयर कलर पाने के लिए ₹%.2f का उत्पाद और जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "फ्री 5 पैकेट हेयर कलर")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f", diffValue))
			return &text, highlightedText
		} else if offerType == "rsb_free_mehndi_10" {
			text := fmt.Sprintf("फ्री 10 पैकेट निशा हेयर कलर पाने के लिए ₹%.2f का उत्पाद और जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "फ्री 10 पैकेट हेयर कलर")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f", diffValue))
			return &text, highlightedText
		} else if offerType == "rsb_free_mehndi_30" {
			text := fmt.Sprintf("फ्री 30 पैकेट निशा हेयर कलर पाने के लिए ₹%.2f का उत्पाद और जोड़ें।", diffValue)
			highlightedText = append(highlightedText, "फ्री 30 पैकेट हेयर कलर")
			highlightedText = append(highlightedText, fmt.Sprintf("₹%.2f", diffValue))
			return &text, highlightedText
		}

	}
	return nil, highlightedText
}

// generate the offer struct applicable according to the cart value and seller
func GetOfferMessage(seller string, totalProductPricing float64, minOrderValueCart float64, shippingAddr *dao.UserAddress) *dto.OfferMessage {
	var minOrderValue *int
	var minOrderFor3Percent *int
	var discount *float64
	var offerText *string
	var discountAmount int
	var styles = &map[string]interface{}{
		"color":           "#4E0EAF",
		"backgroundColor": "#E8D9FF",
	}

	var highlightStyle = &map[string]interface{}{
		"color":      "#4E0EAF",
		"fontWeight": 700,
	}
	iconType := "neutral"

	// Initialize an empty slice for highlighted text
	var highlightedText = make([]dto.HighlightedTextData, 0)

	// Handle minimum order value check for all sellers
	if totalProductPricing < minOrderValueCart {
		iconType = "warning"
		styles = &map[string]interface{}{
			"color":           "#000000",
			"backgroundColor": "#FFD9D9",
		}
		mov := int(minOrderValueCart)
		minOrderValue = &mov
	}

	// Special handling for each seller
	switch seller {
	case "go_desi":
		if totalProductPricing < minOrderValueCart {
			break
		}
		// if totalProductPricing >= minOrderValueCart && totalProductPricing < MIN_ORDER_FOR_10_IMLI_POPS {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_10_IMLI_POPS-totalProductPricing, "free_10_imli_pops", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_10_IMLI_POPS
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#1E90FF",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}
		// } else if totalProductPricing < MIN_ORDER_FOR_30_IMLI_POPS {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_30_IMLI_POPS-totalProductPricing, "free_30_imli_pops", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_30_IMLI_POPS
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#800080",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}
		// } else if totalProductPricing < MIN_ORDER_FOR_60_IMLI_POPS {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_60_IMLI_POPS-totalProductPricing, "free_60_imli_pops", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_60_IMLI_POPS
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#1E90FF",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}
		// }

	case "zoff_foods":
		if totalProductPricing < minOrderValueCart {
			break
		}
		// if totalProductPricing >= minOrderValueCart && totalProductPricing < MIN_ORDER_FOR_10_PACKET_HALDI {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_10_PACKET_HALDI-totalProductPricing, "zoff_10packet_haldi", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_10_PACKET_HALDI
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#800080",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}
		// } else if totalProductPricing < MIN_ORDER_FOR_20_PACKET_HALDI {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_20_PACKET_HALDI-totalProductPricing, "zoff_20packet_haldi", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_20_PACKET_HALDI
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#1E90FF",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}
		// } else if totalProductPricing < MIN_ORDER_FOR_30_PACKET_HALDI {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_30_PACKET_HALDI-totalProductPricing, "zoff_30packet_haldi", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_30_PACKET_HALDI
		// 	styles = &map[string]interface{}{
		// 		"color":           "#000000",
		// 		"backgroundColor": "#FFD700",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#000000",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}
		// }
		if totalProductPricing < MIN_ORDER_FOR_FREE_BAG {
			text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_FREE_BAG-totalProductPricing, "zoff_free_bag", seller)
			offerText = text
			discountAmount = MIN_ORDER_FOR_FREE_BAG
			styles = &map[string]interface{}{
				"color":           "#000000",
				"backgroundColor": "#FFD700",
			}
			highlightStyle = &map[string]interface{}{
				"color":      "#000000",
				"fontWeight": 700,
			}
			for _, text := range highlightedTextString {
				highlightedText = append(highlightedText, dto.HighlightedTextData{
					Text:           text,
					HighlightStyle: highlightStyle,
				})
			}
		} else if totalProductPricing < MIN_ORDER_FOR_7_PERCENT {
			mof3p := MIN_ORDER_FOR_7_PERCENT
			minOrderFor3Percent = &mof3p
			d := 7.0
			discount = &d
			styles = &map[string]interface{}{
				"color":           "#ffffff",
				"backgroundColor": "#800080",
			}
			highlightStyle = &map[string]interface{}{
				"color":      "#ffffff",
				"fontWeight": 700,
			}
		} else if totalProductPricing < MIN_ORDER_FOR_8_PERCENT {
			mof3p := MIN_ORDER_FOR_8_PERCENT
			minOrderFor3Percent = &mof3p
			d := 8.0
			discount = &d
			styles = &map[string]interface{}{
				"color":           "#000000",
				"backgroundColor": "#FFD700",
			}
			highlightStyle = &map[string]interface{}{
				"color":      "#000000",
				"fontWeight": 700,
			}
		} else if totalProductPricing < MIN_ORDER_FOR_10_PERCENT {
			mof3p := MIN_ORDER_FOR_10_PERCENT
			minOrderFor3Percent = &mof3p
			d := 10.0
			discount = &d
			styles = &map[string]interface{}{
				"color":           "#ffffff",
				"backgroundColor": "#1E90FF",
			}
			highlightStyle = &map[string]interface{}{
				"color":      "#ffffff",
				"fontWeight": 700,
			}
		}

	case "panchvati":
		// No additional special cases beyond minimum order value check

	case utils.HUGS:
		// No additional special cases beyond minimum order value check

	case utils.MOTHERS_KITCHEN:
		// No additional special cases beyond minimum order value check

	case utils.APSARA_TEA:
		if totalProductPricing < minOrderValueCart {
			break
		}

		// Commented them so that they don't show up in the offer message for sync_cart v1

		// if totalProductPricing < MIN_ORDER_FOR_1CHAI_PATTI_APSARA {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_1CHAI_PATTI_APSARA-totalProductPricing, "apsara_tea_1_tea_packet", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_1CHAI_PATTI_APSARA
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#800080",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}
		// } else if totalProductPricing < MIN_ORDER_FOR_2CHAI_PATTI_APSARA {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_2CHAI_PATTI_APSARA-totalProductPricing, "apsara_tea_2_tea_packet", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_2CHAI_PATTI_APSARA
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#1E90FF",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}
		// } else if totalProductPricing < MIN_ORDER_FOR_4CHAI_PATTI_APSARA {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_4CHAI_PATTI_APSARA-totalProductPricing, "apsara_tea_4_tea_packet", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_4CHAI_PATTI_APSARA
		// 	styles = &map[string]interface{}{
		// 		"color":           "#000000",
		// 		"backgroundColor": "#FFD700",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#000000",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}
		// }

		// if offerText != nil {
		// 	return &dto.OfferMessage{
		// 		MinOrderValue:   minOrderValue,
		// 		DiscountAmount:  &discountAmount,
		// 		HighlightedText: higlghtedText,
		// 		Discount:        nil,
		// 		Text:            offerText,
		// 		Styles:          styles,
		// 		IconType:        &iconType,
		// 	}
		// }

	case utils.MANGALAM:
		// No additional special cases beyond minimum order value check
		if totalProductPricing < minOrderValueCart {
			break
		}
		// if totalProductPricing < MIN_ORDER_FOR_3_PERCENT_MANGALAM {
		// 	mof3p := MIN_ORDER_FOR_3_PERCENT_MANGALAM
		// 	minOrderFor3Percent = &mof3p
		// 	d := 3.0
		// 	discount = &d
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#800080",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// } else if totalProductPricing < MIN_ORDER_FOR_4_PERCENT_MANGALAM {
		// 	mof4p := MIN_ORDER_FOR_4_PERCENT_MANGALAM
		// 	minOrderFor3Percent = &mof4p
		// 	d := 4.0
		// 	discount = &d
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#1E90FF",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// }

	case utils.NUTRAJ:
		// No additional special cases beyond minimum order value check

	case utils.MICHIS:
		// No additional special cases beyond minimum order value check

	case utils.LOTS:
		// No additional special cases beyond minimum order value check

	case utils.CRAVITOS:
		// No additional special cases beyond minimum order value check

	case utils.SOOTHE:
		if totalProductPricing < minOrderValueCart {
			break
		}
		// if totalProductPricing >= minOrderValueCart && totalProductPricing < MIN_ORDER_FOR_5_PERCENT_SOOTHE {
		// 	mof3p := MIN_ORDER_FOR_5_PERCENT_SOOTHE
		// 	minOrderFor3Percent = &mof3p
		// 	d := 5.0
		// 	discount = &d
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#1E90FF",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// }

	case utils.MILDEN:
		if totalProductPricing < minOrderValueCart {
			break
		}
		// if totalProductPricing < MIN_ORDER_FOR_2_FREE_CANDY_PACKETS_MILDEN {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_2_FREE_CANDY_PACKETS_MILDEN-totalProductPricing, "milden_2_free_candy_packets", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_2_FREE_CANDY_PACKETS_MILDEN
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#800080",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}
		// } else if totalProductPricing < MIN_ORDER_FOR_3_FREE_CANDY_PACKETS_MILDEN {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_3_FREE_CANDY_PACKETS_MILDEN-totalProductPricing, "milden_3_free_candy_packets", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_3_FREE_CANDY_PACKETS_MILDEN
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#1E90FF",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}

		// } else if totalProductPricing < MIN_ORDER_FOR_5_FREE_CANDY_PACKETS_MILDEN {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_5_FREE_CANDY_PACKETS_MILDEN-totalProductPricing, "milden_5_free_candy_packets", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_5_FREE_CANDY_PACKETS_MILDEN
		// 	styles = &map[string]interface{}{
		// 		"color":           "#000000",
		// 		"backgroundColor": "#FFD700",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#000000",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}
		// }

		// if totalProductPricing < MIN_ORDER_FOR_1_PERCENT_MILDEN {
		// 	mof3p := MIN_ORDER_FOR_1_PERCENT_MILDEN
		// 	minOrderFor3Percent = &mof3p
		// 	d := 1.0
		// 	discount = &d
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#800080",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// } else if totalProductPricing < MIN_ORDER_FOR_1_AND_HALF_PERCENT_MILDEN {
		// 	mof4p := MIN_ORDER_FOR_1_AND_HALF_PERCENT_MILDEN
		// 	minOrderFor3Percent = &mof4p
		// 	d := 1.5
		// 	discount = &d
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#1E90FF",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// } else if totalProductPricing < MIN_ORDER_FOR_3_PERCENT_MILDEN {
		// 	mof4p := MIN_ORDER_FOR_3_PERCENT_MILDEN
		// 	minOrderFor3Percent = &mof4p
		// 	d := 3.0
		// 	discount = &d
		// 	styles = &map[string]interface{}{
		// 		"color":           "#000000",
		// 		"backgroundColor": "#FFD700",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#000000",
		// 		"fontWeight": 700,
		// 	}
		// }

	case utils.RSB_SUPER_STOCKIST:
		if totalProductPricing < minOrderValueCart {
			break
		}

		if totalProductPricing < MIN_ORDER_FOR_RSB_FREE_BAG_OFFER {
			text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_RSB_FREE_BAG_OFFER-totalProductPricing, "rsb_free_bag_offer", seller)
			offerText = text
			discountAmount = MIN_ORDER_FOR_FREE_BAG
			styles = &map[string]interface{}{
				"color":           "#ffffff",
				"backgroundColor": "#800080",
			}
			highlightStyle = &map[string]interface{}{
				"color":      "#ffffff",
				"fontWeight": 700,
			}
			for _, text := range highlightedTextString {
				highlightedText = append(highlightedText, dto.HighlightedTextData{
					Text:           text,
					HighlightStyle: highlightStyle,
				})
			}
		}
		// } else if totalProductPricing < MIN_ORDER_FOR_3_PERCENT_EXTRA_MARGIN_RSB {
		// 	mof3p := MIN_ORDER_FOR_3_PERCENT_EXTRA_MARGIN_RSB
		// 	minOrderFor3Percent = &mof3p
		// 	d := 3.0
		// 	discount = &d
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#800080",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// } else if totalProductPricing < MIN_ORDER_FOR_5_PERCENT_EXTRA_MARGIN_RSB {
		// 	mof3p := MIN_ORDER_FOR_5_PERCENT_EXTRA_MARGIN_RSB
		// 	minOrderFor3Percent = &mof3p
		// 	d := 5.0
		// 	discount = &d
		// 	styles = &map[string]interface{}{
		// 		"color":           "#ffffff",
		// 		"backgroundColor": "#1E90FF",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#ffffff",
		// 		"fontWeight": 700,
		// 	}
		// } else if totalProductPricing < MIN_ORDER_FOR_7_PERCENT_EXTRA_MARGIN_RSB {
		// 	mof3p := MIN_ORDER_FOR_7_PERCENT_EXTRA_MARGIN_RSB
		// 	minOrderFor3Percent = &mof3p
		// 	d := 7.0
		// 	discount = &d
		// 	styles = &map[string]interface{}{
		// 		"color":           "#000000",
		// 		"backgroundColor": "#FFD700",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#000000",
		// 		"fontWeight": 700,
		// 	}
		// }

		// if totalProductPricing < MIN_ORDER_FOR_1CHAI_PATTI_APSARA {
		// 			text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_1CHAI_PATTI_APSARA-totalProductPricing, "apsara_tea_1_tea_packet", utils.APSARA_TEA)
		// 			offerText = text
		// 			discountAmount = MIN_ORDER_FOR_1CHAI_PATTI_APSARA
		// 			styles = &map[string]interface{}{
		// 				"color":           "#ffffff",
		// 				"backgroundColor": "#800080",
		// 			}
		// 			highlightStyle = &map[string]interface{}{
		// 				"color":      "#ffffff",
		// 				"fontWeight": 700,
		// 			}
		// 			for _, text := range highlightedTextString {
		// 				highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 					Text:           text,
		// 					HighlightStyle: highlightStyle,
		// 				})
		// 			}
		// 		} else if totalProductPricing < MIN_ORDER_FOR_2CHAI_PATTI_APSARA {
		// 			text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_2CHAI_PATTI_APSARA-totalProductPricing, "apsara_tea_2_tea_packet", utils.APSARA_TEA)
		// 			offerText = text
		// 			discountAmount = MIN_ORDER_FOR_2CHAI_PATTI_APSARA
		// 			styles = &map[string]interface{}{
		// 				"color":           "#ffffff",
		// 				"backgroundColor": "#1E90FF",
		// 			}
		// 			highlightStyle = &map[string]interface{}{
		// 				"color":      "#ffffff",
		// 				"fontWeight": 700,
		// 			}
		// 			for _, text := range highlightedTextString {
		// 				highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 					Text:           text,
		// 					HighlightStyle: highlightStyle,
		// 				})
		// 			}
		// 		} else if totalProductPricing < MIN_ORDER_FOR_4CHAI_PATTI_APSARA {
		// 			text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_4CHAI_PATTI_APSARA-totalProductPricing, "apsara_tea_4_tea_packet", utils.APSARA_TEA)
		// 			offerText = text
		// 			discountAmount = MIN_ORDER_FOR_4CHAI_PATTI_APSARA
		// 			styles = &map[string]interface{}{
		// 				"color":           "#000000",
		// 				"backgroundColor": "#FFD700",
		// 			}
		// 			highlightStyle = &map[string]interface{}{
		// 				"color":      "#000000",
		// 				"fontWeight": 700,
		// 			}
		// 			for _, text := range highlightedTextString {
		// 				highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 					Text:           text,
		// 					HighlightStyle: highlightStyle,
		// 				})
		// 			}
		// 		}

		// if totalProductPricing < MIN_ORDER_FOR_5_FREE_HAIR_COLOUR_RSB {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_5_FREE_HAIR_COLOUR_RSB-totalProductPricing, "rsb_free_mehndi_5", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_5_FREE_HAIR_COLOUR_RSB
		// 	styles = &map[string]interface{}{
		// 		"color":           "#000000",
		// 		"backgroundColor": "#FFD700",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#000000",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}
		// } else if totalProductPricing < MIN_ORDER_FOR_10_FREE_HAIR_COLOR_RSB {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_10_FREE_HAIR_COLOR_RSB-totalProductPricing, "rsb_free_mehndi_10", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_10_FREE_HAIR_COLOR_RSB
		// 	styles = &map[string]interface{}{
		// 		"color":           "#000000",
		// 		"backgroundColor": "#FFD700",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#000000",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}
		// } else if totalProductPricing < MIN_ORDER_FOR_30_FREE_HAIR_COLOR_RSB {
		// 	text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_30_FREE_HAIR_COLOR_RSB-totalProductPricing, "rsb_free_mehndi_30", seller)
		// 	offerText = text
		// 	discountAmount = MIN_ORDER_FOR_30_FREE_HAIR_COLOR_RSB
		// 	styles = &map[string]interface{}{
		// 		"color":           "#000000",
		// 		"backgroundColor": "#FFD700",
		// 	}
		// 	highlightStyle = &map[string]interface{}{
		// 		"color":      "#000000",
		// 		"fontWeight": 700,
		// 	}
		// 	for _, text := range highlightedTextString {
		// 		highlightedText = append(highlightedText, dto.HighlightedTextData{
		// 			Text:           text,
		// 			HighlightStyle: highlightStyle,
		// 		})
		// 	}

		// }

		// No additional special cases beyond minimum order value check

	case utils.KIRANA_CLUB:
		// No additional special cases beyond minimum order value check
		return nil

	case utils.BOLAS:
		// No additional special cases beyond minimum order value check

	case utils.CHUK_DE:
		// No additional special cases beyond minimum order value check
		if totalProductPricing < minOrderValueCart {
			break
		}

		var isUserFromNEState bool = false
		if shippingAddr != nil {
			states := []string{
				"Assam",
				"Manipur",
				"Meghalaya",
				"Mizoram",
				"Nagaland",
				"Tripura",
				"Arunachal Pradesh",
				"Himachal Pradesh",
				"Jammu and Kashmir",
				"Ladakh",
				"Andaman and Nicobar Islands",
				"Lakshadweep",
				"Dadra and Nagar Haveli",
				"Daman and Diu",
			}
			isUserFromNEState = contains(states, *shippingAddr.State)
		}

		if totalProductPricing < MIN_ORDER_FOR_FREE_DELIVERY_CHUK_DE && !isUserFromNEState {
			text, highlightedTextString := getFreeOfferText(MIN_ORDER_FOR_FREE_DELIVERY_CHUK_DE-totalProductPricing, "chuk_de_free_delivery", seller)
			offerText = text
			discountAmount = MIN_ORDER_FOR_2CHAI_PATTI_APSARA
			styles = &map[string]interface{}{
				"color":           "#ffffff",
				"backgroundColor": "#1E90FF",
			}
			highlightStyle = &map[string]interface{}{
				"color":      "#ffffff",
				"fontWeight": 700,
			}
			for _, text := range highlightedTextString {
				highlightedText = append(highlightedText, dto.HighlightedTextData{
					Text:           text,
					HighlightStyle: highlightStyle,
				})
			}
		}

	case utils.KIRANACLUB_LOYALTY_REWARDS:
		// No special handling for other sellers

	case utils.CANDYLAKE:
		// No special handling for other sellers

	case utils.SUGANDH:
		// No special handling for other sellers

	case utils.SOMNATH:
		// No special handling for other sellers

	default:
		// No special handling for other sellers
	}

	// If we have a special offer text, return it immediately
	if offerText != nil {
		return &dto.OfferMessage{
			MinOrderValue:   minOrderValue,
			DiscountAmount:  &discountAmount,
			Discount:        nil,
			Text:            offerText,
			Styles:          styles,
			IconType:        &iconType,
			HighlightedText: highlightedText,
		}
	}

	// Generate appropriate text based on state
	var text *string = nil

	if discount != nil && minOrderFor3Percent != nil {
		offerTextStr := fmt.Sprintf("%.1f%% अतिरिक्त मार्जिन पाने के लिए %d रुपये का उत्पाद और जोड़ें।", *discount, *minOrderFor3Percent-int(totalProductPricing))
		text = &offerTextStr

		highlightedText = append(highlightedText, dto.HighlightedTextData{
			Text:           fmt.Sprintf("%.1f%% अतिरिक्त मार्जिन", *discount),
			HighlightStyle: highlightStyle,
		})
		highlightedText = append(highlightedText, dto.HighlightedTextData{
			Text:           fmt.Sprintf("%d रुपये", *minOrderFor3Percent-int(totalProductPricing)),
			HighlightStyle: highlightStyle,
		})
	} else if minOrderValue != nil && discount == nil && minOrderFor3Percent == nil {
		offerTextStr := fmt.Sprintf("ऑर्डर देने के लिए न्यूनतम कार्ट मूल्य %d से अधिक होना चाहिए", *minOrderValue)
		text = &offerTextStr

		// Set highlight style to black for min order message
		highlightStyle = &map[string]interface{}{
			"color":      "#000000",
			"fontWeight": 700,
		}

		highlightedText = append(highlightedText, dto.HighlightedTextData{
			Text:           fmt.Sprintf("न्यूनतम कार्ट मूल्य %d से अधिक", *minOrderValue),
			HighlightStyle: highlightStyle,
		})
	}

	// Return the final offer message
	return &dto.OfferMessage{
		MinOrderValue:   minOrderValue,
		Discount:        discount,
		DiscountAmount:  minOrderFor3Percent,
		Text:            text,
		HighlightedText: highlightedText,
		Styles:          styles,
		IconType:        &iconType,
	}
}

func GetCurrentOffer(lastAppliedCoupon *coupons.Coupon) *dto.OrderInfoModal {
	if lastAppliedCoupon == nil {
		return nil
	}
	metaData, err := lastAppliedCoupon.GetCouponMeta()
	if err != nil {
		return nil
	}

	if metaData == nil {
		return nil
	}

	if metaData.PopupData == nil {
		return nil
	}
	popupData := metaData.PopupData
	heading := popupData.Heading
	subHeading := "हर ऑर्डर के साथ ज्यादा बचत करें और शानदार ऑफर्स का फायदा उठाएं।"
	imageUrl := "https://d2rstorage2.blob.core.windows.net/widget/December/4/19f49ecd-5932-45dc-989a-69b293a5c231/1733320226450.webp"
	if popupData.SubHeading != "" {
		subHeading = popupData.SubHeading
	}
	if popupData.ImageUrl != "" {
		imageUrl = popupData.ImageUrl
	}
	discountThreshold := lastAppliedCoupon.MinOrderAmount.Float64
	discountPercentage := 0.0
	if lastAppliedCoupon.DiscountType == coupons.DiscountTypePercentage {
		discountPercentage = lastAppliedCoupon.DiscountValue
	} else if popupData.DiscountPercentage != nil {
		discountPercentage = *popupData.DiscountPercentage
	}

	var discountValue *float64 = nil
	if discountPercentage != 0 {
		discountValue = &discountPercentage
	}

	if heading != "" && subHeading != "" {
		return &dto.OrderInfoModal{
			MixpanelEventName: "cart_order_offer_modal",
			ImageUrl:          imageUrl,
			Heading:           heading,
			SubHeading:        subHeading,
			DiscountThreshold: discountThreshold,
			Discount:          discountValue,
		}
	}
	return nil
}

func parseTime(timeStr string) time.Time {
	parsedTime, err := time.Parse(time.RFC3339Nano, timeStr) // Try parsing with RFC3339Nano first
	if err != nil {
		parsedTime, _ = time.Parse("2006-01-02 15:04:05", timeStr) // Fallback to custom format
	}
	return parsedTime
}

func GetShippingOrOrderHistory(easyEcomOrderDetailsData dto.EasyEcomGetOrderDetailsData) []dto.ShippingHistory {
	var result []dto.ShippingHistory

	if len(easyEcomOrderDetailsData.ShippingHistory) > 0 {
		sort.SliceStable(easyEcomOrderDetailsData.ShippingHistory, func(i, j int) bool {
			return parseTime(easyEcomOrderDetailsData.ShippingHistory[i].Time).Before(
				parseTime(easyEcomOrderDetailsData.ShippingHistory[j].Time))
		})

		transitionStatus := strings.ToLower(TRANSITION_STATUS)
		shouldInclude := false
		for _, shippingHistory := range easyEcomOrderDetailsData.ShippingHistory {
			lowerStatus := strings.ToLower(shippingHistory.Status)

			if strings.Contains(lowerStatus, transitionStatus) {
				shouldInclude = true
			}

			statusColor := "#E0A807"
			if shouldInclude {
				result = append(result, dto.ShippingHistory{
					Status:   shippingHistory.Status,
					Time:     shippingHistory.Time,
					Location: shippingHistory.Location,
					Color:    &statusColor,
				})
			}
		}

		if len(result) > 0 {
			return result
		}
	}

	sort.SliceStable(easyEcomOrderDetailsData.EasyecomOrderHistory, func(i, j int) bool {
		return parseTime(easyEcomOrderDetailsData.EasyecomOrderHistory[i].DateTime).Before(
			parseTime(easyEcomOrderDetailsData.EasyecomOrderHistory[j].DateTime))
	})

	statusColor := "#E0A807"
	for _, orderHistory := range easyEcomOrderDetailsData.EasyecomOrderHistory {
		result = append(result, dto.ShippingHistory{
			Status:   orderHistory.Status,
			Time:     orderHistory.DateTime,
			Location: "",
			Color:    &statusColor,
		})
	}

	return result
}

func formatDateTime(input string) (string, error) {
	layout := "2006-01-02 15:04:05"
	t, err := time.Parse(layout, input)
	if err != nil {
		return "", err
	}
	return t.Format("02 January 2006"), nil
}

func compareStatus(s string, statusName string) bool {
	upperCaseStr := strings.ToUpper(s)
	return upperCaseStr == statusName
}

func convertToHindiDate(timestamp time.Time) string {
	hindiDate := fmt.Sprintf("%d %s %d", timestamp.Day(), hindiMonthsMap[int(timestamp.Month())], timestamp.Year())
	return hindiDate
}

type AMSJobRequest struct {
	VideoURL   string `json:"video_url"`
	JobID      string `json:"job_id"`
	ReviewId   string `json:"review_id"`
	User       string `json:"user"`
	WebhookURL string `json:"webhook_url"`
}

func processVideoMedia(reviewMedia []dao.ReviewMedia, userEmail string, reviewID string, webhook_url string) error {
	for _, media := range reviewMedia {
		if media.Type != "video/mp4" && media.Type != "video" {
			continue
		}
		jobId := uuid.New().String()

		jobRequest := AMSJobRequest{
			VideoURL:   media.URI,
			User:       userEmail,
			JobID:      jobId,
			ReviewId:   reviewID,
			WebhookURL: webhook_url,
		}
		jsonBody, err := json.Marshal(jobRequest)
		if err != nil {
			return fmt.Errorf("error marshaling request: %v", err)
		}

		req, err := http.NewRequest("POST", "https://kc.retailpulse.ai/api/ams/jobs/create",
			bytes.NewBuffer(jsonBody))
		if err != nil {
			return fmt.Errorf("error creating request: %v", err)
		}

		req.Header.Set("Content-Type", "application/json")

		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			return fmt.Errorf("error making request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return fmt.Errorf("API request failed with status %d", resp.StatusCode)
		}
	}
	return nil
}

func (s *Service) GetScreenDetails(ctx context.Context, screenName, uid string) (*dto.ScreenConfig, error) {
	// check if screen details are already cached and ttl is not expired
	isInternalUser := includes(utils.INTERNAL_USER_IDS, uid)
	if val, ok := utils.SCREEN_DETAILS_CACHE[screenName]; ok && !isInternalUser {
		if val.(map[string]interface{})["ttl"].(time.Time).After(time.Now()) {
			screenDetailsData := val.(map[string]interface{})["data"]
			screenDetails := screenDetailsData.(dto.ScreenConfig)
			return &screenDetails, nil
		}
	}

	var result dto.ScreenConfig
	ref := s.FirebaseRepository.MetaDb.NewRef(fmt.Sprintf("/globalWidgets/screen/%s", screenName))
	if err := ref.Get(ctx, &result); err != nil {
		fmt.Printf("Failed to fetch data, error: %v", err)
		return nil, fmt.Errorf("error fetching data: %v", err)
	}
	if result.Screen == "" {
		fmt.Println("No data found in Firebase for the requested screen.")
		return nil, fmt.Errorf("no data found in Firebase for the requested screen")
	}
	utils.SCREEN_DETAILS_CACHE[screenName] = map[string]interface{}{
		"data": result,
		"ttl":  time.Now().Add(time.Second * time.Duration(utils.SCREEN_DETAILS_TTL)),
	}
	return &result, nil
}

func extractTextFromHTML(input string) (string, error) {
	// Parse the HTML input
	doc, err := html.Parse(bytes.NewReader([]byte(input)))
	if err != nil {
		return "", fmt.Errorf("error parsing HTML: %w", err)
	}

	// Extract the text
	var textContent string
	var traverse func(*html.Node)
	traverse = func(n *html.Node) {
		if n.Type == html.TextNode {
			textContent += n.Data
		}
		for child := n.FirstChild; child != nil; child = child.NextSibling {
			traverse(child)
		}
	}
	traverse(doc)

	return textContent, nil
}

func decodeAndUnmarshalResponse(res []byte) (*dao.BannerMetricsCache, error) {
	var responseMap map[string]interface{}
	var cart dao.BannerMetricsCache

	if err := json.Unmarshal(res, &responseMap); err != nil {
		return nil, fmt.Errorf("error unmarshalling response map: %w", err)
	}

	encodedResult, ok := responseMap["result"]
	if !ok {
		return nil, fmt.Errorf("missing 'result' field in response")
	}

	if encodedResult == nil || encodedResult == "" {
		return &cart, nil
	}

	resultStr, ok := encodedResult.(string)
	if !ok {
		return nil, fmt.Errorf("unexpected 'result' format: %v", encodedResult)
	}

	if err := json.Unmarshal([]byte(resultStr), &cart); err != nil {
		return nil, fmt.Errorf("error unmarshalling 'result' field: %w", err)
	}

	return &cart, nil
}

func fetchCartFromCache(apiurl, key string) (*dao.BannerMetricsCache, error) {
	requestObject := map[string]interface{}{
		"data": map[string]interface{}{
			"key":       key,
			"operation": "get",
		},
	}

	res, statusCode, err := utils.CallExternalAPI(apiurl, "POST", requestObject, nil)
	if err != nil || *statusCode != http.StatusOK {
		fmt.Println("Error fetching user cart from cache:", err)
		return nil, err
	}

	cart, err := decodeAndUnmarshalResponse(res)
	if err != nil {
		fmt.Println("Error decoding and unmarshalling response:", err)
		return nil, err
	}

	return cart, nil
}

func removeSeller(sellers []string, seller string) []string {
	for i, s := range sellers {
		if s == seller {
			return append(sellers[:i], sellers[i+1:]...)
		}
	}
	return sellers
}

func addSeller(sellers []string, seller string) []string {
	if !contains(sellers, seller) {
		sellers = append(sellers, seller)
	}
	return sellers
}

func saveCartToCache(apiurl, key string, cart *dao.BannerMetricsCache) error {
	cartJSON, err := json.Marshal(cart)
	if err != nil {
		fmt.Println("Error marshaling cart: ", err)
		return err
	}

	requestObject := map[string]interface{}{
		"data": map[string]interface{}{
			"key":       key,
			"value":     string(cartJSON),
			"operation": "set",
			"expiry":    60 * 60 * 24 * 15,
		},
	}

	_, statusCode, err := utils.CallExternalAPI(apiurl, "POST", requestObject, nil)
	if err != nil || *statusCode != http.StatusOK {
		fmt.Println("Error updating user cart in cache:", err)
		return err
	}
	return nil
}

func UpdateUserCartInCache(products []shared.SellerItems, userId, seller string) {
	key := fmt.Sprintf("cart:%s", userId)
	apiurl := "https://asia-south1-op-d2r.cloudfunctions.net/cacheInterface"

	cart, err := fetchCartFromCache(apiurl, key)
	if err != nil {
		fmt.Println("Error fetching user cart from cache:", err)
	}

	cartValue := 0.0
	for _, item := range products {
		if err != nil {
			fmt.Println("err ", err)
		}
		meta := shared.KiranaBazarProductMeta{}
		err = json.Unmarshal(item.Meta, &meta)
		if err != nil {
			fmt.Println("Error unmarshalling product meta:", err)
		}
		cartValue += meta.WholesaleRate * float64(item.Quantity) * float64(meta.PackSize)
	}

	if cartValue < 100 {
		cart.Sellers = removeSeller(cart.Sellers, seller)
	} else {
		cart.Sellers = addSeller(cart.Sellers, seller)
	}

	if err := saveCartToCache(apiurl, key, cart); err != nil {
		fmt.Println("Error updating user cart in cache:", err)
	}
}

func checkIsUserBlocked(rtdb *db.Client, userId, seller string) (bool, error) {
	if userId == "" || seller == "" {
		return false, fmt.Errorf("userId and seller cannot be empty")
	}

	if rtdb == nil {
		return false, fmt.Errorf("firebase client is not initialized")
	}

	rtdbRef := rtdb.NewRef(fmt.Sprintf("/blockedUsers/%s/kbOrder/%s", userId, seller))

	userBlockedDetail := &dao.UserBlockDetail{}
	if err := rtdbRef.Get(context.Background(), userBlockedDetail); err != nil {
		return false, fmt.Errorf("failed to retrieve user block details: %w", err)
	}

	switch userBlockedDetail.BlockType {
	case "TEMPORARY":
		blockedTill := userBlockedDetail.BlockedTill
		if time.Now().After(time.Unix(blockedTill, 0)) {
			return false, nil
		}
		return true, nil

	case "PERMANENT":
		return true, nil

	default:
		return false, nil
	}
}

func convertDateFormat(dateStr string) string {
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return ""
	}
	return fmt.Sprintf("%s (%s)", date.Format("02-01-2006"), date.Weekday())
}

func TopProductsIndexOf(arr []int64, value shared.SellerItems) int {
	for i, v := range arr {
		if v == *value.SizeVariantCode {
			return i
		}
	}
	return -1 // Return -1 if value is not found
}

func getActivationCohortUserData(ctx context.Context, userId string, seller string, gcpRedis *redis.GcpRedis, ch chan<- *dto.ActivationCohortUserData) {
	// if seller != utils.ZOFF_FOODS {
	// 	ch <- nil
	// 	return
	// }

	key := fmt.Sprintf("activation_cohort::%s::%s", userId, seller)
	value, err := gcpRedis.Get(ctx, key)
	if err != nil || value == "" {
		ch <- nil
		return
	}
	eligible := false
	parts := strings.Split(value, "::")
	if parts[0] == utils.COUPON_ADDED {
		eligible = true
	}

	ch <- &dto.ActivationCohortUserData{
		Eligible: eligible,
		CouponId: parts[1],
		Seller:   seller,
		Value:    value,
	}
}

func setActivationCohortCouponRedeemed(ctx context.Context, userId string, seller string, couponId string, gcpRedis *redis.GcpRedis) {
	key := fmt.Sprintf("activation_cohort::%s::%s", userId, seller)
	value := fmt.Sprintf("%s::%s", utils.COUPON_REDEEMED, couponId)
	gcpRedis.Set(ctx, key, value, time.Hour*24*10) // 10 days
}

func GetScreenHeaderConfigByVersion(configs []dto.Header, userVersion string) (dto.Header, error) {
	header := dto.Header{}
	userSemVer, err := semver.NewVersion(userVersion)
	if err != nil {
		return header, fmt.Errorf("invalid user version: %v", err)
	}

	for _, config := range configs {
		versionStr := config.AppVersion
		constraint, err := semver.NewConstraint(versionStr)
		if err != nil {
			continue
		}

		if constraint.Check(userSemVer) {
			return config, nil
		}

	}

	deafaultConfig := utils.BrandsMetaHeaderDataDefaultConfig
	return deafaultConfig, nil
}

func paginateProductIDs(productIds []string, limit, offset int) []string {
	if offset >= len(productIds) || limit <= 0 {
		return []string{} // Return an empty slice if offset is out of bounds or limit is non-positive
	}

	if limit > len(productIds) {
		limit = len(productIds) // Adjust limit if it exceeds array length
	}

	end := offset + limit
	if end > len(productIds) {
		end = len(productIds) // Adjust end to avoid out-of-bounds errors
	}

	return productIds[offset:end]
}

func (s *Service) udpateUserKiranaClubOrder(ctx context.Context, key, value string, expiration time.Duration) error {
	return s.GcpRedis.Set(ctx, key, value, expiration)
}

// PopulateOrderingBottomTabs handles all tab logic in a single function
func populateOrderingBottomTabs(userActiveCartCount int, userId string, screenName string, userCohortNames []string) []dto.OrderingBottomTab {
	tempBottomTabs := make([]dto.OrderingBottomTab, 0)

	for _, tab := range utils.ORDERING_BOTTOM_TABS {
		// Create a new tab map for this entry
		newTab := make(dto.OrderingBottomTab)

		// Copy and potentially transform each tab item
		for tabName, tabData := range tab {
			switch tabName {
			case "Cart":
				cartTab := tabData // Make a copy of the tab data

				if userActiveCartCount > 0 {
					cartTab.Nudge = &dto.Nudge{
						Id:        "cart_nudge",
						Name:      "cart_nudge",
						Shimmer:   true,
						Type:      "text",
						Text:      fmt.Sprintf("%d", userActiveCartCount),
						TextColor: "#FFFFFF",
						Color:     "#F40D9F",
						CreatedAt: 1729775957354,
						ExpiryAt:  1761916757354,
					}
				} else {
					cartTab.Nudge = nil
				}

				// Store the transformed tab
				newTab[tabName] = cartTab

			case "Loyalty":
				loyaltyTab := tabData

				// if userId == "Y98QKSttXzTjtjoSyl7jPO63KnT2" || !LOYALTY_DATA_7_MAY[userId] {
				// 	loyaltyTab.Label = "नई स्कीम"
				// 	loyaltyTab.NavObj = &shared.Nav{
				// 		Name:    "GenericScreen",
				// 		NavType: "Redirect to Screen",
				// 		Params: map[string]interface{}{
				// 			"screenName": "May_Target_Scheme",
				// 		},
				// 	}
				// 	loyaltyTab.Nudge = &dto.Nudge{
				// 		Name:      "cart_nudge",
				// 		Shimmer:   true,
				// 		Type:      "text",
				// 		Text:      "नया",
				// 		TextColor: "#FFFFFF",
				// 		Color:     "#F40D9F",
				// 		CreatedAt: 1729775957354,
				// 		ExpiryAt:  1761916757354,
				// 	}
				// }

				if LOYALTY_DATA_5_JULY[userId] {
					newTab[tabName] = loyaltyTab
				}

			case "Ordering_Offers_Tab":
				if screenName == "Seller_RSB" || userId == "Y98QKSttXzTjtjoSyl7jPO63KnT2" {
					// Skip this tab - don't add anything to newTab for this key
					// We'll just omit this key from the map
				} else {
					// newTab[tabName] = tabData
					// Skipping this tab as per Product team's request
					continue
				}

			case "Categories":
				categoriesTab := tabData

				if includes(userCohortNames, utils.RSB_STOCKIST_USER_COHORT) {
					categoriesTab.NavObj = &shared.Nav{
						Name:    "GenericScreen",
						NavType: "Redirect to Screen",
						Params: map[string]interface{}{
							"screenName": "Categories Screen - RSB",
						},
					}
				}
				newTab[tabName] = categoriesTab

			default:
				newTab[tabName] = tabData
			}
		}

		// Only add the tab if it has content
		if len(newTab) > 0 {
			tempBottomTabs = append(tempBottomTabs, newTab)
		}
	}

	return tempBottomTabs
}

func formatDateInHindi(t time.Time) string {
	// Day as number
	day := t.Day()

	// Map of months in Hindi
	hindiMonths := map[time.Month]string{
		time.January:   "जनवरी",
		time.February:  "फरवरी",
		time.March:     "मार्च",
		time.April:     "अप्रैल",
		time.May:       "मई",
		time.June:      "जून",
		time.July:      "जुलाई",
		time.August:    "अगस्त",
		time.September: "सितंबर",
		time.October:   "अक्टूबर",
		time.November:  "नवंबर",
		time.December:  "दिसंबर",
	}

	// Get month in Hindi
	monthInHindi := hindiMonths[t.Month()]

	// Format as "day monthInHindi"
	return fmt.Sprintf("%d %s", day, monthInHindi)
}

func calculateAdditionalQuantity(bonusDetails BonusSkuDetails) int32 {
	additionalQuantity := int32(0)

	if bonusDetails.Quantity != nil {
		additionalQuantity = int32(*bonusDetails.Quantity)
		return additionalQuantity
	}

	switch bonusDetails.Type {
	case "step":
		if bonusDetails.StepConfig != nil {
			stepQuantity := bonusDetails.StepConfig.StepQuantity
			bonusPerStep := bonusDetails.StepConfig.BonusPerStep
			maxBonusQuantity := bonusDetails.StepConfig.MaxBonusQuantity

			if stepQuantity > 0 && bonusPerStep > 0 {
				additionalQuantity = int32(bonusDetails.CartProductQuantity/stepQuantity) * int32(bonusPerStep)
				if maxBonusQuantity != nil {
					additionalQuantity = int32(math.Min(float64(additionalQuantity), float64(*maxBonusQuantity)))
				}
			}
		}

	case "tier":
		for _, tier := range bonusDetails.Tiers {
			minQuantity := tier.MinQuantity
			maxQuantity := tier.MaxQuantity

			if maxQuantity != nil {
				if bonusDetails.CartProductQuantity >= minQuantity && bonusDetails.CartProductQuantity < *maxQuantity {
					additionalQuantity = int32(tier.BonusQuantity)
					break
				}
			} else {
				if bonusDetails.CartProductQuantity >= minQuantity {
					additionalQuantity = int32(tier.BonusQuantity)
					break
				}
			}
		}
	}

	return additionalQuantity
}

func toTitleCase(str string) string {
	words := strings.Split(strings.Replace(str, "_", " ", -1), " ")
	for i, word := range words {
		if len(word) > 0 {
			words[i] = strings.ToUpper(string(word[0])) + strings.ToLower(word[1:])
		}
	}
	return strings.Join(words, " ")
}

func (s *Service) GetCouponCodesFromCouponIds(couponIds []int64) []string {
	discountCodes := []string{}
	if len(couponIds) > 0 {
		for _, discountId := range couponIds {
			discountCode, err := s.Coupons.GetCouponByID(context.Background(), discountId)
			if err == nil {
				discountCodes = append(discountCodes, discountCode.Code)
			}
		}
	}
	return discountCodes
}
