package lspAllocation

import (
	"context"
	"kc/internal/ondc/external/delhivery"
	"kc/internal/ondc/utils"
	"log"
	"slices"
	"time"
)

var (
	Ekart     = LSPs.EKART_LARGE_SURFACE
	Delhivery = LSPs.DELHIVERY_SURFACE
)

// LSPAllocation handles pincode to LSP mapping and allocation logic
type LSPAllocation struct {
	// Using map for O(1) lookup performance
	ekartPriority     map[string]bool // Both serviceable, Ekart priority
	delhiveryPriority map[string]bool // Both serviceable, Delhivery priority
	ekartOnly         map[string]bool // Only Ekart serviceable
	delhiveryOnly     map[string]bool // Only Delhivery serviceable (placeholder for future)
}

// OrderRequest represents an order allocation request
type OrderRequest struct {
	OrderID   string                 `json:"order_id"`
	Pincode   string                 `json:"pincode"`
	Seller    string                 `json:"seller"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// AllocationResult represents the result of LSP allocation
type AllocationResult struct {
	Success      bool      `json:"success"`
	Pincode      string    `json:"pincode"`
	LSPs         []LSP     `json:"lsps"`          // Ordered by priority
	AllocatedLSP *LSP      `json:"allocated_lsp"` // Final allocated LSP after API calls
	Error        string    `json:"error,omitempty"`
	Timestamp    time.Time `json:"timestamp"`
}

// LSPClient interface for making API calls to LSPs
type LSPClient interface {
	AllocateOrder(ctx context.Context, lsp LSP, request OrderRequest) (bool, error)
}

// NewLSPAllocation creates a new LSPAllocation with all data loaded
func NewLSPAllocationService() *LSPAllocation {
	service := &LSPAllocation{
		ekartPriority:     make(map[string]bool),
		delhiveryPriority: make(map[string]bool),
		ekartOnly:         make(map[string]bool),
		delhiveryOnly:     make(map[string]bool),
	}
	service.loadPincodeData()
	return service
}

// GetServiceableLSPs returns LSPs in priority order for a given pincode
func (ps *LSPAllocation) GetServiceableLSPs(pincode string, seller string) []LSP {
	/*
		Handling seller level -- if the seller is kc_rewards then handle accordinngly:
		We have to make an exception in rule for vendor IndTrabiz (rewards) such that its
		For Pincodes in 3860 Pincodes [Ekart Non Large , Delhivery] where in 790 out of 3860 [Ekart Non Large , None] if not it in 3860 Pincodes then [Ekart Non Large, Delhivery]
	*/

	assignedLSP := []LSP{}
	// handing pincode level logic
	if slices.Contains([]string{"221001", "121004", "226028", "263153", "416404", "282001", "211008", "141007", "152026", "486886", "854203", "221715", "274202", "303108", "458441", "211002", "201301", "495446", "851216", "221706", "212601", "274305", "421302", "241403", "492003", "847423", "842002", "277121", "304021", "212303", "221104", "843313", "212305"}, pincode) {
		assignedLSP = append(assignedLSP, LSP(LSPs.DELHIVERY_SURFACE))
	}

	if seller == utils.KIRANACLUB_LOYALTY_REWARDS {
		if delhivery.DELHIVERY_BLOCKED_PINCODES[pincode] {
			assignedLSP = append(assignedLSP, LSP(LSPs.EKART_NON_LARGE_SURFACE))
			return assignedLSP
		}
		assignedLSP = append(assignedLSP, LSP(LSPs.EKART_NON_LARGE_SURFACE))
		return assignedLSP

	}
	// CandyLake, zoff is only serviceable by Delhivery
	if seller == utils.CANDYLAKE || seller == utils.ZOFF_FOODS {
		assignedLSP = append(assignedLSP, LSP(LSPs.DELHIVERY_SURFACE))
		return assignedLSP
	}

	// if !slices.Contains([]string{utils.MICHIS, utils.BOLAS, utils.APSARA_TEA, utils.CRAVITOS}, seller) {
	// 	return []LSP{LSP(LSPs.EKART_LARGE_SURFACE), LSP(LSPs.DELHIVERY_SURFACE)}
	// }

	// // Check each category and return appropriate LSP array
	// if ps.ekartPriority[pincode] {
	// 	return []LSP{LSP(LSPs.EKART_LARGE_SURFACE), LSP(LSPs.DELHIVERY_SURFACE)} // Ekart has priority
	// }

	// if ps.delhiveryPriority[pincode] {
	// 	return []LSP{LSP(LSPs.DELHIVERY_SURFACE), LSP(LSPs.EKART_LARGE_SURFACE)} // Delhivery has priority
	// }

	// if ps.ekartOnly[pincode] {
	// 	return []LSP{LSP(LSPs.EKART_LARGE_SURFACE)} // Only Ekart
	// }

	// if ps.delhiveryOnly[pincode] {
	// 	return []LSP{LSP(LSPs.DELHIVERY_SURFACE)} // Only Delhivery
	// }

	return []LSP{LSP(LSPs.EKART_LARGE_SURFACE), LSP(LSPs.DELHIVERY_SURFACE)} // No serviceability
}

// AllocateOrder performs the complete allocation logic with API calls
func (ps *LSPAllocation) AllocateOrder(ctx context.Context, client LSPClient, request OrderRequest) AllocationResult {
	result := AllocationResult{
		Pincode:   request.Pincode,
		Timestamp: time.Now(),
	}

	// Get serviceable LSPs in priority order
	lsps := ps.GetServiceableLSPs(request.Pincode, request.Seller)
	result.LSPs = lsps

	if len(lsps) == 0 {
		result.Success = false
		result.Error = "No LSP serviceable for this pincode"
		return result
	}

	// Try allocation with priority-based fallback
	for _, lsp := range lsps {
		accepted, err := client.AllocateOrder(ctx, lsp, request)
		if err != nil {
			log.Printf("Error calling %s API: %v", lsp, err)
			continue // Try next LSP
		}

		if accepted {
			result.Success = true
			result.AllocatedLSP = &lsp
			return result
		}

		log.Printf("%s rejected order %s for pincode %s, trying next LSP", lsp, request.OrderID, request.Pincode)
	}

	// All LSPs rejected
	result.Success = false
	result.Error = "All serviceable LSPs rejected the order"
	return result
}

// loadPincodeData loads all the pincode data into maps
func (ps *LSPAllocation) loadPincodeData() {
	// Ekart Priority Pincodes (Both serviceable, Ekart has priority)

	// Load data into maps for O(1) lookup
	for _, pincode := range ekartPriorityPincodes {
		ps.ekartPriority[pincode] = true
	}

	for _, pincode := range delhiveryPriorityPincodes {
		ps.delhiveryPriority[pincode] = true
	}

	for _, pincode := range ekartOnlyPincodes {
		ps.ekartOnly[pincode] = true
	}

	// delhiveryOnly can be added when you have that data
	// For now, it's empty as you haven't provided delhivery-only pincodes

	log.Printf("Loaded pincode data: Ekart Priority: %d, Delhivery Priority: %d, Ekart Only: %d",
		len(ps.ekartPriority), len(ps.delhiveryPriority), len(ps.ekartOnly))
}

// GetPincodeStats returns statistics about loaded pincode data
func (ps *LSPAllocation) GetPincodeStats() map[string]int {
	return map[string]int{
		"ekart_priority":     len(ps.ekartPriority),
		"delhivery_priority": len(ps.delhiveryPriority),
		"ekart_only":         len(ps.ekartOnly),
		"delhivery_only":     len(ps.delhiveryOnly),
		"total":              len(ps.ekartPriority) + len(ps.delhiveryPriority) + len(ps.ekartOnly) + len(ps.delhiveryOnly),
	}
}

// IsServiceable checks if a pincode is serviceable by any LSP
func (ps *LSPAllocation) IsServiceable(pincode string, seller string) bool {
	return len(ps.GetServiceableLSPs(pincode, seller)) > 0
}

// GetServiceabilityInfo returns detailed serviceability information
func (ps *LSPAllocation) GetServiceabilityInfo(pincode, seller string) map[string]interface{} {
	lsps := ps.GetServiceableLSPs(pincode, seller)

	info := map[string]interface{}{
		"pincode":     pincode,
		"serviceable": len(lsps) > 0,
		"lsps":        lsps,
	}

	if len(lsps) == 0 {
		info["type"] = "not_serviceable"
	} else if len(lsps) == 1 {
		info["type"] = "single_lsp"
		info["lsp"] = lsps[0]
	} else {
		info["type"] = "multiple_lsp"
		info["primary"] = lsps[0]
		info["secondary"] = lsps[1]
	}

	return info
}
