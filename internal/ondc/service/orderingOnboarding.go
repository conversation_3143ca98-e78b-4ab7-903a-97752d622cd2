package service

import (
	"context"
	"encoding/json"
	"kc/internal/ondc/models/dto"
	ordervalue "kc/internal/ondc/service/orderBill/orderValue"
	"kc/internal/ondc/utils"
)


func (s *Service) GetOrderingOnboarding(ctx context.Context, req *dto.GetOrderingOnboardingRequest) (*dto.GetOrderingOnboardingResponse, error) {

	userOrderPlacedCount, err := ordervalue.GetUserLevelOrderCount(req.UserID)
	if err != nil {
		return nil, err
	}

	if userOrderPlacedCount > 0 {
		return nil, nil
	}
	
	orderingOnboardingData := dto.OnboardingData{}
	err = json.Unmarshal([]byte(utils.ORDERING_ONBOARDING_DATA), &orderingOnboardingData)
	if err != nil {
		return nil, err
	}

	return &dto.GetOrderingOnboardingResponse{
		Meta: req.Meta,
		Data: dto.GetOrderingOnboardingResponseData{
			OnboardingData: orderingOnboardingData,
		},
	}, nil
}
