package service

import (
	"context"
	"encoding/csv"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"math"
	"strings"
	"time"

	"encoding/json"

	"github.com/gin-gonic/gin"
)

// GetKPICards retrieves KPI cards data for the dashboard
// GetKPICards retrieves KPI cards data for the dashboard
func (s *Service) GetKPICards(ctx context.Context, request *dto.GetKPICardsRequest) (*dto.GetKPICardsResponse, error) {
	// Calculate date ranges
	dateRange, err := calculateDateRangeFromString(request.Data.DateRange)
	if err != nil {
		return nil, fmt.Errorf("invalid date range: %w", err)
	}

	// Calculate comparison period (same duration, preceding the current period)
	comparisonPeriod := calculateComparisonDateRange(dateRange.StartDate, dateRange.EndDate, request.Data.DateRange)
	// Build base query with join to kiranabazar_products for seller filtering
	baseGTVQuery := `
		SELECT
			SUM(kss.wholesale_rate * kss.units_sold) as gtv,
			SUM(kss.units_sold) as units_sold
		FROM kiranabazar_sku_sales kss
		INNER JOIN kiranabazar_products kp ON kss.product_id = kp.id
		WHERE kss.date BETWEEN ? AND ?
	`

	baseOrdersQuery := `
		SELECT
			sum(kbr.gross_value) as gtv,
			COUNT(kbr.order_id) as orders
		FROM kiranaclubdb.kc_bazar_reconciliation kbr USE INDEX (kc_bazar_reconciliation_order_confirmed_IDX)
		LEFT JOIN kiranabazar_orders ko ON ko.id = kbr.order_id
		WHERE Date(FROM_UNIXTIME(kbr.order_confirmed / 1000)) BETWEEN ? AND ?
		AND ko.display_status != 'ARCHIVED'
	`

	// Add seller filter only if seller array is not empty
	sellerGTVFilter := ""
	sellerOrderFilter := ""
	args := []interface{}{}
	if len(request.Data.Seller) > 0 {
		sellerGTVFilter = " AND kp.seller IN (" + buildPlaceholders(len(request.Data.Seller)) + ")"
		sellerOrderFilter = " AND ko.seller IN (" + buildPlaceholders(len(request.Data.Seller)) + ")"
		for _, seller := range request.Data.Seller {
			args = append(args, seller)
		}
	}

	// Current period query
	currentGtvQuery := baseGTVQuery + sellerGTVFilter
	currentArgs := append([]interface{}{dateRange.StartDate, dateRange.EndDate}, args...)

	// Previous period query
	previousGTVQuery := baseGTVQuery + sellerGTVFilter
	previousArgs := append([]interface{}{comparisonPeriod.StartDate, comparisonPeriod.EndDate}, args...)

	// Current period query
	currentOrdersQuery := baseOrdersQuery + sellerOrderFilter

	// Previous period query
	previousOrdersQuery := baseOrdersQuery + sellerOrderFilter

	// Execute queries
	var currentKPIs, previousKPIs dto.KPIQueryResult
	var currentOrders, previousOrders dto.KPIQueryResult

	// Get current period data
	if err := s.repository.Db.Raw(currentGtvQuery, currentArgs...).Scan(&currentKPIs).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch current KPI data: %w", err)
	}

	// Get previous period data
	if err := s.repository.Db.Raw(previousGTVQuery, previousArgs...).Scan(&previousKPIs).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch previous KPI data: %w", err)
	}

	if err := s.repository.Db.Raw(currentOrdersQuery, currentArgs...).Scan(&currentOrders).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch previous KPI data: %w", err)
	}

	if err := s.repository.Db.Raw(previousOrdersQuery, previousArgs...).Scan(&previousOrders).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch previous KPI data: %w", err)
	}

	// Calculate AOV
	currentAOV := calculateAOV(currentKPIs.GTV, currentOrders.Orders)
	previousAOV := calculateAOV(previousKPIs.GTV, previousOrders.Orders)

	// Build response
	response := &dto.GetKPICardsResponse{
		Filters: dto.KPIFilters{
			DateRange: request.Data.DateRange,
			Seller:    request.Data.Seller,
			StartDate: dateRange.StartDate,
			EndDate:   dateRange.EndDate,
		},
		KPICards: dto.KPICards{
			GTV: dto.KPIMetric{
				Current:       roundToInteger(currentKPIs.GTV),
				Previous:      roundToInteger(previousKPIs.GTV),
				ChangePercent: calculatePercentageChange(currentKPIs.GTV, previousKPIs.GTV),
				ChangeType:    getChangeType(currentKPIs.GTV, previousKPIs.GTV),
			},
			Orders: dto.KPIMetric{
				Current:       currentOrders.Orders,
				Previous:      previousOrders.Orders,
				ChangePercent: calculatePercentageChange(float64(currentOrders.Orders), float64(previousOrders.Orders)),
				ChangeType:    getChangeType(float64(currentOrders.Orders), float64(previousOrders.Orders)),
			},
			UnitsSold: dto.KPIMetric{
				Current:       currentKPIs.UnitsSold,
				Previous:      previousKPIs.UnitsSold,
				ChangePercent: calculatePercentageChange(float64(currentKPIs.UnitsSold), float64(previousKPIs.UnitsSold)),
				ChangeType:    getChangeType(float64(currentKPIs.UnitsSold), float64(previousKPIs.UnitsSold)),
			},
			AOV: dto.KPIMetric{
				Current:       roundToInteger(currentAOV),
				Previous:      roundToInteger(previousAOV),
				ChangePercent: calculatePercentageChange(currentAOV, previousAOV),
				ChangeType:    getChangeType(currentAOV, previousAOV),
			},
		},
		ComparisonPeriod: dto.DateRange{
			StartDate: comparisonPeriod.StartDate,
			EndDate:   comparisonPeriod.EndDate,
		},
		GeneratedAt: time.Now().UTC().Format(time.RFC3339),
	}
	return response, nil
}

// calculateDateRangeFromString converts date range string to actual dates
func calculateDateRangeFromString(dateRange string) (*dto.DateRange, error) {
	now := time.Now()
	var startDate, endDate time.Time

	switch strings.ToLower(dateRange) {
	case "7d":
		endDate = now.AddDate(0, 0, -1)
		startDate = now.AddDate(0, 0, -7)
	case "30d":
		endDate = now.AddDate(0, 0, -1)
		startDate = now.AddDate(0, 0, -30)
	case "mtd":
		endDate = now
		startDate = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	case "today":
		endDate = now
		startDate = now
	default:
		return nil, fmt.Errorf("unsupported date range: %s", dateRange)
	}

	return &dto.DateRange{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	}, nil
}

// calculateComparisonDateRange calculates the comparison period
func calculateComparisonDateRange(startDateStr, endDateStr string, dateRange string) *dto.DateRange {
	startDate, _ := time.Parse("2006-01-02", startDateStr)
	endDate, _ := time.Parse("2006-01-02", endDateStr)

	// Calculate the duration
	duration := endDate.Sub(startDate)

	// Calculate comparison period
	var comparisonStartDate, comparisonEndDate time.Time
	if dateRange == "mtd" {
		comparisonEndDate = endDate.AddDate(0, -1, 0)
		comparisonStartDate = startDate.AddDate(0, -1, 0)
	} else {
		comparisonEndDate = startDate.AddDate(0, 0, -1)
		comparisonStartDate = comparisonEndDate.Add(-duration)

	}

	return &dto.DateRange{
		StartDate: comparisonStartDate.Format("2006-01-02"),
		EndDate:   comparisonEndDate.Format("2006-01-02"),
	}
}

// calculateAOV calculates Average Order Value
func calculateAOV(gtv float64, orders int64) float64 {
	if orders == 0 {
		return 0
	}
	return gtv / float64(orders)
}

// calculatePercentageChange calculates percentage change between current and previous values
func calculatePercentageChange(current, previous float64) int64 {
	if previous == 0 {
		if current == 0 {
			return 0
		}
		return 100 // 100% increase if previous was 0
	}

	change := ((current - previous) / previous) * 100
	return roundToInteger(change)
}

// getChangeType determines if the change is increase, decrease, or no change
func getChangeType(current, previous float64) string {
	if current > previous {
		return "increase"
	} else if current < previous {
		return "decrease"
	}
	return "no_change"
}

// roundToTwoDecimals rounds a float64 to 2 decimal places
func roundToTwoDecimals(value float64) float64 {
	return math.Round(value*100) / 100
}

func roundToInteger(value float64) int64 {
	return int64(math.Round(value))
}

// buildPlaceholders creates SQL placeholders for IN clause
func buildPlaceholders(count int) string {
	if count == 0 {
		return ""
	}

	placeholders := make([]string, count)
	for i := range placeholders {
		placeholders[i] = "?"
	}
	return strings.Join(placeholders, ",")
}

func (s *Service) GetSKULeaderboard(ctx context.Context, request *dto.GetSKULeaderboardRequest) (*dto.GetSKULeaderboardResponse, error) {
	// Validate sort_by parameter
	if !isValidSortBy(request.Data.SortBy) {
		return nil, fmt.Errorf("invalid sort_by parameter: %s. Must be one of: units_sold, gtv, orders", request.Data.SortBy)
	}

	// Calculate date ranges
	dateRange, err := calculateDateRangeFromString(request.Data.DateRange)
	if err != nil {
		return nil, fmt.Errorf("invalid date range: %w", err)
	}

	// Calculate comparison period
	comparisonPeriod := calculateComparisonDateRange(dateRange.StartDate, dateRange.EndDate, request.Data.DateRange)

	// Calculate number of days for GTV per day calculation
	startDate, _ := time.Parse("2006-01-02", dateRange.StartDate)
	endDate, _ := time.Parse("2006-01-02", dateRange.EndDate)
	daysDiff := int(endDate.Sub(startDate).Hours()/24) + 1

	// Build seller filter
	sellerFilter := ""
	sellerArgs := []interface{}{}
	if len(request.Data.Seller) > 0 {
		sellerFilter = " AND kp.seller IN (" + buildPlaceholders(len(request.Data.Seller)) + ")"
		for _, seller := range request.Data.Seller {
			sellerArgs = append(sellerArgs, seller)
		}
	}

	// Build main query for current period
	mainQuery := fmt.Sprintf(`
		SELECT
			kss.product_id,
			JSON_EXTRACT(kp.meta, '$.mrp_number') as mrp,
			JSON_UNQUOTE(JSON_EXTRACT(kp.meta, '$.quantity')) as quantity,
			kp.name as sku_name,
			kp.seller as seller_name,
			kp.is_active,
			kp.is_oos,
			kp.image_urls,
			kp.media_urls,
			kp.code as sku_code,
			SUM(kss.units_sold) as total_units_sold,
			SUM(kss.wholesale_rate * kss.units_sold) as total_gtv,
			SUM(kss.orders) as total_orders,
			SUM(kss.wholesale_rate * kss.units_sold) / %d as gtv_per_day,
			CASE 
				WHEN SUM(kss.orders) > 0 THEN SUM(kss.wholesale_rate * kss.units_sold) / SUM(kss.orders)
				ELSE 0 
			END as aov,
			CASE 
				WHEN SUM(kss.units_sold) > 0 THEN SUM(kss.wholesale_rate * kss.units_sold) / SUM(kss.units_sold)
				ELSE 0 
			END as asp
		FROM kiranabazar_sku_sales kss
		INNER JOIN kiranabazar_products kp ON kss.product_id = kp.id
		WHERE kss.date BETWEEN ? AND ?%s
		GROUP BY kss.product_id
		ORDER BY %s DESC
		LIMIT ? OFFSET ?
	`, daysDiff, sellerFilter, getSortColumn(request.Data.SortBy))

	// Build comparison query for previous period
	comparisonQuery := fmt.Sprintf(`
		SELECT
			kss.product_id,
			JSON_EXTRACT(kp.meta, '$.mrp_number') as mrp,
			JSON_UNQUOTE(JSON_EXTRACT(kp.meta, '$.quantity')) as quantity,
			SUM(kss.units_sold) as prev_units_sold,
			SUM(kss.wholesale_rate * kss.units_sold) as prev_gtv,
			SUM(kss.orders) as prev_orders
		FROM kiranabazar_sku_sales kss
		INNER JOIN kiranabazar_products kp ON kss.product_id = kp.id
		WHERE kss.date BETWEEN ? AND ?%s
		GROUP BY kss.product_id;
	`, sellerFilter)

	// Build count query for pagination
	countQuery := fmt.Sprintf(`
		SELECT COUNT(DISTINCT kss.product_id)
		FROM kiranabazar_sku_sales kss
		INNER JOIN kiranabazar_products kp ON kss.product_id = kp.id
		WHERE kss.date BETWEEN ? AND ?%s
	`, sellerFilter)

	// Prepare arguments for main query
	mainArgs := append([]interface{}{dateRange.StartDate, dateRange.EndDate}, sellerArgs...)
	mainArgs = append(mainArgs, request.Data.Limit, request.Data.Offset)

	// Prepare arguments for comparison query
	comparisonArgs := append([]interface{}{comparisonPeriod.StartDate, comparisonPeriod.EndDate}, sellerArgs...)

	// Prepare arguments for count query
	countArgs := append([]interface{}{dateRange.StartDate, dateRange.EndDate}, sellerArgs...)

	// Execute main query
	var leaderboardItems []dto.SKULeaderboardQueryResult
	if err := s.repository.Db.Raw(mainQuery, mainArgs...).Scan(&leaderboardItems).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch leaderboard data: %w", err)
	}

	// Execute comparison query
	var comparisonItems []dto.SKUComparisonQueryResult
	if err := s.repository.Db.Raw(comparisonQuery, comparisonArgs...).Scan(&comparisonItems).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch comparison data: %w", err)
	}

	// Execute count query
	var totalItems int64
	if err := s.repository.Db.Raw(countQuery, countArgs...).Scan(&totalItems).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch total count: %w", err)
	}

	// Create comparison map for quick lookup
	comparisonMap := make(map[string]dto.SKUComparisonQueryResult)
	for _, item := range comparisonItems {
		key := fmt.Sprintf("%d", item.ProductID)
		comparisonMap[key] = item
	}

	// Build response items
	var responseItems []dto.SKULeaderboardItem
	for i, item := range leaderboardItems {
		key := fmt.Sprintf("%d", item.ProductID)

		// Get comparison data
		var changePercent string
		var changeType string
		var changePercentInt int64
		if compData, exists := comparisonMap[key]; exists {
			changePercentInt, changeType = calculateChangeForSortBy(request.Data.SortBy, item, compData)
			if changeType == "spike" {
				changePercent = fmt.Sprintf("+%d%%", changePercentInt)
			} else {
				changePercent = fmt.Sprintf("%d%%", changePercentInt)
			}

		} else {
			changePercent = "-" // New product, 100% increase
			changeType = "new_product"
		}
		var productStatus string
		if item.IsOos {
			productStatus = "OOS"
		} else {
			productStatus = "INSTOCK"
		}
		if !item.IsActive {
			productStatus = "INACTIVE"
		}

		var mediaUrls []dao.KiranaBazarProductMediaUrl
		if item.MediaUrls != nil {
			if err := json.Unmarshal([]byte(item.MediaUrls), &mediaUrls); err != nil {
				logger.Error(ctx, "error unmarshaling media URLs for SKU leaderboard item: %v", err)
				mediaUrls = []dao.KiranaBazarProductMediaUrl{}
			}
		}

		var imageUrlsArray []string
		for _, media := range mediaUrls {
			if media.Url != "" && media.VideoUrl == nil {
				imageUrlsArray = append(imageUrlsArray, media.Url)
			}
		}
		imageUrlsJson, err := json.Marshal(imageUrlsArray)
		if err != nil {
			logger.Error(ctx, "error marshaling image URLs for SKU leaderboard item: %v", err)
			imageUrlsJson = []byte("[]") // Fallback to empty array
		}

		responseItems = append(responseItems, dto.SKULeaderboardItem{
			Rank:          request.Data.Offset + i + 1,
			ProductID:     item.ProductID,
			SKUCode:       item.SKUCode,
			SKUName:       fmt.Sprintf(`%s %s`, item.SKUName, item.Quantity),
			SellerName:    item.SellerName,
			Quantity:      item.Quantity,
			ImageUrls:     string(imageUrlsJson),
			MRP:           roundToInteger(item.MRP),
			UnitsSold:     item.TotalUnitsSold,
			GTV:           roundToInteger(item.TotalGTV),
			GTVPerDay:     roundToInteger(item.GTVPerDay),
			ASP:           roundToTwoDecimals(item.ASP),
			AOV:           roundToInteger(item.AOV),
			ProductStatus: productStatus,
			Orders:        item.TotalOrders,
			ChangePercent: changePercent,
			ChangeType:    changeType,
		})
	}

	// Build response
	response := &dto.GetSKULeaderboardResponse{
		Filters: dto.SKULeaderboardFilters{
			DateRange: request.Data.DateRange,
			Seller:    getSellerDisplayValue(request.Data.Seller),
			Limit:     request.Data.Limit,
			Offset:    request.Data.Offset,
			SortBy:    request.Data.SortBy,
			StartDate: dateRange.StartDate,
			EndDate:   dateRange.EndDate,
		},
		SKULeaderboard: dto.SKULeaderboardResult{
			Pagination: dto.Pagination{
				CurrentOffset: request.Data.Offset,
				Limit:         request.Data.Limit,
				TotalItems:    totalItems,
				HasNext:       (request.Data.Offset + request.Data.Limit) < int(totalItems),
			},
			Items: responseItems,
		},
		ComparisonPeriod: dto.DateRange{
			StartDate: comparisonPeriod.StartDate,
			EndDate:   comparisonPeriod.EndDate,
		},
		GeneratedAt: time.Now().UTC().Format(time.RFC3339),
	}
	return response, nil
}

func (s *Service) ExportSKULeaderboard(ctx *gin.Context, request *dto.GetSKULeaderboardRequest) (*dto.GetSKULeaderboardResponse, error) {
	resp, err := s.GetSKULeaderboard(ctx, request)

	if err != nil {
		return nil, fmt.Errorf("failed to get SKU leaderboard: %w", err)
	}

	fileName := fmt.Sprintf("sku_leaderboard_%s_to_%s.csv", resp.Filters.StartDate, resp.Filters.EndDate)
	ctx.Header("Content-Disposition", fmt.Sprintf(`attachment; filename="%s"`, fileName))
	ctx.Header("Content-Type", "text/csv")

	writer := csv.NewWriter(ctx.Writer)
	defer writer.Flush()

	header := []string{
		"Rank",
		"SKU Name",
		"MRP",
		"Units Sold",
		fmt.Sprintf("GTV (%s)", resp.Filters.DateRange),
		"GTV Per Day",
		"ASP",
		"Orders",
		"Change %",
		"Seller",
		"Product ID",
		"SKU CODE",
		"Product Status",
	}

	if err := writer.Write(header); err != nil {
		logger.Error(ctx, "error while writing csv header")
		return nil, err
	}

	// Write the records to the CSV
	for _, item := range resp.SKULeaderboard.Items {
		row := []string{
			fmt.Sprintf("%d", item.Rank),
			item.SKUName,
			fmt.Sprintf("%d", item.MRP),
			fmt.Sprintf("%d", item.UnitsSold),
			fmt.Sprintf("%d", item.GTV),
			fmt.Sprintf("%d", item.GTVPerDay),
			fmt.Sprintf("%.2f", item.ASP),
			fmt.Sprintf("%d", item.Orders),
			item.ChangePercent,
			item.SellerName,
			fmt.Sprintf("%d", item.ProductID),
			item.SKUCode,
			item.ProductStatus,
		}

		if err := writer.Write(row); err != nil {
			logger.Error(ctx, "error while writing csv row")
			continue
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		logger.Error(ctx, "error while flushing csv writer")
		return nil, err
	}

	return nil, nil
}

func isValidSortBy(sortBy string) bool {
	validSorts := []string{"units_sold", "gtv", "orders"}
	for _, valid := range validSorts {
		if sortBy == valid {
			return true
		}
	}
	return false
}

// getSortColumn returns the SQL column name for sorting
func getSortColumn(sortBy string) string {
	switch sortBy {
	case "units_sold":
		return "total_units_sold"
	case "gtv":
		return "total_gtv"
	case "orders":
		return "total_orders"
	default:
		return "total_units_sold"
	}
}

// calculateChangeForSortBy calculates change percentage and type based on sort criteria
func calculateChangeForSortBy(sortBy string, current dto.SKULeaderboardQueryResult, previous dto.SKUComparisonQueryResult) (int64, string) {
	var currentValue, previousValue float64

	switch sortBy {
	case "units_sold":
		currentValue = float64(current.TotalUnitsSold)
		previousValue = float64(previous.PrevUnitsSold)
	case "gtv":
		currentValue = current.TotalGTV
		previousValue = previous.PrevGTV
	case "orders":
		currentValue = float64(current.TotalOrders)
		previousValue = float64(previous.PrevOrders)
	default:
		currentValue = float64(current.TotalUnitsSold)
		previousValue = float64(previous.PrevUnitsSold)
	}

	changePercent := calculatePercentageChange(currentValue, previousValue)
	changeType := getChangeTypeForLeaderboard(changePercent)

	return changePercent, changeType
}

// getChangeTypeForLeaderboard determines change type for leaderboard (spike/drop/stable)
func getChangeTypeForLeaderboard(changePercent int64) string {
	if changePercent >= 10 {
		return "spike"
	} else if changePercent <= -10 {
		return "drop"
	}
	return "stable"
}

// getSellerDisplayValue returns appropriate display value for seller filter
func getSellerDisplayValue(sellers []string) interface{} {
	if len(sellers) == 0 {
		return "all"
	}
	return sellers
}

func (s *Service) GetSKUTrends(ctx context.Context, request *dto.GetSKUTrendsRequest) (*dto.GetSKUTrendsResponse, error) {
	// Validate request
	if len(request.Data.SKUs) == 0 {
		return nil, fmt.Errorf("at least one SKU must be provided")
	}

	if len(request.Data.SKUs) > 3 {
		return nil, fmt.Errorf("maximum 3 SKUs allowed for trend analysis")
	}

	// Calculate date range (last 15 days)
	endDate := time.Now()                   // Yesterday
	startDate := endDate.AddDate(0, 0, -15) // 15 days before yesterday

	dateRange := &dto.DateRange{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	}

	// Build SKU filter conditions
	skuConditions := []string{}
	skuArgs := []interface{}{}

	for _, sku := range request.Data.SKUs {
		skuConditions = append(skuConditions, "(kss.product_id = ?)")
		skuArgs = append(skuArgs, sku.ProductID)
	}

	skuFilter := "(" + strings.Join(skuConditions, " OR ") + ")"

	// Build query for combined daily trend data (aggregate all selected SKUs)
	trendQuery := fmt.Sprintf(`
		SELECT
			kss.date,
			SUM(kss.units_sold) as daily_units_sold,
			SUM(kss.wholesale_rate * kss.units_sold) as daily_gtv,
			SUM(kss.orders) as daily_orders,
			CASE 
				WHEN SUM(kss.orders) > 0 THEN SUM(kss.wholesale_rate * kss.units_sold) / SUM(kss.orders)
				ELSE 0 
			END as daily_aov,
			CASE 
				WHEN SUM(kss.units_sold) > 0 THEN SUM(kss.wholesale_rate * kss.units_sold) / SUM(kss.units_sold)
				ELSE 0 
			END as daily_asp
		FROM kiranabazar_sku_sales kss
		WHERE kss.date BETWEEN ? AND ?
		AND %s
		GROUP BY kss.date
		ORDER BY kss.date ASC
	`, skuFilter)

	// Prepare arguments
	trendArgs := append([]interface{}{dateRange.StartDate, dateRange.EndDate}, skuArgs...)

	// Execute trend query
	var trendItems []dto.CombinedSKUTrendQueryResult
	if err := s.repository.Db.Raw(trendQuery, trendArgs...).Scan(&trendItems).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch trend data: %w", err)
	}

	// Generate all dates in range for complete timeline
	allDates := generateDateRange(startDate, endDate)

	// Create daily data map from query results
	dailyDataMap := make(map[string]dto.SKUDailyData)
	for _, item := range trendItems {
		date := strings.Split(item.Date, "T")[0]
		dailyDataMap[date] = dto.SKUDailyData{
			Date:      date,
			UnitsSold: item.DailyUnitsSold,
			GTV:       roundToInteger(item.DailyGTV),
			ASP:       roundToTwoDecimals(item.DailyASP),
			AOV:       roundToInteger(item.DailyAOV),
			Orders:    item.DailyOrders,
		}
	}

	// Fill in missing dates with zeros for complete timeline
	var combinedDailyData []dto.SKUDailyData
	for _, date := range allDates {
		if data, exists := dailyDataMap[date]; exists {
			combinedDailyData = append(combinedDailyData, data)
		} else {
			combinedDailyData = append(combinedDailyData, dto.SKUDailyData{
				Date:      date,
				UnitsSold: 0,
				GTV:       0,
				ASP:       0,
				AOV:       0,
				Orders:    0,
			})
		}
	}

	// Build response
	response := &dto.GetSKUTrendsResponse{
		Filters: dto.SKUTrendsFilters{
			DateRange: dto.DateRange{
				StartDate: dateRange.StartDate,
				EndDate:   dateRange.EndDate,
			},
			SKUCount: len(request.Data.SKUs),
		},
		CombinedTrend: dto.CombinedSKUTrend{
			SelectedSKUs: request.Data.SKUs,
			DailyData:    combinedDailyData,
		},
		GeneratedAt: time.Now().UTC().Format(time.RFC3339),
	}

	return response, nil
}

// generateDateRange generates all dates between start and end (inclusive)
func generateDateRange(startDate, endDate time.Time) []string {
	var dates []string
	for d := startDate; !d.After(endDate); d = d.AddDate(0, 0, 1) {
		dates = append(dates, d.Format("2006-01-02"))
	}
	return dates
}
