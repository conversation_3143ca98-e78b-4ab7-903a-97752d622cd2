package service

import (
	"context"
	"fmt"
	"strconv"
	"time"
)

const FLOATING_WIDGET_SESSION_TTL = 200 * 24 * time.Hour // 200 days

func getUserWidgetKey(userID string) string {
	return fmt.Sprintf("floating_widget:%s", userID)
}

func (s *Service) IncrementWidgetViewCount(ctx context.Context, userID, widgetID string, wttl *time.Duration) error {
	key := getUserWidgetKey(userID)
	redisClient := s.AzureRedis.RedisClient
	if err := redisClient.HIncrBy(ctx, key, widgetID, 1).Err(); err != nil {
		return err
	}

	// Set TTL if the key is new
	_, err := redisClient.TTL(ctx, key).Result()
	// if err == nil && ttl < 0 {
	if err == nil {
		keyTtl := FLOATING_WIDGET_SESSION_TTL
		if wttl != nil {
			keyTtl = *wttl
		}
		redisClient.Expire(ctx, key, keyTtl)
	}
	return nil
}

func (s *Service) GetAllWidgetCounts(ctx context.Context, userID string) (map[string]int, error) {
	key := getUserWidgetKey(userID)

	redisClient := s.AzureRedis.RedisClient
	raw, err := redisClient.HGetAll(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	result := make(map[string]int)
	for widgetID, countStr := range raw {
		if count, err := strconv.Atoi(countStr); err == nil {
			result[widgetID] = count
		}
	}
	return result, nil
}

func (s *Service) GetAllWidgetCountsAsync(ctx context.Context, userID string, resultChan chan<- map[string]int) {
	go func() {
		cacheKey := fmt.Sprintf("widget_counts_cache:%s", userID)
		cacheResult, err := s.GenericCache.GetCachedData(
			ctx,
			cacheKey,
			func() (interface{}, error) {
				return s.GetAllWidgetCounts(ctx, userID)
			},
			10*time.Minute, // Local cache for 10 minutes
			0,              // No caching in redis
		)
		if err == nil && cacheResult.Data != nil {
			resultChan <- cacheResult.Data.(map[string]int)
			return
		}
		// fallback if cache fails
		viewCounts, err := s.GetAllWidgetCounts(ctx, userID)
		if err != nil {
			resultChan <- map[string]int{}
			return
		}
		resultChan <- viewCounts
	}()
}
func getWidgetViewCountFromId(widgetId string, widgetViewCountMap map[string]int) int {
	if count, exists := widgetViewCountMap[widgetId]; exists {
		return count
	}
	return 0
}
