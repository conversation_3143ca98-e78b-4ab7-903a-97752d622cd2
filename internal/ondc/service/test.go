package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dto"
	"net/http"
	"os"
	"time"
)

const (
	MixpanelUsername  = "mp-shubhankar.f83d93.mp-service-account"
	MixpanelPassword  = "q9YA99TZTqaBC6YLWNy9kIIs9NlrQfaQ"
	MixpanelProjectID = "2551336"
)

type MixpanelImportEvent struct {
	Event      string                 `json:"event"`
	Properties map[string]interface{} `json:"properties"`
}
type MixpanelClient struct {
	Username  string
	Password  string
	ProjectID string
	BatchSize int
}

func NewMixpanelClient(username, password, projectID string) *MixpanelClient {
	return &MixpanelClient{
		Username:  username,
		Password:  password,
		ProjectID: projectID,
		BatchSize: 500, // Same batch size as Python code
	}
}

func (m *MixpanelClient) ImportEvents(events []MixpanelImportEvent, filename string) error {
	// Save events to JSON file first
	jsonData, err := json.MarshalIndent(events, "", "  ")
	if err != nil {
		return fmt.Errorf("error marshaling events to file: %w", err)
	}

	err = os.WriteFile(filename, jsonData, 0644)
	if err != nil {
		return fmt.Errorf("error writing events to file: %w", err)
	}
	url := fmt.Sprintf("https://api.mixpanel.com/import?strict=1&project_id=%s", m.ProjectID)
	client := &http.Client{Timeout: 30 * time.Second}

	// Process events in batches
	for i := 0; i < len(events); i += m.BatchSize {
		fmt.Println(i)
		end := i + m.BatchSize
		if end > len(events) {
			end = len(events)
		}
		batch := events[i:end]

		// Convert batch to JSON
		jsonData, err := json.Marshal(batch)
		if err != nil {
			return fmt.Errorf("error marshaling events: %w", err)
		}

		// Create request
		req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
		if err != nil {
			return fmt.Errorf("error creating request: %w", err)
		}

		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Accept", "application/json")
		req.SetBasicAuth(m.Username, m.Password)

		// Send request
		resp, err := client.Do(req)
		if err != nil {
			return fmt.Errorf("error sending request: %w", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
		}
	}

	return nil
}

var shipmetOrderIds = []int64{}

var allValidOrderIds = []int64{}

var allQueries = []string{}

var lbh = ``

func (s *Service) Test(request []byte) (interface{}, error) {
	client := NewMixpanelClient(
		"mp-shubhankar.f83d93.mp-service-account",
		"q9YA99TZTqaBC6YLWNy9kIIs9NlrQfaQ",
		"2551336",
	)
	fmt.Println("client = ", client)

	s.CreateAndSaveExtInvoice(context.Background(), &dto.GetOrderIdRequest{OrderID: "187760"})

	// s.gtvProcessing(175377)
	return map[string]any{
		"message": "completed",
	}, nil
}
