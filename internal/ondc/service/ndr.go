package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/orderStatus/constants"
	"kc/internal/ondc/utils"
	"log"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/mixpanel/mixpanel-go"
)

var updationSource = []string{"SHIPWAY", "IVR", "SHIPWAY_POLLING", "AUTOMATION", "Delhivery"}

func incrementRedisKey(s *Service, ctx context.Context, redisKey string) error {
	// Increment the value of the key by 1
	_, err := s.AzureRedis.RedisClient.Incr(ctx, redisKey).Result()
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("Redis Set Error in assigned to, ", err))
	}

	return nil
}

func (s *Service) assignNDROrder(ctx context.Context, orderValue int, source string) string {
	var userIDs []string
	var admins []string

	// Separate users and admins
	for _, hunter := range utils.B2B_NDR_HUNTERS {
		if hunter.Role == "USER" {
			userIDs = append(userIDs, hunter.Email)
		} else if hunter.Role == "ADMIN" {
			admins = append(admins, hunter.Email)
		}
	}

	// Ensure at least two users exist
	if len(userIDs) < 2 {
		return ""
	}

	currentDate := time.Now().Format("20060102")
	// Assign to an admin if order value is >= 2000
	if orderValue >= 2000 && source == "V1" {
		incrementRedisKey(s, ctx, fmt.Sprintf("NDRAssignee_%s_%s", currentDate, admins[0]))
		return admins[0]
	}

	redisKey := fmt.Sprintf("NDRAssignee_%s", currentDate)
	val, err := s.AzureRedis.RedisClient.Get(ctx, redisKey).Result()
	count := 0
	if err == nil {
		count, _ = strconv.Atoi(val)
	}

	var assignedUser string

	if count < 100 {
		assignedUser = userIDs[0]
	} else {
		if count%2 == 0 {
			assignedUser = userIDs[0]
		} else {
			assignedUser = userIDs[1]
		}
	}
	count++
	incrementRedisKey(s, ctx, redisKey)
	incrementRedisKey(s, ctx, fmt.Sprintf("NDRAssignee_%s_%s", currentDate, assignedUser))
	return assignedUser
}

func (s *Service) GetNDROrder(orderID int) (*dao.NDROrder, error) {
	conditions := map[string]interface{}{
		"order_id": orderID,
	}
	data := &dao.NDROrder{}
	_, err := s.repository.Find(conditions, data)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func GetOrderEssentials(repo *sqlRepo.Repository, orderID int64) (*dao.GetOrderEssentials, error) {
	query := fmt.Sprintf(`SELECT ko.id as order_id, ko.user_id as user_id, ko.seller as seller, ko.order_status, ko.display_status, ko.processing_status, ko.delivery_status,
				kos.courier, kos.awb_number, kop.amount as order_value, kod.customer_phone
				FROM kiranabazar_orders ko
				LEFT JOIN kiranabazar_order_status kos ON ko.id = kos.id
				LEFT JOIN kiranabazar_order_payments kop ON ko.id = kop.order_id
				LEFT JOIN kiranabazar_order_details kod ON ko.id = kod.order_id
				WHERE ko.id = %d`, orderID)
	data := &dao.GetOrderEssentials{}
	_, err := repo.CustomQuery(data, query)
	if err != nil {
		return nil, err
	}
	return data, nil

}

func (s *Service) AddNDROrder(ctx context.Context, request *dto.AddNDROrderRequest) (string, error) {
	orderInt, err := strconv.Atoi(request.OrderID)
	if err != nil {
		fmt.Println("Invalid order ID:", orderInt)
		return "", err
	}
	orderInfo, err := GetOrderEssentials(s.repository, int64(orderInt))
	if err != nil {
		return "", err
	}
	assignedTo := s.assignNDROrder(ctx, request.OrderValue, "V1")
	updatedAt := time.Now().UnixMilli()
	orderActivityID := uuid.New().String()

	ndrOrder := dao.NDROrder{
		OrderID:         int64(orderInt),
		AssignedTo:      assignedTo,
		CreatedAt:       updatedAt,
		UpdatedAt:       updatedAt,
		UpdatedBy:       request.UpdatedBy,
		OrderActivityID: orderActivityID,
		VisibleFrom:     &updatedAt,
		AttemptCount:    request.AttemptCount,
	}

	activityData := dao.Activity{
		UpdatedAt: updatedAt,
		UpdatedBy: request.UpdatedBy,
		Data: dao.ActivityData{
			OrderID:        orderInt,
			OrderStatus:    request.OrderStatus,
			AssignedTo:     assignedTo,
			NDRAgentReason: request.NDRAgentReason,
			NDRTag:         request.NDRTag,
			AttemptCount:   request.AttemptCount,
			Source:         request.Source,
		},
	}
	messageID, err := utils.GenerateShortBase64ID(activityData)
	if err != nil {
		return "", err
	}
	activityData.MessageID = messageID

	_, err = s.repository.Create(&ndrOrder)
	if err != nil {
		return "", err
	}

	orderActivityByte, err := json.Marshal([]dao.Activity{activityData})
	if err != nil {
		return "", err
	}
	orderActivityLogs := dao.OrderActivityLogs{
		ID:        orderActivityID,
		UpdatedAt: updatedAt,
		UpdatedBy: request.UpdatedBy,
		Activity:  orderActivityByte,
	}
	orderActivityLogs.Activity = orderActivityByte
	_, err = s.repository.Create(&orderActivityLogs)
	if err != nil {
		return "", err
	}

	properties := map[string]interface{}{
		"distinct_id":   *orderInfo.UserID,
		"order_id":      request.OrderID,
		"seller":        *orderInfo.Seller,
		"email":         request.UpdatedBy,
		"assigned_to":   assignedTo,
		"order_value":   *orderInfo.OrderValue,
		"attempt_count": *request.AttemptCount,
	}
	addIfNotEmpty := func(key string, value *string) {
		if (value != nil) && (*value != "") {
			properties[key] = value
		}
	}
	addIfNotEmpty("courier", orderInfo.Courier)
	addIfNotEmpty("ndr_agent_reason", request.NDRAgentReason)
	addIfNotEmpty("note", request.Note)

	s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order NDR - Action Allocated", *orderInfo.UserID, properties,
			fmt.Sprintf("%s_%s", "Order NDR - Action Allocated", request.OrderID)),
	})

	return "NDR Data Added Successfully", nil
}

func (s *Service) UpdateNDROrder(ctx context.Context, request *dto.UpdateNDROrderRequest) (*dto.UpdateNDROrderResponse, error) {
	orderInt, err := strconv.Atoi(request.OrderID)
	if err != nil {
		fmt.Println("Invalid order ID:", orderInt)
		return nil, err
	}
	orderInfo, err := GetOrderInfo(s.repository, int64(orderInt))
	if err != nil {
		return nil, err
	}

	var updatedVisibleFrom *int64
	// we can uncomment this line if we have sepcific logic for showing right now its not to show once action taken
	// if request.CallConnected != nil && *request.CallConnected == "NO" {
	// 	updatedVisibleFrom = &request.UpdatedAt
	// }
	// if request.ActionStatus != nil && *request.ActionStatus == "REATTEMPT" {
	updatedAt := time.Now().UnixMilli()
	t := time.Unix(updatedAt/1000+86400, 0).UTC()
	updatedTime := time.Date(t.Year(), t.Month(), t.Day(), 5, 30, 0, 0, time.UTC).UnixMilli()
	updatedVisibleFrom = &updatedTime
	// }
	var escalatedWith3PL *bool
	if request.EscalatedWith3PL != nil {
		value := *request.EscalatedWith3PL == "YES"
		escalatedWith3PL = &value
	}

	var ndrOrder dao.NDROrder
	if strings.Contains(strings.ToLower(request.UpdatedBy), "shipway") || (request.Source != nil && *request.Source == "CSV") {
		ndrOrder = dao.NDROrder{
			OrderID:      int64(orderInt),
			UpdatedAt:    updatedAt,
			UpdatedBy:    request.UpdatedBy,
			ActionStatus: request.ActionStatus,
			Note:         request.Note,
			AttemptCount: request.AttemptCount,
		}

	} else {
		ndrOrder = dao.NDROrder{
			UpdatedAt:        updatedAt,
			UpdatedBy:        request.UpdatedBy,
			CallConnected:    request.CallConnected,
			ActionStatus:     request.ActionStatus,
			NDRUserReason:    request.NDRUserReason,
			NextActionAt:     request.NextActionAt,
			EscalatedWith3PL: escalatedWith3PL,
			Note:             request.Note,
			VisibleFrom:      updatedVisibleFrom,
			AttemptCount:     request.AttemptCount,
		}
	}

	s.repository.Update(&dao.NDROrder{
		OrderID: int64(orderInt),
	}, &ndrOrder)

	if (request.UpdateActivity != nil) && (!(*request.UpdateActivity)) {
		return nil, nil
	}

	activityData := dao.Activity{
		UpdatedAt: updatedAt,
		UpdatedBy: request.UpdatedBy,
		Data: dao.ActivityData{
			OrderID:          orderInt,
			OrderStatus:      request.OrderStatus,
			AssignedTo:       request.AssignedTo,
			CallConnected:    request.CallConnected,
			ActionStatus:     request.ActionStatus,
			NextActionAt:     request.NextActionAt,
			EscalatedWith3PL: request.EscalatedWith3PL,
			Note:             request.Note,
			NDRUserReason:    request.NDRUserReason,
			NDRAgentReason:   request.NDRAgentReason,
			NDRTag:           request.NDRTag,
		},
	}
	messageID, err := utils.GenerateShortBase64ID(activityData)
	if err != nil {
		return nil, err
	}
	activityData.MessageID = messageID
	orderActivityByte, err := json.Marshal(activityData)
	if err != nil {
		return nil, err
	}
	orderActivityLogsQuery := fmt.Sprintf(`UPDATE kiranaclubdb.kiranabazar_order_activity_logs 
              SET updated_at=%d, updated_by='%s', activity = JSON_ARRAY_APPEND(activity, '$', CAST('%s' AS JSON)) 
              WHERE id = '%s'`, updatedAt, request.UpdatedBy, string(orderActivityByte), request.OrderActivityID)
	_, err = s.repository.CustomQuery(nil, orderActivityLogsQuery)
	if err != nil {
		return nil, err
	}

	if !(slices.Contains(updationSource, request.UpdatedBy) || strings.Contains(strings.ToLower(request.UpdatedBy), "bot") || (request.Source != nil && *request.Source == "CSV")) {
		properties := map[string]interface{}{
			"distinct_id": orderInfo.UserID,
			"order_id":    request.OrderID,
			"seller":      orderInfo.Seller,
			"email":       request.UpdatedBy,
			"assigned_to": request.AssignedTo,
		}
		addIfNotEmpty := func(key string, value *string) {
			if (value != nil) && (*value != "") {
				properties[key] = value
			}
		}
		addIfNotEmpty("call_connected", request.CallConnected)
		addIfNotEmpty("action_status", request.ActionStatus)
		addIfNotEmpty("ndr_agent_reason", request.NDRAgentReason)
		addIfNotEmpty("ndr_user_reason", request.NDRUserReason)
		addIfNotEmpty("next_action_at", request.NextActionAt)
		addIfNotEmpty("escalated_with_3pl", request.EscalatedWith3PL)
		addIfNotEmpty("note", request.Note)

		s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
			s.Mixpanel.NewEvent("Order NDR - Action Taken", *orderInfo.UserID, properties,
				fmt.Sprintf("%s_%s", "Order NDR - Action Taken", request.OrderID)),
		})
	}

	return nil, nil
}

func (s *Service) GetNDROrders(ctx context.Context, request *dto.GetNDROrdersRequest) (*dto.GetNDROrdersResponse, error) {
	getOrderQuery := `select 
							kno.order_id,
							ko.created_at as order_date,
						    ko.user_id, 
							u.name as customer_name, 
							u.phone as customer_phone,
							kno.updated_at,
							kno.updated_by,
							kno.assigned_to,
							kop.amount as order_value,
							ko.order_status,
							kno.call_connected,
							kno.action_status,
							kno.next_action_at, 
							kno.escalated_with_3pl as escalated_with_third_party,
							kno.note as note,
							kno.ndr_user_reason, 
							kno.attempt_count,
							ko.seller,
							kod.order_details,
							kno.order_activity_id,
							ko.tracking_link,
							uos.total_placed_order_amount as total_placed_order_amount,
							uos.total_confirmed_order_amount as total_confirmed_order_amount,
							uos.total_delivered_order_amount as total_delivered_order_amount,
							uos.total_returned_order_amount as total_returned_order_amount,
							uos.total_cancelled_order_amount as total_cancelled_order_amount,
							uos.order_placed as total_order_placed,
							uos.confirmed_order as total_order_confirmed,
							uos.order_delivered as total_order_delivered,
							uos.returned_order as total_order_returned,
							uos.cancelled_order as total_order_cancelled
						from kiranabazar_ndr_orders kno 
					  		left join kiranabazar_orders ko on kno.order_id = ko.id
					  		left join kiranabazar_order_payments kop on kno.order_id = kop.order_id
							left join kiranabazar_order_details kod on kno.order_id = kod.order_id
					  		left join users u on ko.user_id = u.user_id
							left join user_order_stats uos ON ko.user_id = uos.user_id
						where 1=1`
	getOrderCountQuery := `select kno.assigned_to, count(*) as count from kiranabazar_ndr_orders kno
							left join kiranabazar_orders ko on kno.order_id = ko.id
					  		left join kiranabazar_order_payments kop on kno.order_id = kop.order_id
							left join kiranabazar_order_details kod on kno.order_id = kod.order_id
					  		left join users u on ko.user_id = u.user_id
						where 1=1`
	if !request.ShowAll {
		getOrderQuery += fmt.Sprintf(` and kno.visible_from <= %d`, time.Now().UnixMilli())
		getOrderCountQuery += fmt.Sprintf(` and kno.visible_from <= %d`, time.Now().UnixMilli())
	}

	if len(request.Seller) != 0 {
		getOrderQuery += fmt.Sprintf(` and ko.seller in ('%s')`, strings.Join(request.Seller, "','"))
		getOrderCountQuery += fmt.Sprintf(` and ko.seller in ('%s')`, strings.Join(request.Seller, "','"))
	}

	if len(request.AssignedTo) > 0 {
		getOrderQuery += fmt.Sprintf(` and kno.assigned_to in ('%s')`, strings.Join(request.AssignedTo, "','"))
	}

	if request.CallConnected != "" {
		getOrderQuery += fmt.Sprintf(` and kno.call_connected = '%s'`, request.CallConnected)
		getOrderCountQuery += fmt.Sprintf(` and kno.call_connected = '%s'`, request.CallConnected)
	}

	if len(request.NextActionDate) != 0 {
		getOrderQuery += fmt.Sprintf(` and Date(kno.next_action_at) in ('%s')`, strings.Join(request.NextActionDate, "','"))
		getOrderCountQuery += fmt.Sprintf(` and Date(kno.next_action_at) in ('%s')`, strings.Join(request.NextActionDate, "','"))
	}

	getOrderQuery += ` and ko.display_status = 'NDR'`
	getOrderCountQuery += ` and ko.display_status = 'NDR' group by kno.assigned_to;`

	getOrderQuery += fmt.Sprintf(` order by kop.amount desc limit %d offset %d;`, request.Limit, request.Offset)

	ndrQueryResponse := []dto.NDROrderDetail{}
	_, err := s.repository.CustomQuery(&ndrQueryResponse, getOrderQuery)
	if err != nil {
		return nil, err
	}

	var ndrCount []dto.NDRStatsCount
	_, err = s.repository.CustomQuery(&ndrCount, getOrderCountQuery)
	if err != nil {
		return nil, err
	}
	stats := make(map[string]int)
	for _, count := range ndrCount {
		stats["ALL"] += count.Count
		stats[count.AssignedTo] = count.Count
	}

	var ndrData []dto.NDROrderDetail
	// valid NSL code for delhivery
	validNSLCode := []string{"EOD-74", "EOD-15", "EOD-104", "EOD-43", "EOD-86", "EOD-11", "EOD-69", "EOD-6"}
	for _, order := range ndrQueryResponse {
		var orderDetailsMap map[string]interface{}
		if err := json.Unmarshal(*order.OrderDetails, &orderDetailsMap); err != nil {
			log.Println(err)
			continue
		}

		ndrTag := constants.NDR
		if order.AttemptCount != nil && *order.AttemptCount > 0 {
			ndrTag = fmt.Sprintf("%s%d", constants.NDR, *order.AttemptCount)
		}
		order.NDRTag = &ndrTag

		var escalatedStatus *string
		if order.EscalatedWith3Party != nil {
			status := "NO"
			if *order.EscalatedWith3Party {
				status = "YES"
			}
			escalatedStatus = &status
		}
		order.EscalatedWith3PL = escalatedStatus
		alternatePhone := ""
		val, ok := orderDetailsMap["alternate_phone"].(string)
		if ok {
			alternatePhone = val
		}

		shippingAddress := orderDetailsMap["shipping_address"].(map[string]interface{})
		order.AddressName = shippingAddress["name"].(string)
		order.AddressCity = shippingAddress["district"].(string)
		order.AddressLine = shippingAddress["line"].(string)
		order.PostalCode = shippingAddress["postal_code"].(string)
		order.State = shippingAddress["state"].(string)
		order.GST = shippingAddress["gst"].(string)
		order.AddressID = shippingAddress["id"].(float64)
		order.StoreName = shippingAddress["store_name"].(string)
		order.HouseNumber = shippingAddress["house_number"].(string)
		order.Neighbourhood = shippingAddress["neighbourhood"].(string)
		order.Village = shippingAddress["village"].(string)
		order.Landmark = shippingAddress["landmark"].(string)
		order.AddressTag = shippingAddress["tag"].(string)
		order.AddressLine1 = shippingAddress["line1"].(string)
		order.AddressLine2 = shippingAddress["line2"].(string)
		order.Phone = shippingAddress["phone"].(string)
		order.AlternatePhone = alternatePhone
		order.OrderDetails = &[]byte{}

		if order.OrderStatus != "" {
			for _, code := range validNSLCode {
				if strings.Contains(order.OrderStatus, code) {
					order.ReAttempt = true
				}
			}
		}

		ndrData = append(ndrData, order)
	}

	return &dto.GetNDROrdersResponse{
		Stats: stats,
		Data:  ndrData,
	}, nil
}

func (s *Service) GetOrderActivityLogs(ctx context.Context, OrderActivityID *string) (*dto.GetOrderActivityLogsResponse, error) {
	conditions := map[string]interface{}{
		"id": OrderActivityID,
	}
	data := &dao.OrderActivityLogs{}
	_, err := s.repository.Find(conditions, data)
	if err != nil {
		return nil, err
	}
	var activityData []dao.OrderAction
	if err := json.Unmarshal(data.Activity, &activityData); err != nil {
		return nil, err
	}
	var noteActivity []map[string]interface{}
	for _, activity := range activityData {
		if activity.Data.Note != nil {
			noteActivity = append(noteActivity,
				map[string]interface{}{
					"message_id": activity.MessageID,
					"updated_at": activity.UpdatedAt,
					"updated_by": activity.UpdatedBy,
					"data": map[string]interface{}{
						"note": activity.Data.Note,
					},
				})
		}
	}
	return &dto.GetOrderActivityLogsResponse{
		OrderActivityID: *OrderActivityID,
		Activity:        activityData,
		Note:            noteActivity,
	}, nil
}
