package service

import (
	"context"
	"errors"
	"fmt"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/service/logistics/couriers"
	orderS "kc/internal/ondc/service/orderStatus"
	orderstatus "kc/internal/ondc/service/orderStatus"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	"kc/internal/ondc/utils"
	"strconv"
	"strings"
	"time"

	"github.com/mixpanel/mixpanel-go"
)

func (s *Service) HandleReturnReceivedOrder(ctx context.Context, request *dto.OrderReturnReceivedRequest) (response *dto.OrderReturnReceivedResponse, err error) {
	if request.Data.OrderID == "" {
		return nil, errors.New("invalid orderID")
	}
	int64OrderId, err := strconv.ParseInt(request.Data.OrderID, 10, 64)
	if err != nil {
		return nil, err
	}
	orderInfo, err := GetOrderInfo(s.repository, int64OrderId)
	if err != nil {
		return nil, err
	}
	if orderInfo.DisplayStatus != displaystatus.RETURNED {
		return nil, errors.New("order is not RTO")
	}
	orderDetails, err := GetOrderDetails(s.repository, request.Data.OrderID)
	if err != nil {
		return
	}
	orderApiStatus, err := GetOrderApiStatus(s.repository, int64OrderId)
	if err != nil {
		return
	}

	orderStatus := strings.ToUpper(request.Data.OrderStatus)
	orderStatuses := orderstatus.MapOrderStatus(orderStatus, "", orderstatus.OrderStatusResponse{})

	if orderStatuses.ShipmentStatus == orderInfo.DeliveryStatus {
		return
	}
	_, _, err = s.repository.Update(dao.KiranaBazarOrder{
		ID: &int64OrderId,
	}, dao.KiranaBazarOrder{
		OrderStatus:      &request.Data.OrderStatus,
		UpdatedAt:        time.Now(),
		DeliveryStatus:   orderStatuses.ShipmentStatus,
		DisplayStatus:    orderStatuses.DisplayStatus,
		ProcessingStatus: orderStatuses.ProcessingStatus,
	})
	if err != nil {
		return
	}
	eventObject := map[string]interface{}{
		"distinct_id":     orderInfo.UserID,
		"order_id":        int64OrderId,
		"cart_value":      int(orderDetails.GetCartValue()),
		"order_value":     int(orderDetails.GetOrderValue()),
		"seller":          orderInfo.Seller,
		"explaination":    request.Data.OrderMeta.ReturnReceivedNote,
		"source":          request.Data.Source,
		"ordering_module": utils.MakeTitleCase(orderInfo.Seller),
	}

	if orderApiStatus.AWBNumber != nil {
		eventObject["awb_number"] = *orderApiStatus.AWBNumber
	}
	if orderApiStatus.Courier != nil {
		eventObject["courier_name"] = couriers.GetActualCourierName(orderApiStatus.Courier)
	}

	if request.Data.Email != "" {
		eventObject["email"] = request.Data.Email
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Return Received", *orderInfo.UserID, eventObject,
			fmt.Sprintf("%s_%d", "order_returned_received", int64OrderId)),
	})

	err = webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{*orderInfo.UserID},
		EventName:   "Order Return Received",
		EventObject: eventObject,
	})
	if err != nil {
		fmt.Println("failed to send webengage event")
	}

	go func(mp *mixpanelRepo.Repository, userID string, orderID int64, orderValue int, s *Service, note, email string, source, seller string, orderStatuses orderS.OrderStatusResponse, orderStatus string) {
		trackingObject := map[string]interface{}{
			"distinct_id":             userID,
			"order_id":                orderID,
			"order_value":             orderValue,
			"status":                  orderStatus,
			"notes":                   note,
			"ordering_module":         utils.MakeTitleCase(seller),
			"seller":                  seller,
			"email":                   email,
			"source":                  source,
			"shipment_status":         orderStatuses.ShipmentStatus,
			"processing_status":       orderStatuses.ProcessingStatus,
			"display_status":          orderStatuses.DisplayStatus,
			"previous_display_status": orderInfo.DisplayStatus,
			"event_trigger":           "order_returned_received",
		}
		mp.Track(context.Background(), []*mixpanel.Event{
			mp.NewEvent("Order Status Updated", userID, trackingObject),
		})
	}(s.Mixpanel, *orderInfo.UserID, int64OrderId, int(orderDetails.GetOrderValue()), s, request.Data.OrderMeta.ReturnReceivedNote, request.Data.Email, request.Data.Source, orderInfo.Seller, orderStatuses, request.Data.OrderStatus)

	response = &dto.OrderReturnReceivedResponse{
		Data: dto.OrderReturnReceivedRequestData{
			OrderID: request.Data.OrderID,
			Message: "Order has been marked returned received",
		},
	}
	return
}
