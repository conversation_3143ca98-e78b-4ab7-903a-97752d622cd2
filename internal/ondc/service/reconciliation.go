package service

import (
	"context"
	"errors"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/brands"
	"math"
	"strconv"
	"strings"
	"time"
)

func getUniqueDatesFromEpochMsRange(startEpochMs, endEpochMs int64) ([]string, error) {
	// Validate input
	if startEpochMs > endEpochMs {
		return nil, errors.New("start epoch must be less than or equal to end epoch")
	}

	// Check for reasonable date range (e.g., prevent excessive memory allocation)
	if endEpochMs-startEpochMs > 365*24*60*60*1000*10 { // 10 years in milliseconds
		return nil, errors.New("date range too large, maximum 10 years supported")
	}

	// Convert milliseconds epochs to time.Time
	startTime := time.UnixMilli(startEpochMs)
	endTime := time.UnixMilli(endEpochMs)

	// Normalize to start of day
	startDate := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, time.Now().Location())
	endDate := time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 0, 0, 0, 0, time.Now().Location())

	// Calculate number of days
	days := int(endDate.Sub(startDate).Hours()/24) + 1

	// Pre-allocate slice
	dates := make([]string, 0, days)

	// Iterate through dates
	currentDate := startDate
	for !currentDate.After(endDate) {
		dates = append(dates, currentDate.Format("2006-01-02"))
		currentDate = currentDate.AddDate(0, 0, 1)
	}

	return dates, nil
}

func getToFromTimestamp(dateSelection dto.DateSelection) (fromDate int64, toDate int64) {

	// default to as custom selection date that FE passes
	fromDate = dateSelection.CustomRange.StartDate
	toDate = dateSelection.CustomRange.EndDate

	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	if dateSelection.IsLast30Days {
		fromDate = today.AddDate(0, 0, -30).UnixMilli()
		toDate = today.AddDate(0, 0, 1).UnixMilli() - 1

	} else if dateSelection.IsLastMonth {
		currentYear, currentMonth, _ := today.Date()
		firstOfMonth := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, time.Now().Location())
		lastMonth := firstOfMonth.AddDate(0, -1, 0)
		fromDate = lastMonth.UnixMilli()
		toDate = firstOfMonth.UnixMilli() - 1

	} else if dateSelection.IsLast7Days {
		fromDate = today.AddDate(0, 0, -7).UnixMilli()
		toDate = today.AddDate(0, 0, 1).UnixMilli() - 1

	} else if dateSelection.IsYesterday {
		fromDate = today.AddDate(0, 0, -1).UnixMilli()
		toDate = today.UnixMilli() - 1

	} else if dateSelection.IsToday {
		fromDate = today.UnixMilli()
		toDate = today.AddDate(0, 0, 1).UnixMilli() - 1
	}

	return fromDate, toDate
}

func getLastMonthEpochs(month, year int) (fromDate int64, toDate int64) {
	if month < 1 || month > 12 {
		fmt.Println("Invalid month. Please enter a value between 1 and 12.")
		return
	}

	// Get the start of the month
	startOfMonth := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Now().Location())

	// Get the end of the month
	endOfMonth := startOfMonth.AddDate(0, 1, 0).Add(-time.Nanosecond) // Last moment of the last day of the month

	// Get start and end dates in epoch
	fromDate = startOfMonth.UnixMilli()
	toDate = endOfMonth.UnixMilli()
	return
}
func getLastXDaysEpoch(fromDate int64, toDate int64, dateSelection dto.DateSelection) (newFromDate int64, newToDate int64) {
	if dateSelection.IsLastMonth {
		newFromDate, newToDate = getLastMonthEpochs(int(time.UnixMilli(fromDate-1).Month()), int(time.UnixMilli(fromDate-1).Year()))
	} else {
		newToDate = fromDate - 1
		newFromDate = fromDate - (toDate - fromDate)
	}
	return
}

type DBMetricsResponse struct {
	Brand *string `json:"brand,omitempty"`
	Value float64 `json:"value"`
	Count int64   `json:"count"`
}

type GraphMetricsResponse struct {
	OrderDate       *string `json:"order_date"`
	TotalGrossValue float64 `json:"total_gross_value"`
	OrderCount      int64   `json:"order_count"`
	OMS             *string `json:"oms"`
}

type NonConfirmedCallStatus struct {
	KBROrderID int64   `json:"kbr_order_id"`
	GrossValue float64 `json:"gross_value"`
	OMS        string  `json:"oms"`
	KCSOrderID *int64  `json:"kcs_order_id,omitempty"`
	CallStatus *string `json:"call_status,omitempty"`
}

type DeliveredOrderGraph struct {
	OrderDate           *string `json:"order_date"`
	TotalGrossValue     float64 `json:"total_gross_value"`
	ConfirmedOrderCount *int64  `json:"confirmed_order_count"`
	PlacedOrderCount    *int64  `json:"placed_order_count"`
	OrdersDelivered     *int64  `json:"orders_delivered"`
	OrdersShipped       *int64  `json:"orders_shipped"`
	OMS                 *string `json:"oms"`
}

type TotalConfirmedAndDeliveredOrder struct {
	OMS                  *string `json:"oms"`
	TotalOrdersConfirmed int64   `json:"total_orders_confirmed"`
	TotalOrdersDelivered int64   `json:"total_orders_delivered"`
	TotalOrdersShipped   int64   `json:"total_orders_shipped"`
}

func formatRupeeAmount(num float64) string {
	// Handle zero or negative amounts
	if num <= 0 {
		return "₹0"
	}

	// Convert to absolute value to handle formatting
	absNum := math.Abs(num)

	// Format for Crore (10 million)
	if absNum >= 10000000 {
		return fmt.Sprintf("₹%.1fCr", absNum/10000000)
	}

	// Format for Lakh (100,000)
	if absNum >= 100000 {
		return fmt.Sprintf("₹%.1fL", absNum/100000)
	}

	// Format for Thousand
	if absNum >= 1000 {
		return fmt.Sprintf("₹%.1fK", absNum/1000)
	}

	// Regular formatting for smaller amounts
	return fmt.Sprintf("₹%.1f", num)
}
func (s *Service) GrossSales(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {

	fromDate, toDate := getToFromTimestamp(request.DateSelection)
	dates, err := getUniqueDatesFromEpochMsRange(fromDate, toDate)
	newFromDate, newToDate := getLastXDaysEpoch(fromDate, toDate, request.DateSelection)

	var seller *string
	if strings.ToUpper(request.Seller) != "ALL" {
		seller = &request.Seller
	}

	mainQuery := fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d `, fromDate, toDate)
	if seller != nil {
		mainQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	dbMetricsResponse := &DBMetricsResponse{}
	// fmt.Println("mainQuery = ", mainQuery)
	_, err = s.repository.CustomQuery(dbMetricsResponse, mainQuery)
	if err != nil {
		return nil, err
	}

	graphDatastructure := []GraphMetricsResponse{}
	graphQueryExtras := ""
	groupByOMS := ""
	if seller != nil {
		graphQueryExtras = fmt.Sprintf(` and oms = '%s'`, *seller)
		groupByOMS = "oms,"
	}

	graphQuery := fmt.Sprintf(`
		SELECT 
			DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata'))  as order_date,
			SUM(gross_value) as total_gross_value,
			%s
			COUNT(*) as order_count
		FROM kc_bazar_reconciliation
		WHERE 1=1 and order_placed BETWEEN %d AND %d %s
		GROUP BY %s  DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata'))
		ORDER BY order_date;`, groupByOMS, fromDate, toDate, graphQueryExtras, groupByOMS)

	// fmt.Println("graphQuery = ", graphQuery)
	_, err = s.repository.CustomQuery(&graphDatastructure, graphQuery)

	dbMetricsResponse2 := &DBMetricsResponse{}
	percentageCalculatorQuery := fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d `, newFromDate, newToDate)
	if seller != nil {
		percentageCalculatorQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}
	// fmt.Println("percentageCalculatorQuery - ", percentageCalculatorQuery)

	_, err = s.repository.CustomQuery(dbMetricsResponse2, percentageCalculatorQuery)

	lastSum := dbMetricsResponse2.Value
	thisSum := 0.0

	graphPoints := []dto.Point{}
	var mp map[string]float64 = make(map[string]float64)
	var mpForOrderCount map[string]int64 = make(map[string]int64)
	for _, j := range graphDatastructure {
		thisSum += j.TotalGrossValue
		_, ok := mp[(*j.OrderDate)[:10]]
		if !ok {
			mp[(*j.OrderDate)[:10]] = 0.0
			mpForOrderCount[(*j.OrderDate)[:10]] = 0

		}
		mp[(*j.OrderDate)[:10]] += j.TotalGrossValue
		mpForOrderCount[(*j.OrderDate)[:10]] += j.OrderCount
	}

	for _, dt := range dates {
		graphPoints = append(graphPoints, dto.Point{
			Y: fmt.Sprintf("%.1f", mp[dt]/1000),
			X: convertDateFormat(dt),
			Z: fmt.Sprintf("%d", mpForOrderCount[dt]),
		})
	}

	var changeColor = "#008000"
	var percentageChange = 0.0
	actualChange := ""
	if lastSum > thisSum {
		changeColor = "#FF0000"
		if lastSum == 0.0 {
			percentageChange = 0.0
		} else {
			percentageChange = 100.0 - (100.0*thisSum)/(lastSum*1.0)
		}
		actualChange = fmt.Sprintf("-%.2f", percentageChange) + "%"
	} else if lastSum == thisSum {
		percentageChange = 0.0
		actualChange = fmt.Sprintf("+%.2f", percentageChange) + "%"
	} else {
		if lastSum == 0 {
			percentageChange = 100.0
		} else {
			percentageChange = (100.0*thisSum)/(lastSum*1.0) - 100.0
		}
		actualChange = fmt.Sprintf("+%.2f", percentageChange) + "%"
	}

	response = &dto.ReconciliationResponse{
		Type:        1,
		Metric:      request.Metric,
		Label:       "Gross Sales",
		Description: "Gross Sales",
		Data: []dto.ReconciliationResponseData{
			{
				Seller: seller,
				Items: []dto.ReconciliationResponseItem{
					{
						Count: fmt.Sprintf("%d orders", dbMetricsResponse.Count),
						Value: fmt.Sprintf("%s", formatRupeeAmount(dbMetricsResponse.Value)),
					},
				},
				Graph: dto.GraphData{
					Points: graphPoints,
					XAxis:  "Date",
					YAxis:  "Gross Sales(₹ in k)",
					ZAxis:  "Orders",
				},
				Change:      actualChange,
				ChangeColor: changeColor,
			},
		},
		DateSelection: request.DateSelection,
	}
	return
}

func (s *Service) OrderPlaced(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {

	fromDate, toDate := getToFromTimestamp(request.DateSelection)
	dates, err := getUniqueDatesFromEpochMsRange(fromDate, toDate)
	newFromDate, newToDate := getLastXDaysEpoch(fromDate, toDate, request.DateSelection)
	newDates, err := getUniqueDatesFromEpochMsRange(newFromDate, newToDate)
	if err != nil {
		return nil, err
	}

	var seller *string
	if strings.ToUpper(request.Seller) != "ALL" {
		seller = &request.Seller
	}
	var query = ""
	if request.DateSelection.IsToday || (!request.DateSelection.IsLast30Days && !request.DateSelection.IsLast7Days && request.DateSelection.IsLastMonth && !request.DateSelection.IsYesterday) {
		query = fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d `, fromDate, toDate)
		if seller != nil {
			query += fmt.Sprintf(` and oms = '%s';`, *seller)
		}
	} else {
		query = fmt.Sprintf(`select sum(order_gross_value) as value, sum(order_placed) as count from kc_bazar_daily_reconciliation where date in (%s) `, "'"+strings.Join(dates, "','")+"'")
		if seller != nil {
			query += fmt.Sprintf(` and brand = '%s';`, *seller)
		}
	}
	dbMetricsResponse := &DBMetricsResponse{}
	_, err = s.repository.CustomQuery(dbMetricsResponse, query)
	if err != nil {
		return nil, err
	}
	kbdr := []dao.KiranaBazarDailyReconciliation{}
	query2 := fmt.Sprintf(`select * from kc_bazar_daily_reconciliation where date in (%s) `, "'"+strings.Join(dates, "','")+"'")
	if seller != nil {
		query2 += fmt.Sprintf(` and brand = '%s';`, *seller)
	}
	_, err = s.repository.CustomQuery(&kbdr, query2)

	dbMetricsResponse2 := &DBMetricsResponse{}
	query3 := fmt.Sprintf(`select sum(order_gross_value) as value, sum(order_placed) as count from kc_bazar_daily_reconciliation where date in (%s) `, "'"+strings.Join(newDates, "','")+"'")
	if seller != nil {
		query3 += fmt.Sprintf(` and brand = '%s';`, *seller)
	}

	_, err = s.repository.CustomQuery(dbMetricsResponse2, query3)

	lastSum := float64(dbMetricsResponse2.Count)
	thisSum := 0.0

	graphPoints := []dto.Point{}
	var mp map[string]int = make(map[string]int)
	for _, j := range kbdr {
		thisSum += float64(j.OrderPlaced)
		_, ok := mp[j.Date[:10]]
		if !ok {
			mp[j.Date[:10]] = 0.0
		}
		mp[j.Date[:10]] += int(j.OrderPlaced)
	}

	for _, dt := range dates {
		graphPoints = append(graphPoints, dto.Point{
			Y: fmt.Sprintf("%d", mp[dt]),
			X: convertDateFormat(dt),
		})
	}

	var changeColor = "#008000"
	var percentageChange = 0.0
	actualChange := ""
	if lastSum > thisSum {
		changeColor = "#FF0000"
		if lastSum == 0.0 {
			percentageChange = 0.0
		} else {
			percentageChange = 100.0 - (100.0*thisSum)/(lastSum*1.0)
		}
		actualChange = fmt.Sprintf("-%.2f", percentageChange) + "%"
	} else if lastSum == thisSum {
		percentageChange = 0.0
		actualChange = fmt.Sprintf("+%.2f", percentageChange) + "%"
	} else {
		if lastSum == 0 {
			percentageChange = 100.0
		} else {
			percentageChange = (100.0*thisSum)/(lastSum*1.0) - 100.0
		}
		actualChange = fmt.Sprintf("+%.2f", percentageChange) + "%"
	}

	response = &dto.ReconciliationResponse{
		Type:        1,
		Metric:      request.Metric,
		Label:       "Orders Placed",
		Description: "Number of Order Placed",
		Data: []dto.ReconciliationResponseData{
			{
				Seller: seller,
				Items: []dto.ReconciliationResponseItem{
					{
						Value: fmt.Sprintf("%d orders", dbMetricsResponse.Count),
					},
				},
				Graph: dto.GraphData{
					Points: graphPoints,
					XAxis:  "Date",
					YAxis:  "Orders",
				},
				Change:      actualChange,
				ChangeColor: changeColor,
			},
		},
		DateSelection: request.DateSelection,
	}
	return
}

func (s *Service) AverageOrderValue(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {

	fromDate, toDate := getToFromTimestamp(request.DateSelection)
	dates, err := getUniqueDatesFromEpochMsRange(fromDate, toDate)
	newFromDate, newToDate := getLastXDaysEpoch(fromDate, toDate, request.DateSelection)
	if err != nil {
		return nil, err
	}

	var seller *string
	if strings.ToUpper(request.Seller) != "ALL" {
		seller = &request.Seller
	}

	mainQuery := fmt.Sprintf(`select SUM(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d `, fromDate, toDate)
	if seller != nil {
		mainQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	dbMetricsResponse := &DBMetricsResponse{}
	_, err = s.repository.CustomQuery(dbMetricsResponse, mainQuery)
	if err != nil {
		return nil, err
	}
	graphDatastructure := []GraphMetricsResponse{}
	graphQueryExtras := ""
	groupByOMS := ""
	if seller != nil {
		graphQueryExtras = fmt.Sprintf(` and oms = '%s'`, *seller)
		groupByOMS = "oms,"
	}

	graphQuery := fmt.Sprintf(`
		SELECT 
			DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata'))  as order_date,
			avg(gross_value) as total_gross_value,
			%s
			COUNT(*) as order_count
		FROM kc_bazar_reconciliation
		WHERE 1=1 and order_placed BETWEEN %d AND %d %s
		GROUP BY %s  DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata'))
		ORDER BY order_date;`, groupByOMS, fromDate, toDate, graphQueryExtras, groupByOMS)

	_, err = s.repository.CustomQuery(&graphDatastructure, graphQuery)

	dbMetricsResponse2 := &DBMetricsResponse{}
	percentageCalculatorQuery := fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d`, newFromDate, newToDate)
	if seller != nil {
		mainQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	_, err = s.repository.CustomQuery(dbMetricsResponse2, percentageCalculatorQuery)

	lastAverage := 0.0
	if dbMetricsResponse2.Count != 0 {
		lastAverage = (dbMetricsResponse2.Value / float64(dbMetricsResponse2.Count))
	}

	graphPoints := []dto.Point{}
	var mpSum map[string]float64 = make(map[string]float64)
	var mpCount map[string]float64 = make(map[string]float64)
	for _, j := range graphDatastructure {
		_, ok := mpSum[(*j.OrderDate)[:10]]
		if !ok {
			mpSum[(*j.OrderDate)[:10]] = 0.0
		}
		_, ok = mpCount[(*j.OrderDate)[:10]]
		if !ok {
			mpCount[(*j.OrderDate)[:10]] = 0.0
		}
		mpSum[(*j.OrderDate)[:10]] += ((j.TotalGrossValue) * float64(j.OrderCount))
		mpCount[(*j.OrderDate)[:10]] += float64(j.OrderCount)
	}

	thisAverage := 0.0
	if dbMetricsResponse.Count != 0 {
		thisAverage = dbMetricsResponse.Value / float64(dbMetricsResponse.Count)
	}

	for _, dt := range dates {
		y := 0.0
		if mpCount[dt] != 0 {
			y = mpSum[dt] / mpCount[dt]
		}
		graphPoints = append(graphPoints, dto.Point{
			Y: fmt.Sprintf("%.2f", y),
			X: convertDateFormat(dt),
		})
	}

	var changeColor = "#008000"
	var percentageChange = 0.0
	actualChange := ""
	if lastAverage > thisAverage {
		changeColor = "#FF0000"
		if lastAverage == 0.0 {
			percentageChange = 0.0
		} else {
			percentageChange = 100.0 - (100.0*thisAverage)/(lastAverage*1.0)
		}
		actualChange = fmt.Sprintf("-%.2f", percentageChange) + "%"
	} else if lastAverage == dbMetricsResponse.Value {
		percentageChange = 0.0
		actualChange = fmt.Sprintf("+%.2f", percentageChange) + "%"
	} else {
		if lastAverage == 0 {
			percentageChange = 100.0
		} else {
			percentageChange = (100.0*thisAverage)/(lastAverage*1.0) - 100.0
		}
		actualChange = fmt.Sprintf("+%.2f", percentageChange) + "%"
	}

	responseValue := 0.0
	if dbMetricsResponse.Count != 0 {
		responseValue = dbMetricsResponse.Value / float64(dbMetricsResponse.Count)
	}
	response = &dto.ReconciliationResponse{
		Type:        1,
		Metric:      request.Metric,
		Label:       "AOV",
		Description: "Average Order Value",
		Data: []dto.ReconciliationResponseData{
			{
				Seller: seller,
				Items: []dto.ReconciliationResponseItem{
					{
						Value: fmt.Sprintf("%s", formatRupeeAmount(responseValue)),
						Count: fmt.Sprintf("%d orders", dbMetricsResponse.Count),
					},
				},
				Graph: dto.GraphData{
					Points: graphPoints,
					XAxis:  "Date",
					YAxis:  "AOV",
				},
				Change:      actualChange,
				ChangeColor: changeColor,
			},
		},
		DateSelection: request.DateSelection,
	}
	return
}

func (s *Service) ConfirmedValue(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {

	fromDate, toDate := getToFromTimestamp(request.DateSelection)
	dates, err := getUniqueDatesFromEpochMsRange(fromDate, toDate)
	if err != nil {
		return nil, err
	}

	var seller *string
	if strings.ToUpper(request.Seller) != "ALL" {
		seller = &request.Seller
	}

	mainQuery := fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_confirmed is not null`, fromDate, toDate)
	if seller != nil {
		mainQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	fmt.Println("confirmed Value main query = ", mainQuery)
	dbMetricsResponse := &DBMetricsResponse{}
	_, err = s.repository.CustomQuery(dbMetricsResponse, mainQuery)
	if err != nil {
		return nil, err
	}

	graphDatastructure := []GraphMetricsResponse{}
	graphQueryExtras := ""
	groupByOMS := ""
	if seller != nil {
		graphQueryExtras = fmt.Sprintf(` and oms = '%s'`, *seller)
		groupByOMS = "oms,"
	}

	graphQuery := fmt.Sprintf(`
		SELECT 
			DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata')) AS order_date,
			SUM(gross_value) AS total_gross_value,
			%s
			COUNT(*) AS order_count
		FROM 
			kc_bazar_reconciliation
		WHERE 1=1 and 
			DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata')) = DATE(CONVERT_TZ(FROM_UNIXTIME(order_confirmed / 1000), 'UTC', 'Asia/Kolkata'))
			and order_placed between %d and %d %s
		GROUP BY %s order_date
		ORDER BY 
			order_date ASC;`, groupByOMS, fromDate, toDate, graphQueryExtras, groupByOMS)

	// fmt.Println("graphQuery = ", graphQuery)
	_, err = s.repository.CustomQuery(&graphDatastructure, graphQuery)

	graphDatastructureOrderPlaced := []GraphMetricsResponse{}
	if seller != nil {
		graphQueryExtras = fmt.Sprintf(` and oms = '%s'`, *seller)
		groupByOMS = "oms,"
	}

	graphQueryOrderPlaced := fmt.Sprintf(`
		SELECT 
			DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata')) as order_date,
			SUM(gross_value) as total_gross_value,
			%s
			COUNT(*) as order_count
		FROM kc_bazar_reconciliation
		WHERE 1=1 and order_placed BETWEEN %d AND %d %s
		GROUP BY %s  DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata'))
		ORDER BY order_date;`, groupByOMS, fromDate, toDate, graphQueryExtras, groupByOMS)

	// fmt.Println("graphQuery = ", graphQuery)
	_, err = s.repository.CustomQuery(&graphDatastructureOrderPlaced, graphQueryOrderPlaced)

	graphPoints := []dto.Point{}
	var mp map[string]float64 = make(map[string]float64)
	var totalOrderConfirmedCount map[string]int64 = make(map[string]int64)
	var orderPlacedCount map[string]int64 = make(map[string]int64)
	var orderPlacedValue map[string]float64 = make(map[string]float64)
	var totalOrdersConfirmedWithinSameDay int64
	var totalOrdersPlaced int64
	for _, j := range graphDatastructure {
		_, ok := mp[(*j.OrderDate)[:10]]
		if !ok {
			mp[(*j.OrderDate)[:10]] = 0.0
			totalOrderConfirmedCount[(*j.OrderDate)[:10]] = 0
		}
		mp[(*j.OrderDate)[:10]] += j.TotalGrossValue
		totalOrderConfirmedCount[(*j.OrderDate)[:10]] += j.OrderCount
		totalOrdersConfirmedWithinSameDay += j.OrderCount
	}
	for _, j := range graphDatastructureOrderPlaced {
		_, ok := orderPlacedCount[(*j.OrderDate)[:10]]
		if !ok {
			orderPlacedCount[(*j.OrderDate)[:10]] = 0
			orderPlacedValue[(*j.OrderDate)[:10]] = 0.0
		}
		orderPlacedCount[(*j.OrderDate)[:10]] += j.OrderCount
		orderPlacedValue[(*j.OrderDate)[:10]] += j.TotalGrossValue
		totalOrdersPlaced += j.OrderCount
	}

	for _, dt := range dates {
		graphPoints = append(graphPoints, dto.Point{
			Y: fmt.Sprintf("%.2f", mp[dt]/1000),
			X: convertDateFormat(dt),
			Z: fmt.Sprintf("%d", int(math.Round((float64(totalOrderConfirmedCount[dt])/float64(orderPlacedCount[dt]))*100.0))),
		})
	}

	// fmt.Println("totalOrdersConfirmedWithinSameDay = ", totalOrdersConfirmedWithinSameDay)
	// fmt.Println("totalOrdersPlaced = ", totalOrdersPlaced)
	actualChange := fmt.Sprintf("%d", int(math.Round(float64(dbMetricsResponse.Count)/float64(totalOrdersPlaced)*100))) + "%"
	// changeColor := "#008000"

	response = &dto.ReconciliationResponse{
		Type:        1,
		Metric:      request.Metric,
		Label:       "Orders Confirmed",
		Description: "Number of orders placed on easyecom",
		Data: []dto.ReconciliationResponseData{
			{
				Seller: seller,
				Items: []dto.ReconciliationResponseItem{
					{
						Count: fmt.Sprintf("%s, %d orders", actualChange, dbMetricsResponse.Count),
						Value: fmt.Sprintf("%s", formatRupeeAmount(dbMetricsResponse.Value)),
					},
				},
				Graph: dto.GraphData{
					Points: graphPoints,
					XAxis:  "Date",
					YAxis:  "Orders Confirmed (₹ in k)",
					ZAxis:  "% Order Confirmed",
				},
			},
		},
		DateSelection: request.DateSelection,
	}
	return
}

func (s *Service) CancelledOrder(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {

	fromDate, toDate := getToFromTimestamp(request.DateSelection)
	dates, err := getUniqueDatesFromEpochMsRange(fromDate, toDate)
	if err != nil {
		return nil, err
	}

	var seller *string
	if strings.ToUpper(request.Seller) != "ALL" {
		seller = &request.Seller
	}

	mainQuery := fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_cancelled is not null and order_confirmed is null`, fromDate, toDate)
	if seller != nil {
		mainQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	fmt.Println("Cancelled Order main query = ", mainQuery)
	dbMetricsResponse := &DBMetricsResponse{}
	_, err = s.repository.CustomQuery(dbMetricsResponse, mainQuery)
	if err != nil {
		return nil, err
	}

	graphDatastructure := []GraphMetricsResponse{}
	graphQueryExtras := ""
	groupByOMS := ""
	if seller != nil {
		graphQueryExtras = fmt.Sprintf(` and oms = '%s'`, *seller)
		groupByOMS = "oms,"
	}

	graphQuery := fmt.Sprintf(`
		SELECT 
			DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata')) AS order_date,
			SUM(gross_value) AS total_gross_value,
			%s
			COUNT(*) AS order_count
		FROM 
			kc_bazar_reconciliation
		WHERE 1=1 and 
			DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata')) = DATE(CONVERT_TZ(FROM_UNIXTIME(order_cancelled / 1000), 'UTC', 'Asia/Kolkata'))
			and order_placed between %d and %d %s
		GROUP BY %s order_date
		ORDER BY 
			order_date ASC;`, groupByOMS, fromDate, toDate, graphQueryExtras, groupByOMS)

	// fmt.Println("graphQuery = ", graphQuery)
	_, err = s.repository.CustomQuery(&graphDatastructure, graphQuery)

	graphDatastructureOrderPlaced := []GraphMetricsResponse{}
	if seller != nil {
		graphQueryExtras = fmt.Sprintf(` and oms = '%s'`, *seller)
		groupByOMS = "oms,"
	}

	graphQueryOrderPlaced := fmt.Sprintf(`
		SELECT 
			DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata')) as order_date,
			SUM(gross_value) as total_gross_value,
			%s
			COUNT(*) as order_count
		FROM kc_bazar_reconciliation
		WHERE 1=1 and order_placed BETWEEN %d AND %d %s
		GROUP BY %s  DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata'))
		ORDER BY order_date;`, groupByOMS, fromDate, toDate, graphQueryExtras, groupByOMS)

	// fmt.Println("graphQuery = ", graphQuery)
	_, err = s.repository.CustomQuery(&graphDatastructureOrderPlaced, graphQueryOrderPlaced)

	graphPoints := []dto.Point{}
	var mp map[string]float64 = make(map[string]float64)
	var totalOrderConfirmedCount map[string]int64 = make(map[string]int64)
	var orderPlacedCount map[string]int64 = make(map[string]int64)
	var orderPlacedValue map[string]float64 = make(map[string]float64)
	var totalOrdersConfirmedWithinSameDay int64
	var totalOrdersPlaced int64
	for _, j := range graphDatastructure {
		_, ok := mp[(*j.OrderDate)[:10]]
		if !ok {
			mp[(*j.OrderDate)[:10]] = 0.0
			totalOrderConfirmedCount[(*j.OrderDate)[:10]] = 0
		}
		mp[(*j.OrderDate)[:10]] += j.TotalGrossValue
		totalOrderConfirmedCount[(*j.OrderDate)[:10]] += j.OrderCount
		totalOrdersConfirmedWithinSameDay += j.OrderCount
	}
	for _, j := range graphDatastructureOrderPlaced {
		_, ok := orderPlacedCount[(*j.OrderDate)[:10]]
		if !ok {
			orderPlacedCount[(*j.OrderDate)[:10]] = 0
			orderPlacedValue[(*j.OrderDate)[:10]] = 0.0
		}
		orderPlacedCount[(*j.OrderDate)[:10]] += j.OrderCount
		orderPlacedValue[(*j.OrderDate)[:10]] += j.TotalGrossValue
		totalOrdersPlaced += j.OrderCount
	}

	for _, dt := range dates {
		graphPoints = append(graphPoints, dto.Point{
			Y: fmt.Sprintf("%.2f", mp[dt]/1000),
			X: convertDateFormat(dt),
			Z: fmt.Sprintf("%d", int(math.Round(float64(totalOrderConfirmedCount[dt])/float64(orderPlacedCount[dt]))*100.0)),
		})
	}

	// fmt.Println("totalOrdersConfirmedWithinSameDay = ", totalOrdersConfirmedWithinSameDay)
	// fmt.Println("totalOrdersPlaced = ", totalOrdersPlaced)
	actualChange := fmt.Sprintf("%d", int(math.Round(float64(dbMetricsResponse.Count)/float64(totalOrdersPlaced)*100))) + "%"
	// changeColor := "#008000"

	response = &dto.ReconciliationResponse{
		Type:        1,
		Metric:      request.Metric,
		Label:       "Orders Cancelled",
		Description: "Number of orders cancelled",
		Data: []dto.ReconciliationResponseData{
			{
				Seller: seller,
				Items: []dto.ReconciliationResponseItem{
					{
						Value: fmt.Sprintf("%s", formatRupeeAmount(dbMetricsResponse.Value)),
						Count: fmt.Sprintf("%s, %d orders", actualChange, dbMetricsResponse.Count),
					},
				},
				Graph: dto.GraphData{
					Points: graphPoints,
					XAxis:  "Date",
					YAxis:  "Orders Cancelled (₹ in k)",
					ZAxis:  "% Order Cancelled",
				},
			},
		},
		DateSelection: request.DateSelection,
	}
	return
}

func (s *Service) DeliveredOrder(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {

	fromDate, toDate := getToFromTimestamp(request.DateSelection)
	dates, err := getUniqueDatesFromEpochMsRange(fromDate, toDate)
	// newFromDate, newToDate := getLastXDaysEpoch(fromDate, toDate, request.DateSelection)

	var seller *string
	if strings.ToUpper(request.Seller) != "ALL" {
		seller = &request.Seller
	}
	var mainQuery = ""

	mainQuery = fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_delivered is not null and order_confirmed is not null`, fromDate, toDate)
	if seller != nil {
		mainQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	fmt.Println(" DeliveredOrder mainQuery = ", mainQuery)

	dbMetricsResponse := &DBMetricsResponse{}
	_, err = s.repository.CustomQuery(dbMetricsResponse, mainQuery)
	if err != nil {
		return nil, err
	}

	orderConfirmedQuery := fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_confirmed is not null`, fromDate, toDate)
	if seller != nil {
		orderConfirmedQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	graphDatastructure := []DeliveredOrderGraph{}
	graphQueryExtras := ""
	groupByOMS := ""
	if seller != nil {
		graphQueryExtras = fmt.Sprintf(` and oms = '%s'`, *seller)
		groupByOMS = "oms,"
	}

	graphQuery := fmt.Sprintf(`
	WITH daily_orders AS (
		SELECT 
			DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata')) as order_date,
			COUNT(CASE WHEN order_confirmed IS NOT NULL THEN 1 END) as confirmed_order_count,
			COUNT(CASE WHEN order_delivered is not null and order_confirmed is not null THEN 1 END) as orders_delivered,
			%s
			SUM(CASE WHEN order_confirmed IS NOT NULL THEN gross_value ELSE 0 END) as total_gross_value
		FROM 
			kc_bazar_reconciliation
		WHERE 
			1 = 1
			AND order_placed BETWEEN %d AND %d %s
		GROUP BY %s
			DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata'))
	)
	SELECT 
		order_date,
		total_gross_value,
		confirmed_order_count,
		%s
		orders_delivered
	FROM 
		daily_orders
	ORDER BY 
		order_date ASC;`, groupByOMS, fromDate, toDate, graphQueryExtras, groupByOMS, groupByOMS)

	fmt.Println("delivered order graphQuery = ", graphQuery)
	_, err = s.repository.CustomQuery(&graphDatastructure, graphQuery)

	graphPoints := []dto.Point{}
	var totalGrossValueMapDate map[string]float64 = make(map[string]float64)
	var confirmedOrderCountMapDate map[string]int64 = make(map[string]int64)
	var deliveredOrderCountMapDate map[string]int64 = make(map[string]int64)
	for _, j := range graphDatastructure {
		_, ok := totalGrossValueMapDate[(*j.OrderDate)[:10]]
		if !ok {
			totalGrossValueMapDate[(*j.OrderDate)[:10]] = 0.0
			confirmedOrderCountMapDate[(*j.OrderDate)[:10]] = 0
			deliveredOrderCountMapDate[(*j.OrderDate)[:10]] = 0
		}
		totalGrossValueMapDate[(*j.OrderDate)[:10]] += j.TotalGrossValue
		confirmedOrderCountMapDate[(*j.OrderDate)[:10]] += *j.ConfirmedOrderCount
		deliveredOrderCountMapDate[(*j.OrderDate)[:10]] += *j.OrdersDelivered
	}

	for _, dt := range dates {
		graphPoints = append(graphPoints, dto.Point{
			Y: fmt.Sprintf("%d", int(math.Round(float64(deliveredOrderCountMapDate[dt])/float64(confirmedOrderCountMapDate[dt])*100))),
			X: convertDateFormat(dt),
			Z: fmt.Sprintf("%.2f", totalGrossValueMapDate[dt]/1000),
		})
	}

	dbMetricsResponse2 := TotalConfirmedAndDeliveredOrder{}

	getTotalOrderConfirmedAndDeliveredQuery := fmt.Sprintf(`
	SELECT 
		COUNT(*) as total_orders_confirmed,
		%s
		COUNT(CASE WHEN order_delivered is not null and order_confirmed is not null THEN 1 END) as total_orders_delivered
	FROM 
		kiranaclubdb.kc_bazar_reconciliation kbr 
	WHERE 1=1 %s and 
		order_placed BETWEEN %d AND %d and order_confirmed is not null;`, groupByOMS, graphQueryExtras, fromDate, toDate)

	_, err = s.repository.CustomQuery(&dbMetricsResponse2, getTotalOrderConfirmedAndDeliveredQuery)
	if err != nil {
		return nil, err
	}

	fmt.Println("getTotalOrderConfirmedAndDeliveredQuery = ", getTotalOrderConfirmedAndDeliveredQuery)
	fmt.Println("dbMetricsResponse2 - ", dbMetricsResponse2)
	perOrderDelivered := 0.0
	if dbMetricsResponse2.TotalOrdersConfirmed != 0 {
		perOrderDelivered = (float64(dbMetricsResponse2.TotalOrdersDelivered) / float64(dbMetricsResponse2.TotalOrdersConfirmed)) * 100
	}

	response = &dto.ReconciliationResponse{
		Type:        1,
		Metric:      request.Metric,
		Label:       "Orders Delivered",
		Description: "Number of orders delivered",
		Data: []dto.ReconciliationResponseData{
			{
				Seller: seller,
				Items: []dto.ReconciliationResponseItem{
					{
						Value: fmt.Sprintf("%s", formatRupeeAmount(dbMetricsResponse.Value)),
						Count: fmt.Sprintf("%d", int(math.Round(perOrderDelivered))) + "%" + fmt.Sprintf(", %d orders", dbMetricsResponse.Count),
					},
				},
				Graph: dto.GraphData{
					Points: graphPoints,
					XAxis:  "date",
					YAxis:  "Orders Delivered %",
					ZAxis:  "Orders Delivered (₹ in k)",
				},
			},
		},
		DateSelection: request.DateSelection,
	}
	return
}

func (s *Service) DispatchedOrderNotDelivered(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {

	fromDate, toDate := getToFromTimestamp(request.DateSelection)
	dates, err := getUniqueDatesFromEpochMsRange(fromDate, toDate)
	// newFromDate, newToDate := getLastXDaysEpoch(fromDate, toDate, request.DateSelection)

	var seller *string
	if strings.ToUpper(request.Seller) != "ALL" {
		seller = &request.Seller
	}
	var mainQuery = ""

	mainQuery = fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_shipment_created is not null and order_delivered is null and order_confirmed is not null and order_returned is null`, fromDate, toDate)
	if seller != nil {
		mainQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	fmt.Println("DispatchedOrderNotDelivered mainQuery = ", mainQuery)
	dbMetricsResponse := &DBMetricsResponse{}
	_, err = s.repository.CustomQuery(dbMetricsResponse, mainQuery)
	if err != nil {
		return nil, err
	}

	orderConfirmedQuery := fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_confirmed is not null`, fromDate, toDate)
	if seller != nil {
		orderConfirmedQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	fmt.Println("DispatchedOrderNotDelivered orderConfirmedQuery = ", orderConfirmedQuery)
	graphDatastructure := []DeliveredOrderGraph{}
	graphQueryExtras := ""
	groupByOMS := ""
	if seller != nil {
		graphQueryExtras = fmt.Sprintf(` and oms = '%s'`, *seller)
		groupByOMS = "oms,"
	}

	graphQuery := fmt.Sprintf(`
	WITH daily_orders AS (
		SELECT 
			DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata')) as order_date,
			COUNT(CASE WHEN order_confirmed IS NOT NULL THEN 1 END) as confirmed_order_count,
			COUNT(CASE WHEN order_shipment_created is not null and order_delivered is null and order_confirmed is not null and order_returned is null THEN 1 END) as orders_shipped,
			%s
			SUM(CASE WHEN order_confirmed IS NOT NULL THEN gross_value ELSE 0 END) as total_gross_value
		FROM 
			kc_bazar_reconciliation
		WHERE 
			1 = 1
			AND order_placed BETWEEN %d AND %d %s
		GROUP BY %s
			DATE(CONVERT_TZ(FROM_UNIXTIME(order_placed / 1000), 'UTC', 'Asia/Kolkata'))
	)
	SELECT 
		order_date,
		total_gross_value,
		confirmed_order_count,
		%s
		orders_shipped
	FROM 
		daily_orders
	ORDER BY 
		order_date ASC;`, groupByOMS, fromDate, toDate, graphQueryExtras, groupByOMS, groupByOMS)

	fmt.Println("dispatched order graphQuery = ", graphQuery)
	_, err = s.repository.CustomQuery(&graphDatastructure, graphQuery)

	graphPoints := []dto.Point{}
	var totalGrossValueMapDate map[string]float64 = make(map[string]float64)
	var confirmedOrderCountMapDate map[string]int64 = make(map[string]int64)
	var deliveredOrderCountMapDate map[string]int64 = make(map[string]int64)
	for _, j := range graphDatastructure {
		_, ok := totalGrossValueMapDate[(*j.OrderDate)[:10]]
		if !ok {
			totalGrossValueMapDate[(*j.OrderDate)[:10]] = 0.0
			confirmedOrderCountMapDate[(*j.OrderDate)[:10]] = 0
			deliveredOrderCountMapDate[(*j.OrderDate)[:10]] = 0
		}
		totalGrossValueMapDate[(*j.OrderDate)[:10]] += j.TotalGrossValue
		confirmedOrderCountMapDate[(*j.OrderDate)[:10]] += *j.ConfirmedOrderCount
		deliveredOrderCountMapDate[(*j.OrderDate)[:10]] += *j.OrdersShipped
	}

	for _, dt := range dates {
		graphPoints = append(graphPoints, dto.Point{
			Y: fmt.Sprintf("%d", int(math.Round(float64(deliveredOrderCountMapDate[dt])/float64(confirmedOrderCountMapDate[dt])*100))),
			X: convertDateFormat(dt),
			Z: fmt.Sprintf("%.2f", totalGrossValueMapDate[dt]/1000),
		})
	}

	dbMetricsResponse2 := TotalConfirmedAndDeliveredOrder{}

	getTotalOrderConfirmedAndShippedQuery := fmt.Sprintf(`
	SELECT 
		COUNT(*) as total_orders_confirmed,
		%s
		COUNT(CASE WHEN order_shipment_created is not null and order_delivered is null and order_confirmed is not null and order_returned is null THEN 1 END) as total_orders_shipped
	FROM 
		kiranaclubdb.kc_bazar_reconciliation kbr 
	WHERE 1=1 %s and 
		order_placed BETWEEN %d AND %d and order_confirmed is not null;`, groupByOMS, graphQueryExtras, fromDate, toDate)

	_, err = s.repository.CustomQuery(&dbMetricsResponse2, getTotalOrderConfirmedAndShippedQuery)
	if err != nil {
		return nil, err
	}

	fmt.Println("getTotalOrderConfirmedAndShippedQuery = ", getTotalOrderConfirmedAndShippedQuery)
	fmt.Println("dbMetricsResponse2 - ", dbMetricsResponse2)
	perOrderDelivered := 0.0
	if dbMetricsResponse2.TotalOrdersConfirmed != 0 {
		perOrderDelivered = (float64(dbMetricsResponse2.TotalOrdersShipped) / float64(dbMetricsResponse2.TotalOrdersConfirmed)) * 100
	}

	response = &dto.ReconciliationResponse{
		Type:        1,
		Metric:      request.Metric,
		Label:       "Orders In Transit",
		Description: "Number of in transit",
		Data: []dto.ReconciliationResponseData{
			{
				Seller: seller,
				Items: []dto.ReconciliationResponseItem{
					{
						Value: fmt.Sprintf("%s", formatRupeeAmount(dbMetricsResponse.Value)),
						Count: fmt.Sprintf("%d", int(math.Round(perOrderDelivered))) + "%" + fmt.Sprintf(", %d orders", dbMetricsResponse.Count),
					},
				},
				Graph: dto.GraphData{
					Points: graphPoints,
					XAxis:  "date",
					YAxis:  "Orders Transit %",
					ZAxis:  "Orders Transit (₹ in k)",
				},
			},
		},
		DateSelection: request.DateSelection,
	}
	return
}

func (s *Service) PendingConfirmation(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {

	fromDate, toDate := getToFromTimestamp(request.DateSelection)

	var seller *string
	if strings.ToUpper(request.Seller) != "ALL" {
		seller = &request.Seller
	}
	mainQuery := fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_cancelled is null and order_confirmed is null `, fromDate, toDate)
	if seller != nil {
		mainQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	fmt.Println("pending confirmation mainQuery = ", mainQuery)
	dbMetricsResponse := &DBMetricsResponse{}
	_, err = s.repository.CustomQuery(dbMetricsResponse, mainQuery)
	if err != nil {
		return nil, err
	}

	dbMetricsResponse2 := &DBMetricsResponse{}
	percentageCalculatorQuery := fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d `, fromDate, toDate)
	if seller != nil {
		percentageCalculatorQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	_, err = s.repository.CustomQuery(dbMetricsResponse2, percentageCalculatorQuery)

	actualChange := fmt.Sprintf("%d", int(math.Round(float64(dbMetricsResponse.Count)/float64(dbMetricsResponse2.Count)*100))) + "%"

	graphQueryExtras := ""
	if seller != nil {
		graphQueryExtras = fmt.Sprintf(` and oms = '%s'`, *seller)
	}

	nccs := []NonConfirmedCallStatus{}
	contactedNonContactedQuery := fmt.Sprintf(`
		WITH filtered_orders AS (
		SELECT 
			order_id
		FROM 
			kiranaclubdb.kc_bazar_reconciliation
		WHERE
			order_placed BETWEEN %d AND %d
			AND order_cancelled IS NULL
			AND order_confirmed IS NULL
			%s
		)
		SELECT 
			kbr.order_id as kbr_order_id,
			kbr.gross_value as gross_value,
			kbr.oms as oms,
			kcs.order_id as kcs_order_id,
			kcs.call_status as call_status
		FROM 
			kiranaclubdb.kc_bazar_reconciliation kbr
		LEFT JOIN 
			kiranaclubdb.kiranabazar_call_status kcs
		ON 
			kbr.order_id = kcs.order_id
		WHERE 
			kbr.order_id IN (SELECT order_id FROM filtered_orders);
		`, fromDate, toDate, graphQueryExtras)

	fmt.Println("contactedNonContactedQuery = ", contactedNonContactedQuery)
	_, err = s.repository.CustomQuery(&nccs, contactedNonContactedQuery)
	if err != nil {
		return nil, err
	}

	contactedNumber := 0
	nonContactedNumber := 0
	contactedGMV := 0.0
	nonContactedGMV := 0.0
	for _, j := range nccs {
		if j.KCSOrderID != nil {
			contactedNumber += 1
			contactedGMV += j.GrossValue
		} else {
			nonContactedNumber += 1
			nonContactedGMV += j.GrossValue
		}
	}

	response = &dto.ReconciliationResponse{
		Type:        1,
		Metric:      request.Metric,
		Label:       "Orders Pending Confirmation",
		Description: "Orders which are placed but not confirmed or cancelled",
		Data: []dto.ReconciliationResponseData{
			{
				Seller: seller,
				Items: []dto.ReconciliationResponseItem{
					{
						Value: fmt.Sprintf("%s", formatRupeeAmount(dbMetricsResponse.Value)),
						Count: fmt.Sprintf("%s, %d orders", actualChange, dbMetricsResponse.Count),
					},
				},
				Table: &dto.TableData{
					Type:    1,
					Columns: []string{"", "Contacted", "NonContacted"},
					Data: [][]string{
						[]string{"ORDERS", fmt.Sprintf("%d", contactedNumber), fmt.Sprintf("%d", nonContactedNumber)},
						[]string{"GMV", fmt.Sprintf("₹%.2fk", contactedGMV/1000), fmt.Sprintf("₹%.2fk", nonContactedGMV/1000)},
					},
				},
			},
		},
		DateSelection: request.DateSelection,
	}
	return
}

func (s *Service) PendingDispatch(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {

	fromDate, toDate := getToFromTimestamp(request.DateSelection)

	var seller *string
	if strings.ToUpper(request.Seller) != "ALL" {
		seller = &request.Seller
	}
	mainQuery := fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_confirmed is not null and order_shipment_created is null and order_delivered is null and order_returned is null`, fromDate, toDate)
	if seller != nil {
		mainQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	fmt.Println("Pending Dispatch mainQuery = ", mainQuery)
	dbMetricsResponse := &DBMetricsResponse{}
	_, err = s.repository.CustomQuery(dbMetricsResponse, mainQuery)
	if err != nil {
		return nil, err
	}
	response = &dto.ReconciliationResponse{
		Type:        1,
		Metric:      request.Metric,
		Label:       "Orders Pending Dispatch",
		Description: "Orders which are confirmed but not yet dispatched",
		Data: []dto.ReconciliationResponseData{
			{
				Seller: seller,
				Items: []dto.ReconciliationResponseItem{
					{
						Value: fmt.Sprintf("%s", formatRupeeAmount(dbMetricsResponse.Value)),
						Count: fmt.Sprintf("%d orders", dbMetricsResponse.Count),
					},
				},
				// Change:      actualChange,
				// ChangeColor: changeColor,
			},
		},
		DateSelection: request.DateSelection,
	}
	return
}

func (s *Service) OrdersReturned(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {

	fromDate, toDate := getToFromTimestamp(request.DateSelection)

	var seller *string
	if strings.ToUpper(request.Seller) != "ALL" {
		seller = &request.Seller
	}
	mainQuery := fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_confirmed is not null and order_returned is not null`, fromDate, toDate)
	if seller != nil {
		mainQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	fmt.Println("OrdersReturned mainQuery = ", mainQuery)
	dbMetricsResponse := &DBMetricsResponse{}
	_, err = s.repository.CustomQuery(dbMetricsResponse, mainQuery)
	if err != nil {
		return nil, err
	}
	response = &dto.ReconciliationResponse{
		Type:        1,
		Metric:      request.Metric,
		Label:       "Orders Returned",
		Description: "Orders which got returned",
		Data: []dto.ReconciliationResponseData{
			{
				Seller: seller,
				Items: []dto.ReconciliationResponseItem{
					{
						Value: fmt.Sprintf("%s", formatRupeeAmount(dbMetricsResponse.Value)),
						Count: fmt.Sprintf("%d orders", dbMetricsResponse.Count),
					},
				},
				// Change:      actualChange,
				// ChangeColor: changeColor,
			},
		},
		DateSelection: request.DateSelection,
	}
	return
}

func (s *Service) TransitOrder(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {

	fromDate, toDate := getToFromTimestamp(request.DateSelection)
	newFromDate, newToDate := getLastXDaysEpoch(fromDate, toDate, request.DateSelection)
	if err != nil {
		return nil, err
	}

	var seller *string
	if strings.ToUpper(request.Seller) != "ALL" {
		seller = &request.Seller
	}
	query := fmt.Sprintf(`select oms as brand, sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_shipment_created is not null and order_cancelled is null and order_delivered is null `, fromDate, toDate)
	if seller != nil {
		query += fmt.Sprintf(` and oms = '%s' `, *seller)
	}
	query += "group by oms;"

	dbMetricsResponse := &[]DBMetricsResponse{}
	_, err = s.repository.CustomQuery(dbMetricsResponse, query)
	if err != nil {
		return nil, err
	}

	dbMetricsResponse2 := &[]DBMetricsResponse{}
	query3 := fmt.Sprintf(`select oms as brand, sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_shipment_created is not null and order_cancelled is null and order_delivered is null `, newFromDate, newToDate)
	if seller != nil {
		query3 += fmt.Sprintf(` and oms = '%s' `, *seller)
	}
	query3 += "group by oms;"

	_, err = s.repository.CustomQuery(dbMetricsResponse2, query3)

	currentMp := make(map[string]DBMetricsResponse)
	newMp := make(map[string]DBMetricsResponse)
	currentBrands := []string{}
	newBrands := []string{}
	reconData := []dto.ReconciliationResponseData{}

	for _, brandLevel := range *dbMetricsResponse {
		if brandLevel.Brand != nil {
			currentMp[*brandLevel.Brand] = brandLevel
			currentBrands = append(currentBrands, *brandLevel.Brand)
		}
	}

	for _, brandLevel := range *dbMetricsResponse2 {
		if brandLevel.Brand != nil {
			newMp[*brandLevel.Brand] = brandLevel
			newBrands = append(newBrands, *brandLevel.Brand)
		}
	}

	// if len(newBrands) != len(currentBrands) {
	// 	return nil, errors.New("issue with brands mismatching")
	// }
	newAllBrands := brands.GetAllBrands()
	if strings.ToUpper(request.Seller) != "ALL" {
		newAllBrands = []string{request.Seller}
	}
	for _, brand := range newAllBrands {
		brandLevel, ok := currentMp[brand]
		if !ok {
			brandLevel = DBMetricsResponse{}
		}
		lastSum := 0.0

		_, ok = newMp[brand]
		if ok {
			lastSum = newMp[brand].Value
		}

		thisSum := brandLevel.Value

		var changeColor = "#008000"
		var percentageChange = 0.0
		actualChange := ""
		if lastSum > thisSum {
			changeColor = "#FF0000"
			if lastSum == 0.0 {
				percentageChange = 0.0
			} else {
				percentageChange = 100.0 - (100.0*float64(thisSum))/float64(lastSum)
			}
			actualChange = fmt.Sprintf("-%.2f", percentageChange) + "%"
		} else if lastSum == thisSum {
			percentageChange = 0.0
			actualChange = fmt.Sprintf("+%.2f", percentageChange) + "%"
		} else {
			if lastSum == 0 {
				percentageChange = 100.0
			} else {
				percentageChange = (100.0*float64(thisSum))/float64(lastSum) - 100.0
			}
			actualChange = fmt.Sprintf("+%.2f", percentageChange) + "%"
		}

		brandMap := brands.GetAllCapsBrandMap()

		newBr, exists := brandMap[brand]

		if !exists {
			return nil, fmt.Errorf("brand %s not found in brand map in transit order", brand)
		}

		reconData = append(reconData, dto.ReconciliationResponseData{
			Seller:      &newBr,
			Change:      actualChange,
			ChangeColor: changeColor,
			Items: []dto.ReconciliationResponseItem{
				{
					Count: fmt.Sprintf("%d orders", brandLevel.Count),
					Value: fmt.Sprintf("%s", formatRupeeAmount(brandLevel.Value)),
				},
			},
		})
	}

	if len(reconData) == 0 {
		reconData = append(reconData, dto.ReconciliationResponseData{
			Seller:      &request.Seller,
			Change:      "0.0%",
			ChangeColor: "#FF0000",
			Items: []dto.ReconciliationResponseItem{
				{
					Count: fmt.Sprintf("%d orders", 0),
					Value: fmt.Sprintf("%s", formatRupeeAmount(0/1000)),
				},
			},
		})
	}

	response = &dto.ReconciliationResponse{
		Type:          3,
		Metric:        request.Metric,
		Label:         "Orders In Transit",
		Description:   "Orders which dispatched but not yet delivered",
		Data:          reconData,
		DateSelection: request.DateSelection,
	}
	return
}

func (s *Service) ReturnedOrder(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {

	fromDate, toDate := getToFromTimestamp(request.DateSelection)
	newFromDate, newToDate := getLastXDaysEpoch(fromDate, toDate, request.DateSelection)
	if err != nil {
		return nil, err
	}

	var seller *string
	if strings.ToUpper(request.Seller) != "ALL" {
		seller = &request.Seller
	}
	query := fmt.Sprintf(`select oms as brand, sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_returned is not null  `, fromDate, toDate)
	if seller != nil {
		query += fmt.Sprintf(` and oms = '%s' `, *seller)
	}
	query += "group by oms;"

	dbMetricsResponse := &[]DBMetricsResponse{}
	_, err = s.repository.CustomQuery(dbMetricsResponse, query)
	if err != nil {
		return nil, err
	}

	dbMetricsResponse2 := &[]DBMetricsResponse{}
	query3 := fmt.Sprintf(`select oms as brand, sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_returned is not null  `, newFromDate, newToDate)
	if seller != nil {
		query3 += fmt.Sprintf(` and oms = '%s' `, *seller)
	}
	query3 += "group by oms;"

	_, err = s.repository.CustomQuery(dbMetricsResponse2, query3)

	currentMp := make(map[string]DBMetricsResponse)
	newMp := make(map[string]DBMetricsResponse)
	currentBrands := []string{}
	newBrands := []string{}
	reconData := []dto.ReconciliationResponseData{}

	for _, brandLevel := range *dbMetricsResponse {
		if brandLevel.Brand != nil {
			currentMp[*brandLevel.Brand] = brandLevel
			currentBrands = append(currentBrands, *brandLevel.Brand)
		}
	}

	for _, brandLevel := range *dbMetricsResponse2 {
		if brandLevel.Brand != nil {
			newMp[*brandLevel.Brand] = brandLevel
			newBrands = append(newBrands, *brandLevel.Brand)
		}
	}

	// if len(newBrands) != len(currentBrands) {
	// 	return nil, errors.New("issue with brands mismatching")
	// }
	newAllBrands := brands.GetAllBrands()
	if strings.ToUpper(request.Seller) != "ALL" {
		newAllBrands = []string{request.Seller}
	}
	for _, brand := range newAllBrands {
		brandLevel, ok := currentMp[brand]
		if !ok {
			brandLevel = DBMetricsResponse{}
		}
		lastSum := 0.0

		_, ok = newMp[brand]
		if ok {
			lastSum = newMp[brand].Value
		}

		thisSum := brandLevel.Value

		var changeColor = "#008000"
		var percentageChange = 0.0
		actualChange := ""
		if lastSum > thisSum {
			changeColor = "#FF0000"
			if lastSum == 0.0 {
				percentageChange = 0.0
			} else {
				percentageChange = 100.0 - (100.0*float64(thisSum))/float64(lastSum)
			}
			actualChange = fmt.Sprintf("-%.2f", percentageChange) + "%"
		} else if lastSum == thisSum {
			percentageChange = 0.0
			actualChange = fmt.Sprintf("+%.2f", percentageChange) + "%"
		} else {
			if lastSum == 0 {
				percentageChange = 100.0
			} else {
				percentageChange = (100.0*float64(thisSum))/float64(lastSum) - 100.0
			}
			actualChange = fmt.Sprintf("+%.2f", percentageChange) + "%"
		}

		brandMap := brands.GetAllCapsBrandMap()

		newBr, exists := brandMap[brand]

		if !exists {
			return nil, fmt.Errorf("brand %s not found in brand map in returned order", brand)
		}

		reconData = append(reconData, dto.ReconciliationResponseData{
			Seller:      &newBr,
			Change:      actualChange,
			ChangeColor: changeColor,
			Items: []dto.ReconciliationResponseItem{
				{
					Count: fmt.Sprintf("%d orders", brandLevel.Count),
					Value: fmt.Sprintf("%s", formatRupeeAmount(brandLevel.Value)),
				},
			},
		})
	}

	if len(reconData) == 0 {
		reconData = append(reconData, dto.ReconciliationResponseData{
			Seller:      &request.Seller,
			Change:      "0.0%",
			ChangeColor: "#FF0000",
			Items: []dto.ReconciliationResponseItem{
				{
					Count: fmt.Sprintf("%d orders", 0),
					Value: fmt.Sprintf("%s", formatRupeeAmount(0/1000)),
				},
			},
		})
	}

	response = &dto.ReconciliationResponse{
		Type:          3,
		Metric:        request.Metric,
		Label:         "Orders Returned",
		Description:   "Order which were placed on selected date range and are returned",
		Data:          reconData,
		DateSelection: request.DateSelection,
	}
	return
}

func (s *Service) DelayedOrder(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {

	fromDate, toDate := getToFromTimestamp(request.DateSelection)
	newFromDate, newToDate := getLastXDaysEpoch(fromDate, toDate, request.DateSelection)
	if err != nil {
		return nil, err
	}

	var seller *string
	if strings.ToUpper(request.Seller) != "ALL" {
		seller = &request.Seller
	}
	mainQuery := fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_shipment_created is not null and order_delivered > expected_delivery_timestamp `, fromDate, toDate)
	if seller != nil {
		mainQuery += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	dbMetricsResponse := &DBMetricsResponse{}
	_, err = s.repository.CustomQuery(dbMetricsResponse, mainQuery)
	if err != nil {
		return nil, err
	}

	dbMetricsResponse2 := &DBMetricsResponse{}
	query3 := fmt.Sprintf(`select sum(gross_value) as value, count(*) as count from kiranaclubdb.kc_bazar_reconciliation kbr where order_placed between %d and %d and order_shipment_created is not null and order_delivered > expected_delivery_timestamp `, newFromDate, newToDate)
	if seller != nil {
		query3 += fmt.Sprintf(` and oms = '%s';`, *seller)
	}

	_, err = s.repository.CustomQuery(dbMetricsResponse2, query3)

	lastSum := dbMetricsResponse2.Count
	thisSum := dbMetricsResponse.Count

	var changeColor = "#FF0000"
	var percentageChange = 0.0
	actualChange := ""
	if lastSum > thisSum {
		changeColor = "#008000"
		if lastSum == 0.0 {
			percentageChange = 0.0
		} else {
			percentageChange = 100.0 - (100.0*float64(thisSum))/float64(lastSum)
		}
		actualChange = fmt.Sprintf("-%.2f", percentageChange) + "%"
	} else if lastSum == thisSum {
		percentageChange = 0.0
		actualChange = fmt.Sprintf("+%.2f", percentageChange) + "%"
	} else {
		if lastSum == 0 {
			percentageChange = 100.0
		} else {
			percentageChange = (100.0*float64(thisSum))/float64(lastSum) - 100.0
		}
		actualChange = fmt.Sprintf("+%.2f", percentageChange) + "%"
	}

	response = &dto.ReconciliationResponse{
		Type:        1,
		Metric:      request.Metric,
		Label:       "Orders Delayed (7 days after dispatched)",
		Description: "Orders placed on selected date range and got delayed from expected delivery, ",
		Data: []dto.ReconciliationResponseData{
			{
				Seller: seller,
				Items: []dto.ReconciliationResponseItem{
					{
						Count: fmt.Sprintf("%d orders", dbMetricsResponse.Count),
						Value: fmt.Sprintf("%s", formatRupeeAmount(dbMetricsResponse.Value)),
					},
				},
				Change:      actualChange,
				ChangeColor: changeColor,
			},
		},
		DateSelection: request.DateSelection,
	}
	return
}

func (s *Service) AverageDeliveryTime(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {
	if request.Seller == "" {
		response = &dto.ReconciliationResponse{
			Type:        1,
			Metric:      request.Metric,
			Label:       "Average Delivery Time",
			Description: "time taken from palced to delivery",
			Data: []dto.ReconciliationResponseData{
				{
					Items: []dto.ReconciliationResponseItem{
						{
							Value: "5.6 days",
						},
					},
					Change:      "+0.1k",
					ChangeColor: "#FF0000",
				},
			},
			DateSelection: request.DateSelection,
		}

	} else {
		response = &dto.ReconciliationResponse{
			Type:        1,
			Metric:      request.Metric,
			Label:       "Average Delivery Time",
			Description: "time taken from palced to delivery",
			Data: []dto.ReconciliationResponseData{
				{
					Items: []dto.ReconciliationResponseItem{
						{
							Value: "4.2 days",
						},
					},
					Seller:      &request.Seller,
					Change:      "+2%",
					ChangeColor: "#008000",
				},
			},
			DateSelection: request.DateSelection,
		}

	}
	return
}

func (s *Service) AverageConfirmationTime(ctx context.Context, request *dto.ReconciliationRequest) (response *dto.ReconciliationResponse, err error) {
	if request.Seller == "" {
		response = &dto.ReconciliationResponse{
			Type:        1,
			Metric:      request.Metric,
			Label:       "Average Confirmation Time",
			Description: "Time for order to get confirm",
			Data: []dto.ReconciliationResponseData{
				{
					Items: []dto.ReconciliationResponseItem{
						{
							Value: "36 HR",
						},
					},
					Change:      "+0.1k",
					ChangeColor: "#FF0000",
				},
			},
			DateSelection: request.DateSelection,
		}

	} else {
		response = &dto.ReconciliationResponse{
			Type:        1,
			Metric:      request.Metric,
			Label:       "Average Confirmation Time",
			Description: "Time for order to get confirm",
			Data: []dto.ReconciliationResponseData{
				{
					Items: []dto.ReconciliationResponseItem{
						{
							Value: "18 HR",
						},
					},
					Seller:      &request.Seller,
					Change:      "+2%",
					ChangeColor: "#008000",
				},
			},
			DateSelection: request.DateSelection,
		}

	}
	return
}

func includes(slice []string, str string) bool {
	for _, item := range slice {
		if item == str {
			return true
		}
	}
	return false
}

var ALL_ORDER_STATUSES = []string{"order_placed", "gross_value", "order_value", "order_confirmed", "order_delivered", "order_shipment_created", "order_returned", "expected_delivery_timestamp", "order_cancelled", "order_updated", "order_dispatched", "order_ofd"}

// this function returns epoch in milliseconds
func normalizeToMilliseconds(epoch int64) int64 {
	// Get the number of digits to determine the format
	digits := len(strconv.FormatInt(epoch, 10))

	switch {
	case digits <= 10: // Seconds (typical Unix timestamp)
		return epoch * 1000
	case digits <= 13: // Milliseconds
		return epoch
	case digits <= 16: // Microseconds
		return epoch / 1000
	default: // Nanoseconds or larger - convert to milliseconds
		return epoch / 1000000
	}
}

// constructInsertOrUpdateQuery constructs the query for inserting or updating the database.
func constructInsertOrUpdateQuery(request *dto.AddReconciliationRequest, reconciliationData dao.KiranaBazarReconciliation, metricsUpdated *[]string) string {
	timenow := time.Now().UnixMilli()
	alias := "new_data"
	baseQuery := `INSERT INTO kiranaclubdb.kc_bazar_reconciliation (order_id`
	valuesPlaceholder := fmt.Sprintf("VALUES (%d", request.OrderID)
	updateClause := []string{}

	// Include OMS if provided
	if request.OMS != "" {
		baseQuery += ", oms"
		valuesPlaceholder += fmt.Sprintf(", '%s'", request.OMS)
		updateClause = append(updateClause, fmt.Sprintf("oms = %s.oms", alias))
	}

	// Include DiscountValue if provided
	if request.DiscountValue != nil {
		baseQuery += ", discount_value"
		valuesPlaceholder += fmt.Sprintf(", %.2f", *request.DiscountValue)
		updateClause = append(updateClause, fmt.Sprintf("discount_value = %s.discount_value", alias))
	}

	// Include GrossValue if provided
	if request.GrossValue != nil {
		baseQuery += ", gross_value"
		valuesPlaceholder += fmt.Sprintf(", %.2f", *request.GrossValue)
		updateClause = append(updateClause, fmt.Sprintf("gross_value = %s.gross_value", alias))
	}

	// Include OrderValue if provided
	if request.OrderValue != nil {
		baseQuery += ", order_value"
		valuesPlaceholder += fmt.Sprintf(", %.2f", *request.OrderValue)
		updateClause = append(updateClause, fmt.Sprintf("order_value = %s.order_value", alias))
	}

	if request.CartValue != nil {
		baseQuery += ", cart_value"
		valuesPlaceholder += fmt.Sprintf(", %.2f", *request.CartValue)
		updateClause = append(updateClause, fmt.Sprintf("cart_value = %s.cart_value", alias))
	}

	if request.GTV != nil {
		baseQuery += ", gtv"
		valuesPlaceholder += fmt.Sprintf(", %.2f", *request.GTV)
		updateClause = append(updateClause, fmt.Sprintf("gtv = %s.gtv", alias))
	}

	// Construct column names, values, and update clause from the data slice
	for _, entry := range request.Data {
		if entry.OrderStatus == "order_placed" && reconciliationData.OrderPlaced != 0 {
			continue
		} else if entry.OrderStatus == "order_confirmed" && reconciliationData.OrderConfirmed != nil && *reconciliationData.OrderConfirmed != 0 {
			continue
		} else if entry.OrderStatus == "order_delivered" && reconciliationData.OrderDelivered != nil && *reconciliationData.OrderDelivered != 0 {
			continue
		} else if entry.OrderStatus == "order_shipment_created" && reconciliationData.OrderShipmentCreated != nil && *reconciliationData.OrderShipmentCreated != 0 {
			continue
		} else if entry.OrderStatus == "order_returned" && reconciliationData.OrderReturned != nil && *reconciliationData.OrderReturned != 0 {
			continue
		} else if entry.OrderStatus == "order_cancelled" && reconciliationData.OrderCancelled != nil && *reconciliationData.OrderCancelled != 0 {
			continue
		} else if entry.OrderStatus == "order_updated" && reconciliationData.OrderUpdated != nil && *reconciliationData.OrderUpdated != 0 {
			continue
		} else if entry.OrderStatus == "order_dispatched" && reconciliationData.OrderDispatched != nil && *reconciliationData.OrderDispatched != 0 {
			continue
		} else if entry.OrderStatus == "order_ofd" && reconciliationData.OrderOfd != nil && *reconciliationData.OrderOfd != 0 {
			continue
		}
		baseQuery += fmt.Sprintf(", %s", entry.OrderStatus)
		*metricsUpdated = append(*metricsUpdated, entry.OrderStatus)
		valuesPlaceholder += fmt.Sprintf(", %d", normalizeToMilliseconds(entry.TimeStamp))
		updateClause = append(updateClause, fmt.Sprintf("%s = %s.%s", entry.OrderStatus, alias, entry.OrderStatus))
	}
	updateClause = append(updateClause, fmt.Sprintf(`updated_at = %d`, timenow))

	// Append static columns (created_at, updated_at)
	baseQuery += `, created_at, updated_at)`
	valuesPlaceholder += fmt.Sprintf(`, %d, %d)`, timenow, timenow)

	// Combine all parts to form the final query with an alias
	finalQuery := fmt.Sprintf("%s %s AS %s ON DUPLICATE KEY UPDATE %s;", baseQuery, valuesPlaceholder, alias, strings.Join(updateClause, ", "))
	return finalQuery
}
func validateAddDataForReconciliationRequest(request *dto.AddReconciliationRequest) (bool, error) {
	if request.OrderID == 0 {
		return false, errors.New("order id is invalid")
	}
	for _, j := range request.Data {
		if !includes(ALL_ORDER_STATUSES, j.OrderStatus) {
			return false, errors.New("order_status is invalid, " + j.OrderStatus)
		}
	}
	return true, nil
}

// FindDifference returns elements that are in array1 but not in array2
func FindDifference(array1, array2 []string) []string {
	// Create a map to store elements from array2 for O(1) lookups
	elementsInArray2 := make(map[string]bool)
	for _, element := range array2 {
		elementsInArray2[element] = true
	}

	// Create a result slice to store elements in array1 but not in array2
	var difference []string

	// Check each element in array1
	for _, element := range array1 {
		// If the element is not in array2, add it to the result
		if !elementsInArray2[element] {
			difference = append(difference, element)
		}
	}

	return difference
}

func (s *Service) AddDataForReconciliation(ctx context.Context, request *dto.AddReconciliationRequest) (response *dto.AddReconciliationResponse, err error) {
	_, err = validateAddDataForReconciliationRequest(request)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("%v", err))
		return nil, err
	}
	reconciliationDataForOrder := dao.KiranaBazarReconciliation{}
	_, err = s.repository.CustomQuery(&reconciliationDataForOrder, fmt.Sprintf(`select * from kc_bazar_reconciliation where order_id = %d`, request.OrderID))
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("%v", err))
		return nil, err
	}
	metricsToBeUpdated := []string{}
	for _, j := range request.Data {
		metricsToBeUpdated = append(metricsToBeUpdated, j.OrderStatus)
	}
	metricsUpdated := []string{}
	query := constructInsertOrUpdateQuery(request, reconciliationDataForOrder, &metricsUpdated)
	_, err = s.repository.CustomQuery(nil, query)
	if err != nil {
		slack.SendSlackMessage(query)
		return nil, err
	}
	response = &dto.AddReconciliationResponse{
		Status:  "200",
		Message: "inserted, updated",
		Error: dto.AppResponseError{
			Code:        nil,
			Message:     nil,
			Description: nil,
		},
		UpdatedMetrics:    metricsUpdated,
		NonUpdatedMetrics: FindDifference(metricsToBeUpdated, metricsUpdated),
	}
	return response, nil
}

func (s *Service) GetReconciliationData(ctx context.Context, request *dto.GetReconciliationRequest) (response *dto.GetReconciliationResponse, err error) {
	data := dao.KiranaBazarReconciliation{}
	_, err = s.repository.Find(map[string]interface{}{
		"order_id": request.OrderID,
	}, &data)
	if err != nil {
		return nil, err
	}

	orderData := dto.KiranaBazarReconciliation{
		OrderID:                   data.OrderID,
		OMS:                       data.OMS,
		DiscountValue:             data.DiscountValue,
		GrossValue:                data.GrossValue,
		OrderValue:                data.OrderValue,
		OrderPlaced:               data.OrderPlaced,
		OrderConfirmed:            data.OrderConfirmed,
		OrderUpdated:              data.OrderUpdated,
		OrderDelivered:            data.OrderDelivered,
		OrderShipmentCreated:      data.OrderShipmentCreated,
		OrderReturned:             data.OrderReturned,
		OrderCancelled:            data.OrderCancelled,
		OrderDispatched:           data.OrderDispatched,
		OrderOfd:                  data.OrderOfd,
		ExpectedDeliveryTimestamp: data.ExpectedDeliveryTimestamp,
		CreatedAt:                 data.CreatedAt,
		UpdatedAt:                 data.UpdatedAt,
	}

	response = &dto.GetReconciliationResponse{
		Data: orderData,
	}

	return response, nil
}
