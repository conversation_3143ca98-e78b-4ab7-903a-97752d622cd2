package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/sqlRepo"
	orderstatus "kc/internal/ondc/service/orderStatus"
	"time"
)

// getOrder returns the order details for particular orderID
func getOrder(repository *sqlRepo.Repository, orderID string) (orderData dao.KiranaBazarOrder, orderDetails dao.KiranaBazarOrderDetail, err error) {
	if orderID == "" {
		err = errors.New("no order available for given orderID")
		return
	}
	kod := dao.KiranaBazarOrderDetail{}
	_, err = repository.Find(map[string]interface{}{
		"order_id": orderID,
	}, &kod)

	if err != nil {
		return
	}

	ko := dao.KiranaBazarOrder{}

	_, err = repository.Find(map[string]interface{}{
		"id": orderID,
	}, &ko)

	if err != nil {
		return
	}
	return ko, kod, nil
}

// getStatusMessage returns the orderID from the object stored for ONDC
func getStatusMessage(orderData dao.KiranaBazarOrder, orderDetails dao.KiranaBazarOrderDetail) (*dto.StatusMessage, error) {
	orderDetailss := dao.KiranaBazarOrderDetails{}
	err := json.Unmarshal(orderDetails.OrderDetails, &orderDetailss)
	if err != nil {
		return nil, err
	}
	orderID := orderDetailss.ONDCOrderID
	if orderID == "" {
		err = errors.New("order id not valid")
		return nil, err
	}
	return &dto.StatusMessage{OrderID: &orderID}, nil
}

// Status is responsible to get Status
func (s *Service) Status(ctx context.Context, req *dto.AppStatusReq) (*dto.AppStatusResp, error) {

	orderData, orderDetails, err := getOrder(s.repository, req.Data.OrderID)
	if err != nil {
		return nil, err
	}
	transactionID := orderData.TransactionID
	msgID := getMessageID()

	identifier := fmt.Sprintf("%s%s", *transactionID, msgID)

	statusRequestContext, err := getContext(&req.Meta.Context, STATUS, msgID)
	if err != nil {
		return nil, err
	}

	statusMessage, err := getStatusMessage(orderData, orderDetails)
	if err != nil {
		return nil, err
	}

	statusRequest := &dto.StatusRequest{
		Context: statusRequestContext,
		Message: statusMessage,
	}

	return s.handleONDCStatusRequest(ctx, *statusRequest, identifier, *transactionID)

}

func (s *Service) handleONDCStatusRequest(ctx context.Context, statusRequest dto.StatusRequest, identifier, transactionID string) (*dto.AppStatusResp, error) {

	adjustedReqJSON, err := json.Marshal(statusRequest)
	if err != nil {
		logger.Error(ctx, "Marshal adjusted request failed: %v", err)
		return nil, err
	}

	fmt.Println("STATUS req is", string(adjustedReqJSON))

	resp, err := s.syncingONDCRequest(ctx, adjustedReqJSON, identifier, STATUS)
	if err != nil {
		return nil, err
	}

	redisResp, ok := resp.(string)
	if !ok {
		logger.Error(ctx, "not able to typecast the redis resp")
		return nil, fmt.Errorf("not able to typecast the redis resp")
	}
	appSelectResp := &dto.AppStatusResp{}
	err = json.Unmarshal([]byte(redisResp), appSelectResp)
	if err != nil {
		logger.Error(ctx, "not able to unmarshal the redis resp, err is %s", err.Error())
		return nil, err
	}
	return appSelectResp, nil
}

func getKCOrderID(ondcOrderID string, repository *sqlRepo.Repository) (*int64, error) {
	orderDetails := dao.KiranaBazarOrderDetail{}
	_, err := repository.Find(map[string]interface{}{
		"ondc_order_id": ondcOrderID,
	}, &orderDetails)
	if err != nil {
		return nil, err
	}

	return orderDetails.OrderID, nil
}

func (s *Service) OnStatus(ctx context.Context, req *dto.OnStatusRequest) error {
	key := fmt.Sprintf("%s%s", *req.Context.TransactionID, *req.Context.MessageID)
	fmt.Println("SETTING IN STATUS", key)
	var invoiceLink string
	for _, doc := range req.Message.Order.Documents {
		if doc.Label == "Invoice" {
			invoiceLink = doc.URL
		}
	}
	resp := dto.AppStatusResp{
		Data: dto.AppStatusData{
			OrderID:     req.Message.Order.ID,
			OrderStatus: req.Message.Order.State,
			Invoice:     invoiceLink,
		},
	}
	kcOrderID, err := getKCOrderID(req.Message.Order.ID, s.repository)
	if err != nil {
		return err
	}

	orderStatuses := orderstatus.MapOrderStatus(req.Message.Order.State, "", orderstatus.OrderStatusResponse{})
	_, _, err = s.repository.Update(&dao.KiranaBazarOrder{
		ID: kcOrderID,
	}, &dao.KiranaBazarOrder{
		OrderStatus:      &req.Message.Order.State,
		UpdatedAt:        time.Now(),
		DeliveryStatus:   orderStatuses.ShipmentStatus,
		DisplayStatus:    orderStatuses.DisplayStatus,
		ProcessingStatus: orderStatuses.ProcessingStatus,
	})

	if err != nil {
		return err
	}

	if req.Error != nil && req.Error.Code != nil {
		resp.Error = dto.AppResponseError{
			Code:        req.Error.Code,
			Message:     &req.Error.Message,
			Description: &req.Error.Path,
			Type:        &req.Error.Type,
		}
	}
	_, err = s.Cache.Create(ctx, key, resp)
	if err != nil {
		return err
	}
	return nil
}
