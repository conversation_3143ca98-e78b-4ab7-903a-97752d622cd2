package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/loyalty"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"time"
)

func (s *Service) GetSellers(ctx context.Context, request *dto.GetSellersRequest) (response *dto.GetSellersResponse, err error) {
	query := fmt.Sprintf(`SELECT * FROM kiranabazar_sellers ks;`)
	if request.Data.Seller != "" {
		query += fmt.Sprintf(" AND ks.code = '%s' ", request.Data.Seller)
	}
	sellers := []dao.KiranaBazarSeller{}
	fmt.Println("query is", query)
	_, err = s.repository.CustomQuery(&sellers, query)
	if err != nil {
		fmt.Println("error in fetching the sellers from db")
		return nil, err
	}
	sellersData := []dto.KiranaBazarSellersWithMetaProps{}
	for _, seller := range sellers {
		invoiceDetails := dao.KiranaBazarSellerInvoiceDetails{}
		shippingDetails := dao.KiranaBazarSellerShippingDetails{}
		sellerMeta := dto.Seller{}
		if seller.InvoiceDetails != nil {
			err = json.Unmarshal(seller.InvoiceDetails, &invoiceDetails)
			if err != nil {
				fmt.Println("error in unmarshalling the invoice details", string(seller.InvoiceDetails))
				return nil, err
			}
		}
		if seller.ShippingDetails != nil {
			err = json.Unmarshal(seller.ShippingDetails, &shippingDetails)
			if err != nil {
				return nil, err
			}
		}

		if seller.Meta != nil {
			err = json.Unmarshal(seller.Meta, &sellerMeta)
			if err != nil {
				fmt.Println("error in unmarshalling the meta details", string(seller.Meta))
				return nil, err
			}
		}

		sellersData = append(sellersData, dto.KiranaBazarSellersWithMetaProps{
			ID:             seller.ID,
			Code:           seller.Code,
			Name:           seller.Name,
			Source:         seller.Source,
			CreatedAt:      seller.CreatedAt,
			UpdatedAt:      *seller.UpdatedAt,
			UpdatedBy:      seller.UpdatedBy,
			Logo:           sellerMeta.Logo,
			PrimaryColor:   sellerMeta.PrimaryColor,
			BulkOrderForm:  sellerMeta.BulkOrderForm,
			IsKcFullFilled: sellerMeta.KcFullFilled,
			BanneImageUrls: sellerMeta.BannerImageUrls,
			InvoiceDetails: dto.KiranaBazarSellerInvoiceDetails{
				Email:             invoiceDetails.Email,
				Phone:             invoiceDetails.Phone,
				State:             invoiceDetails.State,
				Pincode:           invoiceDetails.Pincode,
				CinNumber:         invoiceDetails.CINNumber,
				GstNumber:         invoiceDetails.GSTNumber,
				LegalName:         invoiceDetails.LegalName,
				PanNumber:         invoiceDetails.PANNumber,
				VendorCode:        invoiceDetails.VendorCode,
				DisplayName:       invoiceDetails.DisplayName,
				RegisteredAddress: invoiceDetails.RegisteredAddress,
			},
			ShippingDetails: dto.KiranaBazarSellerShippingDetails{
				City:       shippingDetails.City,
				Email:      shippingDetails.Email,
				Phone:      shippingDetails.Phone,
				State:      shippingDetails.State,
				Pincode:    shippingDetails.Pincode,
				Address1:   shippingDetails.Address1,
				Address2:   shippingDetails.Address2,
				VendorCode: shippingDetails.VendorCode,
				VendorName: shippingDetails.VendorName,
			},
			IsActive: true,
		})
	}
	response = &dto.GetSellersResponse{
		Data: struct {
			Sellers []dto.KiranaBazarSellersWithMetaProps `json:"sellers"`
		}{
			Sellers: sellersData,
		},
	}
	return
}

func (s *Service) GetSellerDetails(ctx context.Context, request *dto.GetSellerDetailsRequest) (response *dto.GetSellerDetailsResponse, err error) {
	seller := dao.KiranaBazarSeller{}
	_, err = s.repository.CustomQuery(&seller, fmt.Sprintf(`SELECT * FROM kiranabazar_sellers ks WHERE ks.code = '%s'`, request.Data.Seller))
	if err != nil {
		return nil, err
	}
	invoiceDetails := dao.KiranaBazarSellerInvoiceDetails{}
	shippingDetails := dao.KiranaBazarSellerShippingDetails{}
	err = json.Unmarshal(seller.InvoiceDetails, &invoiceDetails)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(seller.ShippingDetails, &shippingDetails)
	if err != nil {
		return nil, err
	}
	response = &dto.GetSellerDetailsResponse{
		Data: struct {
			Seller dto.KiranaBazarSellers `json:"seller"`
		}{
			Seller: dto.KiranaBazarSellers{
				ID:        seller.ID,
				Code:      seller.Code,
				Name:      seller.Name,
				Source:    seller.Source,
				CreatedAt: seller.CreatedAt,
				UpdatedAt: *seller.UpdatedAt,
				UpdatedBy: seller.UpdatedBy,
				Meta:      seller.Meta,
				InvoiceDetails: dto.KiranaBazarSellerInvoiceDetails{
					Email:             invoiceDetails.Email,
					Phone:             invoiceDetails.Phone,
					State:             invoiceDetails.State,
					Pincode:           invoiceDetails.Pincode,
					CinNumber:         invoiceDetails.CINNumber,
					GstNumber:         invoiceDetails.GSTNumber,
					LegalName:         invoiceDetails.LegalName,
					PanNumber:         invoiceDetails.PANNumber,
					VendorCode:        invoiceDetails.VendorCode,
					DisplayName:       invoiceDetails.DisplayName,
					RegisteredAddress: invoiceDetails.RegisteredAddress,
				},
				ShippingDetails: dto.KiranaBazarSellerShippingDetails{
					City:       shippingDetails.City,
					Email:      shippingDetails.Email,
					Phone:      shippingDetails.Phone,
					State:      shippingDetails.State,
					Pincode:    shippingDetails.Pincode,
					Address1:   shippingDetails.Address1,
					Address2:   shippingDetails.Address2,
					VendorCode: shippingDetails.VendorCode,
					VendorName: shippingDetails.VendorName,
				},
				IsActive: true,
			},
		},
	}
	return
}

func (s *Service) UpdateSellerDetails(ctx context.Context, request *dto.UpdateSellerDetailsRequest) (response *dto.UpdateSellerDetailsResponse, err error) {
	seller := dao.KiranaBazarSeller{}
	_, err = s.repository.CustomQuery(&seller, fmt.Sprintf(`SELECT * FROM kiranabazar_sellers ks WHERE ks.code = '%s'`, request.Data.Code))
	if err != nil {
		return nil, err
	}
	invoiceDetails := dao.KiranaBazarSellerInvoiceDetails{}
	shippingDetails := dao.KiranaBazarSellerShippingDetails{}
	brandMetaFromDB := dto.Seller{}

	err = json.Unmarshal(seller.InvoiceDetails, &invoiceDetails)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(seller.ShippingDetails, &shippingDetails)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(seller.Meta, &brandMetaFromDB)
	if err != nil {
		return nil, err
	}

	brandMeta, err := s.CreateBrandMeta(ctx, &dto.UpdateBrandRequest{
		SellerCode:      request.Data.Code,
		Logo:            brandMetaFromDB.Logo,
		IsKcFullfilled:  request.Data.IsKcFullFilled,
		BulkOrderForm:   request.Data.BulkOrderForm,
		BannerImageUrls: request.Data.BanneImageUrls,
		PrimaryColor:    request.Data.PrimaryColor,
		UpdatedBy:       request.Data.UpdatedBy,
		RedeemId:        brandMetaFromDB.RedeemCashbackSourceId,
		CashbackId:      brandMetaFromDB.AddCashbackSourceId,
	})

	if err != nil {
		return nil, err
	}

	// brandMeta to json
	brandMetaByte, err := json.Marshal(brandMeta)
	if err != nil {
		return nil, err
	}

	// check which are updatable as in non empty only update those

	if request.Data.InvoiceDetails.Email != "" {
		invoiceDetails.Email = request.Data.InvoiceDetails.Email
	}
	if request.Data.InvoiceDetails.Phone != "" {
		invoiceDetails.Phone = request.Data.InvoiceDetails.Phone
	}
	if request.Data.InvoiceDetails.State != "" {
		invoiceDetails.State = request.Data.InvoiceDetails.State
	}
	if request.Data.InvoiceDetails.Pincode != "" {
		invoiceDetails.Pincode = request.Data.InvoiceDetails.Pincode
	}
	if request.Data.InvoiceDetails.CinNumber != "" {
		invoiceDetails.CINNumber = request.Data.InvoiceDetails.CinNumber
	}
	if request.Data.InvoiceDetails.GstNumber != "" {
		invoiceDetails.GSTNumber = request.Data.InvoiceDetails.GstNumber
	}
	if request.Data.InvoiceDetails.LegalName != "" {
		invoiceDetails.LegalName = request.Data.InvoiceDetails.LegalName
	}
	if request.Data.InvoiceDetails.PanNumber != "" {
		invoiceDetails.PANNumber = request.Data.InvoiceDetails.PanNumber
	}
	if request.Data.InvoiceDetails.VendorCode != "" {
		invoiceDetails.VendorCode = request.Data.InvoiceDetails.VendorCode
	}
	if request.Data.InvoiceDetails.DisplayName != "" {
		invoiceDetails.DisplayName = request.Data.InvoiceDetails.DisplayName
	}
	if request.Data.InvoiceDetails.RegisteredAddress != "" {
		invoiceDetails.RegisteredAddress = request.Data.InvoiceDetails.RegisteredAddress
	}

	if request.Data.ShippingDetails.City != "" {
		shippingDetails.City = request.Data.ShippingDetails.City
	}
	if request.Data.ShippingDetails.Email != "" {
		shippingDetails.Email = request.Data.ShippingDetails.Email
	}
	if request.Data.ShippingDetails.Phone != "" {
		shippingDetails.Phone = request.Data.ShippingDetails.Phone
	}
	if request.Data.ShippingDetails.State != "" {
		shippingDetails.State = request.Data.ShippingDetails.State
	}
	if request.Data.ShippingDetails.Pincode != "" {
		shippingDetails.Pincode = request.Data.ShippingDetails.Pincode
	}
	if request.Data.ShippingDetails.Address1 != "" {
		shippingDetails.Address1 = request.Data.ShippingDetails.Address1
	}
	if request.Data.ShippingDetails.Address2 != "" {
		shippingDetails.Address2 = request.Data.ShippingDetails.Address2
	}
	if request.Data.ShippingDetails.VendorCode != "" {
		shippingDetails.VendorCode = request.Data.ShippingDetails.VendorCode
	}
	if request.Data.ShippingDetails.VendorName != "" {
		shippingDetails.VendorName = request.Data.ShippingDetails.VendorName
	}

	if request.Data.UpdatedBy != "" {
		seller.UpdatedBy = request.Data.UpdatedBy
	}

	invoiceDetailsByte, err := json.Marshal(invoiceDetails)
	if err != nil {
		return nil, err
	}
	shippingDetailsByte, err := json.Marshal(shippingDetails)
	if err != nil {
		return nil, err
	}

	updatedAt := time.Now().UnixMilli()
	_, _, err = s.repository.Update(dao.KiranaBazarSeller{
		Code: request.Data.Code,
	}, dao.KiranaBazarSeller{
		Name:            request.Data.Name,
		Source:          seller.Source,
		UpdatedBy:       seller.UpdatedBy,
		UpdatedAt:       &updatedAt,
		Meta:            brandMetaByte,
		InvoiceDetails:  invoiceDetailsByte,
		ShippingDetails: shippingDetailsByte,
	})
	if err != nil {
		return nil, err
	}
	response = &dto.UpdateSellerDetailsResponse{
		Data: struct {
			Seller dto.KiranaBazarSellers `json:"seller"`
		}{
			Seller: dto.KiranaBazarSellers{
				ID:        seller.ID,
				Code:      seller.Code,
				Name:      seller.Name,
				Source:    seller.Source,
				CreatedAt: seller.CreatedAt,
				UpdatedAt: *seller.UpdatedAt,
				UpdatedBy: seller.UpdatedBy,
				Meta:      brandMetaByte,
				InvoiceDetails: dto.KiranaBazarSellerInvoiceDetails{
					Email:             invoiceDetails.Email,
					Phone:             invoiceDetails.Phone,
					State:             invoiceDetails.State,
					Pincode:           invoiceDetails.Pincode,
					CinNumber:         invoiceDetails.CINNumber,
					GstNumber:         invoiceDetails.GSTNumber,
					LegalName:         invoiceDetails.LegalName,
					PanNumber:         invoiceDetails.PANNumber,
					VendorCode:        invoiceDetails.VendorCode,
					DisplayName:       invoiceDetails.DisplayName,
					RegisteredAddress: invoiceDetails.RegisteredAddress,
				},
				ShippingDetails: dto.KiranaBazarSellerShippingDetails{
					City:       shippingDetails.City,
					Email:      shippingDetails.Email,
					Phone:      shippingDetails.Phone,
					State:      shippingDetails.State,
					Pincode:    shippingDetails.Pincode,
					Address1:   shippingDetails.Address1,
					Address2:   shippingDetails.Address2,
					VendorCode: shippingDetails.VendorCode,
					VendorName: shippingDetails.VendorName,
				},
				IsActive: true,
			},
		},
	}
	return
}

func (s *Service) AddSellerDetails(ctx context.Context, request *dto.AddSellerRequest) (response *dto.AddSellerResponse, err error) {
	invoiceData := dao.KiranaBazarSellerInvoiceDetails{
		Email:             request.Data.Seller.InvoiceDetails.Email,
		Phone:             request.Data.Seller.InvoiceDetails.Phone,
		State:             request.Data.Seller.InvoiceDetails.State,
		Pincode:           request.Data.Seller.InvoiceDetails.Pincode,
		CINNumber:         request.Data.Seller.InvoiceDetails.CinNumber,
		GSTNumber:         request.Data.Seller.InvoiceDetails.GstNumber,
		LegalName:         request.Data.Seller.InvoiceDetails.LegalName,
		PANNumber:         request.Data.Seller.InvoiceDetails.PanNumber,
		VendorCode:        request.Data.Seller.InvoiceDetails.VendorCode,
		DisplayName:       request.Data.Seller.InvoiceDetails.DisplayName,
		RegisteredAddress: request.Data.Seller.InvoiceDetails.RegisteredAddress,
	}
	shippingData := dao.KiranaBazarSellerShippingDetails{
		City:       request.Data.Seller.ShippingDetails.City,
		Email:      request.Data.Seller.ShippingDetails.Email,
		Phone:      request.Data.Seller.ShippingDetails.Phone,
		State:      request.Data.Seller.ShippingDetails.State,
		Pincode:    request.Data.Seller.ShippingDetails.Pincode,
		Address1:   request.Data.Seller.ShippingDetails.Address1,
		Address2:   request.Data.Seller.ShippingDetails.Address2,
		VendorCode: request.Data.Seller.ShippingDetails.VendorCode,
		VendorName: request.Data.Seller.ShippingDetails.VendorName,
	}
	invoiceDetailsByte, err := json.Marshal(invoiceData)
	if err != nil {
		fmt.Println("error in marshalling the invoice details", err)
		return nil, err
	}
	shippingDetailsByte, err := json.Marshal(shippingData)
	if err != nil {
		fmt.Println("error in marshalling the shipping details", err)
		return nil, err
	}

	requestObject := map[string]interface{}{
		"brand":     request.Data.Seller.Code,
		"thumbnail": "https://d2rstorage2.blob.core.windows.net/widget/February/11/1442d342-a7cc-43bb-8bde-11f4c4dc2c2b/1739277797700.webp",
	}

	loyaltyByt, err := loyalty.CallLoyaltyAPI("CREATE_TRANSACTION_SOURCE", requestObject, nil)

	if err != nil {
		return nil, err
	}

	var cashBackActons dto.CashBackActions

	err = json.Unmarshal(loyaltyByt, &cashBackActons)

	if err != nil {
		return nil, err
	}

	brandMeta, err := s.CreateBrandMeta(ctx, &dto.UpdateBrandRequest{
		SellerCode:      request.Data.Seller.Code,
		Logo:            request.Data.Seller.Logo,
		IsKcFullfilled:  request.Data.Seller.IsKcFullFilled,
		BulkOrderForm:   request.Data.Seller.BulkOrderForm,
		BannerImageUrls: request.Data.Seller.BanneImageUrls,
		PrimaryColor:    request.Data.Seller.PrimaryColor,
		UpdatedBy:       request.Data.Seller.UpdatedBy,
		CashbackId:      cashBackActons.CashbackID,
		RedeemId:        cashBackActons.RedeemID,
	})

	if err != nil {
		return nil, err
	}

	// brandMeta to json
	brandMetaByte, err := json.Marshal(brandMeta)
	if err != nil {
		return nil, err
	}

	updatedAt := time.Now().UnixMilli()
	_, err = s.repository.Create(&dao.KiranaBazarSeller{
		Code:            request.Data.Seller.Code,
		Name:            request.Data.Seller.Name,
		Source:          request.Data.Seller.Source,
		Meta:            brandMetaByte,
		InvoiceDetails:  invoiceDetailsByte,
		ShippingDetails: shippingDetailsByte,
		CreatedAt:       time.Now().UnixMilli(),
		UpdatedAt:       &updatedAt,
		UpdatedBy:       request.Data.Seller.UpdatedBy,
	})
	if err != nil {
		fmt.Println("error in creating the seller", err)
		return nil, err
	}
	response = &dto.AddSellerResponse{
		Data: struct {
			Seller dto.KiranaBazarSellersWithMetaProps `json:"seller"`
		}{
			Seller: request.Data.Seller,
		},
	}
	return
}

// GetVendorCodes return all the invoice vendor codes created so far
func (s *Service) GetVendorCodes(ctx context.Context) (response *dto.GetVendorCodesResponse, err error) {
	sellers := []dao.KiranaBazarSeller{}
	_, err = s.repository.CustomQuery(&sellers, `SELECT * FROM kiranabazar_sellers ks;`)
	if err != nil {
		return nil, err
	}
	vendorCodes := make([]string, 0)
	for _, seller := range sellers {
		invoiceDetails := seller.GetInvoicingDetails()
		vendorCodes = append(vendorCodes, invoiceDetails.VendorCode)
	}
	response = &dto.GetVendorCodesResponse{
		Data: struct {
			VendorCodes []string `json:"vendor_codes"`
		}{
			VendorCodes: vendorCodes,
		},
	}
	return
}
