package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	awbMasterDTO "kc/internal/ondc/service/awbMaster/dto"

	tplModels "github.com/Kirana-Club/3pl-go/pkg/models"
)

// EkartWebhook accepts the webhook request from Ekart and logs it into the database
func (s *Service) EkartWebhook(ctx context.Context, request *dto.EkartWebhookRequestInterface, accountName string) (any, error) {

	reqJSonString, err := json.Marshal(request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		return nil, err
	}

	_, err = s.repository.Create(&dao.EkartWebhookLogs{
		RequestObject: reqJSonString,
		CreatedAt:     time.Now(),
		AccountName:   accountName,
	})
	if err != nil {
		logger.Error(ctx, "%s", err.<PERSON><PERSON>r())
		return nil, err
	}

	// typecasting the request to EkartWebhookRequest
	ekartRequest := dto.EkartWebhookRequest{}
	err = json.Unmarshal(reqJSonString, &ekartRequest)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		return nil, err
	}

	type OrderID struct {
		OrderID uint64 `json:"order_id"`
	}
	orderID := OrderID{}
	_, err = s.repository.CustomQuery(&orderID, fmt.Sprintf(`select kam.order_id from kiranabazar_awb_master kam where kam.awb_number = '%s';`, ekartRequest.EntityID))
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		return nil, err
	}
	if orderID.OrderID == 0 {
		logger.Error(ctx, "order id not found for awb number %s", ekartRequest.EntityID)
		return "not out order", nil
	}

	go s.AWBMaster.EnqueueAWBSync(ekartRequest.EntityID, "Ekart")

	// create a awb scan for data tracking
	ekartRequest.OrderID = orderID.OrderID
	logData, err := json.Marshal(ekartRequest)
	if err != nil {
		return nil, err
	}
	s.AWBMaster.CreateAWBScans(context.Background(), string(logData))

	refID := fmt.Sprintf("%d", orderID.OrderID)
	courier := "Ekart"
	TRUE := true
	updatedBy := "EKART_WEBHOOK"

	// update the awb master with the latest status
	lastStatusUpdated, _ := ConvertToEpochMillis(ekartRequest.EventDate)
	s.AWBMaster.Create(context.Background(), awbMasterDTO.CreateAWBRequest{
		AWBNumber:           &ekartRequest.EntityID,
		ReferenceID:         &refID,
		OrderID:             &ekartRequest.OrderID,
		Courier:             &courier,
		Status:              &ekartRequest.Event,
		IsActive:            &TRUE,
		LastStatusUpdatedAt: &lastStatusUpdated,
		AccountName:         &accountName,
		UpdatedBy:           &updatedBy,
		NSLCode:             &ekartRequest.Event,
		StatusType:          &ekartRequest.Event,
		Instructions:        &ekartRequest.Event,
		StatusCode:          &ekartRequest.Status,
	})

	// // fetchAndUpdate primary webhook for the orderiD
	go s.fetchAndUpdatePrimaryWaybill(ekartRequest.OrderID, updatedBy)

	return "success", nil
}

func (s *Service) EkartWebhookGet(ctx context.Context, request *dto.EkartWebhookRequest, accountName string) (any, error) {
	reqJSonString, err := json.Marshal(request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		return nil, err
	}

	_, err = s.repository.Create(&dao.EkartWebhookLogs{
		RequestObject: reqJSonString,
		CreatedAt:     time.Now(),
		AccountName:   accountName,
	})

	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		return nil, err
	}

	return "success", nil
}

// PushOrderToDelhivery pushes the order to Delhivery for processing
func (s *Service) PushOrderToEkart(ctx context.Context, orderID string) (*tplModels.ManifestResponse, error) {
	// Fetch order details
	orderDetails, err := GetOrderDetails(s.repository, orderID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch order details: %w", err)
	}

	// Fetch order seller details
	sellerDetails, err := GetSellerInfo(s.repository, orderDetails.Seller)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch seller details: %w", err)
	}

	pickupDetails := sellerDetails.GetShippingDetails()
	vendorName := strings.ReplaceAll(pickupDetails.VendorName, "'", "")

	orderIDInt, err := strconv.ParseInt(orderID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid order ID: %w", err)
	}
	packageDetails, err := GetPackageDetails(s.repository, orderIDInt)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch package details: %w", err)
	}

	packageDimensions := packageDetails.GetPackageDetails()
	packageCount := len(packageDimensions.Dimensions)
	if packageCount > 5 {
		return nil, fmt.Errorf("package count cannot exceed 5, got %d", packageCount)
	}

	payableAmount := orderDetails.GetCodValue()
	paymentMethod := "COD"
	if payableAmount < 1.0 {
		paymentMethod = "Prepaid"
	}

	var CODAmount float64
	if paymentMethod == "COD" {
		CODAmount = payableAmount
	}

	pickupPincode, err := strconv.Atoi(pickupDetails.Pincode)
	if err != nil {
		return nil, fmt.Errorf("invalid pickup pincode: %w", err)
	}

	// Create ManifestRequest
	manifestRequest := &tplModels.ManifestRequest{
		PickupLocation: tplModels.PickupLocation{
			Name:    vendorName,
			Add:     pickupDetails.Address1 + " " + pickupDetails.Address2,
			City:    pickupDetails.City,
			State:   pickupDetails.State,
			Country: "India",
			PinCode: pickupPincode,
			Phone:   pickupDetails.Phone,
		},

		Shipments: make([]tplModels.Shipment, 0),
	}

	for i := range packageCount {
		// Create Shipment for each package
		shipment := tplModels.Shipment{
			// Reciever details
			Add:         orderDetails.ShippingAddress.Line1 + " " + orderDetails.ShippingAddress.Line2,
			Phone:       *orderDetails.ShippingAddress.Phone,
			Name:        *orderDetails.ShippingAddress.Name,
			Pin:         *orderDetails.ShippingAddress.PostalCode,
			Order:       fmt.Sprintf("KC_%s", orderID),
			PaymentMode: paymentMethod,
			City:        *orderDetails.ShippingAddress.District,
			State:       *orderDetails.ShippingAddress.State,
			Country:     "India",

			Waybill:        "",
			MasterID:       "",
			ReturnAdd:      pickupDetails.Address1 + " " + pickupDetails.Address2,
			ReturnPin:      pickupDetails.Pincode,
			ReturnCity:     pickupDetails.City,
			ReturnState:    pickupDetails.State,
			ReturnCountry:  "India",
			ReturnPhone:    pickupDetails.Phone,
			CODAmount:      CODAmount,
			MPSAmount:      payableAmount,
			TotalAmount:    orderDetails.GetOrderValue(),
			Weight:         packageDimensions.Dimensions[i].Weight,
			Quantity:       "1",
			ProductsDesc:   fmt.Sprintf("%s %d", vendorName, i),
			ShipmentHeight: packageDimensions.Dimensions[i].Height,
			ShipmentLength: packageDimensions.Dimensions[i].Length,
			ShipmentWidth:  packageDimensions.Dimensions[i].Breadth,
			ShippingMode:   "Surface",

			// Seller details
			SellerName: vendorName,
			SellerAdd:  pickupDetails.Address1 + " " + pickupDetails.Address2,
			SellerInv:  fmt.Sprintf("KC_%s", orderID),
		}

		// Append the shipment to the manifest request
		manifestRequest.Shipments = append(manifestRequest.Shipments, shipment)
	}

	// Create a session with TPLService
	session, err := s.TPLService.CreateSession(ctx, "ekart", "kiranaclub")
	if err != nil {
		return nil, fmt.Errorf("failed to create session with TPLService: %w", err)
	}

	// Push the manifest request to TPLService
	manifestResponse, err := session.CreateShipment(ctx, manifestRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to create shipment: %w", err)
	}

	return manifestResponse, nil
}
