// Package couriers provides thread-safe management of courier information
// for logistics and delivery systems.
package couriers

import (
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"sync"
)

var CSR CourierService

// Courier represents a delivery service provider with unique identification
// and branding information.
type Courier struct {
	ID          string `json:"id" validate:"required"`
	CourierName string `json:"courier_name" validate:"required"`
	Image       string `json:"image"`
}

// CourierService handles all courier-related operations with thread-safety.
type CourierService struct {
	mu           sync.RWMutex
	allCouriers  []Courier
	idMap        map[string]Courier
	courierNames map[string]Courier
}

// ValidationError represents errors that occur during courier data validation.
type ValidationError struct {
	Field   string
	Message string
}

func (e *ValidationError) Error() string {
	return fmt.Sprintf("validation error on field %s: %s", e.Field, e.Message)
}

// NewCourierService creates and initializes a new CourierService instance.
func NewCourierService() error {
	CSR = CourierService{
		allCouriers:  make([]Courier, 0),
		idMap:        make(map[string]Courier),
		courierNames: make(map[string]Courier),
	}
	var ShipwayRawCourierData []Courier
	var EasyEcomRawCourierData []Courier

	err := json.Unmarshal([]byte(shipwayCourierData), &ShipwayRawCourierData)
	if err != nil {
		return err
	}

	err = json.Unmarshal([]byte(easyEcomCourierMapping), &EasyEcomRawCourierData)
	if err != nil {
		return err
	}
	for _, c := range ShipwayRawCourierData {
		CSR.AddCourier(c)
	}
	for _, c := range EasyEcomRawCourierData {
		CSR.AddCourier(c)
	}

	return nil
}

// validateCourier checks if all required fields are present and valid.
func (c *Courier) validate() error {
	if c.ID == "" {
		return &ValidationError{Field: "ID", Message: "cannot be empty"}
	}
	if c.CourierName == "" {
		return &ValidationError{Field: "CourierName", Message: "cannot be empty"}
	}
	// Add more validation as needed (e.g., URL format checking for Image)
	return nil
}

// AddCourier adds a new courier to the service.
// It accepts either a Courier struct or a JSON-compatible map/struct.
func (cs *CourierService) AddCourier(nc interface{}) error {
	var newCourier Courier

	switch v := nc.(type) {
	case Courier:
		newCourier = v
	default:
		byt, err := json.Marshal(nc)
		if err != nil {
			return fmt.Errorf("failed to marshal courier data: %w", err)
		}
		if err := json.Unmarshal(byt, &newCourier); err != nil {
			return fmt.Errorf("failed to unmarshal courier data: %w", err)
		}
	}

	if err := newCourier.validate(); err != nil {
		return fmt.Errorf("invalid courier data: %w", err)
	}

	cs.mu.Lock()
	defer cs.mu.Unlock()

	if _, exists := cs.idMap[newCourier.ID]; !exists {
		// adding allCouriers only for shipway couriers for easyecom dont add
		cs.idMap[newCourier.ID] = newCourier
		cs.allCouriers = append(cs.allCouriers, newCourier)
	}
	cs.courierNames[newCourier.CourierName] = newCourier

	return nil
}

// GetAllCouriers returns a copy of all registered couriers.
func GetAllCouriers() []Courier {
	cs := &CSR
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	// Return a copy to prevent external modifications
	couriersCopy := make([]Courier, len(cs.allCouriers))
	copy(couriersCopy, cs.allCouriers)
	return couriersCopy
}

// GetCourierByID retrieves a courier by their ID.
func (cs *CourierService) GetCourierByID(id string) (Courier, error) {
	if id == "" {
		return Courier{}, &ValidationError{Field: "ID", Message: "cannot be empty"}
	}

	cs.mu.RLock()
	defer cs.mu.RUnlock()

	courier, exists := cs.idMap[id]
	if !exists {
		return Courier{}, fmt.Errorf("no courier found with ID %s", id)
	}
	return courier, nil
}

func GetActualCourier(name *string) (Courier, error) {
	cs := &CSR
	cr, err := GetCourierByName(name)
	if err != nil {
		return cr, err
	}
	return cs.GetCourierByID(cr.ID)
}

func GetActualCourierName(name *string) string {
	if name == nil {
		return ""
	}
	if *name == "" {
		return ""
	}
	courier, err := GetActualCourier(name)
	if err != nil {
		return *name
	}
	return courier.CourierName
}

// GetCourierByName retrieves a courier by their name.
func GetCourierByName(name *string) (Courier, error) {
	cs := &CSR
	if name == nil {
		slack.SendSlackMessage("couriern name cannot be empty")
		return Courier{}, &ValidationError{Field: "CourierName", Message: "cannot be empty"}
	}

	cs.mu.RLock()
	defer cs.mu.RUnlock()

	courier, exists := cs.courierNames[*name]
	if !exists {
		slack.SendSlackMessage(fmt.Sprintf("not able to find mapping the courier = %s", *name))
		return Courier{}, fmt.Errorf("no courier found with name %s", *name)
	}
	return courier, nil
}

// RemoveCourier removes a courier by their ID.
func (cs *CourierService) RemoveCourier(id string) error {
	if id == "" {
		return &ValidationError{Field: "ID", Message: "cannot be empty"}
	}

	cs.mu.Lock()
	defer cs.mu.Unlock()

	courier, exists := cs.idMap[id]
	if !exists {
		return fmt.Errorf("no courier found with ID %s", id)
	}

	// Remove from maps
	delete(cs.idMap, id)
	delete(cs.courierNames, courier.CourierName)

	// Remove from slice
	for i, c := range cs.allCouriers {
		if c.ID == id {
			cs.allCouriers = append(cs.allCouriers[:i], cs.allCouriers[i+1:]...)
			break
		}
	}

	return nil
}
