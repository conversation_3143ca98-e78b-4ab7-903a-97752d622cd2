package service

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/products"
	"kc/internal/ondc/utils"
	"math"
	"time"

	"github.com/google/uuid"
	"github.com/mixpanel/mixpanel-go"
	"github.com/surajJha/go-profanity-hindi"
	"gorm.io/datatypes"
)

func (s *Service) GetProductReviews(ctx context.Context, request *dto.GetProductsReviewsRequest) (response *dto.GetProductReviewsResponse, err error) {
	reviewData := []dao.ProductReviews{}
	if request.Data.ProductID == nil {
		return
	}
	limit := request.Meta.Limit
	if limit == 0 {
		limit = 5
	}
	getProductReviewsQuery := fmt.Sprintf(`select kpr.id, kpr.user_id, kpr.product_id, kpr.star_rating, kpr.review_text, kprm.review_media, kpr.created_at, kpr.updated_at, kpr.is_visible,
											 kpr.relevancy, u.name, u.city, u.state from kiranabazar_product_reviews kpr left join kiranabazar_product_reviews_media kprm on kpr.id = kprm.review_id 
											 left join users u on kpr.user_id = u.user_id where kpr.product_id = %d and kpr.is_visible = true and kpr.star_rating > 3 order by created_at desc
											 limit %d offset %d;`, *request.Data.ProductID, limit, request.Meta.Offset)
	_, err = s.repository.CustomQuery(&reviewData, getProductReviewsQuery)
	if err != nil {
		return
	}

	productReviews := []dto.UserReview{}
	for _, review := range reviewData {
		reviewResponse := &dto.UserReview{
			Type:     36,
			UserName: review.UserName,
			Location: dto.ReviewerLocation{
				City:  review.UserCity,
				State: review.UserState,
			},
			PostDate:   convertToHindiDate(review.CreatedAt),
			ReviewText: review.ReviewText,
			Media:      review.ReviewMedia,
			ReviewID:   review.ReviewID,
		}
		starRating := int8(review.StarRating)
		if starRating != 0 {
			reviewResponse.Rating = &starRating
		}

		if review.ReviewText != "" {
			productReviews = append(productReviews, *reviewResponse)
		}
	}

	response = &dto.GetProductReviewsResponse{
		Data: productReviews,
	}
	return
}

func (s *Service) AddMultipleProductsReviews(ctx context.Context, request *dto.AddProductsReviewRequest) (response *dto.AddProductReviewResponse, err error) {
	if len(request.Data) == 0 {
		err = fmt.Errorf("no reviews to add")
		return
	}

	for _, review := range request.Data {
		review.UserID = request.UserID
		review.ReviewSource = "BottomSheet"
		addRequest := &dto.AddProductReviewRequest{
			Data:   review,
			Meta:   request.Meta,
			UserID: request.UserID,
		}
		_, err = s.AddProductReview(ctx, addRequest)
		if err != nil {
			return
		}
	}

	response = &dto.AddProductReviewResponse{
		Data: dto.AddProductReviewStatus{
			Status: "ok",
		},
	}
	return
}

func (s *Service) AddProductReview(ctx context.Context, request *dto.AddProductReviewRequest) (response *dto.AddProductReviewResponse, err error) {
	reviewID := uuid.New().String()
	isVisibleDefault := false
	userId := ""
	if request.UserID != "" {
		userId = request.UserID
	}
	if request.Data.UserID != "" {
		userId = request.Data.UserID
	}
	if userId == "" {
		err = fmt.Errorf("user_id is required")
		return
	}

	// check here if the product is present in the delivered orders of this user
	type UserOrdersData struct {
		Id            int64          `json:"id"`
		DisplayStatus string         `json:"display_status"`
		OrderDetails  datatypes.JSON `json:"order_details"`
	}
	query := fmt.Sprintf(`select ko.id, ko.display_status, kod.order_details from kiranaclubdb.kiranabazar_orders ko left join kiranaclubdb.kiranabazar_order_details kod on ko.id = kod.order_id where user_id = "%s" and display_status = "DELIVERED"`, userId)
	userOrders := []UserOrdersData{}
	_, err = s.repository.CustomQuery(&userOrders, query)
	if err != nil {
		return
	}

	for _, order := range userOrders {
		orderDetails := dao.KiranaBazarOrderDetails{}
		err = json.Unmarshal(order.OrderDetails, &orderDetails)
		if err != nil {
			return
		}
		for _, item := range orderDetails.Cart {
			if item.ID == fmt.Sprintf("%d", request.Data.ProductID) {
				isVisibleDefault = true
				break
			}
		}
		if isVisibleDefault {
			break
		}
	}

	isReviewTextProfane := profanity.IsStringDirty(request.Data.ReviewText)
	if isReviewTextProfane {
		isVisibleDefault = false
	}

	productReview := &dao.ProductReview{
		ID:         reviewID,
		UserID:     userId,
		ProductID:  request.Data.ProductID,
		StarRating: request.Data.StarRating,
		ReviewText: request.Data.ReviewText,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		IsVisible:  isVisibleDefault,
	}
	_, err = s.repository.Create(productReview)
	if err != nil {
		return
	}

	reviewMediaByte, err := json.Marshal(request.Data.ReviewMedia)
	if err != nil {
		return
	}
	if len(request.Data.ReviewMedia) != 0 {
		productReviewMedia := &dao.ProductReviewsMedia{
			ReviewID:    reviewID,
			ReviewMedia: reviewMediaByte,
		}
		_, err = s.repository.Create(productReviewMedia)
		if err != nil {
			return
		}
	}

	// only update the product ratings if the review is visible
	if isVisibleDefault && request.Data.StarRating > 3 {
		productReviewsUpdateQuery := fmt.Sprintf(`update kiranabazar_products set ratings_sum = ratings_sum + %d, ratings_count = ratings_count + %d where id = %d`,
			request.Data.StarRating, 1, request.Data.ProductID)
		_, err = s.repository.CustomQuery(nil, productReviewsUpdateQuery)
		if err != nil {
			return
		}
	}

	err = processVideoMedia(request.Data.ReviewMedia, "<EMAIL>", reviewID, updateReviewMediaURl)

	response = &dto.AddProductReviewResponse{
		Data: dto.AddProductReviewStatus{
			Status: "ok",
		},
	}

	seller := ""
	productInfo, exists := products.GetProductByID(request.Data.ProductID)
	if exists {
		seller = productInfo.Seller
	}

	mixPanelEventInterface := map[string]interface{}{
		"product_name":    request.Data.ProductName,
		"product_id":      request.Data.ProductID,
		"ordering_module": utils.MakeTitleCase(request.Data.Seller),
		"seller":          seller,
		"rating":          request.Data.StarRating,
		"feedback":        request.Data.ReviewText,
		"media_url":       request.Data.ReviewMedia,
		"review_source":   request.Data.ReviewSource,
	}

	if isReviewTextProfane {
		mixPanelEventInterface["profanity"] = true
	}

	s.Mixpanel.Track(ctx, []*mixpanel.Event{
		s.Mixpanel.NewEvent("Product Rating Submitted", request.UserID, mixPanelEventInterface),
	})

	return
}

func (s *Service) GetUserProductRating(ctx context.Context, request *dto.GetUserProductRatingRequest) (response *dto.GetUserProductRatingResponse, err error) {
	if (request.Data.ProductID == nil) || (request.Data.CategoryID == nil) {
		err = fmt.Errorf("product_id and category_id needed")
		return
	}
	getProductRatingsQuery := fmt.Sprintf(`select kp.id, kp.category_id, kp.ratings_sum, kp.ratings_count from kiranabazar_products kp where kp.id = %d and kp.category_id = %d;`,
		*request.Data.ProductID, *request.Data.CategoryID)
	productRating := &dao.ProductRatings{}
	_, err = s.repository.CustomQuery(productRating, getProductRatingsQuery)
	if err != nil {
		return
	}

	userProductRating := []dao.ProductReview{}
	userProductRatedByUserQuery := fmt.Sprintf(`select * from kiranabazar_product_reviews kpr where kpr.product_id = %d and kpr.user_id = '%s';`,
		*request.Data.ProductID, request.UserID)
	_, err = s.repository.CustomQuery(&userProductRating, userProductRatedByUserQuery)
	isProductRatedByUser := false
	if len(userProductRating) > 0 {
		isProductRatedByUser = true
	}

	var starRating float64
	if productRating.RatingsCount != int64(0) {
		starRating = math.Round((float64(productRating.RatingsSum)/float64(productRating.RatingsCount))*10) / 10
	}

	response = &dto.GetUserProductRatingResponse{
		Data: dto.UserProductRatingResponse{
			UserID:               request.UserID,
			ProductID:            request.Data.ProductID,
			CategoryID:           request.Data.CategoryID,
			StarRating:           starRating,
			RatingsCount:         productRating.RatingsCount,
			IsProductRatedByUser: isProductRatedByUser,
		},
	}
	return
}

func (s *Service) GetTopProductReviewVideos(ctx context.Context, request *dto.GetUserProductRatingRequest) (response *dto.GetTopProductReviewVideosResponse, err error) {
	getProductRatingsQuery := fmt.Sprintf(`select kptr.review_id, kptr.product_id, kpr.star_rating, kpr.review_text, kptr.media_url, kptr.thumbnail_url, kpr.user_id, u.name, u.city, u.state from kiranabazar_product_top_reviews kptr
										left join kiranabazar_product_reviews kpr on kptr.review_id = kpr.id left join kiranabazar_product_reviews_media kprm on kptr.review_id = kprm.review_id 
										left join users u on kpr.user_id = u.user_id where kptr.product_id = %d and kpr.is_visible = true order by kptr.relevancy desc limit 5;`,
		*request.Data.ProductID)
	topProductReviews := []dao.TopProductReviewVideos{}
	_, err = s.repository.CustomQuery(&topProductReviews, getProductRatingsQuery)

	topProductVideoReviews := []dto.TopReviewVideo{}
	// for _, review := range topProductReviews {
	// 	videoReview := &dto.TopReviewVideo{
	// 		MixPanelEventName: "Ordering Top Review Video",
	// 		ComponentHeadline: review.Text,
	// 		Image:             review.ThumbnailURL,
	// 		Navigation: dto.Nav{
	// 			Name:    "OrderingModule",
	// 			NavType: "Redirect to Screen",
	// 			Params: map[string]interface{}{
	// 				"screen":     "MediaViewer",
	// 				"type": "video/mp4",
	// 				"videoUrl":  review.MediaURL,
	// 				"product_id": review.ProductID,
	// 				"review_id":  review.ReviewID,
	// 			},
	// 		},
	// 		State: []string{},
	// 	}
	// 	topProductVideoReviews = append(topProductVideoReviews, *videoReview)
	// }
	for i := 0; i < 5; i++ {
		videoReview := &dto.TopReviewVideo{
			MixPanelEventName: "Ordering Top Review Video",
			ComponentHeadline: "Component Headline",
			Image:             "https://d2rstorage2.blob.core.windows.net/news/November/27/f3ce66e4-fc16-46fe-a363-8b56c810fdec/banner/gif/thumbnail-426a6bf4-100d-4e77-a29e-0d9530b21a69.gif",
			Navigation: shared.Nav{
				Name:    "MediaViewer",
				NavType: "Redirect to Screen",
				Params: map[string]interface{}{
					"type":       "video/mp4",
					"videoUrl":   "https://mediacdn.retailpulse.ai/gcp-transcoded/processed_outputs/raw-user-post/userPost/iFFHCvERHphid86VO9pPg72XMZ53/1732687173868.mp4/manifest.m3u8",
					"product_id": *request.Data.ProductID,
					"review_id":  "1234567890",
				},
			},
			State: []string{},
		}
		topProductVideoReviews = append(topProductVideoReviews, *videoReview)
	}
	response = &dto.GetTopProductReviewVideosResponse{
		Data: topProductVideoReviews,
	}
	return
}

func (s *Service) GetSuggestedProductsWidgets(
	ctx context.Context,
	sourcePtr *string,
	productID int64,
	userID string,
	meta dto.Meta,
) ([]dto.Widget, error) {
	// If source is nil, return empty
	if sourcePtr == nil {
		return nil, nil
	}

	source := *sourcePtr
	seller, exists := brands.GetSellerBySource(source)

	if !exists {
		return nil, fmt.Errorf("seller %s not found", source)
	}

	// Modify the meta to set the Limit
	suggestedProductMeta := meta
	suggestedProductMeta.Limit = 100

	// Prepare the request for suggested products
	suggestedProducts, err := s.GetSuggestedProductsV2(ctx, dto.GetSuggestedProductsRequest{
		Meta:   suggestedProductMeta,
		UserID: userID,
		Data: dto.GetSuggestedProductsData{
			Seller:           seller,
			Source:           source,
			ExcludedProducts: []string{fmt.Sprint(productID)},
		},
	})
	if err != nil || suggestedProducts == nil {
		return nil, err
	}

	// Convert suggested products into a generic interface slice
	items := make([]interface{}, 0, len(suggestedProducts.Data.SuggestedProducts))
	total := 0
	for _, it := range suggestedProducts.Data.SuggestedProducts {
		showRecommendation := true
		it.ShowRecommendedProducts = &showRecommendation
		if it.SizeVariants == nil || len(it.SizeVariants) <= 1 {
			items = append(items, it)
			total++
			if total >= 10 {
				break
			}
		}
	}

	// Generate widgets from suggested products
	suggestedWidgets := getProductsResponse(userID, items, 5, []interface{}{}, "review_products")
	if suggestedWidgets == nil {
		return nil, nil
	}

	// Convert to []dto.Widget
	interfaceArray := suggestedWidgets.([]interface{})
	suggestedWidgetsItems := make([]dto.Widget, 0, len(interfaceArray))
	for _, item := range interfaceArray {
		if item != nil {
			suggestedWidgetsItems = append(suggestedWidgetsItems, item.(dto.Widget))
		}
	}

	return suggestedWidgetsItems, nil
}

func (s *Service) GetProductReviewRatings(ctx context.Context, request *dto.GetProductsReviewsRequest) (response *dto.GetProductReviewRatingResponse, err error) {
	if (request.Data.ProductID == nil) || (request.Data.CategoryID == nil) {
		err = fmt.Errorf("product_id and category_id needed")
		return
	}
	productReviewRequest := &dto.GetProductsReviewsRequest{
		Data: dto.ProductReviewsRequest{
			CategoryID: request.Data.CategoryID,
			ProductID:  request.Data.ProductID,
			Source:     request.Data.Source},
		Meta:   request.Meta,
		UserID: request.UserID}
	productReviews, err := s.GetProductReviews(ctx, productReviewRequest)
	if err != nil {
		return
	}

	productRatingRequest := &dto.GetUserProductRatingRequest{
		Data: dto.UserProductRatingRequest{CategoryID: request.Data.CategoryID,
			ProductID: request.Data.ProductID},
		Meta:   request.Meta,
		UserID: request.UserID}
	productRatings, err := s.GetUserProductRating(ctx, productRatingRequest)
	if err != nil {
		return
	}

	topProductVideoReviews, err := s.GetTopProductReviewVideos(ctx, productRatingRequest)
	if err != nil {
		return
	}

	response = &dto.GetProductReviewRatingResponse{
		Data: dto.ProductReviewRatingResponse{
			ProductReviews:         productReviews.Data,
			ProductRatings:         productRatings.Data,
			TopProductReviewVideos: topProductVideoReviews.Data,
		},
	}
	return
}

func (s *Service) UpdateReviewMedia(ctx context.Context, request *dto.UpdateReviewMediaRequest) (response *dto.AddProductReviewStatus, err error) {
	reviewMediaData := dao.ProductReviewsMedia{}
	_, err = s.repository.Find(map[string]interface{}{
		"review_id": request.JobID,
	}, &reviewMediaData)
	if err != nil {
		return
	}

	unmarshaledMedia := []dao.ReviewMedia{}
	err = json.Unmarshal(reviewMediaData.ReviewMedia, &unmarshaledMedia)
	if err != nil {
		return
	}
	reviewMediaToUpdate := []dao.ReviewMedia{}
	for _, media := range unmarshaledMedia {
		if media.URI == request.URL {
			media.URI = request.StreamingURL
			media.GifURL = request.GifURL
			media.ThumbnailURL = request.ThumbnailURL
			media.JobID = request.JobID
		}
		reviewMediaToUpdate = append(reviewMediaToUpdate, media)
	}

	var marshaledMedia datatypes.JSON
	marshaledMedia, err = json.Marshal(reviewMediaToUpdate)
	if err != nil {
		return
	}
	_, _, err = s.repository.Update(dao.ProductReviewsMedia{
		ReviewID: reviewMediaData.ReviewID,
	}, dao.ProductReviewsMedia{
		ReviewMedia: marshaledMedia,
	})
	if err != nil {
		return
	}
	response = &dto.AddProductReviewStatus{
		Status: "ok",
	}
	return
}

func (s *Service) GetProductReviewRatingsWidgets(ctx context.Context, request *dto.GetProductsReviewsRequest) (response *dto.ProductReviewsWidgets, err error) {
	if (request.Data.ProductID == nil) || (request.Data.CategoryID == nil) {
		err = fmt.Errorf("product_id and category_id needed")
		return
	}
	productRatingRequest := &dto.GetUserProductRatingRequest{
		Data: dto.UserProductRatingRequest{CategoryID: request.Data.CategoryID,
			ProductID: request.Data.ProductID},
		Meta:   request.Meta,
		UserID: request.UserID}
	productRatings, err := s.GetUserProductRating(ctx, productRatingRequest)

	if err != nil {
		return
	}

	widgetsData := []dto.Widget{}

	widgetType35Data := &dto.WidgetType35{
		MixPanelEventName: "Product Ordering Users Review Component",
		ID:                747,
		Heading:           "किराना साथियों द्वारा रिव्यू",
		CreatedBy:         "<EMAIL>",
		Expiry:            int64(time.Now().UnixMilli()) + 8640000000,
		ExpiryTime:        int64(time.Now().UnixMilli()) + 8640000000,
		Type:              35,
		Title:             "किराना साथियों द्वारा रिव्यू",
		Rating:            productRatings.Data.StarRating,
		SubTitle:          fmt.Sprintf("%d किराना साथियों द्वारा रेटिंग", productRatings.Data.RatingsCount),
		WidgetInfo: dto.WidgetInfo{
			WidgetName: "Product Ordering Users Review Component",
		},
		IsActive:    1,
		UpdatedAt:   time.Now().Format("2006-01-02T15:04:05.000Z"),
		VisibleFrom: int64(time.Now().UnixMilli()) + 8640000000,
		ProductId:   *request.Data.ProductID,
		Cta:         nil,
	}

	if !productRatings.Data.IsProductRatedByUser {
		widgetType34Data := &dto.WidgetType34{
			MixpanelEventName: "Clicked on Give Rating",
			MixpanelEventObject: map[string]interface{}{
				"product_id":   *request.Data.ProductID,
				"category_id":  *request.Data.CategoryID,
				"product_name": request.Data.ProductName,
			},
			ID:               746,
			Heading:          "आपको यह प्रोडक्ट कैसा लगा?",
			CreatedBy:        "<EMAIL>",
			Expiry:           int64(time.Now().UnixMilli()) + 8640000000,
			ExpiryTime:       int64(time.Now().UnixMilli()) + 8640000000,
			Type:             34,
			Title:            "आपको यह प्रोडक्ट कैसा लगा?",
			RatingTexts:      starRatingTexts,
			FeedbackSection:  reviewFeedbackSection,
			MediaSection:     mediaSection,
			SubmittedSection: SubmittedSection,
			WidgetInfo: dto.WidgetInfo{
				WidgetName: "Product Ordering Rating Component",
			},
			IsActive:    1,
			UpdatedAt:   time.Now().Format("2006-01-02T15:04:05.000Z"),
			VisibleFrom: int64(time.Now().UnixMilli()) + 8640000000,
			ProductId:   *request.Data.ProductID,
			ProductName: request.Data.ProductName,
			Cta: &dto.Cta{
				Text:              "रेटिंग दे",
				MixpanelEventName: "Clicked on Give Rating",
				PrimaryColor:      utils.StrPtr("#00822B"),
			},
		}
		widgetsData = append(widgetsData, widgetType34Data)
	}

	if productRatings.Data.StarRating > 0 {
		// widgetType35Data.Cta = &dto.Cta{
		// 	Text:              "और देखें",
		// 	MixpanelEventName: "clicked_view_more_product_rating",
		// 	Nav: &dto.Nav{
		// 		Name:    "GenericScreen",
		// 		NavType: "Redirect to Screen",
		// 		Params: map[string]interface{}{
		// 			"product_id": *request.Data.ProductID,
		// 			"screenName": "ordering_module_reviews",
		// 			"body": map[string]interface{}{
		// 				"source":      *request.Data.Source,
		// 				"product_id":  *request.Data.ProductID,
		// 				"category_id": *request.Data.CategoryID,
		// 			},
		// 		},
		// 	},
		// }
		widgetsData = append(widgetsData, widgetType35Data)
	}

	// topProductVideoReviews, err := s.GetTopProductReviewVideos(ctx, productRatingRequest)
	// if err != nil {
	// 	return
	// }
	// widgetType12Data := dto.WidgetType12{
	// 	MixPanelEventName: "Product Review Video",
	// 	CreatedBy:         "<EMAIL>",
	// 	Expiry:            int64(time.Now().UnixMilli()) + 8640000000,
	// 	ExpiryTime:        int64(time.Now().UnixMilli()) + 8640000000,
	// 	Heading:           "किराना साथियों द्वारा शेयर किये गये वीडियोस",
	// 	ID:                0,
	// 	IsStateWise:       false,
	// 	IsActive:          1,
	// 	List:              topProductVideoReviews.Data,
	// 	Resolution:        2,
	// 	SqlID:             3040,
	// 	SubType:           "long",
	// 	Tag:               "",
	// 	Type:              12,
	// 	UpdatedBy:         "<EMAIL>",
	// 	UpdatedAt:         time.Now().Format("2006-01-02T15:04:05.000Z"),
	// 	UpdatedBy2:        "<EMAIL>",
	// 	Versions:          ">=6.1.4",
	// 	VisibilityCount:   0,
	// 	VisibleFrom:       int64(time.Now().UnixMilli()) + 8640000000,
	// 	WidgetInfo: dto.WidgetInfo{
	// 		WidgetName: "Product Ordering Review Video",
	// 	},
	// 	ProductId: *request.Data.ProductID,
	// }
	// if len(topProductVideoReviews.Data) > 0 {
	// 	widgetsData = append(widgetsData, widgetType12Data)
	// }

	// get 10 suggested products for the product and create 2 type 43 widget for it and send in response to FE
	suggestedProductsWidgets, err := s.GetSuggestedProductsWidgets(
		ctx,
		request.Data.Source,
		*request.Data.ProductID,
		request.UserID,
		request.Meta)
	if err != nil {
		return nil, err
	}
	if len(suggestedProductsWidgets) > 0 {
		widgetsData = append(widgetsData, suggestedProductsWidgets...)
	}

	productReviewRequest := &dto.GetProductsReviewsRequest{
		Data: dto.ProductReviewsRequest{
			CategoryID: request.Data.CategoryID,
			ProductID:  request.Data.ProductID,
			Source:     request.Data.Source},
		Meta:   request.Meta,
		UserID: request.UserID}
	productReviews, err := s.GetProductReviews(ctx, productReviewRequest)
	if err != nil {
		return
	}

	if len(productReviews.Data) > 0 {
		for _, widgetType36 := range productReviews.Data {
			widgetsData = append(widgetsData, widgetType36)
		}
	}

	response = &dto.ProductReviewsWidgets{
		Data: dto.WidgetsData{
			Widgets: widgetsData,
		},
	}
	return
}
