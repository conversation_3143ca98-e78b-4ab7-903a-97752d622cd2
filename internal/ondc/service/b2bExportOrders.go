package service

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/repositories/sqlRepo"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	"kc/internal/ondc/utils"
	"math"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

func DerefString(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func getAllProducts(repo *sqlRepo.Repository) (map[int]*dao.KiranaBazarProduct, error) {
	query := "SELECT * FROM kiranabazar_products"
	var products []dao.KiranaBazarProduct

	// Execute the query and fetch all products
	_, err := repo.CustomQuery(&products, query)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch products: %v", err)
	}

	// Create a map to store products with ID as the key
	productMap := make(map[int]*dao.KiranaBazarProduct)
	for i := range products {
		product := &products[i] // Get a pointer to the product
		productMap[int(product.ID)] = product
	}

	return productMap, nil
}

func getProductByID(productMap map[int]*dao.KiranaBazarProduct, id int) (*dao.KiranaBazarProduct, error) {
	product, exists := productMap[id]
	if !exists {
		return nil, fmt.Errorf("product with ID %d not found", id)
	}
	return product, nil
}

func (s *Service) createOrderJSON(repo *sqlRepo.Repository, order *dto.OrderExportData, productMap map[int]*dao.KiranaBazarProduct) ([]dto.CSVRecord, error) {
	var records []dto.CSVRecord
	var orderDetails dao.KiranaBazarOrderDetails
	paidAmount := order.PaidAmount

	err := json.Unmarshal([]byte(order.OrderDetails), &orderDetails)
	if err != nil {
		return nil, fmt.Errorf("error unmarshaling order details: %v", err)
	}

	cartValue := orderDetails.GetCartValue()
	totalDiscount := orderDetails.GetDiscountValue()
	serviceCharges := orderDetails.GetChargeValue()

	cartDetails := orderDetails.GetOrderCart()
	if err != nil {
		return nil, fmt.Errorf("error fetching product map: %v", err)
	}

	//allCouponsKeyMap := getAllCouponsDesMap()
	//for _, j := range orderDetails.BillBreakUp.DiscountPricing {
	//value, exists := allCouponsKeyMap[j.Key]
	// if exists {
	// 	if value.Type == "FreeBie" && orderDetails.Seller == utils.APSARA_TEA {
	// 		//productData, exists := products.GetProductByID(value.Freebie.ProductID)
	// 		if exists {
	// 			// cartData := shared.SellerItems{
	// 			// 	SellerItemsData: shared.SellerItemsData{
	// 			// 		Meta:     datatypes.JSON(productData.Meta),
	// 			// 		Quantity: int32(value.Freebie.Quantity),
	// 			// 		ID:       value.Freebie.ProductID,
	// 			// 	},
	// 			// }
	// 			// cartDetails = append(cartDetails, cartData)
	// 		}
	// 	}
	// }
	//}

	for _, cartItemDetails := range cartDetails {
		cartMeta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal([]byte(cartItemDetails.Meta), &cartMeta)
		if err != nil {
			return nil, fmt.Errorf("error unmarshaling order details: %v", err)
		}
		partPaid := "NO"
		if paidAmount > 0 {
			partPaid = "YES"
		}
		wholeSaleRate := cartMeta.WholesaleRate
		if cartMeta.BrandWholesaleRate != nil {
			wholeSaleRate = *cartMeta.BrandWholesaleRate
		}
		productPrice := wholeSaleRate * float64(cartItemDetails.Quantity) * float64(cartMeta.PackSize)
		productPriceWeOffered := cartMeta.WholesaleRate * float64(cartItemDetails.Quantity) * float64(cartMeta.PackSize)
		totalPackets := int32(cartMeta.PackSize) * cartItemDetails.Quantity

		itemID, err := strconv.Atoi(cartItemDetails.ID)
		if err != nil {
			return nil, fmt.Errorf("product id not a string: %v", err)
		}

		itemData, err := getProductByID(productMap, itemID)
		if err != nil {
			continue
		}

		formattedDate := time.Unix(order.OrderConfirmed/1000, 0).Format("2006-01-02")
		if order.OrderConfirmed == 0 {
			formattedDate = ""
		}
		additionalDiscountValue := math.Round(((productPrice/cartValue)*totalDiscount)*100) / 100
		if cartMeta.BrandWholesaleRate != nil {
			additionalDiscountValue += productPrice - productPriceWeOffered
		}

		var productPriceWithoutTaxString *string
		var tax string
		var taxableValue string
		if cartMeta.Tax != nil {
			productPriceWithoutTax := (productPrice / (1 + (*cartMeta.Tax / 100)))
			formattedPrice := fmt.Sprintf("%.2f", productPriceWithoutTax)
			productPriceWithoutTaxString = &formattedPrice
			taxableValue = fmt.Sprintf("%.2f", (productPrice-additionalDiscountValue)/(1+(*cartMeta.Tax/100)))
			tax = fmt.Sprintf("%.2f", ((productPrice-additionalDiscountValue)/(1+(*cartMeta.Tax/100)))*(*cartMeta.Tax/100))
		}
		// totalProductDiscount := math.Ceil(additionalDiscountValue)
		productActiveStatus := ""
		if slices.Contains([]string{displaystatus.PLACED, displaystatus.CONFIRMED, displaystatus.PENDING_CONFIRMATION}, order.OrderStatus) {
			if !productMap[int(itemData.ID)].IsActive {
				productActiveStatus = "INACTIVE"
			} else {
				if productMap[int(itemData.ID)].IsOos {
					productActiveStatus = "OUT_OF_STOCK"
				} else {
					productActiveStatus = "IN_STOCK"
				}
			}
		}

		record := dto.CSVRecord{
			OrderID:                int64(order.ID),
			SaleOrderItemCode:      fmt.Sprintf("KC_%06d-%s", order.ID, cartItemDetails.ID),
			DisplayOrderCode:       fmt.Sprintf("KC_%06d", order.ID),
			WholesalePrice:         fmt.Sprintf("%.2f", wholeSaleRate),
			MRP:                    fmt.Sprintf("%.2f", cartMeta.MRPNumber),
			TotalPrice:             fmt.Sprintf("%.2f", productPrice),
			TotalDiscount:          fmt.Sprintf("%.2f", additionalDiscountValue),
			TotalPriceWithoutTax:   productPriceWithoutTaxString,
			TaxableValue:           taxableValue,
			Tax:                    tax,
			PacketNumber:           fmt.Sprintf("%d", totalPackets),
			Date:                   formattedDate,
			ItemSKUCode:            itemData.Code,
			ItemSKUName:            itemData.Name,
			ShippingAddressName:    orderDetails.ShippingAddress.Name,
			ShippingAddressPhone:   orderDetails.ShippingAddress.Phone,
			ShippingAddressLine1:   orderDetails.ShippingAddress.Line1,
			ShippingAddressLine2:   orderDetails.ShippingAddress.Line2,
			ShippingAddressCity:    orderDetails.ShippingAddress.District,
			ShippingAddressState:   orderDetails.ShippingAddress.State,
			ShippingAddressPincode: orderDetails.ShippingAddress.PostalCode,
			PaymentType:            "COD",
			UserID:                 order.UserID,
			PartPaid:               partPaid,
			AdvancePaidAmount:      fmt.Sprintf("%.0f", paidAmount),
			CodAmount:              fmt.Sprintf("%.0f", order.OrderAmount-paidAmount+serviceCharges),
			OrderStatus:            order.OrderStatus,
			ProductActiveStatus:    productActiveStatus,
		}
		records = append(records, record)
	}

	return records, nil
}

func (s *Service) ExportOrders(ctx *gin.Context, req *dto.ExportOrderRequest) (*[]dto.CSVRecord, error) {
	parsedDate, _ := time.Parse("2006-01-02", req.Data.Date)
	epochStart := parsedDate.Unix() * 1000
	epochEnd := (parsedDate.AddDate(0, 0, 1).Unix() * 1000) - 1

	query := fmt.Sprintf(`SELECT
        ko.id,
        kod.order_details,
        kop.amount AS order_amount,
		kop.paid_amount,
        kbr.order_confirmed,
        ko.user_id
		FROM
			kiranabazar_orders ko
		JOIN
			kiranabazar_order_details kod ON ko.id = kod.order_id
		JOIN
			kiranabazar_order_payments kop ON ko.id = kop.order_id
		JOIN
			kiranaclubdb.kc_bazar_reconciliation kbr ON ko.id = kbr.order_id
		WHERE
        ko.seller = '%s'
        AND ko.display_status = 'CONFIRMED'
        AND kbr.order_confirmed BETWEEN %d AND %d`, req.Data.Seller, epochStart, epochEnd)

	orders := []dto.OrderExportData{}
	_, err := s.repository.CustomQuery(&orders, query)
	if err != nil {
		return nil, err
	}

	productMap, _ := getAllProducts(s.repository)
	var records []dto.CSVRecord
	var orderIds []int
	for _, order := range orders {
		orderCartRecords, err := s.createOrderJSON(s.repository, &order, productMap)
		if err != nil {
			fmt.Printf("Error processing order %d: %v\n", order.ID, err)
			continue
		}
		records = append(records, orderCartRecords...)

		orderIds = append(orderIds, order.ID)
	}

	return &records, nil
}

func preProcessExportRequest(req *dto.GetOrdersRequest) error {
	// TODO: Add more export request validations here
	// add check to have req.to and req.from duration less than equal to 60 days
	if req.To-req.From > 60*24*60*60*1000 {
		return fmt.Errorf("duration between from and to cannot be more than 60 days")
	}
	return nil
}

// ExportOrdersGeneric this returns the records at order level
func (s *Service) ExportOrdersGeneric(ctx *gin.Context, req *dto.GetOrdersRequest) (*[]dto.OrderLevelCSVRecord, error) {
	req.Limit = 1000000
	req.Offset = 0
	err := preProcessExportRequest(req)
	if err != nil {
		return nil, err
	}
	// Preprocess the request for downloading the orders
	allOrders, err := s.GetOrders(ctx, req)
	if err != nil {
		return nil, err
	}
	allOrderIds := []int64{}
	for _, order := range allOrders.Data {
		orderID, err := strconv.ParseInt(order.OrderID, 10, 64)
		if err != nil {
			return nil, err
		}
		allOrderIds = append(allOrderIds, orderID)
	}

	query := fmt.Sprintf(`
	select
		ko.id as order_id,
		ko.user_id as user_id,
		kbr.order_placed as placed_ts,
		kbr.order_confirmed as confirmed_ts,
		kbr.order_dispatched as dispatched_ts,
		kbr.order_delivered as delivered_ts,
		kop.amount as invoice_amount,
		ko.seller as seller,
		kos.courier as courier_name,
		kos.awb_number as awb_number,
		json_extract(kod.order_details, "$.shipping_address.postal_code") as pincode,
		json_extract(kod.order_details, "$.shipping_address.state") as state,
		json_extract(kod.order_details, "$.shipping_address.district") as city,
		kop.ext_invoice_number as invoice_number,
		ko.display_status as order_status
	from
		kiranabazar_orders ko
	left join kiranabazar_order_details kod on
		ko.id = kod.order_id
	left join kc_bazar_reconciliation kbr on
		ko.id = kbr.order_id
	left join kiranabazar_order_payments kop on
		ko.id = kop.order_id
	left join kiranabazar_order_status kos on
		ko.id = kos.id where ko.id IN (%s)`, strings.Trim(strings.Join(strings.Fields(fmt.Sprint(allOrderIds)), ","), "[]"))

	type genericExportOrder struct {
		OrderID       int64   `json:"order_id"`
		UserID        string  `json:"user_id"`
		PlacedTs      int64   `json:"placed_ts"`
		ConfirmedTs   int64   `json:"confirmed_ts"`
		DispatchedTs  int64   `json:"dispatched_ts"`
		DeliveredTs   int64   `json:"delivered_ts"`
		InvoiceAmount float64 `json:"invoice_amount"`
		Seller        string  `json:"seller"`
		CourierName   string  `json:"courier_name"`
		AWBNumber     string  `json:"awb_number"`
		Pincode       string  `json:"pincode"`
		State         string  `json:"state"`
		City          string  `json:"city"`
		InvoiceNumber string  `json:"invoice_number"`
		OrderStatus   string  `json:"order_status"`
	}
	orders := []genericExportOrder{}
	_, err = s.ReadOnlyRepository.CustomQuery(&orders, query)
	if err != nil {
		return nil, err
	}

	istLocation := time.FixedZone("IST", 5*60*60+30*60)
	exportedOrders := []dto.OrderLevelCSVRecord{}
	for _, order := range orders {
		confirmedDate := ""
		dispatchedDate := ""
		deliveredDate := ""
		placedDate := ""

		if order.ConfirmedTs != 0 {
			confirmedDate = time.UnixMilli(order.ConfirmedTs).In(istLocation).Format("02-01-2006 15:04:05")
		}
		if order.DispatchedTs != 0 {
			dispatchedDate = time.UnixMilli(order.DispatchedTs).In(istLocation).Format("02-01-2006 15:04:05")
		}
		if order.DeliveredTs != 0 {
			deliveredDate = time.UnixMilli(order.DeliveredTs).In(istLocation).Format("02-01-2006 15:04:05")
		}
		if order.PlacedTs != 0 {
			placedDate = time.UnixMilli(order.PlacedTs).In(istLocation).Format("02-01-2006 15:04:05")
		}
		exportedOrders = append(exportedOrders, dto.OrderLevelCSVRecord{
			DisplayOrderCode: fmt.Sprintf("KC_%06d", order.OrderID),
			OrderID:          order.OrderID,
			UserID:           order.UserID,
			PlacedDate:       placedDate,
			ConfirmedDate:    confirmedDate,
			DispatchedDate:   dispatchedDate,
			DeliveredDate:    deliveredDate,
			InvoiceAmount:    fmt.Sprintf("%.2f", order.InvoiceAmount),
			Seller:           order.Seller,
			Courier:          order.CourierName,
			AWBNumber:        order.AWBNumber,
			Pincode:          strings.ReplaceAll(order.Pincode, `"`, ""),
			State:            strings.ReplaceAll(order.State, `"`, ""),
			City:             strings.ReplaceAll(order.City, `"`, ""),
			InvoiceNumber:    order.InvoiceNumber,
			OrderStatus:      order.OrderStatus,
		})
	}
	return &exportedOrders, nil
}

// ExportOrdersGenericCSV this exports the csv at order level and is called for internal b2b users
func (s *Service) ExportOrdersGenericCSV(ctx *gin.Context, req *dto.GetOrdersRequest) error {
	records, err := s.ExportOrdersGeneric(ctx, req)
	if err != nil {
		return err
	}

	ctx.Header("Content-Disposition", `attachment; filename="OrderLevelExport.csv"`)
	ctx.Header("Content-Type", "text/csv")

	writer := csv.NewWriter(ctx.Writer)
	header := []string{
		"Order ID",
		"Display Order Code",
		"UserID",
		"Placed Date",
		"Confirmed Date",
		"Dispatched Date",
		"Delivered Date",
		"RTO Expected Date",
		"RTO Delivered Date",
		"Invoice Amount",
		"Seller",
		"Courier",
		"AWB Number",
		"Pincode",
		"State",
		"City",
		"Invoice Number",
		"RTO Reason",
		"Order Status",
	}

	err = writer.Write(header)
	if err != nil {
		logger.Error(ctx, "error while writing csv header")
		return err
	}
	if records != nil {
		for _, record := range *records {
			row := []string{
				fmt.Sprintf("%d", record.OrderID),
				record.DisplayOrderCode,
				record.UserID,
				record.PlacedDate,
				record.ConfirmedDate,
				record.DispatchedDate,
				record.DeliveredDate,
				record.RTOExpectedDate,
				record.RTODeliveredDate,
				record.InvoiceAmount,
				record.Seller,
				record.Courier,
				record.AWBNumber,
				record.Pincode,
				record.State,
				record.City,
				record.InvoiceNumber,
				record.RTOReason,
				record.OrderStatus,
			}
			if err := writer.Write(row); err != nil {
				fmt.Printf("Error writing record: %v\n", err)
				continue
			}
		}
	}
	writer.Flush()
	if err := writer.Error(); err != nil {
		logger.Error(ctx, "error while flushing csv writer")
		return err
	}
	return nil
}

// ExportOrdersGenericSKULevel this returns the records at sku level
func (s *Service) ExportOrdersGenericSKULevel(ctx *gin.Context, req *dto.GetOrdersRequest) (*[]dto.CSVRecord, error) {
	req.Limit = 1000000
	req.Offset = 0
	err := preProcessExportRequest(req)
	if err != nil {
		return nil, err
	}
	// Preprocess the request for downloading the orders
	allOrders, err := s.GetOrders(ctx, req)
	if err != nil {
		return nil, err
	}
	allOrderIds := []int64{}
	for _, order := range allOrders.Data {
		oid, err := strconv.ParseInt(order.OrderID, 10, 64)
		if err != nil {
			return nil, err
		}
		allOrderIds = append(allOrderIds, oid)
	}

	query := fmt.Sprintf(`SELECT
        ko.id,
        kod.order_details,
        kop.amount AS order_amount,
		kop.paid_amount,
        kbr.order_confirmed,
        ko.user_id,
		ko.display_status as order_status
		FROM
			kiranabazar_orders ko
		JOIN
			kiranabazar_order_details kod ON ko.id = kod.order_id
		JOIN
			kiranabazar_order_payments kop ON ko.id = kop.order_id
		JOIN
			kiranaclubdb.kc_bazar_reconciliation kbr ON ko.id = kbr.order_id
		WHERE
        ko.id IN (%s)`, strings.Trim(strings.Join(strings.Fields(fmt.Sprint(allOrderIds)), ","), "[]"))

	orders := []dto.OrderExportData{}
	_, err = s.ReadOnlyRepository.CustomQuery(&orders, query)
	if err != nil {
		return nil, err
	}

	productMap, err := getAllProducts(s.repository)
	if err != nil {
		return nil, err
	}
	var records []dto.CSVRecord

	for _, order := range orders {
		orderCartRecords, err := s.createOrderJSON(s.repository, &order, productMap)
		if err != nil {
			fmt.Printf("Error processing order %d: %v\n", order.ID, err)
			continue
		}
		records = append(records, orderCartRecords...)
	}

	return &records, nil
}

// ExportOrdersGenericSKULevelCSV this exports the csv at sku level and is called for internal b2b users
func (s *Service) ExportOrdersGenericSKULevelCSV(ctx *gin.Context, req *dto.GetOrdersRequest) error {
	records, err := s.ExportOrdersGenericSKULevel(ctx, req)
	if err != nil {
		return err
	}

	ctx.Header("Content-Disposition", `attachment; filename="PendingOrders.csv"`)
	ctx.Header("Content-Type", "text/csv")

	writer := csv.NewWriter(ctx.Writer)
	header := []string{
		"Sale Order Item Code*",
		"Display Order Code*",
		"Order ID",
		"Wholesale Price",
		"MRP",
		"Total Price*",
		"Total Discount",
		"Quantity",
		"Taxable Value",
		"Tax",
		"Date(\"YYYY-MM-DD\")*",
		"Item SKU Code*",
		"Item SKU Name*",
		"Product Status",
		"Shipping Address Name",
		"Payment Type*",
		"UserID",
		"Part Payment",
		"Advance Payment Amount",
		"COD Amount",
		"Order Status",
	}

	err = writer.Write(header)
	if err != nil {
		logger.Error(ctx, "error while writing csv header")
		return err
	}
	if records != nil {
		for _, record := range *records {
			row := []string{
				record.SaleOrderItemCode,
				record.DisplayOrderCode,
				fmt.Sprintf("%d", record.OrderID),
				record.WholesalePrice,
				record.MRP,
				record.TotalPrice,
				record.TotalDiscount,
				record.PacketNumber,
				record.TaxableValue,
				record.Tax,
				record.Date,
				record.ItemSKUCode,
				record.ItemSKUName,
				record.ProductActiveStatus,
				DerefString(record.ShippingAddressName),
				record.PaymentType,
				record.UserID,
				record.PartPaid,
				record.AdvancePaidAmount,
				record.CodAmount,
				record.OrderStatus,
			}
			if err := writer.Write(row); err != nil {
				fmt.Printf("Error writing record: %v\n", err)
				continue
			}
		}
	}
	writer.Flush()
	if err := writer.Error(); err != nil {
		logger.Error(ctx, "error while flushing csv writer")
		return err
	}

	return nil
}

func (s *Service) ExportOrdersCSV(ctx *gin.Context, request *dto.ExportOrderRequest) error {
	records, err := s.ExportOrders(ctx, request)
	if err != nil {
		return err
	}

	ctx.Header("Content-Disposition", `attachment; filename="PendingOrders.csv"`)
	ctx.Header("Content-Type", "text/csv")

	writer := csv.NewWriter(ctx.Writer)
	header := []string{"Sale Order Item Code*",
		"Display Order Code*",
		"Total Price*",
		"Total Discount"}
	if request.Data.Seller == utils.MICHIS {
		header = append(header, "Total Price Without Tax*")
	}
	header = append(header, []string{"Packet Number*",
		"Date(\"YYYY-MM-DD\")*",
		"Item SKU Code*",
		"Item SKU Name*",
		"Shipping Address Name",
		"Shipping Address Line 1",
		"Shipping Address Line 2",
		"Shipping Address City",
		"Shipping Address State",
		"Shipping Address Pincode*",
		"Payment Type*",
		"UserID",
		"Part Payment",
		"Advance Payment Amount",
		"COD Amount",
	}...)

	err = writer.Write(header)
	if err != nil {
		logger.Error(ctx, "error while writing csv header")
		return err
	}
	if records != nil {
		for _, record := range *records {
			row := []string{
				record.SaleOrderItemCode,
				record.DisplayOrderCode,
				record.TotalPrice,
				record.TotalDiscount,
			}
			if request.Data.Seller == utils.MICHIS {
				row = append(row, DerefString(record.TotalPriceWithoutTax))
			}
			row = append(row, []string{
				record.PacketNumber,
				record.Date,
				record.ItemSKUCode,
				record.ItemSKUName,
				DerefString(record.ShippingAddressName),
				record.ShippingAddressLine1,
				record.ShippingAddressLine2,
				DerefString(record.ShippingAddressCity),
				DerefString(record.ShippingAddressState),
				DerefString(record.ShippingAddressPincode),
				record.PaymentType,
				record.UserID,
				record.PartPaid,
				record.AdvancePaidAmount,
				record.CodAmount,
			}...)
			if err := writer.Write(row); err != nil {
				fmt.Printf("Error writing record: %v\n", err)
				continue
			}
		}
	}
	writer.Flush()
	if err := writer.Error(); err != nil {
		logger.Error(ctx, "error while flushing csv writer")
		return err
	}

	return nil
}

func (s *Service) ExportOrderPaymentDetails(ctx *gin.Context, request *dto.ExportOrderPaymentDetailsRequest) (*dto.ExportOrderPaymentDetailsResponse, error) {
	seller := request.Data.Seller
	year := request.Data.Year
	month := request.Data.Month
	if seller == "" || year == 0 || month == 0 {
		return nil, fmt.Errorf("seller and date are required")
	}

	var records []dto.CSVRecordPaymentDetails
	var orderPayments []dao.KiranaBazarOrderPayment

	location, _ := time.LoadLocation("Asia/Kolkata")
	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, location)
	endDate := startDate.AddDate(0, 1, 0) // First day of the next month

	// Optimized query using range condition for date filtering
	query := fmt.Sprintf(`
	SELECT kop.*
	FROM kiranabazar_order_payments kop
	JOIN kiranabazar_orders ko ON ko.id = kop.order_id
	WHERE ko.seller = '%s' 
	AND kop.created_at >= '%v'
	AND kop.created_at < '%v' 
	AND (kop.status = 'PARTIALLY_PAID' OR kop.paid_amount > 0)
	AND ko.is_archived = false
	ORDER BY kop.order_id;
	`, seller, startDate.Format("2006-01-02 15:04:05"), endDate.Format("2006-01-02 15:04:05"))

	_, err := s.repository.CustomQuery(&orderPayments, query)
	if err != nil {
		return nil, err
	}

	for _, payment := range orderPayments {
		createdAtUnixTimestamp := payment.CreatedAt.UnixMilli()
		paymentMeta := dao.PaymentMeta{}
		err := json.Unmarshal(payment.PaymentMeta, &paymentMeta)
		if err != nil {
			return nil, fmt.Errorf("error unmarshaling payment meta: %v", err)
		}

		paidAmountProof := []string{}
		updatedAtUnixTimestamp := payment.UpdatedAt.UnixMilli()
		for _, proof := range paymentMeta.PaidAmountProof {
			paidAmountProof = append(paidAmountProof, proof.Url)
			if proof.UpdatedAt != "" {
				utcTime, err := time.Parse(time.RFC3339, proof.UpdatedAt)
				if err == nil {
					updatedAtUnixTimestamp = utcTime.UnixMilli()
				}
			}
		}
		orderDateString := payment.CreatedAt.In(location).Format("02-01-2006")
		paymentDateString := time.UnixMilli(updatedAtUnixTimestamp).In(location).Format("02-01-2006 15:04:05")
		paidAmountProofString := strings.Join(paidAmountProof, ",")

		record := dto.CSVRecordPaymentDetails{
			OrderID:               *payment.OrderID,
			OrderDate:             createdAtUnixTimestamp,
			TotalOrderValue:       *payment.Amount,
			PartialPaidValue:      *payment.PaidAmount,
			PaidAmountProof:       paidAmountProof,
			PaymentDate:           updatedAtUnixTimestamp,
			PaymentStatus:         *payment.Status,
			OrderDateString:       orderDateString,
			PaymentDateString:     paymentDateString,
			PaidAmountProofString: paidAmountProofString,
		}
		records = append(records, record)
	}

	headers := []dto.CustomField{
		{Name: "Order ID", Value: "order_id"},
		{Name: "Order Date", Value: "order_date_string"},
		{Name: "Total Order Value", Value: "order_amount"},
		{Name: "Partial Payment Value", Value: "partial_paid_value"},
		{Name: "Payment Proof", Value: "paid_amount_proof_string"},
		{Name: "Payment Date", Value: "payment_date_string"},
	}

	return &dto.ExportOrderPaymentDetailsResponse{
		Data:    records,
		Headers: headers,
	}, nil
}

func (s *Service) ExportNDROrders(ctx *gin.Context, request *dto.GetNDROrdersRequestV2) error {
	records, err := s.GetNDROrdersV2(ctx, request)

	if err != nil {
		return err
	}

	ctx.Header("Content-Disposition", `attachment; filename="NDROrders.csv"`)
	ctx.Header("Content-Type", "text/csv")

	writer := csv.NewWriter(ctx.Writer)
	header := []string{
		"Order ID",
		"Order Date",
		"Customer Name",
		"Order Status",
		"Display Status",
		"Seller",
		"Order Amount",
		"Payment Mode",
		"AWB",
		"Courier",
		"Priority",
		"CF Call Status",
		"Status",
		"Attempts",
		"Created At",
		"Source",
		"Delivery Agent Reason",
		"Action Required",
		"CF Next Attempt At",
		"CF NDR User Reason",
		"CF Note",
		"CF Assigned To",
		"CF Action Taken At",
		"3PL Action Taken",
		"3PL Note",
		"3PL Action Taken At",
		"User Delivered Orders",
	}

	// Write the header to the CSV
	if err := writer.Write(header); err != nil {
		logger.Error(ctx, "error while writing csv header")
		return err
	}

	// Write the records to the CSV
	for _, record := range records.Data {
		status := record.LMAction
		if record.LMAction != "PENDING" {
			status = "Open"
		} else if record.LMAction == "CLOSED" {
			status = "Closed"
		}
		var createdAt, cfUpdatedAt, lmUpdatedAt string
		if record.CreatedAt != 0 {
			createdAt = time.Unix(record.CreatedAt/1000, 0).Format("2006-01-02 15:04:05")
		}
		if record.CFUpdatedAt != 0 {
			cfUpdatedAt = time.Unix(record.CFUpdatedAt/1000, 0).Format("2006-01-02 15:04:05")
		}
		if record.LMUpdatedAt != 0 {
			lmUpdatedAt = time.Unix(record.LMUpdatedAt/1000, 0).Format("2006-01-02 15:04:05")
		}
		row := []string{
			fmt.Sprintf("%d", record.OrderID),
			record.OrderDate,
			record.CustomerName,
			record.OrderStatus,
			record.DisplayStatus,
			record.Seller,
			fmt.Sprintf("%.1f", record.OrderValue),
			record.PaymentStatus,
			record.AWBNumber,
			record.Courier,
			record.Priority,
			record.ActionStatus,
			status,
			fmt.Sprintf("%d", *record.NDRAttemptCount),
			createdAt,
			record.Source,
			record.NDRAgentReason,
			record.CFAction,
			record.NextAttemptAt,
			record.CFNDRUserReason,
			record.CFNote,
			record.CFAssignedTo,
			cfUpdatedAt,
			record.LMAction,
			record.LMNote,
			lmUpdatedAt,
			fmt.Sprintf(`%d`, record.TotalOrderDelivered),
		}

		if err := writer.Write(row); err != nil {
			logger.Error(ctx, "error while writing csv row")
			continue
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		logger.Error(ctx, "error while flushing csv writer")
		return err
	}

	return nil
}
