package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/logistics/couriers"
	processingstatus "kc/internal/ondc/service/orderStatus/processingStatus"
	"log"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"gorm.io/datatypes"
)

type ManifestShipment struct {
	AWBNumber    string
	Store        string
	OrderNumber  string
	PaymentMode  string
	SKUCode      string
	CustomerName string
	CourierName  string
	ManifestID   string
}

type ManifestData struct {
	ManifestDate   string
	TotalShipments int
	Shipments      []ManifestShipment
	OrderIDs       []int64
}

// GetShipmentCreatedManifests generates manifest for the shipment created event
func (s *Service) GetShipmentCreatedManifests(ctx context.Context, req *dto.GetOrdersRequest) (*dto.GenerateManifestResponse, error) {
	if len(req.Status) > 0 && !(req.Status[0] == processingstatus.SHIPMENT_CREATED || req.Status[0] == processingstatus.SHIPMENT_MANIFESTED) {
		return nil, fmt.Errorf("status should be SHIPMENT_CREATED")
	}
	req.Limit = 1000000
	req.Offset = 0
	// Preprocess the request for downloading the orders
	allOrders, err := s.GetOrders(ctx, req)
	if err != nil {
		return nil, err
	}
	allOrderIds := []int64{}
	for _, order := range allOrders.Data {
		oid, err := strconv.ParseInt(order.OrderID, 10, 64)
		if err != nil {
			return nil, err
		}
		allOrderIds = append(allOrderIds, oid)
	}
	return s.GenerateManifest(ctx, &dto.GenerateManifestRequest{
		Data: dto.GenerateManifestRequestData{
			OrderIDs: allOrderIds,
		},
	})
}

// GenerateManifest generates manifest for the given order IDs
func (s *Service) GenerateManifest(ctx context.Context, request *dto.GenerateManifestRequest) (*dto.GenerateManifestResponse, error) {
	// Parse order IDs from request
	orderIDs := request.Data.OrderIDs
	if len(orderIDs) == 0 {
		return nil, fmt.Errorf("no valid order IDs provided")
	}

	// Collect shipment data for all orders
	var allShipments []ManifestShipment

	query := fmt.Sprintf(`
		select
			ko.id as order_id,
			kos.awb_numbers as awb_numbers,
			kos.awb_number as awb_number,
			ko.seller,
			kos.courier as courier,
			JSON_UNQUOTE(JSON_EXTRACT(kod.order_details, "$.shipping_address.name")) as customer_name,
			kop.amount,
			kop.paid_amount,
			kos.manifest_id as manifest_id
		from
			kiranabazar_orders ko
		left join kiranabazar_order_details kod on
			ko.id = kod.order_id
		left join kiranabazar_order_status kos on
			ko.id = kos.id
		left join kiranabazar_order_payments kop on
			ko.id = kop.order_id WHERE ko.id IN (%s);`, strings.Trim(strings.Join(strings.Fields(fmt.Sprint(orderIDs)), ","), "[]"))

	type manifestOrder struct {
		OrderID      int64          `json:"order_id"`
		AWBNumbers   datatypes.JSON `json:"awb_numbers"`
		AWBNumber    string         `json:"awb_number"`
		Seller       string         `json:"seller"`
		Courier      string         `json:"courier"`
		CustomerName string         `json:"customer_name"`
		Amount       float64        `json:"amount"`
		PaidAmount   float64        `json:"paid_amount"`
		ManifestID   string         `json:"manifest_id"`
	}
	orders := []manifestOrder{}
	_, err := s.repository.CustomQuery(&orders, query)
	if err != nil {
		return nil, err
	}

	for _, order := range orders {
		awbNumbers := []string{}
		if order.AWBNumbers != nil {
			err := json.Unmarshal([]byte(order.AWBNumbers), &awbNumbers)
			if err != nil {
				return nil, err
			}
		} else {
			awbNumbers = append(awbNumbers, order.AWBNumber)
		}
		for _, awbNumber := range awbNumbers {
			paymentMode := "COD"
			if order.Amount == order.PaidAmount {
				paymentMode = "PREPAID"
			}
			courierName := order.Courier
			cr, err := couriers.GetActualCourier(&courierName)
			if err == nil {
				courierName = cr.CourierName
			}
			shipment := ManifestShipment{
				AWBNumber:    awbNumber,
				Store:        order.Seller,
				OrderNumber:  fmt.Sprintf("KC_%06d", order.OrderID),
				PaymentMode:  paymentMode,
				SKUCode:      order.Seller,
				CustomerName: order.CustomerName,
				CourierName:  courierName,
				ManifestID:   order.ManifestID,
			}
			allShipments = append(allShipments, shipment)
		}
	}

	if len(allShipments) == 0 {
		return nil, fmt.Errorf("no shipments found for the provided orders")
	}

	// Create manifest request
	manifestRequest, err := s.CreateManifestRequest(ctx, ManifestData{
		ManifestDate:   time.Now().Format("2006-01-02T15:04:05"),
		TotalShipments: len(allShipments),
		Shipments:      allShipments,
		OrderIDs:       orderIDs,
	})
	if err != nil {
		return nil, err
	}

	if len(manifestRequest.Data) == 0 {
		return nil, fmt.Errorf("no manifest data generated")
	}

	manifestResp, err := fetchManifest(manifestRequest)
	if err != nil {
		return nil, err
	}
	return &manifestResp, nil
}

// CreateManifestRequest creates the manifest request object to call cloud function
// CreateManifestRequest creates the manifest request object to call cloud function
func (s *Service) CreateManifestRequest(ctx context.Context, md ManifestData) (dto.ManifestRequestObject, error) {
	requestData := make([]dto.ManifestRequestData, 0)

	// Group shipments by courier, store, and manifest ID combination
	courierStoreManifestShipments := make(map[string][]dto.ManifestShipmentData)

	for _, shipment := range md.Shipments {
		courierKey := shipment.CourierName
		if courierKey == "" {
			courierKey = "Default Courier"
		}

		// Create composite key: courier + store + manifest_id
		groupKey := fmt.Sprintf("%s:::%s:::%s", courierKey, shipment.Store, shipment.ManifestID)

		shipmentData := dto.ManifestShipmentData{
			AWBNumber:    shipment.AWBNumber,
			Store:        shipment.Store,
			OrderNumber:  shipment.OrderNumber,
			PaymentMode:  shipment.PaymentMode,
			SKUCode:      shipment.SKUCode,
			CustomerName: shipment.CustomerName,
		}

		courierStoreManifestShipments[groupKey] = append(courierStoreManifestShipments[groupKey], shipmentData)
	}

	// Create manifest data for each courier-store-manifest combination
	for groupKey, shipments := range courierStoreManifestShipments {
		// Extract courier name, store, and manifest ID from the composite key
		parts := strings.Split(groupKey, ":::")
		if len(parts) < 3 {
			return dto.ManifestRequestObject{}, fmt.Errorf("invalid group key format: %s", groupKey)
		}

		courier := parts[0]
		existingManifestID := parts[2]

		// Use existing manifest ID if it's not empty, otherwise generate new one
		manifestID := existingManifestID
		if existingManifestID == "" {
			manifestID = generateManifestID(11)
			err := s.handleFreshlyGeneratedManifest(ctx, manifestID, md.OrderIDs)
			if err != nil {
				return dto.ManifestRequestObject{}, err
			}
		}

		manifestData := dto.ManifestRequestData{
			CourierName:    courier,
			ManifestID:     manifestID,
			ManifestDate:   md.ManifestDate,
			TotalShipments: len(shipments),
			Shipments:      shipments,
		}
		requestData = append(requestData, manifestData)
	}

	return dto.ManifestRequestObject{
		Data: requestData,
	}, nil
}

// handleFreshlyGeneratedManifest handles the manifest generation for the orders which are not manifested yet
func (s *Service) handleFreshlyGeneratedManifest(ctx context.Context, manifestID string, orderIDs []int64) error {

	wg := sync.WaitGroup{}
	wg.Add(2)

	// update manifest id in order status
	go func() {
		defer wg.Done()
		_, err := s.repository.CustomQuery(nil, fmt.Sprintf(`update kiranabazar_order_status set manifest_id = "%s" where id in (%s)`, manifestID, strings.Trim(strings.Join(strings.Fields(fmt.Sprint(orderIDs)), ","), "[]")))
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("error updating manifest id in db %v", err))
		}
	}()

	// update processing status to SHIPMENT_MANIFESTED
	go func() {
		defer wg.Done()
		_, err := s.repository.CustomQuery(nil, fmt.Sprintf(`update kiranabazar_orders set processing_status = "%s" where id in (%s)`, processingstatus.SHIPMENT_MANIFESTED, strings.Trim(strings.Join(strings.Fields(fmt.Sprint(orderIDs)), ","), "[]")))
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("error updating order status in db for manifest generation %v", err))
		}
	}()
	wg.Wait()
	// generate order level manifest in go routine
	go func() {
		err := s.generateOrderLevelManifest(ctx, orderIDs, manifestID)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("error generating order level manifest %v", err))
		}
	}()
	return nil
}

// generateOrderLevelManifest generates manifest for each order and updates the manifest URL in the order status
func (s *Service) generateOrderLevelManifest(ctx context.Context, orderIDs []int64, manifestID string) error {
	for _, orderID := range orderIDs {
		manifestResp, err := s.GenerateManifest(ctx, &dto.GenerateManifestRequest{
			Data: dto.GenerateManifestRequestData{
				OrderIDs: []int64{orderID},
			},
		})
		if err != nil {
			return err
		}
		if len(manifestResp.Result.Manifests) == 0 {
			continue
		}
		manifestURL := manifestResp.Result.Manifests[0].ManifestURL
		_, err = s.repository.CustomQuery(nil, fmt.Sprintf(`update kiranabazar_order_status set manifest_url = "%s" where id = %d`, manifestURL, orderID))
		if err != nil {
			return err
		}
	}
	return nil
}

// fetchManifest makes the HTTP request to the manifest generation API
func fetchManifest(request dto.ManifestRequestObject) (dto.GenerateManifestResponse, error) {
	fmt.Println("calling manifest generation external api")
	const (
		baseURL      = "https://asia-south1-op-d2r.cloudfunctions.net"
		endpoint     = "/manifestGen"
		timeout      = 15 * time.Second
		maxRetries   = 3
		retryBackoff = 500 * time.Millisecond
	)

	var (
		resp       *http.Response
		respBody   []byte
		retryCount = 0
		lastErrMsg string
	)

	for retryCount <= maxRetries {
		payloadBytes, err := json.Marshal(request)
		if err != nil {
			return dto.GenerateManifestResponse{}, fmt.Errorf("failed to marshal request: %w", err)
		}

		// Create request
		client := &http.Client{Timeout: timeout}
		req, err := http.NewRequest(http.MethodPost, baseURL+endpoint, bytes.NewReader(payloadBytes))
		if err != nil {
			return dto.GenerateManifestResponse{}, fmt.Errorf("failed to create request: %w", err)
		}

		// Set headers
		req.Header.Set("Content-Type", "application/json")

		// Execute request
		resp, err = client.Do(req)

		if err == nil && resp.StatusCode == http.StatusOK {
			// Success - break out of retry loop
			defer resp.Body.Close()

			// Read response
			respBody, err = io.ReadAll(resp.Body)
			if err != nil {
				lastErrMsg = fmt.Sprintf("failed to read response body: %v", err)
				retryCount++
				time.Sleep(retryBackoff * time.Duration(retryCount))
				continue
			}

			respp := dto.GenerateManifestResponse{}
			err = json.Unmarshal(respBody, &respp)
			if err != nil {
				return dto.GenerateManifestResponse{}, fmt.Errorf("failed to unmarshal response: %w", err)
			}

			return respp, nil
		}

		// Handle error case
		if resp != nil {
			respBody, _ = io.ReadAll(resp.Body)
			resp.Body.Close()
			lastErrMsg = fmt.Sprintf("unexpected status code: %d, body: %s", resp.StatusCode, string(respBody))
		} else {
			lastErrMsg = fmt.Sprintf("failed to execute request: %v", err)
		}

		// Retry logic
		retryCount++
		if retryCount <= maxRetries {
			log.Printf("Retrying manifest generation request (attempt %d of %d): %s", retryCount, maxRetries, lastErrMsg)
			time.Sleep(retryBackoff * time.Duration(retryCount))
		}
	}

	// Extract manifest ID for slack notification
	manifestID := "Unknown"
	if len(request.Data) > 0 {
		manifestID = request.Data[0].ManifestID
	}

	slack.SendSlackMessage(fmt.Sprintf("Manifest ID: %s, Manifest generation failed after %d retries, last error: %s", manifestID, maxRetries, lastErrMsg))

	// All retries failed
	return dto.GenerateManifestResponse{}, fmt.Errorf("after %d retries, last error: %s", maxRetries, lastErrMsg)
}

// Generate a random manifest ID
func generateManifestID(length int) string {
	// Calculate number of bytes needed (2 hex chars per byte)
	bytes := make([]byte, (length+1)/2)

	// Generate random bytes
	_, err := rand.Read(bytes)
	if err != nil {
		return ""
	}

	// Convert to hex string and uppercase
	hexString := fmt.Sprintf("%X", bytes)

	// Trim to exact length if needed
	if len(hexString) > length {
		hexString = hexString[:length]
	}

	return hexString
}
