package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/external/loyalty"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/external/whatsapp"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/brands"
	orderS "kc/internal/ondc/service/orderStatus"
	"kc/internal/ondc/service/orderStatus/constants"
	displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"
	"kc/internal/ondc/utils"
	"math"
	"strconv"
	"time"

	"github.com/mixpanel/mixpanel-go"
)

var ORIGINAL_TO_RSB_SKU_MAPPING = map[string]string{
	"182": "1074",
	"183": "1076",
	"184": "1051",
	"188": "1047",
	"189": "1054",
	"190": "1052",
	"192": "1075",
	"213": "1036",
	"214": "1037",
	"215": "1174",
	"217": "1038",
	"221": "1039",
	"222": "1040",
	"237": "1041",

	// CINTU
	"2734": "2749",
	"2735": "2750",
	"2736": "2751",
	"2737": "2752",
	"2738": "2753",
	"2739": "2754",
	"2740": "2755",
	"2741": "2756",
	"2742": "2757",
	"2743": "2758",
	"2744": "2759",
	"2745": "2760",
	"2746": "2761",
	"2747": "2762",
	"2748": "2763",
}

// check if the order is of mothers kitchen or panchvati and if so update the seller to rsb
// update the skus and seller in order details and table to that of same sku in RSB
// also update seller in kiranabazar_orders table for that order
func (s *Service) UpdateOrderSellerAndSkus(ctx context.Context, orderId int64, orderDetails *dao.KiranaBazarOrderDetails) {
	if !includes([]string{utils.PANCHVATI, utils.CINTU}, orderDetails.Seller) {
		return
	}

	cart := orderDetails.Cart
	doNotChange := false

	for i, item := range cart {
		rsbProductId, exists := ORIGINAL_TO_RSB_SKU_MAPPING[item.ID]
		if !exists {
			doNotChange = true
			break
		}
		cart[i].ID = rsbProductId
		cart[i].Seller = utils.RSB_SUPER_STOCKIST
	}

	if doNotChange {
		slack.SendSlackMessage(fmt.Sprintf("Found Skus Not in Panchvati or Mothers Kitchen Mapping for %d", orderId))
		return
	}

	orderDetails.Seller = utils.RSB_SUPER_STOCKIST
	orderDetails.Cart = cart

	orderDetailsJson, err := json.Marshal(orderDetails)
	if err != nil {
		return
	}
	_, _, err = s.repository.Update(dao.KiranaBazarOrder{
		ID: &orderId,
	}, dao.KiranaBazarOrder{
		Seller: utils.RSB_SUPER_STOCKIST,
	})
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("Error updating seller and skus for order %d: %v", orderId, err))
		return
	}
	_, _, err = s.repository.Update(dao.KiranaBazarOrderDetail{
		OrderID: &orderId,
	}, dao.KiranaBazarOrderDetail{
		OrderDetails: orderDetailsJson,
	})
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("Error updating order details for order %d: %v", orderId, err))
		return
	}
}

func (s *Service) HandleConfirmedOrder(ctx context.Context, request dto.ConfirmOrderRequest) (response *dto.ConfirmOrderResponse, err error) {
	// getting source
	source := "APP"
	if request.Data.Email == "IVR" || request.Data.Email == "SHIPWAY" {
		source = "AUTOMATION"
	} else if request.Data.Email != "" {
		source = "D2R"
	}

	orderStatus := request.Data.OrderStatus
	orderID := request.Data.OrderID
	if orderID == "" {
		err = errors.New("orderID cannot be empty")
		return
	}

	orderid, err := strconv.Atoi(orderID)
	if err != nil || orderid == 0 {
		err = errors.New("orderID is not defined")
		return
	}

	orderDetails, err := GetOrderDetails(s.repository, orderID)
	if err != nil {
		return
	}

	orderDetails, err = s.populateFreebiesInCart(ctx, &request, orderDetails)
	if err != nil {
		return
	}

	orderid64 := int64(orderid)

	// this is temproarily added for panchvati and mothers kitchen skus to send them as RSB orders by Sitaram Rathi by Wable Sanket on 10/06/2025
	s.UpdateOrderSellerAndSkus(ctx, orderid64, orderDetails)

	orderInfo, err := GetOrderInfo(s.repository, orderid64)
	if err != nil {
		return
	}

	confirmedOrderStatus := displaystatus.CONFIRMED
	if request.Data.OrderStatus != "" {
		confirmedOrderStatus = request.Data.OrderStatus
	}

	orderStatuses := orderS.MapOrderStatus(confirmedOrderStatus, "", orderS.OrderStatusResponse{})
	if orderStatuses.DisplayStatus == orderInfo.DisplayStatus {
		return
	}
	_, _, err = s.repository.Update(dao.KiranaBazarOrder{
		ID: &orderid64,
	}, dao.KiranaBazarOrder{
		OrderStatus:      &confirmedOrderStatus,
		UpdatedAt:        time.Now(),
		DeliveryStatus:   orderStatuses.ShipmentStatus,
		DisplayStatus:    orderStatuses.DisplayStatus,
		ProcessingStatus: orderStatuses.ProcessingStatus,
	})
	if err != nil {
		return
	}

	OMS := orderDetails.Seller

	paidAmountProofUrls := []string{}
	for _, proof := range request.Data.OrderMeta.PaidAmountProof {
		paidAmountProofUrls = append(paidAmountProofUrls, proof.Url)
	}

	if request.Data.OrderMeta.AdvanceTaken != nil && *request.Data.OrderMeta.AdvanceTaken && request.Data.OrderMeta.PaidAmount != nil && *request.Data.OrderMeta.PaidAmount > 0 {
		err := s.UpdateOrderDetailsForPayments(ctx, orderid64, request.Data.OrderMeta.PaymentDiscount, *request.Data.OrderMeta.PaidAmount, orderDetails.TotalAmount)
		if err != nil {
			slack.SendSlackMessage("Error updating order details for payments: " + err.Error())
			return nil, err
		}
	}

	if orderDetails.Seller == utils.ZOFF_FOODS { // send order to easyecom::: Stopped sending orders to easyecom for go_desi
		_, err = s.CreateEasyEcomOrder(ctx, &dto.AppEasyEcomCreateOrderRequest{
			UserID: request.Data.UserID,
			Data: dto.AppEasyEcomCreateOrderData{
				OrderID: orderid,
			},
		})
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error creating easyecom order for: %s %v", request.Data.OrderID, err))
			return nil, err
		}
	} else if orderDetails.Seller == utils.MANGALAM || orderDetails.Seller == utils.NUTRAJ { // send order to unicommerce
		_, err = s.CreateUnicommerceSaleOrder(ctx, &dto.AppUnicommerceCreateSaleOrderRequest{
			UserID: request.Data.UserID,
			Data: dto.AppUnicommerceCreateSaleOrderRequestData{
				OrderID: orderid,
			},
		})
		if err != nil {
			errMsg := fmt.Sprintf("Error creating unicommerce order for: %s %v", request.Data.OrderID, err)
			slack.SendSlackMessage(errMsg)
			err = errors.New(errMsg)
			return nil, err
		}
	} else { //  confirm order
		s.AddDataForReconciliation(ctx, &dto.AddReconciliationRequest{
			OrderID: orderid64,
			Data: []dto.StatusTimeStamp{
				{
					TimeStamp:   time.Now().UnixMilli(),
					OrderStatus: "order_confirmed",
				},
			},
			Service: "INTERNAL",
			OMS:     OMS,
		})
	}

	// reqObject := map[string]interface{}{
	// 	"user_id":  request.Data.UserID,
	// 	"order_id": request.Data.OrderID,
	// 	"amount":   orderDetails.TotalAmount,
	// 	"status":   orderStatus,
	// }
	// call this API again for zoff_foods or RSB for thailand scheme
	// utils.CallExternalAPIAsync(utils.PROGRESS_WIDGET_RESOLVER_API, "POST", reqObject, nil)
	go func(uid, orderid, seller, orderStatus string, orderDetails *dao.KiranaBazarOrderDetails) {
		s.UpdateProgressWidget(context.Background(), &dto.UpdateProgressWidgetRequest{
			UserID: uid,
			Data: dto.UpdateProgressWidgetData{
				OrderID:      orderid,
				Status:       orderStatus,
				Amount:       orderDetails.TotalAmount,
				OrderDetails: orderDetails,
				Seller:       seller,
			},
		})
	}(request.Data.UserID, request.Data.OrderID, orderInfo.Seller, orderStatus, orderDetails)

	go func(phone, seller string, repo *sqlRepo.Repository, oid int64) {
		invoiceLink := "https://d2rstorage2.blob.core.windows.net/whatsapp/whatsapp/Kirana Club (2).png"
		if request.Data.OrderMeta.AdvanceTaken != nil && request.Data.OrderMeta.PaidAmount != nil && *request.Data.OrderMeta.AdvanceTaken && *request.Data.OrderMeta.PaidAmount > 0 {
			resp, err := s.CreateOrderInvoice(ctx, dto.CreateInvoiceRequest{
				OrderID: request.Data.OrderID,
			})
			if err != nil {
				fmt.Println("error creating invoice")
			}
			if resp.Data.InvoiceUrl != "" {
				invoiceLink = resp.Data.InvoiceUrl
			}
		} else {
			orderPayment := dao.KiranaBazarOrderPayment{}
			repo.Find(map[string]interface{}{
				"order_id": oid,
			}, &orderPayment)
			if orderPayment.KCInvoice != "" {
				invoiceLink = orderPayment.KCInvoice
			}
		}

		sellerName, err := brands.GetNameMappingBySeller(seller)

		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error getting seller name mapping for %s: %v Handle Confirmed Order", seller, err))
		}

		err = whatsapp.SendMessage(phone, "order_confirmed_kc_ordering", invoiceLink, sellerName)
		if err != nil {
			fmt.Println("not able to send WA message")
		}
	}(*orderDetails.ShippingAddress.Phone, orderDetails.Seller, s.repository, orderid64)

	paymentMethod := constants.COD
	if request.Data.OrderMeta.PaidAmount != nil && *request.Data.OrderMeta.PaidAmount > 0 {
		if int(*request.Data.OrderMeta.PaidAmount) == int(orderDetails.GetOrderValue()) {
			paymentMethod = constants.FULLY_PAID
		} else {
			paymentMethod = constants.PARTIALLY_PAID
		}
	}

	discountIds := orderDetails.GetDiscountIds()
	discountCodes := []string{}
	if len(discountIds) > 0 {
		for _, discountId := range discountIds {
			discountCode, err := s.Coupons.GetCouponByID(ctx, discountId)
			if err == nil {
				discountCodes = append(discountCodes, discountCode.Code)
			}
		}
	}

	eventObject := map[string]interface{}{
		"distinct_id":       request.Data.UserID,
		"order_id":          request.Data.OrderID,
		"cart_value":        int(orderDetails.GetCartValue()),
		"seller":            orderInfo.Seller,
		"ordering_module":   utils.MakeTitleCase(orderInfo.Seller),
		"email":             request.Data.Email,
		"source":            source,
		"order_value":       int(orderDetails.GetOrderValue()),
		"total_discount":    int(orderDetails.GetDiscountValue()),
		"paid_amount":       request.Data.OrderMeta.PaidAmount,
		"paid_amount_proof": paidAmountProofUrls,
		"payment_discount":  request.Data.OrderMeta.PaymentDiscount,
		"payment_id":        request.Data.OrderMeta.PaymentId,
		"payment_method":    paymentMethod,
		"discount_ids":      discountIds,
		"discount_codes":    discountCodes,
	}
	s.Mixpanel.Track(context.Background(), []*mixpanel.Event{
		s.Mixpanel.NewEvent("Order Confirmed", request.Data.UserID, eventObject,
			fmt.Sprintf("%s_%s", "order_confirmed", request.Data.OrderID)),
	})

	webengage.SendWebengageEvents(&webengage.WebengageEvents{
		UserIds:     []string{request.Data.UserID},
		EventName:   "Order Confirmed",
		EventObject: eventObject,
	})

	// add in redis the order id for kirana club seller
	go func(userId, seller, orderId string) {
		if seller == utils.KIRANA_CLUB {
			err := s.udpateUserKiranaClubOrder(ctx, fmt.Sprintf("KIRANA_CLUB_ORDER::%s", userId), orderId, time.Hour*24*30)
			if err != nil {
				fmt.Println("error in adding order id to redis", err)
			}
		}
	}(request.Data.UserID, orderInfo.Seller, request.Data.OrderID)

	orderAmount := orderDetails.GetOrderValue()

	// call loyalty API for confirmed orders
	go func(userId string, orderId int64, orderAmount float64, appVersion, deviceId string, orderDetails *dao.KiranaBazarOrderDetails, seller string) {
		loyaltyOrderAmount := orderAmount
		if seller == utils.RSB_SUPER_STOCKIST {
			zoffOrderAmount := orderDetails.GetManufacturerCartValue([]string{utils.ZOFF_FOODS})
			amountExcludingZoff := math.Max(0, orderAmount-zoffOrderAmount)
			loyaltyOrderAmount = amountExcludingZoff
		}
		requestObject := map[string]interface{}{
			"data": map[string]interface{}{
				"uid":         userId,
				"appVersion":  appVersion,
				"deviceId":    deviceId,
				"orderId":     orderId,
				"orderAmount": loyaltyOrderAmount,
			},
		}
		loyalty.CallLoyaltyAPI(loyalty.LOYALTY_API_ENDPOINT["CONFIRM_LOYALTY_ORDER"], requestObject, nil)
	}(request.Data.UserID, orderid64, orderAmount, request.Meta.AppVersion, request.Meta.DeviceId, orderDetails, OMS)
	return
}
