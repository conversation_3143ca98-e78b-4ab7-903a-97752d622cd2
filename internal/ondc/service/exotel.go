package service

import (
	"context"
	"encoding/json"
	"fmt"
	ivrorderintegration "kc/internal/ondc/external/exotel/ivrOrderIntegration"
	"kc/internal/ondc/external/whatsapp"
	"kc/internal/ondc/infrastructure/webengage"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/queue"
	queueModels "kc/internal/ondc/queue/models"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	cancelreason "kc/internal/ondc/service/orderReason/cancelReason"
	"kc/internal/ondc/utils"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/mixpanel/mixpanel-go"
)

func (s *Service) ExotelWebhook(ctx context.Context, req *dto.WebhookCallDetails) (interface{}, error) {
	// handling async call for seller related activities
	go handleIVRWebhookCall(req, s)

	byt, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	_, err = s.repository.Create(&dao.ExotelIVRPasthroughWebhookLogs{
		Request:   byt,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Type:      req.Type,
	})
	if err != nil {
		return nil, err
	}
	return req, nil
}

func (s *Service) ExotelReactivationWebhook(ctx context.Context, req *dto.WebhookCallDetails) (interface{}, error) {
	req.Type = "reactivation"
	go handleIVRWebhookCall(req, s)

	byt, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	_, err = s.repository.Create(&dao.ExotelIVRPasthroughWebhookLogs{
		Request:   byt,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Type:      req.Type,
	})
	if err != nil {
		return nil, err
	}
	return req, nil
}

func (s *Service) ExotelPhoneMaskingWebhook(ctx context.Context, req *dto.WebhookCallDetails) (string, error) {
	// handling async call for seller related activities
	result, err := handlePhoneMarkingWebhookCall(ctx, req, s)

	byt, err2 := json.Marshal(req)
	if err2 != nil {
		return "", err
	}
	_, err3 := s.repository.Create(&dao.ExotelIVRPasthroughWebhookLogs{
		Request:   byt,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Type:      req.Type,
	})
	if err3 != nil {
		println("Error saving phone masking webhook call logs")
	}
	if err != nil {
		return "", err
	}
	return result, nil
}

func handlePhoneMarkingWebhookCall(ctx context.Context, request *dto.WebhookCallDetails, s *Service) (string, error) {
	if request.Type == "link_order_id_with_caller_session" {
		orderPin := strings.TrimSpace(request.Digits)
		orderPin = strings.TrimLeft(orderPin, "\"")
		orderPin = strings.TrimRight(orderPin, "\"")
		orderPin = strings.TrimLeft(orderPin, "0")
		orderPinInt, err := strconv.ParseInt(orderPin, 10, 64)

		if err != nil {
			fmt.Println("Error converting orderID to int64:", err)
			return "", err
		}

		orderDetails, err := s.getOrderDetailsFromOrderPin(orderPinInt)
		if err != nil {
			fmt.Println("Error getting details from orderID:", err)
			eventObject := map[string]any{
				"distinct_id":    "82oXtDgnMadYEZvdUwn3VSFQR273",
				"status":         "WrongPin",
				"calledSid":      request.CallSid,
				"incomingNumber": request.CallFrom,
				"orderPin":       orderPinInt,
			}
			e := s.Mixpanel.NewEvent("Delivery Call Triggered", "82oXtDgnMadYEZvdUwn3VSFQR273", eventObject)
			s.Mixpanel.Track(context.Background(), []*mixpanel.Event{e})
			return "", err
		}

		userPhoneNumber := orderDetails.CustomerPhone
		userId := orderDetails.UserID
		userName := orderDetails.UserName
		orderID := orderDetails.OrderID

		fmt.Println("Phone Number Requested:", request.CallSid, orderID, userPhoneNumber, userId, userName)
		s.GcpRedis.RedisClient.Set(ctx, fmt.Sprintf("phoneMasking:phoneNumber:%s", request.CallSid), userPhoneNumber, time.Duration(1)*time.Hour)
		s.GcpRedis.RedisClient.Set(ctx, fmt.Sprintf("phoneMasking:userID:%s", request.CallSid), userId, time.Duration(1)*time.Hour)
		s.GcpRedis.RedisClient.Set(ctx, fmt.Sprintf("phoneMasking:userName:%s", request.CallSid), userName, time.Duration(1)*time.Hour)

		eventObject := map[string]any{
			"distinct_id":     userId,
			"status":          "CallInitiated",
			"calledSid":       request.CallSid,
			"orderID":         orderID,
			"incomingNumber":  request.CallFrom,
			"orderPin":        orderPinInt,
			"seller":          orderDetails.Seller,
			"ordering_module": utils.MakeTitleCase(orderDetails.Seller),
		}
		e := s.Mixpanel.NewEvent("Delivery Call Triggered", userId, eventObject)
		s.Mixpanel.Track(context.Background(), []*mixpanel.Event{e})

		return "OK", nil

	} else if request.Type == "get_call_forward_number_based_on_caller_session" {
		phoneNumber, err := s.GcpRedis.RedisClient.Get(ctx, fmt.Sprintf("phoneMasking:phoneNumber:%s", request.CallSid)).Result()
		userId, err := s.GcpRedis.RedisClient.Get(ctx, fmt.Sprintf("phoneMasking:userID:%s", request.CallSid)).Result()

		if err != nil {
			fmt.Println("Error getting phone number from cache:", err)
			return "", err
		}
		fmt.Println("Phone Number Returned:", request.CallSid, phoneNumber, userId)

		//eventObject := map[string]any{
		//	"distinct_id": userId,
		//	"status":      "CallForwardInitiated",
		//	"calledSid":   request.CallSid,
		//}
		//e := s.Mixpanel.NewEvent("Delivery Call Triggered", userId, eventObject)
		//s.Mixpanel.Track(context.Background(), []*mixpanel.Event{e})

		return phoneNumber, nil
	} else if request.Type == "record_on_caller_session" {
		userId, err := s.GcpRedis.RedisClient.Get(ctx, fmt.Sprintf("phoneMasking:userID:%s", request.CallSid)).Result()

		if err != nil {
			fmt.Println("Error getting phone number from cache:", err)
			return "", err
		}
		fmt.Println("Phone Number Returned:", request.CallSid, userId)

		eventObject := map[string]any{
			"distinct_id":      userId,
			"status":           "CallSuccessful",
			"calledSid":        request.CallSid,
			"DialCallDuration": request.DialCallDuration,
			"RecordingUrl":     request.RecordingUrl,
		}
		e := s.Mixpanel.NewEvent("Delivery Call Finished", userId, eventObject)
		s.Mixpanel.Track(context.Background(), []*mixpanel.Event{e})

		return "Ok", nil
	} else if request.Type == "call_not_picked" {
		userId, err := s.GcpRedis.RedisClient.Get(ctx, fmt.Sprintf("phoneMasking:userID:%s", request.CallSid)).Result()

		if err != nil {
			fmt.Println("Error getting phone number from cache:", err)
			return "", err
		}
		fmt.Println("Phone Number Returned:", request.CallSid, userId)

		eventObject := map[string]any{
			"distinct_id": userId,
			"status":      "CallNotPicked",
			"calledSid":   request.CallSid,
		}
		e := s.Mixpanel.NewEvent("Delivery Call Finished", userId, eventObject)
		s.Mixpanel.Track(context.Background(), []*mixpanel.Event{e})

		return "Ok", nil
	} else if request.Type == "get_call_forward_name_based_on_caller_session" {
		userName, err := s.GcpRedis.RedisClient.Get(ctx, fmt.Sprintf("phoneMasking:userName:%s", request.CallSid)).Result()

		if err != nil {
			fmt.Println("Error getting phone number from cache:", err)
			return "", err
		}
		fmt.Println("Phone Number Returned:", request.CallSid, userName)
		return userName, nil

	}

	fmt.Println("invalid type for phone masking webhook call")
	return "", nil
}

func handleIVRWebhookCall(request *dto.WebhookCallDetails, s *Service) {
	// this is for reactivation temporary mvp
	if request.Type == "reactivation" {
		user, err := s.FirebaseRepository.Auth.GetUserByPhoneNumber(context.Background(), fmt.Sprintf("+91%s", request.CallFrom[1:len(request.CallFrom)]))
		if err != nil {
			fmt.Println("err = ", err)
		}
		uid := user.UserInfo.UID
		webengage.SendWebengageEvents(&webengage.WebengageEvents{
			UserIds:   []string{uid},
			EventName: "IVR Reactivation Ten Second Listened",
			EventObject: map[string]interface{}{
				"distinct_id": uid,
			},
		})
		return
	} else if strings.Contains(request.Type, "trigger_whatsapp") {
		// handle trigger whatsapp
		whatsappTemplate := strings.Replace(request.Type, "trigger_whatsapp", "", 1)
		fromCall := request.CallFrom[1:len(request.CallFrom)]
		whatsapp.SendWhatsAppForTemplate(fromCall, whatsappTemplate[1:])
		return
	}

	customFields := dto.ExotelIVROrderInfo{}
	err := json.Unmarshal([]byte(request.CustomField), &customFields)
	if err != nil {
		fmt.Println("not able to parse customFields")
	}
	if customFields.OrderID == 0 {
		fmt.Println("not a api automated call this is from the campaign, dont do anything here")
		return
	}

	_, err = s.repository.CustomQuery(nil, fmt.Sprintf("update kiranaclubdb.exotel_ivr_data set status = '%s' where sid = '%s' ", strings.ToUpper(request.CallType), request.CallSid))
	if err != nil {
		fmt.Println("not able to update the query", err)
	}

	if request.Type == "zoff_order_confirm" {
		err = ivrorderintegration.HandleOrderConfirmaion(fmt.Sprintf("%d", customFields.OrderID), customFields.UserID, customFields.Seller)
		if err != nil {
			fmt.Println("not able to confirm order")
		}
	} else if request.Type == "zoff_cancel_order" {
		AddCancelOrderRequestToQueue(customFields, request, s.repository, s.Mixpanel)
	} else if request.Type == "order_cancel_reason_galti_se_order" {
		ivrorderintegration.HandleOrderCancellationReason(customFields, cancelreason.ReasonAccidentalOrder, s.repository)
	} else if request.Type == "order_cancel_reason_local_me_jyada_margin" {
		ivrorderintegration.HandleOrderCancellationReason(customFields, cancelreason.ReasonInsufficientMargin, s.repository)
	} else if request.Type == "order_cancel_reason_delivery_time_jyada" {
		ivrorderintegration.HandleOrderCancellationReason(customFields, cancelreason.ReasonLongDelivery, s.repository)
	} else if request.Type == "order_cancel_other_reason" {
		ivrorderintegration.HandleOrderCancellationReason(customFields, cancelreason.ReasonOther, s.repository)
	} else if request.Type == "confirm_order" {
		err = ivrorderintegration.HandleOrderConfirmaion(fmt.Sprintf("%d", customFields.OrderID), customFields.UserID, customFields.Seller)
		if err != nil {
			fmt.Println("not able to confirm order")
		}
	} else if request.Type == "cancel_order" {
		AddCancelOrderRequestToQueue(customFields, request, s.repository, s.Mixpanel)
	}
}

func AddCancelOrderRequestToQueue(ivrInfo dto.ExotelIVROrderInfo, request *dto.WebhookCallDetails, repo *sqlRepo.Repository, mp *mixpanelRepo.Repository) {
	requestBytt, err := json.Marshal(request)
	if err != nil {
		fmt.Println("not able to convert request to []byte")
	}
	id := uuid.NewString()
	repo.Create(dao.ExotelIVROrderCancellationData{
		ID:                 id,
		OrderID:            uint64(ivrInfo.OrderID),
		MetaData:           requestBytt,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
		Status:             "INITIATED",
		CancellationReason: "",
	})
	cancellationReason, ok := ivrorderintegration.OrderCancellationReason[fmt.Sprintf("%s", ivrInfo.OrderID)]
	if !ok {
		cancellationReason = "USER_NOT_SUBMITTED_CANCELLATION_REASON"
	}

	err = queue.OrderCancelQueueInstance.Insert(queueModels.QueueTriggerData{
		ID: id,
		TriggerFunc: func() error {
			ivrorderintegration.CancelKiranaBazarOrderViaQueue(ivrInfo, request, repo, mp, cancellationReason, id)
			return nil
		},
		Data:      "",
		TriggerAt: time.Now().Add(time.Minute * 2),
		Repo:      repo,
	}, time.Now().Add(time.Minute*2))
	if err != nil {
		fmt.Println("error adding cancel order request to queue, ", err)
		return
	}
}
