package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/config"
	"kc/internal/ondc/repositories/cacheRepo"
	"reflect"
	"sync"
	"sync/atomic"
	"time"
)

// CircuitBreaker represents a simple circuit breaker for Redis operations
type CircuitBreaker struct {
	failures    int64
	lastFailure time.Time
	state       int32 // 0: closed, 1: open, 2: half-open
	maxFailures int64
	timeout     time.Duration
	mutex       sync.RWMutex
}

func NewCircuitBreaker(maxFailures int64, timeout time.Duration) *CircuitBreaker {
	return &CircuitBreaker{
		maxFailures: maxFailures,
		timeout:     timeout,
	}
}

func (cb *CircuitBreaker) Call(fn func() error) error {
	cb.mutex.RLock()
	state := atomic.LoadInt32(&cb.state)
	//failures := atomic.LoadInt64(&cb.failures)
	cb.mutex.RUnlock()

	// Circuit is open
	if state == 1 {
		cb.mutex.Lock()
		if time.Since(cb.lastFailure) > cb.timeout {
			atomic.StoreInt32(&cb.state, 2) // Half-open
		}
		cb.mutex.Unlock()

		if atomic.LoadInt32(&cb.state) == 1 {
			return fmt.Errorf("circuit breaker is open")
		}
	}

	err := fn()
	if err != nil {
		cb.mutex.Lock()
		atomic.AddInt64(&cb.failures, 1)
		cb.lastFailure = time.Now()
		if atomic.LoadInt64(&cb.failures) >= cb.maxFailures {
			atomic.StoreInt32(&cb.state, 1) // Open
		}
		cb.mutex.Unlock()
		return err
	}

	// Success - reset circuit breaker
	if state == 2 { // Half-open
		cb.mutex.Lock()
		atomic.StoreInt64(&cb.failures, 0)
		atomic.StoreInt32(&cb.state, 0) // Closed
		cb.mutex.Unlock()
	}

	return nil
}

// Cache represents a generic cache interface
type Cache struct {
	localCache     sync.Map
	maxSize        int64
	currentSize    int64
	cleanupTicker  *time.Ticker
	stopCleanup    chan struct{}
	redisClient    *cacheRepo.Repository
	circuitBreaker *CircuitBreaker
	redisTimeout   time.Duration
	cleanupContext context.Context
	cleanupCancel  context.CancelFunc
}

var (
	instance *Cache
	once     sync.Once
)

// CacheConfig holds configuration for the cache
type CacheConfig struct {
	MaxSize               int64         // Maximum number of entries in local cache (0 = unlimited)
	CleanupInterval       time.Duration // How often to run cleanup (default: 5 minutes)
	RedisTimeout          time.Duration // Timeout for Redis operations (default: 2 seconds)
	CircuitBreakerMax     int64         // Max failures before circuit breaker opens (default: 5)
	CircuitBreakerTimeout time.Duration // Circuit breaker timeout (default: 30 seconds)
}

// DefaultConfig returns default cache configuration
func DefaultConfig() *CacheConfig {
	return &CacheConfig{
		MaxSize:               3000,
		CleanupInterval:       5 * time.Minute,
		RedisTimeout:          1 * time.Second,
		CircuitBreakerMax:     5,
		CircuitBreakerTimeout: 30 * time.Second,
	}
}

// GetInstance returns a singleton instance of Cache with default config
func GetInstance() *Cache {
	return GetInstanceWithConfig(DefaultConfig(), nil)
}

// GetInstanceWithConfig returns a singleton instance of Cache with custom config
func GetInstanceWithConfig(config *CacheConfig, redisClient *cacheRepo.Repository) *Cache {
	once.Do(func() {
		cleanupCtx, cleanupCancel := context.WithCancel(context.Background())

		instance = &Cache{
			maxSize:        config.MaxSize,
			stopCleanup:    make(chan struct{}),
			redisClient:    redisClient,
			redisTimeout:   config.RedisTimeout,
			cleanupContext: cleanupCtx,
			cleanupCancel:  cleanupCancel,
		}

		if redisClient != nil {
			instance.circuitBreaker = NewCircuitBreaker(config.CircuitBreakerMax, config.CircuitBreakerTimeout)
		}

		instance.startCleanupRoutine(config.CleanupInterval)
	})
	return instance
}

// CacheResult represents the result of a cache operation
type CacheResult struct {
	Data interface{}
	Err  error
}

// getCachedDataTyped is the internal generic implementation
func getCachedDataTyped[T any](
	c *Cache,
	ctx context.Context,
	key string,
	fn func() (T, error),
	localExpiry,
	redisExpiry time.Duration,
) (T, error) {
	envVar := config.GetEnv()
	prefixedKey := fmt.Sprintf("%s_%s", envVar, key)

	// Try local cache first with atomic check and cleanup
	if localExpiry > 0 {
		if cached, ok := c.localCache.LoadAndDelete(prefixedKey); ok {
			if cacheEntryValue, ok := cached.(*cacheEntry); ok {
				if !cacheEntryValue.isExpired() {
					// Put back the non-expired entry
					c.localCache.Store(prefixedKey, cacheEntryValue)

					// Type-safe conversion
					if typed, ok := cacheEntryValue.data.(T); ok {
						return typed, nil
					}
					// If type assertion fails, remove the entry and continue
					c.localCache.Delete(prefixedKey)
					atomic.AddInt64(&c.currentSize, -1)
				} else {
					// Entry was expired, decrement counter since we deleted it
					atomic.AddInt64(&c.currentSize, -1)
				}
			}
		}
	}

	// Try Redis cache if expiry is set and Redis client is available
	if redisExpiry > 0 && c.redisClient != nil && c.circuitBreaker != nil {
		var redisData T
		//var redisErr error

		// Use circuit breaker for Redis operations
		cbErr := c.circuitBreaker.Call(func() error {
			// Create context with timeout for Redis operation
			redisCtx, cancel := context.WithTimeout(ctx, c.redisTimeout)
			defer cancel()

			redisResp, err := c.redisClient.RedisClient.Get(redisCtx, prefixedKey).Result()
			if err != nil {
				return err
			}

			// Create a new instance of T for unmarshaling
			var tempData T
			if err := json.Unmarshal([]byte(redisResp), &tempData); err != nil {
				return err
			}

			redisData = tempData
			return nil
		})

		if cbErr == nil {
			// Cache in local memory if local expiry is set
			if localExpiry > 0 {
				c.storeInLocalCache(prefixedKey, redisData, localExpiry)
			}
			fmt.Println("redis cache hit", prefixedKey)
			return redisData, nil
		} else if cbErr != nil {
			fmt.Printf("redis cache error: %v\n", cbErr)
		}
	}

	// Compute the value
	result, err := fn()
	if err != nil {
		var zero T
		return zero, err
	}

	// Cache the result in local cache
	if localExpiry > 0 {
		c.storeInLocalCache(prefixedKey, result, localExpiry)
	}

	// Cache the result in Redis
	if redisExpiry > 0 && c.redisClient != nil && c.circuitBreaker != nil {
		// Use circuit breaker for Redis operations
		c.circuitBreaker.Call(func() error {
			// Create context with timeout for Redis operation
			redisCtx, cancel := context.WithTimeout(ctx, c.redisTimeout)
			defer cancel()

			// Marshal the result for Redis
			data, err := json.Marshal(result)
			if err != nil {
				return fmt.Errorf("failed to marshal data for Redis: %w", err)
			}

			err = c.redisClient.RedisClient.Set(redisCtx, prefixedKey, data, redisExpiry).Err()
			if err != nil {
				return fmt.Errorf("failed to set Redis cache: %w", err)
			}
			return nil
		})
	}

	fmt.Println("cache miss", prefixedKey)
	return result, nil
}

// getCachedDataTyped is the internal generic implementation
func getRedisCachedDataTyped[T any](
	c *Cache,
	ctx context.Context,
	key string,
	localExpiry time.Duration,
) (T, error) {

	if localExpiry > 0 {
		if cached, ok := c.localCache.LoadAndDelete(key); ok {
			if cacheEntry, ok := cached.(*cacheEntry); ok {
				if !cacheEntry.isExpired() {
					// Put back the non-expired entry
					c.localCache.Store(key, cacheEntry)

					// Type-safe conversion
					if typed, ok := cacheEntry.data.(T); ok {
						return typed, nil
					}
					// If type assertion fails, remove the entry and continue
					c.localCache.Delete(key)
					atomic.AddInt64(&c.currentSize, -1)
				} else {
					// Entry was expired, decrement counter since we deleted it
					atomic.AddInt64(&c.currentSize, -1)
				}
			}
		}
	}

	// Try Redis cache if expiry is set and Redis client is available
	if c.redisClient != nil && c.circuitBreaker != nil {
		var redisData T
		//var redisErr error

		// Use circuit breaker for Redis operations
		cbErr := c.circuitBreaker.Call(func() error {
			// Create context with timeout for Redis operation
			redisCtx, cancel := context.WithTimeout(ctx, c.redisTimeout)
			defer cancel()

			redisResp, err := c.redisClient.RedisClient.Get(redisCtx, key).Result()
			if err != nil {
				return err
			}

			// Create a new instance of T for unmarshaling
			var tempData T
			if err := json.Unmarshal([]byte(redisResp), &tempData); err != nil {
				return err
			}

			redisData = tempData
			return nil
		})

		if cbErr == nil {
			// Cache in local memory if local expiry is set
			if localExpiry > 0 {
				c.storeInLocalCache(key, redisData, localExpiry)
			}
			return redisData, nil
		} else if cbErr != nil {
			fmt.Printf("redis cache error: %v\n", cbErr)
		}
	}

	var tempData T
	_ = json.Unmarshal([]byte(""), &tempData)
	return tempData, nil
}

// Legacy method - kept for backward compatibility
func (c *Cache) GetCachedData(
	ctx context.Context,
	key string,
	fn func() (interface{}, error),
	localExpiry,
	redisExpiry time.Duration,
) (*CacheResult, error) {
	// Use the generic method internally and wrap the result
	result, err := getCachedDataTyped[interface{}](c, ctx, key, fn, localExpiry, redisExpiry)
	if err != nil {
		return nil, err
	}
	return &CacheResult{Data: result}, nil
}

// Legacy method - kept for backward compatibility
func (c *Cache) GetRedisCachedData(
	ctx context.Context,
	key string,
	localExpiry time.Duration,
) (*CacheResult, error) {
	// Use the generic method internally and wrap the result
	result, err := getRedisCachedDataTyped[interface{}](c, ctx, key, localExpiry)
	if err != nil {
		return nil, err
	}
	return &CacheResult{Data: result}, nil
}

// Helper function to create zero value of generic type T
func createZeroValue[T any]() T {
	var zero T
	return zero
}

// Helper function to get the type of T for better error messages
func getTypeName[T any]() string {
	var zero T
	return reflect.TypeOf(zero).String()
}

// storeInLocalCache stores data in local cache with size management
func (c *Cache) storeInLocalCache(key string, data interface{}, expiry time.Duration) {
	// Check if we need to make space
	if c.maxSize > 0 && atomic.LoadInt64(&c.currentSize) >= c.maxSize {
		c.cleanupExpired() // Try to make space by cleaning expired entries
	}

	// Only store if we have space
	if c.maxSize == 0 || atomic.LoadInt64(&c.currentSize) < c.maxSize {
		c.localCache.Store(key, newCacheEntry(data, expiry))
		atomic.AddInt64(&c.currentSize, 1)
	}
}

// startCleanupRoutine starts the background cleanup routine
func (c *Cache) startCleanupRoutine(interval time.Duration) {
	c.cleanupTicker = time.NewTicker(interval)
	go func() {
		for {
			select {
			case <-c.cleanupTicker.C:
				c.cleanupExpired()
			case <-c.stopCleanup:
				c.cleanupTicker.Stop()
				return
			case <-c.cleanupContext.Done():
				c.cleanupTicker.Stop()
				return
			}
		}
	}()
}

// cleanupExpired removes expired entries from the local cache
func (c *Cache) cleanupExpired() {
	var keysToDelete []interface{}

	c.localCache.Range(func(key, value interface{}) bool {
		if cacheEntry, ok := value.(*cacheEntry); ok && cacheEntry.isExpired() {
			keysToDelete = append(keysToDelete, key)
		}
		return true
	})

	// Delete expired entries
	for _, key := range keysToDelete {
		if _, existed := c.localCache.LoadAndDelete(key); existed {
			atomic.AddInt64(&c.currentSize, -1)
		}
	}
}

// GetCacheStats returns current cache statistics
func (c *Cache) GetCacheStats() map[string]interface{} {
	circuitState := "closed"
	if c.circuitBreaker != nil {
		switch atomic.LoadInt32(&c.circuitBreaker.state) {
		case 1:
			circuitState = "open"
		case 2:
			circuitState = "half-open"
		}
	}

	return map[string]interface{}{
		"local_cache_size":      atomic.LoadInt64(&c.currentSize),
		"max_size":              c.maxSize,
		"circuit_breaker_state": circuitState,
	}
}

// cacheEntry represents an entry in the local cache
type cacheEntry struct {
	data      interface{}
	expiresAt time.Time
}

func newCacheEntry(data interface{}, expiry time.Duration) *cacheEntry {
	return &cacheEntry{
		data:      data,
		expiresAt: time.Now().Add(expiry),
	}
}

func (e *cacheEntry) isExpired() bool {
	return time.Now().After(e.expiresAt)
}

// Clear removes an entry from both local and Redis cache
func (c *Cache) Clear(ctx context.Context, key string) error {
	envVar := config.GetEnv()
	prefixedKey := fmt.Sprintf("%s_%s", envVar, key)

	// Remove from local cache
	if _, existed := c.localCache.LoadAndDelete(prefixedKey); existed {
		atomic.AddInt64(&c.currentSize, -1)
	}
	err := c.redisClient.RedisClient.Del(ctx, prefixedKey).Err()
	return err
}

// ClearAll clears all entries from both local and Redis cache
func (c *Cache) ClearAll() error {
	// Reset local cache
	c.localCache = sync.Map{}
	atomic.StoreInt64(&c.currentSize, 0)

	// Note: Redis clear all would need to be implemented based on your Redis setup
	return nil
}

// Shutdown gracefully shuts down the cache, stopping background routines
func (c *Cache) Shutdown() {
	if c.stopCleanup != nil {
		close(c.stopCleanup)
	}
}
