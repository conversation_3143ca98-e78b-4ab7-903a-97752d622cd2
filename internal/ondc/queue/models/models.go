package models

import (
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/utils"
	"time"
)

type QueueItem struct {
	Data      interface{}
	TriggerAt time.Time
}
type TriggerFunc func() error

type QueueTriggerData struct {
	ID          string
	TriggerFunc TriggerFunc
	Data        interface{}
	TriggerAt   time.Time
	Repo        *sqlRepo.Repository
}

type QueueInsertParams struct {
	Data             interface{}
	ShouldAdjustTime bool
	TimeAdjustConfig *utils.TimeAdjustConfig
	TriggerAt        time.Time
	TriggerFunction  string
	TriggerFunc      func() error
	QueueID          *string
	CreateInDb       bool
}
