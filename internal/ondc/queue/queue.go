package queue

import (
	"container/heap"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/external/exotel/ivr"
	ivrorderintegration "kc/internal/ondc/external/exotel/ivrOrderIntegration"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	queueModels "kc/internal/ondc/queue/models"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/utils"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/go-sql-driver/mysql"
	"gorm.io/gorm"
)

var IVRQueueInstance *Queue
var OrderCancelQueueInstance *Queue
var TimedFunctionsQueueInstance *Queue

// PriorityQueueItem implements heap.Interface
type PriorityQueueItem struct {
	data      queueModels.QueueTriggerData
	triggerAt time.Time
	index     int
}

type PriorityQueue []*PriorityQueueItem

func HandleDbErr(err error) error {
	if err != nil {
		var mysqlErr *mysql.MySQLError
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return exceptions.GenerateNewServerError(exceptions.DBRecordNotFoundError, err, "not able to find results", http.StatusOK)
		} else if errors.As(err, &mysqlErr) && mysqlErr.Number == 1062 {
			return exceptions.GenerateNewServerError(exceptions.DBDuplicateKeyError, err, "key already exists", http.StatusOK)
		}

		return exceptions.GenerateNewServerError(exceptions.DBReadError, err, "not able to read level data", http.StatusInternalServerError)
	}
	return nil
}

// Heap interface implementation
func (pq PriorityQueue) Len() int { return len(pq) }
func (pq PriorityQueue) Less(i, j int) bool {
	return pq[i].triggerAt.Before(pq[j].triggerAt)
}
func (pq PriorityQueue) Swap(i, j int) {
	pq[i], pq[j] = pq[j], pq[i]
	pq[i].index = i
	pq[j].index = j
}
func (pq *PriorityQueue) Push(x interface{}) {
	n := len(*pq)
	item := x.(*PriorityQueueItem)
	item.index = n
	*pq = append(*pq, item)
}
func (pq *PriorityQueue) Pop() interface{} {
	old := *pq
	n := len(old)
	item := old[n-1]
	old[n-1] = nil  // avoid memory leak
	item.index = -1 // for safety
	*pq = old[0 : n-1]
	return item
}

type Queue struct {
	pq        PriorityQueue
	mutex     sync.RWMutex
	stopChan  chan struct{}
	maxSize   int           // Maximum queue size
	batchSize int           // Batch size for processing
	metrics   *QueueMetrics // For monitoring
}
type QueueMetrics struct {
	itemsProcessed uint64
	avgProcessTime time.Duration
	mutex          sync.RWMutex
}

func NewQueue(maxSize int, batchSize int) *Queue {
	if maxSize <= 0 {
		maxSize = 10000 // Default max size
	}
	if batchSize <= 0 {
		batchSize = 100 // Default batch size
	}

	q := &Queue{
		pq:        make(PriorityQueue, 0),
		stopChan:  make(chan struct{}),
		maxSize:   maxSize,
		batchSize: batchSize,
		metrics:   &QueueMetrics{},
	}

	heap.Init(&q.pq)
	go q.processQueue()
	return q
}

func (q *Queue) Insert(data queueModels.QueueTriggerData, triggerAt time.Time) error {
	q.mutex.Lock()
	defer q.mutex.Unlock()

	if len(q.pq) >= q.maxSize {
		return fmt.Errorf("queue is full (max size: %d)", q.maxSize)
	}

	item := &PriorityQueueItem{
		data:      data,
		triggerAt: triggerAt,
	}

	heap.Push(&q.pq, item)
	return nil
}

func (q *Queue) processQueue() {
	ticker := time.NewTicker(100 * time.Millisecond) // More frequent checks
	defer ticker.Stop()

	for {
		select {
		case <-q.stopChan:
			return
		case currentTime := <-ticker.C:
			q.processBatch(currentTime)
		}
	}
}

func (q *Queue) processBatch(currentTime time.Time) {
	q.mutex.Lock()
	defer q.mutex.Unlock()

	processed := 0
	startTime := time.Now()

	// Process items in batches
	for q.pq.Len() > 0 && processed < q.batchSize {
		item := q.pq[0]
		if currentTime.Before(item.triggerAt) {
			break
		}

		heap.Pop(&q.pq)
		go item.data.TriggerFunc() // Non-blocking trigger

		processed++
	}

	if processed > 0 {
		q.updateMetrics(processed, time.Since(startTime))
	}
}

func (q *Queue) triggerItem(item *PriorityQueueItem) {
	dataJSON, err := json.Marshal(item.data)
	if err != nil {
		// Log error but don't block processing
		fmt.Printf("Error marshaling data: %v\n", err)
		return
	}
	fmt.Printf("TRIGGERED: Function called with data: %s at: %v\n", string(dataJSON), time.Now())
}

func (q *Queue) updateMetrics(processed int, duration time.Duration) {
	q.metrics.mutex.Lock()
	defer q.metrics.mutex.Unlock()

	q.metrics.itemsProcessed += uint64(processed)
	q.metrics.avgProcessTime = (q.metrics.avgProcessTime + duration) / 2
}

func (q *Queue) Stop() {
	close(q.stopChan)
}

// Modify GetMetrics to return a map instead of uint64 and duration
func (q *Queue) GetMetrics() (map[string]interface{}, error) {
	q.metrics.mutex.RLock()
	defer q.metrics.mutex.RUnlock()

	return map[string]interface{}{
		"currentItems": len(q.pq),
		"maxItems":     q.maxSize,
		"memoryUsage":  float64(len(q.pq)) * 1024, // Rough estimate in bytes
		"maxMemory":    float64(q.maxSize) * 1024, // Max memory in bytes
	}, nil
}

// Add GetItems method to work with PriorityQueue
func (q *Queue) GetItems() []*queueModels.QueueItem {
	q.mutex.RLock()
	defer q.mutex.RUnlock()

	items := make([]*queueModels.QueueItem, len(q.pq))
	for i, item := range q.pq {
		items[i] = &queueModels.QueueItem{
			Data:      item.data,
			TriggerAt: item.triggerAt,
		}
	}

	return items
}

func PrefillQueueData(repo *sqlRepo.Repository, mp *mixpanelRepo.Repository) {
	// prefilling ivr queue
	// while prefilling make sure to add to queue the data whose time has already passed
	ivrQueueData := []dao.ExotelIVRQueueData{}
	_, err := repo.Find(map[string]interface{}{
		"status": "INITIATED",
	}, &ivrQueueData)
	if err != nil && err != HandleDbErr(gorm.ErrRecordNotFound) {
		log.Panic("not able to fetch data for exotel ivr queue, ", err)
	}

	for _, queueData := range ivrQueueData {
		queueElementID := queueData.ID
		// handle adding elements to queue
		qd := &dao.ExotelIVRTriggerData{}
		err = json.Unmarshal(queueData.Data, qd)
		if err != nil {
			log.Panic("not able to insert the data into queue", err)
		}
		data := map[string]interface{}{
			"custom_fields": qd.CustomFields,
			"from":          qd.From,
			"to":            qd.To,
			"app_id":        qd.AppID,
		}

		flowType := queueData.Type
		if flowType == "" {
			flowType = ivr.IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW
		}

		triggerData := queueModels.QueueTriggerData{
			TriggerFunc: func() error {
				ivr.CreateIVRCall(context.Background(), dto.IVRRequest{
					FromPhone:    qd.From,
					FlowID:       qd.AppID,
					ToPhone:      qd.To,
					CustomFields: qd.CustomFields,
					ID:           queueElementID,
				}, repo, mp, flowType)
				return nil
			},
			TriggerAt: qd.TriggerAt,
			Data:      data,
			Repo:      repo,
			ID:        queueElementID,
		}
		IVRQueueInstance.Insert(triggerData, qd.TriggerAt)
	}

	ivrOrderCancellationQueue := []dao.ExotelIVROrderCancellationData{}
	_, err = repo.Find(map[string]interface{}{
		"status": "INITIATED",
	}, &ivrOrderCancellationQueue)
	if err != nil && err != HandleDbErr(gorm.ErrRecordNotFound) {
		log.Panic("not able to fetch data for ivr order cancellation", err)
	}

	for _, orderCancellationData := range ivrOrderCancellationQueue {
		// handle adding elements to queue
		qd := &dto.WebhookCallDetails{}
		json.Unmarshal(orderCancellationData.MetaData, qd)
		customFields := dto.ExotelIVROrderInfo{}
		err := json.Unmarshal([]byte(qd.CustomField), &customFields)
		if err != nil {
			log.Panic("not able to unmarshal the data of custom feids", err)
		}
		OrderCancelQueueInstance.Insert(queueModels.QueueTriggerData{
			ID: orderCancellationData.ID,
			TriggerFunc: func() error {
				ivrorderintegration.CancelKiranaBazarOrderViaQueue(customFields, qd, repo, mp, orderCancellationData.CancellationReason, orderCancellationData.ID)
				return nil
			},
			Data:      orderCancellationData.MetaData,
			TriggerAt: orderCancellationData.CreatedAt.Add(time.Minute * 2),
		}, orderCancellationData.CreatedAt.Add(time.Minute*2))
	}

	fmt.Println("added data to all the queues")
	return

}

func AddDataToQueue(ctx context.Context, repo *sqlRepo.Repository, mp *mixpanelRepo.Repository, queueInsertParams queueModels.QueueInsertParams) (time.Time, error) {
	queueId := queueInsertParams.QueueID
	data := queueInsertParams.Data
	if queueId == nil {
		slack.SendSlackMessage(fmt.Sprintf("queueId is nil for %v", data))
		return time.Time{}, fmt.Errorf("queueId is nil")
	}
	triggerAt := queueInsertParams.TriggerAt
	triggerFunction := queueInsertParams.TriggerFunction
	if queueInsertParams.ShouldAdjustTime {
		triggerAt = utils.AdjustTime(triggerAt, queueInsertParams.TimeAdjustConfig)
	}

	triggerData := queueModels.QueueTriggerData{
		TriggerFunc: queueInsertParams.TriggerFunc,
		TriggerAt:   triggerAt,
		Data:        data,
		Repo:        repo,
		ID:          *queueId,
	}

	dataByt, err := json.Marshal(data)
	err = TimedFunctionsQueueInstance.Insert(triggerData, triggerAt)
	if err != nil {
		repo.Create(&dao.TimedFunctionsQueueData{
			QueueId: *queueId,
			Data:    dataByt,
			Status:  "FAILED_QUEUE_INSERTION",
		})
		return triggerAt, err
	}
	if queueInsertParams.CreateInDb {
		repo.Create(&dao.TimedFunctionsQueueData{
			QueueId:         *queueId,
			Source:          "DiagonAlley",
			Data:            dataByt,
			Status:          "INITIATED",
			TriggerAt:       &triggerAt,
			TriggerFunction: triggerFunction,
		})
	}
	return triggerAt, nil
}
