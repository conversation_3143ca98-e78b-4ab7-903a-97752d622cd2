package dao

import (
	"gorm.io/datatypes"
	"time"
)

type TimedFunctionsQueueDataBackup struct {
	ID        string         `gorm:"primaryKey;size:100" json:"id"`
	Source    string         `gorm:"size:20;not null" json:"source"`
	Data      datatypes.JSON `gorm:"type:json" json:"data"`
	CreatedAt *time.Time     `gorm:"type:timestamp" json:"created_at"`
	UpdatedAt *time.Time     `gorm:"type:timestamp" json:"updated_at"`
	Status    string         `gorm:"size:100;not null" json:"status"`
	Comment   *string        `gorm:"size:500" json:"comment"`
	Meta      datatypes.JSON `gorm:"type:json" json:"meta"`
}

type TimedFunctionsQueueData struct {
	ID              int            `gorm:"primaryKey" json:"id"`
	Source          string         `gorm:"size:20;not null" json:"source"`
	Data            datatypes.JSON `gorm:"type:json" json:"data"`
	CreatedAt       *time.Time     `gorm:"type:timestamp" json:"created_at"`
	UpdatedAt       *time.Time     `gorm:"type:timestamp" json:"updated_at"`
	Status          string         `gorm:"size:100;not null" json:"status"`
	Comment         *string        `gorm:"size:500" json:"comment"`
	Meta            datatypes.JSON `gorm:"type:json" json:"meta"`
	TriggerAt       *time.Time     `gorm:"type:timestamp" json:"trigger_at"`
	TriggerFunction string         `gorm:"size:40" json:"trigger_function"`
	QueueId         string         `gorm:"size:50" json:"queue_id"`
}

func (t *TimedFunctionsQueueData) TableName() string {
	return "timed_functions_queue_data"
}

func (t *TimedFunctionsQueueDataBackup) TableName() string {
	return "timed_functions_queue_data_backup"
}
