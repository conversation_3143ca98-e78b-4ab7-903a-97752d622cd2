package dao

import (
	"gorm.io/datatypes"
)

const (
	NDROrderTableName          = "kiranabazar_ndr_orders"
	OrderActivityLogsTableName = "kiranabazar_order_activity_logs"
	KiranaBazarNdrsTable       = "kiranabazar_ndrs"
)

type NDROrder struct {
	ID               int64   `json:"id" gorm:"column:id"`
	OrderID          int64   `json:"order_id" gorm:"column:order_id"`
	AssignedTo       string  `json:"assigned_to" gorm:"column:assigned_to"`
	CallConnected    *string `json:"call_connected" gorm:"column:call_connected"`
	ActionStatus     *string `json:"action_status" gorm:"column:action_status"`
	NDRUserReason    *string `json:"ndr_user_reason" gorm:"column:ndr_user_reason"`
	NextActionAt     *string `json:"next_action_at" gorm:"column:next_action_at"`
	EscalatedWith3PL *bool   `json:"escalated_with_3pl" gorm:"column:escalated_with_3pl"`
	Note             *string `json:"note" gorm:"column:note"`
	CreatedAt        int64   `json:"created_at" gorm:"column:created_at"` // Epoch in milliseconds
	UpdatedAt        int64   `json:"updated_at" gorm:"column:updated_at;autoUpdateTime:false"`
	UpdatedBy        string  `json:"updated_by" gorm:"column:updated_by"`
	OrderActivityID  string  `json:"order_activity_id" gorm:"column:order_activity_id"`
	VisibleFrom      *int64  `json:"visible_from" gorm:"column:visible_from"` // Epoch in milliseconds
	AttemptCount     *int    `json:"attempt_count" gorm:"column:attempt_count"`
}

type OrderActivityLogs struct {
	ID        string         `json:"id"`
	UpdatedAt int64          `json:"updated_at"`
	UpdatedBy string         `json:"updated_by"`
	Activity  datatypes.JSON `json:"activity"`
}

type Activity struct {
	MessageID string       `json:"message_id"`
	UpdatedAt int64        `json:"updated_at"`
	UpdatedBy string       `json:"updated_by"`
	Data      ActivityData `json:"data"`
}

type ActivityData struct {
	OrderID          int     `json:"order_id"`
	OrderStatus      string  `json:"order_status"`
	AssignedTo       string  `json:"assigned_to"`
	CallConnected    *string `json:"call_connected,omitempty"`
	ActionStatus     *string `json:"action_status,omitempty"`
	NextActionAt     *string `json:"next_action_at,omitempty"`
	EscalatedWith3PL *string `json:"escalated_with_3pl,omitempty"`
	Note             *string `json:"note,omitempty"`
	ReturnReason     *string `json:"returned_reason,omitempty"`
	CancelReason     *string `json:"cancel_reason,omitempty"`
	NDRAgentReason   *string `json:"ndr_agent_reason,omitempty"`
	NDRUserReason    *string `json:"ndr_user_reason,omitempty"`
	CallStatus       *string `json:"call_status,omitempty"`
	OrderValue       *int    `json:"order_value,omitempty"`
	NDRTag           *string `json:"ndr_tag,omitempty"`
	AttemptCount     *int    `json:"attempt_count,omitempty"`
	Source           *string `json:"source,omitempty"`
}

type KiranaBazarNdrs struct {
	ID                 int64   `json:"id" gorm:"column:id"`
	OrderID            int64   `json:"order_id" gorm:"column:order_id"`
	CFAction           *string `json:"cf_action" gorm:"column:cf_action"`
	CFCallConnected    *string `json:"cf_call_connected" gorm:"column:cf_call_connected"`
	CFNDRUserReason    *string `json:"cf_ndr_user_reason" gorm:"column:cf_ndr_user_reason"`
	CFNextAttemptAt    *int64  `json:"cf_next_attempt_at" gorm:"column:cf_next_attempt_at"`
	CFNote             *string `json:"cf_note" gorm:"column:cf_note"`
	CFAssignedTo       *string `json:"cf_assigned_to" gorm:"column:cf_assigned_to"`
	CFUpdatedBy        *string `json:"cf_updated_by" gorm:"column:cf_updated_by"`
	CFUpdatedAt        *int64  `json:"cf_updated_at" gorm:"column:cf_updated_at"`
	LMAction           *string `json:"lm_action" gorm:"column:lm_action"`
	LMNote             *string `json:"lm_note" gorm:"column:lm_note"`
	LMAssignedTo       *string `json:"lm_assigned_to" gorm:"column:lm_assigned_to"`
	LMUpdatedBy        *string `json:"lm_updated_by" gorm:"column:lm_updated_by"`
	LMUpdatedAt        *int64  `json:"lm_updated_at" gorm:"column:lm_updated_at"`
	NDRAttemptCount    *int64  `json:"ndr_attempt_count" gorm:"column:ndr_attempt_count"`
	OrderActionID      *string `json:"order_action_id" gorm:"column:order_action_id"`
	NDRAgentReason     *string `json:"ndr_agent_reason" gorm:"column:ndr_agent_reason"`
	LMOrderClosingNote *string `json:"lm_order_closing_note" gorm:"column:lm_order_closing_note"`
	CreatedSource      *string `json:"created_source" gorm:"column:created_source"`
	Source             *string `json:"source" gorm:"column:source"`
	Status             *string `json:"status" gorm:"column:status"` // open, closed
	CustomerEscalated  *bool   `json:"customer_escalated" gorm:"column:customer_escalated"`
	CreatedAt          *int64  `json:"created_at" gorm:"column:created_at"` // Epoch in milliseconds
}

type OrderActionLogs struct {
	ID        string         `json:"id"`
	UpdatedAt int64          `json:"updated_at"`
	UpdatedBy string         `json:"updated_by"`
	Actions   datatypes.JSON `json:"actions"`
}
type OrderAction struct {
	MessageID   string          `json:"message_id"`
	UpdatedAt   int64           `json:"updated_at"`
	UpdatedBy   string          `json:"updated_by"`
	MessageType string          `json:"message_type"`
	Data        OrderActionData `json:"data"`
}

type OrderActionData struct {
	OrderID        int      `json:"order_id"`
	OrderStatus    string   `json:"order_status"`
	CFAssignedTo   string   `json:"cf_assigned_to"`
	LMAssignedTo   string   `json:"lm_assigned_to"`
	CallConnected  *string  `json:"call_connected,omitempty"`
	Action         *string  `json:"action_status,omitempty"`
	NextActionAt   *string  `json:"next_action_at,omitempty"`
	Note           *string  `json:"note,omitempty"`
	ReturnReason   *string  `json:"returned_reason,omitempty"`
	CancelReason   *string  `json:"cancel_reason,omitempty"`
	NDRAgentReason *string  `json:"ndr_agent_reason,omitempty"`
	NDRUserReason  *string  `json:"ndr_user_reason,omitempty"`
	OrderValue     *float64 `json:"order_value,omitempty"`
	AttemptCount   *int     `json:"attempt_count,omitempty"`
	Source         *string  `json:"source,omitempty"`
}

func (*NDROrder) TableName() string {
	return NDROrderTableName
}

func (*OrderActivityLogs) TableName() string {
	return OrderActivityLogsTableName
}

func (*OrderActionLogs) TableName() string {
	return OrderActivityLogsTableName
}

func (*KiranaBazarNdrs) TableName() string {
	return KiranaBazarNdrsTable
}
