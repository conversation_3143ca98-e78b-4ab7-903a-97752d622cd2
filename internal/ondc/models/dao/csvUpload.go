package dao

import "gorm.io/datatypes"

const KiranaBazarBulkActionTableName = "kiranabazar_bulk_action_logs"

type KiranaBazarBulkActionLogs struct {
	ID        *string        `json:"id"`
	UpdatedAt *int64         `json:"updated_at"`
	UpdatedBy *string        `json:"updated_by"`
	Seller    *string        `json:"seller"`
	Type      *string        `json:"type"`
	Meta      datatypes.JSON `json:"meta"`
	Status    *string        `json:"status"`
}

func (*KiranaBazarBulkActionLogs) TableName() string {
	return KiranaBazarBulkActionTableName
}
