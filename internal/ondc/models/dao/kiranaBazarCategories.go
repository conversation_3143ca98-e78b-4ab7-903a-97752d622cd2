package dao

import "time"

type KiranaBazarCategory struct {
	ID        int       `json:"id"`
	Domain    string    `json:"domain"`
	SubDomain string    `json:"sub_domain"`
	Category  string    `json:"category"`
	Code      string    `json:"code"`
	ImageURL  string    `json:"image_url"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Source    string    `json:"source"`
	Rank      int       `json:"rank"`
}

func (oc *KiranaBazarCategory) TableName() string {
	return "kiranabazar_categories"
}
