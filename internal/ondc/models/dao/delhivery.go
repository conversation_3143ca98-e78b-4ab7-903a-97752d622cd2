package dao

import (
	"time"

	"gorm.io/datatypes"
)

type DelhiveryWebhookLogs struct {
	ID            uint64         `json:"id"`
	RequestObject datatypes.JSON `json:"request_object"`
	CreatedAt     time.Time      `json:"created_at"`
	AccountName   string         `json:"account_name"`
}

type DelhiveryFetchShipmetLogs struct {
	ID        uint64         `json:"id"`
	Request   datatypes.JSON `json:"request"`
	Response  datatypes.JSON `json:"response"`
	CreatedAt time.Time      `json:"created_at"`
}

func (*DelhiveryWebhookLogs) TableName() string {
	return "delhivery_webhook_logs"
}

func (*DelhiveryFetchShipmetLogs) TableName() string {
	return "delhivery_fetch_shipment_logs"
}
