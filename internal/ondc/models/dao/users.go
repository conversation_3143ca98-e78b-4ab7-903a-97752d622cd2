package dao

import (
	"time"
)

// Table Name for UserAddress
const UserAddressTableName = "user_address"
const UserTableName = "users"

// Primary table to store user address
type UserAddress struct {
	ID             *uint64   `json:"id"`
	Name           *string   `json:"name"`
	Phone          *string   `json:"phone"`
	AlternatePhone *string   `json:"alternate_phone"`
	UserID         *string   `json:"user_id"`
	Line           *string   `json:"line"`
	District       *string   `json:"district"`
	State          *string   `json:"state"`
	PostalCode     *string   `json:"postal_code"`
	Tag            *string   `json:"tag"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	IsActive       bool      `json:"is_active"`
	IsDefault      bool      `json:"is_default"`
	Latitude       float64   `json:"latitude"`
	Longitude      float64   `json:"longitude"`
	GST            string    `json:"gst"`
	HouseNumber    string    `json:"house_number"`
	Neighbourhood  string    `json:"neighbourhood"`
	Village        string    `json:"village"`
	Landmark       string    `json:"landmark"`
	Line1          string    `json:"line1"`
	Line2          string    `json:"line2"`
	StoreName      string    `json:"store_name"`
	BillingAddress *uint64   `json:"billing_address"`
}

type User struct {
	ID                int64   `json:"id" gorm:"column:id"`
	UserID            string  `json:"user_id" gorm:"column:user_id"`
	Phone             *string `json:"phone" gorm:"column:phone"`
	Name              string  `json:"name" gorm:"column:name"`
	StoreName         string  `json:"store_name" gorm:"column:store_name"`
	City              string  `json:"city" gorm:"column:city"`
	State             string  `json:"state" gorm:"column:state"`
	UserType          string  `json:"user_type" gorm:"column:user_type"`
	CreatedAt         *int64  `json:"created_at" gorm:"column:created_at"`
	UpdatedAt         *int64  `json:"updated_at" gorm:"column:updated_at"`
	Pincode           *string `json:"pincode" gorm:"column:pincode"`
	LastSeen          *int64  `json:"last_seen" gorm:"column:last_seen"`
	ProfileImageURL   string  `json:"profile_image_url" gorm:"column:profile_image_url"`
	LastUninstalledAt *int64  `json:"last_uninstalled_at" gorm:"column:last_uninstalled_at"`
}

func (User) TableName() string {
	return UserTableName
}

func (*UserAddress) TableName() string {
	return UserAddressTableName
}
