package dao

import (
	"time"

	"gorm.io/datatypes"
)

type ShipwayOrderTracking struct {
	ID             uint64    `json:"id"`
	OrderID        int64     `json:"order_id"`
	AwbNumber      string    `json:"awb_number"`
	CurrentStatus  string    `json:"current_status"`
	CourierName    string    `json:"courier_name"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	IsActive       bool      `json:"is_active"`
	LogsID         uint64    `json:"logs_id"`
	ShipwayOrderID string    `json:"shipway_order_id"`
}

type ShipwayAPILogs struct {
	ID         uint64         `json:"id"`
	Request    datatypes.JSON `json:"request"`
	Response   datatypes.JSON `json:"response"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	StatusCode int            `json:"status_code"`
	Endpoint   string         `json:"endpoint"`
}

type ShipwayWebhookLogs struct {
	ID        uint64         `json:"id"`
	Request   datatypes.JSON `json:"request"`
	CreatedAt time.Time      `json:"created_at"`
	Type      string         `json:"type"`
}

type ShipwayFetchShipmentLogs struct {
	ID        uint64         `json:"id"`
	Request   datatypes.JSON `json:"request"`
	Response  datatypes.JSON `json:"response"`
	CreatedAt time.Time      `json:"created_at"`
}

func (*ShipwayFetchShipmentLogs) TableName() string {
	return "shipway_fetch_shipment_logs"
}
func (*ShipwayOrderTracking) TableName() string {
	return "shipway_order_tracking"
}

func (*ShipwayAPILogs) TableName() string {
	return "shipway_api_logs"
}

func (*ShipwayWebhookLogs) TableName() string {
	return "shipway_webhook_logs"
}
