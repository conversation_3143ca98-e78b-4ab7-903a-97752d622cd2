package dao

type KiranaBazarReconciliation struct {
	OrderID                   int64   `json:"order_id"`
	OMS                       string  `json:"oms"`
	GrossValue                float64 `json:"gross_value"`
	OrderValue                float64 `json:"order_value"`
	CartValue                 float64 `json:"cart_value"`
	GTV                       float64 `json:"gtv"`
	OrderPlaced               int64   `json:"order_placed"`
	OrderUpdated              *int64  `json:"order_updated"`          // nullable
	OrderConfirmed            *int64  `json:"order_confirmed"`        // nullable
	OrderDelivered            *int64  `json:"order_delivered"`        // nullable
	OrderShipmentCreated      *int64  `json:"order_shipment_created"` // nullable
	OrderDispatched           *int64  `json:"order_dispatched"`
	OrderOfd                  *int64  `json:"order_ofd"`
	OrderReturned             *int64  `json:"order_returned"` // nullable
	ExpectedDeliveryTimestamp int64   `json:"expected_delivery_timestamp"`
	CreatedAt                 int64   `json:"created_at"`
	UpdatedAt                 int64   `json:"updated_at"`
	OrderCancelled            *int64  `json:"order_cancelled"` // nullable
	DiscountValue             float64 `json:"discount_value"`
}

type KiranaBazarDailyReconciliation struct {
	ID                   int64   `json:"id"`
	Date                 string  `json:"date"`
	Brand                string  `json:"brand"`
	OrderPlaced          int64   `json:"order_placed"`
	OrderConfirmed       int64   `json:"order_confirmed"`
	OrderDelivered       int64   `json:"order_delivered"`
	OrderShipmentCreated int64   `json:"order_shipment_created"`
	OrderReturned        int64   `json:"order_returned"`
	OrderValue           float64 `json:"order_value"`
	OrderGrossValue      float64 `json:"order_gross_value"`
	OrderCancelled       int64   `json:"order_cancelled"`
}

func (u *KiranaBazarReconciliation) TableName() string {
	return "kc_bazar_reconciliation"
}

func (u *KiranaBazarDailyReconciliation) TableName() string {
	return "kc_bazar_daily_reconciliation"
}
