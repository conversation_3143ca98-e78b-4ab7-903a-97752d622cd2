package dao

import (
	"time"

	"gorm.io/datatypes"
)

// KiranaBazarIssue represents issue table in sql
type KiranaBazarIssue struct {
	ID            string    `json:"id"`
	TransactionID string    `json:"transaction_id"`
	Type          string    `json:"type"`
	SubCategory   string    `json:"sub_category"`
	Category      string    `json:"category"`
	OrderID       uint64    `json:"order_id"`
	ONDCOrderID   string    `json:"ondc_order_id"`
	Status        string    `json:"status"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// KiranaBazarIssueMessage represents issue messages table in sql
type KiranaBazarIssueMessage struct {
	ID             uint64         `json:"id"`
	IssueID        string         `json:"issue_id"`
	RespondentInfo datatypes.JSON `json:"respondent_info"`
	Action         datatypes.JSON `json:"action"`
	CreatedAt      time.Time      `json:"created_at"`
	Meta           datatypes.JSON `jsonm:"meta"`
}

// return sql table name for kiranaBazarIssue struct
func (kbi *KiranaBazarIssue) TableName() string {
	return "kiranabazar_issues"
}

//  returns sql table name for kiranaBazarIssueMessage struct
func (kbim *KiranaBazarIssueMessage) TableName() string {
	return "kiranabazar_issues_messages"
}
