package dao

import (
	"time"

	"gorm.io/datatypes"
)

type ProductReviews struct {
	ReviewID    string         `gorm:"column:id;primaryKey;type:uuid;default:uuid_generate_v4()" json:"id"`
	UserID      string         `gorm:"column:user_id;type:uuid;not null;index" json:"user_id"`
	ProductID   int64          `gorm:"column:product_id;not null;index" json:"product_id"`
	StarRating  int8           `gorm:"column:star_rating;type:smallint;not null;check:star_rating >= 1 AND star_rating <= 5" json:"star_rating"`
	ReviewText  string         `gorm:"column:review_text;type:text" json:"review_text"`
	ReviewMedia datatypes.JSON `gorm:"column:review_media;type:jsonb;default:'{}'" json:"review_media"`
	CreatedAt   time.Time      `gorm:"column:created_at;type:timestamp;default:current_timestamp;not null" json:"created_at"`
	UpdatedAt   time.Time      `gorm:"column:updated_at;type:timestamp;default:current_timestamp;not null" json:"updated_at"`
	IsVisible   bool           `gorm:"column:is_visible;type:boolean;default:true;not null" json:"is_visible"`
	Relevancy   int64          `gorm:"column:relevancy;type:bigint;default:0" json:"relevancy"`
	UserName    string         `gorm:"column:name;type:varchar(255)" json:"name"`
	UserCity    string         `gorm:"column:city;type:varchar(100)" json:"city"`
	UserState   string         `gorm:"column:state;type:varchar(50)" json:"state"`
}

type ProductReview struct {
	ID         string    `json:"id"`
	UserID     string    `json:"user_id"`
	ProductID  int64     `json:"product_id"`
	StarRating int8      `json:"star_rating"`
	ReviewText string    `json:"review_text"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	IsVisible  bool      `json:"is_visible"`
	Relevancy  int64     `json:"relevancy"`
}

type TopProductReviewVideos struct {
	ReviewID     string  `gorm:"column:review_id;primaryKey;type:uuid" json:"review_id"`
	ProductID    int64   `gorm:"column:product_id;not null;index" json:"product_id"`
	CategoryID   *uint64 `gorm:"column:category_id" json:"category_id"`
	StarRating   int8    `gorm:"column:star_rating;type:smallint;not null;check:star_rating >= 1 AND star_rating <= 5" json:"star_rating"`
	Text         string  `gorm:"column:review_text;type:text" json:"review_text"`
	MediaURL     string  `gorm:"column:media_url;type:varchar(300)" json:"media_url"`
	ThumbnailURL string  `gorm:"column:thumbnail_url;type:varchar(200)" json:"thumbnail_url"`
	UserID       string  `gorm:"column:user_id;type:uuid;not null;index" json:"user_id"`
	UserName     string  `gorm:"column:name;type:varchar(255)" json:"name"`
	UserCity     string  `gorm:"column:city;type:varchar(100)" json:"city"`
	UserState    string  `gorm:"column:state;type:varchar(50)" json:"state"`
}

type ProductReviewsMedia struct {
	ReviewID    string         `json:"review_id"`
	ReviewMedia datatypes.JSON `json:"review_media"`
}

type ProductRatings struct {
	ProductID    *uint64 `json:"id"`
	CategoryID   *int64  `json:"category_id"`
	RatingsSum   int64   `json:"ratings_sum"`
	RatingsCount int64   `json:"ratings_count"`
}

type ReviewMedia struct {
	Type         string `json:"type"`
	URI          string `json:"uri"`
	ThumbnailURL string `json:"thumbnail_url,omitempty"`
	GifURL       string `json:"gif_url,omitempty"`
	JobID        string `json:"job_id,omitempty"`
}


func (ProductReview) TableName() string {
	return "kiranabazar_product_reviews"
}

func (ProductReviewsMedia) TableName() string {
	return "kiranabazar_product_reviews_media"
}