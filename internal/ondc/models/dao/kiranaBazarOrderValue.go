package dao

import (
	"time"

	"gorm.io/datatypes"
)

type KiranabazarMinOrdervalueRules struct {
	ID          int64          `json:"id"`
	Seller      string         `json:"seller"`
	OrderNumber int            `json:"order_number"`
	MinAmount   float64        `json:"min_amount"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	Meta        datatypes.JSON `json:"meta"`
	IsActive    bool           `json:"is_active"`
	Cohort      string         `json:"cohort"`
}

type KiranabazarMinOrdervalueRulesMetaData struct {
	BannerImageUrl *string `json:"banner_image_url"`
	PositionIndex  *int    `json:"position_index"`
}

func (kmor *KiranabazarMinOrdervalueRules) TableName() string {
	return "kiranabazar_min_ordervalue_rules"
}
