package dao

import "time"

// InvoiceCounter represents the database model for tracking invoice counters
type InvoiceCounter struct {
	ID                 uint      `json:"id"`
	SellerCode         string    `json:"seller_code" gorm:"type:varchar(3);not null;index:idx_seller_fy,unique"`
	FinancialYear      string    `json:"financial_year" gorm:"type:varchar(4);not null;index:idx_seller_fy,unique"`
	LastSequenceNumber int       `json:"last_sequence_number"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// TableName sets the table name for the model
func (InvoiceCounter) TableName() string {
	return "kiranabazar_invoice_counters"
}
