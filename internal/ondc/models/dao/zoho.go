package dao

import (
	"time"

	"gorm.io/datatypes"
)

type ZohoApiLogs struct {
	ID             uint64         `json:"id"`
	Request        string         `json:"request"`
	RequestObject  datatypes.JSON `json:"request_object"`
	ResponseObject datatypes.JSON `json:"response_object"`
	CreatedAt      time.Time      `json:"created_at"`
}

type ZohoWebhookLogs struct {
	ID            uint64         `json:"id"`
	RequestObject datatypes.JSON `json:"request_object"`
	Type          string         `json:"type"`
	CreatedAt     time.Time      `json:"created_at"`
}

type ZohoContactDetails struct {
	ID        string         `json:"id"`
	Data      datatypes.JSON `json:"data"`
	IsActive  bool           `json:"is_active"`
}

type ZohoTicket struct {
	ID        string         `json:"id"`
	Data      datatypes.JSO<PERSON> `json:"data"`
	IsActive  bool           `json:"is_active"`
}

type ZohoTicketComment struct {
	ID        string         `json:"id"`
	Data      datatypes.<PERSON><PERSON><PERSON> `json:"data"`
	IsActive  bool           `json:"is_active"`
}

type ZohoTicketAttachment struct {
	ID        string         `json:"id"`
	Data      datatypes.JSON `json:"data"`
	IsActive  bool           `json:"is_active"`
}

func (u *ZohoApiLogs) TableName() string {
	return "zoho_api_logs"
}

func (u *ZohoWebhookLogs) TableName() string {
	return "zoho_webhook_logs"
}

func (u *ZohoContactDetails) TableName() string {
	return "zoho_contact_details"
}
func (u *ZohoTicket) TableName() string {
	return "zoho_tickets"
}
func (u *ZohoTicketComment) TableName() string {
	return "zoho_ticket_comments"
}
func (u *ZohoTicketAttachment) TableName() string {
	return "zoho_ticket_attachments"
}
