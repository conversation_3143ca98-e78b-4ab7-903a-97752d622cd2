package dao

import "gorm.io/datatypes"

type CSTicket struct {
	ID                string         `gorm:"column:id" json:"id"`
	OrderID           int            `gorm:"column:order_id" json:"order_id"`
	UserID            string         `gorm:"column:user_id" json:"user_id"`
	CreatedAt         int64          `gorm:"column:created_at;autoUpdateTime:false" json:"created_at"`
	UpdatedAt         int64          `gorm:"column:updated_at;autoUpdateTime:false" json:"updated_at"`
	UpdatedBy         string         `gorm:"column:updated_by" json:"updated_by"`
	PrimaryOwnerID    string         `gorm:"column:primary_owner_id" json:"primary_owner_id"`
	Priority          *string        `gorm:"column:priority" json:"priority"`
	Status            *string        `gorm:"column:status" json:"status"`
	UserContactID     *string        `gorm:"column:user_contact_id" json:"user_contact_id"`
	Category          *int           `gorm:"column:category;not null" json:"category"`
	Active            *bool          `gorm:"column:active;default:true" json:"active"`
	Archived          bool           `gorm:"column:archived;default:false" json:"archived"`
	Meta              datatypes.JSON `gorm:"column:meta" json:"meta"`
	ConversationID    string         `gorm:"column:conversation_id" json:"conversation_id"`
	Seller            *string        `gorm:"column:seller" json:"seller"`
	CurrentAssigneeID *string        `gorm:"column:current_assignee_id" json:"current_assignee_id"`
	OwnershipState    *string        `gorm:"column:ownership_state;default:cs" json:"ownership_state"`
	ProgressState     *string        `gorm:"column:progress_state;default:new" json:"progress_state"`
	Source            *string        `gorm:"column:source" json:"source"`
}

type CSAttachment struct {
	ID               string          `gorm:"column:id" json:"id"`
	MessageID        string          `gorm:"column:message_id" json:"message_id"`
	FileType         string          `gorm:"column:file_type" json:"file_type"`
	FileURL          string          `gorm:"column:file_url" json:"file_url"`
	FileSize         int             `gorm:"column:file_size" json:"file_size"`
	CreatedAt        int64           `gorm:"column:created_at" json:"created_at"`
	UpdatedAt        int64           `gorm:"column:updated_at;autoUpdateTime:false" json:"updated_at"`
	FileThumbnailURL *string         `gorm:"column:file_thumbnail_url" json:"file_thumbnail_url"`
	Private          bool            `gorm:"column:private;default:false" json:"private"`
	Meta             *datatypes.JSON `gorm:"column:meta" json:"meta"`
	ContentType      *string         `gorm:"column:content_type" json:"content_type"`
	FileRawURL       *string         `gorm:"column:file_raw_url" json:"file_raw_url"`
}

type CSMessage struct {
	ID             string  `gorm:"column:id" json:"id"`
	ConversationID string  `gorm:"column:conversation_id" json:"conversation_id"`
	Content        *string `gorm:"column:content" json:"content"`
	SenderID       string  `gorm:"column:sender_id" json:"sender_id"`
	SenderType     string  `gorm:"column:sender_type" json:"sender_type"`
	Private        bool    `gorm:"column:private" json:"private"`
	MessageType    *string `gorm:"column:message_type" json:"message_type"`
	CreatedAt      int64   `gorm:"column:created_at" json:"created_at"`
	UpdatedAt      int64   `gorm:"column:updated_at;autoUpdateTime:false" json:"updated_at"`
	Visible        *bool   `gorm:"column:visible;default:true" json:"visible"`
	Status         *string `gorm:"column:status" json:"status"`
}

type CSAgent struct {
	ID             string  `gorm:"column:id" json:"id"`
	Name           string  `gorm:"column:name" json:"name"`
	Role           string  `gorm:"column:role" json:"role"`
	AvatarURL      *string `gorm:"column:avatar_url" json:"avatar_url"`
	Email          string  `gorm:"column:email" json:"email"`
	Seller         *string `gorm:"column:seller" json:"seller"`
	Status         *string `gorm:"column:status" json:"status"`
	CreatedAt      uint64  `gorm:"column:created_at" json:"created_at"`
	UpdatedAt      uint64  `gorm:"column:updated_at;autoUpdateTime:false" json:"updated_at"`
	Team           *string `gorm:"column:team" json:"team"`
	LastActivityAt *int64  `gorm:"column:last_activity_at" json:"last_activity_at"`
	Active         bool    `gorm:"column:active" json:"active"`
}

type CsTicketCategory struct {
	ID       int     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Code     string  `gorm:"column:code;not null" json:"code"`
	Text     string  `gorm:"column:text;not null" json:"text"`
	Type     *string `gorm:"column:type" json:"type"`
	IsActive bool    `gorm:"column:is_active;not null;default:1" json:"is_active"`
	Name     string  `gorm:"column:name;not null" json:"name"`
	ParentID *int    `gorm:"column:parent_id" json:"parent_id"`
}

type CSTicketAction struct {
	ID                 string         `gorm:"column:id" json:"id"`
	TicketID           string         `gorm:"column:ticket_id;not null" json:"ticket_id"`
	RequestedAction    string         `gorm:"column:requested_action;not null" json:"requested_action"`
	RequestedBy        string         `gorm:"column:requested_by;not null" json:"requested_by"`
	RequestedAt        int64          `gorm:"column:requested_at;not null" json:"requested_at"`
	RequestedMessageID *int64         `gorm:"column:requested_message_id" json:"requested_message_id,omitempty"`
	ResponseAction     *string        `gorm:"column:response_action" json:"response_action,omitempty"`
	ResponseBy         *string        `gorm:"column:response_by" json:"response_by,omitempty"`
	ResponseAt         *int64         `gorm:"column:response_at" json:"response_at,omitempty"`
	ResponseMessageID  *int64         `gorm:"column:response_message_id" json:"response_message_id,omitempty"`
	Status             string         `gorm:"column:status;not null" json:"status"`
	CreatedAt          int64          `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt          int64          `gorm:"column:updated_at;autoUpdateTime:false" json:"updated_at"`
	ConversationID     string         `gorm:"column:conversation_id;not null" json:"conversation_id"`
	RequestedMeta      datatypes.JSON `gorm:"column:requested_meta" json:"requested_meta,omitempty"`
	ResponseMeta       datatypes.JSON `gorm:"column:response_meta" json:"response_meta,omitempty"`
}

type CSRating struct {
	ID        int            `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	OrderID   *int           `gorm:"column:order_id" json:"order_id"`
	TicketID  *string        `gorm:"column:ticket_id" json:"ticket_id"`
	Rating    *float32       `gorm:"column:rating" json:"rating"`
	Feedback  *string        `gorm:"column:feedback" json:"feedback"`
	UserID    string         `gorm:"column:user_id;not null" json:"user_id"`
	CreatedAt uint64         `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt uint64         `gorm:"column:updated_at;autoUpdateTime:false" json:"updated_at"`
	Meta      datatypes.JSON `gorm:"column:meta" json:"meta"`
}

type CSMedia struct {
	Type         string `json:"file_type"`
	URI          string `json:"file_url"`
	ThumbnailURL string `json:"file_thumbnail_url,omitempty"`
	GifURL       string `json:"gif_url,omitempty"`
	JobID        string `json:"job_id,omitempty"`
}

func (*CSRating) TableName() string {
	return "cs_ratings"
}

func (*CSTicketAction) TableName() string {
	return "cs_ticket_actions"
}

func (*CsTicketCategory) TableName() string {
	return "cs_ticket_category"
}

func (*CSAgent) TableName() string {
	return "cs_agents"
}

func (*CSMessage) TableName() string {
	return "cs_messages"
}

func (*CSTicket) TableName() string {
	return "cs_tickets"
}

func (*CSAttachment) TableName() string {
	return "cs_attachments"
}
