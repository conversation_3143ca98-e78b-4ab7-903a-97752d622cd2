package dao

import (
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/helper"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/offers"

	"math"
	"strconv"
	"time"

	"gorm.io/datatypes"
)

// Table Names for SQL
const (
	KiranaBazarOrderTableName            = "kiranabazar_orders"
	KiranaBazarOrderPaymentTableName     = "kiranabazar_order_payments"
	KiranaBazarOrderStatusTableName      = "kiranabazar_order_status"
	KiranaBazarOrderDetailTableName      = "kiranabazar_order_details"
	KiranaBazarEasyEcomOrderTableName    = "kiranabazar_easyecom_order"
	KiranaBazarUnicommerceOrderTableName = "kiranabazar_unicommerce_order"
	KiranaBazarOrderRefundsTableName     = "kiranabazar_order_refunds"
)

// Primary table for KiranaBazar orders
type KiranaBazarOrder struct {
	ID               *int64    `json:"id"`
	TransactionID    *string   `json:"transaction_id"`
	MessageID        *string   `json:"message_id"`
	OrderStatus      *string   `json:"order_status"`
	TrackingLink     *string   `json:"tracking_link"`
	UserID           *string   `json:"user_id"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
	Seller           string    `json:"seller"`
	PaymentStatus    string    `json:"payment_status"`
	DeliveryStatus   string    `json:"delivery_status"`
	DisplayStatus    string    `json:"display_status"`
	ProcessingStatus string    `json:"processing_status"`
}

type OrderDetailsForIVR struct {
	CustomerPhone string `gorm:"column:customer_phone"`
	UserID        string `gorm:"column:user_id"`
	DisplayStatus string `gorm:"column:display_status"`
	UserName      string `gorm:"column:name"`
	OrderID       int    `gorm:"column:order_id"`
	Seller        string `gorm:"column:seller"`
}

// Primary table to store payment details/history for KiranaBazar orders
type KiranaBazarOrderPayment struct {
	ID               *string        `json:"id"`
	OrderID          *int64         `json:"order_id"`
	PaymentMethod    *string        `json:"payment_method"`
	TransactionID    *string        `json:"transaction_id"`
	Amount           *float64       `json:"amount"`
	PaidAmount       *float64       `json:"paid_amount"`
	PaymentMeta      datatypes.JSON `json:"payment_meta"`
	Status           *string        `json:"status"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	KCInvoice        string         `json:"kc_invoice"`
	ExtInvoice       string         `json:"ext_invoice"`
	ExtInvoiceNumber string         `json:"ext_invoice_number"`
	Source           string         `json:"source"`
	RefundAmount     *float64       `json:"refund_amount"`
	RefundID         *string        `json:"refund_id"`
	PaymentId        *string        `json:"payment_id"`
	RNN              *string        `json:"rnn"`
}

type PaidAmountProof struct {
	Url       string `json:"url"`
	UpdatedAt string `json:"updated_at"`
	UpdatedBy string `json:"updated_by"`
}

type PaymentMeta struct {
	AdvanceTaken    bool              `json:"advance_taken"`
	PaidAmountProof []PaidAmountProof `json:"paid_amount_proof"`
}

// Primary table to maintain the KiranaBazar order status
type KiranaBazarOrderStatus struct {
	ID                   *int64         `json:"id"`
	Status               datatypes.JSON `json:"status"`
	CreatedAt            *time.Time     `json:"created_at"`
	UpdatedAt            time.Time      `json:"updated_at"`
	ExpectedDeliveryDate *time.Time     `json:"expected_delivery_date"`
	Error                *string        `json:"error"`
	Courier              *string        `json:"courier"`
	AWBNumber            *string        `json:"awb_number"`
	AWBNumbers           datatypes.JSON `json:"awb_numbers"`
}

type DiscountsByProvider struct {
	TotalDiscount    float64
	PlatformDiscount float64
	SellerDiscount   float64
	PlatformCashback float64
	PaymentDiscount  float64
	MarkdownDiscount float64
}

func (orderStatus *KiranaBazarOrderStatus) GetAWBNumbers() []string {
	awbNumbers := []string{}
	err := json.Unmarshal(orderStatus.AWBNumbers, &awbNumbers)
	if err != nil {
		return []string{}
	}
	return awbNumbers
}

// Secondary table to maintain KiranaBazar order details
type KiranaBazarOrderDetail struct {
	ID            *uint64        `json:"id"`
	OrderID       *int64         `json:"order_id"`
	OrderDetails  datatypes.JSON `json:"order_details"`
	UpdatedAt     time.Time      `json:"updated_at"`
	OnSelect      datatypes.JSON `json:"on_select"`
	OnInit        datatypes.JSON `json:"on_init"`
	OnConfirm     datatypes.JSON `json:"on_confirm"`
	PrintingLabel string         `json:"printing_label"`
	Picklist      string         `json:"picklist"`
}

type KiranaBazarOrderDetails struct {
	Cart            []shared.SellerItems     `json:"cart"`
	VirtualSkusCart []shared.SellerItems     `json:"virtual_skus_cart,omitempty"`
	ShippingAddress UserAddress              `json:"shipping_address"`
	BillingAddress  *UserAddress             `json:"billing_address"`
	TotalItems      int                      `json:"total_items"`
	TotalAmount     float64                  `json:"total_amount"`
	BillBreakUp     BillDetails              `json:"bill_breakup"`
	DeliveryStatus  []KiranaBazarOrderStatus `json:"delivery_status"`
	ONDCOrderID     string                   `json:"ondc_order_id"`
	Seller          string                   `json:"seller"`
	PackageDetails  *shared.PackageDetails   `json:"package_details"`
}

type KiranaBazarOrderTags struct {
	ID          uint64    `json:"id"`
	Tag         string    `json:"tag"`
	Description string    `json:"description"`
	IsActive    bool      `json:"is_active"`
	UpdatedBy   string    `json:"updated_by"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type KiranaBazarOrderTagMapping struct {
	ID       uint64 `json:"id"`
	OrderID  int64  `json:"order_id"`
	TagID    int    `json:"tag_id"`
	IsActive bool   `json:"is_active"`
}

type KiranaBazarOrderDetailsTags struct {
	TagId int    `json:"tag_id"`
	Tag   string `json:"tag"`
}

type BillDetails struct {
	ProductsPricing []shared.ProductPrice    `json:"products_pricing"`
	TotalPricing    []shared.TotalPricing    `json:"total_pricing"`
	DiscountPricing []shared.DiscountPricing `json:"discount_pricing"`
	EnableOrdering  bool                     `json:"enable_ordering"`
	Message         string                   `json:"message"`
	Coupon          string                   `json:"coupon"`
}

// Kiranan
type KiranaBazarEasyEcomOrder struct {
	ID            uint64         `json:"id"`
	OrderID       int            `json:"order_id"`
	OrderResponse datatypes.JSON `json:"order_response"`
	OrderRequest  datatypes.JSON `json:"order_request"`
	CreatedAt     time.Time      `json:"created_at"`
	OMS           string         `json:"oms"`
}

type KiranaBazarUnicommerceOrder struct {
	ID            uint64         `json:"id"`
	OrderID       int            `json:"order_id"`
	OrderResponse datatypes.JSON `json:"order_response"`
	OrderRequest  datatypes.JSON `json:"order_request"`
	CreatedAt     time.Time      `json:"created_at"`
	OMS           string         `json:"oms"`
}

type UserBlockDetail struct {
	BlockType     string `json:"block_type"`
	BlockedTill   int64  `json:"blocked_till"`
	BlockedReason string `json:"reason"`
}

type UserOrderStats struct {
	UserID                    string  `gorm:"column:user_id"`
	TotalPlacedOrderAmount    float64 `gorm:"column:total_placed_order_amount"`
	TotalDeliveredOrderAmount float64 `gorm:"column:total_delivered_order_amount"`
	OrderPlaced               int     `gorm:"column:order_placed"`
	OrderDelivered            int     `gorm:"column:order_delivered"`
	ConfirmedOrder            int     `gorm:"column:confirmed_order"`
	TotalConfirmedOrderAmount float64 `gorm:"column:total_confirmed_order_amount"`
	TotalCancelledOrderAmount float64 `gorm:"column:total_cancelled_order_amount"`
	CancelledOrder            int     `gorm:"column:cancelled_order"`
	ReturnedOrder             int     `gorm:"column:returned_order"`
	TotalReturnedOrderAmount  float64 `gorm:"column:total_returned_order_amount"`
}

type KiranabazarOrderPackageDetails struct {
	ID           uint64         `json:"id"`
	OrderID      int64          `json:"order_id"`
	Details      datatypes.JSON `json:"details"`
	NoOfPackages int            `json:"no_of_packages"`
}

// KiranabazarOrderItem represents the kiranabazar_order_items table
type KiranabazarOrderItem struct {
	ID                uint64    `json:"id" db:"id" gorm:"primaryKey;autoIncrement"`
	OrderID           uint64    `json:"order_id" db:"order_id" gorm:"not null;index:ix_order_item_financials_order_id"`
	OrderItemID       uint64    `json:"order_item_id" db:"order_item_id" gorm:"not null;index:ix_order_item_financials_order_item_id"`
	ProductID         uint64    `json:"product_id" db:"product_id" gorm:"not null;index:ix_order_item_financials_product_id"`
	SkuID             *string   `json:"sku_id,omitempty" db:"sku_id" gorm:"size:50"`
	HsnCode           *string   `json:"hsn_code,omitempty" db:"hsn_code" gorm:"size:20;index:ix_order_item_financials_hsn_code"`
	KcUnitPrice       *float64  `json:"kc_unit_price,omitempty" db:"kc_unit_price" gorm:"type:decimal(12,2)"`
	KcSellingPrice    *float64  `json:"kc_selling_price,omitempty" db:"kc_selling_price" gorm:"type:decimal(12,2)"`
	Quantity          *int      `json:"quantity,omitempty" db:"quantity"`
	TotalKcPrice      *float64  `json:"total_kc_price,omitempty" db:"total_kc_price" gorm:"type:decimal(12,2)"`
	TotalSellingPrice *float64  `json:"total_selling_price,omitempty" db:"total_selling_price" gorm:"type:decimal(12,2)"`
	TotalDiscount     *float64  `json:"total_discount,omitempty" db:"total_discount" gorm:"type:decimal(12,2)"`
	TaxableValue      *float64  `json:"taxable_value,omitempty" db:"taxable_value" gorm:"type:decimal(12,2)"`
	GstRate           *float64  `json:"gst_rate,omitempty" db:"gst_rate" gorm:"type:decimal(5,2)"`
	CgstRate          *float64  `json:"cgst_rate,omitempty" db:"cgst_rate" gorm:"type:decimal(5,2)"`
	SgstRate          *float64  `json:"sgst_rate,omitempty" db:"sgst_rate" gorm:"type:decimal(5,2)"`
	IgstRate          *float64  `json:"igst_rate,omitempty" db:"igst_rate" gorm:"type:decimal(5,2)"`
	CessRate          *float64  `json:"cess_rate,omitempty" db:"cess_rate" gorm:"type:decimal(5,2)"`
	CgstAmount        *float64  `json:"cgst_amount,omitempty" db:"cgst_amount" gorm:"type:decimal(12,2)"`
	SgstAmount        *float64  `json:"sgst_amount,omitempty" db:"sgst_amount" gorm:"type:decimal(12,2)"`
	IgstAmount        *float64  `json:"igst_amount,omitempty" db:"igst_amount" gorm:"type:decimal(12,2)"`
	CessAmount        *float64  `json:"cess_amount,omitempty" db:"cess_amount" gorm:"type:decimal(12,2)"`
	TotalTax          *float64  `json:"total_tax,omitempty" db:"total_tax" gorm:"type:decimal(12,2)"`
	ItemTotal         *float64  `json:"item_total,omitempty" db:"item_total" gorm:"type:decimal(12,2)"`
	IsFreeItem        bool      `json:"is_free_item" db:"is_free_item" gorm:"default:0"`
	FreeItemReason    *string   `json:"free_item_reason,omitempty" db:"free_item_reason" gorm:"size:100"`
	CreatedAt         time.Time `json:"created_at" db:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt         time.Time `json:"updated_at" db:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP;autoUpdateTime"`
	PlatformDiscount  *float64  `json:"platform_discount,omitempty" db:"platform_discount"`
	SellerDiscount    *float64  `json:"seller_discount,omitempty" db:"seller_discount"`
	PaymentDiscount   *float64  `json:"payment_discount,omitempty" db:"payment_discount"`
	PlatformCashback  *float64  `json:"platform_cashback,omitempty" db:"platform_cashback"`
	MarkdownDiscount  *float64  `json:"markdown_discount,omitempty" db:"markdown_discount"`
}

// TableName returns the table name for GORM
func (KiranabazarOrderItem) TableName() string {
	return "kiranabazar_order_items"
}

func (*KiranaBazarOrder) TableName() string {
	return KiranaBazarOrderTableName
}

func (*KiranaBazarOrderDetail) TableName() string {
	return KiranaBazarOrderDetailTableName
}

func (*KiranaBazarOrderPayment) TableName() string {
	return KiranaBazarOrderPaymentTableName
}

func (*KiranaBazarOrderStatus) TableName() string {
	return KiranaBazarOrderStatusTableName
}

func (*KiranaBazarEasyEcomOrder) TableName() string {
	return KiranaBazarEasyEcomOrderTableName
}

func (*KiranaBazarUnicommerceOrder) TableName() string {
	return KiranaBazarUnicommerceOrderTableName
}

func (*KiranaBazarOrderTags) TableName() string {
	return "kiranabazar_order_tags"
}

func (*KiranaBazarOrderTagMapping) TableName() string {
	return "kiranabazar_order_tag_mapping"
}

func (*KiranabazarOrderPackageDetails) TableName() string {
	return "kiranabazar_order_package_details"
}

// KiranaBazarOrderRefund represents the kiranabazar_order_refunds table
type KiranaBazarOrderRefund struct {
	ID             int             `json:"id"`
	CreatedAt      time.Time       `json:"created_at"`
	UpdatedAt      time.Time       `json:"updated_at"`
	Meta           *datatypes.JSON `json:"meta"`
	OrderID        int64           `json:"order_id"`
	RefundMethod   string          `json:"refund_method"`
	PaymentID      *string         `json:"payment_id"`
	SourceRefundID string          `json:"source_refund_id"`
	Source         string          `json:"source"`
	Status         string          `json:"status"`
	Amount         float64         `json:"amount"`
	PaymentMethod  string          `json:"payment_method"`
	Reason         *string         `json:"reason"`
	Cohort         *string         `json:"cohort"`
}

func (*KiranaBazarOrderRefund) TableName() string {
	return KiranaBazarOrderRefundsTableName
}

// GetAdvancePaid returns the advance paid amount from paymentDetails this is same as
func (paymentDetails *KiranaBazarOrderPayment) GetAdvancePaid() *float64 {
	if paymentDetails.PaidAmount != nil {
		return paymentDetails.PaidAmount
	}
	return nil
}

func (orderDetail *KiranaBazarOrderDetails) GetOrderValue() float64 {
	return orderDetail.GetCartValue() - orderDetail.GetDiscountValue() + orderDetail.GetChargeValue()
}

// GetOrderValue returns the total order value (this is cart value - total discounts + applicable charges)
func (orderDetail *KiranaBazarOrderDetails) GetOrderValueV2() float64 {
	return orderDetail.GetCartValue() - orderDetail.GetTotalDiscountValue() + orderDetail.GetChargeValue()
}

func (orderDetail *KiranaBazarOrderDetails) PushDimensionsToOrderDetails(orderId int64, packageDetails *shared.PackageDetails, db *sqlRepo.Repository) error {
	// Update package details
	orderDetail.PackageDetails = packageDetails

	orderDetailJson, err := json.Marshal(orderDetail)
	if err != nil {
		return fmt.Errorf("failed to marshal order details: %w", err)
	}

	_, _, err = db.Update(KiranaBazarOrderDetail{OrderID: &orderId}, KiranaBazarOrderDetail{OrderDetails: orderDetailJson})

	if err != nil {
		return fmt.Errorf("failed to update order details in database: %w", err)
	}

	return nil
}

// GetDimensionsOfOrderDetails returns the package details of the order
func (orderDetail *KiranaBazarOrderDetails) GetDimensionsOfOrderDetails(orderId int, packageDetails *shared.PackageDetails, db *sqlRepo.Repository) (shared.PackageDetails, error) {
	if orderDetail.PackageDetails == nil {
		return shared.PackageDetails{}, fmt.Errorf("package details not found")
	}
	return *orderDetail.PackageDetails, nil
}

func (orderDetail *KiranaBazarOrderDetails) GetCartValue() float64 {
	totalProductPrice := 0.0
	for _, pric := range orderDetail.BillBreakUp.ProductsPricing {
		tval, _ := strconv.ParseFloat(pric.TotalValue, 32)
		totalProductPrice += tval
	}
	return totalProductPrice
}

// GetCartValue returns the total cart value calculated by using brandwholesale rate
func (orderDetail *KiranaBazarOrderDetails) GetCartValueV2() float64 {
	totalProductPrice := 0.0
	for _, cartItem := range orderDetail.Cart {
		meta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(cartItem.Meta, &meta)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error unmarshalling product meta in GetCartValue: %v", err))
			continue
		}
		brandWholesaleRate := meta.WholesaleRate
		if meta.BrandWholesaleRate != nil {
			brandWholesaleRate = *meta.BrandWholesaleRate
		}
		totalProductPrice += brandWholesaleRate * float64(cartItem.Quantity) * float64(meta.PackSize)
	}
	return totalProductPrice
}

// GetManufacturerCartValue returns the total cart value for a given manufacturer without considering discounts but markdown discount is considered
func (orderDetail *KiranaBazarOrderDetails) GetManufacturerCartValue(manufacturers []string) float64 {
	totalProductPrice := 0.0
	for _, pric := range orderDetail.BillBreakUp.ProductsPricing {
		if !includes(manufacturers, pric.Manufacturer) {
			continue
		}
		tval, _ := strconv.ParseFloat(pric.TotalValue, 32)
		totalProductPrice += tval
	}
	return totalProductPrice
}

// GetGtvValue returns the total cart value calculated by using brandwholesale rate
func (orderDetail *KiranaBazarOrderDetails) GetGtvValue() float64 {
	return orderDetail.GetCartValue()
}

// GetDiscountValue is deprecated please use GetTotalDiscountValue instead
func (orderDetail *KiranaBazarOrderDetails) GetDiscountValue() float64 {
	totalDiscount := 0.0
	for _, pric := range orderDetail.BillBreakUp.DiscountPricing {
		if pric.Type == offers.OFFER_TYPES.CHARGE || pric.Type == offers.OFFER_TYPES.PREPAID_AMOUNT {
			continue
		}
		tval, err := strconv.ParseFloat(pric.Value, 32)
		if err != nil {
			tval = pric.TotalValue
		}
		if tval < 0 {
			tval = tval * -1.0
		}
		totalDiscount += tval
	}
	if totalDiscount < 0 {
		totalDiscount = totalDiscount * -1.0
	}
	return totalDiscount
}

func (orderDetail *KiranaBazarOrderDetails) GetDiscountIds() []int64 {
	discountIds := []int64{}
	for _, pric := range orderDetail.BillBreakUp.DiscountPricing {
		if pric.Type == offers.OFFER_TYPES.CHARGE || pric.Type == offers.OFFER_TYPES.PREPAID_AMOUNT {
			continue
		}
		if pric.ID != 0 {
			discountIds = append(discountIds, pric.ID)
		}
	}
	return discountIds
}

// GetDiscountsByProvider returns the total discount value by provider here providers are { platform, seller, platform cashback, payment discount, markdown discount}
func (orderDetail *KiranaBazarOrderDetails) GetDiscountsByProvider() (DiscountsByProvider, error) {
	var (
		platformDiscount = 0.0
		sellerDiscount   = 0.0
		platformCashback = 0.0
		paymentDiscount  = 0.0
		markdownDiscount = 0.0
	)

	// Process each discount in the order
	for _, discount := range orderDetail.BillBreakUp.DiscountPricing {
		// Skip zero-value discounts
		if math.Abs(discount.TotalValue) == 0 || discount.Type == "charge" || discount.Type == "PrePaid Amount" {
			continue
		}

		// Determine discount provider and categorize discount
		provider := helper.DetermineDiscountProvider(discount, orderDetail.Seller)
		switch provider {
		case "PLATFORM":
			platformDiscount += math.Abs(discount.TotalValue)
		case "SELLER":
			sellerDiscount += math.Abs(discount.TotalValue)
		case "PLATFORM_CASHBACK":
			platformCashback += math.Abs(discount.TotalValue)
		case "PAYMENT_DISCOUNT":
			paymentDiscount += math.Abs(discount.TotalValue)
		}
	}

	for _, cartItem := range orderDetail.Cart {
		meta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(cartItem.Meta, &meta)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error unmarshalling product meta: %v", err))
			continue
		}
		if meta.BrandWholesaleRate != nil {
			markdownDiscount += math.Abs((meta.WholesaleRate - *meta.BrandWholesaleRate) * float64(cartItem.Quantity) * float64(meta.PackSize))
		}
	}

	return DiscountsByProvider{
		PlatformDiscount: math.Abs(platformDiscount),
		SellerDiscount:   math.Abs(sellerDiscount),
		PlatformCashback: math.Abs(platformCashback),
		PaymentDiscount:  math.Abs(paymentDiscount),
		MarkdownDiscount: math.Abs(markdownDiscount),
		TotalDiscount:    math.Abs(math.Abs(platformDiscount) + math.Abs(sellerDiscount) + math.Abs(platformCashback) + math.Abs(paymentDiscount) + math.Abs(markdownDiscount)),
	}, nil
}

// GetSellerDiscountValue returns the total seller discount value
func (orderDetail *KiranaBazarOrderDetails) GetSellerDiscountValue() float64 {
	sellerDiscount, err := orderDetail.GetDiscountsByProvider()
	if err != nil {
		return 0.0
	}
	return sellerDiscount.SellerDiscount
}

// GetPlatformDiscountValue returns the total platform discount value
func (orderDetail *KiranaBazarOrderDetails) GetPlatformDiscountValue() float64 {
	platformDiscount, err := orderDetail.GetDiscountsByProvider()
	if err != nil {
		return 0.0
	}
	return platformDiscount.PlatformDiscount
}

// GetCummulativePlatformDiscountValue returns the total platform discount value including everything except seller discount
func (orderDetail *KiranaBazarOrderDetails) GetCummulativePlatformDiscountValue() float64 {
	return orderDetail.GetTotalDiscountValue() - orderDetail.GetSellerDiscountValue()
}

// GetPaymentDiscountValue returns the total payment discount value
func (orderDetail *KiranaBazarOrderDetails) GetPaymentDiscountValue() float64 {
	paymentDiscount, err := orderDetail.GetDiscountsByProvider()
	if err != nil {
		return 0.0
	}
	return paymentDiscount.PaymentDiscount
}

// GetPlatformCashbackValue returns the total platform cashback value
func (orderDetail *KiranaBazarOrderDetails) GetPlatformCashbackValue() float64 {
	platformCashback, err := orderDetail.GetDiscountsByProvider()
	if err != nil {
		return 0.0
	}
	return platformCashback.PlatformCashback
}

// GetMarkdownDiscountValue returns the total markdown discount value
func (orderDetail *KiranaBazarOrderDetails) GetMarkdownDiscountValue() float64 {
	markdownDiscount, err := orderDetail.GetDiscountsByProvider()
	if err != nil {
		return 0.0
	}
	return markdownDiscount.MarkdownDiscount
}

// GetItemLevelDiscountValue returns the **total** discount value for an item
func (orderDetail *KiranaBazarOrderDetails) GetItemLevelCummulativeDiscountValue(productID string) float64 {
	ItemDiscount := 0.0
	discountByProvider, err := orderDetail.GetDiscountsByProvider()
	if err != nil {
		return 0.0
	}
	discountWithoutMarkdown := discountByProvider.TotalDiscount - discountByProvider.MarkdownDiscount
	// now distribute the discountWithoutMarkdown proportionally to each item based on its price
	for _, cartItem := range orderDetail.Cart {
		if cartItem.ID != productID {
			continue
		}
		meta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(cartItem.Meta, &meta)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error unmarshalling product meta in GetItemLevelDiscountValue: %v", err))
			continue
		}
		brandWholesaleRate := meta.WholesaleRate
		if meta.BrandWholesaleRate != nil {
			brandWholesaleRate = *meta.BrandWholesaleRate
		}
		ItemDiscount += ((brandWholesaleRate * float64(cartItem.Quantity) * float64(meta.PackSize)) / orderDetail.GetCartValue()) * discountWithoutMarkdown
	}
	return math.Abs(ItemDiscount) + orderDetail.GetItemLevelMarkdownDiscountValue(productID)
}

// GetItemLevelSellerDiscountValue returns the total seller discount value for an item
func (orderDetail *KiranaBazarOrderDetails) GetItemLevelSellerDiscountValue(productID string) float64 {
	ItemSellerDiscount := 0.0
	discountByProvider, err := orderDetail.GetDiscountsByProvider()
	if err != nil {
		return 0.0
	}
	// now distribute the discountWithoutMarkdown proportionally to each item based on its price
	for _, cartItem := range orderDetail.Cart {
		if cartItem.ID != productID {
			continue
		}
		meta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(cartItem.Meta, &meta)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error unmarshalling product meta in GetItemLevelSellerDiscountValue: %v", err))
			continue
		}
		brandWholesaleRate := meta.WholesaleRate
		if meta.BrandWholesaleRate != nil {
			brandWholesaleRate = *meta.BrandWholesaleRate
		}
		ItemSellerDiscount += ((brandWholesaleRate * float64(cartItem.Quantity) * float64(meta.PackSize)) / orderDetail.GetCartValue()) * discountByProvider.SellerDiscount
	}
	return math.Abs(ItemSellerDiscount)
}

// GetItemLevelMarkdownDiscountValue returns the total markdown discount value for an item
func (orderDetail *KiranaBazarOrderDetails) GetItemLevelMarkdownDiscountValue(productID string) float64 {
	ItemMarkdownDiscount := 0.0
	for _, cartItem := range orderDetail.Cart {
		if cartItem.ID != productID {
			continue
		}
		meta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(cartItem.Meta, &meta)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("Error unmarshalling product meta: %v", err))
			continue
		}
		if meta.BrandWholesaleRate != nil {
			ItemMarkdownDiscount += math.Abs((meta.WholesaleRate - *meta.BrandWholesaleRate) * float64(cartItem.Quantity) * float64(meta.PackSize))
		}
	}
	return math.Abs(ItemMarkdownDiscount)
}

// GetTotalDiscountValue returns the total discount value -- here total is cumulative of **all** discounts
func (orderDetail *KiranaBazarOrderDetails) GetTotalDiscountValue() float64 {
	totalDiscount, err := orderDetail.GetDiscountsByProvider()
	if err != nil {
		return 0.0
	}
	return totalDiscount.TotalDiscount
}

// GetUserPaidAmount returns the total amount paid by the user
func (orderDetail *KiranaBazarOrderDetails) GetUserPaidAmount() float64 {
	totalPaidAmount := 0.0
	for _, pric := range orderDetail.BillBreakUp.DiscountPricing {
		if pric.Type == "" || pric.Type != offers.OFFER_TYPES.PREPAID_AMOUNT {
			continue
		}
		tval, err := strconv.ParseFloat(pric.Value, 32)
		if err != nil {
			tval = pric.TotalValue
		}
		if tval < 0 {
			tval = tval * -1.0
		}
		totalPaidAmount += tval
	}
	if totalPaidAmount < 0 {
		totalPaidAmount = totalPaidAmount * -1.0
	}
	return totalPaidAmount
}

func (orderDetail *KiranaBazarOrderDetails) GetCodValue() float64 {
	return math.Max(0, orderDetail.GetCartValue()-orderDetail.GetDiscountValue()+orderDetail.GetChargeValue()-orderDetail.GetUserPaidAmount())
}

// GetCodValue returns the total cod value
func (orderDetail *KiranaBazarOrderDetails) GetCodValueV2() float64 {
	return math.Max(0, orderDetail.GetCartValue()-orderDetail.GetTotalDiscountValue()+orderDetail.GetChargeValue()-orderDetail.GetUserPaidAmount())
}

// GetPaymentMode returns the payment mode for the order --> returns PREPAID if cod value is 0 else returns COD
func (orderDetail *KiranaBazarOrderDetails) GetPaymentMode() string {
	codValue := orderDetail.GetCodValue()
	if codValue == 0 {
		return "PREPAID"
	}
	return "COD"
}

func (orderDetail *KiranaBazarOrderDetails) GetChargeValue() float64 {
	totalServiceCharge := 0.0
	for _, pric := range orderDetail.BillBreakUp.DiscountPricing {
		if pric.Type == offers.OFFER_TYPES.CHARGE {
			tval, err := strconv.ParseFloat(pric.Value, 32)
			if err != nil {
				tval = pric.TotalValue
			}
			if tval < 0 {
				tval = tval * -1.0
			}
			totalServiceCharge += tval
		}
	}
	if totalServiceCharge < 0 {
		totalServiceCharge = totalServiceCharge * -1.0
	}
	return totalServiceCharge
}

func (orderDetail *KiranaBazarOrderDetails) GetShippingCharges() float64 {
	totalServiceCharge := 0.0
	for _, pric := range orderDetail.BillBreakUp.DiscountPricing {
		if pric.Type == offers.OFFER_TYPES.CHARGE {
			tval, err := strconv.ParseFloat(pric.Value, 32)
			if err != nil {
				tval = pric.TotalValue
			}
			if tval < 0 {
				tval = tval * -1.0
			}
			totalServiceCharge += tval
		}
	}
	if totalServiceCharge < 0 {
		totalServiceCharge = totalServiceCharge * -1.0
	}
	return totalServiceCharge
}

func (orderDetail *KiranaBazarOrderDetails) GetAppliedCashbackValue() float64 {
	for _, pric := range orderDetail.BillBreakUp.DiscountPricing {
		if pric.Type == offers.OFFER_TYPES.CASHBACK {
			value := pric.TotalValue
			if value < 0 {
				value = value * -1.0
			}
			return value
		}
	}
	return 0.0
}

func (orderDetails *KiranaBazarOrderDetails) GetOrderCart() []shared.SellerItems {
	return orderDetails.Cart
}

func (orderDetails *KiranaBazarOrderDetails) GetBillBreakup() BillDetails {
	return orderDetails.BillBreakUp
}

func (kbopd *KiranabazarOrderPackageDetails) GetPackageDetails() shared.PackageDetails {
	var packageDetails shared.PackageDetails
	err := json.Unmarshal(kbopd.Details, &packageDetails)
	if err != nil {
		return shared.PackageDetails{}
	}
	return packageDetails
}

type GetOrderEssentials struct {
	OrderID          *int64   `json:"order_id" gorm:"column:order_id"`
	UserID           *string  `json:"user_id" gorm:"column:user_id"`
	Seller           *string  `json:"seller" gorm:"column:seller"`
	OrderStatus      *string  `json:"order_status" gorm:"column:order_status"`
	DisplayStatus    *string  `json:"display_status" gorm:"column:display_status"`
	ProcessingStatus *string  `json:"processing_status" gorm:"column:processing_status"`
	DeliveryStatus   *string  `json:"delivery_status" gorm:"column:delivery_status"`
	Courier          *string  `json:"courier" gorm:"column:courier"`
	AwbNumber        *string  `json:"awb_number" gorm:"column:awb_number"`
	OrderValue       *float64 `json:"order_value" gorm:"column:order_value"`
	CustomerPhone    *string  `json:"customer_phone" gorm:"column:customer_phone"`
}

func includes(slice []string, item string) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}
