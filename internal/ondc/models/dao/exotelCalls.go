// internal/ondc/models/dao/call_dao.go
package dao

import (
	"time"

	"gorm.io/datatypes"
)

type KiranaBazarUserCall struct {
	ID               int     `gorm:"primaryKey;autoIncrement" json:"id"`
	OrderID          *int    `gorm:"column:order_id;index" json:"order_id"`
	CallSid          *string `gorm:"column:call_sid;type:varchar(200);index" json:"call_sid"`
	FromNumber       string  `gorm:"column:from_number;type:varchar(20);not null" json:"from_number"`
	ToNumber         string  `gorm:"column:to_number;type:varchar(20);not null" json:"to_number"`
	VirtualNumber    string  `gorm:"column:virtual_number;type:varchar(20);not null" json:"virtual_number"`
	InitiatedBy      string  `gorm:"column:intiated_by;type:varchar(40);not null" json:"initiated_by"` // Note: typo in DB column name
	Status           string  `gorm:"column:status;type:varchar(20);not null;index:kiranabazar_user_calls_order_id_status_IDX" json:"status"`
	Purpose          string  `gorm:"column:purpose;type:varchar(40);not null" json:"purpose"`
	CreatedAt        int64   `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt        int64   `gorm:"column:updated_at;autoUpdateTime:false" json:"updated_at"`
	CallRecordingUrl string  `gorm:"column:call_recording_url;type:varchar(255)" json:"call_recording_url"`
}

type KiranaBazarCallEvent struct {
	ID           int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CallSid      string    `gorm:"index" json:"call_sid"`
	EventType    string    `json:"event_type"`
	Status       string    `json:"status"`
	DateCreated  string    `json:"date_created"`
	Direction    string    `json:"direction"`
	From         string    `json:"from"`
	To           string    `json:"to"`
	Duration     int       `json:"duration"`
	RecordingUrl string    `json:"recording_url"`
	RawWebhook   []byte    `gorm:"type:json" json:"raw_webhook"`
	CreatedAt    time.Time `json:"created_at"`
}

type KiranaBazarCallAPILog struct {
	ID        int64          `gorm:"primaryKey;autoIncrement" json:"id"`
	OrderID   int            `gorm:"not null;index" json:"order_id"`
	CallSid   string         `gorm:"type:varchar(255);unique;index" json:"call_sid"`
	Request   datatypes.JSON `gorm:"type:json" json:"request"`
	Response  datatypes.JSON `gorm:"type:json" json:"response"`
	Status    string         `gorm:"type:varchar(50)" json:"status"`
	CreatedAt int64          `gorm:"column:created_at" json:"created_at"`
	UpdatedAt int64          `gorm:"column:updated_at;autoUpdateTime:false" json:"updated_at"`
}

func (KiranaBazarCallAPILog) TableName() string {
	return "kiranabazar_user_calls_api_logs"
}

// TableName specifies the table name for GORM
func (KiranaBazarUserCall) TableName() string {
	return "kiranabazar_user_calls"
}

func (KiranaBazarCallEvent) TableName() string {
	return "kiranabazar_call_events"
}

const (
	CallStatusInitiated  = "initiated"
	CallStatusQueued     = "queued"
	CallStatusRinging    = "ringing"
	CallStatusInProgress = "in-progress"
	CallStatusCompleted  = "completed"
	CallStatusFailed     = "failed"
	CallStatusBusy       = "busy"
	CallStatusNoAnswer   = "no-answer"
	CallStatusCancelled  = "cancelled"
)
