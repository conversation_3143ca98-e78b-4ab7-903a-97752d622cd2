package dao

type KiranaBazarCallStatus struct {
	ID            uint64 `json:"id"`
	OrderID       int64 `json:"order_id"`
	CallStatus    string `json:"call_status"`
	Note          string `json:"note"`
	LastUpdatedBy string `json:"last_updated_by"`
	UpdatedAt     int64  `json:"updated_at"`
	UpdatedBy     string `json:"updated_by"`
	CancelReason  string `json:"cancel_reason"`
}

func (u *KiranaBazarCallStatus) TableName() string {
	return "kiranabazar_call_status"
}
