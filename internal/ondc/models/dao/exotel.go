package dao

import (
	"time"

	"gorm.io/datatypes"
)

type ExotelIVRExomlWebhookLogs struct {
	ID        uint64         `json:"id"`
	Request   datatypes.JSON `json:"request"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
}
type ExotelIVRExomlWebhookLogsBackup struct {
	ID        uint64         `json:"id"`
	Request   datatypes.JSON `json:"request"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
}

type ExotelIVRPasthroughWebhookLogs struct {
	ID        uint64         `json:"id"`
	Request   datatypes.JSON `json:"request"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	Type      string         `json:"type"`
}
type ExotelIVRPasthroughWebhookLogsBackup struct {
	ID        uint64         `json:"id"`
	Request   datatypes.J<PERSON><PERSON> `json:"request"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	Type      string         `json:"type"`
}

type ExotelIVRData struct {
	ID        uint64    `json:"id"`
	To        string    `json:"to"`
	From      string    `json:"from"`
	SID       string    `json:"sid" gorm:"column:sid"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
type ExotelIVRDataBackup struct {
	ID        uint64    `json:"id"`
	To        string    `json:"to"`
	From      string    `json:"from"`
	SID       string    `json:"sid" gorm:"column:sid"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type ExotelIVRKCBazarOrderMapping struct {
	ID             uint64 `json:"id"`
	ExotelIVRID    uint64 `json:"exotel_ivr_id"`
	KCBazarOrderID uint64 `json:"kc_bazar_order_id"`
}

type ExotelIVRQueueData struct {
	ID        string         `json:"id"`
	Data      datatypes.JSON `json:"data"`
	Status    string         `json:"status"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	Comment   *string        `json:"comment,omitempty"`
	Type      string         `json:"type"`
}
type ExotelIVRQueueDataBackup struct {
	ID        string         `json:"id"`
	Data      datatypes.JSON `json:"data"`
	Status    string         `json:"status"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	Comment   *string        `json:"comment,omitempty"`
	Type      string         `json:"type"`
}

type ExotelIVRTriggerData struct {
	FunctionName string      `json:"function_name"`
	CustomFields interface{} `json:"custom_fields"`
	From         string      `json:"from"`
	To           string      `json:"to"`
	AppID        int         `json:"app_id"`
	TriggerAt    time.Time   `json:"trigger_at"`
}

type ExotelIVROrderCancellationData struct {
	ID                 string         `json:"id"`
	OrderID            uint64         `json:"order_id"`
	MetaData           datatypes.JSON `json:"meta_data"`
	CreatedAt          time.Time      `json:"created_at"`
	UpdatedAt          time.Time      `json:"updated_at"`
	CancellationReason string         `json:"cancellation_reason"`
	Status             string         `json:"status"`
}
type ExotelIVROrderCancellationDataBackup struct {
	ID                 string         `json:"id"`
	OrderID            uint64         `json:"order_id"`
	MetaData           datatypes.JSON `json:"meta_data"`
	CreatedAt          time.Time      `json:"created_at"`
	UpdatedAt          time.Time      `json:"updated_at"`
	CancellationReason string         `json:"cancellation_reason"`
	Status             string         `json:"status"`
}

type ExotelIVRAPILogs struct {
	ID         uint64         `json:"id"`
	Request    datatypes.JSON `json:"request"`
	Response   datatypes.JSON `json:"response"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	StatusCode int            `json:"status_code"`
	QueueID    string         `json:"queue_id"`
}
type ExotelIVRAPILogsBackup struct {
	ID         uint64         `json:"id"`
	Request    datatypes.JSON `json:"request"`
	Response   datatypes.JSON `json:"response"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	StatusCode int            `json:"status_code"`
	QueueID    string         `json:"queue_id"`
}

func (u *ExotelIVRPasthroughWebhookLogs) TableName() string {
	return "exotel_ivr_pasthrough_webhook_logs"
}

func (u *ExotelIVRData) TableName() string {
	return "exotel_ivr_data"
}

func (u *ExotelIVRKCBazarOrderMapping) TableName() string {
	return "exotel_ivr_kc_bazar_order_mapping"
}

func (u *ExotelIVRQueueData) TableName() string {
	return "exotel_ivr_queue_data"
}

func (u *ExotelIVRExomlWebhookLogs) TableName() string {
	return "exotel_ivr_exoml_webhook_logs"
}

func (u *ExotelIVROrderCancellationData) TableName() string {
	return "exotel_ivr_order_cancellation_data"
}

func (u *ExotelIVRAPILogs) TableName() string {
	return "exotel_ivr_api_logs"
}

func (u *ExotelIVRPasthroughWebhookLogsBackup) TableName() string {
	return "exotel_ivr_pasthrough_webhook_logs_backup"
}

func (u *ExotelIVRDataBackup) TableName() string {
	return "exotel_ivr_data_backup"
}

func (u *ExotelIVRQueueDataBackup) TableName() string {
	return "exotel_ivr_queue_data_backup"
}

func (u *ExotelIVRExomlWebhookLogsBackup) TableName() string {
	return "exotel_ivr_exoml_webhook_logs_backup"
}

func (u *ExotelIVROrderCancellationDataBackup) TableName() string {
	return "exotel_ivr_order_cancellation_data_backup"
}

func (u *ExotelIVRAPILogsBackup) TableName() string {
	return "exotel_ivr_api_logs_backup"
}
