package dao

import "time"

const KiranaBazarPushOrderToOMSLogsTableName = "kiranabazar_push_order_to_oms_logs"

type KiranaBazarPushOrderToOMSLogs struct {
	ID        uint64    `json:"id"`
	OrderID   int64     `json:"order_id"`
	Email     string    `json:"email"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Reason    string    `json:"reason"`
}

func (KiranaBazarPushOrderToOMSLogs) TableName() string {
	return KiranaBazarPushOrderToOMSLogsTableName
}
