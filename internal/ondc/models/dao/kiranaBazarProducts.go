package dao

import (
	"time"

	"gorm.io/datatypes"
)

type KiranaBazarProduct struct {
	ID                 int64          `json:"id"`
	CategoryID         int64          `json:"category_id"`
	Name               string         `json:"name"`
	Code               string         `json:"code"`
	Rank               int            `json:"rank"`
	CreatedAt          time.Time      `json:"created_at"`
	UpdatedAt          time.Time      `json:"updated_at"`
	Meta               datatypes.JSON `json:"meta"`
	IsActive           bool           `json:"is_active"`
	IsOos              bool           `json:"is_oos"`
	IsDefault          bool           `json:"is_default"`
	SizeVariantCode    int64          `json:"size_variant_code"`
	ImageUrls          datatypes.JSON `json:"image_urls"`
	MediaUrls          datatypes.JSON `json:"media_urls,omitempty"` // Optional field for media URLs
	NameLabel          string         `json:"name_label"`
	CodeLabel          string         `json:"code_label"`
	RatingsSum         int            `json:"ratings_sum"`
	RatingsCount       int            `json:"ratings_count"`
	PopularityValue    float64        `json:"popularity_value"`
	ProductType        string         `json:"product_type"` // e.g., "posm", "virtual", "product"
	Seller             string         `json:"seller"`
	Manufacturer       string         `json:"manufacturer"`
	ExtendedMeta       datatypes.JSON `json:"extended_meta,omitempty"` // Optional field for extended metadata
	GTVCalculationRate *float64       `json:"gtv_calculation_rate"`
}

type KiranaBazarProductMediaUrl struct {
	Url      string  `json:"url"`                 // e.g., "https://example.com/image.jpg"
	VideoUrl *string `json:"video_url,omitempty"` // Optional field for video URL
	Type     *string `json:"type,omitempty"`      // Optional field for media type (e.g., "image", "video")
}

type TopBadgeStyles struct {
	Color   string   `json:"color,omitempty"`    // e.g., "#ffffff"
	BgColor []string `json:"bg_color,omitempty"` // e.g., ["#FF0000", "#00FF00"]
}

type KiranaBazarProductExtendedMeta struct {
	TopBadgeStyles *TopBadgeStyles `json:"top_badge_styles,omitempty"` // e.g., {"color": "#ffffff", "bg_color": ["#FF0000", "#00FF00"]}
}

type KiranaBazarEntitiesMapping struct {
	ID        uint       `json:"id"`
	EntityId  uint       `json:"entity_id"`
	TargetId  string     `json:"target_id"`
	Type      string     `json:"type"` // product
	Tag       string     `json:"tag"`  // screen:products
	IsActive  bool       `json:"is_active"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
}

type KiranaBazarProductsAndCategories struct {
	KiranaBazarProduct
	Source   string `json:"source"`
	Category string `json:"category"`
	EntityId int    `json:"entity_id"`
}

func (kbp *KiranaBazarProduct) TableName() string {
	return "kiranabazar_products"
}

func (*KiranaBazarEntitiesMapping) TableName() string {
	return "kiranabazar_entities_mapping"
}
