package dao

import (
	"gorm.io/datatypes"
	"time"
)

type PaymentGatewayTransaction struct {
	ID                    int64          `db:"id"`
	Gateway               string         `db:"gateway"`
	KcTransactionID       string         `db:"kc_transaction_id"`
	GatewayTransactionID  string         `db:"gateway_transaction_id"`
	Entity                string         `db:"entity"`
	OrderAmount           float64        `db:"order_amount"`
	PaymentAmount         float64        `db:"payment_amount"`
	PaidAmount            float64        `db:"paid_amount"`
	GatewayCreatedAt      int64          `db:"gateway_created_at"`
	CreatedAt             time.Time      `db:"created_at"`
	UpdatedAt             time.Time      `db:"updated_at"`
	Meta                  datatypes.JSON `db:"meta"`
	GatewayStatus         string         `db:"gateway_status"`
	KcStatus              string         `db:"kc_status"`
	RefundAmount          float64        `db:"refund_amount"`
	KcOrderID             int64          `db:"kc_order_id"`
	PaymentID             string         `db:"payment_id"`
	PaymentDiscount       float64        `db:"payment_discount"`
	PaymentMethodDiscount float64        `db:"payment_method_discount"`
}

type PaymentGatewayRecords struct {
	ID             string         `gorm:"column:id;primaryKey" db:"id"`
	Entity         string         `gorm:"column:entity" db:"entity"`
	Amount         float64        `gorm:"column:amount" db:"amount"`
	Currency       string         `gorm:"column:currency" db:"currency"`
	Status         string         `gorm:"column:status" db:"status"`
	Method         string         `gorm:"column:method" db:"method"`
	OrderID        string         `gorm:"column:gateway_order_id" db:"gateway_order_id"`
	AmountRefunded int            `gorm:"column:refund_amount" db:"refund_amount"`
	RefundStatus   *string        `gorm:"column:refund_status" db:"refund_status"`
	Captured       bool           `gorm:"column:captured" db:"captured"`
	CreatedAt      int64          `gorm:"column:created_at" db:"created_at"`
	Fee            int            `gorm:"column:fee" db:"fee"`
	Tax            int            `gorm:"column:tax" db:"tax"`
	ErrorCode      *string        `gorm:"column:error_code" db:"error_code"`
	ErrorSource    *string        `gorm:"column:error_source" db:"error_source"`
	ErrorStep      *string        `gorm:"column:error_step" db:"error_step"`
	ErrorReason    *string        `gorm:"column:error_reason" db:"error_reason"`
	Meta           datatypes.JSON `gorm:"column:meta" db:"meta"`
	RNN            *string        `gorm:"column:rnn" db:"rnn"`
}

func (u *PaymentGatewayTransaction) TableName() string {
	return "kiranabazar_payment_gateway_orders"
}
func (u *PaymentGatewayRecords) TableName() string { return "kiranabazar_payment_gateway_records" }
