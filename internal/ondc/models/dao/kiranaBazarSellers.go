package dao

import (
	"encoding/json"

	"gorm.io/datatypes"
)

type KiranaBazarSeller struct {
	ID              int64          `json:"id"`
	Code            string         `json:"code"`
	Name            string         `json:"name"`
	Source          string         `json:"source"`
	UpdatedBy       string         `json:"updated_by"`
	Meta            datatypes.JSON `json:"meta"`
	InvoiceDetails  datatypes.JSON `json:"invoice_details"`
	ShippingDetails datatypes.JSON `json:"shipping_details"`
	CreatedAt       int64          `json:"created_at"`
	UpdatedAt       *int64         `gorm:"autoUpdateTime:false" json:"updated_at"`
}

type KiranaBazarSellerInvoiceDetails struct {
	Email             string `json:"email"`
	Phone             string `json:"phone"`
	State             string `json:"state"`
	Pincode           string `json:"pincode"`
	CINNumber         string `json:"cin_number"`
	GSTNumber         string `json:"gst_number"`
	LegalName         string `json:"legal_name"`
	PANNumber         string `json:"pan_number"`
	VendorCode        string `json:"vendor_code"`
	DisplayName       string `json:"display_name"`
	RegisteredAddress string `json:"registered_address"`
}

type KiranaBazarSellerMeta struct {
	Nav             KiranaBazarSellerMetaNav        `json:"nav"`
	Logo            string                          `json:"logo"`
	Name            string                          `json:"name"`
	Screen          string                          `json:"screen"`
	Seller          string                          `json:"seller"`
	Source          string                          `json:"source"`
	BrandID         string                          `json:"brand_id"`
	BrandCta        KiranaBazarSellerMetaCta        `json:"brand_cta"`
	HelpCentreCta   KiranaBazarSellerMetaCta        `json:"help_centre_cta"`
	SideDrawer      KiranaBazarSellerMetaSideDrawer `json:"side_drawer"`
	SearchTexts     []string                        `json:"search_texts"`
	Categories      any                             `json:"categories"`
	HeaderText      string                          `json:"header_text"`
	PrimaryColor    string                          `json:"primary_color"`
	CartScreenMeta  any                             `json:"cart_screen_meta"`
	BannerImageUrls []string                        `json:"banner_image_urls"`
}

type KiranaBazarSellerMetaNav struct {
	Name    string                         `json:"name"`
	NavType string                         `json:"nav_type"`
	Params  KiranaBazarSellerMetaNavParams `json:"params"`
}

type KiranaBazarSellerMetaNavParams struct {
	Params map[string]interface{} `json:"params"`
	Screen string                 `json:"screen"`
	Seller string                 `json:"seller"`
	Source string                 `json:"source"`
}

type KiranaBazarSellerMetaCta struct {
	Nav               KiranaBazarSellerMetaCtaNav `json:"nav"`
	Icon              string                      `json:"icon"`
	Text              string                      `json:"text"`
	MixpanelEventName string                      `json:"mixpanel_event_name"`
}

type KiranaBazarSellerMetaCtaNav struct {
	Name    string                            `json:"name"`
	NavType string                            `json:"nav_type"`
	Params  KiranaBazarSellerMetaCtaNavParams `json:"params"`
}

type KiranaBazarSellerMetaCtaNavParams struct {
	Uri         string `json:"uri"`
	ShowHeader  bool   `json:"show_header"`
	ScreenTitle string `json:"screen_title"`
}

type KiranaBazarSellerMetaSideDrawer struct {
	List            []KiranaBazarSellerMetaSideDrawerData `json:"list"`
	BannerImageUrls []string                              `json:"banner_image_urls"`
}

type KiranaBazarSellerMetaSideDrawerData struct {
	Nav               KiranaBazarSellerMetaSideDrawerDataNav `json:"nav"`
	Icon              string                                 `json:"icon"`
	Text              string                                 `json:"text"`
	MixpanelEventName string                                 `json:"mixpanel_event_name"`
	IconSet           string                                 `json:"icon_set"`
}

type KiranaBazarSellerMetaSideDrawerDataNav struct {
	Name    string                                       `json:"name"`
	NavType string                                       `json:"nav_type"`
	Params  KiranaBazarSellerMetaSideDrawerDataNavParams `json:"params"`
}

type KiranaBazarSellerMetaSideDrawerDataNavParams struct {
	Screen string `json:"screen"`
	Seller string `json:"seller"`
	Source string `json:"source"`
}

type KiranaBazarSellerShippingDetails struct {
	City       string `json:"city"`
	Email      string `json:"email"`
	Phone      string `json:"phone"`
	State      string `json:"state"`
	Pincode    string `json:"pincode"`
	Address1   string `json:"address1"`
	Address2   string `json:"address2"`
	VendorCode string `json:"vendor_code"`
	VendorName string `json:"vendor_name"`
}

func (kbs *KiranaBazarSeller) GetInvoicingDetails() KiranaBazarSellerInvoiceDetails {
	invoiceDetails := KiranaBazarSellerInvoiceDetails{}
	err := json.Unmarshal(kbs.InvoiceDetails, &invoiceDetails)
	if err != nil {
		return KiranaBazarSellerInvoiceDetails{}
	}
	return invoiceDetails
}

func (kbs *KiranaBazarSeller) GetShippingDetails() KiranaBazarSellerShippingDetails {
	shippingDetails := KiranaBazarSellerShippingDetails{}
	err := json.Unmarshal(kbs.ShippingDetails, &shippingDetails)
	if err != nil {
		return KiranaBazarSellerShippingDetails{}
	}
	return shippingDetails
}

func (KiranaBazarSeller) TableName() string {
	return "kiranabazar_sellers"
}
