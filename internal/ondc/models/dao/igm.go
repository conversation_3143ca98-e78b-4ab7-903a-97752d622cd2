package dao

import (
	"time"

	"gorm.io/datatypes"
)

type ZohoContact struct {
	ID        uint64 `json:"id"`
	ContactID string `json:"contact_id"`
	KCUser<PERSON>  string `json:"kc_user_id"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Phone     string `json:"phone"`
	CreatedAt uint64 `json:"created_at"`
	UpdatedAt uint64 `json:"updated_at"`
}

type KcBazarTicket struct {
	ID                uint64  `gorm:"column:id"`
	TicketID          string  `gorm:"column:ticket_id"`
	TicketNumber      string  `gorm:"column:ticket_number"`
	OrderID           *int64  `gorm:"column:order_id"`
	Type              string  `gorm:"column:type"`
	UserID            string  `gorm:"column:user_id"`
	Subject           string  `gorm:"column:subject"`
	Description       string  `gorm:"column:description"`
	Category          string  `gorm:"column:category"`
	SubCategory       string  `gorm:"column:sub_category"`
	IsSpam            bool    `gorm:"column:is_spam"`
	IsActive          bool    `gorm:"column:is_active"`
	Status            string  `gorm:"column:status"`
	CreatedAt         *int64  `gorm:"column:created_at"`
	UpdatedAt         *int64  `gorm:"column:updated_at"`
	Rating            *int    `gorm:"column:rating"`
	RatingDescription *string `gorm:"column:rating_description"`
}

type KcBazarTicketsAttachments struct {
	ID         uint64 `gorm:"column:id"`
	TicketID   uint64 `gorm:"column:ticket_id"`
	Attachment string `gorm:"type:json;column:attachment"`
}

type KcBazarTicketConversation struct {
	ID             uint64 `gorm:"column:id"`
	ConversationID string `gorm:"column:conversation_id;unique;not null"`
	TicketID       uint64 `gorm:"column:ticket_id"`
	UserID         string `gorm:"column:user_id"`
	UserType       string `gorm:"column:user_type;type:enum('agent', 'kc_user')"`
	ContentType    string `gorm:"column:content_type;type:enum('html', 'plainText')"`
	Content        string `gorm:"column:content"`
	CreatedAt      int64  `gorm:"column:created_at"`
	UpdatedAt      int64  `gorm:"column:updated_at"`
}

type KcBazarTicketConversationsData struct {
	ID             uint64         `gorm:"column:id"`
	ConversationID string         `gorm:"column:conversation_id;unique;not null"`
	TicketID       uint64         `gorm:"column:ticket_id"`
	UserID         string         `gorm:"column:user_id"`
	UserType       string         `gorm:"column:user_type;type:enum('agent', 'kc_user')"`
	ContentType    string         `gorm:"column:content_type;type:enum('html', 'plainText')"`
	Content        string         `gorm:"column:content"`
	Attachment     datatypes.JSON `gorm:"column:attachment"`
	CreatedAt      int64          `gorm:"column:created_at"`
	UpdatedAt      int64          `gorm:"column:updated_at"`
}

type IGMOrderData struct {
	OrderID       *int64    `json:"order_id"`
	OrderStatus   *string   `json:"order_status"`
	DisplayStatus *string   `json:"display_status"`
	CreatedAt     time.Time `json:"created_at"`
	Amount        float64   `json:"amount"`
	TicketID      *uint64   `json:"ticket_id"`
	TicketStatus  string    `json:"ticket_status"`
}

type IGMTicketDetails struct {
	OrderID           *int64         `json:"order_id" gorm:"column:order_id"`
	OrderStatus       string         `json:"order_status" gorm:"column:order_status"`
	OrderPlacedAt     time.Time      `json:"order_placed_at" gorm:"column:placed_at"`
	Amount            float64        `json:"amount" gorm:"column:amount"`
	TicketID          *uint64        `json:"ticket_id" gorm:"column:ticket_id"`
	Type              string         `json:"type" gorm:"column:type"`
	Category          string         `json:"category" gorm:"column:category"`
	SubCategory       string         `json:"sub_category" gorm:"column:sub_category"`
	Description       string         `json:"description" gorm:"column:description"`
	TicketStatus      string         `json:"ticket_status" gorm:"column:ticket_status"`
	Rating            *int           `json:"rating" gorm:"column:rating"`
	RatingDescription *string        `json:"rating_description" gorm:"column:rating_description"`
	Attachment        datatypes.JSON `json:"attachment" gorm:"column:attachment"`
	CreatedAt         *int64         `json:"created_at" gorm:"column:created_at"`
	UpdatedAt         *int64         `json:"updated_at" gorm:"column:updated_at"`
}

type KCBazarConversationAttachment struct {
	ID             uint64         `json:"id"`
	ConversationID string         `gorm:"column:conversation_id"`
	Attachment     datatypes.JSON `gorm:"type:json;column:attachment"`
	CreatedAt      *int64         `json:"created_at"`
	UpdatedAt      *int64         `json:"updated_at"`
	IsActive       bool           `json:"is_active"`
}

type IGMMedia struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}

func (kcbtc *KcBazarTicketConversation) TableName() string {
	return "kc_bazar_tickets_conversations"
}

func (kcbt *KcBazarTicket) TableName() string {
	return "kc_bazar_tickets"
}

func (kbta *KcBazarTicketsAttachments) TableName() string {
	return "kc_bazar_tickets_attachments"
}

func (u *ZohoContact) TableName() string {
	return "zoho_contacts"
}

func (kcbca *KCBazarConversationAttachment) TableName() string {
	return "kc_bazar_conversation_attachments"
}
