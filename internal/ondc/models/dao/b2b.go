package dao

type ProductDataUpdateHistory struct {
	ID            int    `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	ProductID     int    `json:"product_id" gorm:"column:product_id;not null;index"`
	FieldName     string `json:"field_name" gorm:"column:field_name;type:varchar(50);not null"`
	PreviousValue string `json:"previous_value" gorm:"column:previous_value;type:text"`
	UpdatedValue  string `json:"updated_value" gorm:"column:updated_value;type:text"`
	UpdatedAt     int64  `json:"updated_at" gorm:"column:updated_at;autoUpdateTime:false"`
	UpdatedBy     string `json:"updated_by" gorm:"column:updated_by;type:varchar(100)"`
	RequestSource string `json:"request_source" gorm:"column:request_source;type:varchar(20);not null"`
}

func (ProductDataUpdateHistory) TableName() string {
	return "product_data_update_history"
}
