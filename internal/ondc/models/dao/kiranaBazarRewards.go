package dao

import (
	"time"

	"gorm.io/datatypes"
)

type KiranaBazarReward struct {
	ID                    uint64         `json:"id"`
	UserID                string         `json:"user_id"`
	ProductID             int            `json:"product_id"`
	Address               datatypes.JSON `json:"address"`
	AWB                   string         `json:"awb"`
	Courier               string         `json:"courier"`
	CreatedAt             time.Time      `json:"created_at"`
	UpdatedAt             time.Time      `json:"updated_at"`
	OrderStatus           string         `json:"order_status"`
	ProcessingStatus      string         `json:"processing_status"`
	ShipmentStatus        string         `json:"shipment_status"`
	DisplayStatus         string         `json:"display_status"`
	IsArchived            bool           `json:"is_archived"`
	TrackingLink          string         `json:"tracking_link"`
	TentativeDeliveryDate time.Time      `json:"tentative_delivery_date"`
}

func (kbr *KiranaBazarReward) TableName() string {
	return "kiranabazar_rewards"
}
