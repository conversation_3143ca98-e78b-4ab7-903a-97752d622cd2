package dto

type GetProductsRequest struct {
	Data   AppGetProductRequest `json:"data"`
	Meta   Meta                 `json:"meta"`
	UserID string               `json:"user_id"`
}

type AppGetProductRequest struct {
	CategoryID *string `json:"category_id"`
	ProductID  *string `json:"product_id"`
	Source     *string `json:"source"`
}

type GetProductResponse struct {
	Meta  Meta             `json:"meta"`
	Data  AppSearchData    `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type TopProductsConditions struct {
	FetchInactiveProducts bool
}
