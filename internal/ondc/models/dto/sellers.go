package dto

import (
	"kc/internal/ondc/models/shared"

	"gorm.io/datatypes"
)

type KiranaBazarSellers struct {
	ID              int64                            `json:"id"`
	Code            string                           `json:"code"`
	Name            string                           `json:"name"`
	Source          string                           `json:"source"`
	CreatedAt       int64                            `json:"created_at"`
	UpdatedAt       int64                            `json:"updated_at"`
	UpdatedBy       string                           `json:"updated_by"`
	Meta            datatypes.JSON                   `json:"meta"`
	InvoiceDetails  KiranaBazarSellerInvoiceDetails  `json:"invoice_details"`
	ShippingDetails KiranaBazarSellerShippingDetails `json:"shipping_details"`
	IsActive        bool                             `json:"is_active"`
}

type KiranaBazarSellersWithMetaProps struct {
	ID              int64                            `json:"id"`
	Code            string                           `json:"code"`
	Name            string                           `json:"name"`
	Source          string                           `json:"source"`
	CreatedAt       int64                            `json:"created_at"`
	UpdatedAt       int64                            `json:"updated_at"`
	UpdatedBy       string                           `json:"updated_by"`
	Logo            string                           `json:"logo"`
	PrimaryColor    string                           `json:"primary_color"`
	BulkOrderForm   string                           `json:"bulk_order_form"`
	IsKcFullFilled  bool                             `json:"is_kc_fullfilled"`
	BanneImageUrls  []shared.BannerImageUrls         `json:"banner_image_urls"`
	InvoiceDetails  KiranaBazarSellerInvoiceDetails  `json:"invoice_details"`
	ShippingDetails KiranaBazarSellerShippingDetails `json:"shipping_details"`
	IsActive        bool                             `json:"is_active"`
}

type KiranaBazarSellerInvoiceDetails struct {
	Email             string `json:"email"`
	Phone             string `json:"phone"`
	State             string `json:"state"`
	Pincode           string `json:"pincode"`
	CinNumber         string `json:"cin_number"`
	GstNumber         string `json:"gst_number"`
	LegalName         string `json:"legal_name"`
	PanNumber         string `json:"pan_number"`
	VendorCode        string `json:"vendor_code"`
	DisplayName       string `json:"display_name"`
	RegisteredAddress string `json:"registered_address"`
}

type KiranaBazarSellerShippingDetails struct {
	City       string `json:"city"`
	Email      string `json:"email"`
	Phone      string `json:"phone"`
	State      string `json:"state"`
	Pincode    string `json:"pincode"`
	Address1   string `json:"address1"`
	Address2   string `json:"address2"`
	VendorCode string `json:"vendor_code"`
	VendorName string `json:"vendor_name"`
}

type GetSellersRequest struct {
	Data struct {
		Seller string `json:"seller"`
	} `json:"data"`
}

type GetSellersResponse struct {
	Data struct {
		Sellers []KiranaBazarSellersWithMetaProps `json:"sellers"`
	} `json:"data"`
}

type GetSellerDetailsRequest struct {
	Data struct {
		Seller string `json:"seller"`
	} `json:"data"`
}

type GetSellerDetailsResponse struct {
	Data struct {
		Seller KiranaBazarSellers `json:"seller"`
	} `json:"data"`
}

type UpdateSellerDetailsRequest struct {
	Data KiranaBazarSellersWithMetaProps `json:"data"`
}

type UpdateSellerDetailsResponse struct {
	Data struct {
		Seller KiranaBazarSellers `json:"seller"`
	} `json:"data"`
}

type DeleteSellerRequest struct {
	Data struct {
		Seller   string `json:"seller"`
		IsActive bool   `json:"is_active"`
	} `json:"data"`
}

type DeleteSellerResponse struct {
	Data struct {
		Seller KiranaBazarSellers `json:"seller"`
	} `json:"data"`
}

type AddSellerRequest struct {
	Data struct {
		Seller KiranaBazarSellersWithMetaProps `json:"seller"`
	} `json:"data"`
}

type AddSellerResponse struct {
	Data struct {
		Seller KiranaBazarSellersWithMetaProps `json:"seller"`
	} `json:"data"`
}

type GetVendorCodesResponse struct {
	Data struct {
		VendorCodes []string `json:"vendor_codes"`
	} `json:"data"`
}
