package dto

type WidgetType43 struct {
	MixPanelEventName        string        `json:"MixPanelEventName"`
	ExpiryTime               int64         `json:"expiry_time"`
	ID                       int           `json:"id"`
	Type                     int           `json:"type"`
	UpdatedBy                string        `json:"updatedBy"`
	Visibility               int           `json:"visibility"`
	WidthPercentage          int           `json:"width_percentage"`
	OptimisedWidthPercentage int           `json:"optimised_width_percentage"`
	VisibleFrom              int64         `json:"visible_from"`
	WidgetInfo               WidgetInfo    `json:"widget_info"`
	SubType                  string        `json:"sub_type"`
	AspectRation             *float64      `json:"aspect_ratio,omitempty"`     // Optional field for aspect ratio
	BackgroundColor          *string       `json:"background_color,omitempty"` // Optional field for background color
	WidthMultiplier          *float64      `json:"width_multiplier,omitempty"` // Optional field for width multiplier
	Data                     []interface{} `json:"data"`
}
