package dto

import (
	"encoding/json"
	"kc/internal/ondc/models/shared"
)

type AppSelectRequest struct {
	Data   AppSelectRequestData `json:"data"`
	Meta   Meta                 `json:"meta"`
	UserID string               `json:"user_id"`
}

type AppSelectRequestData struct {
	Items    []shared.SellerItems `json:"items"`
	Provider AppSelectProvider    `json:"provider"`
}

type AppSelectProvider struct {
	ProviderID       string   `json:"provider_id"`
	ProviderTTL      string   `json:"provider_ttl"`
	ProviderLocation []string `json:"provider_locations"`
}

type AppSelectResponse struct {
	Cart  Cart             `json:"cart"`
	Error AppResponseError `json:"error,omitempty"`
}

type Cart struct {
	BppID        string `json:"bpp_id"`
	BppUrl       string `json:"bpp_url"`
	ProviderName string `json:"provider_name"`
	ProviderID   string `json:"provider_id"`
	Quote        Quote  `json:"quote"`
	Meta         Meta   `json:"meta"`
}

type Quote struct {
	TotalPrice float64             `json:"total_price"`
	Breakup    map[string]CartItem `json:"breakup"`
}

type CartItem struct {
	ItemID         string  `json:"item_id"`
	Quantity       int32   `json:"quantity"`
	Price          float64 `json:"price"`
	PerItemPrice   float64 `json:"per_item_price"`
	Tax            float64 `json:"tax"`
	Discount       float64 `json:"discount"`
	DeliveryCharge float64 `json:"delivery_charge"`
	Fulfillment
}

type ItemFulfilment struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Category string `json:"category"`
	TAT      string `json:"tat"`
	Tracking bool   `json:"tracking"`
}

func (r AppSelectResponse) MarshalBinary() (data []byte, err error) {
	bytes, err := json.Marshal(r)
	return bytes, err
}
