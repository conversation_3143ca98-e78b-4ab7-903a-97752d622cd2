package dto

// OnSearchRequest contains on_search Catalog for products and services.
type OnSearchRequest struct {
	Context *Context         `json:"context" validate:"required"`
	Message *OnSearchMessage `json:"message,omitempty"`
	Error   *Error           `json:"error,omitempty"`
}

// OnSearchMessage is an inner message of OnSearchRequest.
type OnSearchMessage struct {
	Catalog *Catalog `json:"catalog" validate:"required"`
}

// GetContext returns the ONDC context of the request.
func (r OnSearchRequest) GetContext() Context { return *r.Context }
