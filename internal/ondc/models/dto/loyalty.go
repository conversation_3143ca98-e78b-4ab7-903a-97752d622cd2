package dto

import "kc/internal/ondc/models/shared"

type ComponentHeader struct {
	MixpanelEventName string      `json:"mixpanel_event_name"`
	Url               string      `json:"url"`
	Nav               *shared.Nav `json:"nav,omitempty"`
}

type ComponentBody struct {
	Text              string         `json:"text"`
	MixpanelEventName string         `json:"mixpanel_event_name"`
	Icon              string         `json:"icon"`
	PrimaryColor      *string        `json:"primary_color,omitempty"`
	SecondaryColor    *string        `json:"secondary_color,omitempty"`
	Nav               *shared.Nav    `json:"nav,omitempty"`
	IconSet           string         `json:"icon_set"`
	SecondaryText     string         `json:"secondary_text"`
	CashbackValue     float64        `json:"cashback_value"`
	SecondaryTextV2   *WaysToPayText `json:"secondary_text_v2,omitempty"`
}

type ComponentFooter struct {
	Text    string  `json:"text"`
	Icon    string  `json:"icon"`
	Color   *string `json:"color,omitempty"`
	IconSet string  `json:"icon_set"`
}

type CashbackComponent struct {
	Header       *ComponentHeader `json:"header,omitempty"`
	Body         *ComponentBody   `json:"body,omitempty"`
	Footer       *ComponentFooter `json:"footer,omitempty"`
	IconImageUrl string           `json:"icon_image_url"`
	ActiveColor  *string          `json:"active_color,omitempty"`
}

type LoyaltyRewardsText struct {
	Text            string                  `json:"text"`
	Value           float64                 `json:"value"`
	HighlightedText []HighlightedTextData   `json:"highlighted_text,omitempty"`
	Styles          *map[string]interface{} `json:"styles,omitempty"`
}

type LoyaltyRewardIcon struct {
	Icon    *string `json:"icon,omitempty"`
	IconSet *string `json:"icon_set,omitempty"`
	Color   *string `json:"color,omitempty"`
	Url     *string `json:"url,omitempty"`
}

type LoyaltyRewardsBenefitsItem struct {
	Icon    *LoyaltyRewardIcon `json:"icon,omitempty"`
	Content LoyaltyRewardsText `json:"content,omitempty"`
}

type LoyaltyRewardsComponent struct {
	CurrentTier string                       `json:"current_tier"`
	Header      *ComponentFooter             `json:"header,omitempty"`
	Body        []LoyaltyRewardsBenefitsItem `json:"body,omitempty"`
	Config      map[string]interface{}       `json:"config,omitempty"`
}

type GetUserCashbackBalanceRequest struct {
	UserID string `json:"user_id"`
	Meta   Meta   `json:"meta"`
}

type GetUserCashbackBalanceData struct {
	Cashback *float64 `json:"cashback,omitempty"`
}

type GetUserCashbackBalanceResponse struct {
	Meta  Meta                       `json:"meta"`
	Data  GetUserCashbackBalanceData `json:"data"`
	Error string                     `json:"error"`
}

type RedeemUserCashbackData struct {
	OrderAmount          float64 `json:"order_amount"`
	RedeemCashbackAmount float64 `json:"redeem_cashback_amount"`
	OrderId              int64   `json:"order_id"`
	SourceId             int     `json:"source_id"`
}

type RedeemUserCashbackRequest struct {
	UserID string                 `json:"user_id"`
	Data   RedeemUserCashbackData `json:"redeemed_cashback_data"`
	Meta   Meta                   `json:"meta"`
}

type RedeemUserCashbackResponse struct {
	Meta    Meta   `json:"meta"`
	Error   string `json:"error"`
	Message string `json:"message"`
}

type AddUserLoyaltyPointsRequestData struct {
	OrderAmount float64 `json:"order_amount"`
	SourceId    int     `json:"source_id"`
	OrderId     int64   `json:"order_id"`
}

type AddUserLoyaltyPointsRequest struct {
	UserID string                          `json:"user_id"`
	Meta   Meta                            `json:"meta"`
	Data   AddUserLoyaltyPointsRequestData `json:"data"`
}

type AddUserLoyaltyPointsResponse struct {
	Meta    Meta   `json:"meta"`
	Error   string `json:"error"`
	Message string `json:"message"`
}

type ActivateUserLoyaltyRewardsRequestData struct {
	OrderId int64 `json:"order_id"`
}

type ActivateUserLoyaltyRewardsRequest struct {
	UserID string                                `json:"user_id"`
	Meta   Meta                                  `json:"meta"`
	Data   ActivateUserLoyaltyRewardsRequestData `json:"data"`
}

type ActivateUserLoyaltyRewardsResponse struct {
	Meta    Meta   `json:"meta"`
	Error   string `json:"error"`
	Message string `json:"message"`
}

type RevertUserCashbackRequest struct {
	UserID string                     `json:"user_id"`
	Meta   Meta                       `json:"meta"`
	Data   RevertUserUserCashbackData `json:"data"`
}

type RevertUserUserCashbackData struct {
	OrderId int64 `json:"order_id"`
}

type RevertUserCashbackResponse struct {
	Meta    Meta   `json:"meta"`
	Error   string `json:"error"`
	Message string `json:"message"`
}

type CreateUserLoyaltyOrderRequestData struct {
	ProductIds []string `json:"product_ids"`
	AddressId  *string  `json:"address_id,omitempty"`
}

type CreateUserLoyaltyOrderRequest struct {
	UserID string                            `json:"user_id"`
	Meta   Meta                              `json:"meta"`
	Data   CreateUserLoyaltyOrderRequestData `json:"data"`
}

type GetUserTierInfoRequest struct {
	Data interface{} `json:"data"` // This can be extended to include more specific fields if needed
}

type GetUsersOrderHistoryRequest struct {
	Data interface{} `json:"data"` // This can be extended to include more specific fields if needed
}

type GetUsersTierUpgradeHistoryRequest struct {
	Data interface{} `json:"data"` // This can be extended to include more specific fields if needed
}

type GetUsersCoinsWalletRequest struct {
	Data interface{} `json:"data"` // This can be extended to include more specific fields if needed
}

type GetUsersCashbackWalletRequest struct {
	Data interface{} `json:"data"` // This can be extended to include more specific fields if needed
}

type GetOrderIdRequest struct {
	OrderID string `json:"order_id"`
}
