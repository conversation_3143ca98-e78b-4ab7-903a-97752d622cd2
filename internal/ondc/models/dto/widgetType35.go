package dto

type WidgetType35 struct {
	MixPanelEventName string     `json:"MixPanelEventName"`
	ID                int64      `json:"id"`
	Heading           string     `json:"heading"`
	CreatedBy         string     `json:"created_by"`
	Expiry            int64      `json:"expiry"`
	ExpiryTime        int64      `json:"expiry_time"`
	Type              int8       `json:"type"`
	Title             string     `json:"title"`
	Rating            float64    `json:"rating"`
	SubTitle          string     `json:"sub_title"`
	WidgetInfo        WidgetInfo `json:"widget_info"`
	IsActive          int        `json:"is_active"`
	UpdatedAt         string     `json:"updated_at"`
	VisibleFrom       int64      `json:"visible_from"`
	ProductId         int64      `json:"product_id"`
	Cta               *Cta       `json:"cta"`
}
