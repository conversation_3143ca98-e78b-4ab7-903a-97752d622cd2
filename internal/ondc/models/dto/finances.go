package dto

type CreditNoteRequest struct {
	CreditNote      *CreditNote       `json:"credit_note"`
	CreditNoteItems []*CreditNoteItem `json:"credit_note_items"`
}

type CreditNote struct {
	OrderID        string  `json:"order_id"`
	PaymentID      uint64  `json:"payment_id"`
	CreditNoteType string  `json:"credit_note_type"` // Return, partial_return, price_correction
	Amount         float64 `json:"amount"`           // optional only for custom
}

type CreditNoteItem struct {
	SKUID    string `json:"sku_id"`
	Quantity int    `json:"quantity"`
}
