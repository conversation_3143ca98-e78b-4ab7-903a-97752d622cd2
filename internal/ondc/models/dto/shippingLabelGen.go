package dto

type GenerateShippingLabelRequest struct {
	Data GenerateShippingLabelRequestData `json:"data"`
}
type GenerateShippingLabelRequestData struct {
	OrderID string `json:"order_id"`
}

type GenerateShippingLabelResponse struct {
	Result Result                `json:"result"`
	Error  ShippingLabelGenError `json:"error,omitempty"`
}

type Result struct {
	Success bool    `json:"success"`
	Labels  []Label `json:"labels"`
	URL     string  `json:"url"`
}

type ShippingLabelGenError struct {
	Message string `json:"message"`
	Status  string `json:"status"`
}

type Label struct {
	OrderNumber string `json:"order_number"`
	AWBNumber   string `json:"awb_number"`
	Pages       int    `json:"pages"`
}

type ShippingLabelRequestObject struct {
	Data []ShippingLabelRequestData `json:"data"`
}

type ShippingLabelRequestData struct {
	OrderFrom       string                     `json:"order_from"`
	ProcessedBy     string                     `json:"processed_by"`
	AWBNumber       string                     `json:"awb_number"`
	DeliveryType    string                     `json:"delivery_type"`
	OrderNumber     string                     `json:"order_number"`
	ShippingAddress ShippingAddress            `json:"shipping_address"`
	InvoiceDetails  InvoiceDetails             `json:"invoice_details"`
	Items           []ShippingLabelRequestItem `json:"items"`
	Charges         Charges                    `json:"charges"`
	ReturnAddress   ReturnAddress              `json:"return_address"`
	Note            string                     `json:"note"`
}

type ShippingAddress struct {
	Name    string `json:"name"`
	Address string `json:"address"`
	City    string `json:"city"`
	State   string `json:"state"`
	Pincode string `json:"pincode"`
}

type InvoiceDetails struct {
	OrderNumber       string  `json:"order_number"`
	OrderDate         string  `json:"order_date"`
	InvoiceNumber     string  `json:"invoice_number"`
	PaymentType       string  `json:"payment_type"`
	CollectCashAmount float64 `json:"collect_cash_amount"`
	DeadWeight        string  `json:"dead_weight"`
	LabelGeneratedOn  string  `json:"label_generated_on"`
}

type ShippingLabelRequestItem struct {
	ProductName  string  `json:"product_name"`
	SKU          string  `json:"sku"`
	Quantity     int     `json:"quantity"`
	TotalInclGst float64 `json:"total_incl_gst"`
}

type Charges struct {
	ShippingCharges float64 `json:"shipping_charges"`
	Discount        float64 `json:"discount"`
	NetTotal        float64 `json:"net_total"`
}

type ReturnAddress struct {
	ShippedBy string `json:"shipped_by"`
}
