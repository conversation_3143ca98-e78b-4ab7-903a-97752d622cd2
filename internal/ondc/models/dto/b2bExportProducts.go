package dto

import "gorm.io/datatypes"

// ProductCatalogueExportRequest represents the request structure for product catalogue export
type ProductCatalogueExportRequest struct {
	Data ProductCatalogueExportData `json:"data"`
}

type ProductCatalogueExportData struct {
	Sellers []string `json:"sellers" binding:"required"`
}

// ProductCatalogueRecord represents the structure for product catalogue CSV export
type ProductCatalogueRecord struct {
	Seller                  string   `json:"seller"`
	ProductID               int64    `json:"product_id"`
	SKUCode                 string   `json:"sku_code"`
	HSNCode                 *string  `json:"hsn_code"`
	SKUName                 string   `json:"sku_name"`
	HindiName               string   `json:"hindi_name"`
	HindiCategory           string   `json:"hindi_category"`
	QuantityGrammage        string   `json:"quantity_grammage"`
	PackSize                int32    `json:"pack_size"`
	CaseSize                *int32   `json:"case_size"`
	MRP                     float64  `json:"mrp"`
	WholesaleRate           float64  `json:"wholesale_rate"`
	OfferWholesaleRate      *float64 `json:"offer_wholesale_rate"`
	ExpiryMonths            *int32   `json:"expiry_months"`
	TaxPercent              *float64 `json:"tax_percent"`
	MaxCap                  *int32   `json:"max_cap"`
	Tag                     *string  `json:"tag"`
	Rank                    int32    `json:"rank"`
	BaseSKUCode             string   `json:"base_sku_code"`
	InventoryQuantity       *int32   `json:"inventory_quantity"`
	Unit                    *string  `json:"unit"`
	ImageURLsCommaSeparated string   `json:"image_urls_comma_separated"`
	IsActive                bool     `json:"is_active"`
	IsOOS                   bool     `json:"is_oos"`
}

// ProductCatalogueExportResponse represents the API response structure
type ProductCatalogueExportResponse struct {
	Data    []ProductCatalogueRecord `json:"data"`
	Count   int                      `json:"count"`
	Message string                   `json:"message,omitempty"`
}

// ProductCatalogueCSVExportRequest for CSV-specific requests
type ProductCatalogueCSVExportRequest struct {
	Sellers  []string `json:"sellers"`
	Format   string   `json:"format,omitempty"` // "csv" or "json"
	Filename string   `json:"filename,omitempty"`
}

// ProductCatalogueFilterRequest for filtered exports
type ProductCatalogueFilterRequest struct {
	Sellers    []string `json:"sellers"`
	Categories []string `json:"categories,omitempty"`
	IsActive   *bool    `json:"is_active,omitempty"`
	IsOOS      *bool    `json:"is_oos,omitempty"`
	MinMRP     *float64 `json:"min_mrp,omitempty"`
	MaxMRP     *float64 `json:"max_mrp,omitempty"`
}

type KiranaBazarProductCatalogue struct {
	ID              int64          `db:"id" json:"id"`
	Name            string         `db:"name" json:"name"`
	Code            string         `db:"code" json:"code"`
	Seller          string         `db:"seller" json:"seller"`
	SizeVariantCode int64          `db:"size_variant_code" json:"size_variant_code"`
	Rank            int32          `db:"rank" json:"rank"`
	Meta            string         `db:"meta" json:"meta"`
	ImageURLs       string         `db:"image_urls" json:"image_urls"`
	MediaUrls       datatypes.JSON `db:"media_urls" json:"media_urls,omitempty"` // Optional field for media URLs
	IsActive        bool           `db:"is_active" json:"is_active"`
	IsOOS           bool           `db:"is_oos" json:"is_oos"`
	Category        string         `db:"category" json:"category"`
	DisplayQuantity *int32         `db:"display_quantity" json:"display_quantity"`
}

type KiranaBazarProductCatalogueMeta struct {
	MRPNumber          *float64 `json:"mrp_number"`
	HindiName          string   `json:"hindi_name"`
	MaxCap             *int32   `json:"max_cap"`
	PackSize           int32    `json:"pack_size"`
	CaseSize           *int32   `json:"case_size"`
	Quantity           string   `json:"quantity"`
	WholesaleRate      float64  `json:"wholesale_rate"`
	BrandWholesaleRate *float64 `json:"brand_wholesale_rate"`
	Tax                *float64 `json:"tax"`
	HSNCode            *string  `json:"hsn_code"`
	BadgeText          *string  `json:"badge_text"`
	ExpiresIn          *int64   `json:"expires_in"`
}

type KiranaBazarProductCatalogueExtendedMeta struct {
	TopBadgeStyles map[string]interface{} `json:"top_badge_styles,omitempty"`
}

type KiranaBazarProductInventory struct {
	ID              int64  `db:"id" json:"id"`
	DisplayQuantity *int32 `db:"display_quantity" json:"display_quantity"`
}
