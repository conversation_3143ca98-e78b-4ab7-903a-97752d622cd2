package dto

import (
	"kc/internal/ondc/models/shared"
)

//type PaymentConditions struct {
//	COD                            bool    `json:"cod"`
//	MinimumAmountForCOD            float64 `json:"minimum_amount_for_cod"`
//	MaximumAmountForCOD            float64 `json:"cod_maximum_amount_for_cod"`
//	PartialPayment                 bool    `json:"partial_payment"`
//	MinimumAmountForPartialPayment float64 `json:"minimum_amount_for_partial_payment"`
//	PartialPaymentMinimumAmount    float64 `json:"partial_payment_minimum_amount"`
//	PartialPaymentMaximumAmount    float64 `json:"partial_payment_maximum_amount"`
//	PartialPaymentPercentage       float64 `json:"partial_payment_percentage"`
//	FullPayment                    bool    `json:"full_payment"`
//}

type PaymentConditions struct {
	Type                     string   `json:"type"`
	MinimumAmountForValidity float64  `json:"minimum_amount_for_cod"`
	MaximumAmountForValidity float64  `json:"cod_maximum_amount_for_cod"`
	MinimumPaymentAmount     float64  `json:"minimum_payment_amount"`
	MaximumPaymentAmount     float64  `json:"maximum_payment_amount"`
	Percentage               float64  `json:"percentage"`
	Fixed                    float64  `json:"fixed"`
	ApplicableOn             []string `json:"applicable_on"`
	DiscountPercentage       float64  `json:"discount_percentage"`
	DiscountFixedAmount      float64  `json:"discount_fixed_amount"`
	MaximumDiscountAmount    float64  `json:"maximum_discount_amount"`
}

type BrandPaymentConditions map[string][]PaymentConditions

type InitiatePaymentRequest struct {
	UserID string               `json:"user_id"`
	Meta   Meta                 `json:"meta"`
	Data   *OrderPaymentDetails `json:"data"`
}

type ValidatePaymentRequest struct {
	UserID string      `json:"user_id"`
	Meta   Meta        `json:"meta"`
	Data   PaymentData `json:"data"`
}

type ValidatePaymentResponse struct {
	Meta Meta              `json:"meta"`
	Data *ValidateResponse `json:"data"`
}

type ValidateResponse struct {
	Status     string     `json:"status"`
	StatusMeta StatusMeta `json:"status_meta"`
}

type StatusMeta struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	Color       string `json:"color"`
	BgColor     string `json:"bg_color"`
	TextColor   string `json:"text_color"`
	ImageUrl    string `json:"image_url"`
	Name        string `json:"name"`
}

type OrderPaymentDetails struct {
	TransactionID string           `json:"transaction_id"`
	Seller        string           `json:"seller"`
	Source        string           `json:"source"`
	Mode          *WaysToPayDataV2 `json:"mode"`
	OrderAmount   float64          `json:"order_amount"`
	PaymentAmount float64          `json:"payment_amount"`
	OrderID       *string          `json:"order_id"`
}

type InitiatePaymentResponse struct {
	PaymentAmount  float64     `json:"payment_amount"`
	Gateway        string      `json:"gateway"`
	PaymentOrderId string      `json:"payment_order_id"`
	TransactionId  string      `json:"transaction_id"`
	NavObj         *shared.Nav `json:"navObj"`
}

type InitializePaymentAndAppCreateKiranaBazarOrderRequest struct {
	UserID      string                        `json:"user_id"`
	OrderData   AppCreateKiranaBazarOrderData `json:"data"`
	OrderMeta   Meta                          `json:"meta"`
	PaymentData *OrderPaymentDetails          `json:"payment_data"`
	Latitude    *float64                      `json:"lat"`
	Longitude   *float64                      `json:"long"`
}

type VerifyPaymentAndAppCreateKiranaBazarOrderRequest struct {
	UserID      string                        `json:"user_id"`
	OrderData   AppCreateKiranaBazarOrderData `json:"data"`
	OrderMeta   Meta                          `json:"meta"`
	PaymentData *PaymentData                  `json:"payment_data"`
}

type PaymentData struct {
	PaymentOrderID   string `json:"payment_order_id"`
	TransactionId    string `json:"transaction_id"`
	Gateway          string `json:"gateway"`
	PaymentID        string `json:"payment_id"`
	GatewayStatus    string `json:"gateway_status"`
	GatewaySignature string `json:"gateway_signature"`
}

type VerifyPaymentAndAppKiranaBazarOrderResponse struct {
	OrderMeta   Meta             `json:"order_meta"`
	OrderData   []OrderDetails   `json:"order_data"`
	Error       AppResponseError `json:"error,omitempty"`
	PaymentData PaymentResponse  `json:"payment_data"`
}

type InitializePaymentAndAppKiranaBazarOrderResponse struct {
	OrderMeta   Meta                     `json:"meta"`
	OrderData   []OrderDetails           `json:"data"`
	Error       AppResponseError         `json:"error,omitempty"`
	PaymentData *InitiatePaymentResponse `json:"payment_data"`
	StatusMeta  *StatusMeta              `json:"status_meta,omitempty"`
}

type PaymentResponse struct {
	PaymentOrderID *string  `json:"payment_order_id"`
	TransactionId  *string  `json:"transaction_id"`
	PaymentAmount  *float64 `json:"payment_amount"`
	PaymentStatus  *string  `json:"payment_status"`
}

type VerifyPaymentResponse struct {
	PaymentAmount  float64 `json:"payment_amount"`
	Gateway        string  `json:"gateway"`
	PaymentOrderId string  `json:"payment_order_id"`
	PaymentId      string  `json:"payment_id"`
	TransactionId  string  `json:"transaction_id"`
	Status         string  `json:"status"`
	Method         string  `json:"method"`
}

type VerifyRefundResponse struct {
	RefundAmount  float64 `json:"refund_amount"`
	Gateway       string  `json:"gateway"`
	RefundOrderId string  `json:"refund_order_id"`
	RefundId      string  `json:"refund_id"`
	TransactionId string  `json:"transaction_id"`
	Status        string  `json:"status"`
}

type CheckAdvancePaymentPossibilityRequest struct {
	Data struct {
		UserID      string  `json:"user_id"`
		OrderID     int64   `json:"order_id"`
		OrderAmount float64 `json:"order_amount"`
	} `json:"data"`
}

type CheckAdvancePaymentPossibilityResponse struct {
	Error                 AppResponseError `json:"error,omitempty"`
	PaymentAllowed        bool             `json:"payment_allowed"`
	AdvancePaymentAmount  float64          `json:"advance_payment_amount"`
	PartialPaymentOptions []float64        `json:"partial_payment_options"`
	PhoneNumber           string           `json:"phone_number"`
}

type InitiatePaymentFromB2BRequest struct {
	Data struct {
		OrderId         int64   `json:"order_id"`
		PhoneNumber     string  `json:"phone_number"`
		PaymentAmount   float64 `json:"payment_amount"`
		PaymentDiscount float64 `json:"payment_discount"`
	} `json:"data"`
}

type InitiatePaymentFromB2BResponse struct {
	Error            AppResponseError `json:"error,omitempty"`
	PaymentLink      string           `json:"payment_link"`
	PaymentInitiated bool             `json:"payment_initiated"`
	PaymentOrderId   string           `json:"payment_order_id"`
}

type RefundPaymentApiRequest struct {
	UserID         string  `json:"user_id"`
	OrderID        int64   `json:"order_id"`
	Amount         float64 `json:"amount"`
	InstantPayment *bool   `json:"instant_payment"`
	Force          *bool   `json:"force"`
	Reason         *string `json:"reason"`
	Source         *string `json:"source"`
}

type RefundPaymentApiResponse struct {
	Error               AppResponseError `json:"error,omitempty"`
	RefundInitiated     bool             `json:"refund_initiated"`
	RefundOrderId       string           `json:"refund_order_id"`
	RefundTransactionId string           `json:"refund_transaction_id"`
	RefundAmount        float64          `json:"refund_amount"`
	RefundStatus        string           `json:"refund_status"`
	RefundGateway       string           `json:"refund_gateway"`
}

type RefreshOrderRequest struct {
	UserID  string  `json:"user_id"`
	OrderID int64   `json:"order_id"`
	Source  *string `json:"source"`
}

type RefreshOrderResponse struct {
	Error  AppResponseError `json:"error,omitempty"`
	Status string           `json:"status"`
}

type RefundPaymentRequest struct {
	PaymentOrderId string `json:"payment_order_id"`
	KCOrderId      int64  `json:"kc_order_id"`
	PaymentId      string `json:"payment_id"`
	RefundAmount   int    `json:"refund_amount"`
	InstantPayment *bool  `json:"instant_payment"`
}

type RefundPaymentResponse struct {
	ID             string  `json:"id"`
	Entity         string  `json:"entity"`
	Amount         int     `json:"amount"`
	Receipt        *string `json:"receipt,omitempty"`
	Currency       string  `json:"currency"`
	PaymentID      string  `json:"payment_id"`
	CreatedAt      int64   `json:"created_at"`
	BatchID        *string `json:"batch_id,omitempty"`
	Status         string  `json:"status"`
	SpeedProcessed string  `json:"speed_processed"`
	SpeedRequested string  `json:"speed_requested"`
}

type GetUnmappedRNNRequest struct {
	Amount  float64 `json:"amount"`
	Refresh bool    `json:"refresh"`
}

type RNNItem struct {
	RNN       string
	Amount    float64
	PaymentId string
}

type GetUnmappedRNNResponse struct {
	Error AppResponseError `json:"error,omitempty"`
	Data  []RNNItem        `json:"data"`
}
