package dto

type PushOrderTo3PLRequest struct {
	Data struct {
		OrderID   string    `json:"order_id"`
		Package   []Package `json:"package"`
		UpdatedBy string    `json:"updated_by"`
	}
}

type PushOrderTo3PLResponse struct {
	Status string                     `json:"status"`
	Data   PushOrderTo3PLResponseData `json:"data"`
}

type PushOrderTo3PLResponseData struct {
	OrderID     string `json:"order_id"`
	OMSOrderID  string `json:"oms_order_id"`
	InvoiceID   string `json:"invoice_id"`
	InvoiceURL  string `json:"invoice_url"`
	AWBNumber   string `json:"awb_number"`
	Courier     string `json:"courier"`
	LabelURL    string `json:"label_url"`
	ManifestID  string `json:"manifest_id"`
	ManifestURL string `json:"manifest_url"`
}
