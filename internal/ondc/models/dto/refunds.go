package dto

// GetOrderProductsRequest represents the request for getting order products
type GetOrderProductsRequest struct {
	UserID  string `json:"user_id"`
	OrderID int64  `json:"order_id"`
}

// OrderProduct represents a product in an order
type OrderProduct struct {
	ItemID       string  `json:"item_id"`
	ItemName     string  `json:"item_name"`
	ItemURL      string  `json:"item_url"`
	Quantity     int     `json:"quantity"`
	Price        float64 `json:"price"`
	TotalPrice   float64 `json:"total_price"`
	RefundAmount float64 `json:"refund_amount"`
}

// GetOrderProductsResponse represents the response for getting order products
type GetOrderProductsResponse struct {
	Error    AppResponseError `json:"error,omitempty"`
	Products []RefundItemInfo `json:"products"`
	OrderID  int64            `json:"order_id"`
}

// RefundOption represents a refund option
type RefundOption struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Amount      float64 `json:"amount"`
}

// GetRefundOptionsRequest represents the request for getting refund options
type GetRefundOptionsRequest struct {
	UserID           string            `json:"user_id"`
	OrderID          int64             `json:"order_id"`
	RefundAmount     *float64          `json:"refund_amount"`
	RefundedProducts *[]RefundItemInfo `json:"refunded_products"`
}

// GetRefundOptionsResponse represents the response for getting refund options
type GetRefundOptionsResponse struct {
	Error                 AppResponseError `json:"error,omitempty"`
	Options               []RefundOption   `json:"options"`
	AlreadyRefundedAmount float64          `json:"already_refunded_amount"`
	OrderID               int64            `json:"order_id"`
}

// InitiateRefundRequest represents the request for initiating a refund
type InitiateRefundRequest struct {
	UserID           string            `json:"user_id"`
	OrderID          int64             `json:"order_id"`
	Force            *bool             `json:"force"`
	RefundMode       RefundOption      `json:"refund_mode"`
	RefundAmount     *float64          `json:"refund_amount"`
	RefundedProducts *[]RefundItemInfo `json:"refunded_products"`
	UpdatedBy        string            `json:"updated_by"`
	Reason           *string           `json:"reason"`
	RefundSource     *string           `json:"refund_source"`
}

// InitiateRefundResponse represents the response for initiating a refund
type InitiateRefundResponse struct {
	Error               AppResponseError `json:"error,omitempty"`
	RefundInitiated     bool             `json:"refund_initiated"`
	RefundOrderId       string           `json:"refund_order_id"`
	RefundTransactionId string           `json:"refund_transaction_id"`
	RefundAmount        float64          `json:"refund_amount"`
	RefundStatus        string           `json:"refund_status"`
	RefundGateway       string           `json:"refund_gateway"`
}

type RefundItemInfo struct {
	Description            string  `json:"description"`
	SKU                    string  `json:"sku"`
	Quantity               int     `json:"quantity"`
	RefundedQuantity       int     `json:"refunded_quantity"`
	UnitPrice              float64 `json:"unit_price"`
	UnitPriceAfterDiscount float64 `json:"unit_price_after_discount"`
	ItemValue              float64 `json:"item_value"`
	ItemValueAfterDiscount float64 `json:"item_value_after_discount"`
	ItemDiscount           float64 `json:"item_discount"`
	ImageUrl               string  `json:"image_url"`
	HindiName              string  `json:"hindi_name"`
}

// UpdateKYCRequest represents the request structure for updating KYC status
type UpdateKYCRequest struct {
	UserID string `json:"user_id" validate:"required"`
}

// UpdateKYCResponse represents the response structure for KYC update operations
type UpdateKYCResponse struct {
	RefundKYCUpdated bool             `json:"refund_kyc_updated"`
	Error            AppResponseError `json:"error,omitempty"`
}
