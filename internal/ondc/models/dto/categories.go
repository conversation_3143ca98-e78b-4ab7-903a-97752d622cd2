package dto

import "kc/internal/ondc/models/shared"

type SearchCategory struct {
	ImageURL string  `json:"image_url"`
	Label    string  `json:"label"`
	Value    string  `json:"value"`
	ID       *string `json:"id"`
	BgColor  string  `json:"bg_color"`
}

type CategoriesResp struct {
	Carousel   []string         `json:"carousel"`
	Categories []SearchCategory `json:"categories"`
	Error      AppResponseError `json:"error,omitempty"`
}

type GetCategoriesRequest struct {
	Domain    string `json:"domain,omitempty"`
	SubDomain string `json:"sub_domain,omitempty"`
	Source    string `json:"source"`
}

type GetCategoriesResponse struct {
	Categories []SearchCategory `json:"categories"`
	Domain     string           `json:"domain"`
	SubDomain  string           `json:"sub_domain"`
}

type CategoryWidgetRequestData struct {
	CategoryID string  `json:"category_id"`
	Seller     string  `json:"seller"`
	ScreenTag  *string `json:"screen_tag"`
	ScreenType string  `json:"screen_type"` // e.g., "products", "widgets"
	Latitude   float64 `json:"latitude"`
	Longitude  float64 `json:"longitude"`
	PostalCode string  `json:"postal_code"`
}

type GetCategoryWidgetsRequest struct {
	Meta   Meta                      `json:"meta"`
	UserId string                    `json:"user_id"`
	Data   CategoryWidgetRequestData `json:"data"`
}

type GetCategoriesWidgetsResponseData struct {
	SubType         string  `json:"sub_type"`
	Data            interface{}
}

type GetCategoriesWidgetsResponse struct {
	Meta   Meta                             `json:"meta"`
	Data   GetCategoriesWidgetsResponseData `json:"data"` // Use interface{} to allow flexible response data
	Error  AppResponseError                 `json:"error,omitempty"`
	NavObj *shared.Nav                      `json:"nav_obj,omitempty"`
}
