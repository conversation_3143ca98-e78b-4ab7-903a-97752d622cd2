package dto

import "encoding/json"

type AppConfirmRequest struct {
	Data   AppConfirmRequestData `json:"data"`
	UserID string                `json:"user_id"`
	Meta   Meta                  `json:"meta"`
}

type AppConfirmRequestData struct {
	Provider    *OrderProvider    `json:"provider,omitempty"`
	Items       []OrderItemsInner `json:"items,omitempty"`
	Tags        []TagGroup        `json:"tags,omitempty"`
	Payment     []Payment         `json:"payment,omitempty"`
	Fulfillment []Fulfillment     `json:"fulfillment,omitempty"`
	Quote       *Quotation        `json:"quote,omitempty"`
	Billing     *Billing          `json:"billing,omitempty"`
	AddressID   string            `json:"address_id"`
}

type AppConfirmProvider struct {
	ProviderID       string   `json:"provider_id"`
	ProviderTTL      string   `json:"provider_ttl"`
	ProviderLocation []string `json:"provider_locations"`
}

type AppConfirmResponse struct {
	PaymentStatus string           `json:"payment_status"`
	ShipmentStart Stops            `json:"shipment_start"`
	OrderState    string           `json:"order_state"`
	Order         Order            `json:"order,omitempty"`
	Error         AppResponseError `json:"error,omitempty"`
	MessageID     *string          `json:"message_id"`
}

func (r *AppConfirmResponse) MarshalBinary() (data []byte, err error) {
	bytes, err := json.Marshal(r)
	return bytes, err
}
