package dto

import (
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/shared"
	"time"

	"gorm.io/datatypes"
)

// Get Order Details
type AppGetKiranaBazarOrderRequest struct {
	UserID        string                     `json:"user_id"`
	Email         string                     `json:"email"`
	Data          AppGetKiranaBazarOrderData `json:"data"`
	Meta          Meta                       `json:"meta"`
	RequestSource string                     `json:"request_source"`
}

type AppGetKiranaBazarOrderData struct {
	OrderID string `json:"order_id"`
	Source  string `json:"source"`
	Seller  string `json:"seller"`
	Email   string `json:"email"`
}

type AppKiranaBazarOrderResponse struct {
	OrderStatusMeta []DisplayStatusCount `json:"status_meta"`
	Meta            Meta                 `json:"meta"`
	Data            []OrderDetails       `json:"data"`
	Error           AppResponseError     `json:"error,omitempty"`
}

type DisplayOrderStatusCount struct {
	DisplayStatus string `json:"display_status" gorm:"column:display_status"`
	DisplayCount  int    `json:"display_count" gorm:"column:order_count"`
}

type DisplayStatusCount struct {
	Label string `json:"label"`
	Value string `json:"value"`
	Count int    `json:"count"`
}

type DisplayStatusResult struct {
	Data []DisplayStatusCount
	Err  error
}

type CancelOption struct {
	Key           string `json:"key"`
	Value         string `json:"value"`
	MandatoryText bool   `json:"mandatory_text"`
}

type CancelOrderMeta struct {
	Title             string         `json:"title"`
	MixPanelEventName string         `json:"mixpanel_event_name"`
	Options           []CancelOption `json:"options"`
}

type OrderDetails struct {
	ID                 string                  `json:"id"`
	TransactionID      string                  `json:"transaction_id"`
	MessageID          string                  `json:"message_id"`
	OrderStatus        string                  `json:"order_status"`
	PaymentStatus      string                  `json:"payment_status"`
	DisplayStatus      string                  `json:"display_status"`
	ProcessingStatus   string                  `json:"processing_status"`
	DeliveryStatus     string                  `json:"delivery_status"`
	DeliveryStatusInfo string                  `json:"delivery_status_info,omitempty"`
	OrderPayment       OrderPayment            `json:"order_payment"`
	AllowPayment       bool                    `json:"allow_payment"`
	OrderDate          time.Time               `json:"order_date"`
	OrderDateString    string                  `json:"order_date_string"`
	OrderTimeString    string                  `json:"order_time_string"`
	TotalItems         int                     `json:"total_items"`
	OrderDetails       datatypes.JSON          `json:"order_details"`
	OrderStatusDetails *KiranaBazarOrderStatus `json:"order_status_details,omitempty"`
	Seller             string                  `json:"seller"`
	TotalAmount        float64                 `json:"total_amount"`
	TotalAmountString  string                  `json:"total_amount_string"`
	TrackingLink       string                  `json:"tracking_link"`
	IsCancellable      bool                    `json:"is_cancellable"`
	IsAutoConfirmed    bool                    `json:"is_auto_confirmed"`
	CancelOrderMeta    *CancelOrderMeta        `json:"cancel_order_meta,omitempty"`
	Style              map[string]interface{}  `json:"style"`
	PaidAmount         *float64                `json:"paid_amount"`
	PaidAmountProof    []string                `json:"paid_amount_proof"`
	AdvanceTaken       bool                    `json:"advance_taken"`
	Note               string                  `json:"note"`
	CallStatus         string                  `json:"call_status"`
	CancelReason       string                  `json:"cancel_reason"`
	ReturnedReason     string                  `json:"returned_reason"`
	OrderTags          []string                `json:"order_tags,omitempty"`
	IvrStatus          string                  `json:"ivr_status"`
	OrderActivityID    string                  `json:"order_activity_id"`
	AssignedTo         string                  `json:"assigned_to"`
	Banner             map[string]interface{}  `json:"banner,omitempty"`
	PrintingLabel      *string                 `json:"printing_label,omitempty"`
	Picklist           *string                 `json:"picklist,omitempty"`
	ExtInvoice         *string                 `json:"ext_invoice,omitempty"`
	ExtInvoiceNumber   *string                 `json:"ext_invoice_number,omitempty"`
	PushToOMS          *bool                   `json:"push_to_oms,omitempty"`
	UserPhoneNumber    string                  `json:"user_phone_number"`
	UserName           string                  `json:"user_name"`
	StatusDescription  *string                 `json:"status_description,omitempty"`
	Cta                *Cta                    `json:"cta,omitempty"`
	PackgeDetails      *shared.PackageDetails  `json:"package_details,omitempty"`
	BrandLogoUrl       string                  `json:"brand_logo_url,omitempty"`
	BrandName          string                  `json:"brand_name,omitempty"`
	Info               *OrderDetailInfo        `json:"info,omitempty"`
	ConfirmCod         bool                    `json:"confirm_cod,omitempty"`
	AllowedActions     []string                `json:"allowed_actions,omitempty"`
}

type OrderDetailInfo struct {
	Text    string `json:"text,omitempty"`
	BgColor string `json:"bg_color,omitempty"`
	Color   string `json:"color,omitempty"`
}

type OrderPayment struct {
	PaymentID            string           `json:"payment_id"`
	PaymentMethod        string           `json:"payment_method"`
	PaymentTransactionID string           `json:"payment_transaction_id"`
	Amount               float64          `json:"amount"`
	PaymentStatus        string           `json:"payment_status"`
	PaymentOrderID       *string          `json:"payment_order_id"`
	PaymentAmount        *float64         `json:"payment_amount"`
	PaymentPaidAmount    *float64         `json:"payment_paid_amount"`
	StatusMeta           *StatusMeta      `json:"style_meta"`
	PaymentMethodMeta    *WaysToPayDataV2 `json:"payment_method_meta"`
	NavOjb               *shared.Nav      `json:"navObj"`
}

// Create an Order
type AppCreateKiranaBazarOrderRequest struct {
	UserID      string                        `json:"user_id"`
	Data        AppCreateKiranaBazarOrderData `json:"data"`
	Meta        Meta                          `json:"meta"`
	PaymentData *OrderPaymentDetails          `json:"payment_data"`
}

type AppCreateKiranaBazarOrderData struct {
	AddressID   int                  `json:"address_id"`
	BillBreakUp BillDetails          `json:"bill_breakup"`
	Seller      string               `json:"seller"`
	OrderID     string               `json:"order_id"`
	Email       string               `json:"email"`
	Cart        []shared.SellerItems `json:"cart"`
	Latitude    *float64             `json:"latitude"`
	Longitude   *float64             `json:"longitude"`
}

type AppCreateKiranaBazarOrderResponse struct {
	Meta  Meta             `json:"meta"`
	Data  OrderDetails     `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type AppCancelKiranaBazarOrderRequest struct {
	UserID string                        `json:"user_id"`
	Data   AppCancelKiranaBazarOrderData `json:"data"`
	Meta   Meta                          `json:"meta"`
}

type AppCancelKiranaBazarOrderData struct {
	OrderID     string `json:"order_id"`
	Message     string `json:"message"`
	Reason      string `json:"reason"`
	Explanation string `json:"explanation"`
	Email       string `json:"email"`
	Source      string `json:"source"`
	Seller      string `json:"seller"`
	StatusType  string `json:"status_type"`
}

type AppCancelKiranaBazarOrderResponse struct {
	Meta  Meta                          `json:"meta"`
	Data  AppCancelKiranaBazarOrderData `json:"data"`
	Error AppResponseError              `json:"error,omitempty"`
}

type KiranaBazarOrderStatus struct {
	EasyEcomOrderHistory  []EasyecomOrderHistory `json:"easyecom_order_history,omitempty"`
	ShippingHistory       []ShippingHistory      `json:"shipping_history,omitempty"`
	Documents             *Documents             `json:"documents,omitempty"`
	OrderID               *int64                 `json:"order_id,omitempty"`
	Courier               string                 `json:"courier,omitempty"`
	AWBNumber             string                 `json:"awb_number,omitempty"`
	TentativeDeliveryDate *string                `json:"tentative_delivery_date,omitempty"`
}

type OrderReturnedRequest struct {
	UserID string            `json:"user_id"`
	Data   OrderReturnedData `json:"data"`
	Meta   Meta              `json:"meta"`
}

type OrderReturnedData struct {
	OrderID     string `json:"order_id"`
	Status      string `json:"status"`
	Message     string `json:"message"`
	Reason      string `json:"reason"`
	Explanation string `json:"explanation"`
	Email       string `json:"email"`
	Source      string `json:"source"`
	Seller      string `json:"seller"`
	StatusType  string `json:"status_type"`
}

type OrderReturnedResponse struct {
	Meta  Meta              `json:"meta"`
	Data  OrderReturnedData `json:"data"`
	Error AppResponseError  `json:"error,omitempty"`
}

type OrderDeliveredRequest struct {
	UserID string             `json:"user_id"`
	Data   OrderDeliveredData `json:"data"`
	Meta   Meta               `json:"meta"`
}

type OrderDeliveredData struct {
	OrderID     string `json:"order_id"`
	Message     string `json:"message"`
	Explanation string `json:"explanation"`
	Email       string `json:"email"`
	Source      string `json:"source"`
	DeliveredAt int64  `json:"delivered_at"`
	OrderStatus string `json:"order_status"`
	StatusType  string `json:"status_type"`
}

type OrderDeliveredResponse struct {
	Meta  Meta               `json:"meta"`
	Data  OrderDeliveredData `json:"data"`
	Error AppResponseError   `json:"error,omitempty"`
}

type OrderFailedDeliveryRequest struct {
	UserID string                  `json:"user_id"`
	Data   OrderFailedDeliveryData `json:"data"`
	Meta   Meta                    `json:"meta"`
}

type OrderFailedDeliveryData struct {
	OrderID     string `json:"order_id"`
	Message     string `json:"message"`
	Explanation string `json:"explanation"`
	Email       string `json:"email"`
	Source      string `json:"source"`
	OrderStatus string `json:"order_status"`
	StatusType  string `json:"status_type"`
}

type OrderFailedDeliveryResponse struct {
	Meta  Meta                    `json:"meta"`
	Data  OrderFailedDeliveryData `json:"data"`
	Error AppResponseError        `json:"error,omitempty"`
}

type KiranaBazarOrderTags struct {
	ID          uint64    `json:"id"`
	Tag         string    `json:"tag"`
	Description string    `json:"description"`
	IsActive    bool      `json:"is_active"`
	UpdatedBy   string    `json:"updated_by"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type KiranaBazarOrderDetailsTags struct {
	TagId int    `json:"tag_id"`
	Tag   string `json:"tag"`
}

type ConfirmOrderData struct {
	OrderID     string    `json:"order_id"`
	Message     string    `json:"message"`
	Explanation string    `json:"explanation"`
	Email       string    `json:"email"`
	Source      string    `json:"source"`
	OrderStatus string    `json:"order_status"`
	OrderMeta   OrderMeta `json:"order_meta"`
	UserID      string    `json:"user_id"`
}

type DispatchedOrderRequest struct {
	Data DispatchedOrderData `json:"data"`
	Meta Meta                `json:"meta"`
}

type DispatchedOrderData struct {
	OrderID        string `json:"order_id"`
	DispatchedTime int64  `json:"dispatched_time"` // not mandatory
	UpdatedBy      string `json:"updated_by"`
}
type ConfirmOrderRequest struct {
	Data ConfirmOrderData `json:"data"`
	Meta Meta             `json:"meta"`
}

type ConfirmOrderResponse struct {
	Meta  Meta             `json:"meta"`
	Data  ConfirmOrderData `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type OrderReturnReceivedRequestData struct {
	OrderID     string    `json:"order_id"`
	Message     string    `json:"message"`
	Seller      string    `json:"seller"`
	Email       string    `json:"updated_by"`
	OrderMeta   OrderMeta `json:"order_meta"`
	OrderStatus string    `json:"order_status"`
	StatusType  string    `json:"status_type"`
	Source      string    `json:"source"`
}
type OrderReturnReceivedRequest struct {
	Data OrderReturnReceivedRequestData `json:"data"`
	Meta Meta                           `json:"meta"`
}

type OrderReturnReceivedResponse struct {
	Meta  Meta                           `json:"meta"`
	Data  OrderReturnReceivedRequestData `json:"data"`
	Error AppResponseError               `json:"error,omitempty"`
}

type ShipmentCreatedRequest struct {
	Data ShipmentCreatedRequestData `json:"data"`
	Meta Meta                       `json:"meta"`
}

type ShipmentCreatedRequestData struct {
	// Order Info
	OrderID     string `json:"order_id"`
	OMSOrderID  string `json:"oms_order_id"`
	OrderStatus string `json:"order_status"`
	StatusType  string `json:"status_type"`

	// Shipping Details
	Courier           string                `json:"courier"`
	CourierRequested  string                `json:"courier_requested"`
	CourierAssigned   string                `json:"courier_assigned"`
	AWBNumber         string                `json:"awb_number"`
	ShippingLabel     string                `json:"shipping_label"`
	PackageDetails    shared.PackageDetails `json:"package_details"`
	KCShip            bool                  `json:"kc_ship"`
	ShipmentCreatedAt int64                 `json:"shipment_created_at"`

	// invoicing details
	CSGT             float64 `json:"csgt"`
	IGST             float64 `json:"igst"`
	SGST             float64 `json:"sgst"`
	BuyerGST         string  `json:"buyer_gst"`
	NoOfSkus         int     `json:"no_of_skus"`
	ItemQuantity     int     `json:"item_quantity"`
	Discount         float64 `json:"discount"`
	InvoiceURL       string  `json:"invoice_url"`
	InvoiceID        string  `json:"invoice_id"`
	InvoiceAmount    float64 `json:"invoice_amount"`
	InvoiceCreatedAt int64   `json:"invoice_created_at"`

	// meta
	OMS       string `json:"oms"`
	UpdatedBy string `json:"updated_by"`
	Source    string `json:"source"`
}

type ShipmentCreatedResponse struct {
	Meta  Meta                       `json:"meta"`
	Data  ShipmentCreatedRequestData `json:"data"`
	Error AppResponseError           `json:"error,omitempty"`
}

type UpdateProgressWidgetData struct {
	OrderID      string                       `json:"order_id"`
	Amount       float64                      `json:"amount"`
	Status       string                       `json:"status"`
	OrderDetails *dao.KiranaBazarOrderDetails `json:"order_details,omitempty"`
	Seller       string                       `json:"seller"`
}

type UpdateProgressWidgetRequest struct {
	UserID string                   `json:"user_id"`
	Data   UpdateProgressWidgetData `json:"data"`
}
