package dto

type GetRecommendedProductsRequest struct {
	Data   GetRecommendedProductsData `json:"data"`
	UserID string                     `json:"user_id"`
	Meta   Meta                       `json:"meta"`
}

type GetRecommendedProductsData struct {
	Seller     string   `json:"seller"`
	ProductId  string   `json:"product_id"`
	CategoryId []string `json:"category_id,omitempty"`
}

type GetRecommendedProductsResponse struct {
	Meta  Meta                    `json:"meta"`
	Data  RecommendedProductsData `json:"data"`
	Error AppResponseError        `json:"error,omitempty"`
}

type RecommendedProductsData struct {
	RecommendedProducts interface{}            `json:"recommended_products,omitempty"` // Optional field for widgets
	Title               string                 `json:"title"`
	SubText             string                 `json:"sub_text"`
	HighlightedSubTexts []HighlightedTextData  `json:"highlighted_sub_texts,omitempty"`
	Styles              map[string]interface{} `json:"styles,omitempty"`    // Optional field for styles
	ImageUrl            string                 `json:"image_url,omitempty"` // Optional field for image URL
}
