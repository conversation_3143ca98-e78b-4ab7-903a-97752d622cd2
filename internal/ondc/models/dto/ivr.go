package dto

type IVRStatusUpdateRequest struct {
	IvrStatus  string `json:"ivr_status"`
	Status     string `json:"status"`
	RetryCount int    `json:"retry_count"`
	CallCount  int    `json:"call_count"`
	OrderId    int64  `json:"order_id"`
}

type IVRStatusUpdateResponse struct {
	Success bool                   `json:"success"`
	Data    IVRStatusUpdateRequest `json:"data"`
	Error   string                 `json:"error"`
}

type IVRRequest struct {
	FromPhone    string      `json:"from_phone"`
	FlowID       int         `json:"flow_id"`
	ToPhone      string      `json:"to_phone"`
	CustomFields interface{} `json:"custom_fields"`
	ID           string      `json:"id"`
}

// Call represents the main IVR response structure
type Call struct {
	Sid                   string       `json:"Sid"`
	ParentCallSid         *string      `json:"ParentCallSid,omitempty"`
	DateCreated           string       `json:"DateCreated"`
	DateUpdated           string       `json:"DateUpdated"`
	AccountSid            string       `json:"AccountSid"`
	To                    string       `json:"To"`
	From                  string       `json:"From"`
	PhoneNumberSid        string       `json:"PhoneNumberSid"`
	Status                string       `json:"Status"`
	StartTime             string       `json:"StartTime"`
	EndTime               *string      `json:"EndTime,omitempty"`
	Duration              *int         `json:"Duration,omitempty"`
	Price                 *float64     `json:"Price,omitempty"`
	Direction             string       `json:"Direction"`
	AnsweredBy            *string      `json:"AnsweredBy,omitempty"`
	ForwardedFrom         *string      `json:"ForwardedFrom,omitempty"`
	CallerName            *string      `json:"CallerName,omitempty"`
	Uri                   string       `json:"Uri"`
	RecordingUrl          *string      `json:"RecordingUrl,omitempty"`
	CustomField           string       `json:"CustomField"`
	PreSignedRecordingUrl string       `json:"PreSignedRecordingUrl"`
	ParsedCustomField     *CustomField `json:"-"` // For storing parsed CustomField data
}

// IVRResponse represents the top-level response structure
type IVRResponse struct {
	Call Call `json:"Call"`
}

type ExotelWebhookRequest struct {
	To                          string `json:"To"`
	From                        string `json:"From"`
	CallTo                      string `json:"CallTo"`
	Digits                      string `json:"digits"`
	Seller                      string `json:"seller"`
	CallSid                     string `json:"CallSid"`
	Created                     string `json:"Created"`
	EndTime                     string `json:"EndTime"`
	FlowID                      string `json:"flow_id"`
	CallFrom                    string `json:"CallFrom"`
	CallType                    string `json:"CallType"`
	Direction                   string `json:"Direction"`
	StartTime                   string `json:"StartTime"`
	TenantID                    string `json:"tenant_id"`
	CurrentTime                 string `json:"CurrentTime"`
	CustomField                 string `json:"CustomField"`
	RecordingUrl                string `json:"RecordingUrl"`
	ForwardedFrom               string `json:"ForwardedFrom"`
	ProcessStatus               string `json:"ProcessStatus"`
	DialCallStatus              string `json:"DialCallStatus"`
	DialWhomNumber              string `json:"DialWhomNumber"`
	DialCallDuration            string `json:"DialCallDuration"`
	HangupLatencyStartTime      string `json:"HangupLatencyStartTime"`
	HangupLatencyStartTimeExocc string `json:"HangupLatencyStartTimeExocc"`
}

type ExotelIVROrderInfo struct {
	OrderID      int64   `json:"order_id"`
	Seller       string  `json:"seller"`
	OrderValue   float64 `json:"order_value"`
	CartValue    float64 `json:"cart_value"`
	IVRCallCount int     `json:"ivr_call_count"`
	UserID       string  `json:"user_id"`
	RetryCount   int     `json:"retry_count"`
	RequestType  string  `json:"request_type"`
}

type ExotelIVRExomlWebhookRequest struct {
	Status      string `json:"Status"`
	CallSid     string `json:"CallSid"`
	DateUpdated string `json:"DateUpdated"`
}
