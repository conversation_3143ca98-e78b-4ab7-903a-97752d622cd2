package dto

type AppserviceAbilityAPIResponse struct {
	Meta  Meta                     `json:"meta"`
	Data  AppServiceAbilityAPIData `json:"data"`
	Error AppResponseError         `json:"error,omitempty"`
}

type AppServiceAbilityAPIData struct {
	Servicable          bool   `json:"servicable"`
	Message             string `json:"message"`
	MinimumDeliveryDays string `json:"minimum_delivery_days"`
	CourierServiceName  string `json:"courier_service_name"`
}

type AppServiceAbilityAPIRequestData struct {
	Seller string `json:"seller"`
	Source string `json:"source"`
}

type AppServiceAblityAPIRequest struct {
	COD              string                          `json:"cod"`
	PickUpPostCode   string                          `json:"pickup_postcode"`
	DeliveryPostCode string                          `json:"delivery_postcode"`
	Weight           string                          `json:"weight"`
	UserID           string                          `json:"user_id"`
	Data             AppServiceAbilityAPIRequestData `json:"data"`
}

type CourierServiceAblityAPIRequest struct {
	COD              string `json:"cod"`
	PickUpPostCode   string `json:"pickup_postcode"`
	DeliveryPostCode string `json:"delivery_postcode"`
	Weight           string `json:"weight"`
}

type TrackDelhiveryCourierAPIRequest struct {
	AWBNumber *[]string `json:"awb_number"`
	OrderID   []string  `json:"order_id"`
	OMS       string    `json:"oms"`
}
