package dto

// request object to place an order to easyecom
type AppEasyEcomCreateOrderRequest struct {
	Data   AppEasyEcomCreateOrderData `json:"data"`
	UserID string                     `json:"user_id"`
	Meta   Meta                       `json:"meta"`
}

// nested data object for create an order to easyecom
type AppEasyEcomCreateOrderData struct {
	OrderID int `json:"order_id"`
}

type AppEasyEcomCreateOrderResponse struct {
}

// request object sent to easy ecom to place an order
type EasyEcomCreateOrderRequest struct {
	OrderType         string             `json:"orderType"`
	MarketplaceID     int                `json:"marketplaceId"`
	OrderNumber       string             `json:"orderNumber"`
	OrderDate         string             `json:"orderDate"`
	ExpDeliveryDate   string             `json:"expDeliveryDate"`
	Remarks1          string             `json:"remarks1"`
	Remarks2          string             `json:"remarks2"`
	ShippingCost      float64            `json:"shippingCost"`
	Discount          float64            `json:"discount"`
	WalletDiscount    float64            `json:"walletDiscount"`
	PromoCodeDiscount float64            `json:"promoCodeDiscount"`
	PrepaidDiscount   float64            `json:"prepaidDiscount"`
	PaymentMode       int                `json:"paymentMode"`
	CollectableAmount *float64           `json:"collectableAmount,omitempty"`
	GstNumber         *string            `json:"gstNumber,omitempty"`
	PaymentGateway    string             `json:"paymentGateway"`
	ShippingMethod    int                `json:"shippingMethod"`
	IsMarketShipped   int                `json:"is_market_shipped"`
	Items             []EasyEcomItem     `json:"items"`
	Customer          []EasyEcomCustomer `json:"customer"`
	Notes             []CustomField      `json:"notes"`
}

// easyecom requesy for item
type EasyEcomItem struct {
	OrderItemID  string        `json:"OrderItemId"`
	Sku          string        `json:"Sku"`
	ProductName  string        `json:"productName"`
	Quantity     int           `json:"Quantity"`
	Price        float64       `json:"Price"`
	CustomFields []CustomField `json:"custom_fields"`
}

type CustomField struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

// easyecom customer details
type EasyEcomCustomer struct {
	GSTNumber *string         `json:"gst_number"`
	Billing   EasyEcomAddress `json:"billing"`
	Shipping  EasyEcomAddress `json:"shipping"`
}

// easyecom address details
type EasyEcomAddress struct {
	Name         string `json:"name"`
	AddressLine1 string `json:"addressLine1"`
	AddressLine2 string `json:"addressLine2"`
	PostalCode   string `json:"postalCode"`
	City         string `json:"city"`
	State        string `json:"state"`
	Country      string `json:"country"`
	Contact      string `json:"contact"`
	Email        string `json:"email"`
	Latitude     string `json:"latitude,omitempty"`
	Longitude    string `json:"longitude,omitempty"`
}

type EasyEcomCreateOrderResponse struct {
	Code    int                             `json:"code"`
	Message string                          `json:"message"`
	Data    EasyEcomCreateOrderResponseData `json:"data"`
}

type EasyEcomCreateOrderResponseData struct {
	Status     int    `json:"Status"`
	Message    string `json:"Message"`
	SuborderID string `json:"SuborderID"`
	OrderID    string `json:"OrderID"`
	InvoiceID  string `json:"InvoiceID"`
}

type EasyecomOrderHistory struct {
	Status   string `json:"status"`
	StatusID int    `json:"status_id"`
	DateTime string `json:"date_time"`
}

type ShippingHistory struct {
	Status      string  `json:"status"`
	Time        string  `json:"time"`
	DisplayTime string  `json:"display_time"`
	Location    string  `json:"location"`
	Color       *string `json:"color,omitempty"`
}

type Documents struct {
	EasyecomInvoice       string  `json:"easyecom_invoice"`
	Label                 string  `json:"label"`
	Intaxform             *string `json:"intaxform"`
	Outtaxform            *string `json:"outtaxform"`
	Marketplaceinvoice    *string `json:"marketplaceinvoice"`
	MarketplaceTaxInvoice *string `json:"marketplace_tax_invoice"`
	MarketplaceB2CInvoice *string `json:"marketplace_b2c_invoice"`
}

type BreakupTypes struct {
	ItemAmountExcludingTax float64 `json:"Item Amount Excluding Tax"`
	ItemAmountIGST         float64 `json:"Item Amount IGST"`
}

type EasyecomSuborderHistory struct {
	Status   string `json:"status"`
	StatusID int    `json:"status_id"`
	DateTime string `json:"date_time"`
}

type EasyecomSuborderExtra struct {
	Column string `json:"column"`
	Value  string `json:"value"`
}
type OrderItems struct {
	SuborderID              int                       `json:"suborder_id"`
	SuborderNum             string                    `json:"suborder_num"`
	Invoicecode             *string                   `json:"invoicecode"`
	ItemCollectableAmount   float64                   `json:"item_collectable_amount"`
	ShipmentType            string                    `json:"shipment_type"`
	SuborderQuantity        int                       `json:"suborder_quantity"`
	ItemQuantity            int                       `json:"item_quantity"`
	ReturnedQuantity        int                       `json:"returned_quantity"`
	CancelledQuantity       int                       `json:"cancelled_quantity"`
	ShippedQuantity         int                       `json:"shipped_quantity"`
	TaxType                 string                    `json:"tax_type"`
	ProductID               int                       `json:"product_id"`
	CompanyProductID        int                       `json:"company_product_id"`
	SKU                     string                    `json:"sku"`
	ExpiryType              int                       `json:"expiry_type"`
	SKUType                 string                    `json:"sku_type"`
	SubProductCount         int                       `json:"sub_product_count"`
	MarketplaceSKU          string                    `json:"marketplace_sku"`
	ListingRefNumber        string                    `json:"listing_ref_number"`
	ListingID               string                    `json:"listing_id"`
	ProductName             string                    `json:"productName"`
	Description             string                    `json:"description"`
	Category                string                    `json:"category"`
	Brand                   string                    `json:"brand"`
	BrandID                 int                       `json:"brand_id"`
	ModelNo                 string                    `json:"model_no"`
	ProductTaxCode          string                    `json:"product_tax_code"`
	AccountingSKU           *string                   `json:"AccountingSku"`
	EAN                     *string                   `json:"ean"`
	Size                    *string                   `json:"size"`
	Cost                    float64                   `json:"cost"`
	MRP                     int                       `json:"mrp"`
	Weight                  int                       `json:"weight"`
	Discount                int                       `json:"discount"`
	DiscountedPrice         float64                   `json:"discounted_price"`
	SellingPrice            string                    `json:"selling_price"`
	HandlingCharges         float64                   `json:"handling_charges"`
	ShippingCharges         float64                   `json:"shipping_charges"`
	GrossAmount             int                       `json:"gross_amount"`
	TaxRate                 float64                   `json:"tax_rate"`
	TaxAmount               float64                   `json:"tax_amount"`
	ItemTotal               float64                   `json:"item_total"`
	Currency                string                    `json:"currency"`
	Meta                    *string                   `json:"meta"`
	Hsn                     string                    `json:"hsn"`
	CombinedName            string                    `json:"combined_name"`
	CombinedDescription     string                    `json:"combined_description"`
	CreatedAt               string                    `json:"created_at"`
	UpdatedAt               string                    `json:"updated_at"`
	DeletedAt               *string                   `json:"deleted_at"`
	ShippingProviderID      int                       `json:"shipping_provider_id"`
	MarketCId               int                       `json:"market_c_id"`
	MerchantCId             int                       `json:"merchant_c_id"`
	SuborderStatus          string                    `json:"suborder_status"`
	SuborderStatusID        int                       `json:"suborder_status_id"`
	EasyecomSuborderHistory []EasyecomSuborderHistory `json:"easyecom_suborder_history"`
	EasyecomSuborderExtra   []EasyecomSuborderExtra   `json:"easyecom_suborder_extra"`
	ShippingMethod          string                    `json:"shipping_method"`
	IsReplacement           int                       `json:"is_replacement"`
	ReplacementOrder        *string                   `json:"replacement_order"`
	FulfillmentStatus       string                    `json:"fulfillment_status"`
}
type EasyEcomGetOrderDetailsData struct {
	AWBNumber                       string                 `json:"awb_number"`
	AddressLine1                    string                 `json:"address_line_1"`
	AddressLine2                    string                 `json:"address_line_2"`
	AssignedCompanyGST              string                 `json:"assigned_company_gst"`
	AssignedCompanyName             string                 `json:"assigned_company_name"`
	AssignedWarehouseID             int                    `json:"assigned_warehouse_id"`
	AvailableAfter                  *string                `json:"available_after"`
	BatchCreatedAt                  string                 `json:"batch_created_at"`
	BatchID                         int                    `json:"batch_id"`
	BillableWeight                  float64                `json:"billable_weight"`
	BillingAddress1                 string                 `json:"billing_address_1"`
	BillingAddress2                 string                 `json:"billing_address_2"`
	BillingCity                     string                 `json:"billing_city"`
	BillingCountry                  string                 `json:"billing_country"`
	BillingMobile                   string                 `json:"billing_mobile"`
	BillingName                     string                 `json:"billing_name"`
	BillingPinCode                  string                 `json:"billing_pin_code"`
	BillingState                    string                 `json:"billing_state"`
	BillingStateCode                string                 `json:"billing_state_code"`
	BlockSplit                      int                    `json:"blockSplit"`
	BreakupTypes                    BreakupTypes           `json:"breakup_types"`
	BuyerGST                        string                 `json:"buyer_gst"`
	CarrierID                       int                    `json:"carrier_id"`
	ChannelIcon                     string                 `json:"channel_icon"`
	ChannelKey                      string                 `json:"channel_key"`
	ChannelName                     string                 `json:"channel_name"`
	ChannelPlatform                 string                 `json:"channel_platform"`
	ChannelProductInfo              string                 `json:"channel_product_info"`
	City                            string                 `json:"city"`
	CollectableAmount               float64                `json:"collectable_amount"`
	CompanyName                     string                 `json:"company_name"`
	ContactNum                      string                 `json:"contact_num"`
	Country                         string                 `json:"country"`
	CountryCode                     string                 `json:"country_code"`
	Courier                         string                 `json:"courier"`
	CourierAggregatorName           string                 `json:"courier_aggregator_name"`
	CustomerCode                    string                 `json:"customer_code"`
	CustomerName                    string                 `json:"customer_name"`
	Documents                       Documents              `json:"documents"`
	Email                           *string                `json:"email"`
	EasyecomOrderHistory            []EasyecomOrderHistory `json:"easyecom_order_history"`
	ExportDate                      string                 `json:"export_date"`
	FulfillableStatus               int                    `json:"fulfillable_status"`
	HasInvoice                      bool                   `json:"has_invoice"`
	ImportDate                      string                 `json:"import_date"`
	InvoiceCurrencyCode             string                 `json:"invoice_currency_code"`
	InvoiceDate                     string                 `json:"invoice_date"`
	InvoiceDocuments                *string                `json:"invoice_documents"`
	InvoiceID                       int                    `json:"invoice_id"`
	InvoiceNumber                   string                 `json:"invoice_number"`
	LastUpdateDate                  string                 `json:"last_update_date"`
	Latitude                        *string                `json:"latitude"`
	Longitude                       *string                `json:"longitude"`
	ManifestDate                    string                 `json:"manifest_date"`
	ManifestNo                      string                 `json:"manifest_no"`
	MarketCId                       int                    `json:"MarketCId"`
	Marketplace                     string                 `json:"marketplace"`
	MarketplaceID                   int                    `json:"marketplace_id"`
	MarketplaceInvoiceNum           *string                `json:"marketplace_invoice_num"`
	MarketShipped                   int                    `json:"market_shipped"`
	MerchantCId                     int                    `json:"merchant_c_id"`
	Message                         *string                `json:"message"`
	Meta                            interface{}            `json:"meta"`
	OrderDate                       string                 `json:"order_date"`
	OrderID                         int                    `json:"order_id"`
	OrderItems                      []OrderItems           `json:"order_items"`
	OrderQuantity                   int                    `json:"order_quantity"`
	OrderStatus                     string                 `json:"order_status"`
	OrderStatusID                   int                    `json:"order_status_id"`
	OrderType                       string                 `json:"order_type"`
	OrderTypeKey                    string                 `json:"order_type_key"`
	OriginalOrderID                 int                    `json:"original_order_id"`
	PackageHeight                   interface{}            `json:"Package Height"`
	PackageLength                   interface{}            `json:"Package Length"`
	PackageWeight                   interface{}            `json:"Package Weight"`
	PackageWidth                    interface{}            `json:"Package Width"`
	PaymentGatewayTransactionNumber *string                `json:"payment_gateway_transaction_number"`
	PaymentMode                     string                 `json:"payment_mode"`
	PaymentModeID                   int                    `json:"payment_mode_id"`
	PickupAddress                   string                 `json:"pickup_address"`
	PickupCity                      string                 `json:"pickup_city"`
	PickupCountry                   string                 `json:"pickup_country"`
	PickupPinCode                   string                 `json:"pickup_pin_code"`
	PickupState                     string                 `json:"pickup_state"`
	PickupStateCode                 string                 `json:"pickup_state_code"`
	QCPassed                        int                    `json:"qcPassed"`
	ReferenceCode                   string                 `json:"reference_code"`
	ReplacementOrder                int                    `json:"replacement_order"`
	ReplacementOrderID              int                    `json:"replacement_order_id"`
	SalesmanUserID                  int                    `json:"salesmanUserId"`
	SellerGST                       string                 `json:"seller_gst"`
	ShippingHistory                 []ShippingHistory      `json:"shipping_history"`
	ShippingLastUpdateDate          string                 `json:"shipping_last_update_date"`
	ShippingName                    string                 `json:"shipping_name"`
	ShippingStatus                  string                 `json:"shipping_status"`
	ShippingStatusID                int                    `json:"shipping_status_id"`
	State                           string                 `json:"state"`
	StateCode                       string                 `json:"state_code"`
	SuborderCount                   string                 `json:"suborder_count"`
	TCSAmount                       int                    `json:"tcs_amount"`
	TCSRate                         float64                `json:"tcs_rate"`
	Tat                             string                 `json:"tat"`
	TotalAmount                     float64                `json:"total_amount"`
	TotalTax                        float64                `json:"total_tax"`
	TrackingURL                     *string                `json:"tracking_url"`
	WarehouseContact                string                 `json:"warehouse_contact"`
	WarehouseID                     int                    `json:"warehouse_id"`
}
type EasyEcomGetOrderDetailsResponse struct {
	Code    int                           `json:"code"`
	Message string                        `json:"message"`
	Data    []EasyEcomGetOrderDetailsData `json:"data"`
}

type EasyEcomGetOrderDetailsRequest struct {
	Data   EasyEcomGetOrderDetailsRequestData `json:"data"`
	UserID string                             `json:"user_id"`
	Meta   Meta                               `json:"meta"`
}

type EasyEcomGetOrderDetailsRequestData struct {
	OrderID int `json:"order_id"`
}

type EasyEcomGetTrackingDetailsData struct {
	AWBGenerationType         int               `json:"awb_generation_type"`
	AWBNumber                 string            `json:"awbNumber"`
	CarrierID                 *int              `json:"carrier_id"`
	CarrierName               string            `json:"carrierName"`
	City                      string            `json:"city"`
	CompanyName               string            `json:"companyName"`
	CompanyLogo               *string           `json:"companyLogo"`
	CurrentShippingStatus     string            `json:"currentShippingStatus"`
	CustomerMobileNum         string            `json:"customer_mobile_num"`
	CustomerName              string            `json:"customer_name"`
	EDDDay                    string            `json:"edd_day"`
	EDDMonth                  string            `json:"edd_month"`
	EDDYear                   string            `json:"edd_year"`
	ExpectedDeliveryDate      *string           `json:"expectedDeliveryDate"`
	ExpectedDeliveryDateEnd   *string           `json:"expectedDeliveryDateEnd"`
	ExpectedDeliveryDateStart *string           `json:"expectedDeliveryDateStart"`
	InvoiceAmount             float64           `json:"invoiceAmount"`
	InvoiceDate               string            `json:"invoiceDate"`
	InvoiceID                 *int              `json:"invoiceId"`
	LastStatus                *string           `json:"last_status"`
	LastStatusUpdate          string            `json:"last_status_update"`
	MasterCarrierID           *int              `json:"master_carrier_id"`
	OrderDate                 string            `json:"orderDate"`
	OrderID                   *int              `json:"orderId"`
	OrderStatus               string            `json:"orderStatus"`
	PinCode                   string            `json:"pin_code"`
	ReferenceCode             string            `json:"reference_code"`
	ShippingHistory           string            `json:"shippingHistory"`
	ShippingHistory2          []ShippingHistory `json:"shippingHistory2"`
	ShippingStatusID          int               `json:"shipping_status_id"`
	State                     string            `json:"state"`
	StatusID                  *int              `json:"status_id"`
	SuborderCompanyID         int               `json:"suborder_company_id"`
	SuborderID                int               `json:"suborder_id"`
	Tax                       float64           `json:"tax"`
	Tier                      int               `json:"tier"`
}

type EasyEcomGetTrackingDetailsResponse struct {
	Code    int                              `json:"code"`
	Message string                           `json:"message"`
	Data    []EasyEcomGetTrackingDetailsData `json:"data"`
}
