package dto

type WebhookCallDetails struct {
	CallSid                     string `json:"CallSid"`
	CallFrom                    string `json:"CallFrom"`
	CallTo                      string `json:"CallTo"`
	Direction                   string `json:"Direction"`
	ForwardedFrom               string `json:"ForwardedFrom"`
	Created                     string `json:"Created"`
	DialCallDuration            string `json:"DialCallDuration"`
	RecordingUrl                string `json:"RecordingUrl"`
	StartTime                   string `json:"StartTime"`
	EndTime                     string `json:"EndTime"`
	DialCallStatus              string `json:"DialCallStatus"`
	CallType                    string `json:"CallType"`
	DialWhomNumber              string `json:"DialWhomNumber"`
	ProcessStatus               string `json:"ProcessStatus"`
	FlowID                      string `json:"flow_id"`
	TenantID                    string `json:"tenant_id"`
	From                        string `json:"From"`
	To                          string `json:"To"`
	CurrentTime                 string `json:"CurrentTime"`
	CustomField                 string `json:"CustomField"`
	Digits                      string `json:"digits"`
	HangupLatencyStartTimeExocc string `json:"HangupLatencyStartTimeExocc"`
	HangupLatencyStartTime      string `json:"HangupLatencyStartTime"`
	Seller                      string `json:"seller"`
	Type                        string `json:"type"`
}
