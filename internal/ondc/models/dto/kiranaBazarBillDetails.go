package dto

import (
	"gorm.io/datatypes"
	"kc/internal/ondc/models/shared"
)

type GetBillDetailsRequest struct {
	Data   GetBillDetailsData `json:"data"`
	UserID string             `json:"user_id"`
	Meta   Meta               `json:"meta"`
	Email  string             `json:"email"`
}

type GetBillDetailsData struct {
	CouponID          string               `json:"coupon_id"`
	Seller            string               `json:"seller"`
	Source            string               `json:"source"`
	AddressID         string               `json:"address_id"`
	RequestedCashback float64              `json:"requested_cashback"`
	CashbackApplied   *bool                `json:"cashback_applied"`
	PaymentMethod     *WaysToPayDataV2     `json:"payment_method"`
	Cart              []shared.SellerItems `json:"cart"` // this is handled when we get cart details from FE
}

type GetBillDetailsResponse struct {
	Meta  Meta             `json:"meta"`
	Data  BillDetails      `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type BillDetails struct {
	ProductsPricing      []shared.ProductPrice    `json:"products_pricing"`
	TotalPricing         []shared.TotalPricing    `json:"total_pricing"`
	DiscountPricing      []shared.DiscountPricing `json:"discount_pricing"`
	EnableOrdering       bool                     `json:"enable_ordering"`
	Message              string                   `json:"message"`
	OfferMessage         *OfferMessage            `json:"offer_message,omitempty"`
	Coupon               string                   `json:"coupon"`
	Seller               string                   `json:"seller"`
	RewardCoins          *RewardCoinsData         `json:"reward_coins,omitempty"`
	WaysToPay            []WaysToPayDataV1        `json:"ways_to_pay,omitempty"`
	DefaultPaymentMethod *WaysToPayDataV2         `json:"default_payment_method,omitempty"`
	PaymentSection       PaymentSection           `json:"payment_section,omitempty"`
	PaymentSectionHeader PaymentSectionHeader     `json:"payment_section_header,omitempty"`
	CashbackComponent    *CashbackComponent       `json:"cashback_component,omitempty"`
	AutoApplyCashback    bool                     `json:"auto_apply_cashback,omitempty"`
	OrderLoyaltyRewards  *LoyaltyRewardsComponent `json:"order_loyalty_rewards,omitempty"`
	RequestedCashback    float64                  `json:"requested_cashback"`
	Disclaimer           string                   `json:"disclaimer"`
	PaymentBanner        *string                  `json:"payment_banner,omitempty"`
	CTAText              string                   `json:"cta_text"`
	AppliedCoupons       []shared.AppliedCoupon   `json:"applied_coupons,omitempty"`
}

type PaymentSection struct {
	Section1 []WaysToPayDataV2 `json:"section1"`
	Section2 []WaysToPayDataV2 `json:"section2"`
}

type PaymentSectionHeader struct {
	PaymentSection1 *WaysToPayText `json:"payment_section1"`
	PaymentSection2 *WaysToPayText `json:"payment_section2"`
}

type RewardCoinsData struct {
	Amount   int                    `json:"amount"`
	Text     string                 `json:"text"`
	SubText  string                 `json:"sub_text"`
	Badge    map[string]interface{} `json:"badge"`
	ImageURL string                 `json:"image_url"`
	Nav      *shared.Nav            `json:"nav,omitempty"`
}

type WaysToPayDataV1 struct {
	Id      int         `json:"id"`
	Text    string      `json:"text"`
	SubText string      `json:"sub_text"`
	Nav     *shared.Nav `json:"nav,omitempty"`
}

type WaysToPayDataV2 struct {
	Id              int             `json:"id"`
	Title           string          `json:"title"`
	Type            string          `json:"type"`
	ImageUrl        string          `json:"icon_image_url"`
	ActualAmount    float64         `json:"actual_amount"`
	PaymentDiscount float64         `json:"payment_discount"`
	PaymentAmount   float64         `json:"payment_amount"`
	LeftToPayAmount float64         `json:"left_to_pay_amount"`
	DefferAmount    float64         `json:"deffer_amount"`
	Text            WaysToPayText   `json:"text"`
	CTAText         string          `json:"cta_text"`
	PaymentConfig   datatypes.JSON  `json:"payment_config"`
	Notes           PaymentNotes    `json:"notes"`
	BottomBarData   *BottomBarData  `json:"bottom_bar,omitempty"`
	PaymentNudge    *PaymentNudge   `json:"payment_nudge,omitempty"`
	DiscountColors  *DiscountColors `json:"discount_colors"`
	ActiveColor     *string         `json:"active_color"`
}

type DiscountColors struct {
	BgColor   string `json:"bg_color"`
	TextColor string `json:"text_color"`
}

type PaymentNudge struct {
	Heading      string        `json:"heading"`
	SubHeading   string        `json:"sub_heading"`
	ImageURL     string        `json:"image_url"`
	Features     []string      `json:"features"`
	PrimaryCTA   *CTA          `json:"primary_cta"`
	SecondaryCTA *SecondaryCTA `json:"secondary_cta"`
	Color        string        `json:"color"`
}

type SecondaryCTA struct {
	Text          string           `json:"text"`
	PaymentId     int              `json:"payment_id"`
	Option        *WaysToPayDataV2 `json:"option"`
	MixpanelEvent *string          `json:"mixpanel_event_name"`
}

type BottomBarData struct {
	Text            string        `json:"text"`
	HighlightedText []Text        `json:"highlighted_text"`
	BgColor         string        `json:"bg_color"`
	Styles          *TextStyle    `json:"styles"`
	CTA             *BottomBarCTA `json:"cta,omitempty"`
}

type BottomBarCTA struct {
	ImageURL          string                         `json:"image_url"`
	Nav               *shared.BackHandlerBottomSheet `json:"nav,omitempty"`
	MixpanelEventName string                         `json:"mixpanel_event_name,omitempty"`
}

type PaymentNotes struct {
	UserId string `json:"user_id"`
}

type WaysToPayText struct {
	IconName      string                         `json:"icon_name"`
	Nav           *shared.BackHandlerBottomSheet `json:"nav,omitempty"`
	Text          string                         `json:"text"`
	HighlightText []Text                         `json:"highlight_text"`
	Style         TextStyle                      `json:"styles"`
}

type Text struct {
	Content string    `json:"text"`
	Style   TextStyle `json:"highlight_style"`
}

type TextStyle struct {
	Color      string `json:"color"`
	FontWeight string `json:"fontWeight"`
}

type CreateInvoiceRequest struct {
	OrderID string `json:"order_id"`
}

type GetInvoiceRequest struct {
	OrderIDs []string `json:"order_ids"`
}

type InvoiceResponse struct {
	OrderID    string `json:"order_id"`
	InvoiceUrl string `json:"invoice_url"`
}

type CreateInvoiceResponse struct {
	Data InvoiceResponse `json:"data"`
}

type GetInvoiceResponse struct {
	Data []InvoiceResponse `json:"data"`
}

type BillDifferenceRequest struct {
	Data   BillDifferenceData `json:"data"`
	UserID string             `json:"user_id"`
	Email  string             `json:"email"`
	Meta   Meta               `json:"meta"`
}

type BillDifferenceData struct {
	OrderID           string  `json:"order_id"`
	RequestedCashback float64 `json:"requested_cashback"`
	GetBillDetailsData
}

type ActivationCohortUserData struct {
	CouponId string `json:"coupon_id"`
	Seller   string `json:"seller"`
	Eligible bool   `json:"eligible"`
	Value    string `json:"value"`
}

type ProcessBackendDiscountsData struct {
	Seller                   string
	UserID                   string
	CouponID                 string
	ActivationCohortUserData *ActivationCohortUserData
	TotalProductPricing      float64
	AppVersion               string
}
