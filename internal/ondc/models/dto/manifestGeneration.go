package dto

// Request DTOs
type GenerateManifestRequest struct {
	Data GenerateManifestRequestData `json:"data"`
}

type GenerateManifestRequestData struct {
	OrderIDs []int64 `json:"order_ids"`
}

type ManifestRequestObject struct {
	Data []ManifestRequestData `json:"data"`
}

type ManifestRequestData struct {
	CourierName    string                 `json:"courier_name"`
	ManifestID     string                 `json:"manifest_id"`
	ManifestDate   string                 `json:"manifest_date"`
	TotalShipments int                    `json:"total_shipments"`
	Shipments      []ManifestShipmentData `json:"shipments"`
}

type ManifestShipmentData struct {
	AWBNumber    string `json:"awb_number"`
	Store        string `json:"store"`
	OrderNumber  string `json:"order_number"`
	PaymentMode  string `json:"payment_mode"`
	SKUCode      string `json:"sku_code"`
	CustomerName string `json:"customer_name"`
}

// Response DTOs
type GenerateManifestResponse struct {
	Result GenerateManifestResponseResult `json:"result"`
}

type GenerateManifestResponseResult struct {
	Success   bool                   `json:"success"`
	Message   string                 `json:"message"`
	Manifests []ManifestResponseData `json:"manifests"`
	URL       string                 `json:"url"`
}
type ManifestResponseData struct {
	ManifestID     string `json:"manifest_id"`
	CourierName    string `json:"courier_name"`
	TotalShipments int    `json:"total_shipments"`
	Pages          int    `json:"pages"`
	ManifestURL    string `json:"manifest_url,omitempty"`
	Status         string `json:"status"`
	GeneratedAt    string `json:"generated_at"`
}
