package dto

import "encoding/json"

type AppCancelRequest struct {
	BppID                string  `json:"bpp_id"`
	BppURL               string  `json:"bpp_url"`
	ProviderID           string  `json:"provider_id"`
	Status               string  `json:"status"`
	OrderID              *string `json:"order_id,omitempty"`
	CancellationReasonID *string `json:"cancellation_reason_id,omitempty"`
	Meta                 Meta    `json:"meta"`
}

type AppCancelResponse struct {
	ID           string           `json:"id"`
	OrderState   string           `json:"order_state"`
	Cancellation Cancellation     `json:"cancellation"`
	Meta         Meta             `json:"meta"`
	Error        AppResponseError `json:"error,omitempty"`
}

func (r AppCancelResponse) MarshalBinary() (data []byte, err error) {
	bytes, err := json.Marshal(r)
	return bytes, err
}
