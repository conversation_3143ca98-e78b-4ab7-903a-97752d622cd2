package dto

import "encoding/json"

type AppStatusReq struct {
	Meta   Meta          `json:"meta"`
	Data   AppStatusData `json:"data"`
	UserID string        `json:"user_id"`
}

type AppStatusData struct {
	OrderID      string `json:"order_id"`
	OrderStatus  string `json:"order_status"`
	OrderMessage string `json:"order_message"`
	Invoice      string `json:"invoice_url"`
}

type AppStatusResp struct {
	Data  AppStatusData    `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

func (r AppStatusResp) MarshalBinary() (data []byte, err error) {
	bytes, err := json.Marshal(r)
	return bytes, err
}

type OrderStatusRequest struct {
	OrderID int64 `json:"order_id"`
}

type MapOrderStatusRequest struct {
	OrderStatus string `json:"order_status"`
}
