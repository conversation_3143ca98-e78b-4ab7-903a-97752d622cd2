package dto

import "kc/internal/ondc/models/shared"

type GetSuggestedProductsRequest struct {
	Data   GetSuggestedProductsData `json:"data"`
	UserID string                   `json:"user_id"`
	Meta   Meta                     `json:"meta"`
}

type GetSuggestedProductsData struct {
	Seller           string   `json:"seller"`
	Source           string   `json:"source"`
	AddressID        string   `json:"address_id"`
	SubType          string   `json:"sub_type"`
	Exclusive        bool     `json:"exclusive,omitempty"`
	ExcludedProducts []string `json:"excluded_products,omitempty"`
}

type GetSuggestedProductsResponse struct {
	Meta  Meta                  `json:"meta"`
	Data  SuggestedProductsData `json:"data"`
	Error AppResponseError      `json:"error,omitempty"`
}

type SuggestedProductsData struct {
	SuggestedProducts []shared.SellerItems `json:"suggested_products"`
	Widgets           interface{}          `json:"widgets,omitempty"` // Optional field for widgets
}
