package dto

type ConvertUrlsToZipRequest struct {
	Data ConvertUrlsToZipData `json:"data"`
}

type ConvertUrlsToZipData struct {
	Urls []string `json:"urls"`
}

// ConvertUrlsToZipResponse represents the response structure from the ZIP conversion API
type ConvertUrlsToZipResponse struct {
	Result ConvertUrlsToZipResult `json:"result"`
}

type ConvertUrlsToZipResult struct {
	Success     bool    `json:"success"`
	DownloadUrl string  `json:"downloadUrl"`
	FileCount   int     `json:"fileCount"`
	TotalSizeMB float64 `json:"totalSizeMB"`
	ZipSizeMB   float64 `json:"zipSizeMB"`
}

// ConvertUrlsToZipRequest represents the request structure for the ZIP conversion API
type ConvertUrlsToZipExternalRequest struct {
	Data ConvertUrlsToZipExternalData `json:"data"`
}

type ConvertUrlsToZipExternalData struct {
	Urls []string `json:"urls"`
}
