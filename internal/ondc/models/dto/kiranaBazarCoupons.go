package dto

type CheckCouponData struct {
	CouponId    int     `json:"coupon_id"`
	CouponCode  string  `json:"coupon_code"`
	Seller      string  `json:"seller"`
	Source      string  `json:"source"`
	TotalAmount float64 `json:"total_amount"`
}

type CheckCouponRequest struct {
	UserID string          `json:"user_id"`
	Data   CheckCouponData `json:"data"`
	Meta   Meta            `json:"meta"`
}

type GetAllCouponsResponseData struct {
	Coupons       interface{} `json:"coupons"`
	AllCouponsMap interface{} `json:"all_coupons_map"`
}

type GelAllCouponsResponse struct {
	Data GetAllCouponsResponseData `json:"data"`
}

type CheckCouponResponse struct {
	Meta  Meta             `json:"meta"`
	Data  GetCouponsData   `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type CheckCouponResponseV2 struct {
	Meta  Meta             `json:"meta"`
	Data  GetCouponsDataV2 `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type GetCouponsRequest struct {
	UserID string        `json:"user_id"`
	Data   GetCouponData `json:"data"`
	Meta   Meta          `json:"meta"`
}

type GetApplicableCouponsRequest struct {
	UserID string             `json:"user_id"`
	Data   GetBillDetailsData `json:"data"`
	Meta   Meta               `json:"meta"`
	Email  string             `json:"email,omitempty"` // Optional field for email
}

type GetCouponData struct {
	CouponID int    `json:"coupon_id"`
	Seller   string `json:"seller"`
}

type GetCouponsResponse struct {
	Meta  Meta             `json:"meta"`
	Data  GetCouponsData   `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type GetCouponsData struct {
	Coupons   []Coupon `json:"coupons"`
	Count     int      `json:"count"`
	ErrorText string   `json:"error_text"`
}

type GetCouponsDataV2 struct {
	Coupons   []ValidCoupon `json:"coupons"`
	Count     int           `json:"count"`
	ErrorText string        `json:"error_text"`
}

type ValidCoupon struct {
	Code                   string                `json:"code"`
	Name                   string                `json:"name"`
	Description            string                `json:"description"`
	Discount               float64               `json:"discount"`
	HighlightedDescription []HighlightedTextData `json:"highlighted_description"`
}

type Coupon struct {
	ID                 int              `json:"id"`
	Code               string           `json:"code"`
	Name               string           `json:"name"`
	Description        string           `json:"description"`
	Discount           string           `json:"discount"`
	PercentageDiscount float64          `json:"percentage_discount"`
	ValidFrom          string           `json:"valid_from"`
	ValidTill          string           `json:"valid_till"`
	Valid              bool             `json:"valid"`
	Terms              []string         `json:"terms"`
	MinimumAmount      float64          `json:"minimum_amount"`
	MaximumDiscount    float64          `json:"maximum_discount"`
	IsInternal         bool             `json:"is_internal"`
	Type               string           `json:"type"`
	Freebie            Freebie          `json:"freebie"`
	IsActive           bool             `json:"is_active"`
	Info               string           `json:"info"`
	Title              string           `json:"title"`
	Color              string           `json:"color"`
	CouponURL          string           `json:"coupon_url"`
	BulletPoints       []string         `json:"bullet_points"`
	OfferImageURL      string           `json:"offer_image_url"`
	SideStrip          *CouponSideStrip `json:"side_strip,omitempty"` // Optional field for side strip
}

type Freebie struct {
	ProductID string  `json:"product_id"`
	Quantity  int     `json:"quantity"`
	Value     float64 `json:"value"`
}

type CouponSideStrip struct {
	Label string `json:"label"`
	Color string `json:"color"`
}
