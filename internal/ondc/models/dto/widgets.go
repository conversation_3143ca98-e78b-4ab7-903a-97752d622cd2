package dto

import "fmt"

type Widget interface{}
type GetScreenDetailsRequest struct {
	Screen          string  `json:"screen"`
	Uid             string  `json:"uid"`
	State           string  `json:"state"`
	District        string  `json:"district"`
	GeoCodedCluster string  `json:"geo_coded_cluster"`
	WidgetIndexes   []int64 `json:"widget_indexes"`
	AppVersion      string  `json:"appVersion"`
}

type GetScreenDetailsResponse struct {
	Result []Widget `json:"result"`
}

type ProgressWidgetImageUrlResponse struct {
	WidgetUrl *string `json:"widget_url,omitempty"`
}

type ScreenHeaderConfig struct {
	DynamicConfigs []Header `json:"dynamic_configs"`
}

type ScreenConfig struct {
	HeaderConfig ScreenHeaderConfig `json:"header_config"`
	Widgets      []int64            `json:"widgets"`
	Screen       string             `json:"screen"`
	UpdatedBy    string             `json:"updatedBy"`
}

// WidgetsResult wraps the result of widget fetching with error handling
type WidgetsResult struct {
    Widgets []Widget
    Error   error
}

// WidgetsRequest contains the parameters for fetching widgets
type WidgetsRequest struct {
    ScreenName string
    UserID     string
    AppVersion string
}

// Validate validates the widgets request
func (wr *WidgetsRequest) Validate() error {
    if wr.ScreenName == "" {
        return fmt.Errorf("screen_name is required")
    }
    if wr.UserID == "" {
        return fmt.Errorf("user_id is required")
    }
    if wr.AppVersion == "" {
        return fmt.Errorf("app_version is required")
    }
    return nil
}
