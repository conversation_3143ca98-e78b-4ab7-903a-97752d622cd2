package dto

type CartProductsRequest struct {
	Data struct {
		UserID string  `json:"user_id" binding:"required"`
		Brand  *string `json:"brand,omitempty"`
	} `json:"data" binding:"required"`
}

type CartProduct struct {
	ID              string  `json:"id"`
	Name            string  `json:"name"`
	MRPNumber       float64 `json:"mrp_number"`
	MarkupMargin    float64 `json:"markup_margin"`
	Quantity        string  `json:"quantity"`
	WholesaleRate   float64 `json:"wholesale_rate"`
	ImageURL        string  `json:"imageUrl"`
	PopularityValue float64 `json:"popularity_value"`
	ProductType     *string `json:"product_type,omitempty"` // e.g., "posm", "virtual", "product"
	Seller          string  `json:"seller"`
}

type CartProductsResponse struct {
	Products []CartProduct `json:"products"`
}
