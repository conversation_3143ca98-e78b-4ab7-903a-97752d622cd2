package dto

import (
	"kc/internal/ondc/models/shared"
	"strings"
	"time"
)

type PaidAmountProof struct {
	Url       string `json:"url"`
	UpdatedAt string `json:"updated_at"`
	UpdatedBy string `json:"updated_by"`
}

type OrderMeta struct {
	CallStatus              string            `json:"call_status"`
	Note                    string            `json:"note"`
	CancelReason            string            `json:"cancel_reason"`
	ReturnedReason          string            `json:"returned_reason"`
	AdvanceTaken            *bool             `json:"advance_taken"`
	PaidAmount              *float64          `json:"paid_amount"`
	PaidAmountProof         []PaidAmountProof `json:"paid_amount_proof"`
	DeliveredTimestamp      *int64            `json:"delivered_timestamp"`
	OutForDeliveryTimestamp *int64            `json:"out_for_delivery_timestamp"`
	Courier                 string            `json:"courier"`
	AWBNumber               string            `json:"awb_number"`
	ShipmentCreatedAt       int64             `json:"shipment_created_at"`
	NDRAttempCount          int               `json:"ndr_attempt_count"`
	PaymentId               string            `json:"payment_id"`
	PaymentDiscount         float64           `json:"payment_discount"`
	ReturnReceivedNote      string            `json:"return_received_note"`
	RNN                     *string           `json:"rnn"`
	InvoiceNumber           string            `json:"invoice_number"`
}

type UpdateB2BOrderStatusData struct {
	OrderID         string    `json:"order_id"`
	UpdatedBy       string    `json:"updated_by"`
	OrderStatus     string    `json:"order_status"`
	OrderStatusType string    `json:"order_status_type"`
	UpdatedAt       int64     `json:"updated_at"`
	OrderingModule  string    `json:"ordering_module"`
	Source          string    `json:"source"`
	OrderMeta       OrderMeta `json:"order_meta"`
}

type UpdateB2BOrderStatusRequest struct {
	UpdatedBy string                   `json:"updated_by"`
	Data      UpdateB2BOrderStatusData `json:"data"`
}

type UpdateB2BOrderStatusResponse struct {
	Meta    Meta   `json:"meta"`
	Error   Error  `json:"error,omitempty"`
	Message string `json:"message"`
}

type UpdateOrderReasonsData struct {
	OrderID            string `json:"order_id"`
	OrderStatus        string `json:"order_status"`
	UpdatedBy          string `json:"updated_by"`
	UpdatedAt          *int64 `json:"updated_at"`
	CallStatus         string `json:"call_status"`
	Note               string `json:"note"`
	CancelReason       string `json:"cancel_reason"`
	ReturnedReason     string `json:"returned_reason"`
	ReturnReceivedNote string `json:"return_received_note"`
}

type UpdateOrderReasonsRequest struct {
	UpdatedBy string                 `json:"updated_by"`
	Data      UpdateOrderReasonsData `json:"data"`
}

type UpdateOrderReasonsResponse struct {
	Meta    Meta   `json:"meta"`
	Error   Error  `json:"error,omitempty"`
	Message string `json:"message"`
}

type UpdateOrderAddressData struct {
	OrderID        string `json:"order_id"`
	OrderStatus    string `json:"order_status"`
	AddressId      uint64 `json:"address_id"`
	UpdatedBy      string `json:"updated_by"`
	UpdateAddress  bool   `json:"update_address"`
	Name           string `json:"name"`
	City           string `json:"city"`
	UserId         string `json:"user_id"`
	Phone          string `json:"phone"`
	AlternatePhone string `json:"alternate_phone"`
	Neighbourhood  string `json:"neighbourhood"`
	Village        string `json:"village"`
	HouseNumber    string `json:"house_number"`
	Landmark       string `json:"landmark"`
	StoreName      string `json:"store_name"`
	Tag            string `json:"tag"`
	State          string `json:"state"`
	PostalCode     string `json:"postal_code"`
	OrderingModule string `json:"ordering_module"`
	Line           string `json:"line"`
	GST            string `json:"gst"`
}

type UpdateOrderAddressRequest struct {
	UpdatedBy string                 `json:"updated_by"`
	Data      UpdateOrderAddressData `json:"data"`
}

type GetPendingShipmentsRequest struct {
	Data struct {
		Seller string `json:"seller"`
	} `json:"data"`
}

type GetPendingShipmentsResponse struct {
	Date             string  `json:"date"`
	PendingCount     int     `json:"pending_count"`
	ProcessingCount  int     `json:"processing_count"`
	TotalCount       int     `json:"total_count"`
	TotalOrdersValue float64 `json:"total_orders_value"`
}

type ExportOrderRequest struct {
	Data struct {
		Seller string `json:"seller"`
		Date   string `json:"date"`
	} `json:"data"`
}

type DailyOrderCounts struct {
	Date            string `json:"date"`
	PendingCount    int    `json:"pending_count"`
	ProcessingCount int    `json:"processing_count"`
	TotalCount      int    `json:"total_count"`
}

type GetBrandInsightsRequest struct {
	Data struct {
		Seller    []string `json:"seller"`
		TimeRange string   `json:"time_range"`
	} `json:"data"`
}

// carrier data
type Carrier struct {
	ID          string `json:"id"`
	DisplayName string `json:"display_name"`
	CourierName string `json:"courier_name"`
	Image       string `json:"image"`
}

// response for fetching all the carriers
type GetCarrierResponse struct {
	Carriers []Carrier `json:"carriers"`
}

type ArchiveOrderRequest struct {
	Data struct {
		OrderID    string `json:"order_id"`
		UpdatedBy  string `json:"updated_by"`
		UpdatedAt  int64  `json:"updated_at"`
		CallStatus string `json:"call_status"`
		Note       string `json:"note"`
	} `json:"data"`
}

type ArchiveOrderResponse struct {
	Message string `json:"message"`
}

type B2BProductsRequestData struct {
	Filters Filters   `json:"filters"`
	Meta    Meta      `json:"meta"`
	OrderBy []OrderBy `json:"order_by"`
}
type GetB2BProductsRequest struct {
	Data   B2BProductsRequestData `json:"data"`
	Source string                 `json:"source"`
}

type ExportInventoryProductsRequest struct {
	Data   B2BProductsRequestData `json:"data"`
	Source string                 `json:"source"`
}

type ExportInventoryProductsData struct {
	ID                int64   `json:"id"`
	Code              string  `json:"code"`
	Name              string  `json:"name"`
	Quantity          string  `json:"quantity"`
	CaseSize          int     `json:"case_size"`
	PackSize          int     `json:"pack_size"`
	WholesaleRate     float64 `json:"wholesale_rate"`
	MrpNumber         float64 `json:"mrp_number"`
	OosStatus         bool    `json:"oos_status"`
	InventoryQuantity int64   `json:"inventory_quantity"`
	QuantitySold30d   int64   `json:"quantity_sold30d"`
	QuantitySold7d    int64   `json:"quantity_sold7d"`
	ThroughputPerDay  float64 `json:"throughput_per_day"`
}

type Filters struct {
	Sellers       []string `json:"sellers"`
	CategoryIds   []string `json:"category_ids"`
	ProductIds    []string `json:"product_ids"`
	IsActive      *bool    `json:"is_active"` // no default value
	Oos           *bool    `json:"oos"`       // no default value
	CodeLabels    []string `json:"code_labels"`
	CategoryCodes []string `json:"category_codes"`
	CategoryNames []string `json:"category_names"`
	ShowMerged    *bool    `json:"show_merged"` // no default value
}

type OrderBy struct {
	Key   string `json:"key"`   // column
	Value string `json:"value"` // asc/ desc
}
type B2BCategoriesRequestData struct {
	Filters Filters   `json:"filters"`
	Meta    Meta      `json:"meta"`
	OrderBy []OrderBy `json:"order_by"`
}
type GetB2BCategoriesRequest struct {
	Data B2BCategoriesRequestData `json:"data"`
}

type GetOrdersPaymentDetailsRequest struct {
	Data struct {
		Seller string `json:"seller"`
	} `json:"data"`
}

type GetOrdersPaymentDetailsResponseData struct {
	TotalOrders     int     `json:"total_orders"`
	TotalPaidAmount float64 `json:"total_paid_amount"`
	Year            int     `json:"year"`
	Month           int     `json:"month"`
}

type GetOrdersPaymentDetailsResponse struct {
	Data []GetOrdersPaymentDetailsResponseData `json:"data"`
}

type UpdateProductDetailsRequest struct {
	UpdatedBy     string                   `json:"updated_by"`
	RequestSource string                   `json:"request_source"`
	Data          UpdateProductDetailsData `json:"data"`
}

type UpdateMultipleProductDetailsRequest struct {
	UpdatedBy     string                     `json:"updated_by"`
	RequestSource string                     `json:"request_source"`
	Data          []UpdateProductDetailsData `json:"data"`
}

// ProductUpdateData contains the product ID and list of updates
type UpdateProductDetailsData struct {
	UpdatedBy string               `json:"updated_by"`
	Seller    string               `json:"seller"`
	ProductID int                  `json:"productID"`
	Updates   []UpdateProductField `json:"updates"`
}

// UpdateField represents a single field update with key-value pair
type UpdateProductField struct {
	Key   string      `json:"key"`
	Value interface{} `json:"value"`
}

type UpdateProductDetailsResponse struct {
	Message string                 `json:"message"`
	Product *shared.SellerItemsB2B `json:"product_data"`
}

// UpdatePrimaryWebhookRequest is the request schema for updating the primary waybill
type UpdatePrimaryWaybillRequest struct {
	AWBNumber string `json:"awb_number"`
	UpdatedBy string `json:"updated_by"`
	Comment   string `json:"comment"`
}

type FetchAllWaybillsRequest struct {
	OrderID string `json:"order_id"`
}

type AWB struct {
	AWBNumber    string `json:"awb_number"`
	ReferenceID  string `json:"reference_id"`
	Status       string `json:"status"`
	IsPrimary    bool   `json:"is_primary"`
	NSLCode      string `json:"nsl_code"`
	Instructions string `json:"instructions"`
	Courier      string `json:"courier"`
}
type FetchAllWaybillsResponse struct {
	AWBs []AWB `json:"awbs"`
}

type GetWaybillDetailsRequest struct {
	AWBNumber string `json:"awb_number"`
}

type GetB2BMetaRequest struct {
	Data struct {
		Seller string `json:"seller"`
	} `json:"data"`
}

type UpdateReturnReceivedRequest struct {
	Data struct {
		OrderID       string `json:"order_id"`
		Seller        string `json:"seller"`
		UpdatedBy     string `json:"updated_by"`
		UpdatedAt     int64  `json:"updated_at"`
		Note          string `json:"note"`
		RequestSource string `json:"request_source"`
	} `json:"data"`
}

type BrandData struct {
	Id        int    `json:"id"`
	Seller    string `json:"seller"`
	Source    string `json:"source"`
	Label     string `json:"label"`
	BgColor   string `json:"bg_color"`
	TextColor string `json:"text_color"`
	Url       string `json:"url"`
}

type GetB2BMetaResponse struct {
	Data struct {
		Brands              []BrandData              `json:"brands"`
		CancellationReasons []map[string]interface{} `json:"cancellation_reasons"`
	} `json:"data"`
}

type GetPickListPDFRequest struct {
	Data   GetPickListPDFData `json:"data"`
	UserID string             `json:"user_id"`
	Meta   Meta               `json:"meta"`
}

type CreatePickListForOrderRequest struct {
	Data struct {
		OrderID string `json:"order_id"`
	}
}

type PushOrderToOMSRequest struct {
	Data struct {
		Seller    string    `json:"seller"`
		OrderID   string    `json:"order_id"`
		Package   []Package `json:"package"`
		UpdatedBy string    `json:"updated_by"`
		Force     bool      `json:"force"`
		Reason    string    `json:"reason"`
	}
}
type Package struct {
	Length float64 `json:"length"` // in cm
	Width  float64 `json:"width"`  // in cm
	Height float64 `json:"height"` // in cm
	Weight float64 `json:"weight"` // in grams
}

type PushOrderToOMSResponse struct {
	Success       bool   `json:"success"`
	Message       string `json:"message"`
	OrderID       string `json:"order_id"`
	Invoice       string `json:"invoice"`
	PrintingLabel string `json:"printing_label"`
	OMS           string `json:"oms"`
	Courier       string `json:"courier"`
	AWBNumber     string `json:"awb_number"`
}

type GetPickListPDFData struct {
	Seller string `json:"seller"`
	Date   string `json:"date"`
}

type GetPickListResponse struct {
	PDFUrl string `json:"pdf_url"`
}

type PickListGenerationRequest struct {
	Data []PickListGenerationRequestData `json:"data"`
}

type PickListGenerationRequestData struct {
	OrderID     string                           `json:"order_id"`
	TotalAmount float64                          `json:"total_amount"`
	Quantity    int                              `json:"quantity"`
	Items       []PickListGenerationRequestItems `json:"items"`
}

type PickListGenerationRequestItems struct {
	ItemID        string  `json:"item_id"`
	ImageUrl      string  `json:"image_url"`
	ItemName      string  `json:"item_name"`
	ItemHindiName string  `json:"item_hindi_name"`
	Description   string  `json:"description"`
	PackSize      int     `json:"pack_size"`
	UnitPrice     float64 `json:"unit_price"`
	SKUCode       string  `json:"sku_code,omitempty"`
	Quantity      int     `json:"quantity"`
	Size          string  `json:"size"`
}

type InvoiceData struct {
	IGST        bool         `json:"IGST"`
	Invoice     InvoiceInfo  `json:"invoice"`
	Seller      SellerInfo   `json:"seller"`
	BilledTo    PartyInfo    `json:"billed_to"`
	ShippedTo   ShippingInfo `json:"shipped_to"`
	ShippedFrom SellerInfo   `json:"shipped_from"`
	Items       []ItemInfo   `json:"items"`
	TaxSummary  TaxSummary   `json:"tax_summary"`
}

type InvoiceInfo struct {
	Number            string  `json:"number"`
	Date              string  `json:"date"`
	OrderNumber       string  `json:"order_number"`
	OrderDate         string  `json:"order_date"`
	PaymentMode       string  `json:"payment_mode"`
	CollectableAmount float64 `json:"collectable_amount"`
	Carrier           string  `json:"carrier"`
	AWBNumber         string  `json:"awb_number"`
	Portal            string  `json:"portal"`
	GeneratedAt       string  `json:"generated_at"`
}

type SellerInfo struct {
	Name             string `json:"name"`
	LegalName        string `json:"legal_name"`
	RegisteredOffice string `json:"registered_office"`
	Email            string `json:"email"`
	Phone            string `json:"phno"`
	CIN              string `json:"cin"`
	PAN              string `json:"pan"`
	GSTIN            string `json:"gstin"`
	State            string `json:"state"`
	StateCode        string `json:"state_code"`
}

type PartyInfo struct {
	Name          string `json:"name"`
	Address       string `json:"address"`
	State         string `json:"state"`
	StateCode     string `json:"state_code"`
	PlaceOfSupply string `json:"place_of_supply"`
	GSTIN         string `json:"gstin"`
}

type ShippingInfo struct {
	Name          string `json:"name"`
	LegalName     string `json:"legal_name"`
	Address       string `json:"address"`
	State         string `json:"state"`
	StateCode     string `json:"state_code"`
	PlaceOfSupply string `json:"place_of_supply"`
}

type ItemInfo struct {
	Description                     string  `json:"description"`
	SKU                             string  `json:"sku"`
	HSNCode                         string  `json:"hsn_code"`
	ItemPrice                       float64 `json:"item_price"`
	Quantity                        int     `json:"quantity"`
	TotalItemValue                  float64 `json:"total_item_value"`
	TotalItemValueWithoutTax        float64 `json:"total_item_value_without_tax"`
	ItemTotalDiscount               float64 `json:"item_discount"`                      // this is total discount on item including all discounts --> platform, seller, payment, markdown, cashback
	ItemCummulativePlatformDiscount float64 `json:"item_cummulative_platform_discount"` // this is total discount on item including all discounts except seller discount and payment cashback
	ItemPlatformDiscount            float64 `json:"item_platform_discount"`             // this is direct platform discount
	ItemSellerDiscount              float64 `json:"item_seller_discount"`               // this is direct seller discount
	ItemPaymentDiscount             float64 `json:"item_payment_discount"`              //  this is payment discount which is platform at the end
	ItemPlatformCashback            float64 `json:"item_platform_cashback"`             // this is cashback given by platform
	ItemMarkdownDiscount            float64 `json:"item_markdown_discount"`             // this is markdown discount
	TaxableValue                    float64 `json:"taxable_value"`
	TaxRate                         string  `json:"tax_rate"`
	TaxAmount                       float64 `json:"tax_amount"`
	CGST                            string  `json:"CGST"`
	SGST                            string  `json:"SGST"`
}

type TaxSummary struct {
	TotalItemTaxableValue            float64 `json:"total_item_taxable_value"`
	TotalItemTax                     float64 `json:"total_item_tax"`
	IGST                             float64 `json:"igst"`
	CGST                             float64 `json:"cgst"`
	SGST                             float64 `json:"sgst"`
	InvoiceValue                     float64 `json:"invoice_value"`
	TotalDiscount                    float64 `json:"total_discount"`
	TotalPlatformCummulativeDiscount float64 `json:"total_platform_cummulative_discount"`
	AmountInWords                    string  `json:"amount_in_words"`
	TotalValue                       float64 `json:"total_value"`
	PayableAmount                    float64 `json:"payable_amount"`
	OrderValue                       float64 `json:"order_value"`

	//
	CashbackUsed float64 `json:"cashback_used"`
	AdvancePaid  float64 `json:"advance_paid"`
	PaymentDue   float64 `json:"payment_due"`
}

// Top-level struct for the entire JSON
type InvoiceRequestObject struct {
	Data InvoiceData `json:"data"`
}

type CreateExtInvoiceRequest struct {
	OrderID string `json:"order_id"`
	Courier string `json:"courier"`
	AWB     string `json:"awb"`
}

type CreateExtInvoiceResponse struct {
	InvoiceURL    string  `json:"invoice_url"`
	InvoiceID     string  `json:"invoice_id"`
	InvoiceAmount float64 `json:"invoice_amount"`
	CGST          float64 `json:"cgst"`
	SGST          float64 `json:"sgst"`
	IGST          float64 `json:"igst"`
	BuyerGST      string  `json:"buyer_gst"`
	NoOfSkus      int     `json:"no_of_skus"`
	ItemQuantity  int     `json:"item_quantity"`
	Discount      float64 `json:"discount"`
}

type CancelOrderB2BRequestData struct {
	OrderID           string `json:"order_id"`
	Note              string `json:"note"`
	CancelReason      string `json:"cancel_reason"`
	ResolveTicket     bool   `json:"resolve_ticket"`
	ResolveTicketNote string `json:"resolve_ticket_note"`
}

type CancelOrderB2BRequest struct {
	Data              CancelOrderB2BRequestData `json:"data"`
	UpdatedBy         string                    `json:"updated_by"`
	RequestSource     string                    `json:"request_source"`
	RequestSourcePage string                    `json:"request_source_page"`
}

type AddProductRequest struct {
	UpdatedBy     string                `json:"updated_by"`
	RequestSource string                `json:"request_source"`
	Data          AddProductRequestData `json:"data"`
}
type AddProductRequestData struct {
	CategoryID         int64           `json:"category_id" validate:"required"`
	Name               string          `json:"name" validate:"required,min=3,max=100"`
	HindiName          string          `json:"hindi_name" validate:"required,min=3,max=100"`
	Code               string          `json:"code" validate:"required"`
	Description        string          `json:"description" validate:"omitempty,max=500"`
	Quantity           string          `json:"quantity" validate:"required"`
	PackSize           int             `json:"pack_size" validate:"required,min=1"`
	CaseSize           int             `json:"case_size" validate:"required,min=1"`
	MRPNumber          float64         `json:"mrp_number" validate:"required,gt=0"`
	WholesaleRate      float64         `json:"wholesale_rate" validate:"required,gt=0"`
	BrandWholesaleRate *float64        `json:"brand_wholesale_rate" validate:"omitempty,gt=0"`
	Tax                *float64        `json:"tax" validate:"omitempty,min=0,max=100"`
	HSNCode            *string         `json:"hsn_code" validate:"omitempty"`
	ExpiresAt          *int64          `json:"expires_at" validate:"required,min=0"`
	ImageUrls          []string        `json:"image_urls" validate:"omitempty,dive,url"`
	IsOos              *bool           `json:"is_oos" validate:"omitempty"`
	IsDefault          *bool           `json:"is_default" validate:"omitempty"`
	SizeVariantCode    *int64          `json:"size_variant_code" validate:"omitempty"`
	BadgeText          *string         `json:"badge_text" validate:"omitempty,max=20"`
	TopBadgeStyles     *TopBadgeStyles `json:"top_badge_styles,omitempty"` // Optional field for badge styles
	ProductScreenTag   *string         `json:"product_screen_tag" validate:"omitempty,max=60"`
	ProductType        *string         `json:"product_type" validate:"omitempty,max=60"`
}

type TopBadgeStyles struct {
	Color   *string  `json:"color,omitempty"`    // Optional field for badge color
	BgColor []string `json:"bg_color,omitempty"` // Optional field for badge background color
}

// Updated DTOs
type GetOrderPaymentDetailsRequest struct {
	Data struct {
		OrderID string `json:"order_id" binding:"required"`
		Source  string `json:"source"`
		Screen  string `json:"screen"`
	} `json:"data"`
}

type RefundDetails struct {
	RefundMethod   string `json:"refund_method"`
	SourceRefundID string `json:"source_refund_id"`
	Amount         string `json:"amount"`
	Reason         string `json:"reason,omitempty"`
	RefundedAt     string `json:"refunded_at"`
	RefundVpa      string `json:"refund_vpa,omitempty"`
}

type GetOrderPaymentDetailsResponse struct {
	Source            string          `json:"source"`
	Status            string          `json:"status"`
	PayableAmount     string          `json:"payable_amount"`
	AdvancePayment    string          `json:"advance_payment"`
	FinalCOD          string          `json:"final_cod"`
	TransactionAt     string          `json:"transaction_at"`
	TransactionID     string          `json:"transaction_id,omitempty"`
	Refunded          bool            `json:"refunded"`
	TotalRefundAmount string          `json:"total_refund_amount,omitempty"`
	Refunds           []RefundDetails `json:"refunds,omitempty"`
}

type GetPaymentDetailsPaymentQueryResult struct {
	OrderID        int64      `gorm:"column:order_id"`
	PayableAmount  float64    `gorm:"column:payable_amount"`
	PaidAmount     *float64   `gorm:"column:paid_amount"`
	PaymentID      *string    `gorm:"column:payment_id"`
	RNN            *string    `gorm:"column:rnn"`
	KopSource      *string    `gorm:"column:kop_source"`
	KopUpdatedAt   time.Time  `gorm:"column:kop_updated_at"`
	GatewayStatus  *string    `gorm:"column:gateway_status"`
	AdvancePayment *float64   `gorm:"column:advance_payment"`
	KpgoCreatedAt  *time.Time `gorm:"column:kpgo_created_at"`
	Gateway        *string    `gorm:"column:gateway"`
}

type GetPaymentDetailsRefundQueryResult struct {
	RefundMethod   string    `gorm:"column:refund_method"`
	SourceRefundID *string   `gorm:"column:source_refund_id"`
	Amount         float64   `gorm:"column:amount"`
	Reason         *string   `gorm:"column:reason"`
	CreatedAt      time.Time `gorm:"column:created_at"`
	RefundVpa      string    `gorm:"column:refund_vpa"`
}

type MultiError struct {
	Errors []error
}

func (m *MultiError) Error() string {
	var b strings.Builder
	b.WriteString("Multiple errors occurred:\n")
	for _, err := range m.Errors {
		b.WriteString("- " + err.Error() + "\n")
	}
	return b.String()
}
