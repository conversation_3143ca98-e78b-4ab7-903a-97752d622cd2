package dto

type SellerLevelOrderStats struct {
	UserId string `json:"user_id"`
}

type GetUserPlacedOrderCountResponse struct {
	OrdersCount map[string]int `json:"order_count"`
	ShowWidget  bool           `json:"show_widget"`
}

type ResolveWidgetAccordingToOrderCountResponse struct {
	Result map[string]interface{} `json:"result,omitempty"`
}

type SellerWiseConfirmedOrderCount struct {
	Orders []SellerWiseConfirmedOrderCountInfo `json:"orders"`
}

type SellerWiseConfirmedOrderCountInfo struct {
	Seller    string `json:"seller"`
	Confirmed int    `json:"confirmed"`
	Placed    int    `json:"placed"`
}
