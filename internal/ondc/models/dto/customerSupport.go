package dto

import (
	"kc/internal/ondc/models/dao"

	"gorm.io/datatypes"
)

type GetSupportTicketsRequest struct {
	Data GetSupportTicketsRequestData `json:"data"`
}

type GetSupportTicketsRequestData struct {
	Filters GetSupportTicketsRequestFilters `json:"filters"`
	From    int64                           `json:"from"`
	To      int64                           `json:"to"`
	Email   string                          `json:"email"`
	Limit   int                             `json:"limit"`
	Offset  int                             `json:"offset"`
	AgentID string                          `json:"agent_id"`
	Source  string                          `json:"source"`
}

type GetSupportTicketsRequestFilters struct {
	Status      []string `json:"status"`
	NotStatus   []string `json:"not_status"`
	Owner       []string `json:"owner"`
	Assignee    []string `json:"assignee"`
	Priority    []string `json:"priority"`
	Seller      []string `json:"seller"`
	Category    []int    `json:"category"`
	SearchKey   string   `json:"search_key"`
	OrderAmount struct {
		Min float64 `json:"min"`
		Max float64 `json:"max"`
	} `json:"order_amount"`
	UserID         string `json:"user_id"`
	SellerTatHours int    `json:"seller_tat_hours"` // Add this line
}

type GetSupportTicketsResponse struct {
	Tickets []SupportTickets `json:"tickets"`
	Stats   map[string]int64 `json:"stats"`
}

type SupportTickets struct {
	TicketID             string   `json:"ticket_id" gorm:"column:ticket_id"`
	OrderID              int      `json:"order_id" gorm:"column:order_id"`
	UserId               string   `json:"user_id" gorm:"column:user_id"`
	ConversationID       string   `json:"conversation_id" gorm:"column:conversation_id"`
	CreatedAt            int64    `json:"created_at" gorm:"column:created_at"`
	CustomerName         string   `json:"customer_name" gorm:"column:customer_name"`
	Category             string   `json:"category" gorm:"column:category"`
	UpdatedAt            int64    `json:"updated_at" gorm:"column:updated_at"`
	TotalAmount          float64  `json:"total_amount" gorm:"column:total_amount"`
	OrderStatus          string   `json:"order_status" gorm:"column:order_status"`
	Status               string   `json:"status" gorm:"column:status"`
	Seller               string   `json:"seller" gorm:"column:seller"`
	SellerCode           string   `json:"seller_code" gorm:"column:seller_code"`
	Priority             string   `json:"priority" gorm:"column:priority"`
	AssignedTo           string   `json:"assigned_to" gorm:"column:assigned_to"`
	CurrentAssignee      string   `json:"current_assignee" gorm:"column:current_assignee"`
	ProgressState        string   `json:"progress_state" gorm:"column:progress_state"`
	ProfileImage         string   `json:"customer_image" gorm:"column:customer_image"`
	BlockSellerAssignee  bool     `json:"block_seller_assignee" gorm:"column:block_seller_assignee"`
	ShowChallengeButton  bool     `json:"show_challenge_button" gorm:"column:show_challenge_button"`
	ShowApproveButton    bool     `json:"show_approve_button" gorm:"column:show_approve_button"`
	TicketActionID       string   `json:"ticket_action_id,omitempty" gorm:"column:ticket_action_id"`
	TicketActionStatus   string   `json:"ticket_action_status,omitempty" gorm:"column:ticket_action_status"`
	RequestedAction      string   `json:"requested_action,omitempty" gorm:"column:requested_action"`
	RequestedBy          string   `json:"requested_by,omitempty" gorm:"column:requested_by"`
	RequestedAt          string   `json:"requested_at,omitempty" gorm:"column:requested_at"`
	RequestedMessageID   string   `json:"requested_message_id,omitempty" gorm:"column:requested_message_id"`
	LastPublicSenderType string   `json:"last_public_sender_type,omitempty" gorm:"column:last_public_sender_type"`
	PendingReply         bool     `json:"pending_reply" gorm:"column:pending_reply"`
	TicketTags           []string `json:"ticket_tags" gorm:"column:ticket_tags"`
	OrderTags            []string `json:"order_tags" gorm:"column:order_tags"`
}

type AppGetCustomerSupportHome struct {
	UserID string                        `json:"user_id"`
	Data   AppGetCustomerSupportHomeData `json:"data"`
	Meta   Meta                          `json:"meta"`
}

type AppGetCustomerSupportHomeData struct {
	UserID string `json:"user_id"`
	Limit  int    `json:"limit"`
	Offset int    `json:"offset"`
}

type IssueDetailsAffectedItem struct {
	SkuID            string `json:"sku_id"`
	ProductName      string `json:"product_name"`
	Weight           string `json:"weight"`
	QuantityOrdered  int    `json:"quantity_ordered"`
	QuantityAffected int    `json:"quantity_affected"`
}

// MediaFile represents media files attached to an issue
type IssueDetailsMediaFile struct {
	FileType    string `json:"file_type"`
	FileURL     string `json:"file_url"`
	ContentType string `json:"content_type"`
	SizeBytes   int    `json:"sizeBytes"`
}

type IssueCategoryInfo struct {
	ID         string `json:"id"`
	NextScreen string `json:"nextScreen"`
	Text       string `json:"text"`
}

type QuestionInfo struct {
	Question string `json:"question"`
	Answer   string `json:"answer"`
}

type AppSubmitTicketIssueDetails struct {
	IssueCategory        IssueCategoryInfo          `json:"issue_category"`
	IssueSubCategory     IssueCategoryInfo          `json:"issue_sub_category"`
	AffectedItems        []IssueDetailsAffectedItem `json:"affected_items"`
	ItemMissingQuestions []QuestionInfo             `json:"item_missing_questions"`
	AdditionalQuestion   QuestionInfo               `json:"additional_question"`
	Description          string                     `json:"description"`
	Media                []IssueDetailsMediaFile    `json:"media"`
}

// IssueDTO is the main data transfer object for issue data
type AppSubmitTicketRequest struct {
	Data struct {
		CreatedAt    string                      `json:"created_at"`
		UserID       string                      `json:"userId"`
		OrderID      string                      `json:"order_id"`
		Seller       string                      `json:"seller"`
		FlowID       string                      `json:"flow_id"`
		IssueDetails AppSubmitTicketIssueDetails `json:"issue_details"`
	} `json:"data"`
	Meta Meta `json:"meta"`
}

type GetTicketMessagesRequest struct {
	Data GetTicketMessagesRequestData `json:"data"`
	Meta Meta                         `json:"meta"`
}

type GetTicketMessagesRequestData struct {
	TicketID        string `json:"ticket_id"`
	ConversationID  string `json:"conversation_id"`
	Limit           int    `json:"limit"`
	AnchorMessageID string `json:"anchor_message_id"` // Message to anchor pagination around
	AnchorTimestamp int64  `json:"anchor_timestamp"`  // Timestamp of the anchor message (to avoid an extra DB call)
	Direction       string `json:"direction"`         // BEFORE, AFTER, or AROUND
	UserID          string `json:"user_id"`           // For tracking read receipts
	UserType        string `json:"user_type"`         // USER or AGENT to filter private messages
	MarkAsRead      bool   `json:"mark_as_read"`      // Whether to mark messages as read
	Source          string `json:"source"`            // Source of the request (e.g., "app", "web", etc.)
	AgentEmail      string `json:"agent_email"`       // Agent ID for the request
}

type GetTicketMessagesResponse struct {
	Messages   []TicketMessage        `json:"messages"`
	Pagination WhatsAppPaginationInfo `json:"pagination"`
}

type WhatsAppPaginationInfo struct {
	HasOlderMessages bool   `json:"has_older_messages"` // True if there are older messages to fetch
	HasNewerMessages bool   `json:"has_newer_messages"` // True if there are newer messages to fetch
	FirstMessageID   string `json:"first_message_id"`   // ID of the first (newest) message in the current batch
	LastMessageID    string `json:"last_message_id"`    // ID of the last (oldest) message in the current batch
	Count            int    `json:"count"`              // Number of messages in the current batch
}

type TicketMessage struct {
	ID             string              `json:"id"`
	ConversationID string              `json:"conversation_id"`
	Content        *string             `json:"content"`
	Sender         MessageSender       `json:"sender"`
	Private        bool                `json:"private"`
	MessageType    *string             `json:"message_type"`
	CreatedAt      int64               `json:"created_at"`
	UpdatedAt      int64               `json:"updated_at"`
	Visible        *bool               `json:"visible"`
	Attachments    []MessageAttachment `json:"attachments"`
	DeliveryStatus string              `json:"delivery_status"` // SENT, DELIVERED, READ
	PendingReply   bool                `json:"pending_reply"`
}

type MessageSender struct {
	ID        string  `json:"id"`
	Type      string  `json:"type"`
	Name      string  `json:"name,omitempty"`
	Role      string  `json:"role,omitempty"`
	AvatarURL *string `json:"avatar_url,omitempty"`
}

type MessageAttachment struct {
	ID               int64           `json:"id"`
	MessageID        int64           `json:"message_id"`
	FileType         string          `json:"file_type"`
	FileURL          string          `json:"file_url"`
	FileSize         int             `json:"file_size"`
	CreatedAt        int64           `json:"created_at"`
	UpdatedAt        int64           `json:"updated_at"`
	FileThumbnailURL *string         `json:"file_thumbnail_url"`
	FileRawURL       *string         `json:"file_raw_url"`
	Private          uint8           `json:"private"`
	Meta             *datatypes.JSON `json:"meta"`
}

type SendMessageRequest struct {
	Data SendMessageRequestData `json:"data"`
}

type SendMessageRequestData struct {
	TicketID           string            `json:"ticket_id"`
	ConversationID     string            `json:"conversation_id"`
	Content            string            `json:"content"`
	SenderID           string            `json:"sender_id"`
	SenderType         string            `json:"sender_type"` // USER, AGENT, SYSTEM
	Private            *bool             `json:"private"`
	MessageType        string            `json:"message_type"` // TEXT, TEMPLATE, etc.
	Visible            *bool             `json:"visible"`
	Status             string            `json:"status"` // SENT, DELIVERED, READ
	UpdateTicketStatus string            `json:"update_ticket_status,omitempty"`
	Attachments        []AttachmentInput `json:"attachments"`
	Source             string            `json:"source"` // Source of the message (e.g., "app", "web", etc.)
	PendingReply       bool              `json:"pending_reply"`
}

type AttachmentInput struct {
	FileType         string          `json:"file_type"`
	FileURL          string          `json:"file_url"`
	FileSize         int             `json:"file_size"`
	FileThumbnailURL *string         `json:"file_thumbnail_url"`
	Meta             *datatypes.JSON `json:"meta"`
	ContentType      *string         `json:"content_type"`
}

type SendMessageResponse struct {
	Message TicketMessage `json:"message"`
	Success bool          `json:"success"`
}

type GetCsMetaRequest struct {
	Data GetCsMetaRequestData `json:"data"`
}

type GetCsMetaRequestData struct {
	AgentID    string `json:"agent_id"`
	AgentEmail string `json:"agent_email"`
	Source     string `json:"source"`
}
type GetCSTicketDetailsRequest struct {
	Data  GetCSTicketDetailsRequestData `json:"data"`
	Email string                        `json:"email"`
}

type GetCSTicketDetailsRequestData struct {
	TicketID       string `json:"ticket_id"`
	ConversationID string `json:"conversation_id"`
	OrderID        string `json:"order_id"`
	UserID         string `json:"user_id"`
	AgentID        string `json:"agent_id"`
	Source         string `json:"source"`
}
type GetCSTicketDetailsResponse struct {
	Ticket             map[string]interface{} `json:"ticket"`
	UserDetails        map[string]interface{} `json:"user_details"`
	OrderDetails       interface{}            `json:"order_details"`
	TrackingInfo       []AWB                  `json:"tracking_info"`
	CannedResponses    []QuickReplyTemplate   `json:"canned_responses"`
	RecommendedActions []string               `json:"recommended_actions"`
}

type TicketUserDetails struct {
	UserID                    string   `json:"user_id"`
	UserName                  string   `json:"user_name"`
	UserPhone                 string   `json:"user_phone"`
	TotalPlacedOrderAmount    *float64 `json:"total_placed_order_amount"`
	TotalConfirmedOrderAmount *float64 `json:"total_confirmed_order_amount"`
	TotalDeliveredOrderAmount *float64 `json:"total_delivered_order_amount"`
	TotalReturnedOrderAmount  *float64 `json:"total_returned_order_amount"`
	TotalCancelledOrderAmount *float64 `json:"total_cancelled_order_amount"`
	TotalOrderPlaced          *int64   `json:"total_order_placed"`
	TotalOrderConfirmed       *int64   `json:"total_order_confirmed"`
	TotalOrderDelivered       *int64   `json:"total_order_delivered"`
	TotalOrderReturned        *int64   `json:"total_order_returned"`
	TotalOrderCancelled       *int64   `json:"total_order_cancelled"`
}

// Request structure
type TicketActionRequest struct {
	Data TicketActionRequestData `json:"data"`
}

type TicketActionRequestData struct {
	TicketID                 string     `json:"ticket_id"`
	Action                   string     `json:"action"`    // ASSIGN, CHANGE_PRIORITY, CHANGE_CATEGORY, CHANGE_STATUS
	ActionBy                 string     `json:"action_by"` // Agent ID who performed the action
	NewAssignee              string     `json:"new_assignee,omitempty"`
	NewPriority              string     `json:"new_priority,omitempty"`
	NewCategory              int        `json:"new_category,omitempty"`
	NewStatus                string     `json:"new_status,omitempty"`
	AddMessageToConversation bool       `json:"add_message_to_conversation"`
	ActionData               ActionData `json:"action_data,omitempty"`
	Source                   string     `json:"source"` // Source of the request (e.g., "app", "web", etc.)
	IgnoreTicketStatusChange bool       `json:"ignore_ticket_status_change"`
}

type ActionData struct {
	TicketActionID  string            `json:"ticket_action_id"` // Unique ID for the action
	RequestedAction string            `json:"requested_action"` // e.g., "escalate", "transfer"
	RequestedBy     string            `json:"requested_by"`     // Agent ID who requested the action
	RequestedMeta   datatypes.JSON    `json:"requested_meta"`   // Timestamp of the request
	ResponseAction  string            `json:"response_action"`  // e.g., "approve", "reject"
	ResponseBy      string            `json:"response_by"`      // Agent ID who responded to the action
	ResponseMeta    datatypes.JSON    `json:"response_meta"`    // Timestamp of the response
	Note            string            `json:"note"`             // Optional note for the action
	Attachments     []AttachmentInput `json:"attachments"`      // Optional attachments for the action
}

type ChalengeMetaData struct {
	Message     string            `json:"message"`
	Attachments []AttachmentInput `json:"attachments"`
}

// Response structure
type TicketActionResponse struct {
	Success      bool                   `json:"success"`
	TicketID     string                 `json:"ticket_id"`
	Changes      map[string]interface{} `json:"changes"`
	UpdatedAt    int64                  `json:"updated_at"`
	UpdatedBy    string                 `json:"updated_by"`
	ActionType   string                 `json:"action_type"`
	UpdatedState dao.CSTicket           `json:"updated_state"`
}

type QuickReplyTemplate struct {
	ID   string
	Icon interface{}
	Name string
	Text string
}

type DeliveryTemplateParams struct {
	EstimatedDelivery string // Delivery date, e.g. "5 अप्रैल"
	CurrentCity       string // Current location, e.g. "Delhi"
	DestinationCity   string // Destination, if needed
	AWB               string // AWB number for tracking
	Courier           string // Courier name, e.g. "delhivery", "shiprocket"
}

type SubmitRatingRequest struct {
	Data SubmitRatingRequestData `json:"data"`
	Meta Meta                    `json:"meta"`
}

type SubmitRatingRequestData struct {
	OrderID  int                    `json:"order_id"`
	TicketID string                 `json:"ticket_id"`
	Rating   float32                `json:"rating" validate:"required,min=1,max=5"`
	Feedback string                 `json:"feedback"`
	UserID   string                 `json:"user_id" validate:"required"`
	Source   string                 `json:"source"` // KC_APP, B2B_INTERNAL, etc.
	Meta     map[string]interface{} `json:"meta"`   // Any additional data
}

type SubmitRatingResponse struct {
	Success bool               `json:"success"`
	Message string             `json:"message"`
	Rating  RatingResponseData `json:"rating"`
}

type RatingResponseData struct {
	ID        int      `json:"id"`
	OrderID   *int     `json:"order_id"`
	TicketID  *string  `json:"ticket_id"`
	Rating    *float32 `json:"rating"`
	Feedback  *string  `json:"feedback"`
	UserID    string   `json:"user_id"`
	CreatedAt uint64   `json:"created_at"`
}

// Updated DTOs
type GetSupportInsightsResponse struct {
	Data GetSupportInsightsResponseData `json:"data"`
}

type GetSupportInsightsResponseData struct {
	TotalTickets     int                     `json:"total_tickets"`
	TotalOpenTickets int                     `json:"total_open_tickets"`
	ByAssignee       []OpenTicketsByAssignee `json:"by_assignee"`
	ByCategory       []OpenTicketsByCategory `json:"by_category"`
	BySeller         []OpenTicketsBySeller   `json:"by_seller"`
	ByStatus         []TicketsByStatus       `json:"by_status"`
}

type OpenTicketsByAssignee struct {
	AssigneeName    string  `json:"assignee_name"`
	AssigneeID      string  `json:"assignee_id"`
	AssigneeAvatar  string  `json:"assignee_avatar"`
	AssigneeTeam    string  `json:"assignee_team"`
	AssigneeEmail   string  `json:"assignee_email"`
	TotalCount      int     `json:"total_count"`
	OpenCount       int     `json:"open_count"`
	OpenPercentage  float64 `json:"open_percentage"`
	TotalPercentage float64 `json:"total_percentage"`
}

type OpenTicketsByCategory struct {
	CategoryName    string  `json:"category_name"`
	CategoryCode    string  `json:"category_code"`
	CategoryID      int     `json:"category_id"`
	TotalCount      int     `json:"total_count"`
	OpenCount       int     `json:"open_count"`
	OpenPercentage  float64 `json:"open_percentage"`
	TotalPercentage float64 `json:"total_percentage"`
}

type OpenTicketsBySeller struct {
	SellerName      string  `json:"seller_name"`
	SellerCode      string  `json:"seller_code"`
	TotalCount      int     `json:"total_count"`
	OpenCount       int     `json:"open_count"`
	OpenPercentage  float64 `json:"open_percentage"`
	TotalPercentage float64 `json:"total_percentage"`
}

type TicketsByStatus struct {
	Status     string  `json:"status"`
	Label      string  `json:"label"`
	Count      int     `json:"count"`
	Percentage float64 `json:"percentage"`
}

type GetSupportInsightsRequest struct {
	Data GetSupportInsightsRequestData `json:"data"`
}

type GetSupportInsightsRequestData struct {
	Filters map[string]interface{} `json:"filters"`
	From    int64                  `json:"from"`
	To      int64                  `json:"to"`
	Email   string                 `json:"email"`
	AgentID string                 `json:"agent_id"`
	Source  string                 `json:"source"`
}

type CsTicketOrderDetails struct {
	ID                   *string        `gorm:"column:order_id" json:"id"`
	OrderDetails         datatypes.JSON `gorm:"column:order_details" json:"order_details"`
	Seller               *string        `gorm:"column:seller" json:"seller"`
	UserID               *string        `gorm:"column:user_id" json:"user_id"`
	OrderValue           *float64       `gorm:"column:order_value" json:"order_value"`
	Status               *string        `gorm:"column:status" json:"status"`
	CreatedAt            *string        `gorm:"column:created_at" json:"created_at"`
	Courier              *string        `gorm:"column:courier" json:"courier"`
	AwbNumber            *string        `gorm:"column:awb_number" json:"awb_number"`
	PromisedDelivery     *int64         `gorm:"column:promised_delivery" json:"promised_delivery"`
	OrderPlaced          *int64         `gorm:"column:order_placed" json:"order_placed"`
	OrderConfirmed       *int64         `gorm:"column:order_confirmed" json:"order_confirmed"`
	OrderCancelled       *int64         `gorm:"column:order_cancelled" json:"order_cancelled"`
	OrderDispatched      *int64         `gorm:"column:order_dispatched" json:"order_dispatched"`
	OrderDelivered       *int64         `gorm:"column:order_delivered" json:"order_delivered"`
	OrderReturned        *int64         `gorm:"column:order_returned" json:"order_returned"`
	OrderShipmentCreated *int64         `gorm:"column:order_shipment_created" json:"order_shipment_created"`
	OrderOfd             *int64         `gorm:"column:order_ofd" json:"order_ofd"`
	AttemptCount         *int64         `gorm:"column:attempt_count" json:"attempt_count"`
}

type UpdateMediaRequest struct {
	JobID         string `json:"job_id"`
	URL           string `json:"url"`
	StreamingURL  string `json:"streaming_url"`
	ThumbnailURL  string `json:"thumbnail_url"`
	GifURL        string `json:"gif_url"`
	HighResGIFURL string `json:"thumbnail_res_gif_url"`
}

type UpdateMediaResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// AutoReplyResult represents the result of auto reply processing
type AutoReplyResult struct {
	ShouldSendAutoReply bool   `json:"should_send_auto_reply"`
	Message             string `json:"message"`
	ShouldResolveTicket bool   `json:"should_resolve_ticket"`
	ResolutionNote      string `json:"resolution_note"`
}
