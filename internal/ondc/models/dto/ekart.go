package dto

import (
	"time"
)

type EkartWebhookRequestInterface any

// EkartWebhookRequest represents the complete structure of request_object from ekart_webhook_logs
type EkartWebhookRequest struct {
	// Core fields present in all events
	Event               string   `json:"event"`
	OrderID             uint64   `json:"order_id"`
	Reason              string   `json:"reason"`
	Status              string   `json:"status"`
	EntityID            string   `json:"entity_id"`
	EventDate           string   `json:"event_date"` // Could also use time.Time with custom parsing
	RequestID           string   `json:"request_id,omitempty"`
	SubReasons          []string `json:"sub_reasons"`
	MerchantCode        string   `json:"merchant_code"`
	MerchantName        string   `json:"merchant_name"`
	ShipmentType        string   `json:"shipment_type"`
	VendorTrackingID    string   `json:"vendor_tracking_id"`
	MerchantReferenceID string   `json:"merchant_reference_id"`

	// Optional fields that appear in some events
	Location    string `json:"location,omitempty"`
	Remarks     string `json:"remarks,omitempty"`
	SellerID    string `json:"seller_id,omitempty"`
	CourierName string `json:"courier_name,omitempty"`

	// Complex nested structure for delivery metadata
	MetaData *DeliveryMetaData `json:"meta_data,omitempty"`
}

// DeliveryMetaData represents the metadata field in delivery_attempt_metadata events
type DeliveryMetaData struct {
	CallRecordings  []CallRecording  `json:"call_recordings,omitempty"`
	AttemptLocation *AttemptLocation `json:"attempt_location,omitempty"`
}

// CallRecording represents individual call recording data
type CallRecording struct {
	Status    string `json:"status"`
	Duration  string `json:"duration"`
	StartTime string `json:"start_time"` // Could also use time.Time with custom parsing
}

// AttemptLocation represents GPS coordinates of delivery attempt
type AttemptLocation struct {
	Latitude  string `json:"latitude"`
	Longitude string `json:"longitude"`
}

// Alternative struct with time.Time fields for better date handling
type EkartWebhookRequestWithTime struct {
	Event               string                    `json:"event"`
	Reason              string                    `json:"reason"`
	Status              string                    `json:"status"`
	EntityID            string                    `json:"entity_id"`
	EventDate           time.Time                 `json:"event_date"`
	RequestID           string                    `json:"request_id,omitempty"`
	SubReasons          []string                  `json:"sub_reasons"`
	MerchantCode        string                    `json:"merchant_code"`
	MerchantName        string                    `json:"merchant_name"`
	ShipmentType        string                    `json:"shipment_type"`
	VendorTrackingID    string                    `json:"vendor_tracking_id"`
	MerchantReferenceID string                    `json:"merchant_reference_id"`
	Location            string                    `json:"location,omitempty"`
	Remarks             string                    `json:"remarks,omitempty"`
	SellerID            string                    `json:"seller_id,omitempty"`
	CourierName         string                    `json:"courier_name,omitempty"`
	MetaData            *DeliveryMetaDataWithTime `json:"meta_data,omitempty"`
}

// DeliveryMetaDataWithTime with proper time parsing
type DeliveryMetaDataWithTime struct {
	CallRecordings  []CallRecordingWithTime `json:"call_recordings,omitempty"`
	AttemptLocation *AttemptLocation        `json:"attempt_location,omitempty"`
}

// CallRecordingWithTime with proper time parsing
type CallRecordingWithTime struct {
	Status    string    `json:"status"`
	Duration  string    `json:"duration"`
	StartTime time.Time `json:"start_time"`
}

func ParseEkartDateTime(dateStr string) (time.Time, error) {
	// Ekart uses format: "2025-05-29 09:57:50"
	// IST
	ist, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		return time.Time{}, err
	}
	return time.ParseInLocation("2006-01-02 15:04:05", dateStr, ist)
}
