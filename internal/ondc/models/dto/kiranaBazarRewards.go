package dto

import (
	"time"

	"gorm.io/datatypes"
)

type GetKiranaBazarRewardsRequest struct {
	UserID string                 `json:"user_id"`
	Data   KiranaBazarRewardsData `json:"data"`
	Meta   Meta                   `json:"meta"`
}

type KiranaBazarRewardsData struct {
	RewardID              string         `json:"reward_id"`
	ID                    int64          `json:"id"`
	CategoryID            int64          `json:"category_id"`
	Name                  string         `json:"name"`
	Code                  string         `json:"code"`
	Meta                  datatypes.JSON `json:"meta"`
	IsActive              bool           `json:"is_active"`
	IsOos                 bool           `json:"is_oos"`
	IsDefault             bool           `json:"is_default"`
	SizeVariantCode       int64          `json:"size_variant_code"`
	ImageUrls             datatypes.JSON `json:"image_urls"`
	NameLabel             string         `json:"name_label"`
	CodeLabel             string         `json:"code_label"`
	RatingsSum            int            `json:"ratings_sum"`
	RatingsCount          int            `json:"ratings_count"`
	UserID                string         `json:"user_id"`
	ProductID             int            `json:"product_id"`
	Address               datatypes.JSON `json:"address"`
	AWB                   string         `json:"awb"`
	Courier               string         `json:"courier"`
	CreatedAt             time.Time      `json:"created_at"`
	UpdatedAt             time.Time      `json:"updated_at"`
	OrderStatus           string         `json:"order_status"`
	ProcessingStatus      string         `json:"processing_status"`
	ShipmentStatus        string         `json:"shipment_status"`
	DisplayStatus         string         `json:"display_status"`
	IsArchived            bool           `json:"is_archived"`
	TrackingLink          string         `json:"tracking_link"`
	TentativeDeliveryDate time.Time      `json:"tentative_delivery_date"`
}

type GetKiranaBazarRewardsResponse struct {
	Meta  Meta                     `json:"meta"`
	Data  []KiranaBazarRewardsData `json:"data"`
	Error AppResponseError         `json:"error,omitempty"`
}
