// internal/ondc/models/dto/call_dto.go
package dto

import (
	"encoding/json"
)

type InitiateCallRequest struct {
	Data struct {
		OrderID       int64  `json:"order_id"`
		FromNumber    string `json:"from_number"`
		ToNumber      string `json:"to_number"`
		VirtualNumber string `json:"virtual_number,omitempty"`
		Purpose       string `json:"purpose"`
		InitiatedBy   string `json:"initiated_by"`
	} `json:"data"`
}

type InitiateCallResponse struct {
	CallID  int64  `json:"call_id"`
	CallSID string `json:"call_sid"`
	Status  string `json:"status"`
	Message string `json:"message"`
}

// Exotel V1 API Response structures
type ExotelV1Response struct {
	Call          ExotelCallDetails `json:"Call"`
	RestException *ExotelError      `json:"RestException,omitempty"`
}

type ExotelCallDetails struct {
	Sid            string `json:"Sid"`
	AccountSid     string `json:"AccountSid"`
	From           string `json:"From"`
	To             string `json:"To"`
	PhoneNumberSid string `json:"PhoneNumberSid"`
	Status         string `json:"Status"`
	StartTime      string `json:"StartTime"`
	EndTime        string `json:"EndTime"`
	Duration       int    `json:"Duration"`
	Price          string `json:"Price"`
	Direction      string `json:"Direction"`
	URI            string `json:"Uri"`
	RecordingUrl   string `json:"RecordingUrl"`
	DateCreated    string `json:"DateCreated"`
	DateUpdated    string `json:"DateUpdated"`
}

type ExotelError struct {
	Status   int             `json:"Status"`
	Message  string          `json:"Message"`
	Code     json.RawMessage `json:"Code"` // Can be string or number
	MoreInfo string          `json:"MoreInfo"`
}

// Webhook callback structure
type ExotelStatusCallback struct {
	CallSid              string          `json:"CallSid"`
	CallType             string          `json:"CallType"`
	CallFrom             string          `json:"CallFrom"`
	CallTo               string          `json:"CallTo"`
	Direction            string          `json:"Direction"`
	From                 string          `json:"From"`
	To                   string          `json:"To"`
	CurrentTime          string          `json:"CurrentTime"`
	DialWhomNumber       string          `json:"DialWhomNumber"`
	Status               string          `json:"Status"`
	StartTime            string          `json:"StartTime"`
	EndTime              string          `json:"EndTime"`
	CallDuration         string          `json:"CallDuration"`
	DialCallDuration     int             `json:"DialCallDuration"`
	RecordingUrl         string          `json:"RecordingUrl"`
	DialCallStatus       string          `json:"DialCallStatus"`
	CallResult           string          `json:"CallResult"`
	Digits               string          `json:"Digits"`
	DateCreated          string          `json:"DateCreated"`
	DateUpdated          string          `json:"DateUpdated"`
	CustomField          string          `json:"CustomField"`
	ConversationDuration int             `json:"ConversationDuration"`
	Legs                 []ExotelCallLeg `json:"Legs"`
}

type ExotelCallLeg struct {
	OnCallDuration int    `json:"OnCallDuration" form:"OnCallDuration"`
	Status         string `json:"Status" form:"Status"`
}

type CallDetailsResponse struct {
	CallID       int64                  `json:"call_id"`
	CallSID      string                 `json:"call_sid"`
	Status       string                 `json:"status"`
	Duration     int                    `json:"duration"`
	RecordingUrl string                 `json:"recording_url,omitempty"`
	Events       []CallEvent            `json:"events"`
	CreatedAt    string                 `json:"created_at"`
	UpdatedAt    string                 `json:"updated_at"`
	AnsweredAt   *string                `json:"answered_at,omitempty"`
	CompletedAt  *string                `json:"completed_at,omitempty"`
	MetaData     map[string]interface{} `json:"meta_data"`
}

type CallEvent struct {
	ID           int64  `json:"id"`
	EventType    string `json:"event_type"`
	Status       string `json:"status"`
	Timestamp    string `json:"timestamp"`
	Duration     int    `json:"duration,omitempty"`
	RecordingUrl string `json:"recording_url,omitempty"`
}

type ExotelCallDetailsResponse struct {
	Call ExotelCallDetails `json:"Call"`
}

type GetExotelCallResponse struct {
	Call struct {
		Sid        string `json:"Sid"`
		Status     string `json:"Status"`
		AnsweredBy string `json:"AnsweredBy"` // "human", "machine", or empty
		Duration   string `json:"Duration"`
		Details    struct {
			ConversationDuration int    `json:"ConversationDuration"`
			Leg1Status           string `json:"Leg1Status"`
			Leg2Status           string `json:"Leg2Status"`
		} `json:"Details"`
	} `json:"Call"`
}

// IVR call details response from SID
type CallDetails struct {
	Sid            string  `json:"Sid"`
	ParentCallSid  string  `json:"ParentCallSid"`
	DateCreated    string  `json:"DateCreated"`
	DateUpdated    string  `json:"DateUpdated"`
	AccountSid     string  `json:"AccountSid"`
	To             string  `json:"To"`
	From           string  `json:"From"`
	PhoneNumberSid string  `json:"PhoneNumberSid"`
	Status         string  `json:"Status"`
	StartTime      string  `json:"StartTime"`
	EndTime        string  `json:"EndTime"`
	Duration       int     `json:"Duration"`
	Price          float64 `json:"Price"`
	Direction      string  `json:"Direction"`
	AnsweredBy     string  `json:"AnsweredBy"`
	ForwardedFrom  string  `json:"ForwardedFrom"`
	CallerName     string  `json:"CallerName"`
	Uri            string  `json:"Uri"`
	RecordingUrl   string  `json:"RecordingUrl"`
}

// IVR call details response from SID
type CallDetailForSID struct {
	Call CallDetails `json:"Call"`
}
