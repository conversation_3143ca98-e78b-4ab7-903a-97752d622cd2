package dto

import "kc/internal/ondc/models/shared"

type AppGetProductDetailsRequest struct {
	Data   AppGetProductDetailsData `json:"data"`
	UserID string                   `json:"user_id"`
	Meta   Meta                     `json:"meta"`
}

type AppGetProductDetailsData struct {
	ProductID string `json:"product_id"`
}

type AppGetProductDetailsResponse struct {
	Meta  Meta             `json:"meta"`
	Data  ProductDetails   `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type ProductDetails struct {
	Product shared.SellerItems `json:"product"`
}
