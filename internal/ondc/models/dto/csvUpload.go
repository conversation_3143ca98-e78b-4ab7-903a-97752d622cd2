package dto

type UpdateBulkDataRequest struct {
	UpdatedBy string                    `json:"updated_by"`
	Data      UpdateBulkDataRequestData `json:"data"`
}

type UpdateBulkDataRequestData struct {
	UpdatedAt int64  `json:"updated_at"`
	UpdatedBy string `json:"updated_by"`
	Seller    string `json:"seller"`
	Type      string `json:"type"`
	Data      string `json:"data"`
}

type UpdateBulkDataResponse struct {
	Error *string                     `json:"error"`
	Data  UpdateBulkDataResponseStats `json:"data"`
}

type SubmitBulkDataResponse struct {
	Message string `json:"message"`
}

type UpdateBulkDataResponseStats struct {
	TotalRecords   int    `json:"total_records"`
	Success        int    `json:"success"`
	Failed         int    `json:"failed"`
	ResponseCsvUrl string `json:"response_csv_url"`
	RequestCsvUrl  string `json:"request_csv_url"`
}

type ProcessedCSV struct {
	OriginalData []string
	Success      bool
	Response     string
	Error        string
}

type GetBulkActionLogsRequest struct {
	Data struct {
		UpdatedBy *string `json:"updated_by"`
		Limit     *int    `json:"limit"`
		Offset    *int    `json:"offset"`
	} `json:"data"`
}

type GetBulkActionLogsResponse struct {
	Total int                             `json:"total"`
	Data  []GetBulkActionLogsResponseData `json:"data"`
}

type GetBulkActionLogsResponseData struct {
	ID             string  `json:"id"`
	UpdatedAt      int64   `json:"updated_at"`
	UpdatedBy      string  `json:"updated_by"`
	Seller         string  `json:"seller"`
	Type           string  `json:"type"`
	TotalRecords   int     `json:"total_records"`
	Success        int     `json:"success"`
	Failed         int     `json:"failed"`
	ResponseCsvUrl string  `json:"response_csv_url,omitempty"`
	RequestCsvUrl  string  `json:"request_csv_url,omitempty"`
	Status         *string `json:"status,omitempty"`
	Message        *string `json:"message,omitempty"`
	ActionType     *string `json:"action_type,omitempty"`
	FileID         *string `json:"file_id,omitempty"`
	Force          *bool   `json:"force"`
}
