package dto

import (
	"fmt"
	"maps"
	"strings"

	"github.com/Masterminds/semver"
)

type HighlightedTextData struct {
	Text           string                  `json:"text,omitempty"`
	HighlightStyle *map[string]interface{} `json:"highlight_style,omitempty"`
}

type OfferProgressBar struct {
	Styles map[string]interface{} `json:"styles"`
	Value  *float64               `json:"value,omitempty"` // Value should be between 0 and 1
}

type OfferMessage struct {
	Text            *string                 `json:"text"`
	MinOrderValue   *int                    `json:"min_order_value"`
	Discount        *float64                `json:"discount"`
	DiscountAmount  *int                    `json:"discount_amount"`
	HighlightedText []HighlightedTextData   `json:"highlighted_text,omitempty"`
	Styles          *map[string]interface{} `json:"styles,omitempty"`
	IconType        *string                 `json:"icon_type,omitempty"`
	ProgressBar     *OfferProgressBar       `json:"progress_bar,omitempty"`
	RightIconUrl    *string                 `json:"right_icon_url,omitempty"`
	LeftIconUrl     *string                 `json:"left_icon_url,omitempty"`
	Seller          *string                 `json:"seller,omitempty"` // Optional field for seller
}

// MessageBuilder provides a clean, fluent interface for constructing formatted messages
type MessageBuilder struct {
	text              string
	highlightedText   []HighlightedTextData
	styles            map[string]interface{}
	highlightStyle    map[string]interface{}
	iconType          string
	RightIconUrl      *string `json:"right_icon_url,omitempty"`
	LeftIconUrl       *string `json:"left_icon_url,omitempty"`
	minOrderValue     *int
	discountAmount    *int
	discount          *float64
	formatMap         map[string]any
	ProgressBarStyles map[string]interface{} `json:"progress_bar_styles,omitempty"`
	ProgressBarValue  *float64               `json:"progress_bar_value,omitempty"`
	Seller            *string                `json:"seller,omitempty"` // Optional field for seller
}

// NewMessageBuilder creates a new MessageBuilder with default styles
func NewMessageBuilder() *MessageBuilder {
	return &MessageBuilder{
		styles: map[string]interface{}{
			"color":           "#000000",
			"backgroundColor": "#FFD9D9",
			"gradientColors": []string{
				"#E1EFFF",
				"#F0F7FF",
			},
		},
		highlightStyle: map[string]interface{}{
			"color":      "#000000",
			"fontWeight": 700,
		},
		formatMap: make(map[string]any),
	}
}

// WithText sets the text template and applies formatting using the format map
func (b *MessageBuilder) WithText(textTemplate string) *MessageBuilder {
	if textTemplate != "" {
		b.text = FormatTemplate(textTemplate, b.formatMap)
	}
	return b
}

// WithHighlightedTexts sets the highlighted text templates and applies formatting
func (b *MessageBuilder) WithHighlightedTexts(templates []string) *MessageBuilder {
	b.highlightedText = make([]HighlightedTextData, 0, len(templates))
	for _, t := range templates {
		content := FormatTemplate(t, b.formatMap)
		b.highlightedText = append(b.highlightedText, HighlightedTextData{
			Text:           content,
			HighlightStyle: &b.highlightStyle,
		})
	}
	return b
}

// WithStyles sets custom styles for the message
func (b *MessageBuilder) WithStyles(styles map[string]interface{}, appVersion string) *MessageBuilder {
	if len(styles) > 0 {
		if appVersion != "" {
			userAppVersion, _ := semver.NewVersion(appVersion)
			offerStylesChangeVersion, _ := semver.NewVersion("6.5.5")
			if userAppVersion.GreaterThan(offerStylesChangeVersion) {
				styles["backgroundColor"] = nil
			}
		}
		b.styles = styles
	}
	return b
}

// WithHighlightStyle sets custom highlight styles
func (b *MessageBuilder) WithHighlightStyle(style map[string]interface{}) *MessageBuilder {
	if len(style) > 0 {
		b.highlightStyle = style
	}
	return b
}

// WithIconType sets the icon type
func (b *MessageBuilder) WithIconType(iconType string) *MessageBuilder {
	b.iconType = iconType
	return b
}

// WithMinOrderValue sets the minimum order value
func (b *MessageBuilder) WithMinOrderValue(value int) *MessageBuilder {
	b.minOrderValue = &value
	return b
}

// WithDiscountAmount sets the discount amount
func (b *MessageBuilder) WithDiscountAmount(amount int) *MessageBuilder {
	if amount > 0 {
		b.discountAmount = &amount
	}
	return b
}

// WithDiscount sets the discount value
func (b *MessageBuilder) WithDiscount(discount float64) *MessageBuilder {
	b.discount = &discount
	return b
}

// WithFormatValues adds values to the format map
func (b *MessageBuilder) WithFormatValues(values map[string]any) *MessageBuilder {
	maps.Copy(b.formatMap, values)
	return b
}

// WithRightIconUrl sets the URL for the right icon
func (b *MessageBuilder) WithRightIconUrl(url *string) *MessageBuilder {
	b.RightIconUrl = url
	return b
}

// WithLeftIconUrl sets the URL for the left icon
func (b *MessageBuilder) WithLeftIconUrl(url *string) *MessageBuilder {
	b.LeftIconUrl = url
	return b
}

func (b *MessageBuilder) WithProgressBarStyles(styles map[string]interface{}) *MessageBuilder {
	if len(styles) > 0 {
		b.ProgressBarStyles = styles
	}
	return b
}

// WithProgressBarValue sets the value for the progress bar
func (b *MessageBuilder) WithProgressBarValue(cartValue, couponMov float64) *MessageBuilder {
	if cartValue <= 0 || couponMov <= 0 {
		b.ProgressBarValue = nil
	} else {
		value := cartValue / couponMov
		if value < 0 {
			value = 0
		} else if value > 1 {
			value = 1
		}
		b.ProgressBarValue = &value
	}
	return b
}

// WithSeller sets the seller for the message
func (b *MessageBuilder) WithSeller(seller string) *MessageBuilder {
	if seller != "" {
		b.Seller = &seller
	}
	return b
}

// Build constructs and returns the final OfferMessage
func (b *MessageBuilder) Build() *OfferMessage {

	// progressBar := map[string]interface{}{
	// 	"styles": map[string]interface{}{
	// 		"incomplete_background": "#E7E8F8",
	// 		"completed_background": []string{
	// 			"#EE0004",
	// 			"#7C0103",
	// 		},
	// 	},
	// 	"value": 0.5,
	// }
	message := &OfferMessage{
		Text:            &b.text,
		Styles:          &b.styles,
		IconType:        &b.iconType,
		HighlightedText: b.highlightedText,
		RightIconUrl:    b.RightIconUrl,
		LeftIconUrl:     b.LeftIconUrl,
	}

	// Add optional fields if present
	if b.minOrderValue != nil {
		message.MinOrderValue = b.minOrderValue
	}

	if b.discountAmount != nil {
		message.DiscountAmount = b.discountAmount
	}

	if b.discount != nil {
		message.Discount = b.discount
	}

	if b.ProgressBarStyles != nil && b.ProgressBarValue != nil {
		message.ProgressBar = &OfferProgressBar{
			Styles: b.ProgressBarStyles,
			Value:  b.ProgressBarValue,
		}
	}

	if b.Seller != nil {
		message.Seller = b.Seller
	}

	return message
}

// Helper function that uses the builder pattern
func BuildFormattedMessage(
	textTemplate string,
	highlightTemplates []string,
	highlightStyle, styles map[string]interface{},
	iconType string,
	formatMap map[string]any,
	appVersion string,
) *OfferMessage {
	return NewMessageBuilder().
		WithFormatValues(formatMap).
		WithText(textTemplate).
		WithHighlightedTexts(highlightTemplates).
		WithHighlightStyle(highlightStyle).
		WithStyles(styles, appVersion).
		WithIconType(iconType).
		Build()
}

func FormatTemplate(template string, values map[string]any) string {
	result := template

	for key, val := range values {
		placeholder := fmt.Sprintf("{{%s}}", key)
		var replacement string

		switch v := val.(type) {
		case float64:
			replacement = fmt.Sprintf("%.2f", v)
		case float32:
			replacement = fmt.Sprintf("%.2f", v)
		case int:
			replacement = fmt.Sprintf("%d", v)
		case string:
			replacement = v
		default:
			replacement = fmt.Sprintf("%v", v) // Fallback for unsupported types
		}

		result = strings.ReplaceAll(result, placeholder, replacement)
	}

	return result
}
