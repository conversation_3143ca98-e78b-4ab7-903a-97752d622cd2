package dto

type CSVRecord struct {
	OrderID                int64
	SaleOrderItemCode      string
	DisplayOrderCode       string
	WholesalePrice         string
	MRP                    string
	TotalPrice             string
	TaxableValue           string
	Tax                    string
	TotalPriceWithoutTax   *string
	TotalDiscount          string
	PlatformDiscount       string
	SellerDiscount         string
	PacketNumber           string
	Date                   string
	ItemSKUCode            string
	ItemSKUName            string
	ShippingAddressName    *string
	ShippingAddressPhone   *string
	ShippingAddressLine1   string
	ShippingAddressLine2   string
	ShippingAddressCity    *string
	ShippingAddressState   *string
	ShippingAddressPincode *string
	PaymentType            string
	UserID                 string
	PartPaid               string
	AdvancePaidAmount      string
	CodAmount              string
	OrderStatus            string
	ProductActiveStatus    string
}

type OrderLevelCSVRecord struct {
	DisplayOrderCode string `json:"display_order_code"`
	OrderID          int64  `json:"order_id"`
	UserID           string `json:"user_id"`
	PlacedDate       string `json:"placed_date"`
	ConfirmedDate    string `json:"confirmed_date"`
	DispatchedDate   string `json:"dispatched_date"`
	DeliveredDate    string `json:"delivered_date"`
	InvoiceAmount    string `json:"invoice_amount"`
	Seller           string `json:"seller"`
	Courier          string `json:"courier"`
	AWBNumber        string `json:"awb_number"`
	Pincode          string `json:"pincode"`
	State            string `json:"state"`
	City             string `json:"city"`
	InvoiceNumber    string `json:"invoice_number"`
	OrderStatus      string `json:"order_status"`

	// these need to be caluculated
	RTOExpectedDate  string `json:"rto_expected_date"`  // we dont have in reconciliation
	RTODeliveredDate string `json:"rto_delivered_date"` // we dont have in reconciliation
	RTOReason        string `json:"rto_reason"`         // we dont have in surveyable form
}

type OrderExportData struct {
	ID             int     `json:"id"`
	OrderDetails   string  `json:"order_details"`
	OrderAmount    float64 `json:"order_amount"`
	PaidAmount     float64 `json:"paid_amount"`
	OrderConfirmed int64   `json:"order_confirmed"`
	UserID         string  `json:"user_id"`
	Seller         string  `json:"seller"`
	OrderStatus    string  `json:"order_status"`
}

type ExportOrderPaymentDetailsRequest struct {
	Data struct {
		Seller string `json:"seller"`
		Year   int    `json:"year"`
		Month  int    `json:"month"`
	} `json:"data"`
}

type ExportOrderPaymentDetailsResponse struct {
	Data    []CSVRecordPaymentDetails `json:"data"`
	Headers []CustomField             `json:"headers"`
}

type CSVRecordPaymentDetails struct {
	OrderID               int64    `json:"order_id"`
	OrderDate             int64    `json:"order_date"`
	OrderDateString       string   `json:"order_date_string"`
	TotalOrderValue       float64  `json:"order_amount"`
	PartialPaidValue      float64  `json:"partial_paid_value"`
	PaidAmountProof       []string `json:"paid_amount_proof"`
	PaidAmountProofString string   `json:"paid_amount_proof_string"`
	PaymentDate           int64    `json:"payment_date"`
	PaymentDateString     string   `json:"payment_date_string"`
	PaymentStatus         string   `json:"payment_status"`
	PaymentType           string   `json:"payment_type"`
}
