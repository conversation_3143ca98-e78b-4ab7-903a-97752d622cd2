package dto

import "gorm.io/datatypes"

type GetKPICardsRequest struct {
	Data GetKPICardsRequestData `json:"data"`
}

type GetKPICardsRequestData struct {
	DateRange string   `json:"date_range" validate:"required,oneof=7d 30d mtd"`
	Seller    []string `json:"seller" validate:"required"`
}

type KPIQueryResult struct {
	GTV       float64 `gorm:"column:gtv"`
	Orders    int64   `gorm:"column:orders"`
	UnitsSold int64   `gorm:"column:units_sold"`
}

// DateRange represents a date range with start and end dates
type DateRange struct {
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
}

type GetKPICardsResponse struct {
	Filters          KPIFilters `json:"filters"`
	KPICards         KPICards   `json:"kpi_cards"`
	ComparisonPeriod DateRange  `json:"comparison_period"`
	GeneratedAt      string     `json:"generated_at"`
}

type KPIFilters struct {
	DateRange string   `json:"date_range"`
	Seller    []string `json:"seller"`
	StartDate string   `json:"start_date"`
	EndDate   string   `json:"end_date"`
}

type KPICards struct {
	GTV       KPIMetric `json:"gtv"`
	Orders    KPIMetric `json:"orders"`
	UnitsSold KPIMetric `json:"units_sold"`
	AOV       KPIMetric `json:"aov"`
}

type KPIMetric struct {
	Current       int64  `json:"current"`
	Previous      int64  `json:"previous"`
	ChangePercent int64  `json:"change_percent"`
	ChangeType    string `json:"change_type"`
}

type SKULeaderboardQueryResult struct {
	ProductID      int64          `gorm:"column:product_id"`
	SKUCode        string         `gorm:"column:sku_code"`
	MRP            float64        `gorm:"column:mrp"`
	Quantity       string         `gorm:"column:quantity"`
	SKUName        string         `gorm:"column:sku_name"`
	SellerName     string         `gorm:"column:seller_name"`
	IsActive       bool           `gorm:"column:is_active"`
	IsOos          bool           `gorm:"column:is_oos"`
	ImageUrls      string         `gorm:"column:image_urls"`
	MediaUrls      datatypes.JSON `gorm:"column:media_urls"` // Optional field for media URLs
	TotalUnitsSold int64          `gorm:"column:total_units_sold"`
	TotalGTV       float64        `gorm:"column:total_gtv"`
	TotalOrders    int64          `gorm:"column:total_orders"`
	GTVPerDay      float64        `gorm:"column:gtv_per_day"`
	ASP            float64        `gorm:"column:asp"`
	AOV            float64        `gorm:"column:aov"`
}

// SKUComparisonQueryResult represents the result from comparison query
type SKUComparisonQueryResult struct {
	ProductID     int64   `gorm:"column:product_id"`
	MRP           float64 `gorm:"column:mrp"`
	Quantity      string  `gorm:"column:quantity"`
	PrevUnitsSold int64   `gorm:"column:prev_units_sold"`
	PrevGTV       float64 `gorm:"column:prev_gtv"`
	PrevOrders    int64   `gorm:"column:prev_orders"`
}

type GetSKULeaderboardRequest struct {
	Data GetSKULeaderboardRequestData `json:"data"`
}

type GetSKULeaderboardRequestData struct {
	DateRange string   `json:"date_range" validate:"required,oneof=7d 30d mtd"`
	Seller    []string `json:"seller"` // Empty array means all sellers
	Limit     int      `json:"limit" validate:"required,min=1,max=20"`
	Offset    int      `json:"offset" validate:"min=0"`
	SortBy    string   `json:"sort_by" validate:"required,oneof=units_sold gtv orders"`
}

// GetSKULeaderboardResponse represents the response structure
type GetSKULeaderboardResponse struct {
	Filters          SKULeaderboardFilters `json:"filters"`
	SKULeaderboard   SKULeaderboardResult  `json:"sku_leaderboard"`
	ComparisonPeriod DateRange             `json:"comparison_period"`
	GeneratedAt      string                `json:"generated_at"`
}

type SKULeaderboardFilters struct {
	DateRange string      `json:"date_range"`
	Seller    interface{} `json:"seller"` // Can be string "all" or []string
	Limit     int         `json:"limit"`
	Offset    int         `json:"offset"`
	SortBy    string      `json:"sort_by"`
	StartDate string      `json:"start_date"`
	EndDate   string      `json:"end_date"`
}

type SKULeaderboardResult struct {
	Pagination Pagination           `json:"pagination"`
	Items      []SKULeaderboardItem `json:"items"`
}

type Pagination struct {
	CurrentOffset int   `json:"current_offset"`
	Limit         int   `json:"limit"`
	TotalItems    int64 `json:"total_items"`
	HasNext       bool  `json:"has_next"`
}

type SKULeaderboardItem struct {
	Rank          int     `json:"rank"`
	ProductID     int64   `json:"product_id"`
	SKUCode       string  `json:"sku_code"`
	SKUName       string  `json:"sku_name"`
	Quantity      string  `json:"quantity"`
	SellerName    string  `json:"seller_name"`
	ImageUrls     string  `json:"image_urls"`
	MRP           int64   `json:"mrp"`
	UnitsSold     int64   `json:"units_sold"`
	GTV           int64   `json:"gtv"`
	GTVPerDay     int64   `json:"gtv_per_day"`
	ASP           float64 `json:"asp"`
	AOV           int64   `json:"aov"`
	Orders        int64   `json:"orders"`
	ProductStatus string  `json:"product_status"`
	ChangePercent string  `json:"change_percent"`
	ChangeType    string  `json:"change_type"`
}

type GetSKUTrendsRequest struct {
	Data GetSKUTrendsRequestData `json:"data"`
}

type GetSKUTrendsRequestData struct {
	SKUs []SKUIdentifier `json:"skus" validate:"required,min=1,max=3"`
}

type SKUIdentifier struct {
	ProductID int64   `json:"product_id" validate:"required"`
	MRP       float64 `json:"mrp" validate:"required"`
	Quantity  string  `json:"quantity" validate:"required"`
}

type GetSKUTrendsResponse struct {
	Filters       SKUTrendsFilters `json:"filters"`
	CombinedTrend CombinedSKUTrend `json:"combined_trend"`
	GeneratedAt   string           `json:"generated_at"`
}

type CombinedSKUTrend struct {
	SelectedSKUs []SKUIdentifier `json:"selected_skus"`
	DailyData    []SKUDailyData  `json:"daily_data"`
}

type SKUTrendsFilters struct {
	DateRange DateRange `json:"date_range"`
	SKUCount  int       `json:"sku_count"`
}

type SKUDailyData struct {
	Date      string  `json:"date"`
	UnitsSold int64   `json:"units_sold"`
	GTV       int64   `json:"gtv"`
	ASP       float64 `json:"asp"`
	AOV       int64   `json:"aov"`
	Orders    int64   `json:"orders"`
}

// Query result structures
type CombinedSKUTrendQueryResult struct {
	Date           string  `gorm:"column:date"`
	DailyUnitsSold int64   `gorm:"column:daily_units_sold"`
	DailyGTV       float64 `gorm:"column:daily_gtv"`
	DailyOrders    int64   `gorm:"column:daily_orders"`
	DailyASP       float64 `gorm:"column:daily_asp"`
	DailyAOV       float64 `gorm:"column:daily_aov"`
}
