package dto

type Shipment struct {
	Status      Status `json:"Status"`
	PickUpDate  string `json:"PickUpDate"`
	NSLCode     string `json:"NSLCode"`
	Sortcode    string `json:"Sortcode"`
	ReferenceNo string `json:"ReferenceNo"`
	AWB         string `json:"AWB"`
}

type Status struct {
	Status         string `json:"Status"`
	StatusDateTime string `json:"StatusDateTime"`
	StatusType     string `json:"StatusType"`
	StatusLocation string `json:"StatusLocation"`
	Instructions   string `json:"Instructions"`
}

type DelhiveryWebhookRequest struct {
	Shipment Shipment `json:"Shipment"`
}

type DelhiveryNDRReattemptRequest struct {
	Data DelhiveryNDRReattempt `json:"data"`
}

type DelhiveryNDRReattempt struct {
	OrderId int `json:"order_id"`
}
