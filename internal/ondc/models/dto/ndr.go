package dto

import (
	"kc/internal/ondc/models/dao"
	"time"
)

type AddNDROrderRequest struct {
	OrderID        string  `json:"order_id"`
	NDRAgentReason *string `json:"ndr_agent_reason"`
	UpdatedBy      string  `json:"updated_by"`
	OrderStatus    string  `json:"order_status"`
	OrderValue     int     `json:"order_value"`
	NDRTag         *string `json:"ndr_tag"`
	Note           *string `json:"note"`
	AttemptCount   *int    `json:"attempt_count"`
	Source         *string `json:"source"`
}

type UpdateNDROrderResponse struct {
	Meta    Meta   `json:"meta"`
	Error   Error  `json:"error,omitempty"`
	Message string `json:"message"`
}

type UpdateNDROrderRequest struct {
	OrderID          string  `json:"order_id"`
	OrderActivityID  string  `json:"order_activity_id"`
	UpdatedAt        int64   `json:"updated_at"`
	UpdatedBy        string  `json:"updated_by"`
	OrderStatus      string  `json:"order_status"`
	AssignedTo       string  `json:"assigned_to"`
	CallConnected    *string `json:"call_connected"`
	ActionStatus     *string `json:"action_status"`
	NextActionAt     *string `json:"next_action_at"`
	EscalatedWith3PL *string `json:"escalated_with_3pl"`
	Note             *string `json:"note"`
	ReturnReason     *string `json:"returned_reason"`
	CancelReason     *string `json:"cancel_reason"`
	NDRAgentReason   *string `json:"ndr_agent_reason"`
	NDRUserReason    *string `json:"ndr_user_reason"`
	CallStatus       *string `json:"call_status"`
	OrderValue       *int    `json:"order_value"`
	NDRTag           *string `json:"ndr_tag"`
	AttemptCount     *int    `json:"attempt_count"`
	Source           *string `json:"source"`
	UpdateActivity   *bool   `json:"update_activity"`
}

type GetNDROrdersRequest struct {
	Offset         int      `json:"offset"`
	Limit          int      `json:"limit"`
	Seller         []string `json:"seller"`
	AssignedTo     []string `json:"assigned_to"`
	ShowAll        bool     `json:"show_all"`
	CallConnected  string   `json:"call_connected"`
	NextActionDate []string `json:"next_action_date"`
}

type NDRStatsCount struct {
	AssignedTo string `json:"assigned_to"`
	Count      int    `json:"count"`
}

type GetNDROrdersResponse struct {
	Stats map[string]int   `json:"stats"`
	Data  []NDROrderDetail `json:"data,omitempty"`
}

type NDROrderDetail struct {
	OrderID                   int64      `json:"order_id"`
	OrderActivityID           string     `json:"order_activity_id"`
	OrderDate                 string     `json:"order_date"`
	UserID                    string     `json:"user_id"`
	CustomerName              string     `json:"customer_name"`
	OrderDetails              *[]byte    `json:"order_details,omitempty"`
	CustomerPhone             string     `json:"customer_phone"`
	UpdatedAt                 int64      `json:"updated_at"`
	UpdatedBy                 string     `json:"updated_by"`
	AssignedTo                string     `json:"assigned_to"`
	OrderValue                float64    `json:"order_value"`
	Seller                    string     `json:"seller"`
	OrderStatus               string     `json:"order_status"`
	CallConnected             *string    `json:"call_connected"`
	ActionStatus              *string    `json:"action_status"`
	NextActionAt              *time.Time `json:"next_action_at"`
	AttemptCount              *int       `json:"attempt_count,omitempty"`
	NDRTag                    *string    `json:"ndr_tag"`
	TrackingLink              *string    `json:"tracking_link"`
	TotalPlacedOrderAmount    *float64   `json:"total_placed_order_amount"`
	TotalConfirmedOrderAmount *float64   `json:"total_confirmed_order_amount"`
	TotalDeliveredOrderAmount *float64   `json:"total_delivered_order_amount"`
	TotalReturnedOrderAmount  *float64   `json:"total_returned_order_amount"`
	TotalCancelledOrderAmount *float64   `json:"total_cancelled_order_amount"`
	TotalOrderPlaced          *int64     `json:"total_order_placed"`
	TotalOrderConfirmed       *int64     `json:"total_order_confirmed"`
	TotalOrderDelivered       *int64     `json:"total_order_delivered"`
	TotalOrderReturned        *int64     `json:"total_order_returned"`
	TotalOrderCancelled       *int64     `json:"total_order_cancelled"`
	EscalatedWith3Party       *bool      `json:"escalated_with_third_party" gorm:"column:escalated_with_third_party"`
	EscalatedWith3PL          *string    `json:"escalated_with_3pl" gorm:"column:escalated_with_3pl"`
	Note                      *string    `json:"note"`
	NDRUserReason             *string    `json:"ndr_user_reason"`
	AddressName               string     `json:"address_name"`
	AddressCity               string     `json:"address_city"`
	AddressLine               string     `json:"address_line"`
	PostalCode                string     `json:"postal_code"`
	State                     string     `json:"state"`
	GST                       string     `json:"gst"`
	AddressID                 float64    `json:"address_id"`
	StoreName                 string     `json:"store_name"`
	HouseNumber               string     `json:"house_number"`
	Neighbourhood             string     `json:"neighbourhood"`
	Village                   string     `json:"village"`
	Landmark                  string     `json:"landmark"`
	AddressTag                string     `json:"address_tag"`
	AddressLine1              string     `json:"address_line1"`
	AddressLine2              string     `json:"address_line2"`
	Phone                     string     `json:"phone"`
	AlternatePhone            string     `json:"alternate_phone"`
	ReAttempt                 bool       `json:"reattempt"`
}

type GetOrderActivityLogsRequest struct {
	Data struct {
		OrderActivityID string `json:"order_activity_id"`
	} `json:"data"`
}

type GetOrderActivityLogsResponse struct {
	OrderActivityID string                   `json:"order_activity_id"`
	Activity        []dao.OrderAction        `json:"activity,omitempty"`
	Note            []map[string]interface{} `json:"note,omitempty"`
}

type GetNDROrdersRequestV2 struct {
	Data GetNDROrdersRequestV2Data `json:"data"`
}

type GetNDROrdersRequestV2Data struct {
	Action        string    `json:"action"`
	CallConnected *string   `json:"call_connected"`
	Seller        []string  `json:"seller"`
	AssignedTo    []string  `json:"assigned_to"`
	AttemptCount  *int      `json:"attempt_count"`
	NDRStage      string    `json:"ndr_stage"`
	Limit         int       `json:"limit"`
	Offset        int       `json:"offset"`
	CFAction      *[]string `json:"cf_action"`
	LMAction      *[]string `json:"lm_action"`
}

type GetNDROrdersResponseV2 struct {
	Stats map[string]interface{} `json:"stats"`
	Data  []NDROrderDetails      `json:"data"`
}

type NDROrderDetails struct {
	OrderID                   int64    `json:"order_id"`
	OrderActionID             string   `json:"order_action_id"`
	OrderDate                 string   `json:"order_date"`
	OrderStatus               string   `json:"order_status"`
	DisplayStatus             string   `json:"display_status"`
	CreatedAt                 int64    `json:"created_at"`
	Seller                    string   `json:"seller"`
	NSLCode                   string   `json:"nsl_code"`
	Priority                  string   `json:"priority"`
	Courier                   string   `json:"courier"`
	AWBNumber                 string   `json:"awb_number"`
	OrderValue                float64  `json:"order_value"`
	PaymentStatus             string   `json:"payment_status"`
	OrderDetails              *[]byte  `json:"order_details,omitempty"`
	UserID                    string   `json:"user_id"`
	CFAction                  string   `json:"cf_action,omitempty"`
	CFCallConnected           string   `json:"cf_call_connected,omitempty"`
	CFNDRUserReason           string   `json:"cf_ndr_user_reason,omitempty" gorm:"column:cf_ndr_user_reason;type:varchar(100)"`
	CFNextAttemptAt           int64    `json:"cf_next_attempt_at,omitempty"`
	NextAttemptAt             string   `json:"next_attempt_at,omitempty"`
	CFNote                    string   `json:"cf_note,omitempty"`
	CFUpdatedBy               string   `json:"cf_updated_by,omitempty"`
	CFAssignedTo              string   `json:"cf_assigned_to,omitempty"`
	LMAction                  string   `json:"lm_action,omitempty"`
	LMNote                    string   `json:"lm_note,omitempty"`
	NDRAgentReason            string   `json:"ndr_agent_reason,omitempty"`
	LMUpdatedBy               string   `json:"lm_updated_by,omitempty"`
	LMUpdatedAt               int64    `json:"lm_updated_at,omitempty"`
	CFUpdatedAt               int64    `json:"cf_updated_at,omitempty"`
	NDRAttemptCount           *int     `json:"ndr_attempt_count,omitempty"`
	Source                    string   `json:"source,omitempty"`
	CreatedSource             string   `json:"created_source,omitempty"`
	Status                    string   `json:"status,omitempty"` // open, closed
	CustomerEscalated         bool     `json:"customer_escalated"`
	CustomerName              string   `json:"customer_name"`
	CustomerPhone             string   `json:"customer_phone"`
	AddressName               string   `json:"address_name"`
	AddressCity               string   `json:"address_city"`
	AddressLine               string   `json:"address_line"`
	PostalCode                string   `json:"postal_code"`
	State                     string   `json:"state"`
	GST                       string   `json:"gst"`
	AddressID                 float64  `json:"address_id"`
	StoreName                 string   `json:"store_name"`
	HouseNumber               string   `json:"house_number"`
	Neighbourhood             string   `json:"neighbourhood"`
	Village                   string   `json:"village"`
	Landmark                  string   `json:"landmark"`
	AddressTag                string   `json:"address_tag"`
	AddressLine1              string   `json:"address_line1"`
	AddressLine2              string   `json:"address_line2"`
	Phone                     string   `json:"phone"`
	AlternatePhone            string   `json:"alternate_phone"`
	ReAttempt                 bool     `json:"reattempt"`
	ActionStatus              string   `json:"action_status,omitempty"`
	TotalPlacedOrderAmount    *float64 `json:"total_placed_order_amount"`
	TotalConfirmedOrderAmount *float64 `json:"total_confirmed_order_amount"`
	TotalDeliveredOrderAmount *float64 `json:"total_delivered_order_amount"`
	TotalReturnedOrderAmount  *float64 `json:"total_returned_order_amount"`
	TotalCancelledOrderAmount *float64 `json:"total_cancelled_order_amount"`
	TotalOrderPlaced          *int64   `json:"total_order_placed"`
	TotalOrderConfirmed       *int64   `json:"total_order_confirmed"`
	TotalOrderDelivered       *int64   `json:"total_order_delivered"`
	TotalOrderReturned        *int64   `json:"total_order_returned"`
	TotalOrderCancelled       *int64   `json:"total_order_cancelled"`
}

type UpdateNDROrderRequestV2 struct {
	Data UpdateNDROrderRequestV2Data `json:"data"`
}

type UpdateNDROrderRequestV2Data struct {
	OrderID           string  `json:"order_id"`
	UpdatedAt         int64   `json:"updated_at"`
	UpdatedBy         string  `json:"updated_by"`
	NDRStage          string  `json:"ndr_stage"`
	OrderStatus       string  `json:"order_status"`
	CallConnected     *string `json:"call_connected"`
	Action            *string `json:"action"`
	NextActionAt      *string `json:"next_action_at"`
	Reason            *string `json:"reason"`
	Note              *string `json:"note"`
	ActionID          *string `json:"action_id"`
	NDRAgentReason    *string `json:"ndr_agent_reason"`
	AttemptCount      *int    `json:"attempt_count"`
	Source            *string `json:"source"`
	CustomerEscalated *bool   `json:"customer_escalated"`
}

type AddOrderActionRequest struct {
	OrderActionID    string   `json:"order_action_id"`
	ActionType       string   `json:"action_type"`
	OrderID          int      `json:"order_id"`
	OrderStatus      string   `json:"order_status"`
	UpdatedAt        int64    `json:"updated_at"`
	UpdatedBy        string   `json:"updated_by"`
	CFAssignedTo     string   `json:"cf_assigned_to,omitempty"`
	LMAssignedTo     string   `json:"lm_assigned_to,omitempty"`
	CallConnected    *string  `json:"call_connected,omitempty"`
	CFAction         *string  `json:"action_status,omitempty"`
	LMAction         *string  `json:"lm_action,omitempty"`
	NextActionAt     *string  `json:"next_action_at,omitempty"`
	CFNote           *string  `json:"cf_note,omitempty"`
	LMNote           *string  `json:"lm_note,omitempty"`
	OrderClosingNote *string  `json:"order_closing_note,omitempty"`
	ReturnReason     *string  `json:"returned_reason,omitempty"`
	CancelReason     *string  `json:"cancel_reason,omitempty"`
	NDRAgentReason   *string  `json:"ndr_agent_reason,omitempty"`
	NDRUserReason    *string  `json:"ndr_user_reason,omitempty"`
	OrderValue       *float64 `json:"order_value,omitempty"`
	AttemptCount     *int     `json:"attempt_count,omitempty"`
	Source           *string  `json:"source,omitempty"`
}

type EscalateTo3PLRequest struct {
	Data struct {
		OrderID   string  `json:"order_id"`
		UpdatedBy string  `json:"updated_by"`
		Action    string  `json:"action"`
		Note      *string `json:"note"`
		Source    *string `json:"source"`
	} `json:"data"`
}
