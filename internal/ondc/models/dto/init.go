package dto

import "encoding/json"

type AppInitReq struct {
	Data   AppInitRequestData `json:"data"`
	Meta   Meta               `json:"meta"`
	UserID string             `json:"user_id"`
}

type AppInitRequestData struct {
	Items     []OrderItemsInner `json:"items"`
	Provider  AppInitProvider   `json:"provider"`
	AddressID string            `json:"address_id"`
}

type AppInitProvider struct {
	ProviderID       string   `json:"provider_id"`
	ProviderTTL      string   `json:"provider_ttl"`
	ProviderLocation []string `json:"provider_locations"`
}

type AppInitResp struct {
	Meta             Meta             `json:"meta"`
	ProviderLocation string           `json:"provider_location"`
	Status           string           `json:"status"`           // Status of init, if the card was accepted or rejected
	Reason           string           `json:"reason,omitempty"` // If the status is rejected, the reason for rejection
	Error            AppResponseError `json:"error,omitempty"`
}

func (r *AppInitResp) MarshalBinary() (data []byte, err error) {
	bytes, err := json.Marshal(r)
	return bytes, err
}
