package dto

import (
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/shared"

	"gorm.io/datatypes"
)

type GetProductsReviewsRequest struct {
	Data   ProductReviewsRequest `json:"data"`
	Meta   Meta                  `json:"meta"`
	UserID string                `json:"user_id"`
}

type ProductReviewsRequest struct {
	CategoryID  *int64  `json:"category_id"`
	ProductID   *int64  `json:"product_id"`
	ProductName string  `json:"product_name"`
	Source      *string `json:"source"`
}

type GetProductReviewsResponse struct {
	Meta  Meta             `json:"meta"`
	Data  []UserReview     `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type ProductReviewsResponse struct {
	ReviewID      string         `json:"review_id"`
	ProductID     int64          `json:"product_id"`
	StarRating    int8           `json:"star_rating"`
	ReviewText    string         `json:"review_text"`
	ReviewMedia   datatypes.JSON `json:"review_media"`
	CreatedAtDate string         `json:"created_at_date"`
	Relevancy     int64          `json:"relevancy"`
	ReviewerInfo  ReviewerInfo   `json:"reviewer_info"`
}

type ReviewerInfo struct {
	ReviewerUserID    string `json:"user_id"`
	ReviewerUserName  string `json:"name"`
	ReviewerUserCity  string `json:"city"`
	ReviewerUserState string `json:"state"`
}

type AddProductReviewRequest struct {
	Data   AddProductReview `json:"data"`
	Meta   Meta             `json:"meta"`
	UserID string           `json:"user_id"`
}

type AddProductsReviewRequest struct {
	Data   []AddProductReview `json:"data"`
	Meta   Meta               `json:"meta"`
	UserID string             `json:"user_id"`
}

type AddProductReview struct {
	UserID       string            `json:"user_id"`
	Seller       string            `json:"seller"`
	Source       string            `json:"source"`
	ProductName  string            `json:"product_name"`
	ProductID    int64             `json:"product_id"`
	StarRating   int8              `json:"star_rating"`
	ReviewText   string            `json:"review_text"`
	ReviewMedia  []dao.ReviewMedia `json:"review_media"`
	ReviewSource string            `json:"review_source"`
}

type AddProductReviewResponse struct {
	Meta  Meta                   `json:"meta"`
	Data  AddProductReviewStatus `json:"data"`
	Error AppResponseError       `json:"error,omitempty"`
}

type AddProductReviewStatus struct {
	Status string `json:"status"`
}

type GetUserProductRatingRequest struct {
	Data   UserProductRatingRequest `json:"data"`
	Meta   Meta                     `json:"meta"`
	UserID string                   `json:"user_id"`
}

type UserProductRatingRequest struct {
	CategoryID *int64 `json:"category_id"`
	ProductID  *int64 `json:"product_id"`
}

type GetUserProductRatingResponse struct {
	Meta  Meta                      `json:"meta"`
	Data  UserProductRatingResponse `json:"data"`
	Error AppResponseError          `json:"error,omitempty"`
}

type UserProductRatingResponse struct {
	UserID               string  `json:"user_id"`
	ProductID            *int64  `json:"product_id"`
	CategoryID           *int64  `json:"category_id,omitempty"`
	StarRating           float64 `json:"star_rating"`
	RatingsCount         int64   `json:"ratings_count_text"`
	IsProductRatedByUser bool    `json:"is_product_rated_by_user"`
}

type GetProductReviewRatingResponse struct {
	Meta  Meta                        `json:"meta"`
	Data  ProductReviewRatingResponse `json:"data"`
	Error AppResponseError            `json:"error,omitempty"`
}

type ProductReviewRatingResponse struct {
	ProductReviews         []UserReview              `json:"product_reviews"`
	ProductRatings         UserProductRatingResponse `json:"product_ratings"`
	TopProductReviewVideos []TopReviewVideo          `json:"top_product_review_videos"`
}

type GetTopProductReviewVideosResponse struct {
	Data  []TopReviewVideo `json:"data"`
	Meta  Meta             `json:"meta"`
	Error AppResponseError `json:"error,omitempty"`
}

type TopProductReviewVideosResponse struct {
	ProductID    int64        `json:"product_id"`
	ReviewID     string       `json:"review_id"`
	StarRating   float64      `json:"star_rating"`
	ReviewerInfo ReviewerInfo `json:"reviewer_info"`
	Nav          shared.Nav   `json:"nav"`
}

type ReviewVideoNavParams struct {
	Screen    string `json:"screen"`
	MediaType string `json:"media_type"`
	MediaURL  string `json:"media_url"`
}

type UpdateReviewMediaRequest struct {
	JobID         string `json:"job_id"`
	URL           string `json:"url"`
	StreamingURL  string `json:"streaming_url"`
	ThumbnailURL  string `json:"thumbnail_url"`
	GifURL        string `json:"gif_url"`
	HighResGIFURL string `json:"thumbnail_res_gif_url"`
}

type ProductReviewsWidgets struct {
	Meta  Meta             `json:"meta"`
	Data  WidgetsData      `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type WidgetsData struct {
	Widgets []Widget `json:"Widgets"`
}
