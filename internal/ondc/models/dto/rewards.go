package dto

type CheckUserRewardsRequest struct {
	UserID string `json:"user_id" binding:"required"`
	Meta   Meta   `json:"meta" binding:"required"`
}

type CheckUserRewardsResponse struct {
	ShowRewards     bool                   `json:"show_rewards"`
	Products        []RewardProductDetails `json:"products"`
	RewardClaimType string                 `json:"reward_claim_type"`
	UserAddress     GetUserAddressResponse `json:"user_address,omitempty"`
}

type RewardProductDetails struct {
	ProductId    uint   `json:"product_id"`
	ProductName  string `json:"product_name"`
	ProductImage string `json:"product_image"`
}
