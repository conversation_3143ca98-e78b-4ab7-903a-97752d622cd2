package dto

// Response represents the structure of the JSON response
type GeoLocationResponse struct {
	Status string `json:"status"`
	Data   Data   `json:"data"`
}

// Data represents the "data" field in the JSON response
type Data struct {
	City        string `json:"city"`
	Cluster     string `json:"cluster"`
	District    string `json:"district"`
	Level       string `json:"level"`
	Pincode     string `json:"pincode"`
	State       string `json:"state"`
	SubDistrict string `json:"sub_district"`
	CityCode    string `json:"city_code"`
	CountryCode string `json:"country_code"`
	StdCode     string `json:"std_code"`
}

type UserGeoLocationResponse struct {
	Status string              `json:"status"`
	Data   UserGeoLocationData `json:"data"`
}

type UserGeoLocationData struct {
	UserID               string  `json:"user_id"`
	Latitude             float64 `json:"latitude"`
	Longitude            float64 `json:"longitude"`
	GeoCity              string  `json:"geo_city"`
	GeoSubDistrict       string  `json:"geo_sub_district"`
	GeoDistrict          string  `json:"geo_district"`
	GeoState             string  `json:"geo_state"`
	GeoPincode           int     `json:"geo_pincode"`
	GeoCodedCluster      string  `json:"geo_coded_cluster"`
	GeoLevel             string  `json:"geo_level"`
	GeoPopulationDensity string  `json:"geo_population_density"`
	UserProfileCity      string  `json:"user_profile_city"`
	UserProfileState     string  `json:"user_profile_state"`
	H3Res4               string  `json:"h3_res4"`
	H3Res5               string  `json:"h3_res5"`
	H3Res6               string  `json:"h3_res6"`
}
