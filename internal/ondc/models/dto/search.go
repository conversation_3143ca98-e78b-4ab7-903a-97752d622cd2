package dto

import (
	"encoding/json"
	"kc/internal/ondc/models/shared"
)

type AppSearchRequest struct {
	Data   AppSearchRequestData `json:"data"`
	UserID string               `json:"user_id"`
	Meta   Meta                 `json:"meta"`
}

type AppSearchRequestData struct {
	Query         string                 `json:"query"`
	CategoryID    string                 `json:"category_id"`
	Source        *string                `json:"source"`
	Seller        string                 `json:"seller"`
	ScreenTag     *string                `json:"screen_tag"`
	Latitude      float64                `json:"latitude"`
	Longitude     float64                `json:"longitude"`
	Internal      bool                   `json:"internal"`
	Top           *bool                  `json:"top"`
	Limit         *int                   `json:"limit"`
	Offset        *int                   `json:"offset"`
	PostalCode    string                 `json:"postal_code"`
	Conditions    *TopProductsConditions `json:"-"`
	TopProductIds []string               `json:"top_product_ids,omitempty"`
	Exclusive     *bool                  `json:"exclusive,omitempty"`
}

type SearchResponseSellers struct {
	Name        string               `json:"name"`
	ProviderID  string               `json:"provider_id"`
	ProviderTTL string               `json:"provider_ttl"`
	Type        string               `json:"type"`
	Address     string               `json:"address"`
	ImageURL    string               `json:"imageURL"`
	BppID       string               `json:"bppID"`
	BppURL      string               `json:"bppURL"`
	Items       []shared.SellerItems `json:"items"`
	RawResp     string               `json:"rawResp"`
	LocationID  string               `json:"location_id,omitempty"`
}

type AppSearchResponses struct {
	Meta   Meta             `json:"meta"`
	Data   AppSearchData    `json:"data"`
	Error  AppResponseError `json:"error,omitempty"`
	NavObj *shared.Nav      `json:"nav_obj,omitempty"`
}

type AppSearchData struct {
	Sellers      []*SearchResponseSellers `json:"sellers"`
	Items        interface{}              `json:"items"`
	FloatingData []map[string]interface{} `json:"floating_data"`
}

func (r AppSearchResponses) MarshalBinary() (data []byte, err error) {
	bytes, err := json.Marshal(r)
	return bytes, err
}

type AppProductsSearchData struct {
	Widgets []Widget `json:"widgets"`
}

type AppProductsSearchResponses struct {
	Meta  Meta                  `json:"meta"`
	Data  AppProductsSearchData `json:"data"`
	Error AppResponseError      `json:"error,omitempty"`
}

type ElasticCreateIndexRequest struct {
	Data ElasticCreateIndexData `json:"data"`
}

type ElasticCreateIndexData struct {
	IndexName         string `json:"index_name"`
	EnhancedIndexName string `json:"enhanced_index_name"`
}
