package dto

import "kc/internal/ondc/models/shared"

type TopNudge struct {
	Text    string `json:"text"`
	BgColor string `json:"bg_color"`
	Color   string `json:"color"`
}

type WidgetType37 struct {
	TopNudge            *TopNudge                `json:"top_nudge,omitempty"`
	MixPanelEventName   string                   `json:"mixpanel_event_name"`
	ID                  int64                    `json:"id"`
	CreatedBy           string                   `json:"created_by"`
	Expiry              int64                    `json:"expiry"`
	ExpiryTime          int64                    `json:"expiry_time"`
	Type                int8                     `json:"type"`
	WidgetInfo          WidgetInfo               `json:"widget_info"`
	IsActive            int                      `json:"is_active"`
	UpdatedAt           string                   `json:"updated_at"`
	VisibleFrom         int64                    `json:"visible_from"`
	BannerImageUrls     []shared.BannerImageUrls `json:"banner_image_urls"`
	ProfileImage        string                   `json:"profile_image"`
	Heading             string                   `json:"heading"`
	Icon                map[string]interface{}   `json:"icon"`
	SubHeading          string                   `json:"sub_heading"`
	MixpanelEventObject map[string]interface{}   `json:"mixpanel_event_object"`
	Description         string                   `json:"description"`
	PrimaryCta          *Cta                     `json:"primary_cta,omitempty"`
	SecondaryCta        *Cta                     `json:"secondary_cta,omitempty"`
}
