package dto

type RatingText struct {
	Text  string `json:"text"`
	Color string `json:"color"`
}

type InputField struct {
	Placeholder     string `json:"placeholder"`
	Multiline       bool   `json:"multiline"`
	BorderRadius    int    `json:"border_radius"`
	ContainerHeight int    `json:"container_height"`
	MaxLines        int    `json:"max_lines"`
	KeyboardType    string `json:"keyboard_type"`
}

type UploadPlaceholder struct {
	Text     string `json:"text"`
	ImageURL string `json:"image_url"`
}

type FeedbackSection struct {
	Title      string     `json:"title"`
	InputField InputField `json:"input_field"`
}

type MediaSection struct {
	Title             string            `json:"title"`
	UploadPlaceholder UploadPlaceholder `json:"upload_placeholder"`
}

type SubmittedSection struct {
	Text     string `json:"text"`
	ImageURL string `json:"image_url"`
}

type WidgetType34 struct {
	MixpanelEventName   string                 `json:"mixpanel_event_name"`
	MixpanelEventObject map[string]interface{} `json:"mixpanel_event_object"`
	ID                  int64                  `json:"id"`
	Heading             string                 `json:"heading"`
	CreatedBy           string                 `json:"created_by"`
	Expiry              int64                  `json:"expiry"`
	ExpiryTime          int64                  `json:"expiry_time"`
	Type                int8                   `json:"type"`
	Title               string                 `json:"title"`
	RatingTexts         map[string]RatingText  `json:"rating_texts"`
	FeedbackSection     FeedbackSection        `json:"feedback_section"`
	MediaSection        MediaSection           `json:"media_section"`
	SubmittedSection    SubmittedSection       `json:"submitted_section"`
	WidgetInfo          WidgetInfo             `json:"widget_info"`
	IsActive            int                    `json:"is_active"`
	UpdatedAt           string                 `json:"updated_at"`
	VisibleFrom         int64                  `json:"visible_from"`
	ProductId           int64                  `json:"product_id"`
	ProductName         string                 `json:"product_name"`
	Cta                 *Cta                   `json:"cta,omitempty"`
}
