package dto

import (
	"database/sql"
	"kc/internal/ondc/models/shared"
	"time"

	"gorm.io/datatypes"
)

type GetOrdersRequest struct {
	From        int64 `json:"from"`
	To          int64 `json:"to"`
	Limit       int   `json:"limit"`
	Offset      int   `json:"offset"`
	OrderAmount struct {
		Min float64 `json:"min"`
		Max float64 `json:"max"`
	} `json:"order_amount"`
	UserID              string   `json:"user_id"`
	Seller              []string `json:"seller"`
	Status              []string `json:"status"`
	ProcessingStatus    []string `json:"processing_status"`
	DeliveryStatus      []string `json:"delivery_status"`
	NotProcessingStatus []string `json:"not_processing_status"`
	NotDeliveryStatus   []string `json:"not_delivery_status"`
	RecoDays            int      `json:"reco_days"`
	RecoStatus          []string `json:"reco_status"`
	RequestSource       string   `json:"request_source"`
	Email               string   `json:"email"`
}

type GetOrdersExtendedRequest struct {
	From                int64       `json:"from"`
	To                  int64       `json:"to"`
	Limit               int         `json:"limit"`
	Offset              int         `json:"offset"`
	OrderAmount         OrderAmount `json:"order_amount"`
	UserID              string      `json:"user_id"`
	Seller              []string    `json:"seller"`
	Status              []string    `json:"status"`
	ProcessingStatus    []string    `json:"processing_status"`
	DeliveryStatus      []string    `json:"delivery_status"`
	NotProcessingStatus []string    `json:"not_processing_status"`
	NotDeliveryStatus   []string    `json:"not_delivery_status"`
	RecoDays            int         `json:"reco_days"`
	RecoStatus          string      `json:"reco_status"`
	Tag                 []TagFilter `json:"tag"`
	ExportCSV           bool        `json:"export_csv"`
	RequestSource       string      `json:"request_source"`
	Email               string      `json:"email"`
}

type TagFilter struct {
	Key  string `json:"key"`
	Type string `json:"type"`
}

type OrderAmount struct {
	Min float64 `json:"min"`
	Max float64 `json:"max"`
}

type User struct {
	ID           string `json:"id"`
	Name         string `json:"name,omitempty"`
	ShopName     string `json:"shop_name,omitempty"`
	District     string `json:"district,omitempty"`
	Cluster      string `json:"cluster,omitempty"`
	State        string `json:"state,omitempty"`
	Level        string `json:"level,omitempty"`
	Phone        string `json:"phone,omitempty"`
	ProfileImage string `json:"profile_image,omitempty"`
}

type OrderDetail struct {
	ID                   string                 `json:"id,omitempty"`
	User                 User                   `json:"user"`
	AddressName          string                 `json:"address_name"`
	AddressCity          string                 `json:"address_city"`
	AddressLine          string                 `json:"address_line"`
	PostalCode           string                 `json:"postal_code"`
	State                string                 `json:"state"`
	GST                  string                 `json:"gst"`
	AddressID            float64                `json:"address_id"`
	StoreName            string                 `json:"store_name"`
	HouseNumber          string                 `json:"house_number"`
	Neighbourhood        string                 `json:"neighbourhood"`
	Village              string                 `json:"village"`
	Landmark             string                 `json:"landmark"`
	AddressTag           string                 `json:"address_tag"`
	AddressLine1         string                 `json:"address_line1"`
	AddressLine2         string                 `json:"address_line2"`
	Phone                string                 `json:"phone"`
	AlternatePhone       string                 `json:"alternate_phone"`
	Total                float64                `json:"total"`
	OrderID              string                 `json:"order_id"`
	OrderDate            string                 `json:"order_date"`
	OrderStatus          string                 `json:"order_status"`
	PaymentStatus        string                 `json:"payment_status"`
	DeliveryStatus       string                 `json:"delivery_status"`
	DisplayStatus        string                 `json:"display_status"`
	ProcessingStatus     string                 `json:"processing_status"`
	Note                 string                 `json:"note"`
	CallStatus           string                 `json:"call_status"`
	UpdatedBy            string                 `json:"updated_by"`
	UpdatedAt            string                 `json:"updated_at"`
	CancelReason         string                 `json:"cancel_reason"`
	ReturnedReason       string                 `json:"returned_reason"`
	Seller               string                 `json:"seller"`
	PaidAmount           float64                `json:"paid_amount"`
	PaidAmountProof      []string               `json:"paid_amount_proof"`
	AdvanceTaken         bool                   `json:"advance_taken"`
	OrderPlaced          int64                  `json:"order_placed,omitempty"`
	OrderConfirmed       int64                  `json:"order_confirmed,omitempty"`
	OrderShipmentCreated int64                  `json:"order_shipment_created,omitempty"`
	OrderDelivered       int64                  `json:"order_delivered,omitempty"`
	OrderCancelled       int64                  `json:"order_cancelled,omitempty"`
	OrderReturned        int64                  `json:"order_returned,omitempty"`
	PrintingLabel        *string                `json:"printing_label,omitempty"`
	Picklist             *string                `json:"picklist,omitempty"`
	ExtInvoice           *string                `json:"ext_invoice,omitempty"`
	ExtInvoiceNumber     *string                `json:"ext_invoice_number,omitempty"`
	PushToOMS            *bool                  `json:"push_to_oms,omitempty"`
	StatusDescription    *string                `json:"status_description,omitempty"`
	ExpectedReturnAt     string                 `json:"expected_return_at,omitempty"`
	PackageDetails       *shared.PackageDetails `json:"package_details,omitempty"`
}

type GetOrdersResponse struct {
	Stats map[string]int `json:"stats"`
	Data  []OrderDetail  `json:"data,omitempty"`
}

type GetOrdersQueryResponse struct {
	ID                   string         `db:"id" json:"id"`
	UserID               string         `db:"user_id" json:"user_id"`
	OrderID              string         `db:"order_id" json:"order_id"`
	CreatedAt            time.Time      `db:"created_at" json:"created_at"`
	PaidAmount           float64        `db:"paid_amount" json:"paid_amount"`
	PaymentMeta          sql.NullString `db:"payment_meta" json:"payment_meta"`
	Seller               string         `db:"seller" json:"seller"`
	OrderStatus          string         `db:"order_status" json:"order_status"`
	PaymentStatus        string         `db:"payment_status" json:"payment_status"`
	DeliveryStatus       string         `db:"delivery_status" json:"delivery_status"`
	DisplayStatus        string         `db:"display_status" json:"display_status"`
	ProcessingStatus     string         `db:"processing_status" json:"processing_status"`
	OrderDetails         datatypes.JSON `db:"order_details" json:"order_details"`
	CallStatus           sql.NullString `db:"call_status" json:"call_status"`
	Note                 sql.NullString `db:"note" json:"note"`
	UpdatedAt            uint64         `db:"updated_at" json:"updated_at"`
	UpdatedBy            sql.NullString `db:"updated_by" json:"updated_by"`
	CancelReason         sql.NullString `db:"cancel_reason" json:"cancel_reason"`
	ReturnedReason       sql.NullString `db:"returned_reason" json:"returned_reason"`
	GeoDistrict          string         `db:"geo_district" json:"geo_district"`
	OrderPlaced          int64          `db:"order_placed" json:"order_placed"`
	OrderConfirmed       int64          `db:"order_confirmed" json:"order_confirmed"`
	OrderShipmentCreated int64          `db:"order_shipment_created" json:"order_shipment_created"`
	OrderDelivered       int64          `db:"order_delivered" json:"order_delivered"`
	OrderCancelled       int64          `db:"order_cancelled" json:"order_cancelled"`
	OrderReturned        int64          `db:"order_returned" json:"order_returned"`
	PrintingLabel        *string        `json:"printing_label,omitempty"`
	Picklist             *string        `json:"picklist,omitempty"`
	ExtInvoice           *string        `json:"ext_invoice,omitempty"`
	ExtInvoiceNumber     *string        `json:"ext_invoice_number,omitempty"`
	ExpectedReturnAt     int64          `db:"expected_return_at" json:"expected_return_at"`
}
