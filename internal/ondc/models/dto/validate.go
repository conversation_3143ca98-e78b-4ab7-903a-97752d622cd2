package dto

import (
	"regexp"

	validator "github.com/go-playground/validator/v10"
)

// Validator creates a new validator that support custom validation tags.
func Validator() *validator.Validate {
	validate := validator.New()
	validate.RegisterValidation("custom_decimal_value", isRegex(decimalValueRegex))
	validate.RegisterValidation("custom_gps", isRegex(gpsRegex))
	validate.RegisterValidation("custom_name", isRegex(nameRegex))
	return validate
}

// custom regex patterns from the ONDC API Specification
var (
	decimalValueRegex = regexp.MustCompile(`[+-]?([0-9]*[.])?[0-9]+`)
	gpsRegex          = regexp.MustCompile(`^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$`)
	nameRegex         = regexp.MustCompile(`^\./[^/]+/[^/]*/[^/]*/[^/]*/[^/]*/[^/]*$`)
)

func isRegex(regex *regexp.Regexp) validator.Func {
	return func(fl validator.FieldLevel) bool {
		return regex.MatchString(fl.Field().String())
	}
}
