package dto

import "kc/internal/ondc/models/shared"

type WidgetType39 struct {
	Id              int                    `json:"id"`
	BrandRating     float64                `json:"brand_rating"`
	Cta             Cta                    `json:"cta"`
	Data            []shared.SellerItems   `json:"data"`
	Description     string                 `json:"description"`
	DescriptionIcon map[string]interface{} `json:"description_icon"`
	ExpiryTime      int64                  `json:"expiry_time"`
	Heading         string                 `json:"heading"`
	HeadingIcon     map[string]interface{} `json:"heading_icon"`
	Type            int                    `json:"type"`
	Versions        string                 `json:"versions"`
	VisibleFrom     int64                  `json:"visible_from"`
	WidgetInfo      WidgetInfo             `json:"widget_info"`
}
