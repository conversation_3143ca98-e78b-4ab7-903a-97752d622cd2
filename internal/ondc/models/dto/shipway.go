package dto

type CreateShipwayOrderRequest struct {
	OrderID     string `json:"order_id"`
	ForceCreate bool   `json:"force_create"`
}

type CreateShipwayOrderResponse struct {
	Message        string `json:"message"`
	OrderID        string `json:"order_id"`
	ShipwayOrderID string `json:"shipway_order_id"`
	AwbNumber      string `json:"awb_number"`
	CurrentStatus  string `json:"current_status"`
	CourierName    string `json:"courier_name"`
	LogsID         uint64 `json:"logs_id"`
}

type ShipwayWebhookRequest struct {
	Hash       string       `json:"hash"`
	CarrierID  int          `json:"carrier_id"`
	StatusFeed []StatusFeed `json:"status_feed"`
}

type StatusFeed struct {
	Scans             []Scan      `json:"scans"`
	OrderID           string      `json:"order_id"`
	AWBNumber         string      `json:"awbno"`
	Phone             string      `json:"phone"`
	FirstName         string      `json:"first_name"`
	LastName          string      `json:"last_name"`
	Email             string      `json:"email"`
	CountryCode       string      `json:"country_code"`
	PickupDate        string      `json:"pickupdate"`
	CurrentStatus     string      `json:"current_status"`
	From              string      `json:"from"`
	To                string      `json:"to"`
	StatusTime        string      `json:"status_time"`
	OrderData         string      `json:"order_data"`
	Carrier           string      `json:"carrier"`
	CarrierID         int         `json:"carrier_id"`
	ExtraFields       ExtraFields `json:"extra_fields"`
	ReceivedBy        string      `json:"received_by"`
	CurrentStatusDesc string      `json:"current_status_desc"`
	TrackingURL       string      `json:"tracking_url"`
	CallbackURL       string      `json:"callback_url"`
	WebhookSentDate   string      `json:"webhook_sent_date"`
}

type Scan struct {
	Time     string `json:"time"`
	Status   string `json:"status"`
	Location string `json:"location"`
}

type ExtraFields struct {
	ExpectedDeliveryDate string `json:"expected_delivery_date"`
}

type ShipwayWebhookResponse struct {
	Message string `json:"message"`
}

type ShipwayOrderFetchResponse struct {
	Status   string                `json:"status"`
	Response ShipwayOrderFetchData `json:"response"`
}

type ShipwayOrderFetchData struct {
	To                string      `json:"to"`
	From              string      `json:"from"`
	Scan              []Scan      `json:"scan"`
	Time              string      `json:"time"`
	AWBNo             string      `json:"awbno"`
	Carrier           string      `json:"carrier"`
	OrderID           string      `json:"order_id"`
	Recipient         string      `json:"recipient"`
	OrderData         string      `json:"order_data"`
	PickupDate        string      `json:"pickupdate"`
	PickUpDate        string      `json:"pickup_date"`
	ExtraFields       ExtraFields `json:"extra_fields"`
	TrackingURL       string      `json:"tracking_url"`
	CurrentStatus     string      `json:"current_status"`
	CurrentStatusCode string      `json:"current_status_code"`
}

type ShipwayOrderFetchRequest struct {
	OrderID   string `json:"order_id"`
	KCOrderID int64  `json:"kc_order_id"`
}
