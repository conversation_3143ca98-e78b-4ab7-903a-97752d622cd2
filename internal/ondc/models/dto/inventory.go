package dto

type AddInventoryRequestData struct {
	ProductID    int64  `json:"product_id" validate:"required"`     // Product ID
	Quantity     int    `json:"quantity" validate:"required,min=1"` // Number of cartons being added
	Unit         string `json:"unit" validate:"required"`           // Unit of measurement e.g., "CASES", "PACKS", "PIECES"
	AdditionType string `json:"addition_type" validate:"required"`  // Type of addition NEW_STOCK OR RTO_DELIVERED OR INVENTORY_OVERRIDE
	Seller       string `json:"seller" validate:"required"`         // Seller ID
	Comment      string `json:"comment"`
}

// AddInventoryRequest represents the request structure for adding inventory
type AddInventoryRequest struct {
	UpdatedBy     string                   `json:"updated_by" validate:"required"` // User performing the update
	RequestSource string                   `json:"request_source"`
	Data          *AddInventoryRequestData `json:"data,omitempty"` // Use pointer to allow nil values
}

type AddInventoryResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"` // Use interface{} to allow flexible response data
	Error   string      `json:"error,omitempty"`
}
