package dto

import "time"

type ZohoCreateContactRequest struct {
	Zip            string `json:"zip"`
	LastName       string `json:"lastName"`
	Country        string `json:"country"`
	SecondaryEmail string `json:"secondaryEmail"`
	City           string `json:"city"`
	Facebook       string `json:"facebook"`
	Mobile         string `json:"mobile"`
	Description    string `json:"description"`
	OwnerID        string `json:"ownerId"`
	Type           string `json:"type"`
	Title          string `json:"title"`
	AccountID      string `json:"accountId"`
	FirstName      string `json:"firstName"`
	Twitter        string `json:"twitter"`
	Phone          string `json:"phone"`
	Street         string `json:"street"`
	State          string `json:"state"`
	Email          string `json:"email"`
	CF             struct {
		Email1      string `json:"cf_email1"`
		UserID      string `json:"cf_user_id"`
		Decimal1    string `json:"cf_decimal1"`
		Picklist1   string `json:"cf_picklist1"`
		SingleLine1 string `json:"cf_singleline1"`
	} `json:"cf"`
}

type ZohoCreateContactResponse struct {
	LastName          string  `json:"lastName"`
	ModifiedTime      string  `json:"modifiedTime"`
	Country           string  `json:"country"`
	SecondaryEmail    string  `json:"secondaryEmail"`
	City              string  `json:"city"`
	Description       string  `json:"description"`
	OwnerID           string  `json:"ownerId"`
	Type              string  `json:"type"`
	Title             string  `json:"title"`
	PhotoURL          *string `json:"photoURL"` // Use *string to allow null values
	Twitter           string  `json:"twitter"`
	IsDeleted         bool    `json:"isDeleted"`
	IsTrashed         bool    `json:"isTrashed"`
	Street            string  `json:"street"`
	CreatedTime       string  `json:"createdTime"`
	ZohoCRMContact    *string `json:"zohoCRMContact"`
	State             string  `json:"state"`
	ID                string  `json:"id"`
	CustomerHappiness struct {
		BadPercentage  string `json:"badPercentage"`
		OkPercentage   string `json:"okPercentage"`
		GoodPercentage string `json:"goodPercentage"`
	} `json:"customerHappiness"`
	Email string `json:"email"`
	Zip   string `json:"zip"`
	CF    struct {
		Email1      string `json:"cf_email1"`
		UserID      string `json:"cf_user_id"`
		Decimal1    string `json:"cf_decimal1"`
		Picklist1   string `json:"cf_picklist1"`
		SingleLine1 string `json:"cf_singleline1"`
	} `json:"cf"`
	IsFollowing interface{} `json:"isFollowing"`
	Facebook    string      `json:"facebook"`
	Mobile      string      `json:"mobile"`
	AccountID   string      `json:"accountId"`
	FirstName   string      `json:"firstName"`
	IsAnonymous bool        `json:"isAnonymous"`
	Phone       string      `json:"phone"`
	WebURL      string      `json:"webUrl"`
	IsSpam      bool        `json:"isSpam"`
}
type ZohoCreateTicketRequest struct {
	SubCategory    *string        `json:"subCategory,omitempty"`
	Cf             *CfFields      `json:"cf,omitempty"`
	Contact        *TicketContact `json:"contact,omitempty"`
	ContactId      *string        `json:"contactId,omitempty"`
	Subject        *string        `json:"subject,omitempty"`
	DueDate        string         `json:"dueDate,omitempty"`
	DepartmentId   *string        `json:"departmentId,omitempty"`
	Channel        *string        `json:"channel,omitempty"`
	Description    *string        `json:"description,omitempty"`
	Language       *string        `json:"language,omitempty"`
	Priority       *string        `json:"priority,omitempty"`
	Classification *string        `json:"classification,omitempty"`
	AssigneeId     *string        `json:"assigneeId,omitempty"`
	Phone          *string        `json:"phone,omitempty"`
	Category       *string        `json:"category,omitempty"`
	Status         *string        `json:"status,omitempty"`
}

type ZohoCreateTicketResponse struct {
	ModifiedTime         time.Time        `json:"modifiedTime"`
	SubCategory          string           `json:"subCategory"`
	StatusType           string           `json:"statusType"`
	Subject              string           `json:"subject"`
	DueDate              time.Time        `json:"dueDate"`
	DepartmentId         string           `json:"departmentId"`
	Channel              string           `json:"channel"`
	OnholdTime           *time.Time       `json:"onholdTime"`
	Language             string           `json:"language"`
	Source               ZohoTicketSource `json:"source"`
	Resolution           *string          `json:"resolution"`
	SharedDepartments    []string         `json:"sharedDepartments"`
	ClosedTime           *time.Time       `json:"closedTime"`
	ApprovalCount        string           `json:"approvalCount"`
	IsOverDue            bool             `json:"isOverDue"`
	IsTrashed            bool             `json:"isTrashed"`
	CreatedTime          time.Time        `json:"createdTime"`
	Id                   string           `json:"id"`
	IsResponseOverdue    bool             `json:"isResponseOverdue"`
	CustomerResponseTime time.Time        `json:"customerResponseTime"`
	ProductId            *string          `json:"productId"`
	ContactId            string           `json:"contactId"`
	ThreadCount          string           `json:"threadCount"`
	SecondaryContacts    []string         `json:"secondaryContacts"`
	Priority             string           `json:"priority"`
	Classification       string           `json:"classification"`
	CommentCount         string           `json:"commentCount"`
	TaskCount            string           `json:"taskCount"`
	AccountId            *string          `json:"accountId"`
	Phone                string           `json:"phone"`
	WebUrl               string           `json:"webUrl"`
	IsSpam               bool             `json:"isSpam"`
	Status               string           `json:"status"`
	EntitySkills         []string         `json:"entitySkills"`
	TicketNumber         string           `json:"ticketNumber"`
	CustomFields         CustomFields     `json:"customFields"`
	IsArchived           bool             `json:"isArchived"`
	Description          string           `json:"description"`
	TimeEntryCount       string           `json:"timeEntryCount"`
	ChannelRelatedInfo   *string          `json:"channelRelatedInfo"`
	ResponseDueDate      *time.Time       `json:"responseDueDate"`
	IsDeleted            bool             `json:"isDeleted"`
	ModifiedBy           string           `json:"modifiedBy"`
	Email                *string          `json:"email"`
	LayoutDetails        LayoutDetails    `json:"layoutDetails"`
	ChannelCode          *string          `json:"channelCode"`
	Cf                   CfFields         `json:"cf"`
	SlaId                string           `json:"slaId"`
	LayoutId             string           `json:"layoutId"`
	AssigneeId           string           `json:"assigneeId"`
	TeamId               *string          `json:"teamId"`
	AttachmentCount      string           `json:"attachmentCount"`
	IsEscalated          bool             `json:"isEscalated"`
	Category             string           `json:"category"`
}

type ZohoTicketSource struct {
	AppName     *string `json:"appName"`
	ExtId       *string `json:"extId"`
	Permalink   *string `json:"permalink"`
	Type        string  `json:"type"`
	AppPhotoURL *string `json:"appPhotoURL"`
}

type CustomFields struct {
	UserId  string `json:"User Id"`
	OrderId string `json:"Order Id"`
	Seller  string `json:"Seller"`
}

type CfFields struct {
	CfOrderId string `json:"cf_order_id"`
	CfSeller  string `json:"cf_seller"`
	CfUserId  string `json:"cf_user_id"`
}

type ZohoCreateCommentRequest struct {
	IsPublic      *string   `json:"isPublic,omitempty"`
	AttachmentIds *[]string `json:"attachmentIds,omitempty"`
	ContentType   *string   `json:"contentType,omitempty"`
	Content       *string   `json:"content,omitempty"`
}

type ZohoCreateCommentResponse struct {
	ID               string       `json:"id"`
	Content          string       `json:"content"`
	CommenterId      string       `json:"commenterId"`
	CommentedTime    time.Time    `json:"commentedTime"`
	ModifiedTime     time.Time    `json:"modifiedTime"`
	ContentType      string       `json:"contentType"`
	Commenter        Commenter    `json:"commenter"`
	ImpersonatedUser *string      `json:"impersonatedUser"`
	EncodedContent   string       `json:"encodedContent"`
	IsPublic         bool         `json:"isPublic"`
	Attachments      []Attachment `json:"attachments"`
}

type Commenter struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	Email     string `json:"email"`
	PhotoURL  string `json:"photoURL"`
	Type      string `json:"type"`
	FirstName string `json:"firstName"`
	LastName  string `json:"lastName"`
	RoleName  string `json:"roleName"`
}

type Attachment struct{}

// Contact struct to represent the contact field
type TicketContact struct {
	// Add appropriate fields if needed
}

// LayoutDetails struct to represent layoutDetails
type LayoutDetails struct {
	ID         string `json:"id"`
	LayoutName string `json:"layoutName"`
}

// CF struct for cf field
type CF struct {
	OrderID string `json:"cf_order_id"`
	Seller  string `json:"cf_seller"`
	UserID  string `json:"cf_user_id"`
}

// Event struct for the main JSON
type ZohoWebhookRequest struct {
	PrevState interface{} `json:"prevState"`
	Payload   interface{} `json:"payload"`
	EventTime string      `json:"eventTime"`
	EventType string      `json:"eventType"`
	OrgID     string      `json:"orgId"`
}

type ZohoWebhookResponse struct {
	Stauts string `json:"status"`
	Error  string `json:"error"`
}

type AddTicketEventPayload struct {
	ModifiedTime string  `json:"modifiedTime"`
	SubCategory  *string `json:"subCategory"`
	StatusType   string  `json:"statusType"`
	Subject      string  `json:"subject"`
	DepartmentId string  `json:"departmentId"`
	DueDate      *string `json:"dueDate"`
	Channel      string  `json:"channel"`
	OnholdTime   *string `json:"onholdTime"`
	Language     *string `json:"language"`
	Source       struct {
		AppName     *string `json:"appName"`
		ExtId       *string `json:"extId"`
		Permalink   *string `json:"permalink"`
		Type        string  `json:"type"`
		AppPhotoURL *string `json:"appPhotoURL"`
	} `json:"source"`
	Resolution        *string  `json:"resolution"`
	SharedDepartments []string `json:"sharedDepartments"`
	ClosedTime        *string  `json:"closedTime"`
	ApprovalCount     string   `json:"approvalCount"`
	IsOverDue         bool     `json:"isOverDue"`
	IsTrashed         bool     `json:"isTrashed"`
	Contact           struct {
		FirstName string  `json:"firstName"`
		LastName  string  `json:"lastName"`
		Phone     string  `json:"phone"`
		Mobile    *string `json:"mobile"`
		ID        string  `json:"id"`
		IsSpam    bool    `json:"isSpam"`
		Type      *string `json:"type"`
		Email     string  `json:"email"`
		Account   *string `json:"account"`
	} `json:"contact"`
	CreatedTime          string      `json:"createdTime"`
	ID                   string      `json:"id"`
	IsResponseOverdue    bool        `json:"isResponseOverdue"`
	FirstThread          interface{} `json:"firstThread"`
	CustomerResponseTime string      `json:"customerResponseTime"`
	ProductId            *string     `json:"productId"`
	ContactId            string      `json:"contactId"`
	ThreadCount          string      `json:"threadCount"`
	SecondaryContacts    []string    `json:"secondaryContacts"`
	Priority             *string     `json:"priority"`
	Classification       *string     `json:"classification"`
	CommentCount         string      `json:"commentCount"`
	AccountId            *string     `json:"accountId"`
	TaskCount            string      `json:"taskCount"`
	Phone                string      `json:"phone"`
	WebUrl               string      `json:"webUrl"`
	IsSpam               bool        `json:"isSpam"`
	Assignee             struct {
		PhotoURL  string `json:"photoURL"`
		FirstName string `json:"firstName"`
		LastName  string `json:"lastName"`
		ID        string `json:"id"`
		Email     string `json:"email"`
	} `json:"assignee"`
	LastActivityTime string   `json:"lastActivityTime"`
	Status           string   `json:"status"`
	EntitySkills     []string `json:"entitySkills"`
	TicketNumber     string   `json:"ticketNumber"`
	Sentiment        *string  `json:"sentiment"`
	CustomFields     struct {
		SeverityPercentage *string `json:"severityPercentage"`
		DateOfPurchase     *string `json:"dateofPurchase"`
		URL                *string `json:"url"`
	} `json:"customFields"`
	IsArchived         bool    `json:"isArchived"`
	Description        string  `json:"description"`
	TimeEntryCount     string  `json:"timeEntryCount"`
	ChannelRelatedInfo *string `json:"channelRelatedInfo"`
	ResponseDueDate    *string `json:"responseDueDate"`
	IsDeleted          bool    `json:"isDeleted"`
	ModifiedBy         string  `json:"modifiedBy"`
	FollowerCount      string  `json:"followerCount"`
	Email              string  `json:"email"`
	LayoutDetails      struct {
		ID         string `json:"id"`
		LayoutName string `json:"layoutName"`
	} `json:"layoutDetails"`
	ChannelCode *string `json:"channelCode"`
	CF          struct {
		CfURL             *string `json:"cf_url"`
		CfSeverityPercent *string `json:"cf_severityPercentage"`
		CfDateOfPurchase  *string `json:"cf_dateofPurchase"`
	} `json:"cf"`
	IsFollowing     bool    `json:"isFollowing"`
	AssigneeId      string  `json:"assigneeId"`
	LayoutId        string  `json:"layoutId"`
	CreatedBy       string  `json:"createdBy"`
	TeamId          *string `json:"teamId"`
	TagCount        string  `json:"tagCount"`
	IsEscalated     bool    `json:"isEscalated"`
	AttachmentCount string  `json:"attachmentCount"`
	Category        string  `json:"category"`
	DescAttachments []struct {
		Size string `json:"size"`
		Name string `json:"name"`
		Href string `json:"href"`
		ID   string `json:"id"`
	} `json:"descAttachments"`
}

type UpdateTicketEventPrevState struct {
	ModifiedTime string  `json:"modifiedTime"`
	SubCategory  *string `json:"subCategory"`
	StatusType   string  `json:"statusType"`
	Subject      string  `json:"subject"`
	DepartmentId string  `json:"departmentId"`
	DueDate      *string `json:"dueDate"`
	Channel      string  `json:"channel"`
	OnholdTime   *string `json:"onholdTime"`
	Language     *string `json:"language"`
	Source       struct {
		AppName     *string `json:"appName"`
		ExtId       *string `json:"extId"`
		Permalink   *string `json:"permalink"`
		Type        string  `json:"type"`
		AppPhotoURL *string `json:"appPhotoURL"`
	} `json:"source"`
	Resolution        *string  `json:"resolution"`
	SharedDepartments []string `json:"sharedDepartments"`
	ClosedTime        *string  `json:"closedTime"`
	ApprovalCount     string   `json:"approvalCount"`
	IsOverDue         bool     `json:"isOverDue"`
	IsTrashed         bool     `json:"isTrashed"`
	Contact           struct {
		FirstName string  `json:"firstName"`
		LastName  string  `json:"lastName"`
		Phone     string  `json:"phone"`
		Mobile    *string `json:"mobile"`
		ID        string  `json:"id"`
		IsSpam    bool    `json:"isSpam"`
		Type      *string `json:"type"`
		Email     string  `json:"email"`
		Account   *string `json:"account"`
	} `json:"contact"`
	CreatedTime          string      `json:"createdTime"`
	ID                   string      `json:"id"`
	IsResponseOverdue    bool        `json:"isResponseOverdue"`
	FirstThread          interface{} `json:"firstThread"`
	CustomerResponseTime string      `json:"customerResponseTime"`
	ProductId            *string     `json:"productId"`
	ContactId            string      `json:"contactId"`
	ThreadCount          string      `json:"threadCount"`
	SecondaryContacts    []string    `json:"secondaryContacts"`
	Priority             *string     `json:"priority"`
	Classification       *string     `json:"classification"`
	CommentCount         string      `json:"commentCount"`
	AccountId            *string     `json:"accountId"`
	TaskCount            string      `json:"taskCount"`
	Phone                string      `json:"phone"`
	WebUrl               string      `json:"webUrl"`
	IsSpam               bool        `json:"isSpam"`
	Assignee             struct {
		PhotoURL  string `json:"photoURL"`
		FirstName string `json:"firstName"`
		LastName  string `json:"lastName"`
		ID        string `json:"id"`
		Email     string `json:"email"`
	} `json:"assignee"`
	LastActivityTime string   `json:"lastActivityTime"`
	Status           string   `json:"status"`
	EntitySkills     []string `json:"entitySkills"`
	TicketNumber     string   `json:"ticketNumber"`
	Sentiment        *string  `json:"sentiment"`
	IsArchived       bool     `json:"isArchived"`
	CustomFields     struct {
		SeverityPercentage *string `json:"severityPercentage"`
		DateOfPurchase     *string `json:"dateofPurchase"`
		URL                *string `json:"url"`
	} `json:"customFields"`
	Description        *string `json:"description"`
	TimeEntryCount     string  `json:"timeEntryCount"`
	ChannelRelatedInfo *string `json:"channelRelatedInfo"`
	ResponseDueDate    *string `json:"responseDueDate"`
	IsDeleted          bool    `json:"isDeleted"`
	ModifiedBy         string  `json:"modifiedBy"`
	FollowerCount      string  `json:"followerCount"`
	Email              string  `json:"email"`
	LayoutDetails      struct {
		ID         string `json:"id"`
		LayoutName string `json:"layoutName"`
	} `json:"layoutDetails"`
	ChannelCode *string `json:"channelCode"`
	CF          struct {
		CfURL             *string `json:"cf_url"`
		CfSeverityPercent *string `json:"cf_severityPercentage"`
		CfDateOfPurchase  *string `json:"cf_dateofPurchase"`
	} `json:"cf"`
	IsFollowing     bool    `json:"isFollowing"`
	AssigneeId      string  `json:"assigneeId"`
	LayoutId        string  `json:"layoutId"`
	CreatedBy       string  `json:"createdBy"`
	TeamId          *string `json:"teamId"`
	TagCount        string  `json:"tagCount"`
	IsEscalated     bool    `json:"isEscalated"`
	AttachmentCount string  `json:"attachmentCount"`
	Category        string  `json:"category"`
	DescAttachments []struct {
		Size string `json:"size"`
		Name string `json:"name"`
		Href string `json:"href"`
		ID   string `json:"id"`
	} `json:"descAttachments"`
}

type UpdateTicketEventPayload struct {
	ModifiedTime string  `json:"modifiedTime"`
	SubCategory  *string `json:"subCategory"`
	StatusType   string  `json:"statusType"`
	Subject      string  `json:"subject"`
	DepartmentId string  `json:"departmentId"`
	DueDate      *string `json:"dueDate"`
	Channel      string  `json:"channel"`
	OnholdTime   *string `json:"onholdTime"`
	Language     *string `json:"language"`
	Source       struct {
		AppName     *string `json:"appName"`
		ExtId       *string `json:"extId"`
		Permalink   *string `json:"permalink"`
		Type        string  `json:"type"`
		AppPhotoURL *string `json:"appPhotoURL"`
	} `json:"source"`
	Resolution        *string  `json:"resolution"`
	SharedDepartments []string `json:"sharedDepartments"`
	ClosedTime        *string  `json:"closedTime"`
	ApprovalCount     string   `json:"approvalCount"`
	IsOverDue         bool     `json:"isOverDue"`
	IsTrashed         bool     `json:"isTrashed"`
	Contact           struct {
		FirstName string  `json:"firstName"`
		LastName  string  `json:"lastName"`
		Phone     string  `json:"phone"`
		Mobile    *string `json:"mobile"`
		ID        string  `json:"id"`
		IsSpam    bool    `json:"isSpam"`
		Type      *string `json:"type"`
		Email     string  `json:"email"`
		Account   *string `json:"account"`
	} `json:"contact"`
	CreatedTime          string      `json:"createdTime"`
	ID                   string      `json:"id"`
	IsResponseOverdue    bool        `json:"isResponseOverdue"`
	FirstThread          interface{} `json:"firstThread"`
	CustomerResponseTime string      `json:"customerResponseTime"`
	ProductId            *string     `json:"productId"`
	ContactId            string      `json:"contactId"`
	ThreadCount          string      `json:"threadCount"`
	SecondaryContacts    []string    `json:"secondaryContacts"`
	Priority             *string     `json:"priority"`
	Classification       *string     `json:"classification"`
	CommentCount         string      `json:"commentCount"`
	AccountId            *string     `json:"accountId"`
	TaskCount            string      `json:"taskCount"`
	Phone                string      `json:"phone"`
	WebUrl               string      `json:"webUrl"`
	IsSpam               bool        `json:"isSpam"`
	Assignee             struct {
		PhotoURL  string `json:"photoURL"`
		FirstName string `json:"firstName"`
		LastName  string `json:"lastName"`
		ID        string `json:"id"`
		Email     string `json:"email"`
	} `json:"assignee"`
	LastActivityTime string   `json:"lastActivityTime"`
	Status           string   `json:"status"`
	EntitySkills     []string `json:"entitySkills"`
	TicketNumber     string   `json:"ticketNumber"`
	Sentiment        *string  `json:"sentiment"`
	IsArchived       bool     `json:"isArchived"`
	CustomFields     struct {
		SeverityPercentage *string `json:"severityPercentage"`
		DateOfPurchase     *string `json:"dateofPurchase"`
		URL                *string `json:"url"`
		UserID             *string `json:"User Id"`
		OrderID            *string `json:"Order Id"`
		Seller             *string `json:"Seller"`
	} `json:"customFields"`
	Description        *string `json:"description"`
	TimeEntryCount     string  `json:"timeEntryCount"`
	ChannelRelatedInfo *string `json:"channelRelatedInfo"`
	ResponseDueDate    *string `json:"responseDueDate"`
	IsDeleted          bool    `json:"isDeleted"`
	ModifiedBy         string  `json:"modifiedBy"`
	FollowerCount      string  `json:"followerCount"`
	Email              string  `json:"email"`
	LayoutDetails      struct {
		ID         string `json:"id"`
		LayoutName string `json:"layoutName"`
	} `json:"layoutDetails"`
	ChannelCode *string `json:"channelCode"`
	CF          struct {
		CfURL             *string `json:"cf_url"`
		CfSeverityPercent *string `json:"cf_severityPercentage"`
		CfDateOfPurchase  *string `json:"cf_dateofPurchase"`
	} `json:"cf"`
	IsFollowing     bool    `json:"isFollowing"`
	AssigneeId      string  `json:"assigneeId"`
	LayoutId        string  `json:"layoutId"`
	CreatedBy       string  `json:"createdBy"`
	TeamId          *string `json:"teamId"`
	TagCount        string  `json:"tagCount"`
	IsEscalated     bool    `json:"isEscalated"`
	AttachmentCount string  `json:"attachmentCount"`
	Category        string  `json:"category"`
	DescAttachments []struct {
		Size string `json:"size"`
		Name string `json:"name"`
		Href string `json:"href"`
		ID   string `json:"id"`
	} `json:"descAttachments"`
}

type DeleteTicketEventPayload struct {
	ID string `json:"id"`
}

type TicketApprovalAddEventPayload struct {
	Approver struct {
		PhotoURL  *string `json:"photoURL"`
		FirstName string  `json:"firstName"`
		LastName  string  `json:"lastName"`
		ID        string  `json:"id"`
		Email     *string `json:"email"`
	} `json:"approver"`
	Requester struct {
		PhotoURL  *string `json:"photoURL"`
		FirstName string  `json:"firstName"`
		LastName  string  `json:"lastName"`
		ID        string  `json:"id"`
		Email     *string `json:"email"`
	} `json:"requester"`
	RequestedTime string `json:"requestedTime"`
	Subject       string `json:"subject"`
	Description   string `json:"description"`
	ID            string `json:"id"`
	Status        string `json:"status"`
	ProcessedTime string `json:"processedTime"`
}

type TicketApprovalUpdateEventPrevState struct {
	Approver struct {
		PhotoURL  *string `json:"photoURL"`
		FirstName string  `json:"firstName"`
		LastName  string  `json:"lastName"`
		ID        string  `json:"id"`
		Email     *string `json:"email"`
	} `json:"approver"`
	Requester struct {
		PhotoURL  *string `json:"photoURL"`
		FirstName string  `json:"firstName"`
		LastName  string  `json:"lastName"`
		ID        string  `json:"id"`
		Email     *string `json:"email"`
	} `json:"requester"`
	RequestedTime string  `json:"requestedTime"`
	Subject       string  `json:"subject"`
	Description   string  `json:"description"`
	ID            string  `json:"id"`
	Status        string  `json:"status"`
	ProcessedTime *string `json:"processedTime"`
}

type TicketApprovalUpdateEventPayload struct {
	Approver struct {
		PhotoURL  *string `json:"photoURL"`
		FirstName string  `json:"firstName"`
		LastName  string  `json:"lastName"`
		ID        string  `json:"id"`
		Email     *string `json:"email"`
	} `json:"approver"`
	Requester struct {
		PhotoURL  *string `json:"photoURL"`
		FirstName string  `json:"firstName"`
		LastName  string  `json:"lastName"`
		ID        string  `json:"id"`
		Email     *string `json:"email"`
	} `json:"requester"`
	RequestedTime string  `json:"requestedTime"`
	Subject       string  `json:"subject"`
	Description   string  `json:"description"`
	ID            string  `json:"id"`
	Status        string  `json:"status"`
	ProcessedTime *string `json:"processedTime"`
}

type TicketThreadAddEventPayload struct {
	IsDescriptionThread bool `json:"isDescriptionThread"`
	CanReply            bool `json:"canReply"`
	Attachments         []struct {
		PreviewURL string `json:"previewurl"`
		Size       string `json:"size"`
		Name       string `json:"name"`
		ID         string `json:"id"`
		Href       string `json:"href"`
	} `json:"attachments"`
	IsContentTruncated bool   `json:"isContentTruncated"`
	BCC                string `json:"bcc"`
	DepartmentID       string `json:"departmentId"`
	Channel            string `json:"channel"`
	Source             struct {
		AppName     string `json:"appName"`
		ExtID       string `json:"extId"`
		Permalink   string `json:"permalink"`
		Type        string `json:"type"`
		AppPhotoURL string `json:"appPhotoURL"`
	} `json:"source"`
	Content            string      `json:"content"`
	ChannelRelatedInfo interface{} `json:"channelRelatedInfo"`
	CreatedTime        string      `json:"createdTime"`
	ID                 string      `json:"id"`
	ContentType        string      `json:"contentType"`
	Direction          string      `json:"direction"`
	Summary            string      `json:"summary"`
	CC                 string      `json:"cc"`
	Visibility         string      `json:"visibility"`
	Author             struct {
		PhotoURL  string `json:"photoURL"`
		FirstName string `json:"firstName"`
		LastName  string `json:"lastName"`
		Name      string `json:"name"`
		ID        string `json:"id"`
		Type      string `json:"type"`
		Email     string `json:"email"`
	} `json:"author"`
	FullContentURL   interface{} `json:"fullContentURL"`
	IsForward        bool        `json:"isForward"`
	HasAttach        bool        `json:"hasAttach"`
	ResponderID      string      `json:"responderId"`
	ReplyTo          string      `json:"replyTo"`
	AttachmentCount  string      `json:"attachmentCount"`
	To               string      `json:"to"`
	FromEmailAddress string      `json:"fromEmailAddress"`
	TicketID         string      `json:"ticketId"`
	Status           string      `json:"status"`
}
type TicketCommentAddEventPayload struct {
	ModifiedTime string `json:"modifiedTime"`
	Attachments  []struct {
		Size string `json:"size"`
		Name string `json:"name"`
		ID   string `json:"id"`
		Href string `json:"href"`
	} `json:"attachments"`
	CommentedTime string `json:"commentedTime"`
	IsPublic      bool   `json:"isPublic"`
	ID            string `json:"id"`
	ContentType   string `json:"contentType"`
	Content       string `json:"content"`
	CommenterID   string `json:"commenterId"`
	TicketID      string `json:"ticketId"`
	Commenter     struct {
		PhotoURL  string `json:"photoURL"`
		FirstName string `json:"firstName"`
		LastName  string `json:"lastName"`
		Name      string `json:"name"`
		RoleName  string `json:"roleName"`
		ID        string `json:"id"`
		Type      string `json:"type"`
		Email     string `json:"email"`
	} `json:"commenter"`
}

type TicketCommentUpdateEventPayload struct {
	ModifiedTime string `json:"modifiedTime"`
	Attachments  []struct {
		Size string `json:"size"`
		Name string `json:"name"`
		ID   string `json:"id"`
		Href string `json:"href"`
	} `json:"attachments"`
	CommentedTime string `json:"commentedTime"`
	IsPublic      bool   `json:"isPublic"`
	ID            string `json:"id"`
	ContentType   string `json:"contentType"`
	Content       string `json:"content"`
	CommenterID   string `json:"commenterId"`
	TicketID      string `json:"ticketId"`
	Commenter     struct {
		PhotoURL  string `json:"photoURL"`
		FirstName string `json:"firstName"`
		LastName  string `json:"lastName"`
		Name      string `json:"name"`
		RoleName  string `json:"roleName"`
		ID        string `json:"id"`
		Type      string `json:"type"`
		Email     string `json:"email"`
	} `json:"commenter"`
}

type ContactAddEventPayload struct {
	LastName          string  `json:"lastName"`
	ModifiedTime      string  `json:"modifiedTime"`
	Country           *string `json:"country"`
	SecondaryEmail    *string `json:"secondaryEmail"`
	City              *string `json:"city"`
	Description       *string `json:"description"`
	Title             *string `json:"title"`
	OwnerId           string  `json:"ownerId"`
	Type              *string `json:"type"`
	PhotoURL          *string `json:"photoURL"`
	Twitter           *string `json:"twitter"`
	IsTrashed         bool    `json:"isTrashed"`
	IsDeleted         bool    `json:"isDeleted"`
	Street            *string `json:"street"`
	CreatedTime       string  `json:"createdTime"`
	IsEndUser         bool    `json:"isEndUser"`
	ZohoCRMContact    *string `json:"zohoCRMContact"`
	State             *string `json:"state"`
	ID                string  `json:"id"`
	CustomerHappiness struct {
		BadPercentage  string `json:"badPercentage"`
		OkPercentage   string `json:"okPercentage"`
		GoodPercentage string `json:"goodPercentage"`
	} `json:"customerHappiness"`
	Email         *string `json:"email"`
	LayoutDetails struct {
		ID         string `json:"id"`
		LayoutName string `json:"layoutName"`
	} `json:"layoutDetails"`
	Zip *string `json:"zip"`
	CF  struct {
		CFDecimal1    string `json:"cf_decimal1"`
		CFEmail1      string `json:"cf_email1"`
		CFPicklist1   string `json:"cf_picklist1"`
		CFSingleline1 string `json:"cf_singleline1"`
		CFUserID      string `json:"cf_user_id"`
	} `json:"cf"`
	IsFollowing      bool    `json:"isFollowing"`
	Facebook         *string `json:"facebook"`
	Mobile           *string `json:"mobile"`
	LayoutId         string  `json:"layoutId"`
	FirstName        *string `json:"firstName"`
	AccountId        *string `json:"accountId"`
	IsAnonymous      bool    `json:"isAnonymous"`
	Phone            *string `json:"phone"`
	WebURL           string  `json:"webUrl"`
	IsSpam           bool    `json:"isSpam"`
	LastActivityTime string  `json:"lastActivityTime"`
	Account          *string `json:"account"`
}

type ContactUpdateEventPrevState struct {
	LastName          string  `json:"lastName"`
	ModifiedTime      string  `json:"modifiedTime"`
	Country           *string `json:"country"`
	SecondaryEmail    *string `json:"secondaryEmail"`
	City              *string `json:"city"`
	Description       *string `json:"description"`
	Title             *string `json:"title"`
	OwnerId           string  `json:"ownerId"`
	Type              *string `json:"type"`
	PhotoURL          *string `json:"photoURL"`
	Twitter           *string `json:"twitter"`
	IsTrashed         bool    `json:"isTrashed"`
	IsDeleted         bool    `json:"isDeleted"`
	Street            *string `json:"street"`
	CreatedTime       string  `json:"createdTime"`
	IsEndUser         bool    `json:"isEndUser"`
	ZohoCRMContact    *string `json:"zohoCRMContact"`
	State             *string `json:"state"`
	ID                string  `json:"id"`
	CustomerHappiness struct {
		BadPercentage  string `json:"badPercentage"`
		OkPercentage   string `json:"okPercentage"`
		GoodPercentage string `json:"goodPercentage"`
	} `json:"customerHappiness"`
	Email         *string `json:"email"`
	LayoutDetails struct {
		ID         string `json:"id"`
		LayoutName string `json:"layoutName"`
	} `json:"layoutDetails"`
	Zip *string `json:"zip"`
	CF  struct {
		CFDecimal1    string `json:"cf_decimal1"`
		CFEmail1      string `json:"cf_email1"`
		CFPicklist1   string `json:"cf_picklist1"`
		CFSingleline1 string `json:"cf_singleline1"`
		CFUserID      string `json:"cf_user_id"`
	} `json:"cf"`
	IsFollowing      bool    `json:"isFollowing"`
	Facebook         *string `json:"facebook"`
	Mobile           *string `json:"mobile"`
	LayoutId         string  `json:"layoutId"`
	FirstName        *string `json:"firstName"`
	AccountId        *string `json:"accountId"`
	IsAnonymous      bool    `json:"isAnonymous"`
	Phone            *string `json:"phone"`
	WebURL           string  `json:"webUrl"`
	IsSpam           bool    `json:"isSpam"`
	LastActivityTime string  `json:"lastActivityTime"`
	Account          *string `json:"account"`
}

type ContactUpdateEventPayload struct {
	LastName          string  `json:"lastName"`
	ModifiedTime      string  `json:"modifiedTime"`
	Country           *string `json:"country"`
	SecondaryEmail    *string `json:"secondaryEmail"`
	City              *string `json:"city"`
	Description       *string `json:"description"`
	Title             *string `json:"title"`
	OwnerId           string  `json:"ownerId"`
	Type              *string `json:"type"`
	PhotoURL          *string `json:"photoURL"`
	Twitter           *string `json:"twitter"`
	IsTrashed         bool    `json:"isTrashed"`
	IsDeleted         bool    `json:"isDeleted"`
	Street            *string `json:"street"`
	CreatedTime       string  `json:"createdTime"`
	IsEndUser         bool    `json:"isEndUser"`
	ZohoCRMContact    *string `json:"zohoCRMContact"`
	State             *string `json:"state"`
	ID                string  `json:"id"`
	CustomerHappiness struct {
		BadPercentage  string `json:"badPercentage"`
		OkPercentage   string `json:"okPercentage"`
		GoodPercentage string `json:"goodPercentage"`
	} `json:"customerHappiness"`
	Email         *string `json:"email"`
	LayoutDetails struct {
		ID         string `json:"id"`
		LayoutName string `json:"layoutName"`
	} `json:"layoutDetails"`
	Zip *string `json:"zip"`
	CF  struct {
		CFDecimal1    string `json:"cf_decimal1"`
		CFEmail1      string `json:"cf_email1"`
		CFPicklist1   string `json:"cf_picklist1"`
		CFSingleline1 string `json:"cf_singleline1"`
		CFUserID      string `json:"cf_user_id"`
	} `json:"cf"`
	IsFollowing      bool    `json:"isFollowing"`
	Facebook         *string `json:"facebook"`
	Mobile           string  `json:"mobile"`
	LayoutId         string  `json:"layoutId"`
	FirstName        *string `json:"firstName"`
	AccountId        *string `json:"accountId"`
	IsAnonymous      bool    `json:"isAnonymous"`
	Phone            *string `json:"phone"`
	WebURL           string  `json:"webUrl"`
	IsSpam           bool    `json:"isSpam"`
	LastActivityTime string  `json:"lastActivityTime"`
	Account          *string `json:"account"`
}

type ContactDeleteEventPayload struct {
	ID string `json:"id"`
}

type AccountAddEventPayload struct {
}

type AccountUpdateEventPayload struct {
}

type AccountDeleteEventPayload struct {
}

type DepartmentAddEventPayload struct {
}

type DepartmentUpdateEventPayload struct {
}

type AgentAddEventPayload struct {
}

type AgentUpdateEventPayload struct {
}

type AgentDeleteEventPayload struct {
}

type AgentPresenseEventPayload struct {
}

type TicketAttachmentAddEventPayload struct {
	Size        string `json:"size"`
	IsTrashed   bool   `json:"isTrashed"`
	Name        string `json:"name"`
	CreatorId   string `json:"creatorId"`
	CreatedTime string `json:"createdTime"`
	IsPublic    bool   `json:"isPublic"`
	ID          string `json:"id"`
	Href        string `json:"href"`
	TicketId    string `json:"ticketId"`
}

type TicketAttachmentUpdateEventPayload struct {
	Size        string `json:"size"`
	IsTrashed   bool   `json:"isTrashed"`
	Name        string `json:"name"`
	CreatorId   string `json:"creatorId"`
	CreatedTime string `json:"createdTime"`
	IsPublic    bool   `json:"isPublic"`
	ID          string `json:"id"`
	Href        string `json:"href"`
	TicketId    string `json:"ticketId"`
}

type TicketAttachmentDeleteEventPayload struct {
	ID       string `json:"id"`
	TicketID string `json:"ticketId"`
}

type TaskAddEventPayload struct {
}

type TaskUpdateEventPrevState struct {
	ModifiedTime string `json:"modifiedTime"`
	CF           struct {
		Purpose string `json:"cf_purpose"`
	} `json:"cf"`
	CustomFields struct {
		Purpose string `json:"purpose"`
	} `json:"customFields"`
	Subject       string  `json:"subject"`
	CompletedTime *string `json:"completedTime"` // Pointer to handle null
	DepartmentId  string  `json:"departmentId"`
	DueDate       *string `json:"dueDate"` // Pointer to handle null
	CreatorId     string  `json:"creatorId"`
	Description   string  `json:"description"`
	OwnerId       string  `json:"ownerId"`
	Priority      string  `json:"priority"`
	CreatedTime   string  `json:"createdTime"`
	ID            string  `json:"id"`
	TicketId      *string `json:"ticketId"` // Pointer to handle null
	Status        string  `json:"status"`
}

type TaskUpdateEventPayload struct {
	ModifiedTime string `json:"modifiedTime"`
	CF           struct {
		Purpose string `json:"cf_purpose"`
	} `json:"cf"`
	CustomFields struct {
		Purpose string `json:"purpose"`
	} `json:"customFields"`
	Subject       string  `json:"subject"`
	CompletedTime *string `json:"completedTime"` // Pointer to handle null
	DepartmentId  string  `json:"departmentId"`
	DueDate       *string `json:"dueDate"` // Pointer to handle null
	CreatorId     string  `json:"creatorId"`
	Description   string  `json:"description"`
	OwnerId       string  `json:"ownerId"`
	Priority      string  `json:"priority"`
	CreatedTime   string  `json:"createdTime"`
	ID            string  `json:"id"`
	TicketId      *string `json:"ticketId"` // Pointer to handle null
	Status        string  `json:"status"`
}

type TaskDeleteEventPayload struct {
	ID string `json:"id"`
}

type CallAddEventPayload struct {
	ModifiedTime string `json:"modifiedTime"`
	CF           struct {
		Type string `json:"cf_type"`
	} `json:"cf"`
	CustomFields struct {
		Type string `json:"type"`
	} `json:"customFields"`
	Subject       string  `json:"subject"`
	CompletedTime *string `json:"completedTime"` // Pointer to handle null
	DepartmentId  string  `json:"departmentId"`
	CreatorId     string  `json:"creatorId"`
	Description   string  `json:"description"`
	OwnerId       string  `json:"ownerId"`
	Priority      string  `json:"priority"`
	Duration      string  `json:"duration"`
	CreatedTime   string  `json:"createdTime"`
	StartTime     string  `json:"startTime"`
	ID            string  `json:"id"`
	TicketId      *string `json:"ticketId"` // Pointer to handle null
	Status        string  `json:"status"`
	Direction     string  `json:"direction"`
}

type CallUpdateEventPayload struct {
	ModifiedTime string `json:"modifiedTime"`
	CF           struct {
		Type string `json:"cf_type"`
	} `json:"cf"`
	CustomFields struct {
		Type string `json:"type"`
	} `json:"customFields"`
	Subject       string  `json:"subject"`
	CompletedTime *string `json:"completedTime"` // Pointer to handle null
	DepartmentId  string  `json:"departmentId"`
	CreatorId     string  `json:"creatorId"`
	Description   string  `json:"description"`
	OwnerId       string  `json:"ownerId"`
	Priority      string  `json:"priority"`
	Duration      string  `json:"duration"`
	CreatedTime   string  `json:"createdTime"`
	StartTime     string  `json:"startTime"`
	ID            string  `json:"id"`
	TicketId      *string `json:"ticketId"` // Pointer to handle null
	Status        string  `json:"status"`
	Direction     string  `json:"direction"`
}

type CallUpdateEventPrevState struct {
	ModifiedTime string `json:"modifiedTime"`
	CF           struct {
		Type string `json:"cf_type"`
	} `json:"cf"`
	CustomFields struct {
		Type string `json:"type"`
	} `json:"customFields"`
	Subject       string  `json:"subject"`
	CompletedTime *string `json:"completedTime"` // Pointer to handle null
	DepartmentId  string  `json:"departmentId"`
	DueDate       *string `json:"dueDate"` // Pointer to handle null
	CreatorId     string  `json:"creatorId"`
	Description   string  `json:"description"`
	OwnerId       string  `json:"ownerId"`
	Priority      string  `json:"priority"`
	Duration      string  `json:"duration"`
	CreatedTime   string  `json:"createdTime"`
	ID            string  `json:"id"`
	TicketId      *string `json:"ticketId"` // Pointer to handle null
	Status        string  `json:"status"`
	Direction     string  `json:"direction"`
}

type CallDeleteEventPayload struct {
	ID string `json:"id"`
}

type ZohoTicketAttachmentResponse struct {
	Size        string `json:"size"`
	CreatorID   string `json:"creatorId"`
	Name        string `json:"name"`
	CreatedTime string `json:"createdTime"`
	IsPublic    bool   `json:"isPublic"`
	ID          string `json:"id"`
	Href        string `json:"href"`
}

type AddTicketAttachmentRequest struct {
	URL       string `json:"url"`
	TicketID  string `json:"ticket_id"`
	MediaType string `json:"media_type"`
}
