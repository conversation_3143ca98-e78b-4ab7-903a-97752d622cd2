package dto

import "kc/internal/ondc/models/shared"

type AppGetKiranaBazarMetaRequest struct {
	UserID string `json:"user_id"`
	Meta   Meta   `json:"meta"`
}

type GetOrderingOnboardingRequest struct {
	UserID string `json:"user_id"`
	Meta   Meta   `json:"meta"`
}

type OnboardingScreen struct {
	ID     int     `json:"id"`
	Media  []Media `json:"media,omitempty"`  // Omitted if not present (as in screen 1)
	Footer *Footer `json:"footer,omitempty"` // Omitted if not present (as in screen 3)
	Config *Config `json:"config,omitempty"` // Omitted if not present (as in screen 1)
}

type Media struct {
	MediaType string      `json:"media_type"`
	URL       string      `json:"url"`
	Config    *Config     `json:"config,omitempty"` // Only present in one object
	Height    *int        `json:"height,omitempty"` // Only present in one object
	Nav       *shared.Nav `json:"nav,omitempty"`    // Only present in one object
}

type Config struct {
	GradientColors []string `json:"gradient_colors"`
}

type BottomComponent struct {
	PrimaryText   string                 `json:"primary_text"`
	SecondaryText string                 `json:"secondary_text"`
	ImageUrl      string                 `json:"image_url"`
	Styles        map[string]interface{} `json:"styles"`
}

type Footer struct {
	CTA             Cta              `json:"cta"`
	BottomComponent *BottomComponent `json:"bottom_component,omitempty"` // Omitted if not present (as in screen 1)
}

type OnboardingData struct {
	OnboardingScreens []OnboardingScreen `json:"onboarding_screens"`
	ProgressBar       struct {
		Color string `json:"color"`
		Text  string `json:"text"`
	} `json:"progress_bar"`
}
type GetOrderingOnboardingResponseData struct {
	OnboardingData OnboardingData `json:"onboarding_data"`
}

type GetOrderingOnboardingResponse struct {
	Meta Meta                              `json:"meta"`
	Data GetOrderingOnboardingResponseData `json:"data"`
}

type CartScreen struct {
	Rating          string                   `json:"rating"`
	BannerImageUrls []shared.BannerImageUrls `json:"banner_image_urls"`
	Config          map[string]interface{}   `json:"config"`
	Cta             Cta                      `json:"cta"`
}

type Seller struct {
	PrimaryColor           string                   `json:"primary_color"`
	BannerImageUrls        []shared.BannerImageUrls `json:"banner_image_urls"`
	BannersCarousel        interface{}              `json:"banners_carousel,omitempty"`
	CarouselHeight         *int                     `json:"carousel_height,omitempty"`
	Categories             []SearchCategory         `json:"categories"`
	CartScreenMeta         CartScreen               `json:"cart_screen_meta"`
	Logo                   string                   `json:"logo"`
	Name                   string                   `json:"name"`
	HeaderText             string                   `json:"header_text"`
	Seller                 string                   `json:"seller"`
	Screen                 string                   `json:"screen"`
	BrandID                string                   `json:"brand_id"`
	Source                 string                   `json:"source"`
	Nav                    *shared.Nav              `json:"nav,omitempty"`
	BrandCta               *Cta                     `json:"brand_cta,omitempty"`
	HelpCentreCta          *Cta                     `json:"help_centre_cta,omitempty"`
	SideDrawer             *SideDrawer              `json:"side_drawer,omitempty"`
	SearchTexts            []string                 `json:"search_texts,omitempty"`
	KcFullFilled           bool                     `json:"kc_fullfilled"`
	AddCashbackSourceId    int                      `json:"add_cashback_source_id"`
	RedeemCashbackSourceId int                      `json:"redeem_cashback_source_id"`
	BulkOrderForm          string                   `json:"bulk_order_form,omitempty"`
}

type Cta struct {
	Text              string      `json:"text"`
	MixpanelEventName string      `json:"mixpanel_event_name"`
	Icon              string      `json:"icon"`
	PrimaryColor      *string     `json:"primary_color,omitempty"`
	SecondaryColor    *string     `json:"secondary_color,omitempty"`
	BorderColor       *string     `json:"border_color,omitempty"`
	Nav               *shared.Nav `json:"nav,omitempty"`
}

type Header struct {
	AppVersion    string                 `json:"app_version"`
	Text          string                 `json:"text"`
	HideSearchBar bool                   `json:"hide_search_bar"`
	BackCtaNav    *shared.Nav            `json:"back_cta_nav,omitempty"`
	Cta           Cta                    `json:"cta"`
	CtaSecondary  *Cta                   `json:"cta_secondary,omitempty"`
	HelpCentreCta *Cta                   `json:"help_centre_cta,omitempty"`
	Config        map[string]interface{} `json:"config"`
	Widget        *Widget                `json:"widget,omitempty"`
}

type SideDrawerData struct {
	Cta
	IconSet string `json:"icon_set"`
}
type SideDrawer struct {
	List            []SideDrawerData         `json:"list"`
	BannerImageUrls []shared.BannerImageUrls `json:"banner_image_urls"`
}

type SyncCartSellerData struct {
	Seller string `json:"seller"`
	Source string `json:"source"`
}

type AppGetKiranaBazarMetaData struct {
	Widgets                []Widget                 `json:"widgets"`
	Header                 Header                   `json:"header"`
	SellersMeta            map[string]Seller        `json:"sellers_meta"`
	SideDrawer             SideDrawer               `json:"side_drawer"`
	Config                 map[string]interface{}   `json:"config"`
	SearchHistory          []string                 `json:"search_history,omitempty"`
	SearchTexts            []string                 `json:"search_texts,omitempty"`
	SyncCartSeller         *SyncCartSellerData      `json:"sync_cart_seller,omitempty"`
	OrderingBottomTabs     interface{}              `json:"ordering_bottom_tabs,omitempty"`
	FloatingData           []map[string]interface{} `json:"floating_data,omitempty"`
	ShowOrderingOnboarding *bool                    `json:"show_ordering_onboarding,omitempty"`
}

type AppGetKiranaBazarMetaResponse struct {
	Meta   Meta                      `json:"meta"`
	Data   AppGetKiranaBazarMetaData `json:"data"`
	Error  AppResponseError          `json:"error,omitempty"`
	NavObj *shared.Nav               `json:"nav_obj,omitempty"`
}

type CartScreenData struct {
	Widgets []Widget `json:"widgets"`
	Header  Header   `json:"header"`
}

type AppGetKiranaBazarCartScreenMetaResponse struct {
	Meta   Meta             `json:"meta"`
	Data   CartScreenData   `json:"data"`
	Error  AppResponseError `json:"error,omitempty"`
	NavObj *shared.Nav      `json:"nav_obj,omitempty"`
}

type Nudge struct {
	Id        string `json:"id"`
	Name      string `json:"name"`
	Shimmer   bool   `json:"shimmer"`
	Type      string `json:"type"`
	Text      string `json:"text"`
	TextColor string `json:"text_color"`
	Color     string `json:"color"`
	CreatedAt int64  `json:"created_at"`
	ExpiryAt  int64  `json:"expiry_at"`
}

type OrderingBottomTabData struct {
	HighlightedIconUrl string      `json:"highlightedIconUrl"`
	IconUrl            string      `json:"iconUrl"`
	Label              string      `json:"label"`
	NavObj             *shared.Nav `json:"navObj,omitempty"`
	Nudge              *Nudge      `json:"nudge,omitempty"`
	ScreenName         string      `json:"screenName"`
}

type OrderingBottomTab map[string]OrderingBottomTabData
