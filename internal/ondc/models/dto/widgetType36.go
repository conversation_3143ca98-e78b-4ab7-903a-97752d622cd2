package dto

import "gorm.io/datatypes"

type ReviewerLocation struct {
	City  string `json:"city"`
	State string `json:"state"`
}

type UserReview struct {
	MixPanelEventName string           `json:"MixPanelEventName"`
	ID                int64            `json:"id"`
	Heading           string           `json:"heading"`
	CreatedBy         string           `json:"created_by"`
	Expiry            int64            `json:"expiry"`
	ExpiryTime        int64            `json:"expiry_time"`
	Type              int              `json:"type"`
	UserName          string           `json:"user_name"`
	Location          ReviewerLocation `json:"location"`
	Rating            *int8            `json:"rating,omitempty"`
	PostDate          string           `json:"post_date"`
	ReviewText        string           `json:"review_text"`
	Media             datatypes.JSON   `json:"media"`
	ReviewID          string           `json:"review_id"`
	WidgetInfo        WidgetInfo       `json:"widget_info"`
	IsActive          int              `json:"is_active"`
	UpdatedAt         string           `json:"updated_at"`
	VisibleFrom       int64            `json:"visible_from"`
}
