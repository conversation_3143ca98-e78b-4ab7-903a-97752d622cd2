package dto

type SubmitDeliveryRatingRequest struct {
	OrderId int    `json:"order_id" binding:"required"`
	Rating  int    `json:"rating" binding:"required"`
	Reason  string `json:"reason"`
}

type GetDeliveryRatingRequest struct {
	OrderId int `json:"order_id" binding:"required"`
}

type GetDeliveryRatingResponse struct {
	OrderId        int  `json:"order_id"`
	RatingProvided bool `json:"rating_provided"`
}
