package dto

// OnSubscribeRequest - Request payload of /on_subscribe API
type OnSubscribeRequest struct {
	// A unique ID describing a subscriber on a network.
	SubscriberID string `json:"subscriber_id,omitempty"`

	// String encrypted using the subscriber’s old encryption public key
	Challenge string `json:"challenge,omitempty"`
}

// OnSubscribeResponse - Response payload of /on_subscribe API
type OnSubscribeResponse struct {
	// Decrypted value
	Answer string `json:"answer,omitempty"`
}
