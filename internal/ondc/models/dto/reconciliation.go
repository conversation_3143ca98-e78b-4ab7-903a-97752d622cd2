package dto

import (
	"errors"
	"time"
)

// ReconciliationRequest represents the parameters for a reconciliation query
type ReconciliationRequest struct {
	Seller        string        `json:"seller"`
	DateSelection DateSelection `json:"date_selection"`
	User          string        `json:"user"`
	JWT           string        `json:"jwt"`
	Metric        string        `json:"metric"`
}

// DateSelection represents the date filter options matching the UI
type DateSelection struct {
	// Custom date range using Unix timestamps (milliseconds since epoch)
	CustomRange *CustomDateRange `json:"custom_range,omitempty"`
	// Preset selection (only one can be true)
	IsToday      bool `json:"is_today,omitempty"`
	IsYesterday  bool `json:"is_yesterday,omitempty"`
	IsLast7Days  bool `json:"is_last_7_days,omitempty"`
	IsLast30Days bool `json:"is_last_30_days,omitempty"`
	IsLastMonth  bool `json:"is_last_month,omitempty"`
}

// CustomDateRange represents user-selected date range using Unix timestamps
type CustomDateRange struct {
	StartDate int64 `json:"start_date" validate:"required,min=0"` // Unix timestamp in milliseconds
	EndDate   int64 `json:"end_date" validate:"required,min=0"`   // Unix timestamp in milliseconds
}

var (
	ErrInvalidDateSelection = errors.New("exactly one date selection option must be chosen")
	ErrInvalidDateRange     = errors.New("end date must be after or equal to start date")
)

// Validate ensures only one date selection option is chosen and dates are valid
func (ds *DateSelection) Validate() error {
	// Count selected options
	selections := 0
	if ds.CustomRange != nil {
		selections++
		// Validate custom range timestamps
		if ds.CustomRange.EndDate < ds.CustomRange.StartDate {
			return ErrInvalidDateRange
		}
	}
	if ds.IsToday {
		selections++
	}
	if ds.IsYesterday {
		selections++
	}
	if ds.IsLast7Days {
		selections++
	}
	if ds.IsLast30Days {
		selections++
	}
	if ds.IsLastMonth {
		selections++
	}

	if selections != 1 {
		return ErrInvalidDateSelection
	}

	return nil
}

// GetDateRange returns the calculated start and end timestamps based on selection
func (ds *DateSelection) GetDateRange() (startMs, endMs int64) {
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	switch {
	case ds.CustomRange != nil:
		return ds.CustomRange.StartDate, ds.CustomRange.EndDate

	case ds.IsToday:
		return timeToMs(today), timeToMs(now)

	case ds.IsYesterday:
		yesterday := today.AddDate(0, 0, -1)
		return timeToMs(yesterday), timeToMs(yesterday.Add(23*time.Hour + 59*time.Minute + 59*time.Second))

	case ds.IsLast7Days:
		return timeToMs(today.AddDate(0, 0, -7)), timeToMs(now)

	case ds.IsLast30Days:
		return timeToMs(today.AddDate(0, 0, -30)), timeToMs(now)

	case ds.IsLastMonth:
		firstOfLastMonth := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, now.Location())
		lastOfLastMonth := time.Date(now.Year(), now.Month(), 0, 23, 59, 59, 0, now.Location())
		return timeToMs(firstOfLastMonth), timeToMs(lastOfLastMonth)

	default:
		return timeToMs(now), timeToMs(now)
	}
}

// Helper function to convert time.Time to Unix milliseconds
func timeToMs(t time.Time) int64 {
	return t.UnixNano() / int64(time.Millisecond)
}

// Helper function to convert Unix milliseconds to time.Time
func msToTime(ms int64) time.Time {
	return time.Unix(0, ms*int64(time.Millisecond))
}

type ReconciliationResponse struct {
	Type          int                          `json:"type"`
	Metric        string                       `json:"metric"`
	Label         string                       `json:"label"`
	Description   string                       `json:"description"`
	Data          []ReconciliationResponseData `json:"data"`
	DateSelection DateSelection                `json:"date_selection" validate:"required"`
}

type ReconciliationResponseData struct {
	Seller      *string                      `json:"seller"`
	Change      string                       `json:"change"`
	ChangeColor string                       `json:"change_color"`
	Items       []ReconciliationResponseItem `json:"items"`
	Graph       GraphData                    `json:"graph"`
	Table       *TableData                   `json:"table_data,omitempty"`
	OnHoverText string                       `json:"on_hover_text"`
}

type TableData struct {
	Type    int        `json:"type"`
	Columns []string   `json:"columns"`
	Data    [][]string `json:"data"`
}

type GraphData struct {
	Points []Point `json:"point"`
	XAxis  string  `json:"x_axis"`
	YAxis  string  `json:"y_axis"`
	ZAxis  string  `json:"z_axis"`
}

type Point struct {
	X string `json:"x"`
	Y string `json:"y"`
	Z string `json:"z"`
}

type ReconciliationResponseItem struct {
	Count string `json:"count"`
	Value string `json:"value"`
}

type StatusTimeStamp struct {
	TimeStamp   int64  `json:"time_stamp"`
	OrderStatus string `json:"order_status"`
}
type AddReconciliationRequest struct {
	OrderID       int64             `json:"order_id"`
	Data          []StatusTimeStamp `json:"data"`
	Service       string            `json:"service"`
	OMS           string            `json:"oms,omitempty"`
	DiscountValue *float64          `json:"discount_value,omitempty"`
	GrossValue    *float64          `json:"gross_value,omitempty"`
	CartValue     *float64          `json:"cart_value,omitempty"`
	OrderValue    *float64          `json:"order_value,omitempty"`
	GTV           *float64          `json:"gtv,omitempty"`
}

type AddReconciliationResponse struct {
	Status            string           `json:"status"`
	Message           string           `json:"message"`
	Error             AppResponseError `json:"error,omitempty"`
	UpdatedMetrics    []string         `json:"updated_metrics"`
	NonUpdatedMetrics []string         `json:"non_updated_metrics"`
}

type GetReconciliationRequest struct {
	OrderID string `json:"order_id"`
}

type KiranaBazarReconciliation struct {
	OrderID                   int64   `json:"order_id"`
	OMS                       string  `json:"oms"`
	GrossValue                float64 `json:"gross_value"`
	CartValue                 float64 `json:"cart_value"`
	GTV                       float64 `json:"gtv"`
	OrderValue                float64 `json:"order_value"`
	OrderPlaced               int64   `json:"order_placed"`
	OrderUpdated              *int64  `json:"order_updated"`                    // nullable
	OrderConfirmed            *int64  `json:"order_confirmed,omitempty"`        // nullable
	OrderDelivered            *int64  `json:"order_delivered,omitempty"`        // nullable
	OrderShipmentCreated      *int64  `json:"order_shipment_created,omitempty"` // nullable
	OrderReturned             *int64  `json:"order_returned,omitempty"`         // nullable
	OrderDispatched           *int64  `json:"order_dispatched,omitempty"`
	OrderOfd                  *int64  `json:"order_ofd,omitempty"`
	ExpectedDeliveryTimestamp int64   `json:"expected_delivery_timestamp"`
	CreatedAt                 int64   `json:"created_at"`
	UpdatedAt                 int64   `json:"updated_at"`
	OrderCancelled            *int64  `json:"order_cancelled,omitempty"` // nullable
	DiscountValue             float64 `json:"discount_value"`
}

type GetReconciliationResponse struct {
	Status  string                    `json:"status"`
	Message string                    `json:"message"`
	Data    KiranaBazarReconciliation `json:"data"`
	Error   AppResponseError          `json:"error,omitempty"`
}
