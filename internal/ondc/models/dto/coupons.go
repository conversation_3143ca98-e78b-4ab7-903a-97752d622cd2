package dto

type CouponEditRequest struct {
	Data struct {
		ID             int64     `json:"id"`
		Description    *string   `json:"description"`
		IsActive       *bool     `json:"is_active"`
		IsInternal     *bool     `json:"is_internal"`
		CouponType     *string   `json:"coupon_type"`
		CreatedBy      *string   `json:"created_by"`
		Terms          *any      `json:"terms"`
		StartTimestamp *int64    `json:"start_timestamp"`
		EndTimestamp   *int64    `json:"end_timestamp"`
		Brands         *[]string `json:"brands"`
		Cohorts        *[]string `json:"cohorts"`
		Name           *string   `json:"name"`
		MinOrderAmount *float64  `json:"min_order_amount"`
		MaxOrderAmount *float64  `json:"max_order_amount"`
		Hidden         *bool     `json:"hidden"`
	} `json:"data"`
}

type CouponAddRequest struct {
	Data struct {
		Code            string       `json:"code" validate:"required"`
		Name            string       `json:"name" validate:"required"`
		Description     string       `json:"description"`
		IsInternal      *bool        `json:"is_internal" validate:"required"`
		MaxUsageCount   *int         `json:"max_usage_count"`
		CurrentUsage    *int         `json:"current_usage"`
		MaxUsagePerUser *int         `json:"max_usage_per_user"`
		MinOrderAmount  *float64     `json:"min_order_amount" validate:"required"`
		MaxOrderAmount  *float64     `json:"max_order_amount"`
		Source          *string      `json:"source" validate:"required"`
		CouponType      *string      `json:"coupon_type" validate:"required"`
		DiscountType    *string      `json:"discount_type" validate:"required"`
		DiscountReason  *string      `json:"discount_reason" validate:"required"`
		DiscountMethod  *string      `json:"discount_method" validate:"required"`
		DiscountValue   *float64     `json:"discount_value"`
		Priority        *int         `json:"priority"`
		CreatedBy       *string      `json:"created_by" validate:"required"`
		Meta            *any         `json:"meta"`
		Terms           *any         `json:"terms"`
		StartTimestamp  *int64       `json:"valid_from" validate:"required"`
		EndTimestamp    *int64       `json:"valid_till" validate:"required"`
		Brands          *[]string    `json:"brands" validate:"required"`
		Cohorts         *[]string    `json:"cohorts" validate:"required"`
		Rules           *interface{} `json:"rules"`
		Hidden          *bool        `json:"hidden"`
		CanClub         *bool        `json:"can_club"`
	} `json:"data"`
}

type CouponBrandRequest struct {
	Data struct {
		CouponID int    `json:"coupon_id"`
		Brand    string `json:"brand"`
		IsActive bool   `json:"is_active"`
	} `json:"data"`
}

type CouponCohortRequest struct {
	Data struct {
		CouponID       int     `json:"coupon_id"`
		Cohort         string  `json:"cohort"`
		IsActive       bool    `json:"is_active"`
		StartTimestamp *int64  `json:"start_timestamp"`
		EndTimestamp   *int64  `json:"end_timestamp"`
		Type           *string `json:"type"`
	} `json:"data"`
}

type GetAllCouponsRequest struct {
	Data struct {
		Page     int       `json:"page"`
		PageSize int       `json:"page_size"`
		Valid    *bool     `json:"valid"`
		Brands   *[]string `json:"brands"`
		Cohorts  *[]string `json:"cohorts"`
		Code     *string   `json:"code"`
		Name     *string   `json:"name"`
	} `json:"data"`
}

type CouponUsageRequest struct {
	Data struct {
		CouponID       int     `json:"coupon_id"`
		UserID         string  `json:"user_id"`
		OrderID        string  `json:"order_id"`
		UsedAt         *string `json:"used_at"`
		DiscountAmount float64 `json:"discount_amount"`
	} `json:"data"`
}

type CouponRuleRequest struct {
	Data struct {
		CouponID int         `json:"coupon_id"`
		RuleType string      `json:"rule_type"`
		Value    interface{} `json:"value"`
	} `json:"data"`
}

type CouponRuleResponse struct {
	RuleType string      `json:"rule_type"`
	Value    interface{} `json:"value"`
}

type AddUserCouponRequest struct {
	Data struct {
		UserID    string `json:"user_id" validate:"required"`
		CouponID  int64  `json:"coupon_id" validate:"required"`
		CreatedBy string `json:"created_by"`
		Campaign  string `json:"campaign"`
		Validity  int    `json:"validity"`
	} `json:"data"`
}

type DeactivateUserCouponRequest struct {
	Data struct {
		UserID   string `json:"user_id" validate:"required"`
		CouponID int64  `json:"coupon_id" validate:"required"`
	} `json:"data"`
}
