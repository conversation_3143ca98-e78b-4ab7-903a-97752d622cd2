package dto

// FlowRequest represents the input parameters
type CsTicketFlowRequestData struct {
	OrderID string `json:"order_id"`
	UserID  string `json:"user_id`
	FlowID  string `json:"flow_id"`
}

type CsTicketFlowRequest struct {
	Data CsTicketFlowRequestData `json:"data"`
}

type FlowOrderDetails struct {
	OrderNumber      string `json:"orderNumber"`
	OrderDate        string `json:"orderDate"`
	ExpectedDelivery string `json:"expectedDelivery"`
	Status           string `json:"status"`
	Courier          string `json:"courier"`
	AWBNumber        string `json:"awbNumber"`
}
