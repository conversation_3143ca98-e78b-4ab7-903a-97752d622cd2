package dto

import "kc/internal/ondc/models/shared"

type TopReviewVideo struct {
	MixPanelEventName string     `json:"MixPanelEventName"`
	ComponentHeadline string     `json:"component_headline"`
	Image             string     `json:"img"`
	Navigation        shared.Nav `json:"nav"`
	State             []string   `json:"state"`
}

type WidgetInfo struct {
	WidgetName string `json:"widget_name"`
}

// Main struct
type WidgetType12 struct {
	MixPanelEventName string           `json:"MixPanelEventName"`
	CreatedBy         string           `json:"created_by"`
	Expiry            int64            `json:"expiry"`
	ExpiryTime        int64            `json:"expiry_time"`
	Heading           string           `json:"heading"`
	ID                int              `json:"id"`
	IsStateWise       bool             `json:"isStateWise"`
	IsActive          int              `json:"is_active"`
	List              []TopReviewVideo `json:"list"`
	Resolution        int              `json:"resolution"`
	SqlID             int              `json:"sql_id"`
	SubType           string           `json:"sub_type"`
	Tag               string           `json:"tag"`
	Type              int              `json:"type"`
	UpdatedBy         string           `json:"updatedBy"`
	UpdatedAt         string           `json:"updated_at"`
	UpdatedBy2        string           `json:"updated_by"`
	Versions          string           `json:"versions"`
	VisibilityCount   int              `json:"visibility_count"`
	VisibleFrom       int64            `json:"visible_from"`
	WidgetInfo        WidgetInfo       `json:"widget_info"`
	ProductId         int64            `json:"product_id"`
}
