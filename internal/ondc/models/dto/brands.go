package dto

import (
	"kc/internal/ondc/models/shared"
)

type GetBrandDataRequest struct {
	Seller string `json:"seller" binding:"required"`
}

type GetBrandDataResponse struct {
	SellerCode      string                   `json:"seller_code"`
	Logo            string                   `json:"logo"`
	IsKcFullfilled  bool                     `json:"is_kc_fullfilled"`
	BulkOrderForm   string                   `json:"bulk_order_form"`
	BannerImageUrls []shared.BannerImageUrls `json:"banner_image_urls"`
}

type CreateBrandRequest struct {
	SellerCode      string                   `json:"seller_code" binding:"required"`
	Logo            string                   `json:"logo" binding:"required"`
	IsKcFullfilled  bool                     `json:"is_kc_fullfilled" binding:"required"`
	BulkOrderForm   string                   `json:"bulk_order_form" binding:"required"`
	BannerImageUrls []shared.BannerImageUrls `json:"banner_image_urls" binding:"required"`
	PrimaryColor    string                   `json:"primary_color,omitempty"`
	UpdatedBy       string                   `json:"updated_by,omitempty"`
	RedeemId        int                      `json:"redeem_id,omitempty"`
	CashbackId      int                      `json:"cashback_id,omitempty"`
}

type UpdateBrandRequest CreateBrandRequest

type CarousalBanner struct {
	Url string `json:"url" binding:"required"`
}

type CashBackActions struct {
	CashbackID int `json:"cashback_id" binding:"required"`
	RedeemID   int `json:"redeem_id" binding:"required"`
}
