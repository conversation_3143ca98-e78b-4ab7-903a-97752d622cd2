package dto

import (
	"time"
)

type GetUserAddressRequest struct {
	UserID     string                          `json:"user_id"`
	AddressID  *string                         `json:"address_id,omitempty"`
	AppVersion string                          `json:"appVerssion"`
	Env        string                          `json:"env"`
	DeviceID   string                          `json:"device_id"`
	Data       AppServiceAbilityAPIRequestData `json:"data"`
}

type GetUserAddressResponse struct {
	Address []UserAddress `json:"address"`
}

type UserAddress struct {
	ID                  uint64    `json:"id"`
	Name                string    `json:"name"`
	Phone               string    `json:"phone"`
	AlternatePhone      string    `json:"alternate_phone"`
	Line                string    `json:"line"`
	District            string    `json:"district"`
	State               string    `json:"state"`
	PostalCode          string    `json:"postal_code"`
	Tag                 string    `json:"tag"`
	UpdatedAt           time.Time `json:"updated_at"`
	IsDefault           bool      `json:"is_default"`
	Latitude            float64   `json:"latitude"`
	Longitude           float64   `json:"longitude"`
	GST                 string    `json:"gst"`
	HouseNumber         string    `json:"house_number"`
	Neighbourhood       string    `json:"neighbourhood"`
	Village             string    `json:"village"`
	Landmark            string    `json:"landmark"`
	Line1               string    `json:"line1"`
	Line2               string    `json:"line2"`
	StoreName           string    `json:"store_name,"`
	MinimumDeliveryDays *string   `json:"minimum_delivery_days,omitempty"`
	BillingAddressID    *uint64   `json:"billing_address_id,omitempty"`
}

type UserOrderStats struct {
	UserID                    string  `json:"user_id"`
	TotalPlacedOrderAmount    float64 `json:"total_placed_order_amount"`
	TotalDeliveredOrderAmount float64 `gorm:"column:total_delivered_order_amount"`
	OrderPlaced               int     `gorm:"column:order_placed"`
	OrderDelivered            int     `gorm:"column:order_delivered"`
	ConfirmedOrder            int     `gorm:"column:confirmed_order"`
	TotalConfirmedOrderAmount float64 `gorm:"column:total_confirmed_order_amount"`
	TotalCancelledOrderAmount float64 `gorm:"column:total_cancelled_order_amount"`
	CancelledOrder            int     `gorm:"column:cancelled_order"`
	ReturnedOrder             int     `gorm:"column:returned_order"`
	TotalReturnedOrderAmount  float64 `gorm:"column:total_returned_order_amount"`
}

type SaveUserAddressRequest struct {
	UserID         string       `json:"user_id"`
	Address        UserAddress  `json:"address"`
	BillingAddress *UserAddress `json:"billing_address"`
}

type SaveUserAddressResponse struct {
	Meta  Meta                `json:"meta"`
	Data  SaveUserAddressData `json:"data"`
	Error AppResponseError    `json:"error,omitempty"`
}

type SaveUserAddressData struct {
	Address UserAddress `json:"address"`
}

type UpdateUserAddressRequest struct {
	UserID         string       `json:"user_id"`
	Address        UserAddress  `json:"address"`
	BillingAddress *UserAddress `json:"billing_address"`
}

type GetUserAddressMetaRequestData struct {
	AddressID string   `json:"address_id"`
	Latitude  *float64 `json:"latitude,omitempty"`
	Longitude *float64 `json:"longitude,omitempty"`
}

type GetUserAddressMetaRequest struct {
	UserID string                        `json:"user_id"`
	Data   GetUserAddressMetaRequestData `json:"data"`
	Meta   Meta                          `json:"meta"`
}

type ListObject struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type MetaData struct {
	States []ListObject `json:"states"`
}

type GetUserAddressMetaResponseData struct {
	Address UserAddress `json:"address"`
}

type GetUserAddressMetaResponse struct {
	MetaData  MetaData                       `json:"meta_data"`
	Data  GetUserAddressMetaResponseData `json:"data"`
	Error AppResponseError               `json:"error,omitempty"`
}

type UpdateUserAddressResponse struct {
	Meta  Meta                  `json:"meta"`
	Data  UpdateUserAddressData `json:"data"`
	Error AppResponseError      `json:"error,omitempty"`
}

type UpdateUserAddressData struct {
	Address UserAddress `json:"address"`
}

type GetUserOrderStatsRequest struct {
	UserID string `json:"user_id"`
}

type GetUserOrderStatsResponse struct {
	Data UserOrderStats `json:"order_stats,omitempty"`
}
