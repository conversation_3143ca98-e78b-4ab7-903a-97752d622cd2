package dto

import "kc/internal/ondc/models/shared"

type AppGetKiranaBazarCartRequest struct {
	Data   AppUpsertKiranaBazarCartData `json:"data"`
	UserID string                       `json:"user_id"`
	Meta   Meta                         `json:"meta"`
}

type AppGetKiranaBazarCartResponse struct {
	Meta  Meta                      `json:"meta"`
	Data  AppGetKiranaBazarCartData `json:"data"`
	Error AppResponseError          `json:"error,omitempty"`
}

type AppGetKiranaBazarCartData struct {
	Products     []shared.SellerItems `json:"products"`
	Seller       string               `json:"seller"`
	OfferMessage OfferMessage         `json:"offer_message"`
	OrderID      string               `json:"order_id"`
}

type OrderInfoModal struct {
	MixpanelEventName     string   `json:"mixpanel_event_name"`
	ImageUrl              string   `json:"image_url"`
	Heading               string   `json:"heading"`
	SubHeading            string   `json:"sub_heading"`
	DiscountThreshold     float64  `json:"discount_threshold"`
	NextDiscountThreshold int      `json:"next_discount_threshold"`
	Discount              *float64 `json:"discount,omitempty"`
}

type BillPricing struct {
	TotalPricingString                string   `json:"total_pricing_string"`
	TotalPricing                      float64  `json:"total_pricing"`
	TotalPricingWithoutDiscount       float64  `json:"total_pricing_without_discount"`
	TotalPricingWithoutDiscountString string   `json:"total_pricing_without_discount_string"`
	Discount                          *float64 `json:"discount,omitempty"`
	DiscountString                    *string  `json:"discount_string,omitempty"`
}

type AppGetKiranaBazarCartDataV2 struct {
	Products       map[string]shared.SellerItems `json:"products"`
	Seller         string                        `json:"seller"`
	OfferMessage   OfferMessage                  `json:"offer_message"`
	OrderID        string                        `json:"order_id"`
	OrderInfoModal *OrderInfoModal               `json:"order_info_modal,omitempty"`
	BillPricing    *BillPricing                  `json:"bill_pricing,omitempty"`
	AppliedCoupons []shared.AppliedCoupon               `json:"applied_coupons,omitempty"`
}

type AppUpsertKiranaBazarCartRequest struct {
	Data   AppUpsertKiranaBazarCartData `json:"data"`
	UserID string                       `json:"user_id"`
	Meta   Meta                         `json:"meta"`
}

type AppUpsertKiranaBazarCartData struct {
	Products []shared.SellerItems `json:"products"`
	Seller   string               `json:"seller"`
	Source   string               `json:"source"`
}

type AppUpsertKiranaBazarResponse struct {
	Meta  Meta             `json:"meta"`
	Data  interface{}      `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type AppCartDetailsForOrderDetailsRequest struct {
	Meta   Meta                              `json:"meta"`
	Data   AppCartDetailsForOrderDetailsData `json:"data"`
	UserID string                            `json:"user_id"`
}

type AppCartDetailsForOrderDetailsData struct {
	OrderID string `json:"order_id"`
}
