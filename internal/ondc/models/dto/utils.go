package dto

import "math"

type Meta struct {
	Context       Context           `json:"context"`
	DeviceId      string            `json:"device_id"`
	Platform      string            `json:"platform"`
	AppVersion    string            `json:"app_version"`
	ScreenName    string            `json:"screen_name"`
	Seller        string            `json:"seller"`
	FlowID        string            `json:"flow_id"`
	TransactionID string            `json:"transaction_id"`
	BppID         string            `json:"bpp_id,omitempty"`
	BppUrl        string            `json:"bpp_url,omitempty"`
	Provider      *OrderProvider    `json:"provider,omitempty"`
	Items         []OrderItemsInner `json:"items,omitempty"`
	Tags          []TagGroup        `json:"tags,omitempty"`
	Payment       []Payment         `json:"payment,omitempty"`
	Fulfillment   []Fulfillment     `json:"fulfillment,omitempty"`
	Quote         *Quotation        `json:"quote,omitempty"`
	Billing       *Billing          `json:"billing,omitempty"`
	Limit         int               `json:"limit"`
	Offset        int               `json:"offset"`
	DisplayStatus string            `json:"display_status,omitempty"`
}

type AppResponseError struct {
	Code        *string `json:"code"`
	Message     *string `json:"message"`
	Description *string `json:"description"`
	Type        *string `json:"type"`
}

func roundToNDecimals(value float64, decimals int) float64 {
	precision := math.Pow(10, float64(decimals))
	return math.Round(value*precision) / precision
}

// roundToTwoDecimals rounds a float64 to two decimal places
// This is the standard for financial calculations
func RoundToTwoDecimals(value float64) float64 {
	return roundToNDecimals(value, 2)
}
