package dto

import "time"

type Issue struct {
	ID                     string             `json:"id"`
	ComplainantInfo        ComplainantInfo    `json:"complainant_info"`
	OrderDetails           IssueOrderDetails  `json:"order_details"`
	Description            Description        `json:"description"`
	Category               string             `json:"category"`
	SubCategory            string             `json:"sub_category"`
	IssueType              string             `json:"issue_type"`
	Source                 Source             `json:"source"`
	ExpectedResponseTime   TimeInfo           `json:"expected_response_time"`
	ExpectedResolutionTime TimeInfo           `json:"expected_resolution_time"`
	Status                 string             `json:"status"`
	IssueActions           IssueActions       `json:"issue_actions"`
	SelectedODRsInfo       []SelectedODRInfo  `json:"selected_odrs_info"`
	FinalizedODR           ODR                `json:"finalized_odr"`
	Rating                 string             `json:"rating"`
	ResolutionProvider     ResolutionProvider `json:"resolution_provider"`
	Resolution             Resolution         `json:"resolution"`
	AdditionalInfoRequired []AdditionalInfo   `json:"additional_info_required"`
	CreatedAt              time.Time          `json:"created_at"`
	UpdatedAt              time.Time          `json:"updated_at"`
}

// AppIssueRequest is the request object for issue request from the app
type AppIssueRequest struct {
	Data   AppIssueData `json:"data"`
	Meta   Meta         `json:"meta"`
	UserID string       `json:"user_id"`
}

type AppIssueData struct {
	IssueID          string   `json:"issue_id"`
	ShortDescription string   `json:"short_description"`
	LongDescription  string   `json:"long_description"`
	Images           []string `json:"images"`
	OrderID          string   `json:"order_id"`
}

// AppIssueResponse is the response object for the response from the server
type AppIssueResponse struct {
	Meta  Meta                 `json:"meta"`
	Data  AppIssueResponseData `json:"data"`
	Error AppResponseError     `json:"error,omitempty"`
}

type AppIssueResponseData struct {
	Issue Issue `json:"issue,omitempty"`
}

type AppIssueStatusRequest struct {
	Data   AppIssueStatusData `json:"data"`
	Meta   Meta               `json:"meta"`
	UserID string             `json:"user_id"`
}

type AppIssueStatusData struct {
}

type AppIssueStatusResponse struct {
	Data  AppIssueStatusData `json:"data"`
	Meta  Meta               `json:"meta"`
	Error AppResponseError   `json:"error,omitempty"`
}

type GetTicketsRequest struct {
	Data   TicketRequestData `json:"data"`
	Meta   Meta              `json:"meta"`
	UserID string            `json:"user_id"`
}

type TicketRequestData struct {
	Seller string   `json:"seller"`
	Status []string `json:"status"`
}

type GetTicketsResponse struct {
	Meta  Meta             `json:"meta"`
	Error AppResponseError `json:"error,omitempty"`
	Data  TicketsData      `json:"data"`
}

type TicketsData struct {
	Tickets []Ticket `json:"tickets"`
}

type Ticket struct {
	ID           *uint64         `json:"id"`
	Type         string          `json:"type"`
	Headline     string          `json:"headline"`
	Category     string          `json:"category"`
	SubCategory  string          `json:"sub_category"`
	Description  string          `json:"description"`
	OrderDetails *IGMOrderDetail `json:"order_details,omitempty"`
	CreatedAt    *int64          `json:"created_at"`
	UpdatedAt    *int64          `json:"updated_at"`
	Status       string          `json:"status"`
	Media        *[]IGMMedia     `json:"media,omitempty"`
	Rating       *TicketRating   `json:"rating,omitempty"`
}

type TicketRating struct {
	Rating      *int    `json:"rating"`
	Description *string `json:"description"`
}
type IGMMedia struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}

type IGMOrderDetail struct {
	ID       *int64 `json:"id"`
	PlacedAt *int64 `json:"placed_at"`
	Status   string `json:"status"`
	CTA      CTA    `json:"cta"`
}

type CTA struct {
	Text          string      `json:"text"`
	Nav           interface{} `json:"nav"`
	MixpanelEvent *string     `json:"mixpanel_event_name,omitempty"`
}
type GetTicketDetailsResponse struct {
	Meta  Meta             `json:"meta"`
	Error AppResponseError `json:"error,omitempty"`
	Data  TicketsData      `json:"data"`
}

type CreateTicketRequest struct {
	Meta   Meta       `json:"meta"`
	UserID string     `json:"user_id"`
	Data   TicketData `json:"data"`
}

type TicketData struct {
	Ticket Ticket `json:"ticket"`
}

type CreateTicketResponse struct {
	Meta  Meta             `json:"meta"`
	Data  TicketsData      `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type UpdateTicketRequest struct {
	Meta   Meta       `json:"meta"`
	UserID string     `json:"user_id"`
	Data   TicketData `json:"data"`
}

type UpdateTicketResponse struct {
	Meta  Meta             `json:"meta"`
	Data  TicketsData      `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type FAQResponse struct {
	Meta  Meta             `json:"meta"`
	Error AppResponseError `json:"error,omitempty"`
	Data  Faqs             `json:"data"`
}

type Faqs struct {
	FAQData []FAQData `json:"faqs"`
}

type FAQData struct {
	ID        int    `json:"id"`
	Type      string `json:"type"`
	Question  string `json:"question"`
	Answer    string `json:"answer"`
	CreatedAt int64  `json:"created_at"`
	UpdatedAt int64  `json:"updated_at"`
}

type GetIGMOrdersRequest struct {
	Meta   Meta                `json:"meta"`
	UserID string              `json:"user_id"`
	Data   IGMOrdersRequesData `json:"data"`
}

type IGMOrdersRequesData struct {
	Seller string   `json:"seller"`
	Status []string `json:"status"`
}

type IGMOrder struct {
	ID       int64   `json:"id"`
	Status   string  `json:"status"`
	PlacedAt int64   `json:"placed_at"`
	Amount   int64   `json:"amount"`
	Ticket   *Ticket `json:"ticket,omitempty"`
	CTA      *CTA    `json:"cta,omitempty"`
}

type GetIGMOrdersResponse struct {
	Meta  Meta             `json:"meta"`
	Data  IGMOrdersData    `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type IGMOrdersData struct {
	Orders []IGMOrder `json:"orders"`
}

type IssueCategory struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type CategoriesData struct {
	Categories []IssueCategory `json:"categories"`
}

type GetIssuesCategoriesResponse struct {
	Meta  Meta             `json:"meta"`
	Data  CategoriesData   `json:"data"`
	Error AppResponseError `json:"error,omitempty"`
}

type Action struct {
	Text      string     `json:"text"`
	Media     []IGMMedia `json:"media"`
	CreatedAt int64      `json:"created_at"`
}

type IGMIssueActions struct {
	ComplainantActions []Action `json:"complainant_actions"`
	RespondentActions  []Action `json:"respondent_actions"`
}

type IGMComplainantInfo struct {
	Name            string `json:"name"`
	ProfileImageURL string `json:"profile_image_url"`
}

type IGMRespondentInfo struct {
	Name            string `json:"name"`
	ProfileImageURL string `json:"profile_image_url"`
}

type Conversations struct {
	IssueActions IGMIssueActions `json:"issue_actions"`
}

type ConversationsData struct {
	ComplainantInfo IGMComplainantInfo `json:"complainant_info"`
	RespondentInfo  IGMRespondentInfo  `json:"respondent_info"`
	Conversations   Conversations      `json:"conversations"`
}

type GetIGMConversationsResponse struct {
	Meta  Meta              `json:"meta"`
	Data  ConversationsData `json:"data"`
	Error AppResponseError  `json:"error,omitempty"`
}

type IGMConversationData struct {
	TicketID        *uint64       `json:"ticket_id"`
	Conversations   Conversations `json:"conversations"`
	ConversationsID *string       `json:"conversations_id"`
}

type IGMConversationsRequest struct {
	Meta   Meta                `json:"meta"`
	Data   IGMConversationData `json:"data"`
	UserID string              `json:"user_id"`
}

type IGMConversationsResponse struct {
	Meta  Meta                `json:"meta"`
	Data  IGMConversationData `json:"data"`
	Error AppResponseError    `json:"error,omitempty"`
}
