package dto

// OrderFinancialsBulkUpdateRequest represents the request structure for bulk update
type OrderFinancialsBulkUpdateRequest struct {
	UpdatedBy  string                        `json:"updated_by" validate:"required"`
	ActionType string                        `json:"action_type" validate:"required"`
	Force      bool                          `json:"force"`
	Data       OrderFinancialsBulkUpdateData `json:"data" validate:"required"`
}

// OrderFinancialsBulkUpdateData represents the data structure within the request
type OrderFinancialsBulkUpdateData struct {
	Type   string `json:"type" validate:"required"`
	FileID string `json:"file_id"` // This is ignored as per requirements
	Data   string `json:"data" validate:"required"`
}

// OrderFinancialsBulkUpdateResponse represents the response from external API
type OrderFinancialsBulkUpdateResponse struct {
	Status string                                `json:"status"`
	Data   OrderFinancialsBulkUpdateResponseData `json:"data"`
}

// OrderFinancialsBulkUpdateResponseData represents the data structure within the response
type OrderFinancialsBulkUpdateResponseData struct {
	Data   OrderFinancialsBulkUpdateResult `json:"data"`
	Error  interface{}                     `json:"error"`
	FileID string                          `json:"file_id"`
}

// OrderFinancialsBulkUpdateResult represents the result data from external API
type OrderFinancialsBulkUpdateResult struct {
	Failed         int    `json:"failed"`
	Success        int    `json:"success"`
	TotalRecords   int    `json:"total_records"`
	RequestCSVURL  string `json:"request_csv_url"`
	ResponseCSVURL string `json:"response_csv_url"`
}

type OrderFinancialsBulkGetRequest struct {
	Filters FinancialsFilters `json:"filters"`
}

type FinancialsFilters struct {
	Sellers    []string `json:"sellers"`
	PlacedFrom int64    `json:"placed_from"`
	PlacedTo   int64    `json:"placed_to"`
	OrderIds   []int64  `json:"order_ids"`
}
