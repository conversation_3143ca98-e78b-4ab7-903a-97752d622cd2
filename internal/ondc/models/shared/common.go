package shared

import (
	"time"

	"gorm.io/datatypes"
)

type PackageDetails struct {
	Dimensions []Dimensions `json:"dimentions,omitempty"`
}

func (p *PackageDetails) GetVolumetricWeight() float64 {
	volWeight := 0.0
	for _, d := range p.Dimensions {
		volWeight += d.Length * d.Breadth * d.Height
	}
	return volWeight / 5000 // this is the formula for volumetric weight calculation for ekart and delhivery
}

type Dimensions struct {
	Height  float64 `json:"height,omitempty"`
	Breadth float64 `json:"width,omitempty"`
	Length  float64 `json:"length,omitempty"`
	Weight  float64 `json:"weight,omitempty"`
}

type SellerItems struct {
	SellerItemsData
	SizeVariants []SellerItemsData `json:"size_variants,omitempty"`
}

type OriginalSkuForVirtualSku struct {
	ID            string  `json:"id"`
	ProductID     string  `json:"product_id"`
	WholesaleRate float64 `json:"wholesale_rate"`
	Quantity      int32   `json:"quantity"`
	IsActive      bool    `json:"is_active"`
	Notes         string  `json:"notes,omitempty"` // Optional notes for the SKU
}

type SellerItemsData struct {
	ProviderID               string                     `json:"provider_id"`
	Seller                   string                     `json:"seller"`
	SellerName               string                     `json:"seller_name"`
	ProviderTTL              string                     `json:"provider_ttl"`
	ProviderLocationIDs      []string                   `json:"provider_locations_ids"`
	Name                     string                     `json:"name"`
	ImageUrls                []string                   `json:"image_urls"`
	BannerImageUrls          []BannerImageUrls          `json:"banner_image_urls,omitempty"`
	LocationIds              []string                   `json:"location_ids"`
	CategoryIds              []string                   `json:"category_ids"`
	TopBadge                 *Badge                     `json:"top_badge,omitempty"`
	BottomBadge              *Badge                     `json:"bottom_badge,omitempty"`
	BottomInfoBadge          *Badge                     `json:"bottom_info_badge,omitempty"`
	ProductType              *string                    `json:"product_type,omitempty"`
	FulfilmentIds            []string                   `json:"fulfilment_ids"`
	SkuConfirmationProcessed bool                       `json:"sku_confirmation_processed"`
	ID                       string                     `json:"id"`
	ParentItemID             string                     `json:"parent_item_id"`
	ShowRecommendedProducts  *bool                      `json:"show_recommended_products,omitempty"`
	CTAText                  *string                    `json:"cta_text,omitempty"`
	Price                    Price                      `json:"price"`
	Rating                   *float64                   `json:"rating,omitempty"`
	RatingsCount             *int                       `json:"ratings_count,omitempty"`
	Quantity                 int32                      `json:"quantity"`
	Description              *string                    `json:"description,omitempty"`
	Meta                     datatypes.JSON             `json:"meta"`
	SizeVariantCode          *int64                     `json:"size_variant_code,omitempty"`
	IsDefault                bool                       `json:"is_default"`
	IsOos                    interface{}                `json:"is_oos"`
	IsActive                 bool                       `json:"is_active"`
	Type                     int                        `json:"type"`
	Rank                     int                        `json:"rank"`
	Nav                      *Nav                       `json:"nav,omitempty"`
	PopularityValue          float64                    `json:"popularity_value"`
	Manufacturer             string                     `json:"manufacturer"`
	OriginalSkus             []OriginalSkuForVirtualSku `json:"original_skus,omitempty" gorm:"-"`
}

type SellerItemsB2B struct {
	SellerItemsDataB2B
	SizeVariants []SellerItemsDataB2B `json:"size_variants,omitempty"`
}

type SalesAnalysisData struct {
	Days               int     `json:"days"`                  // Number of days for which sales data is analyzed
	TotalQuantity      int     `json:"total_quantity"`        // Total quantity sold in the specified days
	AverageSalesPerDay float64 `json:"average_sales_per_day"` // Average sales per day
}

type SellerItemsDataB2B struct {
	ProviderID             string              `json:"provider_id"`
	Seller                 string              `json:"seller"`
	ProviderTTL            string              `json:"provider_ttl"`
	ProviderLocationIDs    []string            `json:"provider_locations_ids"`
	Name                   string              `json:"name"`
	ImageUrls              []string            `json:"image_urls"`
	BannerImageUrls        []BannerImageUrls   `json:"banner_image_urls,omitempty"`
	LocationIds            []string            `json:"location_ids"`
	CategoryIds            []string            `json:"category_ids"`
	TopBadge               *Badge              `json:"top_badge,omitempty"`
	BottomBadge            *Badge              `json:"bottom_badge,omitempty"`
	FulfilmentIds          []string            `json:"fulfilment_ids"`
	ID                     string              `json:"id"`
	ParentItemID           string              `json:"parent_item_id"`
	Price                  Price               `json:"price"`
	Rating                 *float64            `json:"rating,omitempty"`
	RatingsCount           *int                `json:"ratings_count,omitempty"`
	Quantity               int32               `json:"quantity"`
	Description            *string             `json:"description,omitempty"`
	Meta                   datatypes.JSON      `json:"meta"`
	SizeVariantCode        *int64              `json:"size_variant_code,omitempty"`
	IsDefault              bool                `json:"is_default"`
	IsOos                  interface{}         `json:"is_oos"`
	IsActive               bool                `json:"is_active"`
	Type                   int                 `json:"type"`
	Rank                   int                 `json:"rank"`
	Nav                    *Nav                `json:"nav,omitempty"`
	PopularityValue        float64             `json:"popularity_value"`
	HSNCode                *string             `json:"hsn_code"`
	SKUCode                *string             `json:"sku_code"`
	PiecesQuantity         *float64            `json:"pieces_quantity,omitempty"`
	SalesThroughputPerDay  float64             `json:"sales_throughput_per_day"`
	ThroughputUpdationDate *string             `json:"throughput_updation_date,omitempty"`
	DaysStockLeft          *float64            `json:"days_stock_left,omitempty"`
	ActualSalesDays        *string             `json:"actual_sales_days,omitempty"`
	SalesAnalysisData      []SalesAnalysisData `json:"sales_analysis_data,omitempty"`
	Manufacturer           *string             `json:"manufacturer,omitempty"`
	ProductType            *string             `json:"product_type,omitempty"`
	SellerName             string              `json:"seller_name"`
}

type BannerImageUrls struct {
	MixpanelEventName string `json:"mixpanel_event_name"`
	Url               string `json:"url"`
	Nav               *Nav   `json:"nav,omitempty"`
	Type              string `json:"type"`
	VideoUrl          string `json:"video_url"`
	HasBulkOrderForm  bool   `json:"has_bulk_order_form,omitempty"`
	BulkOrderFormUrl  string `json:"bulk_order_form_url,omitempty"`
}

type Nav struct {
	Name    string                 `json:"name"`
	NavType string                 `json:"nav_type"`
	Params  map[string]interface{} `json:"params"`
}

type BackHandlerBottomSheet struct {
	Name    string `json:"name"`
	NavType string `json:"nav_type"`
	Params  Params `json:"params"`
}

type Params struct {
	Data Data `json:"data"`
}

type Data struct {
	ShowCloseButton bool    `json:"show_close_button"`
	Widgets         Widgets `json:"widgets"`
}

type Widgets struct {
	Layout []datatypes.JSON `json:"layout"`
}

type Badge struct {
	Text    string   `json:"text"`
	Color   string   `json:"color"`
	BgColor []string `json:"bg_color"`
}

type Price struct {
	// ISO 4217 alphabetic currency code e.g. 'INR'
	Currency       string `json:"currency,omitempty"`
	Value          string `json:"value,omitempty"`
	EstimatedValue string `json:"estimated_value,omitempty"`
	ComputedValue  string `json:"computed_value,omitempty"`
	ListedValue    string `json:"listed_value,omitempty"`
	OfferedValue   string `json:"offered_value,omitempty"`
	MinimumValue   string `json:"minimum_value,omitempty"`
	MaximumValue   string `json:"maximum_value,omitempty"`
}

type ProductPrice struct {
	ID           string                 `json:"id"`
	OfferedValue string                 `json:"offered_value"`
	Quantity     int                    `json:"quantity"`
	TotalValue   string                 `json:"total_value"`
	ProductName  string                 `json:"product_name"`
	PackSize     int                    `json:"pack_size"`
	Key          string                 `json:"key"`
	Value        string                 `json:"value"`
	Size         string                 `json:"size"`
	Styles       map[string]interface{} `json:"styles,omitempty"`
	Type         string                 `json:"type"`
	Manufacturer string                 `json:"manufacturer"`
}

type TotalPricing struct {
	Key        string                  `json:"key"`
	Value      string                  `json:"value"`
	TotalValue float64                 `json:"total_value"`
	Styles     *map[string]interface{} `json:"styles,omitempty"`
	IsInternal bool                    `json:"is_internal"`
	Name       string                  `json:"name"`
	Type       string                  `json:"type"`
	ID         int64                   `json:"id"`
}

type DiscountPricing struct {
	Key        string                  `json:"key"`
	Value      string                  `json:"value"`
	TotalValue float64                 `json:"total_value"`
	Styles     *map[string]interface{} `json:"styles,omitempty"`
	IsInternal bool                    `json:"is_internal"`
	Type       string                  `json:"type"`
	Name       string                  `json:"name"`
	ID         int64                   `json:"id"`
}

type AppliedCoupon struct {
	ImageUrl    string `json:"image_url"`
	Code        string `json:"code"`
	Description string `json:"description"`
	Text        string `json:"text"`
	ID          string `json:"id"`
}

type KiranaBazarVirtualSkuMapping struct {
	ID            uint       `json:"id"`
	VirtualSkuId  uint       `json:"virtual_sku_id"`
	ProductId     uint       `json:"product_id"`
	Quantity      float64    `json:"quantity"`
	WholesaleRate *float64   `json:"wholesale_rate,omitempty"` // Wholesale rate for the virtual SKU
	IsActive      bool       `json:"is_active"`
	CreatedAt     *time.Time `json:"created_at"`
	UpdatedAt     *time.Time `json:"updated_at"`
	UpdatedBy     string     `json:"updated_by"`      // User who last updated the mapping
	Notes         string     `json:"notes,omitempty"` // Optional notes for the mapping
	BrandShare    float64    `json:"brand_share"`     // Optional brand share for the mapping
}

func (*KiranaBazarVirtualSkuMapping) TableName() string {
	return "kiranabazar_virtual_sku_mapping"
}

// dao for selleritems
type KiranaBazarProductMeta struct {
	Description            string   `json:"description"`
	HindiName              string   `json:"hindi_name"`
	Quantity               string   `json:"quantity"`
	PackSize               int      `json:"pack_size"`
	CaseSize               int      `json:"case_size"`
	MRPNumber              float64  `json:"mrp_number"`
	MRPString              string   `json:"mrp_string"`
	MarkupMargin           float64  `json:"markup_margin"`
	MarkupMarginString     string   `json:"markup_margin_string"`
	MarkupMarginKey        string   `json:"markup_margin_key"`
	MarkupMarginValue      string   `json:"markup_margin_value"`
	MRPStringValue         string   `json:"mrp_string_value"`
	WholesaleRate          float64  `json:"wholesale_rate"`
	WholesaleRateString    string   `json:"wholesale_rate_string"`
	Tax                    *float64 `json:"tax,omitempty"`
	HSNCode                *string  `json:"hsn_code,omitempty"`
	RewardCoins            *int     `json:"reward_coins,omitempty"`
	ExpiresIn              *int64   `json:"expires_in,omitempty"`
	ExpiryDateString       *string  `json:"expiry_date_string,omitempty"`
	BrandWholesaleRate     *float64 `json:"brand_wholesale_rate,omitempty"`
	EffectiveWholesaleRate *float64 `json:"effective_brand_wholesale_rate,omitempty"`
	BadgeText              *string  `json:"badge_text,omitempty"`
	MaxCap                 *int     `json:"max_cap,omitempty"`
	DisableAddMore         *bool    `json:"disable_add_more,omitempty"`
	Length                 *float64 `json:"length,omitempty"`
	Breadth                *float64 `json:"breadth,omitempty"`
	Height                 *float64 `json:"height,omitempty"`
	Weight                 *float64 `json:"weight,omitempty"`
}
