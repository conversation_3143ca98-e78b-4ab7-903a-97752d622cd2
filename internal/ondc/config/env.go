package config

import "gorm.io/gorm"

type AppConfig struct {
	Name              string               `json:"name"`
	Mode              string               `json:"mode"`
	DbConfig          *DBConfig            `json:"db_config"`
	Db                *gorm.DB             `json:"db,omitempty"`
	FirebaseKeyPath   string               `json:"firebase_key_path"`
	FirebaseMetaDbUrl string               `json:"firebase_metadb_url"`
	Onboarding        *OnboardingConfig    `json:"onboarding"`
	RedisConfig       *RedisConfig         `json:"redis_config"`
	GcpRedisConfig    *GcpRedisConfig      `json:"gcp_redis_config"`
	EasyEcom          *[]EasyEcomConfig    `json:"easy_ecom_config"`
	UnicommerceConfig *[]UnicommerceConfig `json:"unicommerce_config"`
	ShipRocketConfig  *[]ShipRocketConfig  `json:"shiprocket_config"`
	ShipwayConfig     *ShipwayConfig       `json:"shipway_config"`
	IcarryConfig      *IcarryConfig        `json:"icarry_config"`
	AzureConfig       *AzureConfig         `json:"azure_config"`
	DelhiveryConfig   *[]DelhiveryConfig   `json:"delhivery_config"`
	ZohoConfig        *[]ZohoConfig        `json:"zoho_config"`
	ExotelConfig      ExotelConfig         `json:"exotel_config"`
	Env               string               `json:"env"`
	RabbitMQConfig    RabbitMQConfig       `json:"rabbitmq_config"`
	PaymentConfig     PaymentConfig        `json:"payment_config"`
	ElasticConfig     ElasticConfig        `json:"elastic_config"`
	AuthConfig        AuthConfig           `json:"auth_config"`
}

type AuthConfig struct {
	JwtSecret     string `json:"jwt_secret"`
	EncryptionKey string `json:"encryption_key"`
}

type PaymentConfig struct {
	RazorpayConfig RazorpayConfig `json:"razorpay_config"`
}

type RazorpayConfig struct {
	KeyID     string `json:"key_id"`
	KeySecret string `json:"key_secret"`
	WHSecret  string `json:"wh_secret"`
}

type AzureConfig struct {
	AccountName string `json:"account_name"`
	AccountKey  string `json:"account_key"`
	URL         string `json:"url"`
	Container   string `json:"container"`
}

type UnicommerceConfig struct {
	Username       string `json:"username"`
	Password       string `json:"password"`
	OMS            string `json:"oms"`
	ClientID       string `json:"client_id"`
	GrantType      string `json:"grant_type"`
	BaseUrl        string `json:"base_url"`
	Facility       string `json:"facility"`
	SellerFacility string `json:"seller_facility"`
	Channel        string `json:"channel,omitempty"`
}

type EasyEcomConfig struct {
	Email         string `json:"email"`
	Password      string `json:"password"`
	LocationKey   string `json:"location_key"`
	MarketPlaceID int    `json:"market_place_id"`
	OMS           string `json:"oms"`
}

type ShipRocketConfig struct {
	Email    string `json:"email"`
	Password string `json:"password"`
	OMS      string `json:"oms"`
}

type ShipwayConfig struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type IcarryConfig struct {
	Username string `json:"username"`
	Key      string `json:"key"`
}

type DelhiveryConfig struct {
	Token string `json:"token"`
	OMS   string `json:"oms"`
}
type ZohoConfig struct {
	ClientID       string `json:"client_id"`
	ClientSecret   string `json:"client_secret"`
	RefreshToken   string `json:"refresh_token"`
	OrganizationID string `json:"organization_id"`
	User           string `json:"user"`
}

type ExotelConfig struct {
	APIKey   string `json:"api_key"`
	APIToken string `json:"api_token"`
}

type RedisConfig struct {
	Hostname string `json:"hostname"`
	Password string `json:"password"`
	Username string `json:"username"`
	Database string `json:"database"`
}

type GcpRedisConfig struct {
	Host string `json:"host"`
	Port string `json:"port"`
}

type DBConfig struct {
	Hostname           string `json:"hostname"`
	Password           string `json:"password"`
	Username           string `json:"username"`
	Database           string `json:"database"`
	Port               string `json:"port"`
	MaxOpenConnections int    `json:"max_open_connections"`
	DBDriverName       string `json:"db_driver_name"`
}

//
//type MailerConfig struct {
//	Key    string `json:"key"`
//	Domain string `json:"domain"`
//}

// OnboardingConfig is a config for onboarding service.
type OnboardingConfig struct {
	ProjectID             string `json:"projectID" validate:"required"`
	Port                  int    `json:"port" validate:"required"`
	RequestID             string `json:"requestID" validate:"required"`
	SecretID              string `json:"secretID" validate:"required"`
	RegistryEncryptPubKey string `json:"registryEncryptPubKey" validate:"required"`
	ONDCEnvironment       string `json:"ONDCEnvironment"`
}

type RabbitMQConfig struct {
	Hostname string `json:"hostname"`
	Port     string `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
}

type ElasticConfig struct {
	Host     string `json:"hostname"`
	Username string `json:"username"`
	Password string `json:"password"`
}
