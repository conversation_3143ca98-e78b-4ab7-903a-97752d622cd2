package config

import (
	"encoding/json"
	"fmt"
	"os"

	"github.com/sirupsen/logrus"
)

//var logging = logging.New("")

const EnvVar = "ONDC_CONFIG"
const IVRVar = "IVR_CONFIG"
const DeploymentEnv = "ONDC_DEPLOYMENT_ENV"

type IVRConfig []struct {
	Apps   []IVRAppData `json:"apps"`
	Seller string       `json:"seller"`
}

type IVRAppData struct {
	Name  string `json:"name"`
	AppID int    `json:"app_id"`
}

func GetEnv() string {
	return os.Getenv(DeploymentEnv)
}

func GetDeploymentPort() string {
	env := GetEnv()
	fmt.Println("Environment loaded:", env)
	switch env {
	case "prod":
		{
			return "80"
		}
	case "local":
		{
			return "81"
		}
	case "stage":
		{
			return "80"
		}
	default:
		{
			return "8080"
		}
	}
}

func loadFileFromEnv(fileName string, env *AppConfig, logger *logrus.Logger) {
	data, err := os.ReadFile(fileName)
	if err != nil {
		logger.Fatal("not able to read env file for base configs, error is %+v", err)
	}
	err = json.Unmarshal(data, env)
	if err != nil {
		logger.Fatal("not able to unmarshal the config file, error is %+v", err)
	}
	env.Env = os.Getenv(DeploymentEnv)
}

func loadIVRConfig(fileName string, ivrConfig *IVRConfig, logger *logrus.Logger) {
	data, err := os.ReadFile(fileName)
	if err != nil {
		logger.Fatal("not able to read env file for base configs, error is %+v", err)
	}
	err = json.Unmarshal(data, ivrConfig)
	if err != nil {
		logger.Fatal("not able to unmarshal the config file, error is %+v", err)
	}
}
func LoadConfig(logger *logrus.Logger) (*AppConfig, *IVRConfig) {
	config := &AppConfig{}
	ivrConfig := &IVRConfig{}
	loadFileFromEnv(os.Getenv(EnvVar), config, logger)
	loadIVRConfig(os.Getenv(IVRVar), ivrConfig, logger)
	return config, ivrConfig
}
