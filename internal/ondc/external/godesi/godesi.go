package godesi

import (
	"encoding/json"
	"strconv"
)

type GoDesiPincodeTAT struct {
	Pincode string `json:"Pincode"`
	TAT     int    `json:"TAT"`
}

var goDesiServiceAbilityMap = make(map[string]int)

func PreLoadGoDesiServiceAbilityPincode() error {
	var rawData []map[string]interface{}
	err := json.Unmarshal([]byte(goDesiServiceAbilityPincodes), &rawData)
	if err != nil {
		return err
	}

	for _, item := range rawData {
		var pincode string
		switch p := item["Pincode"].(type) {
		case float64:
			pincode = strconv.FormatInt(int64(p), 10)
		case string:
			pincode = p
		default:
			continue // Skip invalid entries
		}

		tat, ok := item["TAT"].(float64)
		if !ok {
			continue // Skip invalid entries
		}

		goDesiServiceAbilityMap[pincode] = int(tat)
	}

	return nil
}

func CheckServiceAbility(pincode string) (int, bool) {
	value, exists := goDesiServiceAbilityMap[pincode]
	return value, exists
}
