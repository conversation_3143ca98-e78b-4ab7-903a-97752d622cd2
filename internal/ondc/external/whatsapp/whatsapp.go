package whatsapp

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/service/brands"
	"net/http"
	"regexp"
	"strings"
)

// Parameter represents a parameter in the WhatsApp message
type Parameter struct {
	Type     string      `json:"type"`
	Data     interface{} `json:"data"`
	Filename string      `json:"filename,omitempty"`
}

// Component represents a component in the WhatsApp message
type Component struct {
	Type       string      `json:"type"`
	SubType    string      `json:"sub_type,omitempty"`
	Index      string      `json:"index,omitempty"`
	Parameters []Parameter `json:"parameters"`
}

// MessageData represents the inner data structure
type MessageData struct {
	To           string      `json:"to"`
	Template     string      `json:"template"`
	Language     string      `json:"language,omitempty"`
	CampaignName string      `json:"campaign_name"`
	Priority     int         `json:"priority"`
	Components   []Component `json:"components"`
	FollowDND    bool        `json:"followDND"`
}

// RequestBody represents the complete request body
type RequestBody struct {
	Data MessageData `json:"data"`
}

type DynamicLinkConfig struct {
	EmbeddingType string                 `json:"embedding_type"`
	Data          map[string]interface{} `json:"data"`
	ButtonIndex   string                 `json:"button_index,omitempty"`
}

func SendMessage(phoneNumber, template, imageURL, seller string) error {
	whatsAppURL := "https://asia-south1-op-d2r.cloudfunctions.net/WA-Message-Publisher"

	// Create the message structure
	message := RequestBody{
		Data: MessageData{
			To:           phoneNumber,
			Template:     template,
			CampaignName: template,
			Priority:     0,
			Components: []Component{
				{
					Type: "header",
					Parameters: []Parameter{
						{
							Type: "image",
							Data: imageURL,
						},
					},
				},
				{
					Type: "body",
					Parameters: []Parameter{
						{
							Type: "text",
							Data: seller,
						},
					},
				},
			},
		},
	}

	// Convert the message to JSON
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("error marshaling JSON: %v", err)
	}

	// Create the HTTP request
	req, err := http.NewRequest("POST", whatsAppURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("error creating request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return nil
}

func SendNotAbleToConnectMessage(phoneNumber, template, seller, orderID, orderValue string) error {
	whatsAppURL := "https://asia-south1-op-d2r.cloudfunctions.net/WA-Message-Publisher"

	// Create the message structure
	message := RequestBody{
		Data: MessageData{
			To:           phoneNumber,
			Template:     template,
			CampaignName: template,
			Priority:     0,
			Components: []Component{
				{
					Type: "body",
					Parameters: []Parameter{
						{
							Type: "text",
							Data: seller,
						},
						{
							Type: "text",
							Data: orderID,
						},
						{
							Type: "text",
							Data: orderValue,
						},
					},
				},
			},
		},
	}

	// Convert the message to JSON
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("error marshaling JSON: %v", err)
	}

	// Create the HTTP request
	req, err := http.NewRequest("POST", whatsAppURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("error creating request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return nil
}

func SendWhatsAppForTemplate(phoneNumber, template string) error {
	whatsAppURL := "https://asia-south1-op-d2r.cloudfunctions.net/WA-Message-Publisher"

	// Create the message structure
	message := RequestBody{
		Data: MessageData{
			To:           phoneNumber,
			Template:     template,
			CampaignName: template,
			Priority:     0,
			Components:   []Component{},
		},
	}

	// Convert the message to JSON
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("error marshaling JSON: %v", err)
	}

	// Create the HTTP request
	req, err := http.NewRequest("POST", whatsAppURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("error creating request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return nil
}

func GenerateDynamicLink(payload any) (string, error) {
	url := "https://kc.retailpulse.ai/api/dynamicLink"

	// Marshal the payload to JSON
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return "", fmt.Errorf("error marshaling JSON: %v", err)
	}

	// Create the HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("error creating request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36")
	req.Header.Set("sec-ch-ua-platform", "macOS")
	req.Header.Set("sec-ch-ua", `Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"`)
	req.Header.Set("sec-ch-ua-mobile", "?0")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	// Read the response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("error reading response body: %v", err)
	}

	// Parse the response to extract the generated link
	var response string
	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("error unmarshaling response: %v", err)
	}

	return response, nil
}

func SendRetryPaymentMessage(phoneNumber, name, seller, orderID, orderValue, timeToCancel string) error {
	whatsAppURL := "https://asia-south1-op-d2r.cloudfunctions.net/WA-Message-Publisher"

	source, exists := brands.GetSourceBySeller(seller)
	if !exists {
		slack.SendSlackMessage(fmt.Sprintf("Source not found for seller: %s from retry payment msg wa", seller))
		return fmt.Errorf("source not found for seller: %s", seller)
	}

	linkData := map[string]interface{}{
		"cta": map[string]interface{}{
			"name":     "OrderingModule",
			"nav_type": "Redirect to Screen",
			"params": map[string]interface{}{
				"screen": "OrderDetails",
				"params": map[string]interface{}{
					"seller":        seller,
					"source":        source,
					"order_id":      orderID,
					"retry_payment": true,
				},
			},
		},
		"non_firebase_link": false,
	}

	link, err := GenerateDynamicLink(linkData)

	if err != nil {
		fmt.Println("error generating dynamic link", err)
		return fmt.Errorf("error generating dynamic link: %v", err)
	}

	template := "ordering_payment_new"
	// Create the message structure
	message := RequestBody{
		Data: MessageData{
			To:           phoneNumber,
			Template:     template,
			CampaignName: template,
			Priority:     0,
			FollowDND:    false,
			Components: []Component{
				{
					Type: "body",
					Parameters: []Parameter{
						{
							Type: "text",
							Data: name,
						},
						{
							Type: "text",
							Data: orderID,
						},
						{
							Type: "text",
							Data: orderValue,
						},
						{
							Type: "text",
							Data: link,
						},
						{
							Type: "text",
							Data: timeToCancel,
						},
					},
				},
			},
		},
	}

	// Convert the message to JSON
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("error marshaling JSON: %v", err)
	}

	// Create the HTTP request
	req, err := http.NewRequest("POST", whatsAppURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("error creating request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return nil
}

func SendOrderOurForDeliveryWhatsapp(phoneNumber, name, orderID, orderValue string) error {
	whatsAppURL := "https://asia-south1-op-d2r.cloudfunctions.net/WA-Message-Publisher"
	template := "order_out_for_delivery_new"
	// Create the message structure
	message := RequestBody{
		Data: MessageData{
			To:           phoneNumber,
			Template:     template,
			CampaignName: template,
			Priority:     0,
			Components: []Component{
				{
					Type: "body",
					Parameters: []Parameter{
						{
							Type: "text",
							Data: name,
						},
						{
							Type: "text",
							Data: orderID,
						},
						{
							Type: "text",
							Data: orderValue,
						},
					},
				},
			},
		},
	}
	// Convert the message to JSON
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("error marshaling JSON: %v", err)
	}
	// Create the HTTP request
	req, err := http.NewRequest("POST", whatsAppURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("error creating request: %v", err)
	}
	// Set headers
	req.Header.Set("Content-Type", "application/json")
	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error sending request: %v", err)
	}

	defer resp.Body.Close()
	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}
	return nil
}

func ValidatePhoneNumber(phoneNumber string) string {
	validPhoneRegex := regexp.MustCompile(`^\+91\d{10}$`)

	if !validPhoneRegex.MatchString(phoneNumber) {
		digitsOnly := regexp.MustCompile(`\D`).ReplaceAllString(phoneNumber, "")
		if len(digitsOnly) == 10 {
			phoneNumber = "+91" + digitsOnly
		} else {
			fmt.Println("Invalid phone number format")
		}
	}
	return phoneNumber
}

func SendInitiatePaymentMessage(phoneNumber, seller, orderID, orderValue string, onlyLink bool) (string, error) {
	whatsAppURL := "https://asia-south1-op-d2r.cloudfunctions.net/WA-Message-Publisher"
	phoneNumber = ValidatePhoneNumber(phoneNumber)

	source, exists := brands.GetSourceBySeller(seller)
	if !exists {
		slack.SendSlackMessage(fmt.Sprintf("Source not found for seller: %s from send init payment msg wa", seller))
		return "", fmt.Errorf("source not found for seller: %s", seller)
	}

	linkData := map[string]interface{}{
		"cta": map[string]interface{}{
			"name":     "OrderingModule",
			"nav_type": "Redirect to Screen",
			"params": map[string]interface{}{
				"screen": "OrderDetails",
				"params": map[string]interface{}{
					"seller":        seller,
					"source":        source,
					"order_id":      orderID,
					"retry_payment": true,
				},
			},
		},
		"non_firebase_link": false,
	}

	link, err := GenerateDynamicLink(linkData)
	if err != nil {
		fmt.Println("error generating dynamic link", err)
		return "", fmt.Errorf("error generating dynamic link: %v", err)
	}

	if onlyLink {
		return link, nil
	}

	template := "ordering_payment_new"
	// Create the message structure
	message := RequestBody{
		Data: MessageData{
			To:           phoneNumber,
			Template:     template,
			CampaignName: template,
			Priority:     0,
			FollowDND:    false,
			Components: []Component{
				{
					Type: "body",
					Parameters: []Parameter{
						{
							Type: "text",
							Data: orderID,
						},
						{
							Type: "text",
							Data: orderValue,
						},
						{
							Type: "text",
							Data: link,
						},
						{
							Type: "text",
							Data: "1",
						},
					},
				},
			},
		},
	}

	// Convert the message to JSON
	jsonData, err := json.Marshal(message)
	if err != nil {
		return "", fmt.Errorf("error marshaling JSON: %v", err)
	}

	// Create the HTTP request
	req, err := http.NewRequest("POST", whatsAppURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("error creating request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return link, nil
}

func SendGenericWhatsAppMessage(template, phoneNumber string, body []string, header []string) error {
	whatsAppURL := "https://asia-south1-op-d2r.cloudfunctions.net/WA-Message-Publisher"

	component := []Component{}
	if len(body) > 0 {
		parameters := []Parameter{}
		for _, value := range body {
			parameters = append(parameters, Parameter{
				Type: "text",
				Data: value,
			})
		}
		component = append(component, Component{
			Type:       "body",
			Parameters: parameters,
		})
	}

	if len(header) > 0 {
		parameters := []Parameter{}
		for _, value := range header {
			parameters = append(parameters, Parameter{
				Type: "text",
				Data: value,
			})
		}
		component = append(component, Component{
			Type:       "header",
			Parameters: parameters,
		})
	}

	message := RequestBody{
		Data: MessageData{
			To:           phoneNumber,
			Template:     template,
			CampaignName: template,
			Priority:     0,
			FollowDND:    false,
			Components:   component,
		},
	}

	// Convert the message to JSON
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("error marshaling JSON: %v", err)
	}

	// Create the HTTP request
	req, err := http.NewRequest("POST", whatsAppURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("error creating request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return nil
}

func SendOrderCancelledMessage(phoneNumber string, orderValue int, orderID string) error {
	whatsAppURL := "https://asia-south1-op-d2r.cloudfunctions.net/WA-Message-Publisher"
	template := "order_cancel_msg"
	// Create the message structure
	message := RequestBody{
		Data: MessageData{
			To:           phoneNumber,
			Template:     template,
			CampaignName: template,
			Priority:     0,
			FollowDND:    false,
			Components: []Component{
				{
					Type: "body",
					Parameters: []Parameter{
						{
							Type: "text",
							Data: orderID,
						},
						{
							Type: "text",
							Data: fmt.Sprintf("%d", orderValue),
						},
					},
				},
			},
		},
	}
	// Convert the message to JSON
	jsonData, err := json.Marshal(message)

	if err != nil {
		return fmt.Errorf("error marshaling JSON: %v", err)
	}
	// Create the HTTP request
	req, err := http.NewRequest("POST", whatsAppURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("error creating request: %v", err)
	}
	// Set headers
	req.Header.Set("Content-Type", "application/json")
	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()
	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}
	return nil
}

func SendGenericWhatsAppMessageWithDynamicLink(template, phoneNumber string, body []string, header []string, dynamicLinkEmbeddement string, dynamicLinkData map[string]interface{}) error {

	if dynamicLinkEmbeddement != "" && dynamicLinkData == nil {
		return fmt.Errorf("dynamicLinkData cannot be nil when dynamicLinkEmbeddement is provided")
	}

	whatsAppURL := "https://asia-south1-op-d2r.cloudfunctions.net/WA-Message-Publisher"
	component := []Component{}

	headerParameters := []Parameter{}

	if len(body) > 0 {
		for _, value := range body {
			headerParameters = append(headerParameters, Parameter{
				Type: "text",
				Data: value,
			})
		}
		component = append(component, Component{
			Type:       "body",
			Parameters: headerParameters,
		})
	}

	// dynamicLinkEmbeddement where will be dynamic link put
	if dynamicLinkEmbeddement != "" {
		link, err := GenerateDynamicLink(dynamicLinkData)
		if err != nil {
			return fmt.Errorf("error generating dynamic link: %v", err)
		}

		link, _ = extractURLPath(link)

		if dynamicLinkEmbeddement == "button" {
			component = append(component, Component{
				Type:    "button",
				SubType: "url",
				Index:   "0",
				Parameters: []Parameter{
					{
						Type: "text",
						Data: link,
					},
				},
			},
			)
		} else if dynamicLinkEmbeddement == "link" {
			headerParameters = append(headerParameters, Parameter{
				Type: "text",
				Data: link,
			})
			component = []Component{
				{
					Type:       "body",
					Parameters: headerParameters,
				},
			}
		} else {
			return fmt.Errorf("dynamicLinkEmbeddement must be either 'button' or 'link'")
		}
	}

	if len(header) > 0 {
		parameters := []Parameter{}
		for _, value := range header {
			parameters = append(parameters, Parameter{
				Type: "text",
				Data: value,
			})
		}
		component = append(component, Component{
			Type:       "header",
			Parameters: parameters,
		})
	}

	message := RequestBody{
		Data: MessageData{
			To:           phoneNumber,
			Template:     template,
			CampaignName: template,
			Priority:     0,
			FollowDND:    false,
			Components:   component,
		},
	}

	// Convert the message to JSON
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("error marshaling JSON: %v", err)
	}

	// Create the HTTP request
	req, err := http.NewRequest("POST", whatsAppURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("error creating request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return nil
}

func SendGenericWhatsAppMessageWithDynamicLinkFRFR(template, phoneNumber string, body []string, header []string, dynamicLinkConfigs []DynamicLinkConfig) error {
	whatsAppURL := "https://asia-south1-op-d2r.cloudfunctions.net/WA-Message-Publisher"
	component := []Component{}

	// Build body component if body parameters exist
	if len(body) > 0 {
		bodyParameters := []Parameter{}
		for _, value := range body {
			bodyParameters = append(bodyParameters, Parameter{
				Type: "text",
				Data: value,
			})
		}
		component = append(component, Component{
			Type:       "body",
			Parameters: bodyParameters,
		})
	}

	// Process dynamic link configurations
	if len(dynamicLinkConfigs) > 0 {
		for _, config := range dynamicLinkConfigs {
			link, err := GenerateDynamicLink(config.Data)
			if err != nil {
				return fmt.Errorf("error generating dynamic link: %v", err)
			}

			link, _ = extractURLPath(link)

			if config.EmbeddingType == "button" {
				component = append(component, Component{
					Type:    "button",
					SubType: "url",
					Index:   config.ButtonIndex,
					Parameters: []Parameter{
						{
							Type: "text",
							Data: link,
						},
					},
				})
			} else if config.EmbeddingType == "link" {
				// Find existing body component and append the link to it
				found := false
				for i, comp := range component {
					if comp.Type == "body" {
						component[i].Parameters = append(component[i].Parameters, Parameter{
							Type: "text",
							Data: link,
						})
						found = true
						break
					}
				}

				// If no body component exists, create one with the link
				if !found {
					component = append(component, Component{
						Type: "body",
						Parameters: []Parameter{
							{
								Type: "text",
								Data: link,
							},
						},
					})
				}
			} else {
				return fmt.Errorf("dynamicLinkEmbeddement must be either 'button' or 'link'")
			}
		}
	}

	// Add header component if header parameters exist
	if isHeaderNonEmpty(header) {
		headerParameters := []Parameter{}
		for _, value := range header {
			headerParameters = append(headerParameters, Parameter{
				Type: "text",
				Data: value,
			})
		}
		component = append(component, Component{
			Type:       "header",
			Parameters: headerParameters,
		})
	}

	message := RequestBody{
		Data: MessageData{
			To:           phoneNumber,
			Template:     template,
			CampaignName: template,
			Priority:     0,
			FollowDND:    false,
			Components:   component,
		},
	}

	// Convert the message to JSON
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("error marshaling JSON: %v", err)
	}

	// Create the HTTP request
	req, err := http.NewRequest("POST", whatsAppURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("error creating request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return nil
}

func isHeaderNonEmpty(header []string) bool {
	for _, h := range header {
		if strings.TrimSpace(h) != "" {
			return true
		}
	}
	return false
}
