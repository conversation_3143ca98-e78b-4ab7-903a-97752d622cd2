package slack

import (
	"fmt"

	"github.com/ashwanthkumar/slack-go-webhook"
)

func SendSlackMessage(message string) {
	webhookUrl := "*********************************************************************************"
	// attachment1 := slack.Attachment{}
	// attachment1.AddField(slack.Field{Title: "Author", Value: "<PERSON>wan<PERSON> Kumar"})
	// attachment1.AddAction(slack.Action{Type: "button", Text: "Book flights 🛫", Url: "https://flights.example.com/book/r123456", Style: "primary"})
	// attachment1.AddAction(slack.Action{Type: "button", Text: "Cancel", Url: "https://flights.example.com/abandon/r123456", Style: "danger"})
	payload := slack.Payload{
		Text:     message,
		Username: "automation",
		Channel:  "#alerts",
		// IconEmoji:   ":monkey_face:",
		// Attachments: []slack.Attachment{attachment1},
	}
	// fmt.Println("payload = ", payload)
	// fmt.Println("webhookUrl = ", webhookUrl)
	var err []error
	err = slack.Send(webhookUrl, "", payload)
	if len(err) > 0 {
		fmt.Printf("error: %s\n", err)
	}
}

func SendSlackMessageOnOrderingOperations(message string) {
	webhookUrl := "*********************************************************************************"
	// attachment1 := slack.Attachment{}
	// attachment1.AddField(slack.Field{Title: "Author", Value: "Ashwanth Kumar"})
	// attachment1.AddAction(slack.Action{Type: "button", Text: "Book flights 🛫", Url: "https://flights.example.com/book/r123456", Style: "primary"})
	// attachment1.AddAction(slack.Action{Type: "button", Text: "Cancel", Url: "https://flights.example.com/abandon/r123456", Style: "danger"})
	payload := slack.Payload{
		Text:     message,
		Username: "automation",
		Channel:  "#ordering-operations",
		// IconEmoji:   ":monkey_face:",
		// Attachments: []slack.Attachment{attachment1},
	}
	// fmt.Println("payload = ", payload)
	// fmt.Println("webhookUrl = ", webhookUrl)
	var err []error
	err = slack.Send(webhookUrl, "", payload)
	if len(err) > 0 {
		fmt.Printf("error: %s\n", err)
	}
}

func SendSlackMessageOnKC(message string) {
	webhookUrl := "*********************************************************************************"
	// attachment1 := slack.Attachment{}
	// attachment1.AddField(slack.Field{Title: "Author", Value: "Ashwanth Kumar"})
	// attachment1.AddAction(slack.Action{Type: "button", Text: "Book flights 🛫", Url: "https://flights.example.com/book/r123456", Style: "primary"})
	// attachment1.AddAction(slack.Action{Type: "button", Text: "Cancel", Url: "https://flights.example.com/abandon/r123456", Style: "danger"})
	payload := slack.Payload{
		Text:     message,
		Username: "automation",
		Channel:  "#alerts",
		// IconEmoji:   ":monkey_face:",
		// Attachments: []slack.Attachment{attachment1},
	}
	// fmt.Println("payload = ", payload)
	// fmt.Println("webhookUrl = ", webhookUrl)
	var err []error
	err = slack.Send(webhookUrl, "", payload)
	if len(err) > 0 {
		fmt.Printf("error: %s\n", err)
	}
}

func SendSlackMessageOnSellerUpdates(message string) {
	webhookUrl := "*********************************************************************************"
	// attachment1 := slack.Attachment{}
	// attachment1.AddField(slack.Field{Title: "Author", Value: "Ashwanth Kumar"})
	// attachment1.AddAction(slack.Action{Type: "button", Text: "Book flights 🛫", Url: "https://flights.example.com/book/r123456", Style: "primary"})
	// attachment1.AddAction(slack.Action{Type: "button", Text: "Cancel", Url: "https://flights.example.com/abandon/r123456", Style: "danger"})
	payload := slack.Payload{
		Text:     message,
		Username: "automation",
		Channel:  "#seller-updates",
		// IconEmoji:   ":monkey_face:",
		// Attachments: []slack.Attachment{attachment1},
	}
	// fmt.Println("payload = ", payload)
	// fmt.Println("webhookUrl = ", webhookUrl)
	var err []error
	err = slack.Send(webhookUrl, "", payload)
	if len(err) > 0 {
		fmt.Printf("error: %s\n", err)
	}
}

func SendSlackDebugMessage(message string) {
	webhookUrl := "*********************************************************************************"
	// attachment1 := slack.Attachment{}
	// attachment1.AddField(slack.Field{Title: "Author", Value: "Ashwanth Kumar"})
	// attachment1.AddAction(slack.Action{Type: "button", Text: "Book flights 🛫", Url: "https://flights.example.com/book/r123456", Style: "primary"})
	// attachment1.AddAction(slack.Action{Type: "button", Text: "Cancel", Url: "https://flights.example.com/abandon/r123456", Style: "danger"})
	payload := slack.Payload{
		Text:     message,
		Username: "automation",
		Channel:  "#debug-alerts",
		IconEmoji:   "🙉",
		// Attachments: []slack.Attachment{attachment1},
	}
	var err []error
	err = slack.Send(webhookUrl, "", payload)
	if len(err) > 0 {
		fmt.Printf("error: %s\n", err)
	}
}
