package shipway

import "net/http"

const (
	SHIPWAY_BASE_URL  = "https://shipway.in/api"
	AUTH              = "AUTH"
	PUSH_ORDER        = "PUSH_ORDER"
	GET_ORDER_DETAILS = "GET_ORDER_DETAILS"
)

var SHIPWAY_API_ENDPOINT = map[string]string{
	"AUTH":              "/authenticateUser",
	"GET_CARRIER":       "/carriers",
	"PUSH_ORDER":        "/PushOrderData",
	"GET_ORDER_DETAILS": "/getOrderShipmentDetails",
}

var SHIPWAY_API_TYPE = map[string]string{
	"AUTH":              http.MethodPost,
	"GET_CARRIER":       http.MethodGet,
	"PUSH_ORDER":        http.MethodPost,
	"GET_ORDER_DETAILS": http.MethodPost,
}
