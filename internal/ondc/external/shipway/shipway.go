package shipway

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"kc/internal/ondc/config"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/repositories/sqlRepo"
	"net/http"
	urll "net/url"
	"time"
)

var shipwayConfig AppShipwayConfig
var SqlRespository *sqlRepo.Repository

func authenticateShipway() ([]byte, error) {
	url := SHIPWAY_BASE_URL + SHIPWAY_API_ENDPOINT[AUTH]
	method := SHIPWAY_API_TYPE[AUTH]

	var buf bytes.Buffer
	err := json.NewEncoder(&buf).Encode(shipwayConfig)
	if err != nil {
		return nil, err
	}

	client := &http.Client{}
	req, err := http.NewRequest(method, url, &buf)

	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	fmt.Println(string(body))
	return body, err

}

func EnrichRequest(request interface{}) (map[string]interface{}, error) {
	// Convert the original request to map
	originalData, err := structToMap(request)
	if err != nil {
		return nil, fmt.Errorf("error converting struct to map: %w", err)
	}

	// Create enriched request with auth
	enrichedRequest := make(map[string]interface{})

	// Add auth fields
	enrichedRequest["username"] = shipwayConfig.Username
	enrichedRequest["password"] = shipwayConfig.Password

	// Add all fields from original request
	for k, v := range originalData {
		enrichedRequest[k] = v
	}

	return enrichedRequest, nil
}

func structToMap(obj interface{}) (map[string]interface{}, error) {
	data, err := json.Marshal(obj)
	if err != nil {
		return nil, err
	}

	var mapData map[string]interface{}
	err = json.Unmarshal(data, &mapData)
	return mapData, err
}

// {"status": "Failed", "details": "Your shipway balance is low. Please recharge to resume services", "message": "Not Aurthorized"}
func checkBalance(apiResponse []byte) {
	mp := make(map[string]interface{})
	err := json.Unmarshal(apiResponse, &mp)
	if err != nil {
		slack.SendSlackMessage("Error: not able to unmarshal the API response")
		return
	}

	status, ok := mp["status"].(string)
	if !ok {
		slack.SendSlackMessage("Error: status field missing or not a string")
		return
	}

	if status != "Success" {
		details, _ := mp["details"].(string)
		slack.SendSlackMessage("Error status: " + details)
		return
	}
}

func insertApiLogs(api string, request interface{}, apiResponse []byte, endPoint string, statusCode int) uint64 {
	requestObject, err := json.Marshal(request)
	if err != nil {
		requestObject = []byte(fmt.Sprintf(`{"error": "%s"}`, err))
	}
	if api == PUSH_ORDER {
		dbResp, err := SqlRespository.Create(&dao.ShipwayAPILogs{
			Request:    requestObject,
			Response:   apiResponse,
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
			StatusCode: statusCode,
			Endpoint:   endPoint,
		})
		if err != nil {
			fmt.Println("error inserting api logs ", err)
		}
		insertedRequestObject := dbResp.(*dao.ShipwayAPILogs)
		go checkBalance(apiResponse)
		return insertedRequestObject.ID
	} else if api == GET_ORDER_DETAILS {
		dbResp, err := SqlRespository.Create(&dao.ShipwayFetchShipmentLogs{
			Request:   requestObject,
			Response:  apiResponse,
			CreatedAt: time.Now(),
		})
		if err != nil {
			fmt.Println("error inserting api logs ", err)
		}
		insertedRequestObject := dbResp.(*dao.ShipwayFetchShipmentLogs)
		return insertedRequestObject.ID
	}
	return 0
}

// CallShipwayAPI is a api handler function
func CallShipwayAPI(API string, requestObject interface{}, params map[string]interface{}) ([]byte, int, uint64, error) {
	url := SHIPWAY_BASE_URL + SHIPWAY_API_ENDPOINT[API]
	method := SHIPWAY_API_TYPE[API]

	queryParams := urll.Values{}
	for key, value := range params {
		queryParams.Add(key, fmt.Sprintf("%v", value))
	}
	url = url + "?" + queryParams.Encode()

	requestObject, err := EnrichRequest(requestObject)
	if err != nil {
		fmt.Println("err in calling shipway api in enrichRequest", err)
		return nil, 0, 0, err
	}
	var buf bytes.Buffer
	err = json.NewEncoder(&buf).Encode(requestObject)
	if err != nil {
		fmt.Println("err in calling shipway api in new encoder", err)
		return nil, 0, 0, err
	}

	client := &http.Client{}
	req, err := http.NewRequest(method, url, &buf)

	if err != nil {
		fmt.Println("err in calling shipway api while creating new request", err)
		return nil, 0, 0, err
	}

	req.Header.Add("Content-Type", "application/json")
	res, err := client.Do(req)
	if err != nil {
		fmt.Println("err in calling shipway api", err)
		return nil, 0, 0, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		insertApiLogs(API, []byte(fmt.Sprintf(`{"error": "%s"}`, err)), body, SHIPWAY_API_ENDPOINT[API], res.StatusCode)
		return nil, 0, 0, err
	}
	insertID := insertApiLogs(API, requestObject, body, SHIPWAY_API_ENDPOINT[API], res.StatusCode)
	return body, res.StatusCode, insertID, nil
}

func InitShipway(swConfig config.ShipwayConfig) error {
	shipwayConfig = AppShipwayConfig{
		Username: swConfig.Username,
		Password: swConfig.Password,
	}
	if shipwayConfig.Password == "" || shipwayConfig.Username == "" {
		return errors.New("issue reading shipway config credentials")
	}
	_, err := authenticateShipway()
	if err != nil {
		return err
	}
	return nil
}
