package shipway

type AppShipwayConfig struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type ShipwayCourierData struct {
	ID          string `json:"id"`
	CourierName string `json:"courier_name"`
	Image       string `json:"image"`
}

type PushOrderDataRequest struct {
	CarrierID    string `json:"carrier_id"`
	AwbNumber    string `json:"awb"`
	OrderID      string `json:"order_id"`
	FirstName    string `json:"first_name"`
	LastName     string `json:"last_name"`
	Email        string `json:"email"`
	Phone        string `json:"phone"`
	Products     string `json:"products"`
	Company      string `json:"company"`
	ShipmentType string `json:"shipment_type"` // @developer keep in mind, 1 is for forward shipment and 2 for reverse shipment
	OrderData    string `json:"order_data"`
}

type PushOrderDataResponse struct {
	Status  string `json:"status"`
	Message string `json:"message"`
}

// GetOrderShipmentDetails is the request schema for fetching order shipmentDetails
type GetOrderShipmentDetails struct {
	OrderID string `json:"order_id"`
}
