package unicommerce

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"kc/internal/ondc/config"
	"net/http"
	urll "net/url"
	"time"
)

var (
	unicommerceAccessTokenMap = make(map[string]AuthorizationResponse)
	unicommerceConfig         = make(map[string]AppUnicommerceConfig)
)

func callAuthAPI(oms, API string, requestObject interface{}, params map[string]interface{}) ([]byte, error) {
	url := unicommerceConfig[oms].BaseUrl + UNICOMMERCE_API_ENDPOINT[API]
	method := UNICOMMERCE_API_TYPE[API]

	queryParams := urll.Values{}
	for key, value := range params {
		queryParams.Add(key, fmt.Sprintf("%v", value))
	}

	if queryParams.Encode() != "" {
		url = url + "?" + queryParams.Encode()
	}

	var buf bytes.Buffer
	if requestObject != nil {
		err := json.NewEncoder(&buf).Encode(requestObject)
		if err != nil {
			return nil, err
		}
	}

	client := &http.Client{}
	req, err := http.NewRequest(method, url, &buf)
	req.Header.Set("Content-Type", "application/json")

	if err != nil {
		return nil, err
	}

	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}

func CallUnicommerceAPI(oms, API string, requestObject interface{}, params map[string]interface{}) ([]byte, error, *int) {
	url := unicommerceConfig[oms].BaseUrl + UNICOMMERCE_API_ENDPOINT[API]
	method := UNICOMMERCE_API_TYPE[API]

	if params != nil {
		queryParams := urll.Values{}
		for key, value := range params {
			queryParams.Add(key, fmt.Sprintf("%v", value))
		}
		url = url + "?" + queryParams.Encode()
	}

	var buf bytes.Buffer
	if requestObject != nil {
		err := json.NewEncoder(&buf).Encode(requestObject)
		if err != nil {
			return nil, err, nil
		}
	}

	client := &http.Client{}
	req, err := http.NewRequest(method, url, &buf)

	if err != nil {
		return nil, err, nil
	}
	req.Header.Add("Content-Type", "application/json")
	token, err := getAuthToken(oms)
	if err != nil {
		return nil, err, nil
	}
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", *token))
	// req.Header.Add("Facility", unicommerceConfig[oms].Facility)

	res, err := client.Do(req)
	if err != nil {
		return nil, err, nil
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err, nil
	}
	return body, nil, &res.StatusCode
}

func getAuthTokenFromAPI(oms string) (*string, error) {
	config := unicommerceConfig[oms]
	params := map[string]interface{}{
		"username":   config.Username,
		"password":   config.Password,
		"client_id":  config.ClientID,
		"grant_type": config.GrantType,
	}
	authResponse, err := callAuthAPI(oms, "GET_TOKEN", nil, params)
	if err != nil {
		return nil, err
	}
	parsedAuthResponse := AuthorizationResponse{}

	err = json.Unmarshal(authResponse, &parsedAuthResponse)
	if err != nil {
		return nil, err
	}
	unicommerceAccessTokenMap[oms] = AuthorizationResponse{
		AccessToken:  parsedAuthResponse.AccessToken,
		TokenType:    parsedAuthResponse.TokenType,
		RefreshToken: parsedAuthResponse.RefreshToken,
		ExpiresIn:    int(time.Now().Unix()) + parsedAuthResponse.ExpiresIn,
		Scope:        parsedAuthResponse.Scope,
	}
	return &parsedAuthResponse.AccessToken, nil
}

func getAuthToken(oms string) (*string, error) {
	if tokenTesp, ok := unicommerceAccessTokenMap[oms]; ok {
		if tokenTesp.ExpiresIn > int(time.Now().Unix()) {
			return &tokenTesp.AccessToken, nil
		} else {
			return getAuthTokenFromAPI(oms)
		}
	}
	return getAuthTokenFromAPI(oms)
}

func GetSellerUnicommerceFacility(oms string) string {
	return unicommerceConfig[oms].SellerFacility
}

func GetSellerUnicommerChannel(oms string) *string {
	channel := unicommerceConfig[oms].Channel
	if channel == nil && oms == "mangalam" {
		mangalamChannelName := "CUSTOM"
		channel = &mangalamChannelName
	}
	return channel
}

func InitUnicommerce(ucConfig []config.UnicommerceConfig) error {
	for _, config := range ucConfig {
		var channel *string
		if config.Channel != "" {
			channel = &config.Channel
		}
		unicommerceConfig[config.OMS] = AppUnicommerceConfig{
			Username:       config.Username,
			Password:       config.Password,
			BaseUrl:        config.BaseUrl,
			ClientID:       config.ClientID,
			GrantType:      config.GrantType,
			OMS:            config.OMS,
			Facility:       config.Facility,
			SellerFacility: config.SellerFacility,
			Channel:        channel,
		}
	}
	return nil
}
