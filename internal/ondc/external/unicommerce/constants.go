package unicommerce

import "net/http"

var UNICOMMERCE_API_ENDPOINT = map[string]string{
	"GET_TOKEN":         "/oauth/token",
	"CREATE_SALE_ORDER": "/services/rest/v1/oms/saleOrder/create",
	"GET_ORDER_DETAILS": "/services/rest/v1/oms/saleorder/get",
}

var UNICOMMERCE_API_TYPE = map[string]string{
	"GET_TOKEN":         http.MethodGet,
	"CREATE_SALE_ORDER": http.MethodPost,
	"GET_ORDER_DETAILS": http.MethodPost,
}
