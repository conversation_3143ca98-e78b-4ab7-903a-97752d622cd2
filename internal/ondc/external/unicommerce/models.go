package unicommerce

type AppUnicommerceConfig struct {
	Username       string  `json:"username"`
	Password       string  `json:"password"`
	OMS            string  `json:"oms"`
	ClientID       string  `json:"client_id"`
	GrantType      string  `json:"grant_type"`
	BaseUrl        string  `json:"base_url"`
	Facility       string  `json:"facility"`
	SellerFacility string  `json:"seller_facility"`
	Channel        *string `json:"channel,omitempty"`
}

type AuthorizationResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"`
	Scope        string `json:"scope"`
}

type CustomField struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type UnicommerceCreateSaleOrderResponse struct {
	Successful         bool                                        `json:"successful"`
	Message            string                                      `json:"message"`
	Errors             []UnicommerceCreateSaleOrderResponseError   `json:"errors"`
	Warnings           []UnicommerceCreateSaleOrderResponseWarning `json:"warnings"`
	SaleOrderDetailDTO SaleOrderDetailDTO                          `json:"saleOrderDetailDTO"`
}

// SaleOrderRequest represents the top-level request structure
type UnicommerceCreateSaleOrderRequest struct {
	SaleOrder SaleOrder `json:"saleOrder"`
}

// SaleOrder represents the sale order details
type SaleOrder struct {
	Code                       string                      `json:"code"`
	DisplayOrderCode           string                      `json:"displayOrderCode"`
	DisplayOrderDateTime       string                      `json:"displayOrderDateTime"`
	CustomerCode               string                      `json:"customerCode"`
	CustomerName               string                      `json:"customerName"`
	CustomerGSTIN              *string                     `json:"customerGSTIN"`
	Channel                    *string                     `json:"channel,omitempty"`
	CashOnDelivery             bool                        `json:"cashOnDelivery"`
	NotificationMobile         string                      `json:"notificationMobile"`
	PaymentInstrument          string                      `json:"paymentInstrument"`
	AdditionalInfo             string                      `json:"additionalInfo"`
	ThirdPartyShipping         bool                        `json:"thirdPartyShipping"`
	Addresses                  []UnicommerceAddress        `json:"addresses"`
	BillingAddress             UnicommerceAddressReference `json:"billingAddress"`
	ShippingAddress            UnicommerceAddressReference `json:"shippingAddress"`
	SaleOrderItems             []UnicommerceSaleOrderItem  `json:"saleOrderItems"`
	CustomFieldValues          []CustomField               `json:"customFieldValues"`
	CurrencyCode               string                      `json:"currencyCode"`
	TaxExempted                bool                        `json:"taxExempted"`
	CformProvided              bool                        `json:"cformProvided"`
	VerificationRequired       bool                        `json:"verificationRequired"`
	Priority                   int                         `json:"priority"`
	TotalDiscount              float64                     `json:"totalDiscount"`
	TotalShippingCharges       float64                     `json:"totalShippingCharges"`
	TotalCashOnDeliveryCharges float64                     `json:"totalCashOnDeliveryCharges"`
	TotalPrepaidAmount         float64                     `json:"totalPrepaidAmount"`
}

// Address represents an address structure
type UnicommerceAddress struct {
	ID           string  `json:"id"`
	Name         string  `json:"name"`
	AddressLine1 string  `json:"addressLine1"`
	AddressLine2 string  `json:"addressLine2"`
	Latitude     string  `json:"latitude"`
	Longitude    string  `json:"longitude"`
	City         string  `json:"city"`
	State        string  `json:"state"`
	Country      string  `json:"country"`
	Pincode      string  `json:"pincode"`
	Phone        string  `json:"phone"`
	Email        string  `json:"email"`
	Type         *string `json:"type,omitempty"`
}

// UnicommerceAddressReference contains a reference to an address
type UnicommerceAddressReference struct {
	ReferenceID string `json:"referenceId"`
}

// UnicommerceSaleOrderItem represents an individual item in the order
type UnicommerceSaleOrderItem struct {
	ItemSku            string `json:"itemSku"`
	ShippingMethodCode string `json:"shippingMethodCode"`
	Code               string `json:"code"`
	PacketNumber       int    `json:"packetNumber"`
	GiftWrap           bool   `json:"giftWrap"`
	FacilityCode       string `json:"facilityCode"`
	TotalPrice         string `json:"totalPrice"`
	SellingPrice       string `json:"sellingPrice"`
	PrepaidAmount      string `json:"prepaidAmount"`
	Discount           string `json:"discount"`
	ShippingCharges    string `json:"shippingCharges"`
}

// Error represents an error in the response
type UnicommerceCreateSaleOrderResponseError struct {
	Code        int                    `json:"code"`
	FieldName   string                 `json:"fieldName"`
	Description string                 `json:"description"`
	Message     string                 `json:"message"`
	ErrorParams map[string]interface{} `json:"errorParams"`
}

// Warning represents a warning in the response
type UnicommerceCreateSaleOrderResponseWarning struct {
	Code        int    `json:"code"`
	Message     string `json:"message"`
	Description string `json:"description"`
}

// SaleOrderDetailDTO represents the sale order details in the response
type SaleOrderDetailDTO struct {
	Code                 string                         `json:"code"`
	DisplayOrderCode     string                         `json:"displayOrderCode"`
	Channel              string                         `json:"channel"`
	DisplayOrderDateTime int64                          `json:"displayOrderDateTime"`
	Status               string                         `json:"status"`
	Created              int64                          `json:"created"`
	Updated              int64                          `json:"updated"`
	NotificationEmail    string                         `json:"notificationEmail"`
	NotificationMobile   string                         `json:"notificationMobile"`
	CustomerGSTIN        string                         `json:"customerGSTIN"`
	Cod                  bool                           `json:"cod"`
	Priority             int                            `json:"priority"`
	CurrencyCode         string                         `json:"currencyCode"`
	CustomerCode         string                         `json:"customerCode"`
	BillingAddress       UnicommerceAddress             `json:"billingAddress"`
	Addresses            []UnicommerceAddress           `json:"addresses"`
	CustomFieldValues    []CustomFieldUnicommerResponse `json:"customFieldValues"`
}

// CustomField represents a custom field with its value and metadata
type CustomFieldUnicommerResponse struct {
	FieldName      string      `json:"fieldName"`
	FieldValue     interface{} `json:"fieldValue"`
	ValueType      string      `json:"valueType"`
	DisplayName    string      `json:"displayName"`
	Required       bool        `json:"required"`
	PossibleValues []string    `json:"possibleValues"`
}

type UnicommerceOrderDetailsResponse struct {
	Successful       bool                                      `json:"successful"`
	Message          *string                                   `json:"message"`
	Errors           []UnicommerceCreateSaleOrderResponseError `json:"errors"`
	Warnings         *string                                   `json:"warnings"`
	SaleOrderDTO     SaleOrderDTO                              `json:"saleOrderDTO"`
	RefreshEnabled   bool                                      `json:"refreshEnabled"`
	FailedOrderFetch bool                                      `json:"failedOrderFetch"`
}

type SaleOrderDTO struct {
	Code                  string                                 `json:"code"`
	DisplayOrderCode      string                                 `json:"displayOrderCode"`
	Channel               string                                 `json:"channel"`
	Source                string                                 `json:"source"`
	DisplayOrderDateTime  int64                                  `json:"displayOrderDateTime"`
	Status                string                                 `json:"status"`
	Created               int64                                  `json:"created"`
	Updated               int64                                  `json:"updated"`
	FulfillmentTat        *int64                                 `json:"fulfillmentTat"`
	NotificationEmail     string                                 `json:"notificationEmail"`
	NotificationMobile    string                                 `json:"notificationMobile"`
	CustomerGSTIN         *string                                `json:"customerGSTIN"`
	ChannelProcessingTime *int64                                 `json:"channelProcessingTime"`
	Cod                   bool                                   `json:"cod"`
	ThirdPartyShipping    bool                                   `json:"thirdPartyShipping"`
	Priority              int                                    `json:"priority"`
	CurrencyCode          string                                 `json:"currencyCode"`
	CustomerCode          *string                                `json:"customerCode"`
	BillingAddress        UnicommerceAddress                     `json:"billingAddress"`
	Addresses             []UnicommerceAddress                   `json:"addresses"`
	ShippingPackages      []UnicommerceShippingPackage           `json:"shippingPackages"`
	SaleOrderItems        []UnicommerceOrderDetailsSaleOrderItem `json:"saleOrderItems"`
	Returns               []interface{}                          `json:"returns"`
	CustomFieldValues     []CustomFieldUnicommerResponse         `json:"customFieldValues"`
	Cancellable           bool                                   `json:"cancellable"`
	ReversePickable       bool                                   `json:"reversePickable"`
	PacketConfigurable    bool                                   `json:"packetConfigurable"`
	CFormProvided         bool                                   `json:"cFormProvided"`
	TotalDiscount         *float64                               `json:"totalDiscount"`
	TotalShippingCharges  *float64                               `json:"totalShippingCharges"`
	AdditionalInfo        *string                                `json:"additionalInfo"`
	PaymentInstrument     *string                                `json:"paymentInstrument"`
	PaymentDetail         *string                                `json:"paymentDetail"`
	SaleOrderMetadata     []interface{}                          `json:"saleOrderMetadata"`
}

type UnicommerceShippingPackage struct {
	Code                 string                                    `json:"code"`
	ChannelShipmentCode  *string                                   `json:"channelShipmentCode"`
	SaleOrderCode        string                                    `json:"saleOrderCode"`
	Channel              string                                    `json:"channel"`
	Status               string                                    `json:"status"`
	ShippingPackageType  string                                    `json:"shippingPackageType"`
	ShippingProvider     string                                    `json:"shippingProvider"`
	ShippingCourier      string                                    `json:"shippingCourier"`
	ShippingMethod       string                                    `json:"shippingMethod"`
	TrackingNumber       string                                    `json:"trackingNumber"`
	TrackingStatus       string                                    `json:"trackingStatus"`
	TrackingLink         *string                                   `json:"trackingLink"`
	CourierStatus        string                                    `json:"courierStatus"`
	EstimatedWeight      float64                                   `json:"estimatedWeight"`
	ActualWeight         float64                                   `json:"actualWeight"`
	Customer             string                                    `json:"customer"`
	Created              int64                                     `json:"created"`
	Updated              int64                                     `json:"updated"`
	Dispatched           *int64                                    `json:"dispatched"`
	Delivered            *int64                                    `json:"delivered"`
	Invoice              int                                       `json:"invoice"`
	InvoiceCode          string                                    `json:"invoiceCode"`
	InvoiceDisplayCode   string                                    `json:"invoiceDisplayCode"`
	InvoiceDate          int64                                     `json:"invoiceDate"`
	NoOfItems            int                                       `json:"noOfItems"`
	City                 string                                    `json:"city"`
	CollectableAmount    float64                                   `json:"collectableAmount"`
	CollectedAmount      *float64                                  `json:"collectedAmount"`
	PaymentReconciled    bool                                      `json:"paymentReconciled"`
	ShippingManifestCode string                                    `json:"shippingManifestCode"`
	Items                map[string]UnicommerceShippingPackageItem `json:"items"`
	ShippingLabelLink    string                                    `json:"shippingLabelLink"`
	Irn                  *string                                   `json:"irn"`
	AdditionalInfo       *string                                   `json:"additionalInfo"`
}

type UnicommerceShippingPackageItem struct {
	ItemSku          string  `json:"itemSku"`
	ItemName         string  `json:"itemName"`
	ItemTypeImageUrl *string `json:"itemTypeImageUrl"`
	ItemTypePageUrl  *string `json:"itemTypePageUrl"`
	Quantity         int     `json:"quantity"`
}

type UnicommerceOrderDetailsSaleOrderItem struct {
	ID                  int     `json:"id"`
	ShippingPackageCode string  `json:"shippingPackageCode"`
	StatusCode          string  `json:"statusCode"`
	ItemName            string  `json:"itemName"`
	ItemSku             string  `json:"itemSku"`
	SellingPrice        float64 `json:"sellingPrice"`
}
