package razorpay

import "net/http"

const (
	razorpayAPIBaseURL = "https://api.razorpay.com/v1"
)

type RazorpayGateway struct {
	keyID         string
	keySecret     string
	webhookSecret string
	client        *http.Client
}

type RazorpayOrderRequest struct {
	Amount   int               `json:"amount"`
	Currency string            `json:"currency"`
	Receipt  string            `json:"receipt"`
	Notes    map[string]string `json:"notes"`
}

type RazorpayOrderResponse struct {
	ID         string            `json:"id"`
	Entity     string            `json:"entity"`
	Amount     int               `json:"amount"`
	AmountPaid int               `json:"amount_paid"`
	AmountDue  int               `json:"amount_due"`
	Currency   string            `json:"currency"`
	Receipt    string            `json:"receipt"`
	OfferID    *string           `json:"offer_id"`
	Status     string            `json:"status"`
	Attempts   int               `json:"attempts"`
	Notes      map[string]string `json:"notes"`
	CreatedAt  int64             `json:"created_at"`
}

type RazorpayError struct {
	Code        string                 `json:"code"`
	Description string                 `json:"description"`
	Source      string                 `json:"source"`
	Step        string                 `json:"step"`
	Reason      string                 `json:"reason"`
	Metadata    map[string]interface{} `json:"metadata"`
	Field       string                 `json:"field"`
}

type RazorpayPaymentDetails struct {
	ID             string        `json:"id"`
	Entity         string        `json:"entity"`
	Amount         float64       `json:"amount"`
	Currency       string        `json:"currency"`
	Status         string        `json:"status"`
	Method         string        `json:"method"`
	OrderID        string        `json:"order_id"`
	AmountRefunded int           `json:"amount_refunded"`
	RefundStatus   *string       `json:"refund_status"`
	Captured       bool          `json:"captured"`
	CreatedAt      int64         `json:"created_at"`
	Fee            int           `json:"fee"`
	Tax            int           `json:"tax"`
	ErrorCode      *string       `json:"error_code"`
	ErrorSource    *string       `json:"error_source"`
	ErrorStep      *string       `json:"error_step"`
	ErrorReason    *string       `json:"error_reason"`
	AcquirerData   *AcquirerData `json:"acquirer_data"`
}
type AcquirerData struct {
	RNN string `json:"rnn"`
}

type RazorpayPaymentsResponse struct {
	Entity string                   `json:"entity"`
	Count  int                      `json:"count"`
	Items  []RazorpayPaymentDetails `json:"items"`
}

type RazorpayWebhookType1Request struct {
	Entity    string         `json:"entity"`
	AccountID string         `json:"account_id"`
	Event     string         `json:"event"`
	Contains  []string       `json:"contains"`
	Payload   WebhookPayload `json:"payload"`
	CreatedAt int64          `json:"created_at"`
}

type WebhookPayload struct {
	Payment *PaymentContainer `json:"payment,omitempty"`
	Order   *OrderContainer   `json:"order,omitempty"`
}

type PaymentContainer struct {
	Entity interface{} `json:"entity"`
}

type OrderContainer struct {
	Entity interface{} `json:"entity"`
}
