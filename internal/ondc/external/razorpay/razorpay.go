package razorpay

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"kc/internal/ondc/infrastructure/payments"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"net/http"
	"strconv"
	"strings"
	"time"
)

func NewRazorpayGateway(keyID string, keySecret string, webhookSecret string) *RazorpayGateway {
	return &RazorpayGateway{
		keyID:         keyID,
		keySecret:     keySecret,
		webhookSecret: webhookSecret,
		client:        &http.Client{Timeout: 30 * time.Second},
	}
}

func (r *RazorpayGateway) Name() string {
	return "razorpay"
}

func (r *RazorpayGateway) InitiatePayment(ctx context.Context, req dto.InitiatePaymentRequest) (*dto.InitiatePaymentResponse, error) {
	fmt.Println("InitiatePayment", req.Data)
	if req.Data.PaymentAmount <= 0 {
		return nil, payments.ErrInvalidAmount
	}

	amountInSmallestUnit := int(req.Data.PaymentAmount * 100)

	// Prepare order data
	orderData := RazorpayOrderRequest{
		Amount:   amountInSmallestUnit,
		Currency: "INR",
		Receipt:  req.Data.TransactionID,
		Notes: map[string]string{
			"user_id":     req.UserID,
			"seller":      req.Data.Seller,
			"time":        strconv.FormatInt(time.Now().Unix(), 10),
			"kc_order_id": *req.Data.OrderID,
		},
	}

	// Convert order data to JSON
	orderJSON, err := json.Marshal(orderData)
	if err != nil {
		return nil, fmt.Errorf("error marshaling order data: %w", err)
	}

	// Create the HTTP request
	httpReq, err := http.NewRequestWithContext(
		ctx,
		"POST",
		razorpayAPIBaseURL+"/orders",
		strings.NewReader(string(orderJSON)),
	)
	if err != nil {
		return nil, fmt.Errorf("error creating HTTP request: %w", err)
	}

	// Set headers
	httpReq.SetBasicAuth(r.keyID, r.keySecret)
	httpReq.Header.Set("Content-Type", "application/json")

	// Send the request
	resp, err := r.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error sending request to Razorpay: %w", err)
	}
	defer resp.Body.Close()

	// Read the response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	// Check if the request was successful
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Razorpay API error: %s, code: %d", string(body), resp.StatusCode)
	}

	// Parse the Razorpay order
	var razorpayOrder RazorpayOrderResponse
	if err := json.Unmarshal(body, &razorpayOrder); err != nil {
		return nil, fmt.Errorf("error unmarshaling Razorpay order: %w", err)
	}

	return &dto.InitiatePaymentResponse{
		PaymentAmount:  req.Data.PaymentAmount,
		Gateway:        r.Name(),
		PaymentOrderId: razorpayOrder.ID,
		TransactionId:  req.Data.TransactionID,
	}, nil
}

func (r *RazorpayGateway) VerifyPayment(ctx context.Context, req dto.PaymentData) (*dto.VerifyPaymentResponse, error) {
	fmt.Println("VerifyPayment", req.PaymentID)
	paymentDetails, err := r.GetPaymentDetails(ctx, req.PaymentID)
	if err != nil {
		return nil, err
	}

	// TODO: Validate the payment signature

	return &dto.VerifyPaymentResponse{
		PaymentAmount:  paymentDetails.Amount / 100,
		Gateway:        r.Name(),
		PaymentOrderId: paymentDetails.OrderID,
		PaymentId:      paymentDetails.ID,
		TransactionId:  req.TransactionId,
		Status:         paymentDetails.Status,
		Method:         paymentDetails.Method,
	}, nil

}

// GetPaymentDetails retrieves details of a payment from Razorpay
func (r *RazorpayGateway) GetPaymentDetails(ctx context.Context, paymentID string) (*RazorpayPaymentDetails, error) {
	httpReq, err := http.NewRequestWithContext(
		ctx,
		"GET",
		razorpayAPIBaseURL+"/payments/"+paymentID,
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("error creating HTTP request: %w", err)
	}

	httpReq.SetBasicAuth(r.keyID, r.keySecret)
	resp, err := r.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error sending request to Razorpay: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusNotFound {
			return nil, payments.ErrPaymentNotFound
		}
		return nil, fmt.Errorf("Razorpay API error: %s, code: %d", string(body), resp.StatusCode)
	}

	var razorpayPayment RazorpayPaymentDetails
	if err := json.Unmarshal(body, &razorpayPayment); err != nil {
		return nil, fmt.Errorf("error unmarshaling Razorpay payment: %w", err)
	}

	// Map Razorpay status to our status
	status := mapRazorpayStatus(razorpayPayment.Status)
	razorpayPayment.Status = status

	// Map Razorpay response to our standard PaymentDetails
	return &razorpayPayment, nil
}

// mapRazorpayStatus maps Razorpay payment status to our PaymentStatus
func mapRazorpayStatus(status string) string {
	switch status {
	case "created", "authorized":
		return payments.StatusPending
	case "captured":
		return payments.StatusCompleted
	case "failed":
		return payments.StatusFailed
	case "refunded":
		return payments.StatusRefunded
	default:
		return payments.StatusPending
	}
}

// Implement the GetAllTransactionsForPayment method
func (r *RazorpayGateway) GetAllTransactionsForPayment(ctx context.Context, orderId string) ([]dao.PaymentGatewayRecords, error) {
	url := fmt.Sprintf("%s/orders/%s/payments", razorpayAPIBaseURL, orderId)
	httpReq, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating HTTP request: %w", err)
	}

	httpReq.SetBasicAuth(r.keyID, r.keySecret)
	resp, err := r.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error sending request to Razorpay: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Razorpay API error: %s, code: %d", string(body), resp.StatusCode)
	}

	var paymentsResponse RazorpayPaymentsResponse
	if err := json.Unmarshal(body, &paymentsResponse); err != nil {
		return nil, fmt.Errorf("error unmarshaling Razorpay payments response: %w", err)
	}

	paymentRecords := make([]dao.PaymentGatewayRecords, 0, len(paymentsResponse.Items))
	for i := range paymentsResponse.Items {
		// add in db
		meta, err := json.Marshal(paymentsResponse.Items[i])
		if err != nil {
			return nil, fmt.Errorf("error marshaling meta data: %w")
		}
		paymentRecords = append(paymentRecords, dao.PaymentGatewayRecords{
			ID:             paymentsResponse.Items[i].ID,
			Entity:         paymentsResponse.Items[i].Entity,
			Amount:         paymentsResponse.Items[i].Amount,
			Currency:       paymentsResponse.Items[i].Currency,
			Status:         mapRazorpayStatus(paymentsResponse.Items[i].Status),
			Method:         paymentsResponse.Items[i].Method,
			OrderID:        paymentsResponse.Items[i].OrderID,
			AmountRefunded: paymentsResponse.Items[i].AmountRefunded,
			RefundStatus:   paymentsResponse.Items[i].RefundStatus,
			Captured:       paymentsResponse.Items[i].Captured,
			CreatedAt:      paymentsResponse.Items[i].CreatedAt,
			Fee:            paymentsResponse.Items[i].Fee,
			Tax:            paymentsResponse.Items[i].Tax,
			ErrorCode:      paymentsResponse.Items[i].ErrorCode,
			ErrorSource:    paymentsResponse.Items[i].ErrorSource,
			ErrorStep:      paymentsResponse.Items[i].ErrorStep,
			ErrorReason:    paymentsResponse.Items[i].ErrorReason,
			Meta:           meta,
		})
	}

	return paymentRecords, nil
}

func validateWebhookSignature(body []byte, signature, secret string) bool {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(body)
	expectedSignature := hex.EncodeToString(h.Sum(nil))
	return hmac.Equal([]byte(expectedSignature), []byte(signature))
}

func (r *RazorpayGateway) VerifyWebhookSignature(ctx context.Context, webhookType string) bool {
	//signature := ctx.GetHeader("x-razorpay-signature")
	//body, err := io.ReadAll(ctx.Request.Body)
	//if err != nil {
	//	ctx.Status(http.StatusBadRequest)
	//	return false
	//}

	//isValid := validateWebhookSignature(body, signature, r.webhookSecret)
	//if !isValid {
	//	ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid signature"})
	//	return false
	//}
	return true
}

func (r *RazorpayGateway) HandleWebhookType1(ctx context.Context, payload interface{}, event string) (*dao.PaymentGatewayRecords, error) {
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		fmt.Println("error marshaling payload", err)
		return nil, fmt.Errorf("error marshaling payload: %w", err)
	}

	var paymentsResponseData WebhookPayload
	if err := json.Unmarshal(payloadBytes, &paymentsResponseData); err != nil {
		return nil, fmt.Errorf("error unmarshaling Razorpay payments response: %w", err)
	}

	fmt.Println("HandleWebhookType1", paymentsResponseData.Payment.Entity)
	fmt.Println("HandleWebhookType11", paymentsResponseData.Payment.Entity.(map[string]interface{}))

	paymentsResponse := paymentsResponseData.Payment.Entity
	paymentsResponseMap, ok := paymentsResponse.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("error asserting paymentsResponse to map[string]interface{}")
	}

	var refundStatus, errorCode, errorSource, errorStep, errorReason, rnn *string
	var tax int
	orderId := ""
	if paymentsResponseMap["refund_status"] != nil {
		refundStatusValue := paymentsResponseMap["refund_status"].(string)
		refundStatus = &refundStatusValue
	}
	if paymentsResponseMap["error_code"] != nil {
		errorCodeValue := paymentsResponseMap["error_code"].(string)
		errorCode = &errorCodeValue
	}
	if paymentsResponseMap["error_source"] != nil {
		errorSourceValue := paymentsResponseMap["error_source"].(string)
		errorSource = &errorSourceValue
	}
	if paymentsResponseMap["error_step"] != nil {
		errorStepValue := paymentsResponseMap["error_step"].(string)
		errorStep = &errorStepValue
	}
	if paymentsResponseMap["error_reason"] != nil {
		errorReasonValue := paymentsResponseMap["error_reason"].(string)
		errorReason = &errorReasonValue
	}
	if paymentsResponseMap["tax"] != nil {
		tax = int(paymentsResponseMap["tax"].(float64))
	}
	if paymentsResponseMap["order_id"] != nil {
		orderId = paymentsResponseMap["order_id"].(string)
	}

	if acquirerData, ok := paymentsResponseMap["acquirer_data"].(map[string]interface{}); ok {
		if rrn, ok := acquirerData["rrn"].(string); ok {
			rnn = &rrn
		}
	}

	paymentRecords := dao.PaymentGatewayRecords{
		ID:             paymentsResponseMap["id"].(string),
		Entity:         paymentsResponseMap["entity"].(string),
		Amount:         paymentsResponseMap["amount"].(float64),
		Currency:       paymentsResponseMap["currency"].(string),
		Status:         mapRazorpayStatus(paymentsResponseMap["status"].(string)),
		Method:         paymentsResponseMap["method"].(string),
		OrderID:        orderId,
		AmountRefunded: int(paymentsResponseMap["amount_refunded"].(float64)),
		RefundStatus:   refundStatus,
		Captured:       paymentsResponseMap["captured"].(bool),
		CreatedAt:      int64(paymentsResponseMap["created_at"].(float64)),
		Fee:            int(paymentsResponseMap["fee"].(float64)),
		Tax:            tax,
		ErrorCode:      errorCode,
		ErrorSource:    errorSource,
		ErrorStep:      errorStep,
		ErrorReason:    errorReason,
		Meta:           payloadBytes,
		RNN:            rnn,
	}

	return &paymentRecords, nil
}

func (r *RazorpayGateway) RefundPayment(ctx context.Context, req dto.RefundPaymentRequest) (*dto.RefundPaymentResponse, error) {
	// Prepare the refund URL
	url := fmt.Sprintf("%s/payments/%s/refund", razorpayAPIBaseURL, req.PaymentId)

	//speed := "normal"
	//if req.InstantPayment == nil || *req.InstantPayment {
	//	speed = "optimum"
	//}

	// Marshal the request payload
	payload, err := json.Marshal(map[string]interface{}{
		"amount": req.RefundAmount,
		//"speed":  speed,
	})
	if err != nil {
		return nil, fmt.Errorf("error marshaling refund request payload: %w", err)
	}

	// Create the HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, strings.NewReader(string(payload)))
	if err != nil {
		return nil, fmt.Errorf("error creating HTTP request: %w", err)
	}

	// Set headers and authentication
	httpReq.SetBasicAuth(r.keyID, r.keySecret)
	httpReq.Header.Set("Content-Type", "application/json")

	// Send the request
	resp, err := r.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error sending request to Razorpay: %w", err)
	}
	defer resp.Body.Close()

	// Read the response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	// Check if the request was successful
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Razorpay API error: %s, code: %d", string(body), resp.StatusCode)
	}

	// Parse the response
	var refundResponse dto.RefundPaymentResponse
	if err := json.Unmarshal(body, &refundResponse); err != nil {
		return nil, fmt.Errorf("error unmarshaling refund response: %w", err)
	}

	return &refundResponse, nil
}
