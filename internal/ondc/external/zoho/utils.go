package zoho

import (
	"strings"
)

func GetEndpoint(endpoint string, params map[string]string) *string {
	if endpoint == CREATE_TICKET {
		endpoint := ZOHO_API_ENDPOINT[endpoint]
		return &endpoint
	} else if endpoint == CREATE_CONTACT {
		endpoint := ZOHO_API_ENDPOINT[endpoint]
		return &endpoint
	} else if endpoint == CREATE_COMMENT {
		_, ok := params["ticket_id"]
		if !ok {
			return nil
		}
		endpoint := strings.Replace(ZOHO_API_ENDPOINT[endpoint], ":ticketID", params["ticket_id"], 1)
		return &endpoint
	} else if endpoint == CREATE_COMMENT_ATTACHMENT {
		_, ok := params["ticket_id"]
		if !ok {
			return nil
		}
		endpoint := strings.Replace(ZOHO_API_ENDPOINT[endpoint], ":ticketID", params["ticket_id"], 1)
		return &endpoint
	}
	return &endpoint
}
