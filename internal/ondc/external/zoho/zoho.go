package zoho

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"kc/internal/ondc/config"
	"kc/internal/ondc/log"
	"kc/internal/ondc/models/dao"
	"net/http"
	urll "net/url"
	"time"
)

// NewClient creates a new Zoho client
func NewClient(config *[]config.ZohoConfig) *Client {
	configs := []ZohoConfig{}
	for _, cfg := range *config {
		configs = append(configs, ZohoConfig{
			ClientID:       cfg.ClientID,
			ClientSecret:   cfg.ClientSecret,
			RefreshToken:   cfg.RefreshToken,
			OrganizationID: cfg.OrganizationID,
			User:           cfg.User,
		})
	}
	return &Client{
		configs: configs,
		tokens:  make([]ZohoToken, 0),
	}
}

// CallAPI makes a generic API call to Zoho
func (c *Client) CallAPI(user, apiName string, requestObject interface{}, queryParameters map[string]string, headers map[string]string, params map[string]string) ([]byte, *int, error) {
	endpoint := GetEndpoint(apiName, params)
	if endpoint == nil {
		return nil, nil, errors.New("not able to create ")
	}
	url := fmt.Sprintf("%s%s", ZOHO_DESK_BASE_URL, *endpoint)
	method := ZOHO_API_TYPE[apiName]

	// Add query parameters if provided
	if queryParameters != nil {
		queryParams := urll.Values{}
		for key, value := range queryParameters {
			queryParams.Add(key, value)
		}
		url = url + "?" + queryParams.Encode()
	}

	// Encode request body if provided
	var buf bytes.Buffer
	if requestObject != nil {
		if err := json.NewEncoder(&buf).Encode(requestObject); err != nil {
			return nil, nil, fmt.Errorf("failed to encode request body: %w", err)
		}
	}

	// Create and execute request
	req, err := http.NewRequest(method, url, &buf)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("orgId", c.configs[0].OrganizationID)
	for key, value := range headers {
		req.Header.Add(key, value)
	}
	token, err := c.getAuthToken(user)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get auth token: %w", err)
	}
	req.Header.Add("Authorization", fmt.Sprintf("Zoho-oauthtoken %s", *token))

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// addtologs

	go func(requestObject interface{}, endpoint string, body []byte) {
		byteRequest, err := json.Marshal(requestObject)
		if err != nil {
			fmt.Println("error in marshaling the requestObject")
			return
		}
		log.LogZohoAPI([]dao.ZohoApiLogs{
			{
				Request:        endpoint,
				RequestObject:  byteRequest,
				ResponseObject: body,
			},
		})
	}(requestObject, *endpoint, body)

	return body, &resp.StatusCode, nil
}

// TODO: @sanket refactor the code and fix this asap
func (c *Client) TempGetToken(user string) (*string, error) {
	for i, token := range c.tokens {
		if token.User == user {
			if token.Expires > time.Now().UnixMilli() {
				return &token.AccessToken, nil
			}
			return c.refreshAuthToken(user, i)
		}
	}
	return c.refreshAuthToken(user, -1)
}

// getAuthToken retrieves or refreshes the authentication token for a user
func (c *Client) getAuthToken(user string) (*string, error) {
	// Check for existing valid token
	for i, token := range c.tokens {
		if token.User == user {
			if token.Expires > time.Now().UnixMilli() {
				return &token.AccessToken, nil
			}
			return c.refreshAuthToken(user, i)
		}
	}
	return c.refreshAuthToken(user, -1)
}

// refreshAuthToken calls the auth API to get a new token
func (c *Client) refreshAuthToken(user string, index int) (*string, error) {
	config := c.getConfig(user)
	if config == nil {
		return nil, fmt.Errorf("no configuration found for user: %s", user)
	}

	body, err := c.callAuthAPI(config)
	if err != nil {
		return nil, fmt.Errorf("failed to refresh auth token: %w", err)
	}

	var refreshTokenResponse ZohoRefreshTokenResponse
	if err := json.Unmarshal(body, &refreshTokenResponse); err != nil {
		return nil, fmt.Errorf("failed to parse refresh token response: %w", err)
	}

	newToken := ZohoToken{
		User:        user,
		AccessToken: refreshTokenResponse.AccessToken,
		Expires:     time.Now().Add(time.Duration(refreshTokenResponse.ExpiresIn) * time.Second).UnixMilli(),
	}

	if index == -1 {
		c.tokens = append(c.tokens, newToken)
	} else {
		c.tokens[index] = newToken
	}

	return &refreshTokenResponse.AccessToken, nil
}

// callAuthAPI makes the authentication API call
func (c *Client) callAuthAPI(config *ZohoConfig) ([]byte, error) {
	url := fmt.Sprintf("%s%s?client_id=%s&grant_type=refresh_token&client_secret=%s&refresh_token=%s",
		ZOHO_AUTH_BASE_URL,
		ZOHO_API_ENDPOINT["REFRESH_TOKEN"],
		config.ClientID,
		config.ClientSecret,
		config.RefreshToken,
	)

	req, err := http.NewRequest(http.MethodPost, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create auth request: %w", err)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute auth request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read auth response body: %w", err)
	}

	return body, nil
}

// getConfig retrieves the configuration for a specific user
func (c *Client) getConfig(user string) *ZohoConfig {
	for _, cfg := range c.configs {
		if cfg.User == user {
			return &cfg
		}
	}
	return nil
}
