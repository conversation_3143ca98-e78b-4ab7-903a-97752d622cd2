package zoho

import "net/http"

const (
	ZOHO_AUTH_BASE_URL        = "https://accounts.zoho.in"
	ZOHO_DESK_BASE_URL        = "https://desk.zoho.in"
	ZOHO_ADMIN                = "ADMIN"
	ZOHO_USER                 = "USER"
	CREATE_TICKET             = "CREATE_TICKET"
	CREATE_COMMENT            = "CREATE_COMMENT"
	CREATE_COMMENT_ATTACHMENT = "CREATE_COMMENT_ATTACHMENT"
	REFRESH_TOKEN             = "REFRESH_TOKEN"
	CREATE_CONTACT            = "CREATE_CONTACT"
)

var ZOHO_API_ENDPOINT = map[string]string{
	REFRESH_TOKEN:  "/oauth/v2/token",
	CREATE_TICKET:  "/api/v1/tickets",
	CREATE_COMMENT: "/api/v1/tickets/:ticketID/comments",
	CREATE_CONTACT: "/api/v1/contacts",
	CREATE_COMMENT_ATTACHMENT: "/api/v1/tickets/:ticketID/attachments",
}

var ZOHO_API_TYPE = map[string]string{
	REFRESH_TOKEN:  http.MethodPost,
	CREATE_TICKET:  http.MethodPost,
	CREATE_COMMENT: http.MethodPost,
	CREATE_CONTACT: http.MethodPost,
	CREATE_COMMENT_ATTACHMENT: http.MethodPost,
}

const (
	EventTypeTicketAdd                = "Ticket_Add"
	EventTypeTicketUpdate             = "Ticket_Update"
	EventTypeTicketDelete             = "Ticket_Delete"
	EventTypeTicketApprovalAdd        = "Ticket_Approval_Add"
	EventTypeTicketApprovalUpdate     = "Ticket_Approval_Update"
	EventTypeTicketThreadAdd          = "Ticket_Thread_Add"
	EventTypeTicketCommentAdd         = "Ticket_Comment_Add"
	EventTypeTicketCommentUpdate      = "Ticket_Comment_Update"
	EventTypeContactAdd               = "Contact_Add"
	EventTypeContactUpdate            = "Contact_Update"
	EventTypeContactDelete            = "Contact_Delete"
	EventTypeAccountAdd               = "Account_Add"
	EventTypeAccountUpdate            = "Account_Update"
	EventTypeAccountDelete            = "Account_Delete"
	EventTypeDepartmentAdd            = "Department_Add"
	EventTypeDepartmentUpdate         = "Department_Update"
	EventTypeAgentAdd                 = "Agent_Add"
	EventTypeAgentUpdate              = "Agent_Update"
	EventTypeAgentDelete              = "Agent_Delete"
	EventTypeAgentPresenceUpdate      = "Agent_Presence_Update"
	EventTypeTicketAttachmentAdd      = "Ticket_Attachment_Add"
	EventTypeTicketAttachmentUpdate   = "Ticket_Attachment_Update"
	EventTypeTicketAttachmentDelete   = "Ticket_Attachment_Delete"
	EventTypeTaskAdd                  = "Task_Add"
	EventTypeTaskUpdate               = "Task_Update"
	EventTypeTaskDelete               = "Task_Delete"
	EventTypeCallAdd                  = "Call_Add"
	EventTypeCallUpdate               = "Call_Update"
	EventTypeCallDelete               = "Call_Delete"
	EventTypeEventAdd                 = "Event_Add"
	EventTypeEventUpdate              = "Event_Update"
	EventTypeEventDelete              = "Event_Delete"
	EventTypeTimeEntryAdd             = "TimeEntry_Add"
	EventTypeTimeEntryUpdate          = "TimeEntry_Update"
	EventTypeTimeEntryDelete          = "TimeEntry_Delete"
	EventTypeArticleAdd               = "Article_Add"
	EventTypeArticleUpdate            = "Article_Update"
	EventTypeArticleDelete            = "Article_Delete"
	EventTypeArticleTranslationAdd    = "Article_Translation_Add"
	EventTypeArticleTranslationUpdate = "Article_Translation_Update"
	EventTypeArticleTranslationDelete = "Article_Translation_Delete"
	EventTypeArticleFeedbackAdd       = "Article_Feedback_Add"
	EventTypeKBRootCategoryAdd        = "KBRootCategory_Add"
	EventTypeKBRootCategoryUpdate     = "KBRootCategory_Update"
	EventTypeKBRootCategoryDelete     = "KBRootCategory_Delete"
	EventTypeKBSectionAdd             = "KBSection_Add"
	EventTypeKBSectionUpdate          = "KBSection_Update"
	EventTypeKBSectionDelete          = "KBSection_Delete"
	EventTypeIMMessageAdd             = "IM_Message_Add"
)
