package zoho

type ZohoRefreshTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"`
	TokenType    string `json:"token_type"`
	Scope        string `json:"scope"`
}

// Config represents Zoho configuration parameters
type ZohoConfig struct {
	ClientID       string `json:"client_id"`
	ClientSecret   string `json:"client_secret"`
	RefreshToken   string `json:"refresh_token"`
	OrganizationID string `json:"organization_id"`
	User           string `json:"user"`
}
type ZohoRefreshTokenRequest struct {
	GrantType    string `json:"grant_type"`
	RefreshToken string `json:"refresh_token"`
}

// Client represents a Zoho API client
type Client struct {
	configs []ZohoConfig
	tokens  []ZohoToken
}

// Token represents an authentication token
type ZohoToken struct {
	AccessToken string `json:"access_token"`
	Expires     int64  `json:"expires"`
	User        string `json:"user"`
}