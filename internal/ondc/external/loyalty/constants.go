package loyalty

import (
	"net/http"
)

const (
	LOYALTY_BASE_URL = "https://grimwald.retailpulse.ai"
)

var LOYALTY_API_ENDPOINT = map[string]string{
	"GET_CASHBACK_BALANCE":       "/api/loyalty/get_cashback_balance",
	"CALCULATE_LOYALTY_REWARDS":  "/api/loyalty/calculate_loyalty_rewards",
	"ADD_LOYALTY_REWARDS":        "/api/loyalty/add_loyalty_rewards",
	"ACTIVATE_LOYALTY_REWARDS":   "/api/loyalty/activate_loyalty_rewards",
	"REDEEM_CASHBACK":            "/api/loyalty/redeem_cashback",
	"DEACTIVATE_LOYALTY_REWARDS": "/api/loyalty/deactivate_loyalty_rewards",
	"REVERT_CASHBACK":            "/api/loyalty/revert_cashback",
	"GET_USER_TIER_INFO":         "/api/loyalty/get_user_tier_info",
	"ORDERS_HISTORY":             "/api/loyalty/orders_history",
	"TIER_UPGRADE_HISTORY":       "/api/loyalty/tier_upgrade_history",
	"GET_COINS_WALLET":           "/api/loyalty/coins_wallet",
	"GET_CASHBACK_WALLET":        "/api/loyalty/cashback_wallet",
	"CONFIRM_LOYALTY_ORDER":      "/api/loyalty/confirm_loyalty_order",
	"CREATE_TRANSACTION_SOURCE":  "/api/loyalty/create_transaction_source",
}

var LOYALTY_API_TYPE = map[string]string{
	"GET_CASHBACK_BALANCE":       http.MethodPost,
	"CALCULATE_LOYALTY_REWARDS":  http.MethodPost,
	"ADD_LOYALTY_REWARDS":        http.MethodPost,
	"ACTIVATE_LOYALTY_REWARDS":   http.MethodPost,
	"REDEEM_CASHBACK":            http.MethodPost,
	"DEACTIVATE_LOYALTY_REWARDS": http.MethodPost,
	"REVERT_CASHBACK":            http.MethodPost,
	"CONFIRM_LOYALTY_ORDER":      http.MethodPost,
	"GET_USER_TIER_INFO":         http.MethodPost,
	"ORDERS_HISTORY":             http.MethodPost,
	"TIER_UPGRADE_HISTORY":       http.MethodPost,
	"GET_COINS_WALLET":           http.MethodPost,
	"GET_CASHBACK_WALLET":        http.MethodPost,
	"CREATE_TRANSACTION_SOURCE":  http.MethodPost,
}
