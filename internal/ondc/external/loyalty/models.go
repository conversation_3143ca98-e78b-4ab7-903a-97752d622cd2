package loyalty

type GetCashbackBalanceResponse struct {
	Cashback float64 `json:"cashback"`
}

type CashbackActions struct {
	ADD_CASHBACK    int
	REDEEM_CASHBACK int
}

type LoyaltyRewardsResponse struct {
	Cashback         int        `json:"cashback"`
	Coins            int        `json:"coins"`
	CurrentTier      string     `json:"currentTier"`
	CashBackFactor   float64    `json:"cashBackFactor"`
	CoinsFactor      float64    `json:"coinsFactor"`
	CoinImageURL     string     `json:"coinImageUrl"`
	CashbackImageURL string     `json:"cashbackImageUrl"`
	LoyaltyUser      bool       `json:"loyaltyUser"`
	TierName         string     `json:"tierName"`
	Colors           ColorsData `json:"colors"`
}

type ColorsData struct {
	Primary  string       `json:"primary"`
	Gradient GradientData `json:"gradient"`
}

type GradientData struct {
	Start string `json:"start"`
	End   string `json:"end"`
}
