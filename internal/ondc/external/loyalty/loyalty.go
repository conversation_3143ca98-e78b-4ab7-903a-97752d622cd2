package loyalty

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	urll "net/url"
)

func CallLoyaltyAPI(API string, requestObject interface{}, params map[string]interface{}) ([]byte, error) {
	url := LOYALTY_BASE_URL + LOYALTY_API_ENDPOINT[API]
	method := LOYALTY_API_TYPE[API]

	queryParams := urll.Values{}
	for key, value := range params {
		queryParams.Add(key, fmt.Sprintf("%v", value))
	}

	if queryParams.Encode() != "" {
		url = url + "?" + queryParams.Encode()
	}

	var buf bytes.Buffer
	err := json.NewEncoder(&buf).Encode(requestObject)
	if err != nil {
		return nil, err
	}

	client := &http.Client{}
	req, err := http.NewRequest(method, url, &buf)
	req.Header.Set("Content-Type", "application/json")

	if err != nil {
		return nil, err
	}

	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("error: %d", res.StatusCode)
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}
