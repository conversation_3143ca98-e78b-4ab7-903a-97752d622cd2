package panchvati

import (
	"encoding/json"
	"strconv"
)

type PanchvatiPincodeTAT struct {
	Pincode string `json:"Pincode"`
	TAT     int    `json:"TAT"`
}

var panchvatiServiceAbilityMap = make(map[string]int)

func PreLoadPanchvatiServiceAbilityPincode() error {
	var rawData []map[string]interface{}
	err := json.Unmarshal([]byte(panchvatiServiceAbilityPincodes), &rawData)
	if err != nil {
		return err
	}

	for _, item := range rawData {
		var pincode string
		switch p := item["Pincode"].(type) {
		case float64:
			pincode = strconv.FormatInt(int64(p), 10)
		case string:
			pincode = p
		default:
			continue // Skip invalid entries
		}

		tat, ok := item["TAT"].(float64)
		if !ok {
			continue // Skip invalid entries
		}

		panchvatiServiceAbilityMap[pincode] = int(tat)
	}

	return nil
}

func CheckServiceAbility(pincode string) (int, bool) {
	value, exists := panchvatiServiceAbilityMap[pincode]
	return value, exists
}
