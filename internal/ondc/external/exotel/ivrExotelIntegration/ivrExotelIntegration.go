package ivrexotelintegration

import (
	"context"
	"encoding/json"
	"kc/internal/ondc/external/exotel/ivr"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/queue"
	queueModals "kc/internal/ondc/queue/models"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/utils"
	"time"

	"github.com/google/uuid"
)

func AddDataToExotelIVRQueue(ctx context.Context, customFields interface{}, From string, To string, AppID int, triggerAt time.Time, repo *sqlRepo.Repository, mp *mixpanelRepo.Repository, requestType string, adjustTimeValue bool, timeAdjustConfig *utils.TimeAdjustConfig) (time.Time, error) {
	if adjustTimeValue {
		triggerAt = utils.AdjustTime(triggerAt, timeAdjustConfig)
	}
	// add logic to add data to queue
	// create Request object for queue and the function to trigger
	data := map[string]interface{}{
		"custom_fields": customFields,
		"from":          From,
		"to":            To,
		"app_id":        AppID,
	}
	queueElementID := uuid.NewString() + "KC"
	triggerData := queueModals.QueueTriggerData{
		TriggerFunc: func() error {
			ivr.CreateIVRCall(ctx, dto.IVRRequest{
				FromPhone:    From,
				FlowID:       AppID,
				ToPhone:      To,
				CustomFields: customFields,
				ID:           queueElementID,
			}, repo, mp, requestType)
			return nil
		},
		TriggerAt: triggerAt,
		Data:      data,
		Repo:      repo,
		ID:        queueElementID,
	}
	ivrTriggerData := dao.ExotelIVRTriggerData{
		FunctionName: "CREATE_IVR_CALL",
		CustomFields: customFields,
		From:         From,
		To:           To,
		AppID:        AppID,
		TriggerAt:    triggerAt,
	}
	byt, err := json.Marshal(ivrTriggerData)
	if err != nil {
		repo.Create(&dao.ExotelIVRQueueData{
			ID:        queueElementID,
			Data:      byt,
			Status:    "FAILED_QUEUE_INSERTION",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Type:      requestType,
		})
		return triggerAt, err
	}
	err = queue.IVRQueueInstance.Insert(triggerData, triggerAt)
	if err != nil {
		return triggerAt, err
	}

	// inserting into exotel_ivr_queue_data
	repo.Create(&dao.ExotelIVRQueueData{
		ID:        queueElementID,
		Data:      byt,
		Status:    "INITIATED",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Type:      requestType,
	})
	return triggerAt, nil
}
