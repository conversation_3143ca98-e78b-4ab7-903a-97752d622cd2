package ivrorderintegration

import (
	"fmt"
	"kc/internal/ondc/localclient"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/orderStatus/constants"
	"net/http"
	"time"
)

var OrderCancellationReason = make(map[string]string, 1)

func HandleOrderConfirmaion(orderID, userID, orderingModule string) error {
	confirmOrderRequest := dto.UpdateB2BOrderStatusRequest{
		UpdatedBy: "IVR",
		Data: dto.UpdateB2BOrderStatusData{
			OrderID:        orderID,
			UpdatedBy:      "IVR",
			OrderStatus:    constants.CONFIRMED,
			UpdatedAt:      time.Now().UnixMilli(),
			OrderingModule: orderingModule,
		},
	}

	_, err := localclient.MakeLocalRequest("/b2b/update_order_status", http.MethodPost, confirmOrderRequest)
	if err != nil {
		return err
	}
	return nil
}

func HandleOrderCancellationReason(customFields dto.ExotelIVROrderInfo, reason string, repository *sqlRepo.Repository) {
	OrderCancellationReason[fmt.Sprintf("%s", customFields.OrderID)] = reason
	_, _, err := repository.Update(&dao.ExotelIVROrderCancellationData{
		OrderID: uint64(customFields.OrderID),
	}, &dao.ExotelIVROrderCancellationData{
		UpdatedAt:          time.Now(),
		CancellationReason: reason,
	})
	if err != nil {
		fmt.Println("error in handling order cancellation reason,", err)
		return
	}
}

func CancelKiranaBazarOrderViaQueue(ivrInfo dto.ExotelIVROrderInfo, request *dto.WebhookCallDetails, repo *sqlRepo.Repository, mp *mixpanelRepo.Repository, cancellationReason string, id string) error {
	newCancellationReason, _ := OrderCancellationReason[fmt.Sprintf("%s", ivrInfo.OrderID)]
	if newCancellationReason != "" {
		cancellationReason = newCancellationReason
	}
	if cancellationReason == "" {
		var ok bool
		cancellationReason, ok = OrderCancellationReason[fmt.Sprintf("%s", ivrInfo.OrderID)]
		if !ok {
			cancellationReason = "USER_NOT_SUBMITTED_CANCELLATION_REASON"
		}
	}

	_, err := localclient.MakeLocalRequest("/kirana_bazar/order", http.MethodDelete, dto.AppCancelKiranaBazarOrderRequest{
		UserID: ivrInfo.UserID,
		Data: dto.AppCancelKiranaBazarOrderData{
			OrderID:     fmt.Sprintf("%d", ivrInfo.OrderID),
			Seller:      ivrInfo.Seller,
			Message:     "",
			Reason:      cancellationReason,
			Explanation: cancellationReason,
			Email:       "IVR",
			Source:      "AUTOMATION",
		},
	})
	if err != nil {
		repo.CustomQuery(nil, fmt.Sprintf(`update kiranaclubdb.exotel_ivr_order_cancellation_data set status = '%s' where id = "%s";`, err.Error(), id))
		fmt.Println("not able to Cancel the order", err)
		return err
	}

	repo.CustomQuery(nil, fmt.Sprintf(`update kiranaclubdb.exotel_ivr_order_cancellation_data set status = 'CANCELLED' where id = "%s";`, id))
	return nil
}
