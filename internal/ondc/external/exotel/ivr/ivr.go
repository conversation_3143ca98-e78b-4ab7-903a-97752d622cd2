package ivr

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"kc/internal/ondc/config"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/utils"
	"math"
	"math/rand"

	"kc/internal/ondc/repositories/sqlRepo"
	processingstatus "kc/internal/ondc/service/orderStatus/processingStatus"
	shipmentstatus "kc/internal/ondc/service/orderStatus/shipmentStatus"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/mixpanel/mixpanel-go"
	"gorm.io/datatypes"
)

const IVR_TO_PHONE = "+917941057745"

const (
	ORDER_CONFIRMATION_FLOW = "ORDER_CONFIRMATION_FLOW"
)

var IVR_FLOW = map[string]string{
	ORDER_CONFIRMATION_FLOW: ORDER_CONFIRMATION_FLOW,
}

type IVRDataMap map[string]interface{}

var SellerIVRData = make(map[string]IVRDataMap, 0)

func CreateSellerIVRData(ivrconfig config.IVRConfig) {
	for _, ivrSellerData := range ivrconfig {
		_, ok := SellerIVRData[ivrSellerData.Seller]
		if !ok {
			SellerIVRData[ivrSellerData.Seller] = make(IVRDataMap)
		}
		for _, appId := range ivrSellerData.Apps {
			SellerIVRData[ivrSellerData.Seller][appId.Name] = appId.AppID
		}
	}
	// this is to test whether the sellerIVRData is made or not
	fmt.Println("SellerIVRData for zoff = ", SellerIVRData["zoff_foods"][ORDER_CONFIRMATION_FLOW])
}

func checkForIVRFesibility(orderID uint64, repo *sqlRepo.Repository, requestType string) (bool, string, error) {
	// 
	if requestType == IVR_REQUEST_TYPES.ORDER_BAD_DELIVERY {
		return true, "", nil
	}
	orderData := dao.KiranaBazarOrder{}
	_, err := repo.Find(map[string]interface{}{
		"id": orderID,
	}, &orderData)
	if err != nil {
		return false, "", nil
	}

	switch requestType {
	case IVR_REQUEST_TYPES.ORDER_CONFIRMATION_FLOW:
		if orderData.ProcessingStatus == processingstatus.PLACED {
			return true, orderData.ProcessingStatus, nil
		}
	case IVR_REQUEST_TYPES.ORDER_OUT_FOR_DELIVERY:
		if orderData.DeliveryStatus == shipmentstatus.OUT_FOR_DELIVERY {
			return true, orderData.DeliveryStatus, nil
		}
	}
	return false, orderData.ProcessingStatus, nil
}

func truncateError(err error) string {
	if err == nil {
		return ""
	}

	errStr := err.Error()
	const maxLength = 400

	if len(errStr) <= maxLength {
		return errStr
	}

	return errStr[:maxLength-3] + "..."
}

func CreateIVRCall(ctx context.Context, request dto.IVRRequest, repo *sqlRepo.Repository, mp *mixpanelRepo.Repository, requestType string) error {

	// apiresponse
	apiResponse, err := func() ([]byte, error) {

		customFields, err := json.Marshal(request.CustomFields)
		if err != nil {
			return nil, err
		}

		ivrOrderInfo := dto.ExotelIVROrderInfo{}
		err = json.Unmarshal(customFields, &ivrOrderInfo)
		if err != nil {
			return nil, err
		}

		// check the fesibiity for IVR call
		fmt.Println("requestType = ", requestType)
		isFesible, _, err := checkForIVRFesibility(uint64(ivrOrderInfo.OrderID), repo, requestType)
		fmt.Println("isFesible = ", isFesible)
		if !isFesible {
			return nil, errors.New("order status already changed")
		}

		// check the IVR call count
		if ivrOrderInfo.IVRCallCount >= 4 && isFesible {
			// dont cancel order when limit exceeded
			mp.Track(ctx, []*mixpanel.Event{
				mp.NewEvent("IVR Call Limit Exceeded", ivrOrderInfo.UserID, map[string]interface{}{
					"distinct_id":     ivrOrderInfo.UserID,
					"order_id":        ivrOrderInfo.OrderID,
					"order_value":     ivrOrderInfo.OrderValue,
					"try_count":       ivrOrderInfo.IVRCallCount,
					"ordering_module": utils.MakeTitleCase(ivrOrderInfo.Seller),
					"seller":          ivrOrderInfo.Seller,
					"email":           "IVR",
					"source":          "AUTOMATION",
					"retry_count":     ivrOrderInfo.RetryCount,
					"mobile":          request.FromPhone,
					"flow_id":         request.FlowID,
					"request_type":    requestType,
				}),
			})

			return nil, errors.New("cancelled after 3 ivr attempts")
		}

		if request.FlowID == 0 {
			return nil, errors.New("not a valid flowID")
		}
		data := url.Values{}
		data.Set("From", request.FromPhone)
		data.Set("CallerId", request.ToPhone)
		data.Set("Url", fmt.Sprintf("http://my.exotel.com/kiranaclub1/exoml/start_voice/%d", request.FlowID))
		data.Set("StatusCallbackContentType", "application/json")
		data.Set("Record", "true")
		data.Set("CustomField", string(customFields))
		data.Set("StatusCallback", "https://ondc.retailpulse.ai/exotel/ivr/callback")

		// Create client
		client := &http.Client{}

		// Create request
		apiURL := fmt.Sprintf("https://api.exotel.com/v1/Accounts/kiranaclub1/Calls/connect.json")
		req, err := http.NewRequest("POST", apiURL, strings.NewReader(data.Encode()))
		if err != nil {
			return nil, fmt.Errorf("error creating request: %v", err)
		}

		// Add headers
		req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
		req.Header.Add("Accept", "application/json")
		req.SetBasicAuth("812efd93ea7ef3cb4f18d74bc972b2637c7c224b90aa8d63", "07353734fa60d2523d8f51a11ce9ab10c66db32e0e1e0a5e")

		requestData := map[string]string{
			"From":                      request.FromPhone,
			"CallerId":                  request.ToPhone,
			"Url":                       fmt.Sprintf("http://my.exotel.com/kiranaclub1/exoml/start_voice/%d", request.FlowID),
			"StatusCallbackContentType": "application/json",
			"Record":                    "true",
			"CustomField":               string(customFields),
			"StatusCallback":            "https://ondc.retailpulse.ai/exotel/ivr/callback",
		}

		// Convert to JSON
		requestJSON, err := json.Marshal(requestData)
		if err != nil {
			return nil, fmt.Errorf("error marshaling request data: %v", err)
		}
		apilog, err := repo.Create(&dao.ExotelIVRAPILogs{
			Request:   requestJSON,
			CreatedAt: time.Now(),
			QueueID:   request.ID,
		})
		if err != nil {
			return nil, err
		}

		apiLogs := apilog.(*dao.ExotelIVRAPILogs)
		// Make request

		var resp *http.Response
		var statusCode int

		const maxRetries = 30
		const baseDelay = 1 * time.Second

		for retry := 0; retry <= maxRetries; retry += 1 {
			if retry > 0 {
				// Exponential backoff with jitter
				delay := time.Duration(float64(baseDelay) * math.Pow(2, float64(retry-1)) * (0.5 + rand.Float64()))
				time.Sleep(delay)
			}
			resp, err = client.Do(req)
			statusCode = resp.StatusCode
			if (statusCode != 429 && statusCode != 0) || retry == maxRetries {
				break
			}

		}

		statusCode = resp.StatusCode
		if err != nil {
			repo.Update(&dao.ExotelIVRAPILogs{
				ID: apiLogs.ID,
			}, &dao.ExotelIVRAPILogs{
				StatusCode: statusCode,
				Response:   datatypes.JSON(err.Error()),
			})
			return nil, fmt.Errorf("error making request: %v", err)
		}
		defer resp.Body.Close()

		// Read response
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			repo.Update(&dao.ExotelIVRAPILogs{
				ID: apiLogs.ID,
			}, &dao.ExotelIVRAPILogs{
				StatusCode: statusCode,
				Response:   datatypes.JSON(err.Error()),
			})
			return nil, fmt.Errorf("error reading response: %v", err)
		}
		repo.Update(&dao.ExotelIVRAPILogs{
			ID: apiLogs.ID,
		}, &dao.ExotelIVRAPILogs{
			StatusCode: statusCode,
			Response:   body,
		})
		repo.CustomQuery(nil, fmt.Sprintf(`update kiranaclubdb.exotel_ivr_queue_data set status = 'SENT_TO_EXOTEL', updated_at = '%s' where id = '%s'`, time.Now().Format("2006-01-02 15:04:05"), request.ID))

		ivrResponse := dto.IVRResponse{}
		json.Unmarshal(body, &ivrResponse)
		mp.Track(context.Background(), []*mixpanel.Event{
			mp.NewEvent("IVR Call Triggered", ivrOrderInfo.UserID, map[string]interface{}{
				"distinct_id":     ivrOrderInfo.UserID,
				"try_count":       ivrOrderInfo.IVRCallCount,
				"retry_count":     ivrOrderInfo.RetryCount,
				"order_id":        ivrOrderInfo.OrderID,
				"ordering_module": utils.MakeTitleCase(ivrOrderInfo.Seller),
				"seller":          ivrOrderInfo.Seller,
				"order_value":     ivrOrderInfo.OrderValue,
				"mobile":          request.FromPhone,
				"sid":             ivrResponse.Call.Sid,
				"request_type":    requestType,
			}),
		})
		return body, nil
	}()
	if apiResponse == nil && err == nil {
		return nil
	}

	if err != nil {
		// updating ivr_queue_data
		repo.CustomQuery(nil, fmt.Sprintf(`update kiranaclubdb.exotel_ivr_queue_data set status = 'FAILED_TO_SENT_TO_EXOTEL', comment = '%s', updated_at = '%s' where id = '%s'`, truncateError(err), time.Now().Format("2006-01-02 15:04:05"), request.ID))
		return err
	}
	response := dto.IVRResponse{}
	err = json.Unmarshal(apiResponse, &response)
	if err != nil {
		// updating ivr_queue_data
		repo.CustomQuery(nil, fmt.Sprintf(`update kiranaclubdb.exotel_ivr_queue_data set status = 'INVALID_RESPONSE', comment = '%s', updated_at = '%s' where id = '%s'`, truncateError(err), time.Now().Format("2006-01-02 15:04:05"), request.ID))
		return nil
	}
	// creating exotel_ivr_data
	exotelIVRData, err := repo.Create(&dao.ExotelIVRData{
		SID:       response.Call.Sid,
		To:        response.Call.To,
		From:      response.Call.From,
		Status:    strings.ToUpper(response.Call.Status),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	})
	if err != nil {
		return err
	}
	parsedExotelIVRData := exotelIVRData.(*dao.ExotelIVRData)

	CF := request.CustomFields
	bytt, err := json.Marshal(CF)
	if err != nil {
		fmt.Println("not able to marshal the data for ivrOrderInfo", err)
		return err
	}
	ivrOrderInfo := dto.ExotelIVROrderInfo{}
	err = json.Unmarshal(bytt, &ivrOrderInfo)
	if err != nil {
		fmt.Println("not able to parse the data for ivrOrderInfo", err)
		return err
	}
	// creating exotel_ivr_kc_bazar_order_mapping
	_, err = repo.Create(&dao.ExotelIVRKCBazarOrderMapping{
		ExotelIVRID:    parsedExotelIVRData.ID,
		KCBazarOrderID: uint64(ivrOrderInfo.OrderID),
	})
	return nil
}
