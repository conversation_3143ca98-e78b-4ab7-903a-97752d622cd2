package easyecom

type AppEasyEcomConfig struct {
	Email         string `json:"email"`
	Password      string `json:"password"`
	LocationKey   string `json:"location_key"`
	MarketPlaceID int    `json:"market_place_id"`
	OMS           string `json:"oms"`
}

// AuthorizationRequest is the requst object to fetch the jwt token from email to authorize other requests.
type EasyEcomAuthorizationRequest struct {
	Email         string `json:"email"`
	Password      string `json:"password"`
	LocationKey   string `json:"location_key"`
	MarketPlaceID int    `json:"market_place_id"`
}

// authorization response returns the data containing the details of the company and token.
type EasyEcomAuthorizationResponse struct {
	Data    AuthorizationResponseData `json:"data"`
	Message string                    `json:"message"`
}

// AuthorizationResponseData stores the data from the auth response.
type AuthorizationResponseData struct {
	CompanyName   string             `json:"companyname"`
	AllLocation   int                `json:"all_location"`
	TimeZone      string             `json:"time_zone"`
	LogoURL       *string            `json:"logo_url"`
	BrandName     *string            `json:"brandname"`
	BrandID       *string            `json:"brand_id"`
	Logo          *string            `json:"logo"`
	CreditLimit   *float64           `json:"credit_limit"`
	CreditBalance *float64           `json:"credit_balance"`
	UserName      string             `json:"userName"`
	PaymentKey    int                `json:"paymentKey"`
	SerialMode    bool               `json:"serialMode"`
	Token         AuthorizationToken `json:"token"`
}

// Authorization Token is struct to store the jwt token, type and expiry.
type AuthorizationToken struct {
	JwtToken  string `json:"jwt_token"`
	TokenType string `json:"token_type"`
	ExpiresIn int    `json:"expires_in"`
	OMS       string `json:"oms"`
}
