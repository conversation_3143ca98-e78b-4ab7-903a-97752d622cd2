package easyecom

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"kc/internal/ondc/config"
	"net/http"
	urll "net/url"
	"time"
)

var easyEcomConfig []AppEasyEcomConfig
var easyEcomToken []AuthorizationToken

// callAPI is interface function to call the easyecom api.
func callAuthAPI(API string, requestObject interface{}) ([]byte, error) {
	url := fmt.Sprintf("%s%s", EASY_ECOM_BASE_URL, EASY_ECOM_API_ENDPOINT[API])
	method := EASY_ECOM_API_TYPE[API]

	var buf bytes.Buffer
	err := json.NewEncoder(&buf).Encode(requestObject)
	if err != nil {
		return nil, err
	}

	client := &http.Client{}
	req, err := http.NewRequest(method, url, &buf)

	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}

// CallEasyEcomAPI is used to call easyecom apis
func CallEasyEcomAPI(OMS, API string, requestObject interface{}, params map[string]interface{}, retryCount int) ([]byte, error, *int, int) {
	url := fmt.Sprintf("%s%s", EASY_ECOM_BASE_URL, EASY_ECOM_API_ENDPOINT[API])
	method := EASY_ECOM_API_TYPE[API]

	if params != nil {
		queryParams := urll.Values{}
		for key, value := range params {
			queryParams.Add(key, fmt.Sprintf("%v", value))
		}
		url = url + "?" + queryParams.Encode()
	}

	var buf bytes.Buffer
	if requestObject != nil {
		err := json.NewEncoder(&buf).Encode(requestObject)
		if err != nil {
			return nil, err, nil, retryCount
		}
	}

	client := &http.Client{}
	req, err := http.NewRequest(method, url, &buf)

	if err != nil {
		return nil, err, nil, retryCount
	}
	req.Header.Add("Content-Type", "application/json")
	token, err := getAuthToken(OMS)
	if err != nil {
		return nil, err, nil, retryCount
	}
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", *token))

	res, err := client.Do(req)
	if err != nil {
		return nil, err, nil, retryCount
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err, nil, retryCount
	}

	if (res.StatusCode == 504 || res.StatusCode == 429) && retryCount < MAX_RETRY_COUNT {
		retryCount++
		waitTime := time.Duration(1<<(retryCount-1)) * time.Second // 1, 2, 4, 8, 16 seconds
		time.Sleep(waitTime)
		return CallEasyEcomAPI(OMS, API, requestObject, params, retryCount)
	}

	return body, err, &res.StatusCode, retryCount
}

// getAuthTokenFromAPI is used to fetch the auth token for authenticaticating all the apis for easy ecom
func getAuthTokenFromAPI(oms string, index int) (*string, error) {
	config := AppEasyEcomConfig{}
	for _, cfg := range easyEcomConfig {
		if cfg.OMS == oms {
			config = cfg
		}
	}
	byt, err := json.Marshal(config)
	if err != nil {
		return nil, err
	}
	authRequest := EasyEcomAuthorizationRequest{}
	json.Unmarshal(byt, &authRequest)
	authResponse, err := callAuthAPI("GET_TOKEN", authRequest)
	if err != nil {
		return nil, err
	}
	parsedAuthResponse := EasyEcomAuthorizationResponse{}

	err = json.Unmarshal(authResponse, &parsedAuthResponse)
	if err != nil {
		return nil, err
	}

	// saving the token valus in token object
	if index == -1 {
		easyEcomToken = append(easyEcomToken, AuthorizationToken{
			JwtToken:  parsedAuthResponse.Data.Token.JwtToken,
			TokenType: parsedAuthResponse.Data.Token.TokenType,
			ExpiresIn: parsedAuthResponse.Data.Token.ExpiresIn + int(time.Now().Unix()),
			OMS:       oms,
		})
	} else {
		easyEcomToken[index].ExpiresIn = parsedAuthResponse.Data.Token.ExpiresIn + int(time.Now().Unix())
		easyEcomToken[index].JwtToken = parsedAuthResponse.Data.Token.JwtToken
		easyEcomToken[index].TokenType = parsedAuthResponse.Data.Token.TokenType
		easyEcomToken[index].OMS = oms
	}
	return &parsedAuthResponse.Data.Token.JwtToken, nil
}

func getAuthToken(oms string) (*string, error) {
	for index, auth := range easyEcomToken {
		if auth.OMS == oms {
			if auth.ExpiresIn > int(time.Now().Unix()) {
				return &auth.JwtToken, nil
			} else {
				return getAuthTokenFromAPI(oms, index)
			}
		}
	}
	return getAuthTokenFromAPI(oms, -1)
}

func InitEasyEcom(eeConfig []config.EasyEcomConfig) error {
	for _, config := range eeConfig {
		easyEcomConfig = append(easyEcomConfig, AppEasyEcomConfig{
			Email:         config.Email,
			LocationKey:   config.LocationKey,
			Password:      config.Password,
			MarketPlaceID: config.MarketPlaceID,
			OMS:           config.OMS,
		})
	}

	return nil
}

func GetMarketPlaceID(oms string) int {
	for _, config := range easyEcomConfig {
		if config.OMS == oms {
			return config.MarketPlaceID
		}
	}
	return -1
}

func GetEasyEcomOrderType() string {
	return "retailorder"
}
