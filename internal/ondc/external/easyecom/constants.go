package easyecom

import "net/http"

const (
	EASY_ECOM_BASE_URL = "https://api.easyecom.io"
)

const MAX_RETRY_COUNT = 5

var EASY_ECOM_API_ENDPOINT = map[string]string{
	"GET_TOKEN":            "/access/token",
	"CREATE_ORDER":         "/webhook/v2/createOrder",
	"GET_ORDER_DETAILS":    "/orders/V2/getOrderDetails",
	"GET_TRACKING_DETAILS": "/Carriers/getTrackingDetails",
}

var EASY_ECOM_API_TYPE = map[string]string{
	"GET_TOKEN":            http.MethodPost,
	"CREATE_ORDER":         http.MethodPost,
	"GET_ORDER_DETAILS":    http.MethodGet,
	"GET_TRACKING_DETAILS": http.MethodGet,
}
