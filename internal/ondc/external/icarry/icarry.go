package icarry

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"kc/internal/ondc/config"
	"net/http"
	urll "net/url"
	"time"
)

var icarryConfig AppIcarryConfig
var icarryToken AuthorisationToken

func CallIcarryAPI(API string, requestObject interface{}, params map[string]interface{}) ([]byte, error) {
	url := ICARRY_BASE_URL + ICARRY_API_ENDPOINT[API]
	method := ICARRY_API_TYPE[API]

	queryParams := urll.Values{}
	for key, value := range params {
		queryParams.Add(key, fmt.Sprintf("%v", value))
	}
	apiToken, err := getAuthApiToken()
	if err != nil {
		return nil, err
	}

	queryParams.Add("api_token", *apiToken)
	url = url + "?" + queryParams.Encode()

	payload, err := json.Marshal(requestObject)
	if err != nil {
		return nil, err
	}

	client := &http.Client{}
	req, err := http.NewRequest(method, url, bytes.NewReader(payload))

	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}

func callIcarryAuthAPI(requestType string, request IcarryAuthorisationRequest) ([]byte, error) {
	url := ICARRY_BASE_URL + ICARRY_API_ENDPOINT[requestType]
	method := ICARRY_API_TYPE[requestType]

	payload, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	client := &http.Client{}
	req, err := http.NewRequest(method, url, bytes.NewReader(payload))

	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}

func getAuthApiTokenFromAPI() (*string, error) {
	byt, err := json.Marshal(icarryConfig)
	if err != nil {
		return nil, err
	}

	authRequest := IcarryAuthorisationRequest{
		Username: icarryConfig.Username,
		Key:      icarryConfig.Key,
	}
	json.Unmarshal(byt, &authRequest)
	authResponse, err := callIcarryAuthAPI("AUTH", authRequest)
	if err != nil {
		return nil, err
	}

	parsedAuthResponse := IcarryAuthorisationResponse{}
	err = json.Unmarshal(authResponse, &parsedAuthResponse)
	if err != nil {
		return nil, err
	}
	if parsedAuthResponse.Error != "" {
		return nil, fmt.Errorf(parsedAuthResponse.Error)
	}
	icarryToken.ExpiresIn = (60 * 60) + int(time.Now().Unix())
	icarryToken.ApiToken = parsedAuthResponse.ApiToken

	return &parsedAuthResponse.ApiToken, nil
}

func getAuthApiToken() (*string, error) {
	if icarryToken.ExpiresIn > int(time.Now().Unix()) {
		return &icarryToken.ApiToken, nil
	} else {
		return getAuthApiTokenFromAPI()
	}
}

func InitIcarryConfig(icConfig config.IcarryConfig) error {
	icarryConfig = AppIcarryConfig{
		Username: icConfig.Username,
		Key:      icConfig.Key,
	}
	return nil
}

func PreloadMothersKitchenServiceblePincode() error {
	var rawData map[string]CourierServiceAblityAPIResponse
	err := json.Unmarshal([]byte(KC_ICARRY_SERVICEABLE_PINCODES), &rawData)
	if err != nil {
		return err
	}

	ICARRY_SERVICEABLE_PINCODES_MAP = rawData
	return nil
}