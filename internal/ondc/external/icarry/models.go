package icarry

type AppIcarryConfig struct {
	Username string `json:"username"`
	Key      string `json:"key"`
}

type IcarryAuthorisationRequest struct {
	Username string `json:"username"`
	Key      string `json:"key"`
}

type AuthorisationToken struct {
	ApiToken  string `json:"jwt_token"`
	ExpiresIn int    `json:"expires_in"`
}

type IcarryAuthorisationResponse struct {
	Success  string `json:"success"`
	Error    string `json:"error"`
	ApiToken string `json:"api_token"`
}

type CourierServiceAblityAPIResponse struct {
	Success int       `json:"success"`
	Msg     []Message `json:"msg"`
}

type CourierServiceAblityAPIRequest struct {
	Pincode string `json:"pincode"`
}

type Message struct {
	Service string `json:"service"`
	Prepaid string `json:"prepaid"`
	COD     string `json:"cod"`
	Pickup  string `json:"pickup"`
}
