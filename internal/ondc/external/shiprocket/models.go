package shiprocket

type AppShipRocketConfig struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type ShipRocketAuthorisationRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type ShipRocketAuthorisationResponse struct {
	CompanyID *int64 `json:"company_id"`
	CreatedAt string `json:"created_at"`
	Email     string `json:"email"`
	FirstName string `json:"first_name"`
	ID        *int64 `json:"id"`
	LastName  string `json:"last_name"`
	Token     string `json:"token"`
}

type AuthorisationToken struct {
	JWTToken  string `json:"jwt_token"`
	ExpiresIn int    `json:"expires_in"`
}

type CourierServiceAblityAPIRequest struct {
	COD              string `json:"cod"`
	PickUpPostCode   string `json:"pickup_postcode"`
	DeliveryPostCode string `json:"delivery_postcode"`
	Weight           string `json:"weight"`
}

type CourierServiceAbilityAPIResponse struct {
	CompanyAutoShipmentInsuranceSetting bool              `json:"company_auto_shipment_insurance_setting"`
	CovidZones                          CovidZones        `json:"covid_zones"`
	Currency                            string            `json:"currency"`
	Data                                AvailableCouriers `json:"data"`
}

type CovidZones struct {
	DeliveryZone *string `json:"delivery_zone"`
	PickupZone   *string `json:"pickup_zone"`
}

type AvailableCouriers struct {
	AvailableCourierCompanies []CourierCompany `json:"available_courier_companies"`
}

type CourierCompany struct {
	AirMaxWeight           string            `json:"air_max_weight"`
	AssuredAmount          int               `json:"assured_amount"`
	BaseCourierID          *int              `json:"base_courier_id"`
	BaseWeight             string            `json:"base_weight"`
	Blocked                int               `json:"blocked"`
	CallBeforeDelivery     string            `json:"call_before_delivery"`
	ChargeWeight           int               `json:"charge_weight"`
	City                   string            `json:"city"`
	COD                    int               `json:"cod"`
	CODCharges             float64           `json:"cod_charges"`
	CODMultiplier          float64           `json:"cod_multiplier"`
	Cost                   string            `json:"cost"`
	CourierCompanyID       int               `json:"courier_company_id"`
	CourierName            string            `json:"courier_name"`
	CourierType            string            `json:"courier_type"`
	CoverageCharges        int               `json:"coverage_charges"`
	CutoffTime             string            `json:"cutoff_time"`
	DeliveryBoyContact     string            `json:"delivery_boy_contact"`
	DeliveryPerformance    float64           `json:"delivery_performance"`
	Description            string            `json:"description"`
	EDD                    string            `json:"edd"`
	EntryTax               int               `json:"entry_tax"`
	EstimatedDeliveryDays  string            `json:"estimated_delivery_days"`
	ETD                    string            `json:"etd"`
	ETDHours               int               `json:"etd_hours"`
	FreightCharge          float64           `json:"freight_charge"`
	ID                     int64             `json:"id"`
	IsCustomRate           int               `json:"is_custom_rate"`
	IsHyperlocal           bool              `json:"is_hyperlocal"`
	IsInternational        int               `json:"is_international"`
	IsRTOAddressAvailable  bool              `json:"is_rto_address_available"`
	IsSurface              bool              `json:"is_surface"`
	LocalRegion            int               `json:"local_region"`
	Metro                  int               `json:"metro"`
	MinWeight              float64           `json:"min_weight"`
	Mode                   int               `json:"mode"`
	NewEDD                 int               `json:"new_edd"`
	ODABlock               bool              `json:"odablock"`
	OtherCharges           int               `json:"other_charges"`
	Others                 string            `json:"others"`
	PickupAvailability     string            `json:"pickup_availability"`
	PickupPerformance      float64           `json:"pickup_performance"`
	PickupPriority         string            `json:"pickup_priority"`
	PickupSuppressHours    int               `json:"pickup_supress_hours"`
	PODAvailable           string            `json:"pod_available"`
	Postcode               string            `json:"postcode"`
	QCCourier              int               `json:"qc_courier"`
	Rank                   string            `json:"rank"`
	Rate                   float64           `json:"rate"`
	Rating                 float64           `json:"rating"`
	RealtimeTracking       string            `json:"realtime_tracking"`
	Region                 int               `json:"region"`
	RTOCharges             float64           `json:"rto_charges"`
	RTOPerformance         float64           `json:"rto_performance"`
	SecondsLeftForPickup   int               `json:"seconds_left_for_pickup"`
	SecureShipmentDisabled bool              `json:"secure_shipment_disabled"`
	ShipType               int               `json:"ship_type"`
	State                  string            `json:"state"`
	SuppressDate           string            `json:"suppress_date"`
	SuppressText           string            `json:"suppress_text"`
	SuppressionDates       *SuppressionDates `json:"suppression_dates"`
	SurfaceMaxWeight       string            `json:"surface_max_weight"`
	TrackingPerformance    float64           `json:"tracking_performance"`
	VolumetricMaxWeight    *float64          `json:"volumetric_max_weight"`
	WeightCases            float64           `json:"weight_cases"`
	Zone                   string            `json:"zone"`
}

type SuppressionDates struct {
	ActionOn          *string `json:"action_on"`
	DelayRemark       string  `json:"delay_remark"`
	DeliveryDelayBy   int     `json:"delivery_delay_by"`
	DeliveryDelayDays string  `json:"delivery_delay_days"`
	DeliveryDelayFrom *string `json:"delivery_delay_from"`
	DeliveryDelayTo   *string `json:"delivery_delay_to"`
	PickupDelayBy     int     `json:"pickup_delay_by"`
	PickupDelayDays   string  `json:"pickup_delay_days"`
	PickupDelayFrom   *string `json:"pickup_delay_from"`
	PickupDelayTo     *string `json:"pickup_delay_to"`
	BlockedFM         string  `json:"blocked_fm"`
	BlockedLM         string  `json:"blocked_lm"`
}
