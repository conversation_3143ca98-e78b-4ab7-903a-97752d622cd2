package shiprocket

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"kc/internal/ondc/config"
	"net/http"
	urll "net/url"
	"strings"
	"time"
)

var shipRocketConfig = make(map[string]AppShipRocketConfig)
var shipRocketToken = make(map[string]AuthorisationToken)

// callShipRocketAuthAPI gets the auth token from the shiprocket to authenticate all the requests.
func callShipRocketAuthAPI(requestType string, request ShipRocketAuthorisationRequest) ([]byte, error) {

	url := SHIPROCKET_BASE_URL + SHIPROCKET_API_ENDPOINT[requestType]
	method := SHIPROCKET_API_TYPE[requestType]

	payload := strings.NewReader(fmt.Sprintf(`{
    	"email": "%s",
    	"password": "%s"
	}`, request.Email, request.Password))

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payload)

	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}

func CallShipRocketAPI(oms, API string, requestObject interface{}, params map[string]interface{}) ([]byte, error) {
	url := SHIPROCKET_BASE_URL + SHIPROCKET_API_ENDPOINT[API]
	method := SHIPROCKET_API_TYPE[API]

	queryParams := urll.Values{}
	for key, value := range params {
		queryParams.Add(key, fmt.Sprintf("%v", value))
	}
	url = url + "?" + queryParams.Encode()

	var buf bytes.Buffer
	err := json.NewEncoder(&buf).Encode(requestObject)
	if err != nil {
		return nil, err
	}

	client := &http.Client{}
	req, err := http.NewRequest(method, url, &buf)

	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	token, err := getAuthToken(oms)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", *token))

	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}

// getAuthTokenFromAPI is used to fetch the auth token for authenticaticating all the apis for easy ecom
func getAuthTokenFromAPI(oms string) (*string, error) {
	byt, err := json.Marshal(shipRocketConfig[oms])
	if err != nil {
		return nil, err
	}
	authRequest := ShipRocketAuthorisationRequest{}
	json.Unmarshal(byt, &authRequest)
	authResponse, err := callShipRocketAuthAPI("AUTH", authRequest)
	if err != nil {
		return nil, err
	}
	parsedAuthResponse := ShipRocketAuthorisationResponse{}

	err = json.Unmarshal(authResponse, &parsedAuthResponse)
	if err != nil {
		return nil, err
	}

	// saving the token valus in token object
	authorisationTokenObject := AuthorisationToken{
		ExpiresIn: int(time.Now().Unix()) + 432000,
		JWTToken:  parsedAuthResponse.Token,
	}
	shipRocketToken[oms] = authorisationTokenObject
	return &parsedAuthResponse.Token, nil
}

func getAuthToken(oms string) (*string, error) {
	if shipRocketToken[oms].ExpiresIn > int(time.Now().Unix()) {
		token := shipRocketToken[oms].JWTToken
		return &token, nil
	} else {
		return getAuthTokenFromAPI(oms)
	}
}

func InitShipRocket(srConfig []config.ShipRocketConfig) error {
	for _, c := range srConfig {
		shipRocketConfig[c.OMS] = AppShipRocketConfig{
			Email:    c.Email,
			Password: c.Password,
		}
	}
	return nil
}
