package api

import (
	"errors"
	"github.com/gin-gonic/gin"
	"kc/internal/ondc/external/razorpay"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"
)

func (h *Handler) InitializePayment(ctx *gin.Context) {
	var request dto.InitiatePaymentRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	hash, err := utils.GenerateHash(request)
	if err != nil {
		logger.Error(ctx, "Failed to generate hash: "+err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	if h.cache.Exists(hash) {
		err = errors.New("duplicate request detected for InitializePayment, can not process")
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}
	h.cache.Add(hash)

	resp, err := h.myService.InitializePayment(ctx, request)
	if err != nil {
		logger.Info(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) ValidatePayment(ctx *gin.Context) {
	var request dto.ValidatePaymentRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	hash, err := utils.GenerateHash(request)
	if err != nil {
		logger.Error(ctx, "Failed to generate hash: "+err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	if h.cache.Exists(hash) {
		err = errors.New("duplicate request detected for InitializePayment, can not process")
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}
	h.cache.Add(hash)

	resp, err := h.myService.ValidatePayment(ctx, request)
	if err != nil {
		logger.Info(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) InitializePaymentAndCreateKiranaBazarOrder(ctx *gin.Context) {
	request := dto.InitializePaymentAndAppCreateKiranaBazarOrderRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	}
	//
	//hash, err := utils.GenerateHash(request)
	//if err != nil {
	//	logger.Error(ctx, "Failed to generate hash: "+err.Error())
	//	utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	//	return
	//}

	//if h.cache.Exists(hash) {
	//	err = errors.New("duplicate request detected in InitializePaymentAndAppCreateKiranaBazarOrderRequest, can not process")
	//	ctx.JSON(http.StatusBadRequest, gin.H{
	//		"error": err.Error(),
	//	})
	//	return
	//}
	//h.cache.Add(hash)

	resp, err := h.myService.InitializePaymentAndCreateKiranaBazarOrder(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func (h *Handler) RazorpayWebhookType1(ctx *gin.Context) {
	request := razorpay.RazorpayWebhookType1Request{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	}

	eventId := ctx.GetHeader("x-razorpay-event-id")

	if h.cache.Exists(eventId) {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "duplicate request detected in RazorpayWebhookType1, can not process",
		})
		return
	}
	h.cache.Add(eventId)

	err = h.myService.RazorpayWebhookType1(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"message": "success"})
}

func (h *Handler) CheckAdvancePaymentPossibility(ctx *gin.Context) {
	request := dto.CheckAdvancePaymentPossibilityRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	}

	resp, err := h.myService.CheckAdvancePaymentPossibility(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)

}

func (h *Handler) InitiatePaymentFromB2B(ctx *gin.Context) {
	request := dto.InitiatePaymentFromB2BRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	}

	resp, err := h.myService.InitiatePaymentFromB2B(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func (h *Handler) RefundAdvancePayment(ctx *gin.Context) {
	request := dto.RefundPaymentApiRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	}

	resp, err := h.myService.RefundAdvancePayment(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func (h *Handler) RefreshOrderPayment(ctx *gin.Context) {
	request := dto.RefreshOrderRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	}

	resp, err := h.myService.RefreshOrderPayment(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func (h *Handler) GetQRRNN(ctx *gin.Context) {
	request := dto.GetUnmappedRNNRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	}

	resp, err := h.myService.GetUnmappedRNN(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}
