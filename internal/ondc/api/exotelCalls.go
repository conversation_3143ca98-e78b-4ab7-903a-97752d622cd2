package api

import (
	"encoding/json"
	"net/http"
	"strconv"

	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"

	"github.com/gin-gonic/gin"
)

func (h *Handler) InitiateCall(ctx *gin.Context) {
	req := &dto.InitiateCallRequest{}
	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	// Set default virtual number if not provided
	resp, err := h.myService.InitiateCall(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)

}

func (h *Handler) GetCallDetails(ctx *gin.Context) {
	callIDStr := ctx.Query("call_id")
	callID, err := strconv.ParseInt(callIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid call_id"})
		return
	}

	response, err := h.myService.GetCallDetails(ctx, callID)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, response)

}

func (h *Handler) ExotelStatusCallback(ctx *gin.Context) {
	// Parse the request body
	request := dto.ExotelStatusCallback{}
	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	response, err := h.myService.ExotelStatusCallback(ctx, &request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, response)
}

// Get all calls for an order
func (h *Handler) GetOrderCallStatus(ctx *gin.Context) {
	orderIDMap := map[string]int{}
	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(&orderIDMap)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	calls, err := h.myService.GetOrderCallStatus(ctx, orderIDMap["order_id"])
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, calls)
}

// GetOrderCallHistory handles POST /api/v1/calls/history
func (h *Handler) GetOrderCallHistory(ctx *gin.Context) {
	var request struct {
		OrderID int64 `json:"order_id" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid request: " + err.Error()})
		return
	}

	history, err := h.myService.GetCallHistoryByOrderID(ctx, int(request.OrderID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, history)
}
