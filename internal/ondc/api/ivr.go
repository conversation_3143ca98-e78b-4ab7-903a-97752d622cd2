package api

import (
	"encoding/json"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func (h *Handler) CreateIVRCall(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	req := &dto.IVRRequest{}

	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.CreateIVRCall(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) ExotelIVRStatusCallback(ctx *gin.Context) {
	// handle the IVR callback status after sending IVR request
	ctx.Set("key", uuid.NewString())
	var req interface{}

	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(&req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.ExotelIVRStatusCallback(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) ExotelIVRWebhookHandler(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())

	ivrType := ctx.Param("type")

	req := &dto.WebhookCallDetails{
		CallSid:                     ctx.Query("CallSid"),
		CallType:                    ctx.Query("CallType"),
		CallFrom:                    ctx.Query("CallFrom"),
		CallTo:                      ctx.Query("CallTo"),
		Direction:                   ctx.Query("Direction"),
		ForwardedFrom:               ctx.Query("ForwardedFrom"),
		Created:                     ctx.Query("Created"),
		DialCallDuration:            ctx.Query("DialCallDuration"),
		RecordingUrl:                ctx.Query("RecordingUrl"),
		StartTime:                   ctx.Query("StartTime"),
		EndTime:                     ctx.Query("EndTime"),
		DialCallStatus:              ctx.Query("DialCallStatus"),
		DialWhomNumber:              ctx.Query("DialWhomNumber"),
		ProcessStatus:               ctx.Query("ProcessStatus"),
		FlowID:                      ctx.Query("flow_id"),
		TenantID:                    ctx.Query("tenant_id"),
		From:                        ctx.Query("From"),
		To:                          ctx.Query("To"),
		CurrentTime:                 ctx.Query("CurrentTime"),
		CustomField:                 ctx.Query("CustomField"),
		Digits:                      ctx.Query("digits"),
		HangupLatencyStartTimeExocc: ctx.Query("HangupLatencyStartTimeExocc"),
		HangupLatencyStartTime:      ctx.Query("HangupLatencyStartTime"),
		Type:                        ivrType,
	}

	resp, err := h.myService.ExotelWebhook(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) ExotelPhoneMaskingWebhook(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())

	ivrType := ctx.Param("type")
	req := &dto.WebhookCallDetails{
		CallSid:          ctx.Query("CallSid"),
		CallType:         ctx.Query("CallType"),
		CallFrom:         ctx.Query("CallFrom"),
		CallTo:           ctx.Query("CallTo"),
		Direction:        ctx.Query("Direction"),
		ForwardedFrom:    ctx.Query("ForwardedFrom"),
		Created:          ctx.Query("Created"),
		DialCallDuration: ctx.Query("DialCallDuration"),
		RecordingUrl:     ctx.Query("RecordingUrl"),
		StartTime:        ctx.Query("StartTime"),
		EndTime:          ctx.Query("EndTime"),
		DialCallStatus:   ctx.Query("DialCallStatus"),
		DialWhomNumber:   ctx.Query("DialWhomNumber"),
		ProcessStatus:    ctx.Query("ProcessStatus"),
		FlowID:           ctx.Query("flow_id"),
		TenantID:         ctx.Query("tenant_id"),
		From:             ctx.Query("From"),
		To:               ctx.Query("To"),
		Digits:           ctx.Query("digits"),
		Type:             ivrType,
	}

	resp, err := h.myService.ExotelPhoneMaskingWebhook(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	for k, v := range ctx.Request.Header {
		ctx.Writer.Header()[k] = v
	}
	utils.RespondText(ctx, http.StatusOK, resp)
}
