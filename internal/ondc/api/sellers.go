package api

import (
	"fmt"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) GetSellers(ctx *gin.Context) {
	request := dto.GetSellersRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetSellers(ctx, &request)
	if err != nil {
		logger.Error(ctx, err.<PERSON>rror())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) GetSellerDetails(ctx *gin.Context) {
	request := dto.GetSellerDetailsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetSellerDetails(ctx, &request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) UpdateSellerDetails(ctx *gin.Context) {
	request := dto.UpdateSellerDetailsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.UpdateSellerDetails(ctx, &request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) AddSellerDetails(ctx *gin.Context) {
	request := dto.AddSellerRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		fmt.Println("error in binding the request", err.Error())
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.AddSellerDetails(ctx, &request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

// GetVendorCodes return all the invoice vendor codes created so far
func (h *Handler) GetVendorCodes(ctx *gin.Context) {
	resp, err := h.myService.GetVendorCodes(ctx)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}
