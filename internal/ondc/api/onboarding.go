package api

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"html/template"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"
)

func (h *Handler) OnSubscribe(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())

	r := ctx.Request.Body
	req := &dto.OnSubscribeRequest{}
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.OnSubscribeHandler(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

const siteVerificationHTML = `
<html>
    <head>
        <meta name='ondc-site-verification' content='{{.}}' />
    </head>
    <body>
        ONDC Site Verification Page
    </body>
</html>
`

var siteVerificationTemplate = template.Must(template.New("site-verification").Parse(siteVerificationHTML))

func (h *Handler) SiteVerificationHandler(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	err, signedRequestIDB64 := h.myService.SiteVerificationHandler(ctx)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	w := ctx.Writer
	if err = siteVerificationTemplate.Execute(w, signedRequestIDB64); err != nil {
		logger.Error(ctx, "Failed to execute template: %s", err)
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
}
