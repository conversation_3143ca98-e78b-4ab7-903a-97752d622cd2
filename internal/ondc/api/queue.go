package api

import (
	"fmt"
	"kc/internal/ondc/queue"
	"kc/internal/ondc/queue/models"
	"net/http"

	"github.com/gin-gonic/gin"
)

type QueueMetrics struct {
	CurrentItems int         `json:"currentItems"`
	MaxItems     int         `json:"maxItems"`
	MemoryUsage  float64     `json:"memoryUsage"`
	MaxMemory    float64     `json:"maxMemory"`
	Items        []QueueItem `json:"items"`
}

type QueueItem struct {
	Data      interface{} `json:"data"`
	TriggerAt string      `json:"triggerAt"`
	Status    string      `json:"status"`
}

func (h *<PERSON>ler) ServeQueueMonitor(c *gin.Context) {
	c.HTML(http.StatusOK, "monitor_ivr_queue.html", nil)
}

func (h *<PERSON><PERSON>) ServeOrderCancellationMonitor(c *gin.Context) {
	c.HTML(http.StatusOK, "monitor_order_cancellation_queue.html", nil)
}

func (h *<PERSON><PERSON>) TimedFunctionsQueueMonitor(c *gin.Context) {
	c.HTM<PERSON>(http.StatusOK, "monitor_payments_queue.html", nil)
}

func (h *<PERSON>ler) ServeQueueMetrics(c *gin.Context) {
	if queue.IVRQueueInstance == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Queue not initialized"})
		return
	}

	metrics, _ := queue.IVRQueueInstance.GetMetrics()
	items := queue.IVRQueueInstance.GetItems()

	queueItems := make([]QueueItem, 0)
	for _, item := range items {
		queueData, ok := item.Data.(models.QueueTriggerData)
		if !ok {
			fmt.Println("Failed to convert item.Data to QueueTriggerData")
			return
		}

		queueItems = append(queueItems, QueueItem{
			Data: map[string]interface{}{
				"id":        queueData.ID,
				"TriggerAt": queueData.TriggerAt,
				"Data":      queueData.Data,
			},
			TriggerAt: item.TriggerAt.Format("2006-01-02 15:04:05"),
			Status:    "Pending",
		})
	}

	response := QueueMetrics{
		CurrentItems: metrics["currentItems"].(int),
		MaxItems:     metrics["maxItems"].(int),
		MemoryUsage:  metrics["memoryUsage"].(float64),
		MaxMemory:    metrics["maxMemory"].(float64),
		Items:        queueItems,
	}

	c.JSON(http.StatusOK, response)
}

func (h *Handler) ServeOrderCancelQueueMetrics(c *gin.Context) {
	if queue.OrderCancelQueueInstance == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Queue not initialized"})
		return
	}

	metrics, _ := queue.OrderCancelQueueInstance.GetMetrics()
	items := queue.OrderCancelQueueInstance.GetItems()

	queueItems := make([]QueueItem, 0)
	for _, item := range items {
		queueItems = append(queueItems, QueueItem{
			Data:      item.Data,
			TriggerAt: item.TriggerAt.Format("2006-01-02 15:04:05"),
			Status:    "Pending",
		})
	}

	response := QueueMetrics{
		CurrentItems: metrics["currentItems"].(int),
		MaxItems:     metrics["maxItems"].(int),
		MemoryUsage:  metrics["memoryUsage"].(float64),
		MaxMemory:    metrics["maxMemory"].(float64),
		Items:        queueItems,
	}

	c.JSON(http.StatusOK, response)
}

func (h *Handler) TimedFunctionsQueueMetrics(c *gin.Context) {
	if queue.TimedFunctionsQueueInstance == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Queue not initialized"})
		return
	}

	metrics, _ := queue.TimedFunctionsQueueInstance.GetMetrics()
	items := queue.TimedFunctionsQueueInstance.GetItems()

	queueItems := make([]QueueItem, 0)
	for _, item := range items {
		queueItems = append(queueItems, QueueItem{
			Data:      item.Data,
			TriggerAt: item.TriggerAt.Format("2006-01-02 15:04:05"),
			Status:    "Pending",
		})
	}

	response := QueueMetrics{
		CurrentItems: metrics["currentItems"].(int),
		MaxItems:     metrics["maxItems"].(int),
		MemoryUsage:  metrics["memoryUsage"].(float64),
		MaxMemory:    metrics["maxMemory"].(float64),
		Items:        queueItems,
	}

	c.JSON(http.StatusOK, response)
}
