package api

import (
	"encoding/json"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) CreateShipwayOrder(ctx *gin.Context) {
	req := &dto.CreateShipwayOrderRequest{}
	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.CreateShipwayOrder(ctx, req)
	if err != nil {
		logger.Error(ctx, err.<PERSON><PERSON>r())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)

}

func (h *Handler) HandleShipwayWebhook(ctx *gin.Context) {
	req := &dto.ShipwayWebhookRequest{}
	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.HandleShipwayWebhook(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondJSON(ctx, http.StatusOK, err)
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)

}

func (h *Handler) TestShipwayWebhook(ctx *gin.Context) {
	utils.RespondJSON(ctx, http.StatusOK, "resp")
}

// This is the handler for fetching order details
func (h *Handler) GetShipwayOrderDetails(ctx *gin.Context) {
	req := &dto.ShipwayOrderFetchRequest{}
	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetShipwayOrderDetails(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondJSON(ctx, http.StatusOK, err)
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}
