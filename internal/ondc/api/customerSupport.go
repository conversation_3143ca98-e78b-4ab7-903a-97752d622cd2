package api

import (
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) GetCsTicketFlow(ctx *gin.Context) {
	request := dto.CsTicketFlowRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.<PERSON>rror())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	if (request.Data.UserID == "5ql7tXMd0NhmaxDIcs18M5229tn1") {
		request.Data.UserID = "p5wnFLKGc9UTtbdmCevo39sjPyG3"
	}
	resp, err := h.myService.GetCsTicketFlow(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetSupportTickets(ctx *gin.Context) {
	request := dto.GetSupportTicketsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	if request.Data.Filters.UserID == "5ql7tXMd0NhmaxDIcs18M5229tn1" {
		request.Data.Filters.UserID = "p5wnFLKGc9UTtbdmCevo39sjPyG3"
	}
	resp, err := h.myService.GetSupportTickets(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) AppGetSupportOrders(ctx *gin.Context) {
	request := dto.AppGetCustomerSupportHome{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	if (request.Data.UserID == "5ql7tXMd0NhmaxDIcs18M5229tn1") {
		request.Data.UserID = "p5wnFLKGc9UTtbdmCevo39sjPyG3"
	}
	resp, err := h.myService.AppGetSupportOrders(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) AppGetSupportTickets(ctx *gin.Context) {
	request := dto.AppGetCustomerSupportHome{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	if (request.Data.UserID == "5ql7tXMd0NhmaxDIcs18M5229tn1") {
		request.Data.UserID = "p5wnFLKGc9UTtbdmCevo39sjPyG3"
	}
	resp, err := h.myService.AppGetSupportTickets(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) AppSubmitTicket(ctx *gin.Context) {
	request := dto.AppSubmitTicketRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	if request.Data.UserID == "5ql7tXMd0NhmaxDIcs18M5229tn1" {
		request.Data.UserID = "p5wnFLKGc9UTtbdmCevo39sjPyG3"
	}
	resp, err := h.myService.AppSubmitTicket(ctx, &request, false)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetMessages(ctx *gin.Context) {
	request := dto.GetTicketMessagesRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	if request.Data.UserID == "5ql7tXMd0NhmaxDIcs18M5229tn1" {
		request.Data.UserID = "p5wnFLKGc9UTtbdmCevo39sjPyG3"
	}
	resp, err := h.myService.GetMessages(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) SendMessage(ctx *gin.Context) {
	request := dto.SendMessageRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	if request.Data.SenderID == "5ql7tXMd0NhmaxDIcs18M5229tn1" {
		request.Data.SenderID = "p5wnFLKGc9UTtbdmCevo39sjPyG3"
	}
	resp, err := h.myService.SendMessage(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetCsMeta(ctx *gin.Context) {
	request := dto.GetCsMetaRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetCsMeta(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetCSTicketDetails(ctx *gin.Context) {
	request := dto.GetCSTicketDetailsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetCSTicketDetails(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) PerformTicketAction(ctx *gin.Context) {
	request := dto.TicketActionRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.PerformTicketAction(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) SubmitRating(ctx *gin.Context) {
	request := dto.SubmitRatingRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.SubmitRating(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetSupportInsights(ctx *gin.Context) {
	request := dto.GetSupportInsightsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetSupportInsights(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) AppSubmitFlowResponse(ctx *gin.Context) {
	request := dto.AppSubmitTicketRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	if request.Data.UserID == "5ql7tXMd0NhmaxDIcs18M5229tn1" {
		request.Data.UserID = "p5wnFLKGc9UTtbdmCevo39sjPyG3"
	}
	resp, err := h.myService.AppSubmitFlowResponse(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) UpdateMedia(ctx *gin.Context) {
	request := &dto.UpdateMediaRequest{}

	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.UpdateMedia(ctx, request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)

}