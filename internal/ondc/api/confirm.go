package api

import (
	"bytes"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func (h *Handler) Confirm(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	req := &dto.AppConfirmRequest{}

	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.Confirm(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}
func (h *Handler) OnConfirm(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())

	r := ctx.Request.Body

	defer r.Close() // Close the request body when done with it

	// Read the request body and store it in a buffer for printing
	var buf bytes.Buffer
	_, err := buf.ReadFrom(r)
	if err != nil {
		logger.Error(ctx, "Error reading request body: %s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	// Print the value of the request body
	fmt.Println("Request Body for on_confirm:", buf.String())
	req := &dto.OnConfirmRequest{}
	err = json.NewDecoder(&buf).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	err = h.myService.OnConfirm(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.SendNack(ctx)
		//utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.SendAck(ctx)
}
