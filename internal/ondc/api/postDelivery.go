package api

import (
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) SubmitDeliveryRating(ctx *gin.Context) {
	request := dto.SubmitDeliveryRatingRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	// Set default virtual number if not provided
	resp, err := h.myService.SubmitDeliveryRating(ctx, &request)

	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)

}

func (h *<PERSON><PERSON>) GetDeliveryRating(ctx *gin.Context) {
	request := dto.GetDeliveryRatingRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetDeliveryRatingStatus(ctx, &request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)

}
