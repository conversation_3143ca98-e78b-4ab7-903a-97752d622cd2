package api

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func (h *Handler) Status(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	req := &dto.AppStatusReq{}

	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.Status(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}
func (h *Handler) OnStatus(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())

	r := ctx.Request.Body

	defer r.Close() // Close the request body when done with it

	// Read the request body and store it in a buffer for printing
	var buf bytes.Buffer
	_, err := buf.ReadFrom(r)
	if err != nil {
		logger.Error(ctx, "Error reading request body: %s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	// Print the value of the request body
	fmt.Println("Request Body for on_status:", buf.String())

	req := &dto.OnStatusRequest{}
	dummyReq := map[string]interface{}{}
	err = json.Unmarshal(buf.Bytes(), &dummyReq)
	if err != nil {
		fmt.Print("err = ", err)
	}
	value, ok := dummyReq["error"].(map[string]interface{})
	if ok {
		byt, _ := json.Marshal(value)
		errorr := dto.Error{}
		json.Unmarshal(byt, &errorr)
		if errorr.Code != nil || errorr.Message != "" {
			utils.RespondError(ctx, errors.New(errorr.Message), utils.ConstructErrorAPIResp(errors.New(errorr.Message)))
			return
		}
	}
	err = json.NewDecoder(&buf).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	err = h.myService.OnStatus(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.SendNack(ctx)
		//utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.SendAck(ctx)
}

func (h *Handler) GetOrderStatus(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	req := &dto.OrderStatusRequest{}

	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetOrderStatus(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) MapOrderStatus(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	req := &dto.MapOrderStatusRequest{}

	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.MapOrderStatus(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}
