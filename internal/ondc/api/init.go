package api

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"
)

func (h *Handler) Init(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	req := &dto.AppInitReq{}

	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.Init(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}
func (h *Handler) OnInit(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())

	r := ctx.Request.Body

	defer r.Close() // Close the request body when done with it

	// Read the request body and store it in a buffer for printing
	var buf bytes.Buffer
	_, err := buf.ReadFrom(r)
	if err != nil {
		logger.Error(ctx, "Error reading request body: %s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	// Print the value of the request body
	fmt.Println("Request Body for on_select:", buf.String())
	req := &dto.OnInitRequest{}
	err = json.NewDecoder(&buf).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	err = h.myService.OnInitReq(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.SendNack(ctx)
		//utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.SendAck(ctx)
}
