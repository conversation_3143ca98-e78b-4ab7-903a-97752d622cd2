package api

import (
	"encoding/json"
	"errors"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) ShipDelightWebhook(ctx *gin.Context) {

	var req any
	r := ctx.Request.Body

	key := ctx.Request.Header.Get("key")

	if key != utils.ShipDelightKey {
		err := errors.New("invalid_shipdelight_key")
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "Unauthorized request", http.StatusUnauthorized)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	}

	err := json.NewDecoder(r).Decode(&req)
	if err != nil {
		logger.Error(ctx, "%s", err.<PERSON>rror())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.ShipDelightWebhook(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}
