package api

import (
	"bytes"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func (h *Handler) Issues(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	req := &dto.AppIssueRequest{}

	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.Issues(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) OnIssues(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())

	r := ctx.Request.Body

	defer r.Close()
	var buf bytes.Buffer
	_, err := buf.ReadFrom(r)
	if err != nil {
		logger.Error(ctx, "Error reading request body: %s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	// Print the value of the request body
	fmt.Println("Request Body for on_issues:", buf.String())
	req := &dto.OnIssueRequest{}
	err = json.NewDecoder(&buf).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	err = h.myService.OnIssueRequest(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.SendNack(ctx)
		return
	}

	utils.SendAck(ctx)
}

func (h *Handler) IssueStatus(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	req := &dto.AppIssueStatusRequest{}

	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.IssuesStatus(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)

}

func (h *Handler) OnIssueStatus(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())

	r := ctx.Request.Body

	defer r.Close()
	var buf bytes.Buffer
	_, err := buf.ReadFrom(r)
	if err != nil {
		logger.Error(ctx, "Error reading request body: %s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	// Print the value of the request body
	fmt.Println("Request Body for on_issues_status:", buf.String())
	req := &dto.OnIssueRequest{}
	err = json.NewDecoder(&buf).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	err = h.myService.OnIssueStatusRequest(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.SendNack(ctx)
		return
	}

	utils.SendAck(ctx)
}

func (h *Handler) GetIGMTickets(ctx *gin.Context) {
	req := &dto.GetTicketsRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetIGMTickets(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) GetIGMTicketDetails(ctx *gin.Context) {
	id := ctx.Query("id")
	if id == "" {
		err := exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, nil, "id is required", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	ticketId, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		err := exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid id format", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetIGMTicketDetails(ctx, ticketId)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) CreateIGMTicket(ctx *gin.Context) {
	req := &dto.CreateTicketRequest{}
	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.CreateIGMTicket(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) UpdateIGMTicket(ctx *gin.Context) {
	req := &dto.UpdateTicketRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.UpdateIGMTicket(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}
func (h *Handler) GetIGMFAQ(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}

	resp, err := h.myService.GetIGMFAQ(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}
func (h *Handler) GetIGMOrders(ctx *gin.Context) {
	req := &dto.GetIGMOrdersRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetIGMOrders(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) GetIssuesCategories(ctx *gin.Context) {
	queryType := ctx.Query("type")

	resp, err := h.myService.GetIssuesCategories(ctx, queryType)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) GetIGMConversations(ctx *gin.Context) {
	ticketId := ctx.Query("ticket_id")
	if ticketId == "" {
		err := exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, nil, "ticket_id is required", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	id, err := strconv.ParseInt(ticketId, 10, 64)
	if err != nil {
		err := exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid ticket_id format", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetIGMConversations(ctx, id)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}
func (h *Handler) AddIGMConversations(ctx *gin.Context) {
	req := &dto.IGMConversationsRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.AddIGMConversations(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) MediaUpload(ctx *gin.Context) {
}
