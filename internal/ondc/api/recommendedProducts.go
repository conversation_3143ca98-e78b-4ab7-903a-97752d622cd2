package api

import (
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) GetRecommendedProducts(ctx *gin.Context) {
	var request dto.GetRecommendedProductsRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetRecommendedProducts(ctx, request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}