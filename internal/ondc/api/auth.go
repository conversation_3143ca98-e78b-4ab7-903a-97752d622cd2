package api

import (
	"fmt"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/middleware"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/auth"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

// Add these methods to your existing Handler
func (h *Handler) Login(ctx *gin.Context) {
	var req auth.LoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		serverErr := exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "Invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, serverErr, utils.ConstructErrorAPIResp(serverErr))
		return
	}

	// Additional security checks
	clientIP := ctx.ClientIP()
	userAgent := ctx.GetHeader("User-Agent")

	response, err := h.myService.Login(ctx, &req, clientIP, userAgent)
	if err != nil {
		serverErr := exceptions.GenerateNewServerError(exceptions.UserNotFound, err, "Login failed", http.StatusUnauthorized)
		utils.RespondError(ctx, serverErr, utils.ConstructErrorAPIResp(serverErr))
		return
	}

	// Enhanced cookie security
	// h.clearAllAuthCookies(ctx)
	ctx.SetSameSite(http.SameSiteNoneMode) // Changed from None to Strict

	// Access token cookie with enhanced security
	ctx.SetCookie(
		auth.AccessTokenCookieName, // Prefix for secure cookies
		response.AccessToken,
		3600,              // 1 hour
		"/",               // Restrict to API paths only
		".retailpulse.ai", // domain (will use current domain)
		true,              // secure - HTTPS only
		true,              // httpOnly
	)

	// Refresh token cookie with enhanced security
	ctx.SetCookie(
		auth.RefreshTokenCookieName, // Prefix for secure cookies
		response.RefreshToken,
		7*24*3600,         // 7 days
		"/",               // Restrict to refresh endpoint only
		".retailpulse.ai", // domain
		true,              // secure - HTTPS only
		true,              // httpOnly
	)

	// Set CSRF token as additional security layer
	csrfToken := h.myService.GenerateCSRFToken(ctx) // You'll need to implement this
	ctx.SetCookie(
		auth.CSRFCookieName,
		csrfToken,
		3600, // 1 hour
		"/",
		".retailpulse.ai",
		true, // secure
		true, // not httpOnly (JS needs to read this)
	)

	ctx.SetCookie(
		auth.LoginValidationKey, // Prefix for secure cookies
		auth.LoginValidationValue,
		7*24*3600,         // 7 days
		"/",               // Restrict to refresh endpoint only
		".retailpulse.ai", // domain
		true,              // secure - HTTPS only
		false,             // httpOnly
	)

	// Return response without tokens + CSRF token for client
	userResponse := struct {
		User      *auth.CSAgent `json:"user"`
		ExpiresIn int64         `json:"expires_in"`
	}{
		User:      response.User,
		ExpiresIn: response.ExpiresIn,
	}

	// Additional security headers
	ctx.Header("X-Content-Type-Options", "nosniff")
	ctx.Header("X-Frame-Options", "DENY")
	ctx.Header("X-XSS-Protection", "1; mode=block")

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("Login successful", userResponse))
}

func (h *Handler) RefreshToken(ctx *gin.Context) {
	// Get refresh token from secure cookie
	refreshToken, err := ctx.Cookie(auth.RefreshTokenCookieName)
	if err != nil {
		serverErr := exceptions.GenerateNewServerError(exceptions.InvalidAuthToken, err, "Refresh token not found", http.StatusUnauthorized)
		utils.RespondError(ctx, serverErr, utils.ConstructErrorAPIResp(serverErr))
		return
	}

	// Additional security validation
	clientIP := ctx.ClientIP()
	userAgent := ctx.GetHeader("User-Agent")

	req := auth.RefreshRequest{
		RefreshToken: refreshToken,
		ClientIP:     clientIP,
		UserAgent:    userAgent,
	}

	response, err := h.myService.RefreshToken(ctx, &req)
	if err != nil {
		// Clear cookies on refresh failure
		// h.clearAuthCookies(ctx)
		serverErr := exceptions.GenerateNewServerError(exceptions.InvalidAuthToken, err, "Token refresh failed", http.StatusUnauthorized)
		utils.RespondError(ctx, serverErr, utils.ConstructErrorAPIResp(serverErr))
		return
	}

	// Update access token cookie with new token
	ctx.SetSameSite(http.SameSiteNoneMode) // Changed from None to Strict
	ctx.SetCookie(
		auth.AccessTokenCookieName,
		response.AccessToken,
		3600, // 1 hour
		"/",  // Restrict to API paths
		".retailpulse.ai",
		true, // secure
		true, // httpOnly
	)

	// Generate new CSRF token
	csrfToken := h.myService.GenerateCSRFToken(ctx)
	ctx.SetCookie(
		auth.CSRFCookieName,
		csrfToken,
		3600,
		"/",
		".retailpulse.ai",
		true,
		true,
	)

	ctx.SetCookie(
		auth.LoginValidationKey, // Prefix for secure cookies
		auth.LoginValidationValue,
		7*24*3600,         // 7 days
		"/",               // Restrict to refresh endpoint only
		".retailpulse.ai", // domain
		true,              // secure - HTTPS only
		false,             // httpOnly
	)

	userResponse := struct {
		User      *auth.CSAgent `json:"user"`
		ExpiresIn int64         `json:"expires_in"`
	}{
		User:      response.User,
		ExpiresIn: response.ExpiresIn,
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("Token refreshed successfully", userResponse))
}

func (h *Handler) Logout(ctx *gin.Context) {
	refreshToken, err := ctx.Cookie(auth.RefreshTokenCookieName)
	if err != nil {
		refreshToken = ""
	}

	if refreshToken != "" {
		req := auth.RefreshRequest{
			RefreshToken: refreshToken,
		}
		err = h.myService.Logout(ctx, &req)
		if err != nil {
			// Log error but don't fail logout
		}
	}

	// Clear all auth-related cookies securely
	h.clearAuthCookies(ctx)

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("Logged out successfully", nil))
}

func (h *Handler) clearAuthCookies(ctx *gin.Context) {
	// Clear all variations of auth cookies
	ctx.SetSameSite(http.SameSiteNoneMode)
	ctx.SetCookie(
		auth.AccessTokenCookieName,
		"",
		-1,
		"/",
		".retailpulse.ai",
		true,
		true,
	)

	ctx.SetCookie(
		auth.RefreshTokenCookieName,
		"",
		-1,
		"/",
		".retailpulse.ai",
		true,
		true,
	)

	// Set CSRF token as additional security layer
	ctx.SetCookie(
		auth.CSRFCookieName,
		"",
		-1,
		"/",
		".retailpulse.ai",
		true,
		true,
	)

	ctx.SetCookie(
		auth.LoginValidationKey,
		"",
		-1,
		"/",
		".retailpulse.ai",
		true,
		false,
	)
}

func (h *Handler) GetProfile(ctx *gin.Context) {
	user, exists := middleware.GetUserFromContext(ctx)
	if !exists {
		serverErr := exceptions.GenerateNewServerError(exceptions.UserNotFound, fmt.Errorf("user not found in context"), "User not found", http.StatusInternalServerError)
		utils.RespondError(ctx, serverErr, utils.ConstructErrorAPIResp(serverErr))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("Profile retrieved successfully", user))
}

func (h *Handler) TrueCallerWebhook(ctx *gin.Context) {
	var req dto.TrueCallerWebhookRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		serverErr := exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "Invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, serverErr, utils.ConstructErrorAPIResp(serverErr))
		return
	}

	err := h.myService.SaveTrueCallerWebhook(ctx, req)
	if err != nil {
		serverErr := exceptions.GenerateNewServerError(exceptions.InternalError, err, "Failed to save webhook data", http.StatusInternalServerError)
		utils.RespondError(ctx, serverErr, utils.ConstructErrorAPIResp(serverErr))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("Webhook data saved", nil))
}

func (h *Handler) ResolveWebhook(ctx *gin.Context) {
	var req dto.ResolveWebhookRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		serverErr := exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "Invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, serverErr, utils.ConstructErrorAPIResp(serverErr))
		return
	}

	resp, err := h.myService.ResolveWebhook(ctx, req.RequestId)
	if err != nil {
		serverErr := exceptions.GenerateNewServerError(exceptions.InternalError, err, "Failed to resolve webhook", http.StatusNoContent)
		utils.RespondError(ctx, serverErr, utils.ConstructErrorAPIResp(serverErr))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}
