package api

import (
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

// GetOrderProducts handles the request to get order products for refund
func (h *Handler) GetOrderProducts(ctx *gin.Context) {
	var request dto.GetOrderProductsRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetOrderProducts(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

// GetRefundOptions handles the request to get refund options
func (h *Handler) GetRefundOptions(ctx *gin.Context) {
	var request dto.GetRefundOptionsRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetRefundOptions(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

// InitiateRefund handles the request to initiate a refund
func (h *Handler) InitiateRefund(ctx *gin.Context) {
	var request dto.InitiateRefundRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.InitiateRefund(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func (h *Handler) UpdatePaymentRefundKYC(ctx *gin.Context) {
	var request dto.UpdateKYCRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.ProcessPaymentRefundKYCUpdate(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}
