package api

import (
	"encoding/json"
	"errors"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/utils"
	"net/http"

	"kc/internal/ondc/models/dto"

	"github.com/gin-gonic/gin"
)

func (h *Handler) ExpressBeesWebhook(ctx *gin.Context) {
	accountName := ctx.Query("account_name")
	if accountName == "" {
		accountName = "zoff_foods_heavy"
	}

	var req dto.ExpressBeesWebhookRequest
	r := ctx.Request.Body

	key := ctx.Request.Header.Get("key")

	if key != utils.ExpressBeesKey {
		err := errors.New("invalid_expressbees_key")
		logger.Error(ctx, "%s", err.<PERSON>rror())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "Unauthorized request", http.StatusUnauthorized)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	}

	err := json.NewDecoder(r).Decode(&req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.ExpressBeesWebhook(ctx, &req, accountName)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}
