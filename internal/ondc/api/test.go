package api

import (
	"io"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) Test(c *gin.Context) {
	// Read the request body
	requestByt, err := io.ReadAll(c.Request.Body)
	if err != nil {
		logger.Error(c, "failed to read request body: %s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "failed to read request body", http.StatusBadRequest)
		utils.RespondError(c, err, utils.ConstructErrorAPIResp(err))
		return
	}
	defer c.Request.Body.Close()

	response, err := h.myService.Test(requestByt)
	if err != nil {
		logger.Error(c, err.Error())
		utils.RespondError(c, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(c, http.StatusOK, response)
}
