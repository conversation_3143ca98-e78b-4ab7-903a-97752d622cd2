package api

import (
	"encoding/json"
	"kc/internal/ondc/exceptions"

	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *<PERSON>ler) EkartWebhook(ctx *gin.Context) {
	// key := ctx.Request.Header.Get("key")

	accountName := ctx.Query("account_name")
	if accountName == "" {
		accountName = "kiranaclub-kic"
	}

	// kiranaclub-kic

	// if key != utils.EkartKey {
	// 	err := errors.New("invalid_ekart_key")
	// 	logger.Error(ctx, "%s", err.Error())
	// 	err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "Unauthorized request", http.StatusUnauthorized)
	// 	utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	// 	return
	// }

	var req dto.EkartWebhookRequestInterface
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(&req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.EkartWebhook(ctx, &req, accountName)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "Error processing webhook", http.StatusInternalServerError)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) PushOrderToEkart(ctx *gin.Context) {
	orderID := ctx.Query("orderID")
	if orderID == "" {
		logger.Error(ctx, "orderID query parameter is required")
		err := exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, nil, "orderID query parameter is required", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.PushOrderToEkart(ctx, orderID)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	ctx.JSON(http.StatusOK, resp)
}
