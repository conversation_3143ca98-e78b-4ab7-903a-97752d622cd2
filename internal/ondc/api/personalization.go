package api

import (
	"github.com/gin-gonic/gin"
	"kc/internal/ondc/models/dto"
	"net/http"
)

func (h *Handler) GetCartProducts(c *gin.Context) {
	var req dto.CartProductsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	products, err := h.myService.GetCartProducts(c, req.Data.UserID, req.Data.Brand)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSO<PERSON>(http.StatusOK, dto.CartProductsResponse{Products: products})
}
