package api

import (
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

// GetCoupons returns all the valid coupons for the user.
func (h *Handler) GetCoupons(ctx *gin.Context) {
	var request dto.GetCouponsRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetCoupons(ctx, request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) GetApplicableCoupons(ctx *gin.Context) {
	var request dto.GetApplicableCouponsRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetApplicableCoupons(ctx, request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) GetCouponsBottomSheet(ctx *gin.Context) {
	var request dto.GetApplicableCouponsRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetCouponsBottomSheet(ctx, request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) CheckCoupon(ctx *gin.Context) {
	var request dto.CheckCouponRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.CheckCoupon(ctx, request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) CheckCouponV2(ctx *gin.Context) {
	var request dto.GetApplicableCouponsRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.CheckCouponV2(ctx, request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}
