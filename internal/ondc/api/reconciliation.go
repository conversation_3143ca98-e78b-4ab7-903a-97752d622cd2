package api

import (
	"encoding/json"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) GrossSales(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GrossSales(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)

}

func (h *Handler) OrderPlaced(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.OrderPlaced(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)

}

func (h *Handler) AverageOrderValue(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.AverageOrderValue(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) ConfirmedValue(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.ConfirmedValue(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) DeliveredOrder(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.DeliveredOrder(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) DispatchedOrderNotDelivered(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.DispatchedOrderNotDelivered(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) CancelledOrder(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.CancelledOrder(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) PendingConfirmation(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.PendingConfirmation(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) PendingDispatch(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.PendingDispatch(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) OrdersReturned(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.OrdersReturned(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}


func (h *Handler) TransitOrder(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.TransitOrder(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) ReturnedOrder(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.ReturnedOrder(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) DelayedOrder(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.DelayedOrder(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) AverageDeliveryTime(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.AverageDeliveryTime(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) AverageConfirmationTime(ctx *gin.Context) {
	req := &dto.ReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.AverageConfirmationTime(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) AddDataForReconciliation(ctx *gin.Context) {
	req := &dto.AddReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.AddDataForReconciliation(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)

}

func (h *Handler) GetReconciliationData(ctx *gin.Context) {
	req := &dto.GetReconciliationRequest{}
	r := ctx.Request.Body

	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetReconciliationData(ctx, req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}
