package api

import (
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) GetBillDetailsV3(ctx *gin.Context) {
	var request dto.GetBillDetailsRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetBillDetailsV3(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *<PERSON><PERSON>) GetBillDetailsV2(ctx *gin.Context) {
	var request dto.GetBillDetailsRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetBillDetailsV2(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) GetBillDetails(ctx *gin.Context) {
	var request dto.GetBillDetailsRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetBillDetails(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) CreateOrderInvoice(ctx *gin.Context) {
	var request dto.CreateInvoiceRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.CreateOrderInvoice(ctx, request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) BillDifference(ctx *gin.Context) {
	var request dto.BillDifferenceRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.BillDifference(ctx, request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}
