package api

import (
	"kc/internal/ondc/infrastructure/logging"
	"kc/internal/ondc/service"
	"kc/internal/ondc/utils"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

var logger = logging.GetLogrusLogger("api")

type Handler struct {
	myService *service.Service
	cache     *utils.Cache
}

func NewHandler(myService *service.Service) *Handler {
	return &Handler{
		cache:     utils.NewCache(1 * time.Minute),
		myService: myService,
	}
}

func (h *Handler) PerformBusinessLogic(c *gin.Context) {
	result, err := h.myService.PerformBusinessLogic()
	if err != nil {
		utils.RespondError(c, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(c, http.StatusOK, utils.ConstructAPIResp("OK", result))
	return
}
