package api

import (
	"bytes"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func (h *Handler) SearchV2(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	req := &dto.AppSearchRequest{}

	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	var resp *dto.AppSearchResponses
	// if req.Data.Source != nil {
	// 	resp, err = h.myService.GetProductsV2(ctx, req)
	// } else {
	// 	resp, err = h.myService.HandleSearchReq(ctx, req)
	// }
	resp, err = h.myService.GetProductsV2(ctx, req)
	// now sending 200 even if no servicable
	// if err != nil {
	// 	logger.Error(ctx, err.Error())
	// 	utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	// 	return
	// }
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) ProductsSearch(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	req := &dto.AppSearchRequest{}

	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	var resp *dto.AppProductsSearchResponses
	resp, err = h.myService.HandleProductsSearch(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) Search(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	req := &dto.AppSearchRequest{}

	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	var resp *dto.AppSearchResponses
	if req.Data.Source != nil {
		resp, err = h.myService.GetProducts(ctx, req)
	} else {
		resp, err = h.myService.HandleSearchReq(ctx, req)
	}
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) OnSearch(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())

	r := ctx.Request.Body

	defer r.Close() // Close the request body when done with it

	// Read the request body and store it in a buffer for printing
	var buf bytes.Buffer
	_, err := buf.ReadFrom(r)
	if err != nil {
		logger.Error(ctx, "Error reading request body: %s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	// Print the value of the request body
	fmt.Println("Request Body:", buf.String())
	req := &dto.OnSearchRequest{}
	err = json.NewDecoder(&buf).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	err = h.myService.OnSearch(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.SendNack(ctx)
		//utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.SendAck(ctx)
}

func (h *Handler) CreateElasticIndex(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	req := &dto.ElasticCreateIndexRequest{}

	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp := "Enhanced Index Created Succesfully. Please Check App Ordering Search"
	err = h.myService.CreateElasticEnhancedIndex(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}
