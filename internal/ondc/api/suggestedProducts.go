package api

import (
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) GetSuggestedProductsV2(ctx *gin.Context) {
	var request dto.GetSuggestedProductsRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetSuggestedProductsV2(ctx, request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}


func (h *Handler) GetSuggestedProducts(ctx *gin.Context) {
	var request dto.GetSuggestedProductsRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetSuggestedProducts(ctx, request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}
