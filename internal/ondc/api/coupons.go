package api

import (
	"fmt"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func (h *Handler) GetCouponNameMap(ctx *gin.Context) {
	resp, err := h.myService.GetCouponNameMap(ctx)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) GetCouponByCode(ctx *gin.Context) {
	code := ctx.Query("code")
	if code == "" {
		err := fmt.Errorf("coupon_code is required")
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetCouponByCode(ctx, code, false)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) GetCouponByName(ctx *gin.Context) {
	name := ctx.Query("name")
	if name == "" {
		err := fmt.Errorf("coupon_name is required")
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetCouponByName(ctx, name, false)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

// Use the main Handler struct for coupon endpoints, not a separate CouponHandler.

// AddCoupon adds a new coupon
func (h *Handler) AddCoupon(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	var req dto.CouponAddRequest
	if err := ctx.BindJSON(&req); err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	id, err := h.myService.Coupons.AddCoupon(ctx, &req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, gin.H{"id": id})
}

// EditCoupon updates an existing coupon
func (h *Handler) EditCoupon(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	var req dto.CouponEditRequest
	if err := ctx.BindJSON(&req); err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	err := h.myService.Coupons.EditCoupon(ctx, &req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, gin.H{"success": true})
}

// GetCouponByID retrieves a coupon by its ID
func (h *Handler) GetCouponByID(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	id, err := strconv.ParseInt(ctx.Param("id"), 10, 64)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	coupon, err := h.myService.Coupons.GetCouponByID(ctx, id)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	if coupon == nil {
		utils.RespondJSON(ctx, http.StatusNotFound, gin.H{"error": "coupon not found"})
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, coupon)
}

// GetCouponBrandsByCouponID retrieves all brands for a coupon
func (h *Handler) GetCouponBrandsByCouponID(c *gin.Context) {
	couponID, err := strconv.ParseInt(c.Param("coupon_id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid coupon ID"})
		return
	}

	brands, err := h.myService.Coupons.GetCouponBrandsByCouponID(c.Request.Context(), couponID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"brands": brands})
}

// GetCouponCohortsByCouponID retrieves all cohorts for a coupon
func (h *Handler) GetCouponCohortsByCouponID(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())
	couponID, err := strconv.ParseInt(ctx.Param("coupon_id"), 10, 64)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	cohorts, err := h.myService.Coupons.GetCouponCohorts(ctx, couponID)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, gin.H{"cohorts": cohorts})
}

// Change GetAllCoupons to POST and accept params in JSON body
func (h *Handler) GetAllCoupons(c *gin.Context) {
	var req dto.GetAllCouponsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	resp, err := h.myService.Coupons.GetAllCoupons(c, req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, resp)
}

// GetAllBrandIds returns a list of all brand IDs from the service layer
func (h *Handler) GetAllBrandIds(ctx *gin.Context) {
	brands, err := h.myService.GetAllBrandIds(ctx)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch brands"})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"brands": brands})
}

// GetAllCohortIds returns a list of all cohort IDs from the service layer
func (h *Handler) GetAllCohortIds(ctx *gin.Context) {
	cohorts, err := h.myService.GetAllCohortIds(ctx)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch cohorts"})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"cohorts": cohorts})
}

// GetCouponRulesByCouponID retrieves all rules for a coupon
func (h *Handler) GetCouponRulesByCouponID(ctx *gin.Context) {
	couponID := ctx.Param("coupon_id")
	if couponID == "" {
		utils.RespondError(ctx, fmt.Errorf("coupon_id is required"), utils.ConstructErrorAPIResp(fmt.Errorf("coupon_id is required")))
		return
	}
	couponIDInt, err := strconv.ParseInt(couponID, 10, 64)
	if err != nil {
		utils.RespondError(ctx, fmt.Errorf("invalid coupon_id: %v", err), utils.ConstructErrorAPIResp(fmt.Errorf("invalid coupon_id: %v", err)))
		return
	}

	rules, err := h.myService.Coupons.GetCouponRulesByCouponID(ctx, couponIDInt)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, rules)
}

func (h *Handler) AddUserCoupon(ctx *gin.Context) {
	var req dto.AddUserCouponRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	err := h.myService.Coupons.AddUserCoupon(ctx, &req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, gin.H{"success": true})

}

func (h *Handler) DeactivateUserCoupon(ctx *gin.Context) {
	var req dto.DeactivateUserCouponRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	err := h.myService.Coupons.DeactivateUserCoupon(ctx, &req)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, gin.H{"success": true})
}
