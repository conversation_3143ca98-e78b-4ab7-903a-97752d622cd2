package api

import (
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) GetKiranaBazarCart(ctx *gin.Context) {
	var req dto.AppGetKiranaBazarCartRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetKiranaBazarCart(ctx, &req)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) UpsertKiranaBazarCart(ctx *gin.Context) {
	var req dto.AppUpsertKiranaBazarCartRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.UpsertKiranaBazarCart(ctx, &req)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) UpsertKiranaBazarCartV2(ctx *gin.Context) {
	var req dto.AppUpsertKiranaBazarCartRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.UpsertKiranaBazarCartV2(ctx, &req)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}
