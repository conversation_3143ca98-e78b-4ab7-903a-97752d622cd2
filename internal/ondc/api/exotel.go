package api

import (
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func (h *Handler) ExotelWebhook(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())

	seller := ctx.Param("seller")

	req := &dto.WebhookCallDetails{
		CallSid:                     ctx.Query("CallSid"),
		CallType:                    ctx.Query("CallType"),
		CallFrom:                    ctx.Query("CallFrom"),
		CallTo:                      ctx.Query("CallTo"),
		Direction:                   ctx.Query("Direction"),
		ForwardedFrom:               ctx.Query("ForwardedFrom"),
		Created:                     ctx.Query("Created"),
		DialCallDuration:            ctx.Query("DialCallDuration"),
		RecordingUrl:                ctx.Query("RecordingUrl"),
		StartTime:                   ctx.Query("StartTime"),
		EndTime:                     ctx.Query("EndTime"),
		DialCallStatus:              ctx.Query("DialCallStatus"),
		DialWhomNumber:              ctx.Query("DialWhomNumber"),
		ProcessStatus:               ctx.Query("ProcessStatus"),
		FlowID:                      ctx.Query("flow_id"),
		TenantID:                    ctx.Query("tenant_id"),
		From:                        ctx.Query("From"),
		To:                          ctx.Query("To"),
		CurrentTime:                 ctx.Query("CurrentTime"),
		CustomField:                 ctx.Query("CustomField"),
		Digits:                      ctx.Query("digits"),
		HangupLatencyStartTimeExocc: ctx.Query("HangupLatencyStartTimeExocc"),
		HangupLatencyStartTime:      ctx.Query("HangupLatencyStartTime"),
		Seller:                      seller,
	}

	resp, err := h.myService.ExotelWebhook(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) ExotelReactivationWebhook(ctx *gin.Context) {
	ctx.Set("key", uuid.NewString())

	req := &dto.WebhookCallDetails{
		CallSid:                     ctx.Query("CallSid"),
		CallType:                    ctx.Query("CallType"),
		CallFrom:                    ctx.Query("CallFrom"),
		CallTo:                      ctx.Query("CallTo"),
		Direction:                   ctx.Query("Direction"),
		ForwardedFrom:               ctx.Query("ForwardedFrom"),
		Created:                     ctx.Query("Created"),
		DialCallDuration:            ctx.Query("DialCallDuration"),
		RecordingUrl:                ctx.Query("RecordingUrl"),
		StartTime:                   ctx.Query("StartTime"),
		EndTime:                     ctx.Query("EndTime"),
		DialCallStatus:              ctx.Query("DialCallStatus"),
		DialWhomNumber:              ctx.Query("DialWhomNumber"),
		ProcessStatus:               ctx.Query("ProcessStatus"),
		FlowID:                      ctx.Query("flow_id"),
		TenantID:                    ctx.Query("tenant_id"),
		From:                        ctx.Query("From"),
		To:                          ctx.Query("To"),
		CurrentTime:                 ctx.Query("CurrentTime"),
		CustomField:                 ctx.Query("CustomField"),
		Digits:                      ctx.Query("digits"),
		HangupLatencyStartTimeExocc: ctx.Query("HangupLatencyStartTimeExocc"),
		HangupLatencyStartTime:      ctx.Query("HangupLatencyStartTime"),
	}

	resp, err := h.myService.ExotelReactivationWebhook(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}
