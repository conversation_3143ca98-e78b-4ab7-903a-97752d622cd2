package api

import (
	"encoding/json"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) GetUserSellerLevelOrderStats(ctx *gin.Context) {
	req := &dto.SellerLevelOrderStats{}

	r := ctx.Request.Body
	defer r.Close()

	err := json.NewDecoder(r).Decode(req)

	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetUserSellerLevelOrderStats(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) ResolveWidgetAccordingToOrderCount(ctx *gin.Context) {

	userId := ctx.Query("user_id")

	if userId == "" {
		userId = "temp_user_id"
	}

	resp, err := h.myService.ResolveWidgetAccordingToOrderCount(ctx, userId)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)

}

func (h *Handler) GetUserSellerLevelOrderStatsFork(ctx *gin.Context) {
	req := &dto.SellerLevelOrderStats{}

	r := ctx.Request.Body
	defer r.Close()

	err := json.NewDecoder(r).Decode(req)

	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetUserSellerLevelOrderStatsFork(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}
