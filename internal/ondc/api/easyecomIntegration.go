package api

import (
	"bytes"
	"encoding/json"

	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) CreateEasyEcomOrder(ctx *gin.Context) {
	req := &dto.AppEasyEcomCreateOrderRequest{}
	r := ctx.Request.Body
	defer r.Close()
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	}

	resp, err := h.myService.CreateEasyEcomOrder(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) EasyEcomWebhook(ctx *gin.Context) {
	r := ctx.Request.Body
	defer r.Close()
	var buf bytes.Buffer
	_, err := buf.ReadFrom(r)
	if err != nil {
		logger.Error(ctx, "Error reading request body: %s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	h.myService.EasyEcomWebhook(buf.String())
	utils.RespondJSON(ctx, http.StatusOK, true)
}

func (h *Handler) GetEasyEcomOrderDetails(ctx *gin.Context) {
	req := &dto.EasyEcomGetOrderDetailsRequest{}
	r := ctx.Request.Body
	defer r.Close()
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	}
	
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "not able to get invoice id", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	}
	resp, err := h.myService.GetEasyEcomOrderDetails(ctx, req.Data.OrderID)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}
