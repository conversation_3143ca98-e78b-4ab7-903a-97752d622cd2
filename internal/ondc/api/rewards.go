package api

import (
	"errors"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) CheckUserRewards(ctx *gin.Context) {
	var request dto.CheckUserRewardsRequest

	if err := ctx.ShouldBindJSON(&request); err != nil {
		logger.Error(ctx, err.Error())
		ctx.JSON(400, gin.H{"error": "Invalid request"})
		return
	}

	resp, err := h.myService.CheckUserRewards(ctx, request)

	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, resp)
}

func (h *Handler) ClaimUserRewards(ctx *gin.Context) {

	var request dto.CreateUserLoyaltyOrderRequest

	if err := ctx.ShouldBindJSON(&request); err != nil {
		logger.Error(ctx, err.Error())
		ctx.JSON(400, gin.H{"error": "Invalid request"})
		return
	}

	hash, err := utils.GenerateHash(request)
	if err != nil {
		logger.Error(ctx, "Failed to generate hash: %v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	if h.cache.Exists(hash) {
		err = errors.New("duplicate request detected, can not process")
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		logger.Error(ctx, "%v", err.Error())
		return
	}
	h.cache.Add(hash)

	resp, err := h.myService.ClaimUserRewards(ctx, request)

	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}
