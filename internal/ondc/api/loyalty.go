package api

import (
	"errors"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handler) CreateUserLoyaltyRewardOrder(ctx *gin.Context) {
	request := dto.CreateUserLoyaltyOrderRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
	}

	hash, err := utils.GenerateHash(request)
	if err != nil {
		logger.Error(ctx, "Failed to generate hash: %v", err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	if h.cache.Exists(hash) {
		err = errors.New("duplicate request detected, can not process")
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		logger.Error(ctx, "%v", err.Error())
		return
	}
	h.cache.Add(hash)

	resp, err := h.myService.CreateUserLoyaltyRewardOrder(ctx, request)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("err while creating order in CreateKiranaBazarOrderV2 err = %v", err))
		if err.Error() == "not servicable" {
			err = errors.New("आपके पिनकोड पर ऑर्डर सर्विसेबल नहीं है। कृपया पिनकोड बदलकर वापस प्रयास करें")
			logger.Error(ctx, "%v", err.Error())
			utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
			return
		}
		logger.Error(ctx, "%v", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}
