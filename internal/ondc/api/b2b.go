package api

import (
	"encoding/json"
	"fmt"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

func (h *Handler) UpdateB2BOrderStatus(ctx *gin.Context) {
	request := dto.UpdateB2BOrderStatusRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.UpdateB2BOrderStatus(ctx, &request)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.<PERSON>rror()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func (h *<PERSON><PERSON>) UpdateOrderAddress(ctx *gin.Context) {
	request := dto.UpdateOrderAddressRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.UpdateB2BOrderAddress(ctx, &request)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func (h *Handler) GetOrders(ctx *gin.Context) {
	request := dto.GetOrdersRequest{
		From:     time.Now().AddDate(0, 0, -7).UnixMilli(),
		To:       time.Now().UnixMilli(),
		Limit:    utils.LIMIT_DEFAULT,
		Offset:   utils.OFFSET_DEFAULT,
		RecoDays: utils.RECO_DAYS_DEFAULT,
		Seller:   []string{},
		Status:   utils.ORDER_STATUS_DEFAULT}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	if (len(request.RecoStatus) > 0) && (request.RecoStatus[0] == "ALL") {
		request.RecoStatus = utils.RECO_STATUS_DEFAULT
	}

	resp, err := h.myService.GetOrders(ctx, &request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetOrdersExtended(ctx *gin.Context) {
	request := dto.GetOrdersExtendedRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetOrdersExtended(ctx, &request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) GetCallingOrders(ctx *gin.Context) {
	request := dto.GetOrdersExtendedRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetCallingOrders(ctx, &request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) GetPendingShipments(ctx *gin.Context) {
	request := dto.GetPendingShipmentsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetPendingShipments(ctx, &request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) ExportOrders(ctx *gin.Context) {
	request := dto.ExportOrderRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.ExportOrders(ctx, &request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) ExportOrdersCSV(ctx *gin.Context) {
	request := dto.ExportOrderRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	err = h.myService.ExportOrdersCSV(ctx, &request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
}

func (h *Handler) ExportProductCatalogue(ctx *gin.Context) {
	request := dto.ProductCatalogueExportRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.ExportProductCatalogue(ctx, &request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) ExportProductCatalogueCSV(ctx *gin.Context) {
	request := dto.ProductCatalogueExportRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	err = h.myService.ExportProductCatalogueCSV(ctx, &request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
}

// ExportOrdersGenericSKULevel this returns the records at sku level and is called for sellers
func (h *Handler) ExportOrdersGenericSKULevel(ctx *gin.Context) {
	request := dto.GetOrdersRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.ExportOrdersGenericSKULevel(ctx, &request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

// ExportOrdersGeneric this returns the records at order level and is called for sellers
func (h *Handler) ExportOrdersGeneric(ctx *gin.Context) {
	request := dto.GetOrdersRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.ExportOrdersGeneric(ctx, &request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

// ExportOrdersGenericSKULevelCSV this exports the csv at sku level and is called for internal b2b users
func (h *Handler) ExportOrdersGenericSKULevelCSV(ctx *gin.Context) {
	request := dto.GetOrdersRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	err = h.myService.ExportOrdersGenericSKULevelCSV(ctx, &request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
}

// ExportOrdersGenericCSV this exports the csv at order level and is called for internal b2b users
func (h *Handler) ExportOrdersGenericCSV(ctx *gin.Context) {
	request := dto.GetOrdersRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	err = h.myService.ExportOrdersGenericCSV(ctx, &request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
}

func (h *Handler) GetBrandInsights(ctx *gin.Context) {
	request := dto.GetBrandInsightsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetBrandInsights(ctx, &request)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetCarriers(ctx *gin.Context) {
	resp, err := h.myService.GetCarriers(ctx)
	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) UpdateOrderMetaData(ctx *gin.Context) {
	request := dto.UpdateOrderReasonsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.UpdateOrderMetaData(ctx, &request)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func (h *Handler) ArchiveOrder(ctx *gin.Context) {
	request := dto.ArchiveOrderRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.ArchiveOrder(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetNDROrders(ctx *gin.Context) {
	request := dto.GetNDROrdersRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetNDROrders(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) UpdateNDROrder(ctx *gin.Context) {
	request := dto.UpdateNDROrderRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.UpdateNDROrder(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) AddNDROrder(ctx *gin.Context) {
	request := dto.AddNDROrderRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.AddNDROrder(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetOrderActivityLogs(ctx *gin.Context) {
	request := dto.GetOrderActivityLogsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetOrderActivityLogs(ctx, &request.Data.OrderActivityID)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetB2BCategories(ctx *gin.Context) {
	request := dto.GetB2BCategoriesRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetB2BCategories(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetB2BProducts(ctx *gin.Context) {
	request := dto.GetB2BProductsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetB2BProducts(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) UpdateBulkData(ctx *gin.Context) {
	request := dto.UpdateBulkDataRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.SubmitBulkData(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetBulkActionLogs(ctx *gin.Context) {
	request := dto.GetBulkActionLogsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetBulkActionLogs(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetOrdersPaymentDetails(ctx *gin.Context) {
	request := dto.GetOrdersPaymentDetailsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetOrdersPaymentDetails(ctx, &request)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func (h *Handler) ExportOrderPaymentDetails(ctx *gin.Context) {
	request := dto.ExportOrderPaymentDetailsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.ExportOrderPaymentDetails(ctx, &request)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

// Update order data
func (h *Handler) UpdateThirdPartySellerOrder(ctx *gin.Context) {
	request := dto.BillDifferenceRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.UpdateThirdPartySellerOrder(ctx, request)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) CreateThirdPartySellerOrder(ctx *gin.Context) {
	request := dto.BillDifferenceRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.CreateThirdPartySellerOrder(ctx, request)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

// Update order data
func (h *Handler) UpdateProductDetails(ctx *gin.Context) {
	request := dto.UpdateProductDetailsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.UpdateProductDetails(ctx, &request)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) AddProduct(ctx *gin.Context) {
	request := dto.AddProductRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	var validate = validator.New()
	err = validate.Struct(request)
	if err != nil {
		var validationErrors []string
		for _, err := range err.(validator.ValidationErrors) {
			validationErrors = append(validationErrors, utils.FormatValidationError(err))
		}
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, fmt.Sprintf("Validation Failed: %v", validationErrors), http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.AddProduct(ctx, &request)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

// UpdatePrimaryWaybill function updates the primary waybill for order
func (h *Handler) UpdatePrimaryWaybill(ctx *gin.Context) {
	req := &dto.UpdatePrimaryWaybillRequest{}
	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.UpdatePrimaryWaybill(ctx, req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) FetchWallWaybills(ctx *gin.Context) {
	req := &dto.FetchAllWaybillsRequest{}
	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.FetchWallWaybills(ctx, *req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) GetWayBillDetails(ctx *gin.Context) {
	req := &dto.GetWaybillDetailsRequest{}
	r := ctx.Request.Body
	err := json.NewDecoder(r).Decode(req)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetWayBillDetails(ctx, *req)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) GetB2BMeta(ctx *gin.Context) {
	request := dto.GetB2BMetaRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetB2BMeta(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetPickListPDF(ctx *gin.Context) {
	request := dto.GetPickListPDFRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.GetPickListPDF(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

// CreatePickListForOrder handles picklist generation for a single order
func (h *Handler) CreatePickListForOrder(ctx *gin.Context) {
	request := dto.CreatePickListForOrderRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.CreatePickListForOrder(ctx, request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

// PushOrderToOMS handles b2b side order confirmation and pushing to OMS
func (h *Handler) PushOrderToOMS(ctx *gin.Context) {
	request := dto.PushOrderToOMSRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.PushOrderToOMSWithLogs(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

// PushOrderToOMS handles b2b side order confirmation and pushing to OMS
func (h *Handler) RePushOrderToOMS(ctx *gin.Context) {
	request := dto.PushOrderToOMSRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	// this is repush/recreate -- this is handled only when we are repushing order via internal apis, when the order is shipped via shipdelight this will not be a valid case here
	// but this case will not be the case in maximum cases
	request.Data.Force = true
	resp, err := h.myService.PushOrderToOMSWithLogs(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

// PushOrderToDelhivery handles b2b side order confirmation and pushing to Delhivery
func (h *Handler) PushOrderToDelhivery(ctx *gin.Context) {
	orderID := ctx.Query("orderID")
	if orderID == "" {
		logger.Error(ctx, "orderID query parameter is required")
		err := exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, nil, "orderID query parameter is required", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.PushOrderToDelhivery(ctx, orderID)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func (h *Handler) CreateExtInvoice(ctx *gin.Context) {
	request := dto.CreateExtInvoiceRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.CreateExtInvoice(ctx, request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func (h *Handler) GetNDROrdersV2(ctx *gin.Context) {
	request := dto.GetNDROrdersRequestV2{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetNDROrdersV2(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) UpdateNDROrderV2(ctx *gin.Context) {
	request := dto.UpdateNDROrderRequestV2{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.UpdateNDROrderV2(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) EscalateTo3PL(ctx *gin.Context) {
	request := dto.EscalateTo3PLRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	resp, err := h.myService.EscalateTo3PL(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) ExportNDROrders(ctx *gin.Context) {
	request := dto.GetNDROrdersRequestV2{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	err = h.myService.ExportNDROrders(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
}

func (h *Handler) ExportLeaderBoardOrders(ctx *gin.Context) {
	request := dto.GetSKULeaderboardRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	_, err = h.myService.ExportSKULeaderboard(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

}

func (h *Handler) GetUserTierInfo(ctx *gin.Context) {
	request := dto.GetUserTierInfoRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetUserTierInfo(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetUsersOrderHistory(ctx *gin.Context) {
	request := dto.GetUsersOrderHistoryRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetUsersOrderHistory(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetUsersTierUpgradeHistory(ctx *gin.Context) {
	request := dto.GetUsersTierUpgradeHistoryRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetUsersTierUpgradeHistory(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetUsersCoinsWallet(ctx *gin.Context) {
	request := dto.GetUsersCoinsWalletRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetUsersCoinsWallet(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetUsersCashbackWallet(ctx *gin.Context) {
	request := dto.GetUsersCashbackWalletRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetUsersCashbackWallet(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) CreateAndSaveExtInvoice(ctx *gin.Context) {
	request := dto.GetOrderIdRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.CreateAndSaveExtInvoice(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) CreateAndSavePrintingLabel(ctx *gin.Context) {
	request := dto.GetOrderIdRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.CreateAndSavePrintingLabel(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))

}

func (h *Handler) GetOrderPaymentDetails(ctx *gin.Context) {
	request := dto.GetOrderPaymentDetailsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetOrderPaymentDetails(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))

}

func (h *Handler) GetKPICards(ctx *gin.Context) {
	request := dto.GetKPICardsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetKPICards(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetSKULeaderboard(ctx *gin.Context) {
	request := dto.GetSKULeaderboardRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetSKULeaderboard(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) GetSKUTrends(ctx *gin.Context) {
	request := dto.GetSKUTrendsRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.GetSKUTrends(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}

func (h *Handler) CancelOrderB2B(ctx *gin.Context) {
	request := dto.CancelOrderB2BRequest{}
	err := ctx.BindJSON(&request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.CancelOrderB2B(ctx, &request)

	if err != nil {
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, utils.ConstructAPIResp("OK", resp))
}