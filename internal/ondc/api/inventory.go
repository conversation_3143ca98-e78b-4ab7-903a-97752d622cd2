package api

import (
	"encoding/json"
	"fmt"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/utils"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

func (h *Hand<PERSON>) AddProductInventory(ctx *gin.Context) {
	request := &dto.AddInventoryRequest{}

	r := ctx.Request.Body
	defer r.Close()

	err := json.NewDecoder(r).Decode(request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	var validate = validator.New()
	err = validate.Struct(request)
	if err != nil {
		var validationErrors []string
		for _, err := range err.(validator.ValidationErrors) {
			validationErrors = append(validationErrors, utils.FormatValidationError(err))
		}
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, fmt.Sprintf("Validation Failed: %v", validationErrors), http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	resp, err := h.myService.AddProductInventory(ctx, request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	utils.RespondJSON(ctx, http.StatusOK, resp)
}

func (h *Handler) ExportInventoryProducts(ctx *gin.Context) {
	request := &dto.ExportInventoryProductsRequest{}

	r := ctx.Request.Body
	defer r.Close()

	err := json.NewDecoder(r).Decode(request)
	if err != nil {
		logger.Error(ctx, "%s", err.Error())
		err = exceptions.GenerateNewServerError(exceptions.InvalidRequestStruct, err, "invalid request", http.StatusBadRequest)
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}

	err = h.myService.ExportInventoryProducts(ctx, request)
	if err != nil {
		logger.Error(ctx, err.Error())
		utils.RespondError(ctx, err, utils.ConstructErrorAPIResp(err))
		return
	}
}