package helper

import (
	"context"
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/localclient"
	"kc/internal/ondc/models/shared"
	"net/http"
)

func DetermineDiscountProvider(discount shared.DiscountPricing, seller string) string {
	// Check discount type to determine provider
	// payment discount handled
	switch discount.Key {
	case "पेमेंट डिस्काउंट":
		return "PAYMENT_DISCOUNT"
	case "कैशबैक":
		return "PLATFORM_CASHBACK"
	}

	allCouponsKeyMap := getCouponNameMap(context.Background())
	fullKey := fmt.Sprintf("%s:%s", discount.Key, seller)
	coupon, exists := allCouponsKeyMap[fullKey]
	if !exists {
		slack.SendSlackMessage(fmt.Sprintf("sitaram bhai nhi mil raha allcoupnsKey map me data please dekho bhai thi is important %s, %s, %s", discount.Key, discount.Name, discount.Type))
		return "PLATFORM"
	}

	actualCoupon, err := getCouponByCode(context.Background(), coupon.Code, false)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf("sitaram bhai nhi mil raha actual coupon me data please dekho bhai thi is important %s, %s, %s", discount.Key, discount.Name, discount.Type))
		return "PLATFORM"
	}

	if actualCoupon.Source == "kc" {
		return "PLATFORM"
	} else if actualCoupon.Source == "brand" {
		return "SELLER"
	}

	slack.SendSlackMessage(fmt.Sprintf("sitram bhai ab toh return kr raha bhai abhi tk nhi mila data %s, %s, %s ", discount.Key, discount.Name, discount.Type))
	return "PLATFORM"
}

type helperCoupon struct {
	Code   string `json:"code"`
	Name   string `json:"name"`
	Source string `json:"source"`
}

func getCouponNameMap(ctx context.Context) map[string]*helperCoupon {
	response, err := localclient.MakeLocalRequest("/coupons/name_map", http.MethodGet, nil)
	if err != nil {
		return nil
	}

	var couponMap map[string]*helperCoupon
	err = json.Unmarshal(response, &couponMap)
	if err != nil {
		return nil
	}
	return couponMap

}

func getCouponByCode(_ context.Context, code string, fetchActive bool) (*helperCoupon, error) {
	response, err := localclient.MakeLocalRequest("/coupons/by_code?code="+code, http.MethodGet, nil)
	if err != nil {
		return nil, err
	}

	var coupon *helperCoupon
	err = json.Unmarshal(response, &coupon)
	if err != nil {
		return nil, err
	}

	return coupon, nil

}
