package middleware

import (
	"sync"
	"time"
)

type RateLimiter struct {
	mu              sync.RWMutex
	visitors        map[string]*Visitor // map[email]*Visitor instead of IP
	cleanupDuration time.Duration
}

type Visitor struct {
	requests []time.Time
	lastSeen time.Time
}

var rateLimiter *RateLimiter

// Initialize the global rate limiter
func InitRateLimiter(cleanup time.Duration) {
	rateLimiter = NewRateLimiter(cleanup)
}

func NewRateLimiter(cleanup time.Duration) *RateLimiter {
	rl := &RateLimiter{
		visitors:        make(map[string]*Visitor),
		cleanupDuration: cleanup,
	}

	// Start cleanup routine
	go rl.cleanupRoutine()

	return rl
}

func (rl *RateLimiter) cleanupRoutine() {
	ticker := time.NewTicker(rl.cleanupDuration)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			rl.cleanup()
		}
	}
}

func (rl *RateLimiter) cleanup() {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	cutoff := time.Now().Add(-time.Hour)
	for email, visitor := range rl.visitors {
		if visitor.lastSeen.Before(cutoff) {
			delete(rl.visitors, email)
		}
	}
}

// IsAllowed checks if a request from an email is allowed based on rate limits
func (rl *RateLimiter) IsAllowed(email string, maxRequests int, window time.Duration) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now()
	visitor, exists := rl.visitors[email]

	if !exists {
		rl.visitors[email] = &Visitor{
			requests: []time.Time{now},
			lastSeen: now,
		}
		return true
	}

	// Filter out old requests
	cutoff := now.Add(-window)
	validRequests := []time.Time{}
	for _, req := range visitor.requests {
		if req.After(cutoff) {
			validRequests = append(validRequests, req)
		}
	}

	// Check if limit exceeded
	if len(validRequests) >= maxRequests {
		visitor.lastSeen = now
		return false
	}

	// Add current request
	validRequests = append(validRequests, now)
	visitor.requests = validRequests
	visitor.lastSeen = now

	return true
}