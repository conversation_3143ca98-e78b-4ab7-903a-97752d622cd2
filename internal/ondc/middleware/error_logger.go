package middleware

import (
	"bytes"
	"fmt"
	"github.com/gin-gonic/gin"
	"kc/internal/ondc/infrastructure/logging"
)

type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// ErrorLoggerMiddleware logs detailed information for 4xx and 5xx responses
// including the request body
func ErrorLoggerMiddleware(logger logging.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Wrap the response writer to capture response body
		blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = blw

		// Process request
		c.Next()

		statusCode := c.Writer.Status()

		if statusCode >= 400 {
			method := c.Request.Method
			path := c.Request.URL.Path
			requestBodyStr, _ := c.Get("requestBody")
			responseBodyStr := blw.body.String()
			userAgent := c.Request.UserAgent()
			ip := c.ClientIP()
			ctx := c.Request.Context()

			var errVal interface{}
			if len(c.Errors) > 0 {
				errVal = c.Errors.String()
			}

			tranceId, _ := c.Get("trace_id")

			fmt.Println("requestBodyStr", tranceId, requestBodyStr)
			fmt.Println("responseBodyStr", tranceId, responseBodyStr)

			logger.WithFields(map[string]interface{}{
				"method":      method,
				"path":        path,
				"user_agent":  userAgent,
				"ip":          ip,
				"status_code": statusCode,
				// "request_body":  requestBodyStr,
				// "response_body": responseBodyStr,
				"trace_id": tranceId,
				"error":    errVal,
				"module":   "http",
				"reason":   "Request Failed",
			}).Error(ctx, "HTTP error response", "status", statusCode)
		}
	}
}
