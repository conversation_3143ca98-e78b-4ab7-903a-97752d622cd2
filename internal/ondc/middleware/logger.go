package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"io"
	"kc/internal/ondc/infrastructure/logging"
)

func LoggerContextMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		if userID := c.<PERSON>er("X-User-ID"); userID != "" {
			ctx = logging.WithUserID(ctx, userID)
		} else if userID := c.Query("user_id"); userID != "" {
			ctx = logging.WithUserID(ctx, userID)
		}

		if orderID := c.Query("order_id"); orderID != "" {
			ctx = logging.WithOrderID(ctx, orderID)
		}

		uuidVal, err := uuid.NewUUID()
		if err == nil {
			ctx = logging.WithTraceID(ctx, uuidVal.String())
			c.Set("trace_id", uuidVal.String())
		}

		// For POST requests, try to extract fields from the request body
		if c.Request.Method == "POST" {
			var bodyMap map[string]interface{}
			if c.Request.Body != nil {
				bodyBytes, err := c.GetRawData()
				if err == nil && len(bodyBytes) > 0 {
					c.Request.Body = NewReadCloser(bodyBytes)
					if err := json.Unmarshal(bodyBytes, &bodyMap); err == nil {
						ctx = extractFieldsFromBody(ctx, bodyMap)
					}
				}
			}
		}

		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}

// NewReadCloser creates a new io.ReadCloser from a byte slice
func NewReadCloser(body []byte) io.ReadCloser {
	return io.NopCloser(bytes.NewBuffer(body))
}

// extractFieldsFromBody extracts fields from the request body and adds them to the context
func extractFieldsFromBody(ctx context.Context, body map[string]interface{}) context.Context {
	// Extract user ID from request body if available
	if userID, ok := body["user_id"].(string); ok && userID != "" {
		ctx = logging.WithUserID(ctx, userID)
	}

	// Extract order ID from request body if available
	if orderID, ok := body["order_id"].(string); ok && orderID != "" {
		ctx = logging.WithOrderID(ctx, orderID)
	}

	return ctx
}
