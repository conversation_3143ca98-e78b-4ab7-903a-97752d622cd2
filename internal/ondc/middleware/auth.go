package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"kc/internal/ondc/exceptions"
	"kc/internal/ondc/service/auth"
	"kc/internal/ondc/utils"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type AuthServiceInterface interface {
	GetUserByEmail(email string) (*auth.CSAgent, error)
	UpdateUserActivity(email string) error
	ValidateToken(tokenString string) (*auth.Claims, error)
	ValidateSessionSecurity(email, clientIP, userAgent string) bool // New method
	ValidateCSRFToken(csrfToken string) bool
}

var authService AuthServiceInterface

func SetAuthService(service AuthServiceInterface) {
	authService = service
}

// CSRFMiddleware validates CSRF tokens for state-changing operations
func CSRFMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip CSRF check for safe methods
		if c.Request.Method == "GET" || c.Request.Method == "HEAD" || c.Request.Method == "OPTIONS" {
			c.Next()
			return
		}

		// Skip for auth endpoints that don't need CSRF (login, initial registration)
		skipPaths := []string{
			"/api/auth/login",
			"/api/auth/register", // if you have registration
		}

		for _, path := range skipPaths {
			if strings.HasPrefix(c.Request.URL.Path, path) {
				c.Next()
				return
			}
		}

		// Get CSRF token from header
		csrfCookie, _ := c.Cookie(auth.CSRFCookieName)
		if csrfCookie == "" {
			err := exceptions.GenerateNewServerError(
				exceptions.InvalidAuthToken,
				fmt.Errorf("CSRF token missing"),
				"CSRF token required",
				http.StatusForbidden,
			)
			utils.RespondError(c, err, utils.ConstructErrorAPIResp(err))
			c.Abort()
			return
		}

		// Get user from context (this middleware should run AFTER auth middleware)
		// _, exists := GetUserFromContext(c)
		// if !exists {
		// 	err := exceptions.GenerateNewServerError(
		// 		exceptions.UserNotFound,
		// 		fmt.Errorf("user not found in context"),
		// 		"Authentication required",
		// 		http.StatusUnauthorized,
		// 	)
		// 	utils.RespondError(c, err, utils.ConstructErrorAPIResp(err))
		// 	c.Abort()
		// 	return
		// }

		// Validate CSRF token using auth service (SERVER-SIDE VALIDATION)
		if !authService.ValidateCSRFToken(csrfCookie) {
			err := exceptions.GenerateNewServerError(
				exceptions.InvalidAuthToken,
				fmt.Errorf("CSRF token validation failed"),
				"Invalid CSRF token",
				http.StatusForbidden,
			)
			utils.RespondError(c, err, utils.ConstructErrorAPIResp(err))
			c.Abort()
			return
		}

		c.Next()
	}
}

// Enhanced RateLimitMiddleware with additional security
func RateLimitMiddleware(maxRequests int, window time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		var email string

		// Try to get email from token first
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" {
			tokenString := strings.TrimPrefix(authHeader, "Bearer ")
			if tokenString != authHeader && authService != nil {
				if claims, err := authService.ValidateToken(tokenString); err == nil {
					email = claims.Email
				}
			}
		}

		// If no email from token, try from cookie
		if email == "" {
			if tokenString, err := c.Cookie(auth.AccessTokenCookieName); err == nil {
				if claims, err := authService.ValidateToken(tokenString); err == nil {
					email = claims.Email
				}
			}
		}

		// If no email from token, try from request body
		if email == "" {
			var loginReq struct {
				Email string `json:"email"`
			}
			if c.Request.Body != nil {
				bodyBytes, _ := io.ReadAll(c.Request.Body)
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

				if len(bodyBytes) > 0 {
					json.Unmarshal(bodyBytes, &loginReq)
					email = loginReq.Email
				}

				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			}
		}

		// Enhanced rate limiting: stricter for failed attempts
		identifier := email
		if identifier == "" {
			identifier = c.ClientIP()
		}

		if rateLimiter == nil {
			InitRateLimiter(5 * time.Minute)
		}

		// Different limits for different endpoints
		currentMaxRequests := maxRequests
		if strings.Contains(c.Request.URL.Path, "/login") {
			currentMaxRequests = 100 // Stricter for login attempts
		}

		if !rateLimiter.IsAllowed(identifier, currentMaxRequests, window) {
			err := exceptions.GenerateNewServerError(exceptions.RateLimitExceeded, fmt.Errorf("rate limit exceeded for: %s", identifier), "Too many requests", http.StatusTooManyRequests)
			utils.RespondError(c, err, utils.ConstructErrorAPIResp(err))
			c.Abort()
			return
		}

		c.Next()
	}
}

// Enhanced AuthMiddleware with additional security validations
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Try secure cookie first, fallback to regular cookie for backward compatibility
		tokenString, err := c.Cookie(auth.AccessTokenCookieName)
		if err != nil {
			tokenString, err = c.Cookie("access_token")
			if err != nil {
				err := exceptions.GenerateNewServerError(exceptions.MissingAuthToken, fmt.Errorf("access token cookie missing"), "Access token required", http.StatusUnauthorized)
				utils.RespondError(c, err, utils.ConstructErrorAPIResp(err))
				c.Abort()
				return
			}
		}

		// Validate token
		claims, err := authService.ValidateToken(tokenString)
		if err != nil {
			serverErr := exceptions.GenerateNewServerError(exceptions.InvalidAuthToken, err, "Invalid or expired token", http.StatusUnauthorized)
			utils.RespondError(c, serverErr, utils.ConstructErrorAPIResp(serverErr))
			c.Abort()
			return
		}

		// Additional security validations
		// clientIP := c.ClientIP()
		// userAgent := c.GetHeader("User-Agent")

		// // Validate session security (implement this in your auth service)
		// if !authService.ValidateSessionSecurity(claims.Email, clientIP, userAgent) {
		// 	serverErr := exceptions.GenerateNewServerError(exceptions.InvalidAuthToken, fmt.Errorf("suspicious session activity"), "Session validation failed", http.StatusUnauthorized)
		// 	utils.RespondError(c, serverErr, utils.ConstructErrorAPIResp(serverErr))
		// 	c.Abort()
		// 	return
		// }

		// Get user (now from full cache)
		user, err := authService.GetUserByEmail(claims.Email)
		if err != nil {
			serverErr := exceptions.GenerateNewServerError(exceptions.UserNotFound, err, "User not found or inactive", http.StatusUnauthorized)
			utils.RespondError(c, serverErr, utils.ConstructErrorAPIResp(serverErr))
			c.Abort()
			return
		}

		// Check if user is active
		if !user.Active {
			serverErr := exceptions.GenerateNewServerError(exceptions.UserNotFound, fmt.Errorf("user is inactive"), "User account is inactive", http.StatusUnauthorized)
			utils.RespondError(c, serverErr, utils.ConstructErrorAPIResp(serverErr))
			c.Abort()
			return
		}

		// Update user activity (async)
		go authService.UpdateUserActivity(user.Email)

		// Set user info in context
		c.Set("user", user)
		c.Set("user_id", user.ID)
		c.Set("user_email", user.Email)
		c.Set("user_role", user.Role)
		c.Set("user_name", user.Name)

		c.Next()
	}
}

// Helper functions remain the same
func GetUserFromContext(c *gin.Context) (*auth.CSAgent, bool) {
	user, exists := c.Get("user")
	if !exists {
		return nil, false
	}
	u, ok := user.(*auth.CSAgent)
	return u, ok
}

func GetUserIDFromContext(c *gin.Context) (string, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return "", false
	}
	id, ok := userID.(string)
	return id, ok
}

func GetUserEmailFromContext(c *gin.Context) (string, bool) {
	email, exists := c.Get("user_email")
	if !exists {
		return "", false
	}
	e, ok := email.(string)
	return e, ok
}

func GetUserRoleFromContext(c *gin.Context) (string, bool) {
	role, exists := c.Get("user_role")
	if !exists {
		return "", false
	}
	r, ok := role.(string)
	return r, ok
}

func GetUserNameFromContext(c *gin.Context) (string, bool) {
	name, exists := c.Get("user_name")
	if !exists {
		return "", false
	}
	n, ok := name.(string)
	return n, ok
}
