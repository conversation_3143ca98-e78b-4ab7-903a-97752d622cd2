package middleware

import (
	"bytes"
	"fmt"
	"io"
	"time"

	"github.com/prometheus/client_golang/prometheus/collectors"
	"github.com/prometheus/client_golang/prometheus/promauto"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"

	"kc/internal/ondc/config"
)

var appName = fmt.Sprintf("diagonAlleyBE_%s", config.GetEnv())

const MAX_BODY_SIZE = 16 * 1024 // 16KB limit

var (
	httpRequestCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name:        "http_requests_total",
			Help:        "Total number of HTTP requests",
			ConstLabels: prometheus.Labels{"app": appName},
		}, []string{"method", "route", "status"})

	httpRequestDurationMicroseconds = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:        "http_request_duration_ms",
			Help:        "Duration of HTTP requests in ms",
			ConstLabels: prometheus.Labels{"app": appName},
			Buckets:     []float64{10, 30, 50, 100, 300, 500, 1000, 3000, 5000}, // Buckets for response time from 0.1ms to 5s
		}, []string{"method", "route", "status"})

	paymentIssueCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name:        "payment_issue_total",
			Help:        "Total number of payment failures",
			ConstLabels: prometheus.Labels{"app": appName},
		}, []string{"point_of_failure", "error_msg"})
)

func UpdatePaymentIssueCounter(pointOfFailure string, err error) {
	paymentIssueCounter.WithLabelValues(pointOfFailure, err.Error()).Inc()
}

func PrometheusInit() *prometheus.Registry {
	reg := prometheus.NewRegistry()

	reg.MustRegister(
		collectors.NewGoCollector(),
		collectors.NewProcessCollector(collectors.ProcessCollectorOpts{}),
	)

	reg.MustRegister(httpRequestCounter)
	reg.MustRegister(httpRequestDurationMicroseconds)
	reg.MustRegister(paymentIssueCounter)

	return reg
}

func normalizeStatusCode(status int) string {
	switch {
	case status >= 200 && status < 300:
		return "2XX"
	case status >= 300 && status < 400:
		return "3XX"
	case status >= 400 && status < 500:
		return "4XX"
	default:
		return "5XX"
	}
}

func TrackMetrics() gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()
		method := c.Request.Method
		path := c.Request.URL.Path
		bodyBytes, _ := io.ReadAll(c.Request.Body)
		c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes)) // Reassign body for further use
		c.Set("requestBody", string(bodyBytes))
		c.Set("path", path)
		c.Set("method", method)
		c.Set("start_time", startTime.UnixMilli())

		c.Next()

		status := c.Writer.Status()
		duration := time.Since(startTime).Milliseconds()

		httpRequestCounter.WithLabelValues(method, path, normalizeStatusCode(status)).Inc()
		httpRequestDurationMicroseconds.WithLabelValues(method, path, normalizeStatusCode(status)).Observe(float64(duration))
	}
}
