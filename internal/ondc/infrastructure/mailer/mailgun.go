package mailer

import (
	"context"
	"github.com/mailgun/mailgun-go/v4"
	"kc/internal/ondc/infrastructure/logging"
	"time"
)

var logger = logging.New("mailgun")

type MailGunClient struct {
	Client *mailgun.MailgunImpl
}

func GetMailGunClient(apiKey string, domain string) Mailer {
	mg := mailgun.NewMailgun(domain, apiKey)
	client := &MailGunClient{Client: mg}
	return client
}

func (client *MailGunClient) Send(ctx context.Context, msg *Email) error {
	if msg.Attachment != nil {
		defer msg.Attachment.Close()
	}

	sender := msg.From
	subject := msg.Subject
	// we are setting body as blank as we will send the html content here which is described below
	body := ""
	recipient := msg.To

	message := client.Client.NewMessage(sender, subject, body, recipient)
	if msg.Attachment != nil {
		message.AddReaderAttachment("video.mp4", msg.Attachment)
	}

	message.SetHtml(msg.Content)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*100)
	defer cancel()

	// Send the message with a 10 second timeout
	_, _, err := client.Client.Send(ctx, message)

	if err != nil {
		logger.Error(ctx, "error in sending email %s", err.Error())
	}
	return nil

}
