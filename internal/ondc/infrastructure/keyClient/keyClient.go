package keyClient

import (
	sm "cloud.google.com/go/secretmanager/apiv1"
	smpb "cloud.google.com/go/secretmanager/apiv1/secretmanagerpb"
	smrpb "cloud.google.com/go/secretmanager/apiv1/secretmanagerpb"
	"context"
	"encoding/json"
	"fmt"
	"google.golang.org/api/option"
)

type KeyClient interface {
	ServiceSigningPrivateKeyset(context.Context) ([]byte, error)
	ServiceEncryptionPrivateKey(context.Context) ([]byte, error)
}

// SecretManagerKeyClient provides keys for encryption and authentication.
type SecretManagerKeyClient struct {
	secretClient *sm.Client
	projectID    string
	secretID     string
}

// New create a new SecretManagerKeyService.
func New(ctx context.Context, projectID, secretID string, opts ...option.ClientOption) (*SecretManagerKeyClient, error) {
	//secretClient, err := sm.NewClient(ctx, opts...)
	//if err != nil {
	//	return nil, fmt.Errorf("failed to create GCP Secret Manager client: %v", err)
	//}

	client := &SecretManagerKeyClient{
		secretClient: nil,
		projectID:    projectID,
		secretID:     secretID,
	}
	return client, nil
}

func (c *SecretManagerKeyClient) Close() error {
	return c.secretClient.Close()
}

// ServiceSigningPrivateKeyset provides the ED25519 private key of our service.
func (c *SecretManagerKeyClient) ServiceSigningPrivateKeyset(ctx context.Context) ([]byte, error) {
	return []byte("STdKqQkiRS205Avl2f6k+Bsnf481suBnCIicBOUGeI9KjFQcJmMXojPQDIo+kvu8LjZlGm4C9GVCzeJzweAUeg=="), nil
	//result, err := c.readSecret(ctx)
	//if err != nil {
	//	return nil, err
	//}
	//return result["signingKey"]["signingKeySet"], nil
}

// ServiceEncryptionPrivateKey provides the X25519 private key of our service.
func (c *SecretManagerKeyClient) ServiceEncryptionPrivateKey(ctx context.Context) ([]byte, error) {
	return []byte("MC4CAQEwBQYDK2VuBCIEIJjeXx0DNnfavvaoTIKwZIJsxH3N6mweUDScqgkNKZN+"), nil
	//result, err := c.readSecret(ctx)
	//if err != nil {
	//	return nil, err
	//}
	//return result["encryptionKey"]["privateKeyEncryption"], nil
}

// AddKey adds a new secret version to a given secret ID with a given payload.
func (c *SecretManagerKeyClient) AddKey(ctx context.Context, secretID string, payload []byte) error {
	request := &smpb.AddSecretVersionRequest{
		Parent: secretID,
		Payload: &smrpb.SecretPayload{
			Data: payload,
		},
	}
	_, err := c.secretClient.AddSecretVersion(ctx, request)
	return err
}

func (c *SecretManagerKeyClient) readSecret(ctx context.Context) (map[string]map[string][]byte, error) {
	req := &smpb.AccessSecretVersionRequest{
		Name: fmt.Sprintf("projects/%s/secrets/%s/versions/latest", c.projectID, c.secretID),
	}
	keyData, err := c.secretClient.AccessSecretVersion(ctx, req)
	if err != nil {
		return nil, err
	}

	var result map[string]map[string][]byte
	err = json.Unmarshal(keyData.Payload.Data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
