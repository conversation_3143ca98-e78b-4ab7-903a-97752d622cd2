package firebase

import (
	"context"

	"cloud.google.com/go/firestore"
	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/auth"
	"firebase.google.com/go/v4/db"
	"github.com/sirupsen/logrus"
	"google.golang.org/api/option"
)

func GetNewFirebaseApp(logger *logrus.Logger, keyPath string) *firebase.App {
	opt := option.WithCredentialsFile(keyPath)

	fb, err := firebase.NewApp(context.Background(), &firebase.Config{
		AuthOverride:     nil,
		DatabaseURL:      "https://op-d2r-prod.asia-southeast1.firebasedatabase.app/",
		ProjectID:        "",
		ServiceAccountID: "",
		StorageBucket:    "",
	}, opt)
	if err != nil {
		logger.Fatalf("not able to connect to firebase %s", err.Error())
		return nil
	}
	return fb
}

func GetRealtimeDb(ctx context.Context, app *firebase.App) *db.Client {
	db, _ := app.Database(ctx)
	return db
}

func GetMetaDb(ctx context.Context, app *firebase.App, url string) *db.Client {
	db, _ := app.DatabaseWithURL(ctx, url)
	return db
}

func GetFirestore(ctx context.Context, app *firebase.App) *firestore.Client {
	db, _ := app.Firestore(ctx)
	return db
}

func GetAuth(ctx context.Context, app *firebase.App) *auth.Client {
	authClient, _ := app.Auth(ctx)
	return authClient
}
