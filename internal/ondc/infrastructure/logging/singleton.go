package logging

import (
	"runtime"
	"strings"
	"sync"
)

var (
	// loggerMap stores loggers by tag
	loggerMap     = make(map[string]Logger)
	loggerMapLock sync.RWMutex
)

// GetLogger returns a logger for the given tag
// If a logger with the given tag already exists, it is returned
// Otherwise, a new logger is created and stored in the map
func GetLogger(tag string) Logger {
	loggerMapLock.RLock()
	logger, exists := loggerMap[tag]
	loggerMapLock.RUnlock()

	if exists {
		return logger
	}

	loggerMapLock.Lock()
	defer loggerMapLock.Unlock()

	// Check again in case another goroutine created the logger while we were waiting for the lock
	logger, exists = loggerMap[tag]
	if exists {
		return logger
	}

	// Create a new logger and store it in the map
	logger = New(tag)
	loggerMap[tag] = logger
	return logger
}

// GetDefaultLogger returns a logger for the current package
// It uses the caller's package name as the tag
func GetDefaultLogger() Logger {
	// Get the caller's package name
	_, fileName, _, _ := runtime.Caller(1)
	parts := strings.Split(fileName, "/")
	var packageName string
	if len(parts) > 0 {
		packageName = parts[len(parts)-2] // Get the parent directory name as the package name
	} else {
		packageName = "unknown"
	}
	return GetLogger(packageName)
}
