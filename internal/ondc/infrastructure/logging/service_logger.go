package logging

import (
	"context"
)

// GetLogrusLogger returns a LogrusLogger for the given tag
// This is a simpler alternative to the ServiceLogger that is compatible with existing code
func GetLogrusLogger(tag string) *LogrusLogger {
	logger, ok := GetLogger(tag).(*LogrusLogger)
	if !ok {
		// This should never happen since GetLogger always returns a LogrusLogger
		panic("GetLogger did not return a LogrusLogger")
	}
	return logger
}

// LogDatabaseOperation is a helper function that directly calls the LogDatabaseOperation method
// on a LogrusLogger without requiring a type assertion
func LogDatabaseOperation(logger Logger, ctx context.Context, operation, table, query string, duration int64, err error) {
	if logrusLogger, ok := logger.(*LogrusLogger); ok {
		logrusLogger.LogDatabaseOperation(ctx, operation, table, query, duration, err)
	}
}

// LogHTTPRequest is a helper function that directly calls the LogHTTPRequest method
// on a LogrusLogger without requiring a type assertion
func LogHTTPRequest(logger Logger, ctx context.Context, method, path, userAgent, ip string, statusCode int, duration int64) {
	if logrusLogger, ok := logger.(*LogrusLogger); ok {
		logrusLogger.LogHTTPRequest(ctx, method, path, userAgent, ip, statusCode, duration)
	}
}
