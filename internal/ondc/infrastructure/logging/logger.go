package logging

import (
	"context"
	"fmt"
	"os"
	"runtime"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	log "github.com/sirupsen/logrus"
	"github.com/yukitsune/lokirus"
)

// Logger defines the standard logging interface.
type Logger interface {
	Info(ctx context.Context, format string, args ...interface{})
	Warn(ctx context.Context, format string, args ...interface{})
	Error(ctx context.Context, format string, args ...interface{})

	WithField(key string, value interface{}) Logger
	WithFields(fields map[string]interface{}) Logger
	WithError(ctx context.Context, err error) Logger
	WithFunction(functionName string) Logger
	WithTrace(ctx context.Context) Logger
	WithReason(reason string) Logger

	LogDatabaseOperation(ctx context.Context, operation, table, query string, duration int64, err error)
	LogHTTPRequest(ctx context.Context, method, path, userAgent, ip string, statusCode int, duration int64)
}

// LoggerConfig holds configuration for logger creation
type LoggerConfig struct {
	LokiURL     string
	Environment string
	Service     string
	Job         string
}

// DefaultConfig returns default logger configuration
func DefaultConfig() *LoggerConfig {
	return &LoggerConfig{
		LokiURL:     getEnv("LOKI_URL", "http://*************:3100"),
		Environment: getEnv("ENVIRONMENT", "production"),
		Service:     getEnv("SERVICE_NAME", "diagonAlley"),
		Job:         getEnv("JOB_NAME", "diagonAlleyBE_prod"),
	}
}

// ContextKey type for context keys
type ContextKey string

const (
	TraceIDKey     ContextKey = "trace_id"
	UserIDKey      ContextKey = "user_id"
	RequestIDKey   ContextKey = "request_id"
	CorrelationKey ContextKey = "correlation_id"
	OrderIDKey     ContextKey = "order_id"
	Data1Key       ContextKey = "data1"
	Data2Key       ContextKey = "data2"
	Data3Key       ContextKey = "data3"
	Data4Key       ContextKey = "data4"
	Data5Key       ContextKey = "data5"
	Data6Key       ContextKey = "data6"
)

// LogrusLogger implements the Logger interface
type LogrusLogger struct {
	logger *logrus.Logger
	entry  *logrus.Entry
	config *LoggerConfig
}

func NewWithConfig(logtag string, config *LoggerConfig) Logger {
	if config == nil {
		config = DefaultConfig()
	}

	// Configure the Loki hook with enhanced labels and better formatting
	opts := lokirus.NewLokiHookOptions().
		// Grafana doesn't have a "panic" level, but it does have a "critical" level
		// https://grafana.com/docs/grafana/latest/explore/logs-integration/
		WithLevelMap(lokirus.LevelMap{logrus.PanicLevel: "critical"}).
		WithFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339Nano,
			FieldMap: logrus.FieldMap{
				logrus.FieldKeyTime:  "timestamp",
				logrus.FieldKeyLevel: "level",
				logrus.FieldKeyMsg:   "message",
			},
			PrettyPrint: false,
		}).
		WithStaticLabels(lokirus.Labels{
			"job":         config.Job,
			"service":     config.Service,
			"environment": config.Environment,
			"tag":         logtag,
		})

	hook := lokirus.NewLokiHookWithOpts(
		config.LokiURL,
		opts,
		logrus.InfoLevel,
		logrus.WarnLevel,
		logrus.ErrorLevel,
		logrus.FatalLevel,
		logrus.PanicLevel)

	l := log.New()
	l.AddHook(hook)
	l.SetFormatter(&log.JSONFormatter{
		TimestampFormat: time.RFC3339Nano,
		FieldMap: logrus.FieldMap{
			logrus.FieldKeyTime:  "timestamp",
			logrus.FieldKeyLevel: "level",
			logrus.FieldKeyMsg:   "message",
		},
		PrettyPrint: false,
	})

	return &LogrusLogger{
		logger: l,
		entry:  l.WithFields(logrus.Fields{}),
		config: config,
	}
}

// New creates a new logger with default configuration (backwards compatibility)
func New(logtag string) Logger {
	return NewWithConfig(logtag, nil)
}

// getCallerInfo gets the function name and file info of the caller
func getCallerInfo(skip int) (functionName, fileName string, lineNumber int) {
	for i := skip; i <= skip+2; i++ {
		pc, file, line, ok := runtime.Caller(i)
		if !ok {
			continue
		}

		fn := runtime.FuncForPC(pc)
		if fn != nil {
			fullFuncName := fn.Name()
			// Skip if this is still within the logging package
			if strings.Contains(fullFuncName, "logging.") {
				continue
			}

			functionName = fullFuncName
			// Extract just the function name without package path
			if idx := strings.LastIndex(functionName, "."); idx != -1 {
				functionName = functionName[idx+1:]
			}
			// Remove receiver type if present (for methods)
			if idx := strings.Index(functionName, ")"); idx != -1 && strings.Contains(functionName, "(") {
				functionName = functionName[idx+2:] // Skip "). "
			}
		}

		// Extract just the filename without full path
		if idx := strings.LastIndex(file, "/"); idx != -1 {
			fileName = file[idx+1:]
		} else {
			fileName = file
		}

		return functionName, fileName, line
	}
	return "unknown", "unknown", 0
}

// extractContextFields extracts common fields from context
func (l *LogrusLogger) extractContextFields(ctx context.Context) logrus.Fields {
	fields := logrus.Fields{}

	if ctx != nil {
		if traceID := ctx.Value(TraceIDKey); traceID != nil {
			fields["trace_id"] = traceID
		}
		if userID := ctx.Value(UserIDKey); userID != nil {
			fields["user_id"] = userID
		}
		if requestID := ctx.Value(RequestIDKey); requestID != nil {
			fields["request_id"] = requestID
		}
		if correlationID := ctx.Value(CorrelationKey); correlationID != nil {
			fields["correlation_id"] = correlationID
		}
		if orderID := ctx.Value(OrderIDKey); orderID != nil {
			fields["order_id"] = orderID
		}
		if data1 := ctx.Value(Data1Key); data1 != nil {
			fields["data1"] = data1
		}
		if data2 := ctx.Value(Data2Key); data2 != nil {
			fields["data2"] = data2
		}
		if data3 := ctx.Value(Data3Key); data3 != nil {
			fields["data3"] = data3
		}
		if data4 := ctx.Value(Data4Key); data4 != nil {
			fields["data4"] = data4
		}
		if data5 := ctx.Value(Data5Key); data5 != nil {
			fields["data5"] = data5
		}
		if data6 := ctx.Value(Data6Key); data6 != nil {
			fields["data6"] = data6
		}
	}
	return fields
}

// createEntryWithContext creates a log entry with context and caller info
func (l *LogrusLogger) createEntryWithContext(ctx context.Context) *logrus.Entry {
	// Get caller information (skip 3 frames: createEntryWithContext -> Debug/Info/etc -> actual caller)
	functionName, fileName, lineNumber := getCallerInfo(3)

	// Base fields with caller info
	fields := logrus.Fields{
		"function": functionName,
		"file":     fileName,
		"line":     lineNumber,
		"source":   fmt.Sprintf("%s:%d", fileName, lineNumber),
		"module":   extractModuleFromFunction(functionName),
	}

	// Add context fields
	contextFields := l.extractContextFields(ctx)
	for k, v := range contextFields {
		fields[k] = v
	}

	// Merge with existing entry fields
	if l.entry != nil {
		for k, v := range l.entry.Data {
			fields[k] = v
		}
	}

	return l.logger.WithFields(fields)
}

// getEnv gets environment variable with default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// extractModuleFromFunction tries to extract module/package info from function name
func extractModuleFromFunction(functionName string) string {
	// For functions like "orderService.ProcessOrder" -> "orderService"
	// For functions like "ProcessOrder" -> "main"
	if idx := strings.Index(functionName, "."); idx != -1 {
		return functionName[:idx]
	}
	return "main"
}

// Helper functions to create context with trace information
func WithTraceID(ctx context.Context, traceID string) context.Context {
	return context.WithValue(ctx, TraceIDKey, traceID)
}

func WithUserID(ctx context.Context, userID string) context.Context {
	return context.WithValue(ctx, UserIDKey, userID)
}

func WithRequestID(ctx context.Context, requestID string) context.Context {
	return context.WithValue(ctx, RequestIDKey, requestID)
}

func WithCorrelationID(ctx context.Context, correlationID string) context.Context {
	return context.WithValue(ctx, CorrelationKey, correlationID)
}

func WithOrderID(ctx context.Context, orderID string) context.Context {
	return context.WithValue(ctx, OrderIDKey, orderID)
}

func WithData1(ctx context.Context, data1 string) context.Context {
	return context.WithValue(ctx, Data1Key, data1)
}

func WithData2(ctx context.Context, data2 string) context.Context {
	return context.WithValue(ctx, Data2Key, data2)
}

func WithData3(ctx context.Context, data3 string) context.Context {
	return context.WithValue(ctx, Data3Key, data3)
}

func WithData4(ctx context.Context, data4 string) context.Context {
	return context.WithValue(ctx, Data4Key, data4)
}

func WithData5(ctx context.Context, data5 string) context.Context {
	return context.WithValue(ctx, Data5Key, data5)
}

func WithData6(ctx context.Context, data6 string) context.Context {
	return context.WithValue(ctx, Data6Key, data6)
}
