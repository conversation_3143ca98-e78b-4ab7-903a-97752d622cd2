package logging

import (
	"context"
	"fmt"
	"os"
	"reflect"
	"runtime/debug"
	"strings"

	"github.com/sirupsen/logrus"
)

// Info logs an info message with context and caller information
func (l *LogrusLogger) Info(ctx context.Context, format string, args ...interface{}) {
	entry := l.createEntryWithContext(ctx)
	if len(args) > 0 {
		entry.Infof(format, args...)
	} else {
		entry.Info(format)
	}
}

// Warn logs a warning message with context and caller information
func (l *LogrusLogger) Warn(ctx context.Context, format string, args ...interface{}) {
	entry := l.createEntryWithContext(ctx)
	if len(args) > 0 {
		entry.Warnf(format, args...)
	} else {
		entry.Warn(format)
	}
}

// Error logs an error message with context and caller information
func (l *LogrusLogger) Error(ctx context.Context, format string, args ...interface{}) {
	entry := l.createEntryWithContext(ctx)
	if len(args) > 0 {
		entry.Errorf(format, args...)
	} else {
		entry.Error(format)
	}
}

// WithField adds a single field to the logger
func (l *LogrusLogger) WithField(key string, value interface{}) Logger {
	var newEntry *logrus.Entry
	if l.entry != nil {
		newEntry = l.entry.WithField(key, value)
	} else {
		newEntry = l.logger.WithField(key, value)
	}

	return &LogrusLogger{
		logger: l.logger,
		entry:  newEntry,
	}
}

// WithFields adds multiple fields to the logger
func (l *LogrusLogger) WithFields(fields map[string]interface{}) Logger {
	logrusFields := make(logrus.Fields, len(fields))
	for k, v := range fields {
		logrusFields[k] = v
	}

	var newEntry *logrus.Entry
	if l.entry != nil {
		newEntry = l.entry.WithFields(logrusFields)
	} else {
		newEntry = l.logger.WithFields(logrusFields)
	}

	return &LogrusLogger{
		logger: l.logger,
		entry:  newEntry,
	}
}

// WithError adds comprehensive error information to the logger
func (l *LogrusLogger) WithError(ctx context.Context, err error) Logger {
	if err == nil {
		return l
	}

	// Enhanced error fields with detailed information
	errorFields := logrus.Fields{
		"error":          err.Error(),
		"error_type":     fmt.Sprintf("%T", err),
		"error_category": categorizeError(err),
		"has_error":      true,
	}

	// Add error details if it's a structured error
	if structuredErr := extractStructuredError(err); structuredErr != nil {
		for k, v := range structuredErr {
			errorFields[fmt.Sprintf("error_%s", k)] = v
		}
	}

	// Add stack trace in development environment only
	if shouldIncludeStackTrace() {
		errorFields["stack_trace"] = string(debug.Stack())
	}

	var newEntry *logrus.Entry
	if l.entry != nil {
		newEntry = l.entry.WithFields(errorFields)
	} else {
		newEntry = l.logger.WithFields(errorFields)
	}

	return &LogrusLogger{
		logger: l.logger,
		entry:  newEntry,
	}
}

// WithFunction adds a custom function name (overrides auto-detected function)
func (l *LogrusLogger) WithFunction(functionName string) Logger {
	return l.WithField("custom_function", functionName)
}

// WithReason adds a reason
func (l *LogrusLogger) WithReason(reason string) Logger {
	return l.WithField("reason", reason)
}

// WithTrace adds all trace information from context
func (l *LogrusLogger) WithTrace(ctx context.Context) Logger {
	fields := l.extractContextFields(ctx)
	logrusFields := make(map[string]interface{}, len(fields))
	for k, v := range fields {
		logrusFields[k] = v
	}
	return l.WithFields(logrusFields)
}

// categorizeError categorizes errors into types for better filtering
func categorizeError(err error) string {
	errStr := strings.ToLower(err.Error())
	errType := strings.ToLower(fmt.Sprintf("%T", err))

	switch {
	case strings.Contains(errStr, "connection") || strings.Contains(errStr, "timeout"):
		return "connection"
	case strings.Contains(errStr, "not found") || strings.Contains(errStr, "no rows"):
		return "not_found"
	case strings.Contains(errStr, "permission") || strings.Contains(errStr, "unauthorized") || strings.Contains(errStr, "forbidden"):
		return "authorization"
	case strings.Contains(errStr, "validation") || strings.Contains(errStr, "invalid"):
		return "validation"
	case strings.Contains(errType, "sql") || strings.Contains(errStr, "database"):
		return "database"
	case strings.Contains(errType, "json") || strings.Contains(errStr, "marshal") || strings.Contains(errStr, "unmarshal"):
		return "serialization"
	case strings.Contains(errType, "http") || strings.Contains(errStr, "request") || strings.Contains(errStr, "response"):
		return "http"
	default:
		return "general"
	}
}

// extractStructuredError attempts to extract additional fields from structured errors
func extractStructuredError(err error) map[string]interface{} {
	fields := make(map[string]interface{})

	// Use reflection to extract fields from error structs
	v := reflect.ValueOf(err)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() == reflect.Struct {
		t := v.Type()
		for i := 0; i < v.NumField(); i++ {
			field := t.Field(i)
			value := v.Field(i)

			// Skip unexported fields
			if !field.IsExported() {
				continue
			}

			// Skip error interface field (to avoid recursion)
			if field.Type.String() == "error" {
				continue
			}

			fieldName := strings.ToLower(field.Name)
			if value.IsValid() && value.CanInterface() {
				fields[fieldName] = value.Interface()
			}
		}
	}

	if len(fields) == 0 {
		return nil
	}
	return fields
}

// shouldIncludeStackTrace determines if stack trace should be included
func shouldIncludeStackTrace() bool {
	env := strings.ToLower(os.Getenv("ENVIRONMENT"))
	return env == "development" || env == "dev" || env == "local"
}

// Additional helper methods for common logging patterns

// LogDatabaseOperation logs database operations with common fields
func (l *LogrusLogger) LogDatabaseOperation(ctx context.Context, operation, table, query string, duration int64, err error) {
	logger := l.WithFields(map[string]interface{}{
		"operation":   operation,
		"table":       table,
		"query":       query,
		"duration_ms": duration,
		"module":      "database",
	})

	if err != nil {
		logger.WithError(ctx, err).Error(ctx, "Database operation failed")
	} else {
		logger.Info(ctx, "Database operation completed successfully")
	}
}

// LogHTTPRequest logs HTTP requests with common fields
func (l *LogrusLogger) LogHTTPRequest(ctx context.Context, method, path, userAgent, ip string, statusCode int, duration int64) {
	l.WithFields(map[string]interface{}{
		"method":           method,
		"path":             path,
		"user_agent":       userAgent,
		"ip":               ip,
		"status_code":      statusCode,
		"response_time_ms": duration,
		"module":           "http",
	}).Info(ctx, "HTTP request processed")
}
