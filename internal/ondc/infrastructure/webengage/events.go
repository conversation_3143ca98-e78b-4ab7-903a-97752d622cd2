package webengage

import (
	"bytes"
	"encoding/json"
	"net/http"
)

// @wable please fix this function (rewrite this function properly)
func SendWebengageEvents(event *WebengageEvents) error {
	url := "https://dobby.retailpulse.ai/api/webengage/events"
	method := http.MethodPost
	var payload bytes.Buffer
	err := json.NewEncoder(&payload).Encode(*event)
	if err != nil {
		return err
	}

	client := &http.Client{}
	req, err := http.NewRequest(method, url, &payload)

	if err != nil {
		return err
	}
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		return err
	}
	return nil
}
