package cache

import (
	"context"
	"fmt"
	"kc/internal/ondc/external/slack"
	"time"

	"github.com/go-redis/redis/v8"
)

type Cache struct {
	RedisClient *redis.Client
}

func NewCache(address, password string) Cache {
	cache := redis.NewClient(&redis.Options{
		Addr:         address,
		Password:     password, // no password set
		DB:           0,        // use default DB
		WriteTimeout: 1 * time.Minute,
	})
	err := cache.Ping(context.Background()).Err()
	if err != nil {
		fmt.Println("error connecting to cache ", err)
		slack.SendSlackMessage("Old Redis connection failed diagon alley: " + err.<PERSON>rror())
		panic("not able to ping redis")
	}
	return Cache{
		RedisClient: cache,
	}
}
