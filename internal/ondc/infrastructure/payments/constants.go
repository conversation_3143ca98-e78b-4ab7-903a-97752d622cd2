package payments

import (
	errors "errors"
	"kc/internal/ondc/models/dto"
)

// Common errors
var (
	ErrInvalidAmount       = errors.New("invalid payment amount")
	ErrPaymentNotFound     = errors.New("payment not found")
	ErrPaymentFailed       = errors.New("payment failed")
	ErrInvalidPaymentToken = errors.New("invalid payment token")
	ErrGatewayNotSupported = errors.New("payment gateway not supported")
	ErrAlreadyRefunded     = errors.New("payment already refunded")
	ErrPaymentNotCompleted = errors.New("payment not completed")
	ErrNoAdvancePayment    = errors.New("no advance payment")
)

const (
	StatusInitiated       string = "initiated"
	StatusPending         string = "pending"
	StatusCompleted       string = "completed"
	StatusFailed          string = "failed"
	StatusRefunded        string = "refunded"
	StatusRefundInitiated string = "refund_initiated"
	Updated               string = "updated"
	StatusDelayedDispatch string = "delayed_dispatch"
)

var StatusUIData = map[string]dto.StatusMeta{
	StatusFailed: {
		Title:       "पेमेंट फेल हो गया है",
		Description: "अगर अमाउंट कट गया है, तो 3 से 5 दिनों में अपने आप वापस आ जाएगा। कृपया चिंता न करें।",
		ImageUrl:    "https://d2rstorage2.blob.core.windows.net/widget/March/19/7bc7ea45-7bc3-4b11-968d-ae53761dc7f3/1742373819773.webp",
		BgColor:     "#FF9D9D",
		Color:       "#5C0303",
		Name:        "पेमेंट फेल",
	},
	StatusPending: {
		Title:       "पेमेंट पेंडिंग",
		Description: "चिंता ना करें, आपका पेमेंट स्टेटस 20 मिनट में अपडेट हो जाएगा। आप “मेरे ऑर्डर” सेक्शन से जानकारी ले सकते है। \nअगर अमाउंट कट जाता है और ऑर्डर कन्फर्म नहीं होता है तो 3 से 5 दिन में अमाउंट वापस आ जाएगा ",
		ImageUrl:    "https://d2rstorage2.blob.core.windows.net/widget/March/19/f7287d7a-38c9-4590-9c5a-a2ee7a548642/1742373830822.webp",
		BgColor:     "#FFF0AB",
		Color:       "#6D5A00",
		Name:        "पेमेंट पेंडिंग",
	},
	StatusCompleted: {
		Title:       "ऑर्डर सफलतापूर्वक लग गया है",
		Description: "जल्द ही हमारी टीम आपसे संपर्क कर के ऑर्डर की पुष्टि करेगी",
		ImageUrl:    "https://d2rstorage2.blob.core.windows.net/widget/March/19/6387bfbd-c553-4a30-9011-c1f2c8d3263b/1742373805367.webp",
		BgColor:     "#E9FFF1",
		Color:       "#1D7D3D",
		Name:        "पेमेंट सफल",
	},
	StatusRefunded: {
		Title:       "रिफंड सफलतापूर्वक हो गया है",
		Description: "आपका रिफंड सफलतापूर्वक कर दिया गया है और 3-5 बैंकिंग दिनों के अंदर आपके बैंक अकाउंट में पहुंच जाएगा।",
		ImageUrl:    "https://d2rstorage2.blob.core.windows.net/widget/May/16/3c40b2de-49c2-4aa8-94e7-50dcbe8f2c6a/1747382109727.webp",
		BgColor:     "#C4ECFF",
		Color:       "#004565",
		Name:        "रिफंड सफल",
	},
	StatusDelayedDispatch: {
		Title:       "आपका ऑर्डर तैयार किया जा रहा है",
		Description: "आपके ऑर्डर के डिस्पैच में थोड़ी देरी हो रही है। हम असुविधा के लिए क्षमा चाहते हैं, आपका ऑर्डर जल्द ही डिस्पैच किया जाएगा।",
		ImageUrl:    "https://d2rstorage2.blob.core.windows.net/widget/June/30/9e69288f-130a-4438-aee3-1f3c8d271d6f/1751293354358.webp",
		BgColor:     "#FFFAEA", // light yellow/orange background
		Color:       "#92400E", // dark orange/brown text
	},
}
