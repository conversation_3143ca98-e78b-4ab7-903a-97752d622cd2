package payments

import (
	"context"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
)

type Gateway interface {
	Name() string
	InitiatePayment(ctx context.Context, req dto.InitiatePaymentRequest) (*dto.InitiatePaymentResponse, error)
	VerifyPayment(ctx context.Context, req dto.PaymentData) (*dto.VerifyPaymentResponse, error)
	GetAllTransactionsForPayment(ctx context.Context, paymentOrderId string) ([]dao.PaymentGatewayRecords, error)
	VerifyWebhookSignature(ctx context.Context, webhookType string) bool
	HandleWebhookType1(ctx context.Context, payload interface{}, event string) (*dao.PaymentGatewayRecords, error)
	RefundPayment(ctx context.Context, req dto.RefundPaymentRequest) (*dto.RefundPaymentResponse, error)
}

type Service struct {
	gateways       map[string]Gateway
	defaultGateway string
}

// NewService creates a new payment service
func NewService() *Service {
	return &Service{
		gateways: make(map[string]Gateway),
	}
}

func (s *Service) RegisterGateway(gateway Gateway) {
	s.gateways[gateway.Name()] = gateway

	// Set as default if it's the first gateway registered
	if s.defaultGateway == "" {
		s.defaultGateway = gateway.Name()
	}
}

func (s *Service) SetDefaultGateway(gatewayName string) error {
	if _, exists := s.gateways[gatewayName]; !exists {
		return ErrGatewayNotSupported
	}
	s.defaultGateway = gatewayName
	return nil
}

func (s *Service) InitiatePayment(ctx context.Context, req dto.InitiatePaymentRequest, gatewayName string) (*dto.InitiatePaymentResponse, error) {
	// Use default gateway if not specified
	if gatewayName == "" {
		gatewayName = s.defaultGateway
	}

	gateway, exists := s.gateways[gatewayName]
	if !exists {
		return nil, ErrGatewayNotSupported
	}

	return gateway.InitiatePayment(ctx, req)
}

func (s *Service) VerifyPayment(ctx context.Context, req dto.PaymentData, gatewayName string) (*dto.VerifyPaymentResponse, error) {
	// Use default gateway if not specified
	if gatewayName == "" {
		gatewayName = s.defaultGateway
	}

	gateway, exists := s.gateways[gatewayName]
	if !exists {
		return nil, ErrGatewayNotSupported
	}

	return gateway.VerifyPayment(ctx, req)
}

func (s *Service) GetAllTransactionsForPayment(ctx context.Context, paymentOrderId string, gatewayName string) ([]dao.PaymentGatewayRecords, error) {
	// Use default gateway if not specified
	if gatewayName == "" {
		gatewayName = s.defaultGateway
	}

	gateway, exists := s.gateways[gatewayName]
	if !exists {
		return nil, ErrGatewayNotSupported
	}

	return gateway.GetAllTransactionsForPayment(ctx, paymentOrderId)
}

func (s *Service) VerifyWebhookSignature(ctx context.Context, webhookType string, gatewayName string) bool {
	// Use default gateway if not specified
	if gatewayName == "" {
		gatewayName = s.defaultGateway
	}

	gateway, exists := s.gateways[gatewayName]
	if !exists {
		return false
	}

	return gateway.VerifyWebhookSignature(ctx, webhookType)
}

func (s *Service) HandleWebhookType1(ctx context.Context, payload interface{}, event string, gatewayName string) (*dao.PaymentGatewayRecords, error) {
	// Use default gateway if not specified
	if gatewayName == "" {
		gatewayName = s.defaultGateway
	}

	gateway, exists := s.gateways[gatewayName]
	if !exists {
		return nil, ErrGatewayNotSupported
	}

	return gateway.HandleWebhookType1(ctx, payload, event)
}

func (s *Service) RefundPayment(ctx context.Context, req dto.RefundPaymentRequest, gatewayName string) (*dto.RefundPaymentResponse, error) {
	// Use default gateway if not specified
	if gatewayName == "" {
		gatewayName = s.defaultGateway
	}

	gateway, exists := s.gateways[gatewayName]
	if !exists {
		return nil, ErrGatewayNotSupported
	}

	return gateway.RefundPayment(ctx, req)
}
