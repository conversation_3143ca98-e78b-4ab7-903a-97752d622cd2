package redis

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

type GcpRedis struct {
	RedisClient *redis.Client
}

func NewGcpRedisClient(host string, port string) *GcpRedis {
	// host = "127.0.0.1"

	redisClient := redis.NewClient(&redis.Options{
		Addr:         host + ":" + port,
		PoolSize:     10,
		PoolTimeout:  time.Duration(2) * time.Second,
		DialTimeout:  time.Duration(2) * time.Second,
		ReadTimeout:  time.Duration(2) * time.Second,
		WriteTimeout: time.Duration(2) * time.Second,
		MaxRetries:   1,
	})

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	_, err := redisClient.Ping(ctx).Result()
	if err != nil {
		fmt.Println("Redis connection failed diagon alley: " + err.Error())
		//slack.SendSlackMessage("Redis connection failed diagon alley: " + err.<PERSON>rror())
		// panic(fmt.Sprintf("Redis connection failed diagon alley: %v\n", err))
	}

	return &GcpRedis{
		RedisClient: redisClient,
	}
}

// Get retrieves a value by key.
func (r *GcpRedis) Get(ctx context.Context, key string) (string, error) {
	if r.RedisClient == nil {
		return "", fmt.Errorf("redis client is not initialized")
	}

	value, err := r.RedisClient.Get(ctx, key).Result()
	if err == redis.Nil {
		fmt.Printf("Key %s does not exist\n", key)
		return "", nil
	} else if err != nil {
		return "", fmt.Errorf("error getting key %s: %v", key, err)
	}

	return value, nil
}

// Set sets a key-value pair with an expiration.
func (r *GcpRedis) Set(ctx context.Context, key string, value string, expiration time.Duration) error {
	if r.RedisClient == nil {
		return fmt.Errorf("redis client is not initialized")
	}

	err := r.RedisClient.Set(ctx, key, value, expiration).Err()
	if err != nil {
		return fmt.Errorf("error setting key %s: %v", key, err)
	}

	return nil
}
