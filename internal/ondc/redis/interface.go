package redis

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// RedisConfig holds the configuration for Redis client
type RedisConfig struct {
	BaseURL    string
	Timeout    time.Duration
	MaxRetries int
}

// RedisClient represents a Redis client
type RedisClient struct {
	config     RedisConfig
	httpClient *http.Client
}

// RedisCommand represents the structure of the Redis command
type RedisCommand struct {
	Data struct {
		Key       string      `json:"key"`
		Value     interface{} `json:"value"`
		Expiry    int         `json:"expiry"`
		Operation string      `json:"operation"`
	} `json:"data"`
}

// RedisResponse represents the structure of the Redis response
type RedisResponse struct {
	Status  string      `json:"status"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// NewRedisClient creates a new Redis client with the given configuration
func NewRedisClient(config RedisConfig) *RedisClient {
	if config.Timeout == 0 {
		config.Timeout = 10 * time.Second
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}

	return &RedisClient{
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

var config = RedisConfig{
	BaseURL:    "https://asia-south1-op-d2r.cloudfunctions.net/redis_interface_python",
	Timeout:    5 * time.Second,
	MaxRetries: 3,
}

var Client = NewRedisClient(config)

// ExecuteCommand executes a Redis command
func (c *RedisClient) ExecuteCommand(key string, value interface{}, expiry int, operation string) (*RedisResponse, error) {
	command := RedisCommand{}
	command.Data.Key = key
	command.Data.Value = value
	command.Data.Expiry = expiry
	command.Data.Operation = operation

	payload, err := json.Marshal(command)
	if err != nil {
		return nil, fmt.Errorf("error marshaling command: %w", err)
	}

	var response *RedisResponse
	var lastErr error

	// Implement retry logic
	for attempt := 0; attempt < c.config.MaxRetries; attempt++ {
		response, lastErr = c.executeRequest(payload)
		if lastErr == nil {
			return response, nil
		}

		// Wait before retrying, with exponential backoff
		if attempt < c.config.MaxRetries-1 {
			time.Sleep(time.Duration(attempt+1) * 500 * time.Millisecond)
		}
	}

	return nil, fmt.Errorf("failed after %d attempts, last error: %w", c.config.MaxRetries, lastErr)
}

func (c *RedisClient) executeRequest(payload []byte) (*RedisResponse, error) {
	req, err := http.NewRequest(http.MethodPost, c.config.BaseURL, bytes.NewReader(payload))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error executing request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}

	var redisResp RedisResponse
	if err := json.Unmarshal(body, &redisResp); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %w", err)
	}

	return &redisResp, nil
}

// Common Redis operations
const (
	OperationSet    = "set"
	OperationGet    = "get"
	OperationDelete = "delete"
)

// Helper methods for common operations
func (c *RedisClient) Set(key string, value interface{}, expiry int) (*RedisResponse, error) {
	return c.ExecuteCommand(key, value, expiry, OperationSet)
}

func (c *RedisClient) Get(key string) (*RedisResponse, error) {
	return c.ExecuteCommand(key, nil, 0, OperationGet)
}

func (c *RedisClient) Delete(key string) (*RedisResponse, error) {
	return c.ExecuteCommand(key, nil, 0, OperationDelete)
}
