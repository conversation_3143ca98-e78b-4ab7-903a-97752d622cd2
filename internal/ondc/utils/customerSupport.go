package utils

import (
	"fmt"
	"kc/internal/ondc/models/dto"
	"regexp"
	"strings"
	"time"
)

const (
	TICKET_PRIORITY_P0 = "p0"
	TICKET_PRIORITY_P1 = "p1"
	TICKET_PRIORITY_P2 = "p2"
	TICKET_PRIORITY_P3 = "p3"
	TICKET_PRIORITY_P4 = "p4"
)

var TICKETS_PRIORITY_VALUES = []map[string]string{
	{"value": TICKET_PRIORITY_P0, "label": "P0", "color": "#FF0000"}, // Red for critical priority
	{"value": TICKET_PRIORITY_P1, "label": "P1", "color": "#FF8C00"}, // Dark orange for high priority
	{"value": TICKET_PRIORITY_P2, "label": "P2", "color": "#FFD700"}, // Gold/yellow for medium priority
	{"value": TICKET_PRIORITY_P3, "label": "P3", "color": "#4682B4"}, // Steel blue for low priority
	{"value": TICKET_PRIORITY_P4, "label": "P4", "color": "#708090"}, // Slate gray for lowest priority
}

var TICKETS_PRIORITY_VALUES_MAP = map[string]map[string]string{
	TICKET_PRIORITY_P0: {"label": "P0", "color": "#FF0000"}, // Red for critical priority
	TICKET_PRIORITY_P1: {"label": "P1", "color": "#FF8C00"}, // Dark orange for high priority
	TICKET_PRIORITY_P2: {"label": "P2", "color": "#FFD700"}, // Gold/yellow for medium priority
	TICKET_PRIORITY_P3: {"label": "P3", "color": "#4682B4"}, // Steel blue for low priority
	TICKET_PRIORITY_P4: {"label": "P4", "color": "#708090"}, // Slate gray for lowest priority
}

const (
	TICKET_STATUS_OPEN       = "open"
	TICKET_STATUS_INPROGRESS = "in_progress"
	TICKET_STATUS_ONHOLD     = "on_hold"
	TICKET_STATUS_RESOLVED   = "resolved"
	TICKET_STATUS_CLOSED     = "closed"
	TICKET_STATUS_INPROGRESS_3PL = "in_progress_3pl"
)

var TICKETS_STATUS_VALUES = []map[string]string{
	{"value": "all", "label": "All"},
	{"value": TICKET_STATUS_OPEN, "label": "OPEN"},
	{"value": TICKET_STATUS_INPROGRESS, "label": "IN PROGRESS"},
	{"value": TICKET_STATUS_ONHOLD, "label": "ON HOLD"},
	{"value": TICKET_STATUS_RESOLVED, "label": "RESOLVED"},
	{"value": TICKET_STATUS_CLOSED, "label": "CLOSED"},
	{"value": TICKET_STATUS_INPROGRESS_3PL, "label": "IN PROGRESS 3PL"},
}

var TICKETS_STATUS_VALUES_SELLER_SIDE = []map[string]string{
	{"value": "pending", "label": "OPEN"},
	{"value": "completed", "label": "CLOSED"},
}

const (
	TICKET_PROGRESS_NEW                   = "new"                     // Just created, no action taken
	TICKET_PROGRESS_PENDING_AGENT_REVIEW  = "pending_agent_review"    // Waiting for agent review
	TICKET_PROGRESS_PENDING_AGENT_RESP    = "pending_agent_response"  // Agent is reviewing/preparing response
	TICKET_PROGRESS_AGENT_REPLIED         = "agent_replied"           // Agent has replied, waiting for next step
	TICKET_PROGRESS_PENDING_SELLER_REVIEW = "pending_seller_review"   // Assigned to brand but not yet
	TICKET_PROGRESS_PENDING_SELLER_RESP   = "pending_seller_response" // Brand has seen it but not yet responded
	TICKET_PROGRESS_SELLER_REPLIED        = "seller_replied"          // Brand has replied
	TICKET_PROGRESS_PENDING_TECH_REVIEW   = "pending_tech_review"     // Assigned to tech but not yet reviewed
	TICKET_PROGRESS_PENDING_TECH_RESP     = "pending_tech_response"   // Tech has seen it but not yet
	TICKET_PROGRESS_TECH_REPLIED          = "tech_replied"            // Tech has replied
	TICKET_PROGRESS_PENDING_OPS_REVIEW    = "pending_ops_review"      // Assigned to ops but not yet reviewed
	TICKET_PROGRESS_PENDING_OPS_RESP      = "pending_ops_response"    // Ops has seen it but not yet responded
	TICKET_PROGRESS_OPS_REPLIED           = "ops_replied"             // Ops has replied
	TICKET_PROGRESS_PENDING_CUSTOMER      = "pending_customer"        // Waiting for customer response
	TICKET_PROGRESS_PENDING_ADDITIONAL    = "pending_additional_info" // Waiting for additional info from any party
	TICKET_PROGRESS_READY_TO_RESOLVE      = "ready_to_resolve"        // Issue addressed, preparing to close
	TICKET_PROGRESS_RESOLVED              = "resolved"                // Solution provided
	TICKET_PROGRESS_CLOSED                = "closed"                  // Ticket closed after confirmation
)

var TICKETS_PROGRESS_STATE_VALUES = []map[string]string{
	{"value": TICKET_PROGRESS_NEW, "label": "New"},
	{"value": TICKET_PROGRESS_PENDING_AGENT_REVIEW, "label": "Pending Agent Review"},
	{"value": TICKET_PROGRESS_PENDING_AGENT_RESP, "label": "Pending Agent Response"},
	{"value": TICKET_PROGRESS_AGENT_REPLIED, "label": "Agent Replied"},
	{"value": TICKET_PROGRESS_PENDING_SELLER_REVIEW, "label": "Pending Brand Review"},
	{"value": TICKET_PROGRESS_PENDING_SELLER_RESP, "label": "Pending Brand Response"},
	{"value": TICKET_PROGRESS_SELLER_REPLIED, "label": "Brand Replied"},
	{"value": TICKET_PROGRESS_PENDING_TECH_REVIEW, "label": "Pending Tech Review"},
	{"value": TICKET_PROGRESS_PENDING_TECH_RESP, "label": "Pending Tech Response"},
	{"value": TICKET_PROGRESS_TECH_REPLIED, "label": "Tech Replied"},
	{"value": TICKET_PROGRESS_PENDING_OPS_REVIEW, "label": "Pending Ops Review"},
	{"value": TICKET_PROGRESS_PENDING_OPS_RESP, "label": "Pending Ops Response"},
	{"value": TICKET_PROGRESS_OPS_REPLIED, "label": "Ops Replied"},
	{"value": TICKET_PROGRESS_PENDING_CUSTOMER, "label": "Pending Customer"},
	{"value": TICKET_PROGRESS_PENDING_ADDITIONAL, "label": "Pending Additional Info"},
	{"value": TICKET_PROGRESS_READY_TO_RESOLVE, "label": "Ready to Resolve"},
	{"value": TICKET_PROGRESS_RESOLVED, "label": "Resolved"},
	{"value": TICKET_PROGRESS_CLOSED, "label": "Closed"},
}

const (
	AGENT_ROLE_AGENT       = "agent"
	AGENT_ROLE_ADMIN       = "admin"
	AGENT_ROLE_SUPER_ADMIN = "super_admin"
)

var AGENT_ROLES = []map[string]string{
	{"value": AGENT_ROLE_AGENT, "label": "Agent"},
	{"value": AGENT_ROLE_ADMIN, "label": "Admin"},
	{"value": AGENT_ROLE_SUPER_ADMIN, "label": "Super Admin"},
}

const (
	AGENT_STATUS_ACTIVE    = "active"
	AGENT_STATUS_INACTIVE  = "inactive"
	AGENT_STATUS_SUSPENDED = "suspended"
)

var AGENT_STATUS = []map[string]string{
	{"value": AGENT_STATUS_ACTIVE, "label": "Active"},
	{"value": AGENT_STATUS_INACTIVE, "label": "Inactive"},
	{"value": AGENT_STATUS_SUSPENDED, "label": "Suspended"},
}

const (
	AGENT_TEAM_CS     = "cs"
	AGENT_TEAM_SELLER = "seller"
	AGENT_TEAM_TECH   = "tech"
	AGENT_TEAM_OPS    = "ops"
)

var AGENT_TEAM = []map[string]string{
	{"value": AGENT_TEAM_CS, "label": "Customer Support"},
	{"value": AGENT_TEAM_SELLER, "label": "Seller"},
	{"value": AGENT_TEAM_TECH, "label": "Tech"},
	{"value": AGENT_TEAM_OPS, "label": "Ops"},
}

const (
	// Content Messages
	MESSAGE_TYPE_TICKET_CREATION = "ticket_creation"
	MESSAGE_TYPE_USER_REPLY      = "user_reply"
	MESSAGE_TYPE_AGENT_REPLY     = "agent_reply"
	MESSAGE_TYPE_SELLER_REPLY    = "seller_reply"

	// Status Changes
	MESSAGE_TYPE_STATUS_CHANGE     = "status_change"
	MESSAGE_TYPE_TICKET_RESOLUTION = "ticket_resolution"
	MESSAGE_TYPE_TICKET_REOPENED   = "ticket_reopened"

	// Assignment Changes
	MESSAGE_TYPE_ASSIGNEE_CHANGE           = "assignee_change"
	MESSAGE_TYPE_ESCALATION                = "escalation"
	MESSAGE_TYPE_TRANSFERRED_TO_DEPARTMENT = "transferred_to_department"

	// Metadata Changes
	MESSAGE_TYPE_PRIORITY_CHANGE                = "priority_change"
	MESSAGE_TYPE_CATEGORY_CHANGE                = "category_change"
	MESSAGE_TYPE_TAGS_UPDATED                   = "tags_updated"
	MESSAGE_TYPE_TICKET_RESOLUTION_RATING_ADDED = "ticket_resolution_rating_added"

	// Agent Action Requests
	MESSAGE_TYPE_AGENT_REQUEST_REFUND        = "agent_request_refund"
	MESSAGE_TYPE_AGENT_REQUEST_CLARIFICATION = "agent_request_clarification"

	// Seller/Brand Responses
	MESSAGE_TYPE_SELLER_APPROVE_REFUND        = "seller_approve_refund"
	MESSAGE_TYPE_SELLER_CHALLENGE_REFUND      = "seller_challenge_refund"
	MESSAGE_TYPE_SELLER_PROVIDE_CLARIFICATION = "seller_provide_clarification"

	// Documentation
	MESSAGE_TYPE_ATTACHMENT_ADDED    = "attachment_added"
	MESSAGE_TYPE_INTERNAL_NOTE       = "internal_note"
	MESSAGE_TYPE_SYSTEM_NOTIFICATION = "system_notification"
	MESSAGE_TYPE_AUDIT_LOG_ENTRY     = "audit_log_entry"

	// Ticket Management
	// MESSAGE_TYPE_TICKET_MERGED    = "ticket_merged"
	// MESSAGE_TYPE_TICKET_SPLIT     = "ticket_split"
	MESSAGE_TYPE_DUPLICATE_MARKED = "duplicate_marked"

	// Customer Experience
	MESSAGE_TYPE_USER_SATISFACTION_UPDATE = "user_satisfaction_update"
	MESSAGE_TYPE_FOLLOW_UP_REMINDER       = "follow_up_reminder"

	// SLA/Timing Related
	MESSAGE_TYPE_SLA_BREACH                   = "sla_breach"
	MESSAGE_TYPE_FIRST_RESPONSE_TIME_EXCEEDED = "first_response_time_exceeded"
	MESSAGE_TYPE_AWAITING_RESPONSE_REMINDER   = "awaiting_response_reminder"
)

const (
	MESSAGE_SENDER_USER   = "user"
	MESSAGE_SENDER_AGENT  = "agent"
	MESSAGE_SENDER_SELLER = "seller"
	MESSAGE_SENDER_SYSTEM = "system"
)

var MESSAGE_SENDER_TYPE = []map[string]string{
	{"value": MESSAGE_SENDER_USER, "label": "User"},
	{"value": MESSAGE_SENDER_AGENT, "label": "Agent"},
	{"value": MESSAGE_SENDER_SELLER, "label": "Seller"},
	{"value": MESSAGE_SENDER_SYSTEM, "label": "System"},
}

const (
	// Ticket Action Types
	TICKET_REQUESTED_ACTION_REFUND               = "request_refund"
	TICKET_REQUESTED_ACTION_CLARIFICATION        = "request_clarification"
	TICKET_RESPONSE_ACTION_CHALLENGE_REFUND      = "response_challenge_refund"
	TICKET_RESPONSE_ACTION_APPROVE_REFUND        = "response_approve_refund"
	TICKET_RESPONSE_ACTION_PROVIDE_CLARIFICATION = "response_provide_clarification"
)

var TICKET_ACTION_MAP = map[string]string{
	TICKET_REQUESTED_ACTION_REFUND:               "Requested Refund",
	TICKET_REQUESTED_ACTION_CLARIFICATION:        "Requested Clarification",
	TICKET_RESPONSE_ACTION_CHALLENGE_REFUND:      "Challenge Refund",
	TICKET_RESPONSE_ACTION_APPROVE_REFUND:        "Approve Refund",
	TICKET_RESPONSE_ACTION_PROVIDE_CLARIFICATION: "Provide Clarification",
}

const (
	TICKET_FLOW_FOFD = "fofdPlusOneFlow"
	TICKET_FLOW_APP_STANDARD = "defaultOrderingSupportFlow"
)

// GetMessageTemplate returns a specific template with custom parameters
func GetMessageTemplate(templateID string, params dto.DeliveryTemplateParams) string {
	templates := map[string]string{
		"out_for_delivery": "**आपका ऑर्डर आज डिलीवरी के लिए निकला है। यह आज डिलीवर किया जाएगा।",
		"in_transit":       "**आपका ऑर्डर %s से निकल चुका है। %s तक डिलीवरी हो जाएगी।",
		"inconvenience":    "असुविधा के लिए खेद है। कृपया मुझे जाँच करने और आपसे संपर्क करने के लिए कुछ समय दें।",
	}

	baseTemplate, exists := templates[templateID]
	if !exists {
		return ""
	}

	// For "in_transit" template
	if templateID == "in_transit" {
		hasCurrentCity := params.CurrentCity != ""
		hasEstimatedDelivery := params.EstimatedDelivery != ""

		if hasCurrentCity && hasEstimatedDelivery {
			return fmt.Sprintf(baseTemplate, params.CurrentCity, params.EstimatedDelivery)
		} else if hasCurrentCity {
			// Only current city is present
			return fmt.Sprintf("**आपका ऑर्डर %s से निकल चुका है। डिलीवरी जल्द ही होगी।", params.CurrentCity)
		} else if hasEstimatedDelivery {
			// Only estimated delivery is present
			return fmt.Sprintf("**आपका ऑर्डर निकल चुका है। %s तक डिलीवरी हो जाएगी।", params.EstimatedDelivery)
		}
		// Neither current city nor estimated delivery is present
		return "**आपका ऑर्डर निकल चुका है। डिलीवरी जल्द ही होगी।"
	}

	return baseTemplate
}


func GetOrderStatusTemplate(params dto.DeliveryTemplateParams) string {
	
	// If either Courier or AWB is missing, show generic message
	if params.Courier == "" || params.AWB == "" {
		return "आपका ऑर्डर इन-ट्रांज़िट है और डिलीवर हो जाएगा।"
	}

	// Generate courier link based on courier name
	var courierLink string
	switch strings.ToLower(params.Courier) {
	case "delhivery":
		courierLink = fmt.Sprintf("https://www.delhivery.com/track-v2/package/%s", params.AWB)
	case "ekart":
		courierLink = fmt.Sprintf("https://www.ekartlogistics.com/ekartlogistics-web/shipmenttrack/%s", params.AWB)
	case "ekart large":
		courierLink = fmt.Sprintf("https://www.ekartlogistics.com/ekartlogistics-web/shipmenttrack/%s", params.AWB)
	default:
		
		courierLink = fmt.Sprintf("https://www.delhivery.com/track-v2/package/%s", params.AWB)
	}

	// Use different templates based on whether EstimatedDelivery is available
	if params.EstimatedDelivery == "" {
		// Template without "तक" when no specific date
		template := "आपका ऑर्डर इन-ट्रांज़िट है और जल्द ही डिलीवर हो जाएगा। आप इसे नीचे दिए गए लिंक से ट्रैक कर सकते हैं:\n%s"
		return fmt.Sprintf(template, courierLink)
	} else {
		// Template with "तक" when specific date is available
		template := "आपका ऑर्डर इन-ट्रांज़िट है और %s तक डिलीवर हो जाएगा। आप इसे नीचे दिए गए लिंक से ट्रैक कर सकते हैं:\n%s"
		return fmt.Sprintf(template, params.EstimatedDelivery, courierLink)
	}
}


func GetDefaultTemplates(params dto.DeliveryTemplateParams) []dto.QuickReplyTemplate {
	// New delivery-related templates with customized text
	templates := []dto.QuickReplyTemplate{
		{
			ID:   "out_for_delivery",
			Icon: "DeliveryIcon",
			Name: "Out for Delivery",
			Text: "**आपका ऑर्डर आज डिलीवरी के लिए निकला है। यह आज डिलीवर किया जाएगा।",
		},
		{
			ID:   "in_transit",
			Icon: "PackageFilledIcon",
			Name: "In Transit",
			Text: GetMessageTemplate("in_transit", params), // This will be customized based on params
		},
		{
			ID:   "inconvenience",
			Icon: "AlertCircleIcon",
			Name: "Inconvenience",
			Text: "असुविधा के लिए खेद है। कृपया मुझे जाँच करने और आपसे संपर्क करने के लिए कुछ समय दें।",
		},
		
		{
			ID:   "return_related_quality_issue",
			Icon: "AlertTriangleIcon",
			Name: "Return Related or Quality Issue",
			Text: "नमस्ते सर,\nकिराना क्लब से ऑर्डर करने के लिए धन्यवाद।\nहम आपकी बात को पूरी तरह समझते हैं।\nकिराना क्लब पर डिलीवरी के बाद रिटर्न की सुविधा नहीं है, क्योंकि हम होलसेल मॉडल पर काम करते हैं और ऑर्डर सीधे ब्रांड या स्टॉकिस्ट से सप्लाई होते हैं। इससे आपको सबसे बेहतर रेट और मुनाफा मिल पाता है।\nहम हर ऑर्डर को भेजने से पहले अच्छी तरह चेक करते हैं ताकि प्रोडक्ट सही और बढ़िया हालत में पहुंचे।\nआपके सहयोग के लिए धन्यवाद।",
		},
		{
			ID:   "order_cancel_approved",
			Icon: "CheckCircleIcon",
			Name: "Order Cancel Approved",
			Text: "हमें खेद है कि आपको समय पर डिलीवरी नहीं मिल पाई। जैसा कि आपने बताया है कि अब आपको ऑर्डर नहीं चाहिए, तो हम रिटर्न प्रक्रिया शुरू कर रहे हैं। आपके धैर्य और सहयोग के लिए धन्यवाद। आशा है कि हमें भविष्य में फिर से सेवा का अवसर मिलेगा",
		},
		{
			ID:   "missing_damaged_wrong_item_query_received",
			Icon: "PackageIcon",
			Name: "Missing/Damaged/Wrong Item - Query Received",
			Text: "नमस्ते सर/मैडम,\nहमने आपकी शिकायत प्राप्त कर ली है और इस पर जांच शुरू कर दी है। हम मामले की पूरी तरह से जांच करेंगे और 5-7 दिनों में आपको परिणाम से अवगत कराएंगे। अगर आपकी क्लेम सही पाई जाती है, तो हम रिफंड की प्रक्रिया शुरू कर देंगे।\nधन्यवाद,",
		},
		{
			ID:   "delivery_delay_non_delivery_response",
			Icon: "ClockIcon",
			Name: "Delivery Delay / Non-Delivery Response",
			Text: "हमें खेद है कि आपका ऑर्डर तय समय पर डिलीवर नहीं हो पाया। डिलीवरी में अप्रत्याशित देरी के लिए हम माफी चाहते हैं। हमारा प्रयास है कि आपका ऑर्डर जल्द से जल्द आपको पहुँचाया जाए। कृपया हमें थोड़ा और समय दें — हम आपको अपडेट देते रहेंगे। आपके धैर्य और सहयोग के लिए धन्यवाद।",
		},
		{
			ID:   "reward_points_eligible",
			Icon: "ForkliftIcon",
			Name: "Reward Points - Eligible",
			Text: "डिलीवरी के बाद मिलने वाले रिवॉर्ड पॉइंट्स जल्द ही आपके अकाउंट में भेज दिए जाएंगे।",
		},
		{
			ID:   "self_pickup_delivery_failure",
			Icon: "LocationIcon",
			Name: "Self Pickup & Delivery Failure",
			Text: "इस असुविधा के लिए हमें खेद है कि डिलीवरी बॉय ने आपको बाहर बुलाया। हम आपसे निवेदन करते हैं कि कृपया थोड़ा सहयोग करें, आपका ऑर्डर सुरक्षित रूप से आपको मिल जाएगा। हम भविष्य में इस बात का पूरा ध्यान रखेंगे कि आपको बेहतर सेवा मिले, धन्यवाद !!!!",
		},
		{
			ID:   "missing_damaged_wrong_item_proof_needed",
			Icon: "CameraIcon",
			Name: "Missing/Damaged/Wrong Item - Proof Needed",
			Text: "हमें खेद है कि आपको प्रोडक्ट डैमेज/मिसिंग /गलत प्राप्त हुआ। कृपया समस्या को बेहतर तरीके से समझने के लिए हमें उस प्रोडक्ट की कुछ स्पष्ट फोटो और एक छोटा वीडियो भेजने की कृपा करें। इससे हमें आपकी शिकायत को जल्दी सुलझाने में सहायता मिलेगी। धन्यवाद |",
		},
		{
			ID:   "missing_bag_specific_zoff",
			Icon: "ArchiveIcon",
			Name: "Missing Bag Specific (Zoff)",
			Text: "हमें खेद है कि आपके Zoff ऑर्डर के साथ Zoff का बैग नहीं मिल पाया। यह गलती हमारी तरफ से हुई है, जिसके लिए हम sincerely माफी चाहते हैं। हम इस मुद्दे को एस्केलेट कर रहे हैं और जल्द से जल्द इसे सुलझाने की कोशिश करेंगे।",
		},
		{
			ID:   "cashback",
			Icon: "CreditCardIcon",
			Name: "Cashback",
			Text: "हमें खेद है कि आपका कैशबैक अब तक क्रेडिट नहीं हो पाया है। कृपया निश्चिंत रहें, यह जल्द ही आपकी आईडी पर जोड़ दिया जाएगा। हम आपके धैर्य और विश्वास के लिए आभारी हैं। आपकी संतुष्टि हमारे लिए सबसे महत्वपूर्ण है, और हमें उम्मीद है कि आप हमें दोबारा सेवा का अवसर देंगे। अगला ऑर्डर आपके लिए और भी बेहतर अनुभव लेकर आएगा",
		},
		{
			ID:   "pincode_serviceability_issue_order_cancelled",
			Icon: "DomainIcon",
			Name: "Pincode Serviceability Issue Order Cancelled",
			Text: "आपको हुई असुविधा के लिए खेद है सर पर फिलहाल आपके क्षेत्र में हमारी डिलीवरी सेवा उपलब्ध नहीं है , इस वजह से हम आपको ऑर्डर पहुचाने मे असमर्थ है , जल्द ही आपके एरिया मे डेलीवेरी सेवा उपलब्ध होजाएगी आप ऑर्डर फिरसे लगा पाएंगे , किराना क्लब के साथ बने रहने के लिए धन्यवाद !!!",
		},
		{
			ID:   "spam_or_uncleared_ticket",
			Icon: "StopCircleIcon",
			Name: "Spam or Uncleared Ticket",
			Text: "नमस्ते,\nआपने एक टिकट दर्ज किया है, लेकिन उसमें समस्या स्पष्ट नहीं है और आपका कॉल भी नहीं लग पा रहा है। कृपया हमें बताएं कि आपको किस बात में सहायता चाहिए, ताकि हम जल्दी समाधान दे सकें। धन्यवाद!",
		},
		{
			ID:   "advance_refund",
			Icon: "MoneyFilledIcon",
			Name: "Advance Refund",
			Text: "नमस्ते सर,\nहमने आपका रिफंड इनिशिएट कर दिया है। यह आपके अकाउंट में 1-2 दिनों में रिफ्लेक्ट हो जाएगा।\nयदि आपको किसी और सहायता की आवश्यकता हो, तो कृपया हमसे संपर्क करें।\nधन्यवाद,\nKirana Club सपोर्ट टीम",
		},
		{
			ID:   "call_not_connected",
			Icon: "PhoneIcon",
			Name: "Call Not Connected",
			Text: "नमस्ते,\nहमने आपकी समस्या के समाधान के लिए आपको कॉल किया था, लेकिन कॉल कनेक्ट नहीं हो पाया। कृपया जब भी आप फ्री हों, हमें चैट के माध्यम से संपर्क करें । हम आपकी मदद के लिए तैयार हैं। धन्यवाद",
		},
		{
			ID:   "cant_change_order",
			Icon: "AlertCircleIcon",
			Name: "Can't Change Order",
			Text: "नमस्कार, आपका ऑर्डर पहले ही ट्रांज़िट (डिलीवरी के रास्ते में) जा चुका है। इस स्थिति में ऑर्डर में किसी भी प्रकार का बदलाव करना संभव नहीं है। आप से रिक्वेस्ट है की आप  आर्डर को एक्सेप्ट करे ताकि आपका प्लेटफ्रॉम पर रिकॉर्ड अच्छा बना रहे। हम आपकी असुविधा के लिए क्षमा चाहते हैं और आपके सहयोग के लिए धन्यवाद करते हैं।",
		},
		{
			ID:   "poonji",
			Icon: "IconsIcon",
			Name: "Poonji",
			Text: "नमस्ते,\nआपका ऑर्डर सफलतापूर्वक डिलीवर हो गया है, और पूंजी से जुड़ी जानकारी के लिए धन्यवाद।\nहम आपको सूचित करना चाहते हैं कि सभी योग्य उपयोगकर्ताओं को पूंजी एक साथ प्रदान की जाएगी। यह प्रक्रिया कंपनी की पॉलिसी के अनुसार की जा रही है।\nजैसे ही पूंजी आपके अकाउंट में ट्रांसफर की जाएगी या भेजी जाएगी, आपको इसकी पुष्टि संदेश के माध्यम से दी जाएगी।\nआपकी समझदारी और धैर्य के लिए धन्यवाद।",
		},
		{
			ID:   "refund_in_wallet",
			Icon: "WalletIcon",
			Name: "Refund in Wallet",
			Text: "नमस्ते सर/मैडम, आपका ₹{{refund_amount}} का रिफंड आपके Kirana Club वॉलेट में सफलतापूर्वक जमा कर दिया गया है। आप इसे अपनी यूपीआई आईडी के माध्यम से सीधे अपने बैंक खाते में ट्रांसफर भी कर सकते हैं। वॉलेट का उपयोग, बैलेंस चेक करने और ट्रांसफर करने के लिए कृपया इस लिंक पर जाएँ: https://app.kirana.club/tRdLdcF1g5X8MmNw9 भविष्य में, यदि आप अपना पूरा अनपैकिंग वीडियो प्रदान करेंगे, तो आपकी समस्या का समाधान और भी आसानी से और तेजी से किया जा सकेगा। हम हुई देरी के लिए दिल से क्षमा चाहते हैं। आपकी समस्या को हल मानते हुए इस टिकट को बंद किया जा रहा है। यदि आपको किसी भी और सहायता की आवश्यकता हो, तो कृपया हमसे बेझिझक संपर्क करें। धन्यवाद",
		},
		{
			ID:   "commented_mistakenly",
			Icon: "ChatIcon",
			Name: "Commented Mistakenly",
			Text: "कृपया ऊपर वाला संदेश नजरअंदाज करें, वह गलती से भेजा गया था। इसके लिए हमें खेद है। सही जानकारी के लिए नीचे दिया गया मैसेज देखें। धन्यवाद!",
		},
		{
			ID:   "proof_not_received",
			Icon: "BillIcon",
			Name: "Proof Not Received",
			Text: "नमस्ते,\nआपके द्वारा बताई गई मिसिंग आइटम संबंधी शिकायत को लेकर हमने पूरी जांच की है।\nहालांकि, बार-बार अनुरोध करने के बावजूद हमें आपकी तरफ से कोई unboxing वीडियो या प्रूफ प्राप्त नहीं हुआ, जो कि इस तरह की शिकायतों को सुलझाने के लिए अनिवार्य होता है।\nइस कारणवश, हम आपकी टिकट को बिना समाधान के बंद करने के लिए बाध्य हैं।\nकृपया भविष्य में कोई भी प्रोडक्ट रिसीव करते समय अनबॉक्सिंग वीडियो जरूर रिकॉर्ड करें ताकि इस प्रकार की स्थिति में त्वरित सहायता दी जा सके।",
		},
		{
			ID:   "order_status",
			Icon: "LinkIcon",
			Name: "Order Status",
			Text: GetOrderStatusTemplate(params),
		},
	}

	return templates
}

// ConvertUTCToIST converts a UTC timestamp to IST date string
func ConvertUTCToIST(utcTimestamp int64) string {
	// Create time from Unix timestamp (seconds since epoch)
	utcTime := time.UnixMilli(utcTimestamp)

	// Load IST location (UTC+5:30)
	istLocation, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		// Fallback if location loading fails
		istLocation = time.FixedZone("IST", 5*60*60+30*60) // UTC+5:30
	}

	// Convert UTC time to IST
	istTime := utcTime.In(istLocation)

	// Month names in Hindi
	hindiMonths := map[time.Month]string{
		time.January:   "जनवरी",
		time.February:  "फरवरी",
		time.March:     "मार्च",
		time.April:     "अप्रैल",
		time.May:       "मई",
		time.June:      "जून",
		time.July:      "जुलाई",
		time.August:    "अगस्त",
		time.September: "सितंबर",
		time.October:   "अक्टूबर",
		time.November:  "नवंबर",
		time.December:  "दिसंबर",
	}

	// Format with day, Hindi month name, and year
	dateIST := fmt.Sprintf("%d %s %d",
		istTime.Day(),
		hindiMonths[istTime.Month()],
		istTime.Year(),
	)

	return dateIST
}

// Alternative function to return date in English
func ConvertUTCToISTEnglish(utcTimestamp int64) string {
	// Create time from Unix timestamp
	utcTime := time.UnixMilli(utcTimestamp)

	// Load IST location (UTC+5:30)
	istLocation, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		istLocation = time.FixedZone("IST", 5*60*60+30*60)
	}

	// Convert UTC time to IST
	istTime := utcTime.In(istLocation)

	// Format date in English - "2 Jan 2023"
	return istTime.Format("2 Jan 2006")
}

func SafeDeref[T any](ptr *T) T {
	var zero T
	if ptr == nil {
		return zero
	}
	return *ptr
}

func FormatScanLocation(input string) string {
	// Replace underscores with spaces and remove _H or H before the parentheses
	result := strings.ReplaceAll(input, "_", " ")
	result = regexp.MustCompile(`\s*H\s*(\()`).ReplaceAllString(result, " $1")

	// Clean up extra spaces
	return strings.Join(strings.Fields(result), " ")
}
