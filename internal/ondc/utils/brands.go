package utils

import (
	"encoding/json"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/service/orderStatus/constants"
)

var ADVANCE_PAYMENT_SHEET_APIS = map[string]string{
	"zoff_foods": "https://script.google.com/macros/s/AKfycby4wQ1f_nNvqfZu1zBUZeKvaxzPT-w1c7vRr94wRWTJvhp15ThzIYQ5InDJXtDwdCg-/exec",
}

var TOP_BADGE_STYLES = map[string]interface{}{
	"hugs": map[string]interface{}{
		"color":    "#000000",
		"bg_color": []string{"rgba(250, 228, 76, 1)", "rgba(246, 198, 67, 1)"},
	},
	"go_desi": map[string]interface{}{
		"color":    "#000000",
		"bg_color": []string{"rgba(250, 228, 76, 1)", "rgba(246, 198, 67, 1)"},
	},
}

var FIRST_ORDER_BANNER_IMAGES = map[string]string{
	HUGS:            "https://d2rstorage2.blob.core.windows.net/widget/January/14/cba1996b-f4c3-46bf-ae1e-d730714a0fd1/1736854460344.webp",
	GO_DESI:         "https://d2rstorage2.blob.core.windows.net/widget/January/8/642141f8-851d-4d26-abf6-14a73fd94f41/1736347188263.webp",
	APSARA_TEA:      "https://d2rstorage2.blob.core.windows.net/widget/March/11/0992fca9-9c33-4ce6-816f-ef6e1eb283d4/1741691454360.webp",
	MOTHERS_KITCHEN: "https://d2rstorage2.blob.core.windows.net/widget/January/24/a490b9a5-f5c5-4607-9a5f-a211949cf8b1/1737728108651.webp",
	ZOFF_FOODS:      "https://d2rstorage2.blob.core.windows.net/widget/March/19/dc925f28-2041-4243-a9dd-353787329b79/1742395432807.webp",
}

const (
	ZOFF_FOODS                 = "zoff_foods"
	GO_DESI                    = "go_desi"
	PANCHVATI                  = "panchvati"
	HUGS                       = "hugs"
	MOTHERS_KITCHEN            = "mothers_kitchen"
	APSARA_TEA                 = "apsara_tea"
	MANGALAM                   = "mangalam"
	NUTRAJ                     = "nutraj"
	MICHIS                     = "michis"
	LOTS                       = "lots"
	CRAVITOS                   = "cravitos"
	SOOTHE                     = "soothe"
	MILDEN                     = "milden"
	KIRANACLUB_REWARDS         = "kiranaclub_rewards"
	RSB_SUPER_STOCKIST         = "rsb_super_stockist"
	KIRANA_CLUB                = "kirana_club"
	BOLAS                      = "bolas"
	CHUK_DE                    = "chuk_de"
	KIRANACLUB_LOYALTY_REWARDS = "kiranaclub_loyalty_rewards"
	CANDYLAKE                  = "candylake"
	SUGANDH                    = "sugandh"
	SOMNATH                    = "somnath"
	UNDEFINED                  = "undefined"
	CINTU                      = "cintu"
	PARIMAL                    = "parimal"
)

var BRAND_PAYMENT_CONDITIONS = dto.BrandPaymentConditions{
	"default": []dto.PaymentConditions{
		{
			Type:                     constants.COD,
			MinimumAmountForValidity: 0,
			MaximumAmountForValidity: 99999999,
			MinimumPaymentAmount:     0,
			MaximumPaymentAmount:     0,
			Percentage:               0,
			Fixed:                    0,
			DiscountPercentage:       0,
			DiscountFixedAmount:      0,
			MaximumDiscountAmount:    0,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.FULLY_PAID,
			MinimumAmountForValidity: 0,
			MaximumAmountForValidity: 600,
			MinimumPaymentAmount:     0,
			MaximumPaymentAmount:     99999999,
			Percentage:               1,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      1,
			MaximumDiscountAmount:    1,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.FULLY_PAID,
			MinimumAmountForValidity: 601,
			MaximumAmountForValidity: 99999999,
			MinimumPaymentAmount:     0,
			MaximumPaymentAmount:     99999999,
			Percentage:               1,
			Fixed:                    -1,
			DiscountPercentage:       .02,
			DiscountFixedAmount:      -1,
			MaximumDiscountAmount:    99999999,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.PARTIALLY_PAID,
			MinimumAmountForValidity: 0,
			MaximumAmountForValidity: 600,
			MinimumPaymentAmount:     100,
			MaximumPaymentAmount:     2000,
			Percentage:               0.05,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      1,
			MaximumDiscountAmount:    1,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.PARTIALLY_PAID,
			MinimumAmountForValidity: 601,
			MaximumAmountForValidity: 1200,
			MinimumPaymentAmount:     100,
			MaximumPaymentAmount:     2000,
			Percentage:               0.05,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      10,
			MaximumDiscountAmount:    10,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.PARTIALLY_PAID,
			MinimumAmountForValidity: 1201,
			MaximumAmountForValidity: 2500,
			MinimumPaymentAmount:     100,
			MaximumPaymentAmount:     2000,
			Percentage:               0.05,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      30,
			MaximumDiscountAmount:    30,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.PARTIALLY_PAID,
			MinimumAmountForValidity: 2501,
			MaximumAmountForValidity: 99999999,
			MinimumPaymentAmount:     100,
			MaximumPaymentAmount:     2000,
			Percentage:               0.05,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      50,
			MaximumDiscountAmount:    50,
			ApplicableOn:             []string{"ALL"},
		},
	},
	"kirana_club": []dto.PaymentConditions{
		{
			Type:                     constants.FULLY_PAID,
			MinimumAmountForValidity: 0,
			MaximumAmountForValidity: 99999999,
			MinimumPaymentAmount:     0,
			MaximumPaymentAmount:     99999999,
			Percentage:               1,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      1,
			MaximumDiscountAmount:    1,
			ApplicableOn:             []string{"ALL"},
		},
	},
	"go_desi": []dto.PaymentConditions{
		{
			Type:                     constants.COD,
			MinimumAmountForValidity: 0,
			MaximumAmountForValidity: 99999999,
			MinimumPaymentAmount:     0,
			MaximumPaymentAmount:     0,
			Percentage:               0,
			Fixed:                    0,
			DiscountPercentage:       0,
			DiscountFixedAmount:      0,
			MaximumDiscountAmount:    0,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.FULLY_PAID,
			MinimumAmountForValidity: 0,
			MaximumAmountForValidity: 600,
			MinimumPaymentAmount:     0,
			MaximumPaymentAmount:     99999999,
			Percentage:               1,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      1,
			MaximumDiscountAmount:    1,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.FULLY_PAID,
			MinimumAmountForValidity: 601,
			MaximumAmountForValidity: 99999999,
			MinimumPaymentAmount:     0,
			MaximumPaymentAmount:     99999999,
			Percentage:               1,
			Fixed:                    -1,
			DiscountPercentage:       .02,
			DiscountFixedAmount:      -1,
			MaximumDiscountAmount:    99999999,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.PARTIALLY_PAID,
			MinimumAmountForValidity: 0,
			MaximumAmountForValidity: 600,
			MinimumPaymentAmount:     100,
			MaximumPaymentAmount:     2000,
			Percentage:               0.05,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      1,
			MaximumDiscountAmount:    1,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.PARTIALLY_PAID,
			MinimumAmountForValidity: 601,
			MaximumAmountForValidity: 1200,
			MinimumPaymentAmount:     100,
			MaximumPaymentAmount:     2000,
			Percentage:               0.05,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      10,
			MaximumDiscountAmount:    10,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.PARTIALLY_PAID,
			MinimumAmountForValidity: 1201,
			MaximumAmountForValidity: 2500,
			MinimumPaymentAmount:     100,
			MaximumPaymentAmount:     2000,
			Percentage:               0.05,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      30,
			MaximumDiscountAmount:    30,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.PARTIALLY_PAID,
			MinimumAmountForValidity: 2501,
			MaximumAmountForValidity: 99999999,
			MinimumPaymentAmount:     100,
			MaximumPaymentAmount:     2000,
			Percentage:               0.05,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      50,
			MaximumDiscountAmount:    50,
			ApplicableOn:             []string{"ALL"},
		},
	},
	"michis": {
		{
			Type:                     constants.COD,
			MinimumAmountForValidity: 99999999,
			MaximumAmountForValidity: 99999999,
			MinimumPaymentAmount:     0,
			MaximumPaymentAmount:     99999999,
			Percentage:               1,
			Fixed:                    -1,
			DiscountPercentage:       0,
			DiscountFixedAmount:      -1,
			MaximumDiscountAmount:    99999999,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.FULLY_PAID,
			MinimumAmountForValidity: 0,
			MaximumAmountForValidity: 600,
			MinimumPaymentAmount:     0,
			MaximumPaymentAmount:     99999999,
			Percentage:               1,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      1,
			MaximumDiscountAmount:    1,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.FULLY_PAID,
			MinimumAmountForValidity: 601,
			MaximumAmountForValidity: 99999999,
			MinimumPaymentAmount:     0,
			MaximumPaymentAmount:     99999999,
			Percentage:               1,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      1,
			MaximumDiscountAmount:    99999999,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.PARTIALLY_PAID,
			MinimumAmountForValidity: 0,
			MaximumAmountForValidity: 600,
			MinimumPaymentAmount:     100,
			MaximumPaymentAmount:     2000,
			Percentage:               0.05,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      1,
			MaximumDiscountAmount:    1,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.PARTIALLY_PAID,
			MinimumAmountForValidity: 601,
			MaximumAmountForValidity: 1200,
			MinimumPaymentAmount:     100,
			MaximumPaymentAmount:     2000,
			Percentage:               0.05,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      1,
			MaximumDiscountAmount:    1,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.PARTIALLY_PAID,
			MinimumAmountForValidity: 1201,
			MaximumAmountForValidity: 2500,
			MinimumPaymentAmount:     100,
			MaximumPaymentAmount:     2000,
			Percentage:               0.05,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      1,
			MaximumDiscountAmount:    1,
			ApplicableOn:             []string{"ALL"},
		},
		{
			Type:                     constants.PARTIALLY_PAID,
			MinimumAmountForValidity: 2501,
			MaximumAmountForValidity: 99999999,
			MinimumPaymentAmount:     100,
			MaximumPaymentAmount:     2000,
			Percentage:               0.05,
			Fixed:                    -1,
			DiscountPercentage:       -1,
			DiscountFixedAmount:      1,
			MaximumDiscountAmount:    1,
			ApplicableOn:             []string{"ALL"},
		},
	},
}

func DeepCloneMap[K comparable, V any](original map[K]V) map[K]V {
	// Convert the original map to JSON
	jsonData, err := json.Marshal(original)
	if err != nil {
		panic(err)
	}

	// Unmarshal into a new map
	var cloned map[K]V
	err = json.Unmarshal(jsonData, &cloned)
	if err != nil {
		panic(err)
	}

	return cloned
}
