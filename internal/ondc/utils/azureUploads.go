package utils

import (
	"context"
	"fmt"
	"kc/internal/ondc/azure"
	"kc/internal/ondc/config"
	"os"
	"strings"
	"time"

	"github.com/Azure/azure-storage-blob-go/azblob"
)

func UploadImageToBlob(ctx context.Context, localPath string, imageBytes []byte, blob string, contentType string, container string) (string, error) {
	var imgByt []byte
	var err error
	if localPath != "" {
		imgByt, err = os.ReadFile(localPath)
		if err != nil {
			return "", err
		}
	} else {
		imgByt = imageBytes
	}

	az := azure.AzureClient{
		Container: container,
		Blob:      blob,
		Client: &config.AzureConfig{
			AccountName: "d2rstorage2",
			AccountKey:  "****************************************************************************************",
			URL:         "https://d2rstorage2.blob.core.windows.net/",
		},
	}
	blobUrl := fmt.Sprintf(`https://d2rstorage2.blob.core.windows.net/%s/%s`, container, blob)
	err = az.UploadFileToBlob(ctx, &imgByt, "image/png")
	if err != nil {
		fmt.Println("falied to upload multimedia, ", err)
		return "", err
	}
	return blobUrl, nil
}

func UploadCSVToBlob(ctx context.Context, imageBytes []byte, blob string, contentType string, container string) (string, error) {
	az := azure.AzureClient{
		Container: container,
		Blob:      blob,
		Client: &config.AzureConfig{
			AccountName: "d2rstorage2",
			AccountKey:  "****************************************************************************************",
			URL:         "https://d2rstorage2.blob.core.windows.net/",
		},
	}
	blobUrl := fmt.Sprintf(`https://d2rstorage2.blob.core.windows.net/%s/%s`, container, blob)
	err := az.UploadFileToBlob(ctx, &imageBytes, "text/csv")
	if err != nil {
		fmt.Println("falied to upload multimedia, ", err)
		return "", err
	}
	return blobUrl, nil
}

func generateSASURL(blobURL string) (string, error) {
	// Create a SAS signature that's valid for 10 minutes
	endTime := time.Now().UTC().Add(10 * time.Minute)

	// Create credential for SAS token
	credential, err := azblob.NewSharedKeyCredential("d2rstorage2",
		"****************************************************************************************")
	if err != nil {
		return "", fmt.Errorf("failed to create credential: %w", err)
	}

	urlPath := blobURL
	pathParts := strings.Split(urlPath, "/")

	if len(pathParts) < 2 {
		return "", fmt.Errorf("invalid blob URL format: %s", urlPath)
	}
	containerName := pathParts[1]
	blobName := pathParts[len(pathParts)-1]

	// Generate the SAS query parameters
	sasQueryParams, err := azblob.BlobSASSignatureValues{
		Protocol:      azblob.SASProtocolHTTPS,
		ExpiryTime:    endTime,
		ContainerName: containerName,
		BlobName:      blobName,
		Permissions:   azblob.BlobSASPermissions{Read: true}.String(),
	}.NewSASQueryParameters(credential)

	if err != nil {
		return "", fmt.Errorf("failed to create SAS query parameters: %w", err)
	}

	// Add the SAS query parameters to the blob URL
	sasURL := fmt.Sprintf("%s?%s", blobURL, sasQueryParams.Encode())

	return sasURL, nil
}
