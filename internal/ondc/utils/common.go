package utils

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net/http"
	netUrl "net/url"
	"strings"
	"time"

	"kc/internal/ondc/models/dto"

	"github.com/dustin/go-humanize"
)

var SCREEN_DETAILS_TTL = 600 // 10 minutes
var SCREEN_DETAILS_CACHE = make(map[string]interface{})

// TimeAdjustConfig defines configurable bounds
type TimeAdjustConfig struct {
	LowerBoundHour *int // e.g. 9
	UpperBoundHour *int // e.g. 19
}

func CallExternalAPI(apiUrl string, method string, requestObject interface{}, params map[string]interface{}) ([]byte, *int, error) {
	if params != nil {
		queryParams := netUrl.Values{}
		for key, value := range params {
			queryParams.Add(key, fmt.Sprintf("%v", value))
		}
		apiUrl = apiUrl + "?" + queryParams.Encode()
	}

	var buf bytes.Buffer
	if requestObject != nil {
		err := json.NewEncoder(&buf).Encode(requestObject)
		if err != nil {
			return nil, nil, err
		}
	}

	client := &http.Client{}
	req, err := http.NewRequest(method, apiUrl, &buf)
	if err != nil {
		return nil, nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	res, err := client.Do(req)
	if err != nil {
		return nil, nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, nil, err
	}
	return body, &res.StatusCode, err
}

func CallExternalAPIAsync(apiUrl string, method string, requestObject interface{}, params map[string]interface{}) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("Recovered from panic in CallExternalAPIAsync: %v", r)
			}
		}()

		if params != nil {
			queryParams := netUrl.Values{}
			for key, value := range params {
				queryParams.Add(key, fmt.Sprintf("%v", value))
			}
			apiUrl = apiUrl + "?" + queryParams.Encode()
		}

		var buf bytes.Buffer
		if requestObject != nil {
			err := json.NewEncoder(&buf).Encode(requestObject)
			if err != nil {
				log.Printf("Failed to encode request object: %v", err)
				return
			}
		}

		client := &http.Client{}
		req, err := http.NewRequest(method, apiUrl, &buf)
		if err != nil {
			log.Printf("Failed to create request: %v", err)
			return
		}
		req.Header.Add("Content-Type", "application/json")

		res, err := client.Do(req)
		if err != nil {
			log.Printf("Error sending request: %v", err)
			return
		}
		defer res.Body.Close()

		body, err := io.ReadAll(res.Body)
		if err != nil {
			log.Printf("Error reading response body CallExternalAPIAsync: %v", err)
			return
		}

		log.Printf("CallExternalAPIAsync API call to %s returned status: %d, response: %s", apiUrl, res.StatusCode, string(body))
	}()
}

func Reverse[T any](s []T) {
    for i, j := 0, len(s)-1; i < j; i, j = i+1, j-1 {
        s[i], s[j] = s[j], s[i]
    }
}

func FormatIndianNumber(number int) string {
	formatted := humanize.Comma(int64(number))

	parts := strings.Split(formatted, ",")
	if len(parts) <= 3 {
		return formatted
	}

	indianFormatted := parts[0] + "," + strings.Join(parts[1:], "")
	return indianFormatted
}

func GenerateStrikethrough(value string) string {
	var sb strings.Builder
	for _, char := range value {
		sb.WriteRune(char)
		sb.WriteRune('\u0336') // Unicode for the strikethrough
	}
	return sb.String()
}

func GenerateBold(value string) string {
	var sb strings.Builder
	for _, char := range value {
		boldChar := toBold(char)
		sb.WriteRune(boldChar)
	}
	return sb.String()
}

func toBold(char rune) rune {
	switch {
	case char >= 'A' && char <= 'Z':
		return char - 'A' + '𝗔'
	case char >= 'a' && char <= 'z':
		return char - 'a' + '𝗮'
	case char >= '0' && char <= '9':
		return char - '0' + '𝟬'
	default:
		return char
	}
}

func CallGcpRedis(key string, operation string, value interface{}, expiry *int64) {
	if expiry == nil {
		expiry = new(int64)
		*expiry = 3600 * 24 * 30
	}
	redisRequest := dto.RedisInterfaceRequest{
		Data: dto.RedisInterfaceData{
			Key:       &key,
			Value:     value,
			Expiry:    expiry,
			Operation: &operation,
		},
	}

	_, statusCode, err := CallExternalAPI(GCP_REDIS_INTERFACE_API, "POST", redisRequest, nil)
	if err != nil {
		fmt.Println("Error in calling redis", err)
	}
	if statusCode != nil {
		fmt.Println("Response code from redis", *statusCode)
	}
}

func MakeTitleCase(s string) string {
	return strings.ToTitle(strings.ReplaceAll(s, "_", " "))
}

func GetKCOrderIDFromIntOrderID(orderID int64) string {
	return fmt.Sprintf("KC_%06d", orderID)
}

// CompressJSON compresses JSON data using gzip
func CompressJSON(jsonData []byte) ([]byte, error) {
	var buf bytes.Buffer
	gz := gzip.NewWriter(&buf)

	if _, err := gz.Write(jsonData); err != nil {
		return nil, err
	}

	if err := gz.Close(); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

// DecompressJSON decompresses gzipped data
func DecompressJSON(compressedData []byte) ([]byte, error) {
	buf := bytes.NewBuffer(compressedData)
	gz, err := gzip.NewReader(buf)
	if err != nil {
		return nil, err
	}
	defer gz.Close()

	return io.ReadAll(gz)
}

func AssignGroup(userId string) string {
	sum := 0
	for _, char := range userId {
		sum += int(char) // ASCII value of the character
	}
	if sum%2 == 0 {
		return "A"
	}
	return "B"
}


// adjustTime adjusts the time based on configurable or default bounds
func AdjustTime(triggeredAt time.Time, config *TimeAdjustConfig) time.Time {
	// Set defaults if not provided
	defaultLower := 9
	defaultUpper := 19
	lowerBound := defaultLower
	upperBound := defaultUpper

	if config != nil {
		if config.LowerBoundHour != nil {
			lowerBound = *config.LowerBoundHour
		}
		if config.UpperBoundHour != nil {
			upperBound = *config.UpperBoundHour
		}
	}

	hour := triggeredAt.Hour()

	// If within bounds, return as is
	if hour >= lowerBound && hour <= upperBound {
		return triggeredAt
	}

	// Random minute offset (0-60)
	rand.Seed(time.Now().UnixNano())
	randomMinutes := rand.Intn(61)

	location := triggeredAt.Location()

	// If time is before lower bound
	if hour < lowerBound {
		return time.Date(
			triggeredAt.Year(),
			triggeredAt.Month(),
			triggeredAt.Day(),
			lowerBound, 0, 0, 0,
			location,
		).Add(time.Duration(randomMinutes) * time.Minute)
	}

	// If time is after upper bound
	return time.Date(
		triggeredAt.Year(),
		triggeredAt.Month(),
		triggeredAt.Day()+1,
		lowerBound, 0, 0, 0,
		location,
	).Add(time.Duration(randomMinutes) * time.Minute)
}
