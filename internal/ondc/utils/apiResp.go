package utils

import (
	"fmt"
	"kc/internal/ondc/exceptions"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

type ApiResp struct {
	Status string      `json:"status"`
	Data   interface{} `json:"data,omitempty"`
}
type AckResponse struct {
	Message *MessageAck `json:"message,omitempty"`
	Error   *Error      `json:"error,omitempty"`
}

type Error struct {
	Type string `json:"type" validate:"oneof=CONTEXT-ERROR CORE-ERROR DOMAIN-ERROR POLICY-ERROR JSON-SCHEMA-ERROR"`

	// ONDC specific error code. For full list of error codes, refer to docs/drafts/Error Codes.md of this repo
	Code *string `json:"code" validate:"required"`

	// Path to json schema generating the error. Used only during json schema validation errors
	Path string `json:"path,omitempty"`

	// Human readable message describing the error
	Message string `json:"message,omitempty"`
}

// MessageAck is an inner message of AckResponse.
type MessageAck struct {
	Ack *Ack `json:"ack" validate:"required"`
}

// Ack - Describes the ACK response
type Ack struct {
	// Describe the status of the ACK response. If schema validation passes, status is ACK else it is NACK
	Status string `json:"status" validate:"oneof=ACK NACK"`
}

type ErrorResp struct {
	ErrorCode exceptions.ServerErrorCode `json:"error_code,omitempty"`
	Msg       string                     `json:"message,omitempty"`
	Reason    string                     `json:"reason,omitempty"`
}

func ConstructAPIResp(message string, data interface{}) ApiResp {
	if data == nil {
		fmt.Println()
		return ApiResp{Status: message}
	}
	return ApiResp{
		Status: message,
		Data:   data,
	}
}

func ConstructErrorAPIResp(err error) ApiResp {
	var payload ApiResp
	if serverErr, ok := err.(exceptions.ServerError); ok {
		payload = ApiResp{
			Status: "error",
			Data: ErrorResp{
				Msg:       serverErr.Msg,
				Reason:    serverErr.ActualErr.Error(),
				ErrorCode: serverErr.Code,
			},
		}
	} else {
		payload = ApiResp{
			Status: "error",
			Data: ErrorResp{
				Msg: err.Error(),
			},
		}
	}
	return payload
}

func RespondJSON(c *gin.Context, status int, payload interface{}) {
	c.JSON(status, payload)
}

func RespondText(c *gin.Context, status int, payload interface{}) {
	c.Header("Content-Type", "text/plain")
	c.String(status, fmt.Sprintf("%v", payload))
}

// RespondError makes the error response with payload as json format
func RespondError(c *gin.Context, err error, payload interface{}) {
	//send the error to track
	statusCode := http.StatusInternalServerError
	if serverErr, ok := err.(exceptions.ServerError); ok {
		statusCode = serverErr.HttpCode
	}
	RespondJSON(c, statusCode, payload)
}

func SendAck(c *gin.Context) {
	resp := &AckResponse{Message: &MessageAck{Ack: &Ack{Status: "ACK"}}}
	RespondJSON(c, http.StatusOK, resp)
}

func SendNack(c *gin.Context) {
	resp := &AckResponse{Message: &MessageAck{Ack: &Ack{Status: "NACK"}}}
	RespondJSON(c, http.StatusOK, resp)
}

func FormatValidationError(err validator.FieldError) string {
	field := strings.ToLower(err.Field())

	switch err.Tag() {
	case "required":
		return fmt.Sprintf("%s is required", field)
	case "min":
		return fmt.Sprintf("%s must be at least %s", field, err.Param())
	case "max":
		return fmt.Sprintf("%s must be at most %s", field, err.Param())
	case "gt":
		return fmt.Sprintf("%s must be greater than %s", field, err.Param())
	case "url":
		return fmt.Sprintf("%s must be a valid URL", field)
	case "datetime":
		return fmt.Sprintf("%s must be a valid date format (%s)", field, err.Param())
	default:
		return fmt.Sprintf("%s validation failed: %s", field, err.Tag())
	}
}
