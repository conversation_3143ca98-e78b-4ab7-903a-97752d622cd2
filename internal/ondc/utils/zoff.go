// this file is deprecated
package utils

import (
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/shared"
	"math"
	"strconv"
	"strings"
)

// GetOrderMass returns the mass in gram for the seller items send in the argument
func GetOrderMass(cartDetails []shared.SellerItems) int {
	totalMass := 0.0
	for _, j := range cartDetails {
		meta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(j.Meta, &meta)
		if err != nil {
			meta.PackSize = 0
			meta.Quantity = "0g"
		}
		packMass, err := getGramToInt(meta.Quantity)
		if err != nil {
			packMass = 0
		}
		totalMass += float64(packMass) * float64(meta.PackSize) * float64(j.Quantity)
	}
	fmt.Println("int(math.Ceil(totalMass / 1000)) = ", int(math.Ceil(totalMass/1000)))
	return int(math.Ceil(totalMass / 1000)) // converting gram to KG
}

// getGramToInt returns the 100g to 100 i.e string to int
func getGramToInt(s string) (int, error) {
	idx := strings.IndexFunc(s, func(r rune) bool {
		return r < '0' || r > '9'
	})
	if idx == -1 {
		idx = len(s)
	}
	numStr := s[:idx]
	num, err := strconv.Atoi(numStr)
	if err != nil {
		return 0, err
	}
	return num, nil
}
