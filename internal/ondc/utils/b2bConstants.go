package utils

import displaystatus "kc/internal/ondc/service/orderStatus/displayStatus"

const (
	LIMIT_DEFAULT     = 10
	OFFSET_DEFAULT    = 0
	RECO_DAYS_DEFAULT = 5
)

var (
	RECO_STATUS_DEFAULT  = []string{"PLACED", "CONFIRMED", "IN_TRANSIT"}
	RECO_STATUS_ALL      = []string{"ALL"}
	SELLER_DEFAULT       = []string{"zoff_foods"}
	ORDER_STATUS_DEFAULT = []string{"INITIATED"}
)

type Hunter struct {
	Name  string
	Email string
	Role  string
}

var (
	B2B_NDR_HUNTERS = []Hunter{
		{Name: "Jaspal Devra", Email: "<EMAIL>", Role: "USER"},
		{Name: "<PERSON><PERSON><PERSON>", Email: "<EMAIL>", Role: "USER"},
		{Name: "Sarfaraz Rahut", Email: "<EMAIL>", Role: "ADMIN"},
	}
	B2B_NDR_ADMIN_HUNTER = Hunter{
		Name: "Sarfaraz <PERSON>hu<PERSON>", Email: "<EMAIL>", Role: "ADMIN",
	}
	NDR_PENDING_STATUS = "PENDING"
)

var NDRRequiredColumns = []string{"Waybill", "Remarks", "Attempt count"}

const ORDERING_UPDATE_DEFAULT_DISTINCT_ID = "<EMAIL>"

const (
	// Source types
	SOURCE_CUSTOMER_ESCALATION = "customerEscalation"
	SOURCE_B2B_INTERNAL        = "B2B_INTERNAL"
	SOURCE_AUTOMATIONS         = "automations"
)

const (
	// B2B NDR Ticket Types
	NDR_OPEN_STATUS        = "open"
	NDR_CLOSED_STATUS      = "closed"
	NDR_IN_PROGRESS_STATUS = "in_progress"
)

var OrderCancelAuthorizedUsers = []string{
	"<EMAIL>",
	"<EMAIL>",
	"<EMAIL>",
	"<EMAIL>",
	"<EMAIL>",
	"<EMAIL>",
	"<EMAIL>",
}

var RestrictedSellers = []string{"zoff_foods", "nutraj", "mangalam"}

var RestrictedOrderEditStatuses = []string{displaystatus.NDR, displaystatus.DELIVERED, displaystatus.RETURNED, displaystatus.CANCELLED}
