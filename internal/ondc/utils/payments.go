package utils

import (
	"gorm.io/datatypes"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/service/orderStatus/constants"
	"math"
)

var PaymentMethodsMap = map[string]dto.WaysToPayDataV2{
	constants.COD: {
		Id:       3,
		Title:    "कैश ऑन डिलीवरी",
		ImageUrl: "https://d2rstorage2.blob.core.windows.net/widget/March/24/d7a40f88-53b1-4fe2-9470-93869e3173e8/1742809033982.webp",
		Text: dto.WaysToPayText{
			Text: "आप ऑनलाइन पेमेंट के साथ ज़्यादा बचत कर सकते है",
			HighlightText: []dto.Text{
				{
					Content: "आप ऑनलाइन पेमेंट के साथ ज़्यादा बचत कर सकते है",
					Style: dto.TextStyle{
						Color:      "#636363",
						FontWeight: "500",
					},
				},
			},
			Style: dto.TextStyle{
				Color:      "#636363",
				FontWeight: "500",
			},
		},
		ActiveColor: StrPtr("#6A21D0"),
		CTAText:     "ऑर्डर करें",
		//PaymentNudge: &dto.PaymentNudge{
		//	Heading:    "एडवांस पेमेंट करके",
		//	SubHeading: "₹20 बचाएं",
		//	ImageURL:   "https://d2rstorage2.blob.core.windows.net/widget/June/19/3e1b6933-d49b-460c-bc15-2b43a3676f9b/1750339187871.webp",
		//	Features:   []string{"✅  100% सुरक्षित", "💰 कैंसिलेशन पर पूरा पैसा वापस!"},
		//	PrimaryCTA: &dto.CTA{
		//		Text:          "फ़ायदा नहीं चाहिए",
		//		MixpanelEvent: StrPtr("Clicked on Close Advance Payment Bottomsheet"),
		//	},
		//	SecondaryCTA: &dto.SecondaryCTA{
		//		Text:          "एडवांस पेमेंट करें",
		//		MixpanelEvent: StrPtr("Clicked on Advance Payment in Bottomsheet"),
		//		PaymentId:     1,
		//	},
		//	Color: "#C7A4FF",
		//},
	},
	constants.PARTIALLY_PAID: {
		Id:       2,
		Title:    "एडवांस पेमेंट",
		ImageUrl: "https://d2rstorage2.blob.core.windows.net/widget/March/24/aab8fcc9-b835-4c80-a015-c693a63c30e2/1742808998750.webp",
		Text: dto.WaysToPayText{
			Text: "एडवांस पेमेंट क्यों जरूरी समझे?",
			HighlightText: []dto.Text{
				{
					Content: "एडवांस पेमेंट क्यों जरूरी",
					Style: dto.TextStyle{
						Color:      "#E25522",
						FontWeight: "500",
					},
				},
				{
					Content: "समझे?",
					Style: dto.TextStyle{
						Color:      "#3758D9",
						FontWeight: "700",
					},
				},
			},
			Nav: &shared.BackHandlerBottomSheet{
				Name:    "BackHandlerBottomSheet",
				NavType: "Redirect to Screen",
				Params: shared.Params{
					Data: shared.Data{
						ShowCloseButton: true,
						Widgets: shared.Widgets{
							Layout: []datatypes.JSON{
								datatypes.JSON([]byte(`{
											"MixPanelEventName": "Banner",
											"component_title": "Why pay????",
											"expiry_time": 1801731526972,
											"id": 3257,
											"type": 3,
											"updatedBy": "<EMAIL>",
											"versions": ">=6.4.1",
											"visibility": 1,
											"visible_from": 1738659526972,
											"widget_info": {
												"widget_name": "Why pay????"
											},
											"image_url": "https://d2rstorage2.blob.core.windows.net/widget/March/26/fa53fa95-f2f0-43db-b344-530440951f5f/1742972029570.webp"
										}`)),
							},
						},
					},
				},
			},
			Style: dto.TextStyle{
				Color:      "#E25522",
				FontWeight: "500",
			},
		},
		DiscountColors: &dto.DiscountColors{
			TextColor: "#2023FA",
			BgColor:   "#E6E6FF",
		},
		ActiveColor: StrPtr("#6A21D0"),
		CTAText:     "पेमेंट करें",
		BottomBarData: &dto.BottomBarData{
			Text:    "बाक़ी पेमेंट डिलीवरी के समय",
			BgColor: "#EBF0FF",
			Styles: &dto.TextStyle{
				Color: "#1E3A8A",
			},
			CTA: &dto.BottomBarCTA{
				ImageURL: "https://d2rstorage2.blob.core.windows.net/widget/June/19/61e4540e-0a3e-4a92-8422-9bc9810f6552/1750329917542.webp",
				Nav: &shared.BackHandlerBottomSheet{
					Name:    "BackHandlerBottomSheet",
					NavType: "Redirect to Screen",
					Params: shared.Params{
						Data: shared.Data{
							ShowCloseButton: true,
							Widgets: shared.Widgets{
								Layout: []datatypes.JSON{
									datatypes.JSON([]byte(`{
											"MixPanelEventName": "Banner",
											"component_title": "Why pay????",
											"expiry_time": 1801731526972,
											"id": 3257,
											"type": 3,
											"updatedBy": "<EMAIL>",
											"versions": ">=6.4.1",
											"visibility": 1,
											"visible_from": 1738659526972,
											"widget_info": {
												"widget_name": "Why pay????"
											},
											"image_url": "https://d2rstorage2.blob.core.windows.net/widget/March/26/fa53fa95-f2f0-43db-b344-530440951f5f/1742972029570.webp"
										}`)),
								},
							},
						},
					},
				},
				MixpanelEventName: "Clicked on Advance Payment Samjhe",
			},
		},
	},
	constants.FULLY_PAID: {
		Id:       1,
		Title:    "फुल पेमेंट",
		ImageUrl: "https://d2rstorage2.blob.core.windows.net/widget/March/26/2c2ca8e2-5935-4b8f-a5f5-19fc00b5e2a0/1742994008966.png",
		Text: dto.WaysToPayText{
			IconName: "Percentage",
			Text:     "फुल पेमेंट पर 2% एक्स्ट्रा डिस्काउंट",
			HighlightText: []dto.Text{
				{
					Content: "फुल पेमेंट पर 2% एक्स्ट्रा डिस्काउंट",
					Style: dto.TextStyle{
						Color:      "#01A501",
						FontWeight: "500",
					},
				},
			},
			Style: dto.TextStyle{
				Color:      "#01A501",
				FontWeight: "500",
			},
		},
		DiscountColors: &dto.DiscountColors{
			TextColor: "#2023FA",
			BgColor:   "#E6E6FF",
		},
		ActiveColor: StrPtr("#6A21D0"),
		CTAText:     "पेमेंट करें",
		BottomBarData: &dto.BottomBarData{
			Text:    "डिलीवरी के समय कोई पैसा नहीं देना होगा",
			BgColor: "#FFF1DF",
			Styles: &dto.TextStyle{
				Color: "#5F3400",
			},
		},
	},
}

var PaymentBannerURLForBillDetails = "https://d2rstorage2.blob.core.windows.net/widget/March/10/86f4b4be-fe8a-4db8-9a1e-78156b435a05/1741602634483.webp"

func Round(num float64) float64 {
	return math.Round(num*100) / 100 // Round to 2 decimal places
}
