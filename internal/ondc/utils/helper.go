package utils

import (
	"math"
	"reflect"
	"strconv"
)

func SafeString(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func SafeInt(i *int) int {
	if i == nil {
		return 0
	}
	return *i
}

func SafeBool(b *bool) bool {
	if b == nil {
		return false
	}
	return *b
}

func IsReallyNil(i interface{}) bool {
	if i == nil {
		return true
	}
	v := reflect.ValueOf(i)
	return (v.Kind() == reflect.Ptr || v.Kind() == reflect.Interface) && v.IsNil()
}

// Utility functions for accurate financial calculations

// roundToNDecimals rounds a float64 to n decimal places using banker's rounding
func roundToNDecimals(value float64, decimals int) float64 {
	precision := math.Pow(10, float64(decimals))
	return math.Round(value*precision) / precision
}

// roundToTwoDecimals rounds a float64 to two decimal places
// This is the standard for financial calculations
func RoundToTwoDecimals(value float64) float64 {
	return roundToNDecimals(value, 2)
}

func DefaultFloat64IfNil(value *float64, defaultValue float64) float64 {
	if value == nil {
		return defaultValue
	}
	return *value
}

func DefaultStringIfNil(value *string, defaultValue string) string {
	if value == nil {
		return defaultValue
	}
	return *value
}

func DefaultIntIfNil(value *int, defaultValue int) int {
	if value == nil {
		return defaultValue
	}
	return *value
}

func DefaultInt64IfNil(value *int64, defaultValue int64) int64 {
	if value == nil {
		return defaultValue
	}
	return *value
}

func DefaultBoolIfNil(value *bool, defaultValue bool) bool {
	if value == nil {
		return defaultValue
	}
	return *value
}

func includes(slice []string, item string) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

func StringSliceToUintSlice(strs []string) []uint {
	var result []uint
	for _, s := range strs {
		if v, err := strconv.ParseUint(s, 10, 64); err == nil {
			result = append(result, uint(v))
		}
	}
	return result
}
