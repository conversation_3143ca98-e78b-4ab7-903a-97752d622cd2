package utils

import (
	"encoding/json"
	"fmt"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/search"
	"reflect"
	"sort"
	"strings"
)

const MAX_PRODUCTS = 10
const (
	SEARCH_PRODUCTS_INDEX            = "kcbaazar_products"
	SEARCH_PRODUCTS_ENHANCED_INDEX   = "kcbaazar_products_enhanced"
	SEARCH_CATEGORIES_INDEX          = "kcbaazar_categories"
	SEARCH_CATEGORIES_ENHANCED_INDEX = "kcbaazar_categories_enhanced"
	SEARCH_PRODUCTS_OFFSET           = 0
	SEARCH_PRODUCTS_LIMIT            = 160
	SEARCH_DIVERSIFIED_SELLERS       = true // Get Diversified sellers results
	SEARCH_INNER_HITS_SIZE           = 8    // Get top 8 products per seller
	SEARCH_ENABLE_PHONETIC           = true // Enable phonetic search
	SEARCH_PRODUCTS_INDEX_WEIGHT     = 1
	SEARCH_CATEGORIES_INDEX_WEIGHT   = 2.5
)

type SearchProduct struct {
	ID              int64    `json:"id"`
	SizeVariantCode int64    `json:"size_variant_code"`
	NameLabel       string   `json:"name_label"`
	Name            string   `json:"name"`
	IsDefault       bool     `json:"is_default"`
	Seller          string   `json:"seller"`
	SellerName      string   `json:"seller_name"`
	Source          string   `json:"source"`
	Code            string   `json:"code"`
	HindiName       string   `json:"hindi_name"`
	Category        string   `json:"category"`
	Keywords        []string `json:"keywords"`
}

var SEARCH_PRODUCTS = make([]SearchProduct, 0)

var SEARCH_PRODUCTS_WEIGTHS map[string]float64 = map[string]float64{
	"keywords":    0.25,
	"code":        0.2,
	"name_label":  0.1,
	"name":        0.1,
	"hindi_name":  0.1,
	"category":    0.15,
	"seller":      0.04,
	"seller_name": 0.06,
}

var SEARCH_SELLER_WEIGTHS map[string]float64 = map[string]float64{}

var SEARCH_WIDGET_META map[string]interface{} = map[string]interface{}{
	"zoff_foods": map[string]interface{}{
		"heading":     "Zoff Foods",
		"description": "1200+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   814,
	},
	"go_desi": map[string]interface{}{
		"heading":     "Go Desi",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   815,
	},
	"panchvati": map[string]interface{}{
		"heading":     "Panchvati",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   816,
	},
	"hugs": map[string]interface{}{
		"heading":     "Hugs",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   817,
	},
	"mothers_kitchen": map[string]interface{}{
		"heading":     "Mother's Kitchen",
		"description": "100+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   818,
	},
	"apsara_tea": map[string]interface{}{
		"heading":     "Apsara Tea",
		"description": "100+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   819,
	},
	"mangalam": map[string]interface{}{
		"heading":     "Mangalam",
		"description": "100+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   886,
	},
	"nutraj": map[string]interface{}{
		"heading":     "Nutraj",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   887,
	},
	MICHIS: map[string]interface{}{
		"heading":     "Michis",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   888,
	},
	LOTS: map[string]interface{}{
		"heading":     "Lots",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   889,
	},
	CRAVITOS: map[string]interface{}{
		"heading":     "Cravitos",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   890,
	},
	MILDEN: map[string]interface{}{
		"heading":     "Milden",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   891,
	},
	SOOTHE: map[string]interface{}{
		"heading":     "Soothe",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   892,
	},
	BOLAS: map[string]interface{}{
		"heading":     "Bolas",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   892,
	},
	CHUK_DE: map[string]interface{}{
		"heading":     "Chuk De",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   892,
	},
	CANDYLAKE: map[string]interface{}{
		"heading":     "Candylake",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   892,
	},
	SUGANDH: map[string]interface{}{
		"heading":     "Sugandh",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   892,
	},
	SOMNATH: map[string]interface{}{
		"heading":     "Somnath",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   892,
	},
	CINTU: map[string]interface{}{
		"heading":     "Cintu",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   892,
	},
	PARIMAL: map[string]interface{}{
		"heading":     "Parimal",
		"description": "670+ किराना दुकानदारों ने ख़रीदा",
		"rating":      4.5,
		"widget_id":   892,
	},
}

func RemoveDuplicates(slice interface{}) interface{} {
	sliceVal := reflect.ValueOf(slice)

	if sliceVal.Kind() != reflect.Slice {
		panic("Input is not a slice")
	}

	seen := make(map[interface{}]bool)
	result := reflect.MakeSlice(sliceVal.Type(), 0, sliceVal.Len())

	for i := 0; i < sliceVal.Len(); i++ {
		val := sliceVal.Index(i).Interface()
		if !seen[val] {
			seen[val] = true
			result = reflect.Append(result, sliceVal.Index(i))
		}
	}
	return result.Interface()
}

func PreloadProductsForSearch(sqlRepo *sqlRepo.Repository) {
	type KiranaBazarProductsCategoriesKeywords struct {
		dao.KiranaBazarProductsAndCategories
		Keywords *json.RawMessage `json:"keywords"`
	}
	query := `SELECT kc.category, 
				kc.source, 
				kpk.keywords,
				kp.* FROM 
			kiranabazar_products kp 
			JOIN kiranabazar_categories kc
			ON kp.category_id = kc.id
			LEFT JOIN kiranabazar_products_keywords kpk ON kp.id = kpk.product_id
			WHERE kp.is_active = true AND is_oos = false AND is_default=true	`
	products := []KiranaBazarProductsCategoriesKeywords{}
	searchProducts := []SearchProduct{}
	sqlRepo.CustomQuery(&products, query)
	for _, product := range products {
		productMeta := shared.KiranaBazarProductMeta{}
		err := json.Unmarshal(product.Meta, &productMeta)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("not able to preload product for search 1 product ID: %d", product.ID))
			continue
		}

		keywords := []string{}
		if product.Keywords == nil {
			continue
		}
		err = json.Unmarshal(*product.Keywords, &keywords)
		if err != nil {
			slack.SendSlackMessage(fmt.Sprintf("not able to preload product for search 2 product ID: %d", product.ID))
			continue
		}

		seller, _ := brands.GetSellerBySource(product.Source)

		searchProduct := SearchProduct{
			ID:              product.ID,
			SizeVariantCode: product.SizeVariantCode,
			NameLabel:       strings.ToLower(product.NameLabel),
			IsDefault:       product.IsDefault,
			Seller:          seller,
			SellerName:      strings.ToLower(strings.ReplaceAll(seller, "_", " ")),
			Source:          product.Source,
			Code:            product.Code,
			HindiName:       productMeta.HindiName,
			Category:        product.Category,
			Name:            strings.ToLower(productMeta.HindiName),
			Keywords:        keywords,
		}
		searchProducts = append(searchProducts, searchProduct)
	}
	SEARCH_PRODUCTS = searchProducts
}

// SortItemsBySellerScore sorts widgets based on the final_score in SellerScores
func SortItemsBySellerOrder(sellerScores []search.SellerScore, items []dto.Widget) []dto.Widget {
	// Make a copy of items to avoid modifying the original
	result := make([]dto.Widget, len(items))
	copy(result, items)

	// Create a map of seller to their final score
	scoreMap := make(map[string]float64)
	for _, sellerScore := range sellerScores {
		scoreMap[sellerScore.Seller] = sellerScore.FinalScore
	}

	// Default score for sellers not in the list (lowest priority)
	defaultScore := -1.0

	// Sort using a stable sort to maintain relative order of equal elements
	sort.SliceStable(result, func(i, j int) bool {
		sellerI := extractSeller(result[i])
		sellerJ := extractSeller(result[j])

		// Get scores
		scoreI, okI := scoreMap[sellerI]
		scoreJ, okJ := scoreMap[sellerJ]

		if !okI {
			scoreI = defaultScore
		}
		if !okJ {
			scoreJ = defaultScore
		}

		// Sort by highest score first
		return scoreI > scoreJ
	})

	return result
}

func extractSeller(widgetItem dto.Widget) string {
	// Type assert to the concrete type
	if widget, ok := widgetItem.(dto.WidgetType39); ok {
		// First try to get seller from cta.nav.params
		if widget.Cta.Nav != nil && widget.Cta.Nav.Params != nil {
			if seller, ok := widget.Cta.Nav.Params["seller"].(string); ok {
				return seller
			}
		}

		// Then try to get from data[0].seller
		if len(widget.Data) > 0 && widget.Data[0].Seller != "" {
			return widget.Data[0].Seller
		}
	}

	return ""
}

func SortProductsByScore(products []dao.KiranaBazarProductsAndCategories, response search.ProductsResponse) []dao.KiranaBazarProductsAndCategories {
	// Create a map to easily look up scores by product ID
	productScores := make(map[int64]float64)
	for _, product := range response.Products {
		productScores[int64(product.ProductID)] = product.Score
	}

	// Sort the products slice based on scores (highest first)
	sort.Slice(products, func(i, j int) bool {
		scoreI := productScores[products[i].ID]
		scoreJ := productScores[products[j].ID]
		return scoreI > scoreJ // Sort in descending order (highest score first)
	})

	return products
}
