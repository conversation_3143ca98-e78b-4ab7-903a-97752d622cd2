package utils

import (
	"encoding/json"
	"fmt"
	"io"
	"kc/internal/ondc/models/dto"
	"net/http"
	"strings"
	"time"
)

const COUNTRY_CODE = "IND"

var CityCodeMapping = make(map[string]string)

// GeoLocationConfig holds the configuration for the getLocationDetailsFromLatLong function.
type GeoLocationConfig struct {
	URL         string
	Method      string
	ContentType string
	MaxRetries  int
	RetryDelay  time.Duration
}

// DefaultGeoLocationConfig returns the default configuration for the getLocationDetailsFromLatLong function.
func DefaultGeoLocationConfig() GeoLocationConfig {
	return GeoLocationConfig{
		URL:         "https://darkarts.retailpulse.ai/api/v1/geoLocation/getDetails",
		Method:      "POST",
		ContentType: "application/json",
		MaxRetries:  3,
		RetryDelay:  1 * time.Second,
	}
}

// CityCode struct represents the structure of each city code entry
type CityCode struct {
	City string `json:"city"`
	Code string `json:"code"`
}

// getCodeForLocation retrieves the city code for the given city name.
func getCodeForLocation(geoLocationResponse *dto.GeoLocationResponse) {
	geoLocationResponse.Data.CountryCode = COUNTRY_CODE
	if geoLocationResponse.Data.StdCode != "" {
		geoLocationResponse.Data.CityCode = fmt.Sprintf("std:%s", geoLocationResponse.Data.StdCode)
	} else {
		geoLocationResponse.Data.CityCode = "std:080"
	}
}

// GetLocationDetailsFromLatLong retrieves the geographical location details based on latitude and longitude.
func GetLocationDetailsFromLatLong(lat, long float64) (*dto.GeoLocationResponse, error) {
	config := DefaultGeoLocationConfig()
	payload := strings.NewReader(fmt.Sprintf(`{"latitude": %v, "longitude": %v}`, lat, long))

	client := &http.Client{}
	req, err := http.NewRequest(config.Method, config.URL, payload)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", config.ContentType)

	for i := 0; i < config.MaxRetries; i++ {
		res, err := client.Do(req)
		if err != nil {
			time.Sleep(config.RetryDelay)
			continue
		}
		defer res.Body.Close()

		body, err := io.ReadAll(res.Body)
		if err != nil {
			return nil, err
		}

		if res.StatusCode == http.StatusOK {
			geoLocationResponse := &dto.GeoLocationResponse{}
			err = json.Unmarshal(body, geoLocationResponse)
			if err != nil {
				return nil, err
			}
			getCodeForLocation(geoLocationResponse)
			return geoLocationResponse, nil
		}
		time.Sleep(config.RetryDelay)
	}

	return nil, fmt.Errorf("failed to get location details after %d retries", config.MaxRetries)
}

func GetUserGeolocationData(userId string) (*dto.UserGeoLocationData, error) {
	params := map[string]interface{}{
		"user_id": userId,
	}
	resp, status, err := CallExternalAPI(USER_GEOLOCATION_API, "GET", nil, params)
	if err != nil {
		return nil, err
	}
	if status == nil || *status != http.StatusOK {
		return nil, fmt.Errorf("error in fetching user geolocation data: %s", string(resp))
	}

	userGeoLocationResponse := &dto.UserGeoLocationResponse{}
	err = json.Unmarshal(resp, userGeoLocationResponse)
	if err != nil {
		return nil, err
	}
	return &userGeoLocationResponse.Data, nil
}