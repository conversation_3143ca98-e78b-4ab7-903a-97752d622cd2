package utils

import (
	"kc/internal/ondc/models/dto"
	"kc/internal/ondc/models/shared"
	"time"
)

const (
	CANCEL_TIME_LIMIT           = 24
	MAX_ORDER_FOR_AUTOMATED_IVR = 4000.0
)

const GCP_REDIS_INTERFACE_API = "https://asia-south1-op-d2r.cloudfunctions.net/redis_interface_python"
const WIDGET_RESOLVER_API = "https://asia-south1-op-d2r.cloudfunctions.net/widgetsResolver"
const USER_GEOLOCATION_API = "https://darkarts.retailpulse.ai/api/v1/geoLocation/user"
const PROGRESS_WIDGET_RESOLVER_API = "https://asia-south1-op-d2r.cloudfunctions.net/get_ordering_progress_widget"

var TARGET_SCHEME_CAMPAIGN_EXPIRY_DATE = time.Date(2025, time.June, 23, 23, 59, 59, 59, time.Local)

var THAILAND_SCHEME_CAMPAIGN_EXPIRY_DATE = time.Date(2025, time.September, 15, 23, 59, 59, 59, time.Local)

var FLOATING_WIDGET_TYPES = map[float64]bool{
	15: true,
	38: true,
}

var RSB_MARGIN_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/May/15/03559fe1-f5a6-4a7e-af47-d471247a9114/1747314762885.webp",
}

var SELLER_FAQ_BANNER = shared.BannerImageUrls{
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/March/5/a26ae707-8eae-4a3c-8537-327593e5c38a/1741153617731.webp",
	MixpanelEventName: "Seller FAQ Banner Clicked",
	Nav: &shared.Nav{
		Name:    "GenericScreen",
		NavType: "Redirect to Screen",
		Params: map[string]interface{}{
			"screenName": "FAQ_All",
		},
	},
}

var RSB_APSARA_OFFER_BANNER_FOR_NEW_TO_RSB = shared.BannerImageUrls{
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/May/20/b6b815da-41e1-4dd9-87dc-76f04399136c/1747747144586.webp",
	MixpanelEventName: "RSB APSARA OFFER FOR NEW TO RSB",
	Nav: &shared.Nav{
		Name:    "BackHandlerBottomSheet",
		NavType: "Redirect to Screen",
		Params: map[string]interface{}{
			"data": map[string]interface{}{
				"widgets": map[string]interface{}{
					"layout": []map[string]interface{}{
						{
							"MixPanelEventName": "RSB APSARA NEW TO order Bottomsheet",
							"bottom":            "68.5%",
							"container_color":   "black",
							"created_by":        "<EMAIL>",
							"end_date":          1747863180000,
							"expiry":            1747863180000,
							"expiry_time":       1747863180000,
							"format":            "hmns",
							"id":                1028,
							"image_url":         "https://d2rstorage2.blob.core.windows.net/widget/May/21/44907962-1cb9-4721-beb9-2eff575e710d/1747823048918.webp",
							"is_active":         1,
							"margin":            false,
							"navObj": map[string]interface{}{
								"name":     "OrderingModule",
								"nav_type": "Redirect to Screen",
								"params": map[string]interface{}{
									"params": map[string]interface{}{
										"seller": "rsb_super_stockist",
										"source": "third_party_rsb_super_stockist",
									},
									"screen": "Products",
								},
							},
							"sql_id":           5002,
							"time_color":       "white",
							"time_label_color": "white",
							"type":             26,
							"updatedBy":        "<EMAIL>",
							"updated_at":       "2025-03-29T07:15:23.000Z",
							"updated_by":       "<EMAIL>",
							"visibility_count": 1,
							"widget_info": map[string]interface{}{
								"widget_name": "RSB APSARA NEW TO order Bottomsheet",
							},
						},
					},
				},
				"show_close_button": true,
			},
		},
	},
}

var TESTING_BANNER_TYPE_48 = shared.BannerImageUrls{
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/June/17/7faec8ca-66bd-4921-80d2-ff5e955a5a66/1750158845766.webp",
	MixpanelEventName: "TESTING_BANNER_TYPE_48",
	Nav: &shared.Nav{
		Name:    "BackHandlerBottomSheet",
		NavType: "Redirect to Screen",
		Params: map[string]interface{}{
			"data": map[string]interface{}{
				"widgets": map[string]interface{}{
					"layout": []map[string]interface{}{
						{
							"id":                589,
							"MixPanelEventName": "Update Michis Playstore Banner",
							"expiry_time":       int64(1738743360000),
							"type":              48,
							"updatedBy":         "<EMAIL>",
							"visibility":        1,
							"visible_from":      int64(1735892181530),
							"widget_info": map[string]interface{}{
								"widget_name": "Products Rating Bottomsheet",
							},
							"header": map[string]interface{}{
								"primary_text":   "सभी प्रॉडक्ट्स का फीडबैक दें",
								"secondary_text": "अपना फीडबैक दीजिये",
							},
							"rating_meta": map[string]interface{}{
								"options": []interface{}{
									map[string]interface{}{
										"option_id": 1,
										"icon":      "🤩",
										"label":     "बिक रहा हैं",
										"value":     1,
									},
									map[string]interface{}{
										"option_id": 2,
										"icon":      "🙂",
										"label":     "धीरें बिक रहा हैं ",
										"value":     3,
									},
									map[string]interface{}{
										"option_id": 3,
										"icon":      "☹️",
										"label":     "नहीं बिक रहा हैं",
										"value":     5,
									},
								},
								"placeholder":  "और जानकारी दें...",
								"success_text": "फ़ीडबैक दर्ज किया गया ✅",
							},
							"cta": map[string]interface{}{
								"text": "जमा करें",
							},
							"items": []interface{}{
								map[string]interface{}{
									"seller":       "hugs",
									"product_id":   1,
									"image_url":    "https://d2rstorage2.blob.core.windows.net/widget/June/16/01c5d6eb-5771-4107-bdaa-42d9748eeae9/1750067940894.webp",
									"product_name": "हल्दी पाउडर",
									"description":  "Zoff का हल्दी पाउडर (15 ग्राम) आपने 1 जून को ख़रीदा था।",
								},
								map[string]interface{}{
									"seller":       "hugs",
									"product_id":   2,
									"image_url":    "https://d2rstorage2.blob.core.windows.net/widget/June/16/01c5d6eb-5771-4107-bdaa-42d9748eeae9/1750067940894.webp",
									"product_name": "हल्दी पाउडर",
									"description":  "Zoff का हल्दी पाउडर (15 ग्राम) आपने 1 जून को ख़रीदा था।",
								},
								map[string]interface{}{
									"seller":       "hugs",
									"product_id":   3,
									"image_url":    "https://d2rstorage2.blob.core.windows.net/widget/June/16/01c5d6eb-5771-4107-bdaa-42d9748eeae9/1750067940894.webp",
									"product_name": "हल्दी पाउडर",
									"description":  "Zoff का हल्दी पाउडर (15 ग्राम) आपने 1 जून को ख़रीदा था।",
								},
							},
						},
					},
				},
				"show_close_button": true,
			},
		},
	},
}

var RSB_APSARA_OFFER_BANNER_FOR_RSB = shared.BannerImageUrls{
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/May/20/dbcd316c-065c-47ef-843d-ef73d7d710bd/1747748814205.webp",
	MixpanelEventName: "RSB APSARA order Bottomsheet",
	Nav: &shared.Nav{
		Name:    "BackHandlerBottomSheet",
		NavType: "Redirect to Screen",
		Params: map[string]interface{}{
			"data": map[string]interface{}{
				"widgets": map[string]interface{}{
					"layout": []map[string]interface{}{
						{
							"MixPanelEventName": "RSB APSARA order Bottomsheet",
							"bottom":            "68.5%",
							"container_color":   "black",
							"created_by":        "<EMAIL>",
							"end_date":          1747863180000,
							"expiry":            1747863180000,
							"expiry_time":       1747863180000,
							"format":            "hmns",
							"id":                1028,
							"image_url":         "https://d2rstorage2.blob.core.windows.net/widget/May/21/1f453619-2463-4aa9-828b-cdc21d1abdaf/1747823006882.webp",
							"is_active":         1,
							"margin":            false,
							"navObj": map[string]interface{}{
								"name":     "OrderingModule",
								"nav_type": "Redirect to Screen",
								"params": map[string]interface{}{
									"params": map[string]interface{}{
										"seller": "rsb_super_stockist",
										"source": "third_party_rsb_super_stockist",
									},
									"screen": "Products",
								},
							},
							"sql_id":           5002,
							"time_color":       "white",
							"time_label_color": "white",
							"type":             26,
							"updatedBy":        "<EMAIL>",
							"updated_at":       "2025-03-29T07:15:23.000Z",
							"updated_by":       "<EMAIL>",
							"visibility_count": 1,
							"widget_info": map[string]interface{}{
								"widget_name": "RSB APSARA order Bottomsheet",
							},
						},
					},
				},
				"show_close_button": true,
			},
		},
	},
}

var RSB_LOOT_OFFER_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/May/23/3b17735c-102b-4003-a5bb-1f821e92bf4a/1748010656888.webp",
	Nav: &shared.Nav{
		Name:    "OrderingModule",
		NavType: "Redirect to Screen",
		Params: map[string]interface{}{
			"screen": "Products",
			"params": map[string]interface{}{
				"seller":        "rsb_super_stockist",
				"source":        "third_party_rsb_super_stockist",
				"categoryIndex": "1",
			},
		},
	},
}

// var RSB_FREE_BAG_OFFER = shared.BannerImageUrls{
// 	MixpanelEventName: "Carousal in Brand Store",
// 	Url:               "https://d2rstorage2.blob.core.windows.net/widget/May/22/23bf70b7-b6d2-44fa-8452-179aa7c38496/1747911640891.webp",
// }

var RSB_B_FLASH_SALE_BANNER = shared.BannerImageUrls{
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/May/14/4dd6c585-f74d-4a38-8f6b-b192609096a6/1747239362377.webp",
	MixpanelEventName: "RSB B Flash Sale Banner Clicked",
}

var RSB_NON_PUNJEE_USERS_B1 = shared.BannerImageUrls{
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/May/16/21f50c37-6f3f-4c3f-b2f7-fa458b768b8b/1747378902196.webp",
	MixpanelEventName: "RSB_Non_Punjee_Users Banner 1",
}

var RSB_NON_PUNJEE_USERS_B2 = shared.BannerImageUrls{
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/May/16/3cc02b13-6be1-42a2-9086-439b1205c440/1747378989759.webp",
	MixpanelEventName: "RSB_Non_Punjee_Users Banner 2",
}

var CHUK_DE_OFFER_BANNER = shared.BannerImageUrls{
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/June/12/67c631c8-f81b-433c-b51b-f8a2aa25c4a6/1749716290989.webp",
	MixpanelEventName: "Carousal in Brand Store",
}

var SOOTHE_OFFER_BANNER = shared.BannerImageUrls{
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/June/27/d7b61607-07ed-40f9-b43e-8bfcdfaac9cf/1751005499388.webp",
	MixpanelEventName: "Carousal in Brand Store",
}

var APSARA_FREE_TEA_BANNER = shared.BannerImageUrls{
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/June/19/8d4d49c6-b784-42e3-855a-c7b6c3872e0b/1750341691768.webp",
	MixpanelEventName: "Carousal in Brand Store",
}

var ZOFF_HALDI_OFFER_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/July/9/f9b86c99-d50e-488b-a574-ec2ef73cefd5/1752058900600.webp",
	Nav: &shared.Nav{
		Name:    "BackHandlerBottomSheet",
		NavType: "Redirect to Screen",
		Params: map[string]interface{}{
			"data": map[string]interface{}{
				"widgets": map[string]interface{}{
					"layout": []map[string]interface{}{
						{
							"MixPanelEventName": "ZOFF Free Haldi Timer Newsfeed",
							"bottom":            "50%",
							"container_color":   "white",
							"created_by":        "<EMAIL>",
							"end_date":          1752344999000,
							"expiry":            1752344999000,
							"expiry_time":       1752344999000,
							"format":            "hmns",
							"id":                3222,
							"image_url":         "https://d2rstorage2.blob.core.windows.net/widget/July/9/5964de80-60c8-4021-97c7-951bc0b89854/1752058940068.webp",
							"is_active":         1,
							"margin":            false,
							"navObj": map[string]interface{}{
								"name":     "OrderingModule",
								"nav_type": "Redirect to Screen",
								"params": map[string]interface{}{
									"params": map[string]interface{}{
										"seller": "zoff_foods",
										"source": "third_party_zoff_foods",
									},
									"screen": "Products",
									"seller": "zoff_foods",
									"source": "third_party_zoff_foods",
								},
							},
							"sql_id":           4544,
							"time_color":       "red",
							"time_label_color": "white",
							"type":             26,
							"updatedBy":        "<EMAIL>",
							"updated_at":       "2025-02-24T10:26:52.000Z",
							"updated_by":       "<EMAIL>",
							"visibility_count": 1,
							"widget_info": map[string]interface{}{
								"widget_name": "ZOFF Free Haldi Timer Newsfeed",
							},
						},
					},
				},
				"show_close_button": true,
			},
		},
	},
}

var RSB_UNDEFINED_PRODUCTS_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/May/27/4fc077e4-e868-4b16-a3a0-a78d0a3f5eaf/1748335977904.webp",
	Nav: &shared.Nav{
		Name:    "OrderingModule",
		NavType: "Redirect to Screen",
		Params: map[string]interface{}{
			"screen": "Products",
			"params": map[string]interface{}{
				"seller": "rsb_super_stockist",
				"source": "third_party_rsb_super_stockist",
			},
		},
	},
}

var GO_DESI_OFFER_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/April/22/c789829f-289e-44ab-830a-44ba0fdcf21a/1745317591841.webp",
	Nav: &shared.Nav{
		Name:    "BackHandlerBottomSheet",
		NavType: "Redirect to Screen",
		Params: map[string]interface{}{
			"data": map[string]interface{}{
				"widgets": map[string]interface{}{
					"layout": []map[string]interface{}{
						{
							"MixPanelEventName": "Banner",
							"component_title":   "ZOFF Offers Bottomsheet",
							"created_by":        "<EMAIL>",
							"expiry":            1773394380000,
							"expiry_time":       1773394380000,
							"id":                989,
							"image_urls": []string{
								"https://d2rstorage2.blob.core.windows.net/widget/April/16/8137ec5a-a76a-4c71-a065-fb6b0a39f4ab/1744790790147.webp",
							},
							"image_url":        "https://d2rstorage2.blob.core.windows.net/widget/April/16/8137ec5a-a76a-4c71-a065-fb6b0a39f4ab/1744790790147.webp",
							"isStateWise":      true,
							"is_active":        1,
							"sql_id":           4838,
							"type":             3,
							"updatedBy":        "<EMAIL>",
							"updated_at":       "2025-03-18T09:34:05.000Z",
							"updated_by":       "<EMAIL>",
							"visibility":       1,
							"visibility_count": 0,
							"visible_from":     1742290396832,
							"widget_info": map[string]interface{}{
								"widget_name": "ZOFF Offers Bottomsheet",
							},
						},
					},
				},
				"show_close_button": true,
			},
		},
	},
}

var APSARA_OFFER_BOTTOMSHEET_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/May/20/b6b815da-41e1-4dd9-87dc-76f04399136c/1747747144586.webp",
	Nav: &shared.Nav{
		Name:    "BackHandlerBottomSheet",
		NavType: "Redirect to Screen",
		Params: map[string]interface{}{
			"data": map[string]interface{}{
				"widgets": map[string]interface{}{
					"layout": []map[string]interface{}{
						{
							"MixPanelEventName": "APSARA OFFER TO order Bottomsheet",
							"bottom":            "68.5%",
							"container_color":   "black",
							"created_by":        "<EMAIL>",
							"end_date":          1748064699000,
							"expiry":            1748064699000,
							"expiry_time":       1748064699000,
							"format":            "hmns",
							"id":                1028,
							"image_url":         "https://d2rstorage2.blob.core.windows.net/widget/May/21/44907962-1cb9-4721-beb9-2eff575e710d/1747823048918.webp",
							"is_active":         1,
							"margin":            false,
							"navObj": map[string]interface{}{
								"name":     "OrderingModule",
								"nav_type": "Redirect to Screen",
								"params": map[string]interface{}{
									"params": map[string]interface{}{
										"seller": "apsara_tea",
										"source": "third_party_apsara_tea",
									},
									"screen": "Products",
								},
							},
							"sql_id":           5002,
							"time_color":       "white",
							"time_label_color": "white",
							"type":             26,
							"updatedBy":        "<EMAIL>",
							"updated_at":       "2025-03-29T07:15:23.000Z",
							"updated_by":       "<EMAIL>",
							"visibility_count": 1,
							"widget_info": map[string]interface{}{
								"widget_name": "APSARA OFFER TO order Bottomsheet",
							},
						},
					},
				},
				"show_close_button": true,
			},
		},
	},
}

var RSB_LOOT_OFFER_BOTTOM_SHEET = &shared.Nav{
	Name:    "BackHandlerBottomSheet",
	NavType: "Redirect to Screen",
	Params: map[string]interface{}{
		"data": map[string]interface{}{
			"show_close_button": true,
			"widgets": map[string]interface{}{
				"layout": []map[string]interface{}{
					{
						"CTA": map[string]interface{}{
							"nav": map[string]interface{}{
								"name":     "OrderingModule",
								"nav_type": "Redirect to Screen",
								"params": map[string]interface{}{
									"params": map[string]interface{}{
										"seller":        "rsb_super_stockist",
										"source":        "third_party_rsb_super_stockist",
										"categoryIndex": "1",
									},
									"screen": "Products",
								},
							},
						},
						"MixPanelEventName": "Banner",
						"component_title":   "RSB LOOT OFFER BANNER",
						"expiry_time":       1746023184001,
						"id":                1210,
						"image_url":         "https://d2rstorage2.blob.core.windows.net/widget/May/23/4955e0ce-9f17-4541-b2c5-89009e74c743/1748015115384.webp",
						"type":              3,
						"updatedBy":         "<EMAIL>",
						"versions":          ">=6.3.0",
						"visibility":        1,
						"visible_from":      1741789584001,
						"widget_info": map[string]interface{}{
							"widget_name": "RSB LOOT OFFER BANNER",
						},
					},
				},
			},
		},
	},
}

var MILDEN_FREE_CANDY_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/April/29/3728b28f-ab36-484f-9b41-dc09b06a7c2c/1745935169967.webp",
	Nav: &shared.Nav{
		Name:    "BackHandlerBottomSheet",
		NavType: "Redirect to Screen",
		Params: map[string]interface{}{
			"data": map[string]interface{}{
				"widgets": map[string]interface{}{
					"layout": []map[string]interface{}{
						{
							"MixPanelEventName": "Free Milden Campaign Timer Bottomsheet",
							"bottom":            "55.5%",
							"container_color":   "black",
							"created_by":        "<EMAIL>",
							"end_date":          1746016200000,
							"expiry":            1746016200000,
							"expiry_time":       1746016200000,
							"format":            "hmns",
							"id":                1028,
							"image_url":         "https://d2rstorage2.blob.core.windows.net/widget/April/29/fa5b3511-0f98-47f1-bac6-4910104f2473/1745935276984.webp",
							"is_active":         1,
							"margin":            false,
							"navObj": map[string]interface{}{
								"name":     "OrderingModule",
								"nav_type": "Redirect to Screen",
								"params": map[string]interface{}{
									"params": map[string]interface{}{
										"seller": "milden",
										"source": "third_party_milden",
									},
									"screen": "Products",
								},
							},
							"sql_id":           5002,
							"time_color":       "white",
							"time_label_color": "black",
							"type":             26,
							"updatedBy":        "<EMAIL>",
							"updated_at":       "2025-03-29T07:15:23.000Z",
							"updated_by":       "<EMAIL>",
							"visibility_count": 1,
							"widget_info": map[string]interface{}{
								"widget_name": "Free Milden Campaign Timer Bottomsheet",
							},
						},
					},
				},
				"show_close_button": true,
			},
		},
	},
}

var SOOTHE_24hr_OFFER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/April/29/d5835318-35f4-44e5-94b8-5f30a69034b5/1745936186271.webp",
	Nav: &shared.Nav{
		Name:    "BackHandlerBottomSheet",
		NavType: "Redirect to Screen",
		Params: map[string]interface{}{
			"data": map[string]interface{}{
				"widgets": map[string]interface{}{
					"layout": []map[string]interface{}{
						{
							"MixPanelEventName": "Free Soothe Campaign Timer Bottomsheet",
							"bottom":            "64.5%",
							"container_color":   "pink",
							"created_by":        "<EMAIL>",
							"end_date":          1746016200000,
							"expiry":            1746016200000,
							"expiry_time":       1746016200000,
							"format":            "hmns",
							"id":                1028,
							"image_url":         "https://d2rstorage2.blob.core.windows.net/widget/April/29/9b10229d-968e-421f-bcd4-855aed4e637e/1745936942221.webp",
							"is_active":         1,
							"margin":            false,
							"navObj": map[string]interface{}{
								"name":     "OrderingModule",
								"nav_type": "Redirect to Screen",
								"params": map[string]interface{}{
									"params": map[string]interface{}{
										"seller": "soothe",
										"source": "third_party_soothe",
									},
									"screen": "Products",
								},
							},
							"sql_id":           5002,
							"time_color":       "black",
							"time_label_color": "black",
							"type":             26,
							"updatedBy":        "<EMAIL>",
							"updated_at":       "2025-03-29T07:15:23.000Z",
							"updated_by":       "<EMAIL>",
							"visibility_count": 1,
							"widget_info": map[string]interface{}{
								"widget_name": "Free Soothe Campaign Timer Bottomsheet",
							},
						},
					},
				},
				"show_close_button": true,
			},
		},
	},
}

var ZOFF_OFFER_BANNER_1 = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/April/28/bbe9fc90-1cd2-4d94-b786-5a5742b3e134/1745842869218.webp",
}

var ZOFF_OFFER_BANNER_2 = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/April/28/ad34233e-1546-4971-ab2d-f95cfbbfa208/1745844246211.webp",
}

var ZOFF_FREE_BAG_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/April/23/50190977-daf5-456f-a4b9-d8c495ca838a/1745414659294.webp",
}

var ZOFF_MASALE_NEVER_ORDERED_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/June/13/d837d1a3-0314-4b1e-a9d7-1f068e41df2e/1749809702054.webp",
}

var ZOFF_MASALE_FREE_LADI_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/June/13/4922e82f-3085-4c57-8529-ece174bcf9bc/1749813068657.webp",
}

var ZOFF_THAILAND_TRIP_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/June/17/585242ad-a5ec-4c58-b1e4-726765fa0c9d/1750164302737.webp",
}

var ZOFF_FOODS_NEVER_ORDERED_SPOON_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/July/14/9a3c7b6a-3f5c-4ff2-aa83-085579554bce/1752503442696.webp",
}

var ZOFF_FOODS_ALREADY_ORDERED_SPOON_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/July/14/152921a0-45b1-495f-9d47-503313d59748/1752503467622.webp",
}

var MILDEN_RUPEE_1_CANDY_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/June/23/409f7ec3-e881-49f7-847d-e475112582a3/1750693936252.webp",
}

var APSARA_TEA_NEVER_ORDERED_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/June/13/3204d1d3-3146-4a30-9a32-3326e2b3fdbe/1749812634336.webp",
}

var RSB_FREE_CROCKERY_SET = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/July/9/826f7fd9-339d-4c2b-9964-1b2d53a91e5c/1752061151163.webp",
}

var RSB_FREE_BAG_BANNER = shared.BannerImageUrls{
	MixpanelEventName: "Carousal in Brand Store",
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/July/15/f2c07db1-6442-4e93-a206-7794a8d9b0d9/1752573706495.webp",
}

var UPDATE_APK_BANNER = shared.BannerImageUrls{
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/April/8/66f02f68-8edb-4777-af0d-4672721dc862/1744118292355.webp",
	MixpanelEventName: "Update Playstore Banner Clicked",
	Nav: &shared.Nav{
		Name:    "ExternalLink",
		NavType: "Redirect to External Link",
		Params: map[string]interface{}{
			"uri": "https://play.google.com/store/apps/details?id=club.kirana&hl=en_IN",
		},
	},
}

var RSB_NO_ORDER_BANNER = shared.BannerImageUrls{
	Url:               "https://d2rstorage2.blob.core.windows.net/widget/June/10/c60864d0-008c-44eb-81fb-b0d441dd5054/1749564477171.webp",
	MixpanelEventName: "RSB No order placed carousel banner",
}

var RSB_STOCKISTS_WIDGETS = []int64{1112, 1111, 1113, 1053, 1059, 1060, 1057}
var RSB_STOCKIST_USER_COHORT = "RSB Geography Cohort"

const (
	COUPON_ADDED    = "COUPON_ADDED"
	COUPON_REDEEMED = "COUPON_REDEEMED"
)

const (
	ADDRESS_STORE       = "STORE"
	ADDRESS_HOME        = "HOME"
	ADDRESS_STORE_HINDI = "दुकान"
	ADDRESS_HOME_HINDI  = "घर"
)

var ADDRESS_TAG map[string]string = map[string]string{
	ADDRESS_STORE_HINDI: ADDRESS_STORE,
	ADDRESS_HOME_HINDI:  ADDRESS_HOME,
}

// 819 - zoff -> global
// 820 - go desi -> global
// 821 - panchvati -> global
// 831 - hugs
// 841 - mothers kitchen
var STATE_WIDGETS_MAP = map[string][]float64{
	"Maharashtra":    {809, 832, 819, 820, 821, 841, 710},
	"Uttar Pradesh":  {809, 832, 819, 831, 820, 821, 710},
	"Rajasthan":      {809, 832, 819, 831, 820, 821, 841, 710},
	"Delhi":          {809, 832, 819, 831, 820, 821, 710},
	"Haryana":        {809, 832, 819, 831, 820, 821, 710},
	"Punjab":         {809, 832, 819, 831, 820, 821, 710},
	"Madhya Pradesh": {809, 832, 819, 820, 821, 841, 710},
	"Chandigarh":     {809, 832, 819, 831, 820, 821, 710},
}

const MAX_SIZES_TO_SHOW = 2

var SEARCH_GLOBAL_SUGGESTION_TEXTS = []string{
	"‘मसाले’ खोजे",
	"‘चॉकलेट’ खोजे",
	"‘पापड़’ खोजे",
	"‘मेहँदी’ खोजे",
}

var META_SCREEN_CONFIG = map[string]interface{}{
	"gradient_colors":   []string{"#ffffff", "#ffffff"},
	"gradient_location": []int{1, 1},
	"start": map[string]interface{}{
		"x": 0, "y": 0,
	},
	"end": map[string]interface{}{
		"x": 1, "y": 1,
	},
	// "bg_image": "https://d2rstorage2.blob.core.windows.net/widget/December/3/fd574bce-3609-424a-ae5d-93da0a090fad/1733231521590.webp",
}

var NON_IVR_USERS = map[string]bool{"0ANaEAmS8KQHH2mPuJDTBBOfGI32": true, "0XMf0vUPqWXZzbOzqOqMK3wObQh2": true, "0ZeLwxHfJHMdeITjyHEHRf30zgR2": true, "0fjNUefZC8U10hyPI0mw3EIysc42": true, "0m0DsZIzYYeu2J8lA1RV07wwEz12": true, "0mZSp1VtnfexH6Ic96so8mBSVIi2": true, "0teVcvFwO4TVpMMba9UPOOZsXtE2": true, "0uCJ7GDTwuRJZpnOdm3LXSio8K52": true, "1CFf8ugZsFc8ue38q8ibvpiDsrH2": true, "1EwjJp31c4MJb9a91O07mZyiyr52": true, "1Kb8nhh1hzdz0hGZ7pRUykWjdDl2": true, "1TEg8poCk9Th6cjufJEgTpqXD8i2": true, "1UjWRw3OVyW1IetsVYT5LoPQ2CU2": true, "1bZI743ueVZX0qxxWK13bB5rHgH2": true, "1hsVLWEKXtXE2YPEeoaKiERb8qy2": true, "1muaippGA4Tf7ho9Wi4JfccuR7C3": true, "1nZDL2LJVeTnM8yrXeJgYrPYIHA3": true, "1sljtsn7axWVNjF0yfIIHToF5af1": true, "29w5w8TwLohFBv9v1HgPNoxKoke2": true, "2KiTtmSKwuO7WHzyxme8ybGASNW2": true, "2NFUeQ5KbbcIRll5d5u2U3OpM233": true, "2aV3Y1cXFSTd52ouOXHKGFKzCoI3": true, "2bQMmNiPz8VNaLshWhyM0JgrTAn2": true, "2oOZ40tMSdVi2HzICyfoFfMIjm33": true, "2umFQCakiKYqcMVdcg95lXhVqzC3": true, "36pZfdr3CvXGFe1Im8tmz8SShL32": true, "3H97kPth72OPZP4YepPnSuerng93": true, "3LYbJHgfY6QH8DpsYzjHfYsnLtx2": true, "3aCshQz5XTfW3zfuPQDcfOOqYWS2": true, "3bHp1WlcK2YmiMyqmTwbnIg2FhI2": true, "3epEHRXPDkVV77PA8aGPikYNV4v1": true, "3rJ8YWIuSDWRJIbOffC5eL6wZPn1": true, "3sI8MA2d3uQqQlJMOjn6yK29s9Y2": true, "4CGZRyCKRYfXHF7149hvijrXjsU2": true, "4PVczefushYGpoarDlRqOZ7kqK52": true, "4jpytsZJ9LYg6QSpmP1HICBwKl32": true, "4pxVBU35R3NMHg0KZ7NF2HIfDpG3": true, "4wMVZEddbGPSfp421l7oBGjdlA12": true, "50hNPiAJEIhpFw5Fwn7pHvq80zJ3": true, "59cD9wm1flZaQaFxa7cJ7eFyZpi2": true, "5F04OEaQ7iW7VWVDymLilrU0Z7w1": true, "5qEvpQLp9PNyfRYUwQOxTSb6EX32": true, "5rWMqaiwSvYSctdyd8qQX0P154f1": true, "62WSpI7ARGXiFkME9HFddlubBsF2": true, "67kjq8QwTIgoU0gov0PMq0S6RjZ2": true, "6cUIHGulwrZzwObIE4wpEeRUIfD2": true, "6cfT5XJvisPWP3QD6jZjwieDCHo2": true, "6cmfLl5Q1ZZNrQeKPknXVs7YuC33": true, "6kLexBpquWMAWURJfxYs0M1xl0o2": true, "6mb23U6yliPDHA9f7mKk1sREaGD3": true, "6oVUUsTYDrOmpevqGl1Xh9VqMKQ2": true, "6tPWAden2UcLX8nKhlo2t2JQw8R2": true, "788KgYMUXWVxLLBIsht0ynSARzo2": true, "7GTHamHoHDSZTC5N55akFOIkHiq2": true, "7WjoIiwJQMTGvS9eo1vvBsn2bSJ2": true, "7cwdpklUJhhmGZ9V2duNM41ZvgO2": true, "7pvNWdPjfigjNHwsybbeMDLbxDA3": true, "84vklUTeGPccKTmzUp30eZ7JnIP2": true, "86zFLpmnmEOOgZ0aWZRMh2c0H482": true, "8CVd88T02HYdhoCPCJqMJQ6azm32": true, "8Cp4F84SKyRd2rpYCb3Ch9bHDfe2": true, "8U7hNSBmYzdkUFU9q8j8OX9jN9f1": true, "8y2AL5oEAgTcxUWwzi3oPp868lf1": true, "970hPAZ3ttZo4sXsouEejdBnxqG3": true, "9Yp6e5heI1OKEkORLC07JjIiJiB2": true, "A1fRrjEkoocHjw2cwanKRVvfpDz2": true, "A1shL8w0rpgfAGuds3XGS1JjjRB3": true, "A5jjqagWYLTdPpBOhrqFzTHZJOo1": true, "B0mATdY7UBYYlyzLdt5oXCqtAtW2": true, "B4BP35KYKCPUut0RjHz0ZhJbZ7w2": true, "B4fWEKiWkkXHmHuJ7V87T9AHlGt1": true, "B82EI363maakFoIF2Lu9zb4g15n2": true, "Bky3dF6ujEcLhFEZxF1KwINDUas2": true, "BuLXg0lQoWe2I1mhw5IpeTDiy4B3": true, "C4Vxdhg2f9Uah92L6uSyTTqHLX42": true, "CAyBsRePRXQqdy0iI26FvSIpthX2": true, "COwZcXuuRqZavE4TdKAukSewkrs1": true, "CRc0gVw0eaQP7vOcd1LJBB4FJln1": true, "CvqETvoxr1PyXTqYNwtDwIqPYQS2": true, "D1aJ0ZcJikMnUWsgbjWSzKuG6022": true, "DCpWKFJA9KffvIgsVpW8lSeAsgD2": true, "DhIEW8XgKWhfjbrtxZPpluQjKLi2": true, "DjC1N0VK6rctBTVleWTMCDYUTuu1": true, "EN7niUgAGrWmkRT44GPwxNex3Si2": true, "Ee5OAT2TUIQ5Us4jagMzYO2x49s2": true, "EfZrL4wkUoP0QSHEF2VsuMLMPE83": true, "ElINbapptBSQ7aLZO2z8wzShWJ43": true, "EoMoznn6eMgIy5NqvN2T1IiNHeP2": true, "EpSmgjZt2PNT6PzG2I3NPoVRYxI3": true, "Es3V0A1JZbZ9MIlHucwnnxvgPDG2": true, "F9mO7kaMPicuylLQ2J1wnSkoXQ92": true, "FChO8pVtsVWF7oTje6WXyhUpF492": true, "FS5KgZ1KHdUPTCr0qRL8obDk5Xz1": true, "FXIEc7VGokhLCwfFAehQUxqx2TN2": true, "FdZXdA9SCUXwC5QCuwv3ZhVERz73": true, "Fea2qlaL5PWUJWRuRTyPpYEqRHR2": true, "FxLxSO4Od3M3fmHXDUonz7laObJ3": true, "G3qIIVAGM9Uwb4e57qbhhZv9JRk1": true, "G6YFcMdtr9Xx3z6WnOIx670iQuo2": true, "G7aK7iAQImesceYicLrEVsjzrRI3": true, "GFBtm8MjUIbx38r0OgRsM5XgqoX2": true, "GQioDaJRYfW9sZut8yRjibX2evE3": true, "H3GMQXN143Snf53psTAoDRUX3pJ2": true, "HGLWCERoz1Q00xLECEq6ED64cNx1": true, "HJSiQXQeTBbePlfSgxF9iIniEK33": true, "HSJjjkzsgOcfNHTTJbIuvw9wXGh1": true, "HTGYnFxYOkTNK7zLHOevIFTcf7m2": true, "HwihKEDMTeNg9HIaew94htVN2M52": true, "I2An3UeztZRCAFl5k9xEwobG74X2": true, "I5eholekCdRsuHZCUu9E0VIK05e2": true, "I8R9OXF6jRMipOJj6Fz55egOJrr1": true, "IU6ZKcl13bXGWklvoTTxK8tcGnk2": true, "IY3DDcFBuURcerV8U5hASAkhfHh1": true, "IjpnlTJXYsUwDbNSJwB3IXeCUDu2": true, "Il5cVpVQrKWjTpjsCDfuTGo8vBk2": true, "IyUQnRkD3PbEVAUzVFsPF7pAtcN2": true, "IzSfniqBl9Zlb97V8BGcf17Hynk2": true, "J3BydMFMlYTf0F7bbEaMLu0eXyp1": true, "J6chI9ro9qXbN8n5EIjuFlG00493": true, "J7T9Y25plQPyHUh1aq8SDD05n123": true, "JA9Ko3u8idVgZhbyLALWwK8p6So1": true, "JYjkUwgAwFTONe1PwRU2JojvA3x1": true, "JydQKzcDYDawZ602y9USRolrnIu2": true, "Jzu2ZL2o7bXa1teOSsVGCugfPQh1": true, "K1vDLCbGnWNsrjSwZ5u9xSP2Ham1": true, "K5OwhgUp8OYRGyRc3u8nsVsebTx2": true, "KIsR1j4U5tanUQulRr8BVQEm16O2": true, "KLTyu4cJf9QaXCgKe4r9amwXMjj2": true, "KQ2Q3kIBiwYjfq55pKyHWieojPX2": true, "KlMw1A3weVfGD3GcFxZnJLWWk2h1": true, "KomyJQXzpEXkPFx6X0XhQZWXzKf1": true, "KsmYBwmTd8ZV1I4V0kSz5IiNhHu1": true, "LFgu0CMG4bZXxpjW5oHlX4zEtFf2": true, "LVQKMvddaET8JIyrwnC8jSKZ7EA2": true, "LWQfmrTHyGXwERqXTryQRbLXpeD2": true, "LkX4FZsivAd9ChnQkziqxo51Kfv2": true, "LteQsY0uMCZMLxGNQVxXuVGjDTp1": true, "LyO0jPemkPay11o1FuxY3KnogLK2": true, "MHgUgcFRy3YSX7sisQczGcA5dZP2": true, "N40oLhJ1C0fFIaDK1GQTqmiZcfd2": true, "NCr8RBnBoCfDB6GXfY6Cx5ztW8H3": true, "NEK0YT2JdNgWOOtjnLtFgnFRyrf2": true, "NTW1l7rDCEW1X0IhHIjM6awt3mD2": true, "NXuHHzV6G6M8owravKZEIpuCA4R2": true, "NeX1UTK24reopSCkZ2Et86ZqNbh2": true, "NmSb5ZlojMNkKDJK3Fc6a3iC5aZ2": true, "NuTVjRxNRQZSou88RsDsgEhT5Rm1": true, "O2kz59vXAvT9la75s54E1Kq62PD2": true, "O9m0GYJ4w4MToVDCEqS7OHHzIXs1": true, "OFHZfMZT7KTkb0ppbHwOyrvjLJ92": true, "OIItp4VLY1M88mL58ryRtohYItu1": true, "OMhCaUvmBqdG6W4qtbJcKRr7OgX2": true, "OMuo3lo6d2UuWJU21RqUyTYKBfh2": true, "OV4wqzWykDaelRFxAvL8D9wpmvr1": true, "Oi6AhL5k7UMi2kv9lUVLO8efywA3": true, "P007gF3v8tRCqEu0cgphrmYL6RJ3": true, "PBks0UNq55bKKLdh6ozTQiaKO3F3": true, "PY6GS3guB8UU5y4Qz67TpJSHprY2": true, "Q8uejPSrBVUVI1eSAcpmeZ8AAam1": true, "QUFMxj6kvCNaA8U1HdQEpH3O4ef2": true, "QcMiYfImORcc3lcLKfJCJnVqYKZ2": true, "QdTmwpfNY3VyjM9USWMXUjlhcDx1": true, "QfssiMrXqxfHxJyGprNdKR1UiON2": true, "RA0DUIisCZWvTjy0efWMWWoVG1a2": true, "RRv2SOg7sfVshlNu7elrpsqHJcb2": true, "SLZdf3S0ZfV3CZFs4M3N6RVqFRc2": true, "SY3kSFOSRpNEpDmSfUoGegagMv92": true, "SdjWb5jeCfTeHe9lDuK9Dkc3cJ52": true, "ShN6aRgBbygYH82Zwu2uY9AQOSY2": true, "Svrjj9JrfoYN6pcd5GipKQMcx9D2": true, "T0klZZvewFWFG8SqS1aT5ljkMR63": true, "TGyG0ZWcz2N5VNhkpdumbCXUCpj1": true, "TJ5bpZ4wENeOaaQXsbC5qhw7BqN2": true, "TZaiSPpQDUbyXaLduzQrNUZkszq2": true, "TiPjDp8qYShzq5fpzbSQtKyPaVV2": true, "TrbySyg129ao6pRRoKN5Dvzlm4q2": true, "Tv0X6wQ42nhoWDQ1fnYPKu3AAuz1": true, "UCZzuSMA3sVIseKCquqoeAn5E322": true, "UUcCfoUHeUdvfQ9nkISLbXIYFn72": true, "UVH82cn0NYOW5m4kVQew0LHqkYH2": true, "UcobhV30v5dAzoC7SJwrA7DRxNs1": true, "UrsN1wPxNWdwVWbDz9R5nKxI9lx2": true, "V6UwH4DghLdooFRFSNqz7EPrga52": true, "V8lFgBhVd5SKfjRnkhcOmBivl5r2": true, "V9JIldYqgObEluaXJQkOHe41Mj52": true, "VXMIPxyHFnVS6JUByhdD9Z9lg6k1": true, "VXWSsMgdy1WFHot7rBacX8sVZQ92": true, "VglxIkRcPbdQWyRpQxAXI42TRdy2": true, "VnR2309vrQN1wdkN6Rvz69JaB8p2": true, "VrvHOlDeLkVYkZ125nwcPYCxBux2": true, "VzjWek3DXUW3fdVN7XqFouSEuwh2": true, "WC7XRSmh2NX0NGvvPhhqNLd2ls22": true, "WCUnJKUfp3QLZDrFkGx5Ms2agfH3": true, "WDWzYWlLa9Q02z6oMPs8x8L72mW2": true, "WKTXnUqcmVRiRosPOFfzpUED1G73": true, "WONuHD2vtKhS1EPN8Vr21ONVB5m1": true, "WdyNssaw0ZVv4Dkl7KIgQaZOyP12": true, "WtSQ2BuUKhR11MV7Pzxj2sjLhV62": true, "XCNbd0gw65YQb7DuObsRTemePh73": true, "XGp1LVTaVZXW6SCwXprRgDrrDIk1": true, "XNxX5g6lo5RdbwzSw1U5vlBJCmI2": true, "XcQUFsk1YucSFYb8R3FbQUyXiaC2": true, "XjlQCn1jdJe6f5RKO58TQ06Xsp73": true, "Xn5fHRaSrhSvBKX7045JAClcnGr2": true, "XwRrdUqtl8XCvJ9TYl4a2dLZlBx2": true, "Y3YqeSsqhEMJVKWhMFeQlP3i9113": true, "YGwINou2LhbEjYhhqU5m9vJ1uNy2": true, "YJwgdt5Vi5QgjxPONocaIU97V0f2": true, "YMwR0wI4bibVgCvxnvIUAjb1vmP2": true, "YP1gtWn42ZczSZkOiZolb7XMGzg1": true, "YRkhMNtBTfdVMlL6bSwZ6FToNrk2": true, "YWhbgNUyb2MUl4FnYvtDhWbAw2n2": true, "YbPGAe3P1KS014iHcI0JHNy3Ewd2": true, "YdSQoirY1LhnG0PxPztJbjpFPJJ3": true, "Yw7sIKMLc0fb3inArI9x9Kk8EMF2": true, "Z99b8wsxm7Sta9JXzXUEQjBmkIu1": true, "ZBu8xEWUMtOMCBseoxbZVml2yA93": true, "ZFjgiJNjeKczZMI69zx7nhSfXLL2": true, "ZPufpPspUufng2dO1TDhpxCfFZV2": true, "ZQq5RvXFpBOIYyCmxV0jFLttiGz2": true, "ZT80SR8IDeSxo2vBsfiby6bP7pW2": true, "ZYJDI0vTAVOa9jsonvXjxCdWWqg2": true, "ZwMK0PQTHiYjsgmyHmsJixZLRgD2": true, "Zwmpcj7yB3bzzwUmSeM02pyV7Sr1": true, "a8cirC2o1zYHwJd71BUJ6TEGlpg2": true, "aZGegatPxzaIdxoffqGGWxHbihJ3": true, "aZHJ1wTYz4QZs4HX0hAEQT6YTq33": true, "ap219KeHZMfKhzzgTcwPXz0a0jk2": true, "auadbwGOS1VtG2nSoSMgmOzGv2r2": true, "ayIFC56ifZgWSS1YSd9pGTXOpmw2": true, "b2jOXBz3TIb6HTmhBAAbUlPQ3zJ2": true, "bFq72MrpKMMP3mBYOmJOzKLpa7e2": true, "bbNeN5y6FJVHNiRwdZ4CiY0k68t1": true, "boCyeRpf6AhHg093Zghj3ohVrZH3": true, "brUGH3dTdNTBwyLgZkI0Oo3GhU63": true, "cISlp9kzwPPNcr5Zwksz0JrJM4A2": true, "cVERpG4pGEP0JXiESMmkvxhZrWA3": true, "cbX4xTF6Pca9YLQIctw3mlsWXs42": true, "cdtUNtPgWxeItFCwIdWA0TY9HJi1": true, "ciaPfGWKMdMlEJhip3QA2wFUoSI3": true, "ciiX056XCZeSbodpETmWzugIhu53": true, "cx7fcbunyYO6Eh9mJeoU7kT7nh83": true, "cy8w8d0tNDXgSrO2VadWEtEz9Iw1": true, "cyGwYwtbK4bMYYBICHGx2CC8mlE2": true, "dGQTQkd6HhbQU1hia05INPxWSAj1": true, "dHXLSo6UbWXNnfo5wmCQAY6s7A72": true, "dcimirsfvVOIMBrJjS2tsObid4V2": true, "deBXPSHIvnavx5Ntfbvivdn6zCg2": true, "dzjkbYatNQXUIzenQR23ywkVrMO2": true, "e7cKw6HPMqcTsjitROvEfPEYWvx2": true, "eSP7VGQ0VxMSApcaCZWNE3DgNwC3": true, "eWwK8onI9Oav6QLc2Lfh5P32G8Z2": true, "eaNfkH3mP1VDXpcFU17OmHNopVM2": true, "ejfwg7P6ORZ3LwMN8eF1kTM8k5D2": true, "emgxQYvvGSQspXmHrunImjFzojA3": true, "ew7Z8vI9SkYLbvvkwNtiTzaxi8j1": true, "eyCt8SB1ombkVEIjEK2swOE40S93": true, "fV5CDkSXq7Nt2G7FB0nKit71dAf2": true, "fYsbBUdIbrM9906nu1ddNU5kSyP2": true, "fgcvpBtzZRM71g1Rmde2twMJr4h1": true, "fgfetQIKBRMIe8yO6MzAGiTQZ3t2": true, "g2buKlTfvpgapHA00mDoCdJ2kOR2": true, "g3IpKXRpqsZTzftIEXynB3AAJtx1": true, "g7QguW4SbtUrcnYP8u3i1h5JpOS2": true, "gDS9Qd27DxTqrbtA9Jjm0k1ldCe2": true, "gEEWv80BDBb6QOzFA4atdDw5nZx2": true, "gESw8VAJfebxwZK2cVCZwcJvIwG3": true, "gRHSaiBqhHhKmKzHsPeJgBIwkd62": true, "gcTZMfA4piRDfGxy85hJrj2GgT23": true, "gtbf1MdswWhi9RQ4YT1YnYULHkq1": true, "h2jXul0tyPOcvDsZcNgB48QwSNn1": true, "h5Cz4icNbgN18BYGc4HcUwlbNZ12": true, "hDOBdfYgeHayr3NucLhwLU2zGMI2": true, "hDOmLmo7YneBVjyDb68l6fTbMME3": true, "hJPAIV66wEYPHI304v6m2VNhZlm1": true, "hRs0anoW2yZtdXCF0cSPnQFJMMH2": true, "hTX8T4oOwwMVtTZHLEtuqi28Vhz1": true, "htDwUKLAPRelVSd1FjMsxHYpPSk1": true, "hyv8M911zwZ1GtqfjmUy3UdqfX73": true, "i2Aw7mRJL2bg6EzUF2jBclX4S1l2": true, "iDU5869S4DUXL4S34XzrfVwPMye2": true, "iPI8jbVkIhNqYbrsvZmXydcalO82": true, "iZpLSi9tKLdTKozD5q11bJyv2T02": true, "iaK7vOqdr9dFKqNrIJVrFL4ykhq1": true, "ivkzNZ0AQTOM6AlWHCNzPXCeUSi1": true, "j121GlqS9uhW8kxKZoVqgD2pud63": true, "j9evYvBZevgYtSEFgi67erDtqXI3": true, "jG0ZNkz79IhcE9Ab0jH5u5Vyt8m2": true, "jP2bry05lbTvOUh1rivTqADG6Fm1": true, "jqCe8xBzHPRt8qBUPUpkRu6HLjk1": true, "jz18zpqHNHPfPwguqilCxXENyzE2": true, "k2i8As2ncqgNM2aeGeyl7Quhcxk2": true, "k84IgHD1aaRY3dRw7CWfs4to6yk1": true, "kIBnr4NHa4ZKwH5teeP92rPIrl63": true, "kTJs4f9H5DQ4XqIWHQx28KRrFOF3": true, "kccHymwSurSEOoOTb6n1nlGYECz2": true, "kfqUDGJQ9ZUT77kYmxbv3HIC2q03": true, "kiaqmZfuc9YCqSm73nsvtvJbVAh2": true, "kofAL67eMNg5RkICXrUUTWUj65E3": true, "kv3z79Vqq5RJimteIFMDV3K27TQ2": true, "l0WYzV9DWnghPUDNaYTDRFQWtaS2": true, "l9OUk7dXymMU2nXFl2x21DaqlpV2": true, "lAnTRGsJM9cLmT30OKYeOs2bJf03": true, "lGNH36KMosX2HnjMHEef9j5lPj72": true, "lIdrqsZWhQXX3miqAE4gwScIdkS2": true, "lKpyYRXjJ9c3DjWHOh5V2Ar7Hs83": true, "lLjW7LI9m7WvuUmQgSb4Ceg365s2": true, "leh082WAcWaiIldVdvtkTE35Cd02": true, "lo7UWMma3hReZBsUeTDu7LSHzUg1": true, "lpnnHd2A2sSxXHIzUiSHz2g7e152": true, "lqTmxyNHOTV0GXwbhoQr4S8ITHs1": true, "m1RMeMuRgaT4DOnnOgh7tTRd5ic2": true, "m44nDdACXNYSlssdelE6obRgX9i2": true, "m5A9QxdSypgaxPRiaYXUBOWuj9G2": true, "m5JNqGaIqmRkH2U2ptrdIzzbq6r1": true, "m5mgm9eXbhcSlBu9EmTvAme0L7w1": true, "m8PhkA8SgfSWGimWxLYSze2kuq92": true, "mB4vqkaXu8YaoZDf7BslWusSPEp1": true, "mOWywm2u1xST7lYViGKWJNKQi0H2": true, "mSkYcvN4ZYMgv248Re5JBoBwArx1": true, "n1Ny3LZXsgh8D6ftWv89sv3tkwq2": true, "n3PosqLpDNewWEkvdAAJWUNYSz13": true, "n6BNcw59KvQnivjkOxKxfzwTTZ82": true, "nEl5VE3qZYRQhuFchvqPWiCtHSu1": true, "nFvuHC4FHOOx5AsZDJlQ069Ez0i2": true, "nHvcOpF7dvhr8mYJLJXiHoSGlsm1": true, "nLBX7uQMklVlPBx6RzQdkfZuujG3": true, "nNyBXVASSzRBHNsEjkHP8ZLfvGi2": true, "nP6uCqjzwYWrel04BckrUS5C5JD2": true, "nRIVxx2XIYSYTiEEj54m4XI7qJV2": true, "nZxbkmDeDIZ9tuLubBpulvan6Mu2": true, "nx2yntvEJUStnqWUl9HMDuatgv92": true, "oD2BdK85Yback3IsBy8YmVZxBAI2": true, "ovTE9I0dPZbY3vLrpEXdKzNbuT42": true, "owhbHNHZF1fUScrPZ6YQhwojLwx1": true, "p2FgyAx6jjM7jQu8DZwZIRjdgQ23": true, "p4xBvbP96dQivvRrRbmc8CB3SM02": true, "p9Tgc8tGS2WkTX2BMlG4tNyiKKw2": true, "pjs20xw4cRdOkkKmTY6dpMpAlQo2": true, "pkxq8PwnS4SRh84cRbDBIc35pKf2": true, "pnWtZOQwSkNaLL0q7YjYX8IZWkk2": true, "qAdfwlUiBggnt76MN9rwf04mk9S2": true, "qCdQZzyeEagA6YjDUjU2oFTbkaf2": true, "qoVxRNvK7AfOZc7yFDLBfut7rQp1": true, "rOG6XmYZCPfdq1bbPt02nPkdcX22": true, "rsdwmp5vN0NO8rClOxaV0JYWEnB3": true, "rvGzcyJMoNZA92gU4j82rXRFO2v2": true, "rvujXefnTBfoPofyv6XbtcA6lX13": true, "rwaBMpV23bf8Vem08SHLr6GW0ah2": true, "s433l3JeWMMz7q0qdPIxmDPQsXI3": true, "s8iIZPCyNtezQ84OxcgqgsubY1D3": true, "sQ1mheI81ZaXzI50oABVe5ANbNq2": true, "sWmFqmu4R3PhIUiTJlwwpMQ5Ng52": true, "scb8n2PhVFgIrTt7bf9NCGgnbFC3": true, "sdS65IpxdqbIDgUjYJtGaZUTQIF3": true, "spyubUZvXBWlI4HhCGjelW3PPmD2": true, "sqdOL8wEGvPK057ONT3jplxbFfJ3": true, "t63WLQuXebYi8Mci9hxu7SQhjlt1": true, "tDTkLvuFBwVvru0hOYdNc455hN83": true, "tLjqEqmzrSSVzVZeS0VWubnVpZL2": true, "taECDjYmCjZKSyDDgmeVOAKRVVG2": true, "tbrunAs22Bfov8MCYJCeZRDExGA2": true, "tc9wQYQS2rMTR5naVMT9yVw1UFA2": true, "tcIgqobkobadkCfBQOWftlpwwT62": true, "u40CtqA91WRDMqpWzxWNmHE9i4x1": true, "uQfrSFi5wkWcbS9lT1zuTnesiTE3": true, "uZmHzNCmMxgJJxw8QK86r6szrFD3": true, "ud8zZuyaplQ0Tzb72zVQPAzRK383": true, "untB0WsniZbcS6CkhtLrjkVK0DZ2": true, "upb91P3KDPNKnEPQec8IEN6suYo1": true, "uq9MaOaPz3TGQFfZenmiRaeXFVE2": true, "uqOpL8XOfnPncUtPwQscF95Nw102": true, "v4ijYfHpWobn5N2zSbqizoK2jAD2": true, "v7Z0AdGYHUgaVkhRo0NqQF1IONL2": true, "vMUicAxJAgPZmw4cPBTYxfAhuqf2": true, "vOYkKHyBnFZZvKNUTorN4wasCwY2": true, "vROerXGczaYDBfngP3LtNlqkY6i1": true, "vanE8Per1HWKuufZM64o3MJao8a2": true, "vdOdDAVmaqWeKDYZPryyVNNdJaK2": true, "vvJG6rvxLVNalc9lMbmvjY1TPoM2": true, "vwt6CctyciVwUi5RlUnQsdNQw8s1": true, "vzpjfjI3yMfa5xVpCv30hNC5y2p2": true, "wDwkYyvFN1S159t5fy7wVqeLuAs2": true, "wQd4oNZeTGTVGZPseoFmNInP8Co2": true, "wUWHQbBRmvbtQXvfb2jVLFOvVB33": true, "wahszlBAllTI8qYw1Qf2QXPvb4f2": true, "ws2UMgf71VfFolKif5brGx28gTo1": true, "wugHj8LVFtZYDFS6YT3fmUjbx7n1": true, "wze8WEEtrtX7TpRRlPYE5HiATAi1": true, "x5Lvi6cHvlap7dDTEhqGjKpQOQi2": true, "xxxXouIP0Icd9BLVZkSFbV8T2Oh2": true, "y1NtteTUvTbLsTIxL64PrI7QwtD3": true, "y70otppTDafOeTiTP7lUMimPdOU2": true, "yOIeIaYC52UKoSbDdwwXSD56rEs1": true, "yTwX152WAHQ2hr29wG2d9p82hcv2": true, "yY4sOwwxvYVkXEM6xiIvnyVLNeQ2": true, "yfkrlH9ersc0KWUJ2zBWr6KxBF03": true, "ynyxcNbsvwgPioBcNZMZL2sDmo93": true, "yuPhmNutK3fgr9JA0BNW9xcSe4j1": true, "z1fxoDX6oFU8dixKEoYqqDyyDvE3": true, "z6TL5RjpxOZ8sgUTPlsxExthjJy1": true, "zXAt0jgrTPagOmD8CnpGnxjBN2R2": true, "zfRCj01lpXWpxnHPAOal0sVxgwJ3": true, "zldyeIJ470bBOH4uFILhJ2rl6Wy2": true, "zoRgYRaEJ0Su6Q4FgMBYLoz0tuZ2": true, "zvgpL3EMcoYUxjHzlqEyMSraZxu1": true, "15a9a3c1-8d0b-44d8-9ed4-9dafa4ed4a7a": true, "ig7yAkgflIda7YzkQITq2FMqT0v1": true, "ef89fOgs6SbxE3Vf02fa8075tvl2": true, "B3fdz2BwBRT03RhvtRQl8B9r7gl1": true, "r2QeAYFZRyXthEJrXL7zZWAfa7l2": true, "JxWvHbk3xzQljKAB80wUjcg6Fbu1": true, "f128eacd-e3a9-44b1-a9fe-93386291ecc7": true, "pH4uTVFgvsSr91JAM7s4d1AoSK53": true, "lusCznzKcPaYEw7xKad3oM0Oh0q1": true, "8cedbd40-aa28-490c-8fdb-40fe33b73690": true, "4xmARzt3U8aoZzPg9hs1cFme0xw1": true, "Vs0ZhJotVCPUOygoHY4dTew2tgA3": true, "sWjk6JYiKeaZk46DvqJTB7yFjao1": true, "KbtR7xoKzjaiffz6bHTgGULVTbw2": true, "AzodkD1Muedw5rlprSSBfkqB1oW2": true, "a34a6c17-01fa-42f9-9841-eb619553c267": true, "PuDNuIvrTAfo4Tyu9Z7UPQDUM943": true, "a9f259c0-51ef-46a5-b96e-897ce44e878a": true, "tsddEhZ4TgcSAAW5nVKW9rsDtvf1": true, "0GLWuVu9n9ShdmRCSZlB1xpnlhn1": true, "PZx7Qpftr9Oo8PGpYVVPk2WZaor2": true, "OEpDN9M8i4ahbAF1BVexWNngHHC3": true, "8nDqlHfT1sVm3WnNCXCZRTmlXWx2": true, "gJOnFLP3qxROHqBdkGHKGX9RGsV2": true, "iyIXzt2sr9apKEsBvuqOgdIXWJm2": true, "nvPtkTFxnwd7ote4nHcK6fG57R73": true, "L4JhKvfuQeafOUwDTeW2WSdjJ4d2": true, "DjVAJh7SgZTjPW4Z6uMPmZPObLO2": true, "jney897VYBdLL65QKrTXBTs1bCm2": true, "6a9e2ae2-ea3e-4519-a62a-a7eb752443d5": true, "40086d23-a521-4942-9ee6-78075f22b526": true, "ShGL6CDSUfc89Xh6UnQbQL2ipN32": true, "faSIjDk0aoSh0WViKtgVMyuRB492": true, "e1aff6da-ca00-4c37-940a-e40a9e5d91a1": true, "0b2fa85f-15d4-482d-931c-4798744fc631": true, "z7nd2haVpNPYwwihrN7rNwstuR92": true, "NnfjSopW53ebaDJGkIsKTq9G4Gs1": true, "v114HU550Sao0WoChsTbVLuIc252": true, "1c85f4eb-51d8-4a5f-a63a-c8e645dac5b7": true, "hKsU4msyYyPRlC8x2EIeoojCRMg2": true, "Wqe6Z7gN76QPqNbDonbOwZZ7OC12": true, "BVcHRANXKOVt7k46k9DV3r2Acuj2": true, "inANWlXk2cOoYq3YBfx4Kt8CN4E2": true, "qlMD1PJYvJbr9t2vDn3ySr8Zpri1": true, "TnYfJcjUH3YaPSS7PLYRpizrgtU2": true, "J6eipJrgyXT754na7UjTgYKEYVh1": true, "GNM35uXga4NV8tUcHBOwiSBkwla2": true, "ubmfUraVEMZj1LqX8u1258kR4Gp2": true, "XJKeCycDUgQgOmQtTkDOHKm8op03": true, "dt27oBWPCHa0a3XGQQZu1L9dlSg2": true, "dAIxRX4tCJOlrlZs1Hrr5V3FIF32": true, "2Xbk8xqCLfXQMCFOpSecOakkpTI3": true, "fs6c6AK7a8coQOebqH7KmZTZNjm1": true, "CP0EmNsRSmY7V4p7Y5f3tcfoYFN2": true, "E5A7q38Cc0arXco5kHamY5zvzw32": true, "$device:b84af07b-e707-42de-be0c-4b499b57c17c": true, "sn8kxrmhlVSsQ9QZrM6tAzsHXOq1": true, "Y0CVu0zImPgrA48Env7uCkzkpk83": true, "XYhAzgHHm3U46fBJ8YOUXuFDesp2": true, "N7bHH0wgLWhymVF66ezEBhtCBvT2": true, "KCjfugH4ekQqVvL445chXm2UsCB3": true, "2J69obbcszZhuEl5eSw3CeBPLxr2": true, "07d87669-6af9-43b7-91a7-f82a37921d68": true, "jpmQ0UphfqhGAQwM3eNIGI2d7QW2": true, "vsfoblIUp8WjgmHKW3vLPPqXE2J3": true, "DnpBlLLPqcOC4deP2ugMn66utYr1": true, "FfzLgUAbMChttxulY9tNwTnGFfQ2": true, "5RVSH67JDkN26S8iB6z4mYRhMkp1": true, "jpA9A7YgpEXGjrO9MzM5O2t8wbL2": true, "wgsztHBZ6odtDWIZkRfQU1zZu5f2": true, "fmLTxljuciQrNaglQDLG5uEuZNh1": true, "8WzMuMY41uUZPJK3wmgWh2TvQRR2": true, "QW4Io7YctCRNhv0V4XeLUC4hmJh2": true, "nffahsuaeJNtZCT9jBjtpvgeXrJ2": true, "8f843e33-3563-43d7-85f8-47cf161a708a": true, "kVmVnG1pBXYKF90OJ6CHGe2Igur1": true, "wQt80nS9OEU00WXqsFQiEggFPLI3": true, "MacGpzdl8bbHjMHv113cfYBcIXA3": true, "GW7JLq9X3gMU9tMJVxEkdEtmWkl1": true, "KU9QhgACdzXWKaWxJwRzPDMc9Iu1": true, "LFIPHT30f1dRxzf4NIhGUNVMmri2": true, "8JpZ6yMZFBXJ7G2Kyplpu9lMjv62": true, "oWEXpXqkYIfkE5YRSbaHtl19Hla2": true, "eGi4hPx9NZcIO7WuRghlW6OiwKr1": true, "iZ3wyWLe5cTjrOHYU9Rq9vY4IuH3": true, "aDc7ZI5CnqSb1iBLD6CZ0TfEWTj2": true, "HIeEWmmx11TZ2ZnjaWFExYu0QYD2": true, "foRQEigLEKQi2ACJNGN8OMq5O2p1": true, "$device:877ba832-d65f-416d-8e57-9afb801ccca6": true, "UC4bwnHVBRYj4cojmxcWnd3uaAN2": true, "Z10XPB1xxvTP3iEu0r4eBnOaO5x2": true, "181c8c4b-e65b-4eea-86e4-bed963c46d70": true, "80535973-f52e-4864-81e2-2be7228ee14c": true, "Qq5qhthpjIh7wn0h5sUOvkUIWBp1": true, "EljUS4XunIUhD1fuDYv2B6T4Trb2": true, "Db9sTZrox4Tjz8N7ups87eegzij1": true, "a5DKNEm6eKe4NEwh97dwOVmNBev2": true, "MejPKptPwaesJEIVRUi9PWmLsdM2": true, "vrKz3JWINJg6eG76pgkmhfPljvG3": true, "1r0WQg4QD1UBpQbpCuWCeM5zQrk1": true, "87k4JEGrTZboWlEnDyKwvDMKhrs1": true, "tXObzKl9MdMDzPGTOuSUzJ6s1SG3": true, "ngiIzEAwaBafCtBPe07VG3B0Aop2": true, "xicPVIuxlRWnjPLQ4msKsQVIwkF3": true, "1YskqReZu3PEVAJXbkKyZLFRRuh2": true, "D40LL9oShyWMwDRcLk5pQo6NHHw2": true, "7b5128ea-2949-4fb3-a937-440b5369b2f0": true, "K9573SoUNCgPpTN8uPSSYzwzFvA3": true, "4pnfYqIINBURgkhF4RZaGVUPFkc2": true, "TPA7A1TorvTRwLvMMoOZrk5RlzM2": true, "6SUzVn9DKkclY5WOWYUPNYeKdcz2": true, "jwpkqC7Pc4XpCpndeMIPnfXA6wN2": true, "4df0ebe3-0378-4dae-bbfa-0558b2ca3cc6": true, "YAg664PNKVVmEhovIsvnNlAkK1a2": true, "mN0IqZu5BCcXUfbCLCYGuGluKBU2": true, "ptds7tEBu3OLe8g6H6Cxn08q0Gn1": true, "fosdXAVTzWfpUOB1SWoqr8HLEcg1": true, "htFyfP0r3qZQqRzzHCVZT6DbBCg2": true, "CtwhiBVdMVUVinmWoa0zjYOvkXJ2": true, "d2683612-370f-46d3-9713-6d4a422b7c7f": true, "OzUapG8wupTy3M1WHQhp1PsG2fP2": true, "Y6bQM8KxOvbtlwm91I0Ensmu1Br2": true, "1PJYCOl30vOmCoenzYVpX5ZUFHm2": true, "bQkEr5683AcezBi8hMSUBJe1g9s1": true, "hpoSu4WMhgaFvYtFVAM1yWRKLSc2": true, "mUYgTx3UNUZ9UrNhLh3tVKIFJFr1": true, "Z2zmrIwyDdM3Kv5flA3tLSOe7op1": true, "Dv7BpbC43jfAlzHpyvjMiboXKbS2": true, "4bbP7TJQoUR5K1LaxFzVpg6XIiv2": true, "ExxNQONzExeJ6T5zYBaHNMSxtSB3": true, "qEmBiEBPtLg2vYJ5ok56ZCWG4WA2": true, "sHLzed7ccKWMG5ozXATlNXGeTGY2": true, "****************************": true, "ZAnwlygRehVpclk43hThP7Jd79K2": true, "ea652845-d242-41b6-a4e0-0ba222f8b6b7": true, "clNWUpTbL9Up3FAFgTb5rPwlRe52": true, "eFnx8wZzhOhEWX5EXwAl1CwSHun2": true, "mDvRWiGkCsYAsjDxBFzTi8PNM3v1": true, "cfb7pi4d7Gf8exqFbjUf14Ou9nv2": true, "UtmvgdDpdNhHhnk352pIXe5GbSA3": true, "myLaoRgm8Hf4SEUn2kbh3EIuBtC3": true, "g6PmLWYWCwaKnG1cehGTElb70DI2": true, "0xwFPQQ20bM3FMFciIVftEm2uoJ2": true, "5HxYUJdCE8SoWnKqMesER7je4eO2": true, "5PFpoV6xkJTrWSpSSqPtlq9vjvH3": true, "W3sS65kLm2bhw1BIqflg6NK5mrM2": true, "tTL2MJjUqQdQc4PzNMdfEHT99er1": true, "54yJ9ShiWLcZrwikKFOMMtkDm3P2": true, "VmkgRtyJaPaEv3pl6Nv4EixHkKD2": true, "VdY7L8TUd5SUdFwOy31QfO00XZV2": true, "5597a83c-c2fc-4fbd-8af0-703d5abe8d39": true, "a336327a-c48b-4298-9002-3cbdfef94d0c": true, "$device:949e1d80-3191-4059-81a1-842f91bf0c04": true, "sXf7nOM7zVYLAnJTuBwaBHgxSM33": true, "909c10b5-9632-4cb1-9674-bbeed818be51": true, "RLGjOx9zWhdJL9tM4Kknew6AKBH2": true, "gXzfog8Md6gOzqhjJ0JZ6aE2Z7c2": true, "Ith1LSbQOuZfnk9BJm966LxF9Jf2": true, "3878479d-f01a-4dc5-8e98-d2dbf99d89c8": true, "ZsYZPtiTzFOsi8audedmExnijZs1": true, "hzRFHQwD77fZSAX3IWiXnX8Dn6p1": true, "onrskiQecPWt5tE42M3Xl8U9OuD3": true, "c76I1i5xcvNCCOEwSJaxLlHTAzp2": true, "TK4dOuTYZWYJU5ecixIxvCHvsF33": true, "aWt0jtIrCwhexrNu1srjk4vl3uZ2": true, "5f9399ca-723b-47bf-a5fc-3faf6d47d3e4": true, "qs4MQeL3sZgUTTfh1HeVnW6hfks1": true, "gyINHLoO0hgQFBdSajV1c1R7T0l2": true, "omD9YbZpAbcCKokMXlTfirrfd8i2": true, "6Kcm6m4WRAUU2uqBdT0hwP5EsVL2": true, "etXCcU4kk5QTx68mrsMlnvT7ZHS2": true, "96a755a3-8367-48d2-93bc-017c62dcfc2e": true, "FJRhQFq38kOXx8pkvrdwgVtIM2G3": true, "FKZlEXVKNCXHV7ICVnZzpB9i45q2": true, "tW5VPie4QXWyqyKgn71kCN1kdgg1": true, "0fb1daf2-bee0-4bd9-8de8-a083d0154500": true, "992330e7-9722-40f2-a539-c219aab37a7f": true, "6Qp3M9MG7BdLvKU5vt6ZTN9DTD42": true, "aKqqfdJpyGWfsXYiDLZz3DY9e103": true, "xtAFoUn82SWc2PATJdMIdtT9a2o2": true, "sahwKcKHtUYAGpDZA6LHTlBc2Re2": true, "e456ffbc-ae52-4ffb-977b-0c7c7ce7d4fc": true, "gnEoDOLCrmP8FZ1LimY1gIWtkry2": true, "UVREK9j5a4agwsqWe8PLy29Wvcr1": true, "Fm89cT2BAaPWJGulAgNe9O3vSFJ3": true, "bk1HYWIU2pTGwZNdo0nxG1TFdXk2": true, "ZOCdwUxIEtMu6HseskXtKNoPM8z1": true, "M7OTOzhXO4frH4gOVsCRLFchduP2": true, "6HQZtNzM1ic8yGaPhwwbmF1wiz22": true, "eum6eidbHcT2aUNnEkr3LaT4bH82": true, "7d7df01b-fc47-4f34-b8a0-34cba8275f64": true, "mzAMBKmxsrNnILdiQ62hx5DHOtq2": true, "LNgRjj0yshfxpVNx0QFIwiGynOD3": true, "iD3ObYAct4TRilaJAI7MVuBDz6E2": true, "e4077421-a092-4733-a1d4-d6acf64bf6a2": true, "yZO2tH8G0OWeQmpnCgtomDQsDau2": true, "bYeXjc2SuNYodKa7mCebKa0RNnx1": true, "TBA4MyKUInMbmmPetorjATCeTJC3": true, "G7mU6SqTYHfeTjLjwGfjhVZ0w3i1": true, "zTMnusfwoFWe1zMMKazhEdM5yBk2": true, "A9Eh7zUViWT1XGVeMvKAXMR9tdF2": true, "PEYYA5LSoYQPLSuAXeaAvSsghC62": true, "ajISoXYfirSf6l2D2dy0Jvf0LzC2": true, "6a23e958-8fe2-436f-978b-b446dcfccc3f": true, "0068f473-1494-4490-8197-6b66d24d9796": true, "KMKj4u6PyhWJeXsBqh19Ou2KnP22": true, "c66ea743-36fa-4bc3-a62c-207db6e3eda1": true, "AzKqg17CCpexrvgLlQCTDv33y3S2": true, "Ngwyi8qCXUXeLyQlVvVXlWF1Ms62": true, "Tpe3mBG0sgYdxKzIPl8CgcJc1v93": true, "4NYHkMfaavbbsYuWrlrOvGfAIsx2": true, "UnEqhKbqHXbO6y87cooWdvltupP2": true, "J3D4FWYJGqM1GSf7OvrF0WcM6pY2": true, "IpPym58m5OZY33zhkHjDLAU2FLH3": true, "GjXdo9426uTRY7n32KUGu0ljcKt1": true, "2bqxDGLw8QOePAGWSGpMxKRZ3zD2": true, "lPh5rzqBzbXWcQYpbZB093hhDE12": true, "dUEDCdq9WKNAzqe9DsBxWjCjMR03": true, "vf0fpAX3WyZ9CVthLUQFYzbNJVr1": true, "9g2dOez6BLakQElmbbmvBcIUwmB2": true, "UzWK3hniqzOLpAJc4GZA6xqKX523": true, "zE40UOprW6Sc9QTEEgI65gzojnl1": true, "HHT3m7hGMJP5Ej79z5Q8T4F0nKf2": true, "WeZ9vF1tTZXVdLOPt9r8CXDcGBJ3": true, "sPNFoZ3LKAeZb0OmEprI5c8Cr3F3": true, "Nye6zpt5HIMeZRl71pL0y8U2zOz1": true, "LaDkZ3DUufTojPrKvqa66kIJWxn1": true, "Vjjg4LT4HnfW9TAX2v1dzT3AbqC2": true, "H3Bha45LUDcsMeBzyLwivC2wvMx1": true, "NjZGf3DIq2cDaW8PLb5c1XyVOD22": true, "805cbebb-10bc-42c8-8b6c-e1a7340a3064": true, "NPVZjrxLWeWYWfPrcEDohVA7Fim2": true, "6ISkGAeD6dVNdBfeoTa6OhULJI02": true, "9AnDz1QDgfTfOKw3LzwCz5HqZIZ2": true, "WL1MEktpNvPHnqpuB33xiennc2m2": true, "N3w5bUj8C0XSTk1EXEIiDXd7nLc2": true, "ALakNKOGAMYNAm97XD6Qwfa85ij2": true, "VFhS8QL0G2NVFt6wDAd9wpT7koG2": true, "zAlDSoU85SQZ3uNwLsWYN0EeCYw2": true, "b11489ca-c7f9-4c84-884f-704f36c88302": true, "9DByFzqhhBaPHaRKVFYKhhdys262": true, "baWP1ihdnzbYJnHHRxxxdgn0qKz1": true, "64eaafdc-ae8e-44df-96ce-a3dc7966c03b": true, "UrlRigqmiiMOVds1uETJLEOVdlf2": true, "zg549IioAke5G9e3nOSd26XdqUp1": true, "6co7jKLmmOS94kNMSrzWwdfWE9A2": true, "nOoSKskk82PRHn8hZTndiPITtxg2": true, "sf5z3WVpTzLAocsEOg00CdsfilP2": true, "UmMmiGvAlYW4TuZFdlD79c939yv2": true, "l8trtDQihpOkz61KvBCyisTp7gr2": true, "W54H42Xe1YhSna0DcbAnVf9n5992": true, "aCaXA01AS9OuiNw7dJ4nNozYneI2": true, "jnm9rOd9Fueq15TwhHdyaDNztF13": true, "611b1bde-0889-430f-ab73-4bc85716c772": true, "ae58c46a-b014-4b27-ba84-6fc90e4dee70": true, "An4NjH04M6Z6cl4hfdFt4Mw7v4h2": true, "cCcG72fB8dXvTLShOnokj9o3Wuy1": true, "ywjoP1ZnlPWfUfAMoOvBxD0HFEQ2": true, "VzyZIMuMMJYmeEZe4xVoHCzOPPn2": true, "Jl7apKPNnkZ6XoYBVw8N1ixTg523": true, "wP8sWYhlmGRYc3210U6Sa2kqb5n2": true, "7ec16c5f-81bc-4a21-9e40-8b3a5ca63ad1": true, "oADbaRUnS9XKqRom9INFvPn41Is2": true, "bc3f8f19-e6d1-43da-aacc-fee1e6ce2c6e": true, "UgzjXIrJFWM5qbJcJDmGyICzi4B3": true, "XwU5Ishc7yaOM0NJdrsstsgYiSG3": true, "Ab1vAWCivganplJ3D3MtMaIMDzw2": true, "Tt0twd8WEHPMujj2YtBnFvAxWqF3": true, "VCMceVve5JOjklYqF9Vf33j91Hh2": true, "bOeS3vNuxrPjbUfywgniFFSlzAB3": true, "80213163-9d99-43d5-ae1c-f5e21d56106d": true, "rzIbn9Zo4Yf8nwMiMAJiNYfMCjr2": true, "CGOjELIrM4R19W0cKbJCpCbwzMW2": true, "qkXNBVvepzaSGzYXXLaSWXqN1Ip2": true, "icJTlPJdQTOSBRdkttWZFSo19I32": true, "9cQEQnuIQmZPaZ2HSmcEgf4nUEq1": true, "hj1X7vNlxNehGYqjaWD22dYm2q83": true, "4e3f92d3-17df-457d-951d-b3909f4e55a8": true, "SYfNOhj0KlVcu2rZ5zOhcCw0EZz2": true, "fgkpbTJ24uQdhoCIEI6Y71Bx3A42": true, "fc1ee539-81b9-4719-ba7c-38cbf9bd7b21": true, "APGDjPdhHYWJawMS51PMPfLUwit1": true, "voe1UM4KLZciAfew9VIXMXrj7Rq1": true, "46jq6RPzDUeJabQdsq2AKrkI9I93": true, "d6WK3nkZe4YxnFF4OREhrJtTvWv2": true, "W09E8XjBnphi7xq8nywyhtmknZi1": true, "mqxe4XpLMze2eIkrKh7Emq8984n1": true, "RcasUGzrSoRDRpPGoowZCimUq4I3": true, "x7ihyqIS0gZ1PACdZAEyBCV0vY93": true, "B1tLBBTmNgSVC7H9d5UcwRHey3H3": true, "7d588f5e-a0b6-4078-b1a2-da9d782ff93e": true, "872142cb-407d-4a57-a657-b4c8ff02bb3a": true, "2y96RUxUTFYy2GsddwV58FHEoyx2": true, "ehv6lgnSQecrUOg4hMbLQ2AJN6F2": true, "8NXNWZX5BuQCPxXnIkvpjiZo3vw2": true, "hkLMYzZBcpcZAxs3HCzP4TgMRpP2": true, "iZG43SxgOEeZwBhQrxAYGm3sDB13": true, "bFtxvRZqrDhfH9XearybaTBl4B43": true, "R3VlrHvRq7hWxyrwriuYcaJVVJt1": true, "xxz4cVmGmiVQQDaQXDoouWbmEM03": true, "$device:9f728d87-dbb6-4794-8234-777d7fc4f997": true, "v3AGKQbSutPAx1b26ZFnfZzfFVz2": true, "cb44cdb5-5470-462c-90cc-05f78e0f833d": true, "9NH0Sh1klyPOyGn9rVgiXWrssNG3": true, "221onwgtvmhditWPbAOZpOOi6To2": true, "d4c6e2d1-43d8-42e7-9327-da024f971a3f": true, "lTkmXsC3jBYIDOY5wXyvINLEA092": true, "7pqgeJ0bAygSx6tIi42owgJk0ac2": true, "uJsAtaDu4tY7EYvoGpHNqJFIAMc2": true, "e5c5d705-ccc2-494e-8ed7-6901f62f41a1": true, "10vA23swYTO7MiEBBttWWkbYWPs1": true, "2rwnPwuEBTUSk5eSuclpPbTg3Lr2": true, "rRvbLKix9xPF8DhDvkS5Gj5y1zl2": true, "$device:b05073da-180a-45e0-9b18-404a658157d2": true, "5nxLxL00rhey397wcko6O8AiPsF3": true, "8426aecb-adc7-4ee6-9f8a-2352a362fe6f": true, "EQLiREYgdpQYFETtNJhJ1FlUFcD2": true, "jwHtDXqghfQyNOPDVU4LueWKAq03": true, "1lBZy3DutBbT4sdYuzDjxkLak333": true, "JLJvQAkxYtfhqeed25tz8rRAHGU2": true, "MT4zejwcb4hnoHCFXpYDEBhnbV72": true, "owIOmz7QVweFQ53KARKP1ftfhSy2": true, "WYtNEUZKICRvLfgxsWTcwoMdThG3": true, "wD24TPcWH3c4NB7f9JSLa4ZSQMG3": true, "3RjnDqGmdfQlMi4RAozd9ylVi6z1": true, "zY0iSTbpNBg0buqbSjI0PXYjlUs1": true, "cn8nhaLe8jZD7PbYDF5ZbOROtQB3": true, "5241839a-3159-49bb-b0cb-04c65df8c9a6": true, "NvfGmn7jzHd9s7qbRtcDvHy9JZr1": true, "5pyIhvbY06aD3HaJHVkgt8GdVn33": true, "Ote9QJobMvRwwtjF3ELXKBVGt122": true, "peeNAIGOAZQ7B43OTUiGkE7yRDt1": true, "A1uQZUaueCfUDvk3aT7lGph2CnM2": true, "XYohgZVCuwfNWfWodY5NWIVyx872": true, "KKI5k5g10ZWKWS3lAPwWPfqnej83": true, "akAQjNzaHKYvVPQsGYmsp8TxLli2": true, "sGhEcqU1RHaOiQdLOc9A8M0AqlE2": true, "eDoXZxdDanUYdtFtmC0Vibyb2qX2": true, "gJ9g7GBsvmTyL8zncD3aow89M1P2": true, "2W6GAWNTUnQBRNIaGxSqAMGHahB3": true, "u05aAsMuSuOLZzsVktsz4nmynBn1": true, "fyJOqL1K13RBfZDdfE77IjIpH9n2": true, "IfhLtf2EgkWOSRqmo9FOUCEVKUJ2": true, "IEQlGCBu4fPUyoxetibgmXDp9vD3": true, "zFz3vTF5uAQPHivEBAsKq7rdtD93": true, "8v0kPN72bvXTS5AbsUpulP2WMZk1": true, "EJ9pyHzR8ZY5iH37J3wQCYcM3GF2": true, "78bc1LIUarXTGDNgYrri7pE1nnG2": true, "z5VbWxL6DERmDhdmD8tBDGDKabI2": true, "YXnYlBC3EGNPGGiYoPDTmaKyqsz1": true, "AVTsFU3ZDMfNI0ArkQyNEM51HWF3": true, "QNCObTAOnpOP7DUCoVd4CqzL9W73": true, "Fa1tCMsGS8WWlJ4PYny510ejdMU2": true, "qDzQKuxxTZfcbPhWL7H3u12SCLR2": true, "PhcDq5PfGrVjUS4WBmlkuN0B2ny2": true, "a59d9151-3a41-4bdc-a73f-b3718f31c3e1": true, "4L1ed3nCCqV79yjbKmm05aUDENQ2": true, "loDWvmGI3aewZmWmi9Rws4T2wnz2": true, "6jS4hNl1BCQQmLYN55kVytrPRiF3": true, "6joqiiCNlxSEI1YRmRxqhJPmkO83": true, "dbSJpDdme7S1tYThKkqTWQp5Vho1": true, "C7a6WhVclnZdYPaXcVLRKH4nVow1": true, "09a1d146-af88-4499-b38b-246d3975efde": true, "Rc9MuTmdc0cYK13GtA3dN7UF5BV2": true, "eeHB8uhDUeXF5gZeTX8VP0Lql8y2": true, "AT9fZyVoMaRtac9o4kjHeqN7OI42": true, "rDwszjK5ifN0lAX7P7vAHIjrs9n1": true, "rhyIgXHneKT8x842RofQE4wnQ9W2": true, "GbfEhEOegaN5chLCJGunzqzXMZt2": true, "OjwqgyPCFUV1gx46xIiw0LMalCv2": true, "jsYptbGluwgUTDtyfygK7maQo3l1": true, "hLHLo6cH6DOgi0vwpNneOThf29z1": true, "YRfnTh72RMcJKaEt6XaNfrlXZeR2": true, "QSWlzsXKJDOlQvrTXGiGfUlPoZO2": true, "rT4U5wxxrVNC8U3zelj2z01ma592": true, "1bXb9XmXwbaEBaEwEVGdSHBK5Cc2": true, "tEmBMd2mwtbfjiZQFSVbTNREDd23": true, "tTB8SJ8RfvZCJudodKVi2l94ajr2": true, "UbkqU2OPUPScXShP2FwaDLmlHL32": true, "d2AqFIbXjnMJnH8OR0hFPVHhy6B2": true, "bshnGEszMKT5HSjDvy8drrLMVKu2": true, "y4WRwr4DrraToJcQ0z3lYD3Zut43": true, "zLBkp6jkTbaLoReuGbECCQ6yV5L2": true, "QJjNTuVac7QbzqNVeXvznnUPgvH2": true, "PW4g42f7MTNcqwklUJQmzxUDDxm2": true, "PUdYPmkkbNZFsCfa8KpN9GV7bAO2": true, "fgVoLhQWy0MxDChpnYfrGnEVrmt2": true, "6001ed26-9411-479d-bf28-be7efd697660": true, "8f3590e3-24d7-4eaa-917f-249379bf4ef8": true, "NDSiDVUpgihRpxbjmcmGQrWpLin1": true, "127c4e9b-12f4-4928-83fd-c28cbd83b270": true, "8rFxa6M3iwgKQ2t93O4qJORbmEq1": true, "u9ErPjRF3KT7CVGyG9rnsDxpFew1": true, "kUKpocOXHAOkqUWriySc8Xcm4M62": true, "GW7BYPrtJpdW1xNG9Q1B22F85pI2": true, "f93ce3a2-2be8-4b1e-984d-fedfc7f5ea48": true, "4wJvgsSkfFTSwT7ZQfT6ZHF7LMv1": true, "60b40be7-3410-487e-95b8-29fd90f47f11": true, "o0ho6gHzvCaiOqlUIXmtG5sKtXh1": true, "f34dc490-e455-4cd6-a3e6-cf29d9df1661": true, "OTHwNb5ntwg1aHBAm2RzcBtv4O23": true, "uqgj9dlRBnZ9jAvowhWvRR24RKu2": true, "4oR1nOPMdgP4wiOKRDI2ILVSIQ53": true, "IDj3rxWkgtZjjboonW3qSkTqxY33": true, "hVrSKET28yOwiQIHcafLpl75IIz1": true, "9JQ7h15lahVqLAKebGKGmMbdZDg2": true, "00debdca-50ce-422b-8728-94f1cb656a27": true, "sRaSZK6zAOVHpTMKVMHwz5ZJ3YT2": true, "JZw6qn2dzTMlVMUZ2RHSqTAHLPO2": true, "Cx14UCMmlAhVmg6BjUXlFeA5vym2": true, "VpDuHrvJ7Ue71alTFBMwZwUY9UG3": true, "lRngXBrDpURGnLKo5Ug5clrm8uQ2": true, "F6WDwK5jlmXS9lMGgAeAXGvFUiA3": true, "0lZOBFbVyAVMuGo7vhTHGdODAoj1": true, "MdovgpVxXxQcXNMQ9GS3qUFglur1": true, "qRKjU6cFqgQ2DW9sYMa7F1hqW5B3": true, "Iba2k8tiqKYfLqpxEdAVj2ykIZL2": true, "ZLcraI4Ezpdpkti2bWnYZ0gSGcZ2": true, "cH54ieVhn2ZNsfrGSgcWk8pIesp2": true, "XlePa23s4AaVYyuFk0fM5BBKuQY2": true, "af8ff192-c3aa-42eb-9cea-fe08764499bb": true, "2gPhdtF3TlerqMcjlQwaFZSKTM03": true, "xjhDhlmgrth5aLGDiCBHWyPf6EX2": true, "1bpVKa4DUWaWkOBqRBtRcCLKNKp1": true, "2a696916-07e1-4e6a-86db-57f9adb75cce": true, "6nVLrDRffXMNkAt3fI13Du3OGz52": true, "EGUiXls05qSzxhBe1fK7dUntjOx2": true, "TNrI5stNg8htM4MQTWBvOjKbFYh2": true, "6euRqPEKyWdnjWB1IvTyavCGivW2": true, "Hq9cUixylTTwsW80Xy662xcNEgb2": true, "aC6zf4zT2pZGpGcxjc53gpB9YuX2": true, "I7qOvyzSiaO59y0Gwm9PMTEnXJU2": true, "f43c7960-f918-4128-8578-6cb96c37210b": true, "65fe4966-94da-4681-8dd8-22aa318ea6f8": true, "4c08QHPB5fNqUABkB7f2KqiPCfi1": true, "Es5i70gcwxftNtTSXTRqBgiUcwf1": true, "e018a372-75ba-45b1-9a98-83e7a40f7f93": true, "nU9uu6gEJihLyH2w5vxP5Uzld503": true, "xL2ApcDVVVPjr5krxLZ7UiNNND53": true, "93C2876ihKgqK6jIyxiTXImX2jO2": true, "sGSym4wg82Vhjg10FMWdCtMo1am1": true, "L1ksaZq1sUfD9i3J6gw4lifWBZ03": true, "IjdWPWkIngTZT1Xutvbq1oGt8S72": true, "442454eb-483f-48f8-bb5f-f0234e8dd9e5": true, "mR7awisD00OCjWp40ifZmnank4f1": true, "OEI55w6ub6Stq8Eeif2kxwk2yuZ2": true, "m8WsDXZ3W2g0ElPSrtNu7I2YSSx2": true, "caadce7a-5ba7-4cf6-8cb9-da60a45654df": true, "d5d430b9-135c-40d6-bcd2-3e7247338fe0": true, "34qDMBtT4iOnUqUQAuW2cErJxvi1": true, "gIB0llS3BJgLktbXJHhBb9KnYM13": true, "Yb5PaWOHn8WPT2SpghOVdwOrd7g2": true, "t3M4T0nvN1TbMKSzbyV5WGAEVak2": true, "xZCh7VOyLFOQd7x28XAdgoGm2Ah2": true, "bWh4zhjlIKPmOctDvMlXK9SbAEx2": true, "UtuQWblWJUOZ6QFoSe5KuEerlyM2": true, "y1NUDEjMAoSZVsH5kwVtnxn8rrf1": true, "i5kc6lnzGYY9f35iF1LTXZ0HS2m1": true, "1KTetQIhZnUNdlGxpqn25WelT0s1": true, "Rep0qLZtwFNECZh6ViquXgZsvQk2": true, "7XN38RsQIGZJLY7CgZCIHnuX3nA2": true, "WPXTXMQOh3c5ErZhF0ZPXQZbsfg2": true, "6zROQAJCPdZ0wPxpSZOAXijuIl92": true, "acncPCpnRpSc7dmPY9omNqj6bcf2": true, "OWjfohQCcdcovtrRQkTLGYIZeuV2": true, "FegF6fgmBKdMu20cyIOX7jeFU2H2": true, "77339cab-af5b-48cc-906a-ecb14bb48a8d": true, "07GImsLeZLOxCV5yQlUCFvFpxyt1": true, "ehaKPEc3WPNqX0BIrJHK8KeQHix1": true, "gxevU3mqg9gaErlNb2SYuLySQ5w2": true, "mDYoUrXh77eNcgkgoSRuM4Vq4VZ2": true, "GIlGGUehq1hyoUeF3NEuD3wshfc2": true, "lL4ZeszZOVTZMuQWjxUrIhtDqV63": true, "ya0ZkFBx27aHwHmFWGMg9mtXnQI2": true, "N2vYedfx7cX5FbnNsmR9pjYjfFg2": true, "OrFqInTu7RhbgP54SABnytXG5Dd2": true, "DQP6ecsEsWSQREb8lDkA6njXM5J3": true, "hvmPpF2gUFcd9CpFIsRbGMEQR7u1": true, "OOuJD6xLPggQNFsB2WHiedpS1yT2": true, "DkJuJwuoEPSDkwsh4akud2iQ2qN2": true, "iYeL91ngtTZrCcegjlYU3wgvayA2": true, "Gt37C3ejeabPVcUJN7iDyH5AQxj1": true, "lBAqcAmJYzWy7dI4sls7LBiYSsn2": true, "OG0qtCbO4mUmvAcLBAHYBR2R8Xx2": true, "BtCyPrD1MwhQNsAaio2jMCZkWl52": true, "rkzRL2mNitau4sSmFnFS4hCK5AK2": true, "cMT9XRSc4EYRzSeHqKbc33rTJiH2": true, "qsO4eL1V7kWRSnlNOFpbIsv0LSt2": true, "57apS8BhAfcvy5mEubbQgJWnlqm2": true, "F9fDhRTwEtcY0f9IBPsn9c88MNs1": true, "RXxlrQGFDZTIdTDqlDgnz3YLejL2": true, "pyD3MQJXk5cOEmqwxRHTwDSilGU2": true, "SHCD7dZOawelCEiqgBDKvbgNxZc2": true, "wCAFO5daC7St9gDQNukAIrssdLd2": true, "MqxFFYZG4KhQXoD4qy2eQQXPJRm1": true, "17b3f369-bc53-41d8-ac11-7001b7158f79": true, "b1yaEaDE0bXtdv8QANtvu6JEH0I3": true, "khvZ9dyds3SscuLna8DjCEgxIK93": true, "7a2e3d0b-e6ad-4c13-b2d6-30524edaaea9": true, "6f9VIyGSP0dEXj3AuUdUjURBhWH2": true, "NyLvHZ2mk3do0S07SCr68NFE8zn1": true, "PY5Xtoz5BYMWrsK75d6s8e859Sk1": true, "470a1a6d-5330-406a-aed0-5ff777bec7fa": true, "HMF2rAWFhdWLg0GR1gP8FU2N7Jj1": true, "sXkJLWF6mdYuMASsmJuCYCb3qRJ2": true, "cx7eA04NmkRqjdwDufBgiIECTZi2": true, "YtzVFxre92Q4YZCVItsd5q0OKcI2": true, "PTv9v0PxWqNAvZYw5gRXoPaxwn53": true, "9VNe4sRy40fc9bQVTBRG3ASQZ5W2": true, "rVqxvGanM6brNQizMmPYbEUXF1p1": true, "qdR5AKbgJqTqjfnOYDA1QtEA4Rv2": true, "piFxT4Xg2TRtRqQkgibB0db3iL43": true, "4uxaUHaJozfjOpdXn5NhX59rCu92": true, "HcBXyUBoUlYshITsq6ZxUTe7Zgf1": true, "MKcpP09ygoUVEWhzQAKuKoPOiYk2": true, "teJLZzLZJWdqsEC4QlezTcbet7W2": true, "aRTSGG83LHcx2oCBWhOF55ed9e22": true, "5PtGNRCkTzYQrlVOfNgK72XHz762": true, "k3Ay45FVDuYbYXSl2TmrxxTeKQe2": true, "04a36553-3ae9-4909-b291-96ecca9adb31": true, "cFiOSv3g1bfpGBSRZEqT9E4G9ab2": true, "mvmOqxdf32UObhP6y3yE9HkI34C2": true, "gtLkcr9fKCVPbRzpTyqoSHdHxGY2": true, "3nN9Y44q5PYh4RTmFPft6ktxG5m1": true, "RdObr9JNoWeaGdqYBzjSlfH9iAU2": true}

var ORDERING_MODULE_HOME_GENERIC_SCREEN = "ordering_module_home_screen"

var BrandsMetaWidgetsData string = `[
	{"decoration":{"background":{"type":"image","source":"https://d2rstorage2.blob.core.windows.net/widget/December/11/6c81984b-ea07-4802-a024-de81bd1b9ea9/1733901356158.png","nav":{"name":"WebViewOld","nav_type":"Redirect to WebviewOld","params":{"screenTitle":"","showHeader":false,"uri":"https://webapps.retailpulse.ai/brand?manufacturer=godesi"}}},"header":{"type":"rive","source":"https://d2rstorage.retailpulse.ai/temp/winterAnim.riv","nav":{"name":"WebViewOld","nav_type":"Redirect to WebviewOld","params":{"screenTitle":"","showHeader":false,"uri":"https://webapps.retailpulse.ai/brand?manufacturer=godesi"}}}},"MixPanelEventName":"Profile component","components":[{"MixPanelEventName":"P2P","image_url":"https://d2rstorage2.blob.core.windows.net/widget/December/11/f49bfbcf-efb8-4f2c-8981-915509df4f0e/1733900410751.webp","nav":{"name":"WebViewOld","nav_type":"Redirect to WebviewOld","params":{"screenTitle":"","showHeader":false,"uri":"https://webapps.retailpulse.ai/brand?manufacturer=godesi"}}},{"MixPanelEventName":"Quiz","image_url":"https://d2rstorage2.blob.core.windows.net/widget/December/11/1c95b8c3-0812-4fa0-a77c-0ea7d3ac574b/1733900428432.webp","nav":{"name":"WebViewOld","nav_type":"Redirect to WebviewOld","params":{"screenTitle":"","showHeader":false,"uri":"https://webapps.retailpulse.ai/brand?manufacturer=godesi"}}},{"MixPanelEventName":"Whatsapp-Channel","image_url":"https://d2rstorage2.blob.core.windows.net/widget/December/11/ad3a90b2-0b6c-4499-a8b2-f17cd7526bd0/1733900446264.webp","nav":{"name":"WebViewOld","nav_type":"Redirect to WebviewOld","params":{"screenTitle":"","showHeader":false,"uri":"https://webapps.retailpulse.ai/brand?manufacturer=godesi"}}},{"MixPanelEventName":"P2P","image_url":"https://d2rstorage2.blob.core.windows.net/widget/December/11/f49bfbcf-efb8-4f2c-8981-915509df4f0e/1733900410751.webp","nav":{"name":"WebViewOld","nav_type":"Redirect to WebviewOld","params":{"screenTitle":"","showHeader":false,"uri":"https://webapps.retailpulse.ai/brand?manufacturer=godesi"}}},{"MixPanelEventName":"Quiz","image_url":"https://d2rstorage2.blob.core.windows.net/widget/December/11/1c95b8c3-0812-4fa0-a77c-0ea7d3ac574b/1733900428432.webp","nav":{"name":"WebViewOld","nav_type":"Redirect to WebviewOld","params":{"screenTitle":"","showHeader":false,"uri":"https://webapps.retailpulse.ai/brand?manufacturer=godesi"}}},{"MixPanelEventName":"Whatsapp-Channel","image_url":"https://d2rstorage2.blob.core.windows.net/widget/December/11/ad3a90b2-0b6c-4499-a8b2-f17cd7526bd0/1733900446264.webp","nav":{"name":"WebViewOld","nav_type":"Redirect to WebviewOld","params":{"screenTitle":"","showHeader":false,"uri":"https://webapps.retailpulse.ai/brand?manufacturer=godesi"}}}],"heading":"किराना व्यापार के बेस्ट वीडियो","id":793,"limit":6,"num_col":3,"type":4,"expiry_time":1766829527593,"visible_from":1734688727593,"versions":">=6.3.1","widget_info":{"widget_name":"Omkar Decoration Test WIdget"},"config":{"gradient_colors":["rgba(0,0,0,0)","rgba(0,0,0,0)"]}},
  {
    "MixPanelEventName": "Banner",
    "banner_image_urls": [
      {
        "mixpanel_event_name": "Carousal in Brand Store",
        "nav": {
          "name": "OrderingModule",
          "nav_type": "Redirect to Screen",
          "params": {
            "params": {
              "seller": "mangalam",
              "source": "third_party_mangalam"
            },
            "screen": "Products",
            "seller": "mangalam",
            "source": "third_party_mangalam"
          }
        },
        "url": "https://d2rstorage2.blob.core.windows.net/widget/January/14/b5654e7b-bc76-4d47-aee6-bcb77471baf7/1736854555772.webp"
      },
      {
        "mixpanel_event_name": "Carousal in Brand Store",
        "nav": {
          "name": "OrderingModule",
          "nav_type": "Redirect to Screen",
          "params": {
            "params": {
              "seller": "mangalam",
              "source": "third_party_mangalam"
            },
            "screen": "Products",
            "seller": "mangalam",
            "source": "third_party_mangalam"
          }
        },
        "url": "https://d2rstorage2.blob.core.windows.net/widget/January/14/b5654e7b-bc76-4d47-aee6-bcb77471baf7/1736854555772.webp"
      }
    ],
    "brand_id": "brand_hugs",
    "created_by": "BACKEND",
    "description": "बच्चों की पसंदीदा चॉकलेट शानदार वैरायटी में, मार्जिन 30% तक",
    "expiry": 1767181941847,
    "expiry_time": 1767181941847,
    "heading": "mangalam",
    "icon": {
      "color": "#2C99FF",
      "name": "verified",
      "size": 22,
      "type": "MaterialIcons"
    },
    "id": 822,
    "is_active": 1,
    "is_state_wise": true,
    "mixpanel_event_name": "Ordering Module App Entry hugs",
    "mixpanel_event_object": {
      "brand_id": "brand_hugs",
      "seller": "hugs",
      "source": "third_party_hugs"
    },
    "primary_cta": {
      "icon": "",
      "mixpanel_event_name": "widget_type_37_brand_post_cta",
      "nav": {
        "name": "BrandProfile",
        "nav_type": "Redirect to Brand Profile",
        "params": {
          "brandId": "brand_hugs",
          "screen_name": "BrandProfile_Hugs"
        }
      },
      "primary_color": "#D45339",
      "secondary_color": "#FFFFFF",
      "text": "ब्रांड पोस्ट"
    },
    "profile_image": "https://d2rstorage2.blob.core.windows.net/widget/January/14/35663c7e-afdf-44a3-8fa4-d2614cf7e286/1736855057901.webp",
    "secondary_cta": {
      "icon": "",
      "mixpanel_event_name": "widget_type_37_order_cta",
      "nav": {
        "name": "OrderingModule",
        "nav_type": "Redirect to Screen",
        "params": {
          "params": {
            "seller": "mangalam",
            "source": "third_party_mangalam"
          },
          "screen": "Products",
          "seller": "mangalam",
          "source": "third_party_mangalam"
        }
      },
      "primary_color": "#FFFFFF",
      "secondary_color": "#D45339",
      "text": "ऑर्डर करें"
    },
    "state": [
      "Delhi", "Haryana", "Uttar Pradesh", "Punjab", "Maharashtra"
    ],
    "top_nudge": {
      "bg_color": "#E5EFFF",
      "color": "#023588",
      "text": "1200 किराना दुकानदारों ने अभी तक ख़रीदा"
    },
    "type": 37,
    "updatedBy": "<EMAIL>",
    "updated_at": "2024-12-11T12:00:00Z",
    "visible_from": 1733917941847,
    "widget_info": {
      "widget_name": "Ordering Module App Hugs Entry Widget"
    }
  }
]
`

var BS_NAV_OBJECT1 = `
{
  "name": "BackHandlerBottomSheet",
  "nav_type": "Redirect to Screen",
  "params": {
    "data": {
      "show_close_button": true,
      "widgets": {
        "layout": [
          {
            "CTA": {
              "nav": {
                "name": "OrderingModule",
                "nav_type": "Redirect to Screen",
                "params": {
                  "params": {
                    "seller": "go_desi",
                    "source": "third_party_go_desi"
                  },
                  "screen": "Products"
                }
              }
            },
            "MixPanelEventName": "Banner",
            "component_title": "Go Desi Scheme 1203",
            "expiry_time": 1746023184001,
            "id": 3205,
            "image_url": "https://d2rstorage2.blob.core.windows.net/widget/March/25/d6213ec4-4382-442a-b741-4b84db79db77/1742903959196.webp",
            "type": 3,
            "updatedBy": "<EMAIL>",
            "versions": ">=6.3.0",
            "visibility": 1,
            "visible_from": 1741789584001,
            "widget_info": {
              "widget_name": "Go Desi Scheme 1203"
            }
          }
        ]
      }
    }
  }
}
`

var BS_NAV_OBJECT2 = `
{
  "name": "BackHandlerBottomSheet",
  "nav_type": "Redirect to Screen",
  "params": {
    "data": {
      "show_close_button": true,
      "widgets": {
        "layout": [
          {
            "CTA": {
              "nav": {
                "name": "OrderingModule",
                "nav_type": "Redirect to Screen",
                "params": {
                  "params": {
                    "seller": "go_desi",
                    "source": "third_party_go_desi"
                  },
                  "screen": "Products"
                }
              }
            },
            "MixPanelEventName": "Banner",
            "component_title": "Go Desi Scheme 1203",
            "expiry_time": 1746023184001,
            "id": 3205,
            "image_url": "https://d2rstorage2.blob.core.windows.net/widget/March/25/44968d91-021d-4bb4-b069-efa4a3b34e66/1742903796954.webp",
            "type": 3,
            "updatedBy": "<EMAIL>",
            "versions": ">=6.3.0",
            "visibility": 1,
            "visible_from": 1741789584001,
            "widget_info": {
              "widget_name": "Go Desi Scheme 1203"
            }
          }
        ]
      }
    }
  }
}
`

var ORDERING_PROGRES_WIDGET = `
[{
	"MixPanelEventName": "Banner",
	"component_title": "January Ordering Default Progress",
	"expiry_time": 1738743360000,
	"id": 811,
	"image_urls": [
		"https://d2rstorage2.blob.core.windows.net/widget/February/24/003748ee-0838-47ba-acdb-98710f161873/1740376584462.webp"
	],
	"image_url": "https://d2rstorage2.blob.core.windows.net/widget/February/24/003748ee-0838-47ba-acdb-98710f161873/1740376584462.webp",
	"type": 3,
	"updatedBy": "<EMAIL>",
	"visibility": 1,
	"visible_from": 1735892181530,
	"widget_info": {
		"widget_name": "January Orders Progress Widget"
	}
}]
`
var BrandsMetaHeaderDataLT6_4_0 string = `{
	"text": "",
	"cta": {
	  "text": "मेरे ऑर्डर",
	  "mixpanel_event_name": "Clicked on Aapke Orders CTA on Seller Screen",
	  "icon": "https://d2rstorage2.blob.core.windows.net/widget/March/6/f8829d11-47de-417c-b286-33f534eacb71/1741276470062.webp",
	  "primary_color": "#60062C",
	  "secondary_color": "#ffffff",
	  "nav": {
		"name": "OrderingModule",
		"nav_type": "Redirect to Screen",
		"params": {
		  "screen": "Products",
		  "seller": "go_desi",
		  "source": "third_party_go_desi",
		  "params": {
			"seller": "go_desi",
			"source": "third_party_go_desi"
		  }
		}
	  }
	},
	"help_centre_cta": {
		"text": "सहायता केंद्र",
		"mixpanel_event_name": "Clicked on Help Center CTA on Seller Screen",
		"nav": {
			"name": "WebViewOld",
			"nav_type": "Redirect to WebviewOld",
			"params": {
				"screenTitle": "",
				"showHeader": false,
				"uri": "https://webapps.retailpulse.ai/customer-support/home"
			}
		}
	},
	"cta_secondary": {
	  "text": "लॉयल्टी प्रोग्राम",
	  "mixpanel_event_name": "Clicked on Loyalty CTA on Seller Screen",
	  "icon": "https://d2rstorage2.blob.core.windows.net/widget/March/6/e5de7249-ca4c-4edf-a625-8ee18e22d1b8/1741276480915.webp",
	  "primary_color": "#60062C",
	  "secondary_color": "#ffffff",
	  "nav": {
		"name": "Loyalty"
	  }
	},
	"config": {
	  "gradient_colors": [
		"#A41F4D",
		"#A41F4D"
	  ],
	  "bg_img": ""
	},
	"widget": {
	  "CTA": {
		"nav": {
		  "name": "ExternalLink",
		  "nav_type": "Redirect to External Link",
		  "params": {
		    "uri": "https://play.google.com/store/apps/details?id=club.kirana"
		  }
		}
	  },
	  "MixPanelEventName": "Banner",
	  "component_title": "January Ordering Default Progress",
	  "expiry_time": 1738743360000,
	  "id": 811,
	  "image_urls": [
		"https://d2rstorage2.blob.core.windows.net/widget/March/4/3b3608fc-73d1-4f77-8876-97b8b48153b2/1741094410305.png"
	  ],
	  "image_url": "https://d2rstorage2.blob.core.windows.net/widget/March/4/3b3608fc-73d1-4f77-8876-97b8b48153b2/1741094410305.png",
	  "type": 3,
	  "updatedBy": "<EMAIL>",
	  "visibility": 1,
	  "visible_from": 1735892181530,
	  "widget_info": {
		"widget_name": "January Orders Progress Widget"
	  }
	}
  }`

var BrandsMetaHeaderDataGTE6_4_0 string = `{
  "text": "",
  "cta": {
    "text": "मेरे ऑर्डर",
    "mixpanel_event_name": "Clicked on Aapke Orders CTA on Seller Screen",
    "icon": "https://d2rstorage2.blob.core.windows.net/widget/March/15/c096ff39-1f25-44d8-8ade-88c8d16c8ca9/1742029597970.webp",
    "primary_color": "#3E0E61",
    "secondary_color": "#ffffff",
	"border_color": "#e2b5fd",
    "nav": {
      "name": "OrderingModule",
      "nav_type": "Redirect to Screen",
      "params": {
        "screen": "Products",
        "seller": "go_desi",
        "source": "third_party_go_desi",
        "params": {
          "seller": "go_desi",
          "source": "third_party_go_desi"
        }
      }
    }
  },
  "help_centre_cta": {
		"text": "सहायता केंद्र",
		"mixpanel_event_name": "Clicked on Help Center CTA on Seller Screen",
		"nav": {
			"name": "WebViewOld",
			"nav_type": "Redirect to WebviewOld",
			"params": {
				"screenTitle": "",
				"showHeader": false,
				"uri": "https://webapps.retailpulse.ai/customer-support/home"
			}
		}
	},
  "cta_secondary": {
    "text": "लॉयल्टी प्रोग्राम",
    "mixpanel_event_name": "Clicked on Loyalty CTA on Seller Screen",
    "icon": "https://d2rstorage2.blob.core.windows.net/widget/March/15/b7cadb06-e16c-4612-bdb3-1d7f0a216de2/1742029613542.webp",
    "primary_color": "#3E0E61",
    "secondary_color": "#ffffff",
    "nav": {
      "name": "Loyalty"
    }
  },
  "config": {
    "gradient_colors": [
      "#090164",
      "#090164"
    ],
    "bg_img": ""
  },
  "widget": {
    "CTA": {
      "nav": {
        "name": "GenericScreen",
		"nav_type": "Redirect to Screen",
		"params": {
			"screenName": "March_Loyalty"
		}
      }
    },
    "MixPanelEventName": "Banner",
    "component_title": "January Ordering Default Progress",
    "expiry_time": 1738743360000,
    "id": 811,
    "image_urls": [
      "https://d2rstorage2.blob.core.windows.net/widget/March/15/2cf692e2-3004-42ac-9017-66dce5d08835/1742053588208.png"
    ],
    "image_url": "https://d2rstorage2.blob.core.windows.net/widget/March/15/2cf692e2-3004-42ac-9017-66dce5d08835/1742053588208.png",
    "type": 3,
    "updatedBy": "<EMAIL>",
    "visibility": 1,
    "visible_from": 1735892181530,
    "widget_info": {
      "widget_name": "January Orders Progress Widget"
    }
  }
}`

var BrandsMetaHeaderDataDefaultConfig dto.Header = dto.Header{
	Text: "",
	Cta: dto.Cta{
		Text:              "आपके ऑर्डर्स",
		MixpanelEventName: "Clicked on Aapke Orders CTA on Seller Screen",
		Icon:              "https://d2rstorage2.blob.core.windows.net/widget/March/15/c096ff39-1f25-44d8-8ade-88c8d16c8ca9/1742029597970.webp",
		PrimaryColor:      StrPtr("#3E0E61"),
		SecondaryColor:    StrPtr("#ffffff"),
		BorderColor:       StrPtr("#e2b5fd"),
		Nav: &shared.Nav{
			Name:    "OrderingModule",
			NavType: "Redirect to Screen",
			Params: map[string]interface{}{
				"screen": "Products",
				"seller": "go_desi",
				"source": "third_party_go_desi",
				"params": map[string]interface{}{
					"seller": "go_desi",
					"source": "third_party_go_desi",
				},
			},
		},
	},
	CtaSecondary: &dto.Cta{
		Text:              "लॉयल्टी प्रोग्राम",
		MixpanelEventName: "Clicked on Loyalty CTA on Seller Screen",
		Icon:              "https://d2rstorage2.blob.core.windows.net/widget/March/15/b7cadb06-e16c-4612-bdb3-1d7f0a216de2/1742029613542.webp",
		PrimaryColor:      StrPtr("#3E0E61"),
		SecondaryColor:    StrPtr("#ffffff"),
		Nav: &shared.Nav{
			Name: "Loyalty",
		},
	},
	HelpCentreCta: &dto.Cta{
		Text:              "सहायता केंद्र",
		MixpanelEventName: "Clicked on Help Center CTA on Seller Screen",
		Nav: &shared.Nav{
			Name:    "WebViewOld",
			NavType: "Redirect to WebviewOld",
			Params: map[string]interface{}{
				"screenTitle": "",
				"showHeader":  false,
				"uri":         "https://webapps.retailpulse.ai/customer-support/home",
			},
		},
	},
	Config: map[string]interface{}{
		"gradient_colors": []interface{}{
			"#090164",
			"#090164",
		},
		"bg_img": "",
	},
}

const RewardsCoinsData string = `{
	"amount": 100,
	"text": "100",
	"sub_text": "आपको डिलीवरी के 07 दिन बाद कॉइन मिलेंगे",
	"badge": {
		"text": "फ्री",
		"style": {
			"backgroundColor": "#419169",
			"color": "#ffffff"
		}
	},
	"image_url": "https://d2rstorage2.blob.core.windows.net/widget/November/13/586e7e0c-693f-48c6-a6ae-6f5b842c71b3/1731481931204.webp",
	"nav": {
		"name": "OrderingModule",
		"nav_type": "Redirect to Screen",
		"params": {
			"screen": "Products",
			"seller": "zoff_foods",
			"source": "third_party_zoff_foods",
			"params": {
				"seller": "zoff_foods",
				"source": "third_party_zoff_foods"
			}
		}
	}
}`

const WaysToPayDataCOD string = `[
	{
		"id": 1,
		"text": "कैश ऑन डिलीवरी",
		"sub_text": "इस ऑर्डर की पेमेंट आप डिलीवरी पर कर सकते है"
	}
]`

const CancelOrderMetaData string = `
{
	"title": "कैंसिल करने का कारण बतायें",
	"mixpanel_event_name": "Cancle Kiranabazar Order",
	"options": [
		{
		"value": "गलती से ऑर्डर हो गया था",
		"key": "गलती से ऑर्डर हो गया था ",
		"mandatory_text": false
		},
		{
		"value": "लोकल में ज़्यादा मार्जिन मिल रहा",
		"key": "लोकल में ज़्यादा मार्जिन मिल रहा",
		"mandatory_text": false
		},
		{
		"value": "ऑर्डर में बदलाव करना है",
		"key": "ऑर्डर में बदलाव करना है",
		"mandatory_text": false
		},
		{
		"value": "अन्य कारण",
		"key": "अन्य कारण",
		"mandatory_text": true
		}
	]
}
`

const WaysToPayData string = `[
	{
		"id": 1,
		"text": "कैश ऑन डिलीवरी",
		"sub_text": "इस ऑर्डर की पेमेंट आप डिलीवरी पर कर सकते है"
	}
]`

var INTERNAL_USER_IDS = []string{
	"b8JVr0PvfsNgy9YuKahDseEN4kn1",
	"wMn4a578ChXPCC34Vl2Qn7KZFHV2",
	"XkOExufEktP25tJlsZ1Cr5jFpD93",
	"zqkrZZNnK9hZ7h6rMGLwo5koWt13",
	"KmuBkb6bQISl22uC98Qvw4Ufbnr1",
	"LFgu0CMG4bZXxpjW5oHlX4zEtFf2",
	"q4FCxc8DrEWyuNTS15ko7r1is6i2",
	"82oXtDgnMadYEZvdUwn3VSFQR273",
	"qvFtKgxk8YQrGBi9LEORJduW62l1",
	"k3nIvqWQmQXHKz0gI9QET8Dn3973",
	"2aG0836UBObjwPnaXCtPBWNuMOu2",
	"VPP8HBoYTiZWKyaMU00K6gs8dnq1",
	"jpgesQadplgh6CCjtBoSaAgDbea2",
	"5ql7tXMd0NhmaxDIcs18M5229tn1",
	"yQ9Ms1ZEunRrrMZ1LyBwouhl8x13",
	"jy8mOXgqYLgGKk1rwvBfVVj8vYI3",
	"YkmCvjx1ItMQtXJwcwdhLD3nSbq1",
	"48TzuFIoJneuizT586w28LzxGUA3",
	"PyerCWeFzhhcn0wXk4MLgGMB44G2",
	"KpCxV8GPnRWzb4N6rPMEDtkGgIy2",
	"fDJu3Y85aPU0LU3wBK4JUMds4262",
}

var OrderSuccessBannerWidget = map[string]interface{}{
	"widget_type": "banner",
	"image_url":   "https://firebasestorage.googleapis.com/v0/b/op-d2r.appspot.com/o/marketing%2FTarget_Scheme_Order_Confirm_Screen.webp?alt=media",
	"CTA": map[string]interface{}{
		"nav": &shared.Nav{
			Name:    "GenericScreen",
			NavType: "Redirect to Screen",
			Params: map[string]interface{}{
				"screenName": "May_Target_Scheme",
			},
		},
	},
}

var AUTO_CONFIRM_BANNER_WIDGET = map[string]interface{}{
	"widget_type": "banner",
	"image_url":   "https://d2rstorage2.blob.core.windows.net/widget/June/19/39a2d200-98da-49c9-a95f-9a15d25d1388/1750339202007.png",
}

var ORDERING_BOTTOM_TABS = []dto.OrderingBottomTab{
	{
		"BackButton": {
			IconUrl: "https://d2rstorage2.blob.core.windows.net/widget/March/20/78e27d92-d52c-49a4-b893-5e4463fc0ea6/1742458676002.webp",
			Label:   "फीड",
			NavObj: &shared.Nav{
				Name: "BackButton",
			},
		},
	},
	{
		"Seller": {
			HighlightedIconUrl: "https://d2rstorage2.blob.core.windows.net/widget/March/19/cc75db48-9abd-4e51-800c-b120668d4293/1742388621874.webp",
			IconUrl:            "https://d2rstorage2.blob.core.windows.net/widget/March/19/737162b5-083d-407f-a4b3-b90c89610421/1742393023493.webp",
			Label:              "आर्डर",
			ScreenName:         "Seller",
			NavObj: &shared.Nav{
				Name:    "OrderingModule",
				NavType: "Redirect to Screen",
				Params: map[string]interface{}{
					"screen": "Seller",
				},
			},
		},
	},
	{
		"Categories": {
			HighlightedIconUrl: "https://d2rstorage2.blob.core.windows.net/widget/June/19/6cb69f50-0e77-471c-b4f2-998f969e694d/1750332702856.webp",
			IconUrl:            "https://d2rstorage2.blob.core.windows.net/widget/June/19/3af73680-253e-4c9b-8cb8-158df078ce07/1750332675839.webp",
			Label:              "केटेगरी",
			ScreenName:         "Categories",
			NavObj: &shared.Nav{
				Name:    "GenericScreen",
				NavType: "Redirect to Screen",
				Params: map[string]interface{}{
					"screenName": "Categories Screen - Non RSB",
				},
			},
		},
	},
	{
		"Loyalty": {
			HighlightedIconUrl: "https://d2rstorage2.blob.core.windows.net/widget/March/19/12483b7c-ace8-4a06-bcc4-9915ebd0e165/1742388449334.webp",
			IconUrl:            "https://d2rstorage2.blob.core.windows.net/widget/March/19/754d0def-6b69-4bf8-bb66-a08a27970451/1742392945870.webp",
			Label:              "लॉयल्टी",
			ScreenName:         "Loyalty",
			NavObj: &shared.Nav{
				Name:    "OrderingModule",
				NavType: "Redirect to Screen",
				Params: map[string]interface{}{
					"screen": "Loyalty",
				},
			},
			// Nudge: &dto.Nudge{
			// 	Id:    "New",
			// 	Name:  "New",
			// 	Type:  "dot",
			// 	Color: "#D93025",
			// },
		},
	},
	{
		"Ordering_Offers_Tab": {
			HighlightedIconUrl: "https://d2rstorage2.blob.core.windows.net/widget/March/19/50300d9b-63b9-4739-8e54-ec958da32af0/1742388480023.webp",
			IconUrl:            "https://d2rstorage2.blob.core.windows.net/widget/March/19/18ead6ef-5617-4e35-b164-5893a0034364/1742392983497.webp",
			Label:              "ऑफर",
			ScreenName:         "DealsGenericScreen", // always DealsGenericScreen static DO NOT CHANGE
			NavObj: &shared.Nav{
				Name:    "GenericScreen",
				NavType: "Redirect to Screen",
				Params: map[string]interface{}{
					"screenName": "Ordering_Offers_Tab",
				},
			},
			// Nudge: &dto.Nudge{
			// 	Text:      "नया",
			// 	Shimmer:   true,
			// 	Id:        "New",
			// 	Name:      "New",
			// 	Type:      "text",
			// 	TextColor: "#ffffff",
			// 	Color:     "#F40D9F",
			// 	CreatedAt: 1729775957354,
			// 	ExpiryAt:  1761916757354,
			// },
		},
	},
	{
		"Cart": {
			HighlightedIconUrl: "https://d2rstorage2.blob.core.windows.net/widget/March/20/3575ca1e-1f10-4482-9b88-4f55bf96e39f/1742454079976.webp",
			IconUrl:            "https://d2rstorage2.blob.core.windows.net/widget/March/20/22c7b99e-4765-4425-b8b1-a93ce8c57b98/1742454037702.webp",
			Label:              "कार्ट",
			ScreenName:         "Cart",
			NavObj: &shared.Nav{
				Name:    "OrderingModule",
				NavType: "Redirect to Screen",
				Params: map[string]interface{}{
					"screen": "Cart",
				},
			},
		},
	},
}

// expressBees key constants
const ExpressBeesKey = "a14acdee-3c6a-4d33-8638-f8e7a7e83c02"

const ShipDelightKey = "cd5942d6-31fd-4b91-af43-1d2e1a187977"

const EkartKey = "2160a19f-0e8b-4438-af17-d0db22dcf906"

const TYPE_44_WIDGET string = `
{
    "category": [
        {
            "image_url": "https://d2rstorage2.blob.core.windows.net/widget/May/12/bda5a09e-25fe-4119-954a-285b2a56f82c/1747050782900.webp",
            "label": "हल्दी / मिर्च / धनिया",
            "value": "हल्दी / मिर्च / धनिया",
            "id": "169",
			"no_of_rows": 1,
            "screen_tag": "widget:1239",
            "screen_type": "products",
            "bg_color": "#E6E6E6"
        },
        {
            "image_url": "https://d2rstorage2.blob.core.windows.net/widget/May/12/2673eb82-1d37-4c95-8aa6-5d09a78f54d5/1747054815326.webp",
            "label": "मसाले - ₹5 /10 पैकेट",
            "value": "मसाले - ₹5 /10 पैकेट",
            "id": "98",
            "screen_tag": "gaurav_sir_category_test",
            "screen_type": "widgets",
            "bg_color": "#E6E6E6"
        },
        {
            "image_url": "https://d2rstorage2.blob.core.windows.net/widget/May/12/136a0810-6fca-4511-afd3-b44a6716dd61/1747050605694.webp",
            "label": "खड़े मसाले",
            "value": "खड़े मसाले",
            "id": "245",
			"no_of_rows": 2,
            "screen_tag": "widget:1239",
            "screen_type": "products",
            "bg_color": "#E6E6E6"
        },
        {
            "image_url": "https://d2rstorage2.blob.core.windows.net/widget/May/12/2673eb82-1d37-4c95-8aa6-5d09a78f54d5/1747054815326.webp",
            "label": "मसाले - 50/100g पैकेट",
            "value": "मसाले - 50/100g पैकेट",
            "id": "100",
            "screen_tag": "Ordering_Punjee_Contest",
            "screen_type": "widgets",
            "bg_color": "#E6E6E6"
        },
        {
            "image_url": "https://d2rstorage2.blob.core.windows.net/widget/May/12/3d9b4c5b-6483-4a27-a304-27f7911e14ed/1747050807630.webp",
            "label": "सूखे मेवे",
            "value": "सूखे मेवे",
            "id": "169",
			"no_of_rows": 1,
            "screen_tag": "widget:1239",
            "screen_type": "products",
            "bg_color": "#E6E6E6"
        },
        {
            "image_url": "https://d2rstorage2.blob.core.windows.net/widget/May/12/f4326eb8-b555-4413-9fb2-3734236fc251/1747050818706.webp\n",
            "label": "नमक",
            "value": "नमक",
            "id": "245",
			"no_of_rows": 2,
            "screen_tag": "widget:1239",
            "screen_type": "products",
            "bg_color": "#E6E6E6"
        },
        {
            "image_url": "https://d2rstorage2.blob.core.windows.net/widget/May/12/4872f861-8d5a-48a5-879d-e2417371c45d/1747050829468.webp",
            "label": "अन्य",
            "value": "अन्य",
            "id": "169",
			"no_of_rows": 1,
            "screen_tag": "widget:1239",
            "screen_type": "products",
            "bg_color": "#E6E6E6"
        }
    ],
    "color": "#076060",
    "type": 44,
    "header": {
        "text": "दुबारा खरीदें",
        "highlighted_text": [
            {
                "text": "खरीदें",
                "highlight_style": {
                    "color": "#076060",
                    "fontWeight": 700
                }
            }
        ],
        "styles": {
            "backgroundColor": "#ffffff",
            "color": "#000000"
        }
    },
    "MixPanelEventName": "ProductList Widget",
    "expiry_time": 1746622965,
    "id": 1133,
    "updatedBy": "system",
    "visibility": 1,
    "visible_from": 1746536565,
    "widget_info": {
        "widget_name": "ProductList Products Screen Widget"
    }
}
`

const TYPE_46_WIDGET string = `
{
  "type": 46,
  "header": {
    "text": "5 आइटम आपके कार्ट में है",
    "color": "#333333"
  },
  "products": [
    {
      "image_url": "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2015.jpeg?timestamp=1720181217",
      "nav": {
        "name": "OrderingModule",
        "params": {
          "screen": "ProductDetailsOrdering",
          "params": {
            "seller": "zoff_foods",
            "source": "third_party_zoff_foods",
            "id": "118",
            "category_ids": [
              "103"
            ],
            "product_name": "सेंधा नमक पाउडर"
          }
        }
      }
    },
    {
      "image_url": "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2046.jpg?timestamp=1720181221",
      "nav": {
        "name": "OrderingModule",
        "params": {
          "screen": "ProductDetailsOrdering",
          "params": {
            "seller": "zoff_foods",
            "source": "third_party_zoff_foods",
            "id": "44",
            "category_ids": [
              "101"
            ],
            "product_name": "बादाम (स्वतंत्र)"
          }
        }
      }
    },
    {
      "image_url": "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2000.jpeg?timestamp=1720181205",
      "nav": {
        "name": "OrderingModule",
        "params": {
          "screen": "ProductDetailsOrdering",
          "params": {
            "seller": "zoff_foods",
            "source": "third_party_zoff_foods",
            "id": "2321",
            "category_ids": [
              "97"
            ],
            "product_name": "हल्दी पाउडर"
          }
        }
      }
    },
    {
      "image_url": "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2002.jpeg?timestamp=1720181219",
      "nav": {
        "name": "OrderingModule",
        "params": {
          "screen": "ProductDetailsOrdering",
          "params": {
            "seller": "zoff_foods",
            "source": "third_party_zoff_foods",
            "id": "2428",
            "category_ids": [
              "97"
            ],
            "product_name": "धनिया पाउडर"
          }
        }
      }
    },
    {
      "image_url": "https://kiranaclub.blob.core.windows.net/brands/zoff/products/400x400/zoff_2004.jpeg?timestamp=1720181240",
      "nav": {
        "name": "OrderingModule",
        "params": {
          "screen": "ProductDetailsOrdering",
          "params": {
            "seller": "zoff_foods",
            "source": "third_party_zoff_foods",
            "id": "2441",
            "category_ids": [
              "99"
            ],
            "product_name": "जीरा पाउडर"
          }
        }
      }
    }
  ],
  "offer_details": {
    "text": "₹500 का सामान जोड़े और पायें 5% एक्स्ट्रा",
    "color": "#2D7D32",
    "icon_url": "https://d2rstorage2.blob.core.windows.net/widget/June/11/48604a47-6641-42a6-9a64-ba3a0456f691/1749643393361.webp"
  },
  "cta": {
    "text": "ऑर्डर करें",
    "background_color": "#FFF1ED",
    "color": "#D94F29",
    "nav": {
      "name": "OrderingModule",
      "params": {
        "screen": "Checkout",
        "params": {
          "seller": "zoff_foods",
          "source": "third_party_zoff_foods"
        }
      }
    }
  }
}
`

const ORDERING_ONBOARDING_DATA string = `
{
  "onboarding_screens": [
    {
      "id": 1,
      "media": [
        {
          "media_type": "rive",
          "url": "https://d2rstorage2.blob.core.windows.net/temp/Onboarding screen rive brands.riv",
          "config": {
            "gradient_colors": [
              "#FDF1CC",
              "#FDF1CC"
            ]
          },
          "height": 100
        },
        {
          "media_type": "image",
          "url": "https://d2rstorage2.blob.core.windows.net/widget/June/10/67b26c74-eca2-4aca-9f4e-5f8936f1e089/1749550466776.webp",
		  "config": {
            "gradient_colors": [
              "#FDF1CC",
              "#FDF1CC"
            ]
          }
        },
        {
          "media_type": "image",
          "url": "https://d2rstorage2.blob.core.windows.net/widget/June/12/a24cac75-37ab-42f0-9c23-06105b80822d/1749709457169.webp"
        }
      ],
      "footer": {
        "cta": {
          "text": "आगे बढ़े",
          "mixpanel_event_name": "",
          "icon": ""
        }
      }
    },
    {
      "id": 2,
      "media": [
        {
          "media_type": "image",
          "url": "https://d2rstorage2.blob.core.windows.net/widget/June/10/59d0a3c3-daa4-4e95-acef-94c7d19e4d88/1749550424535.webp",
		  "config": {
            "gradient_colors": [
              "#FDF1CC",
              "#FDF1CC"
            ]
          }
        },
        {
          "media_type": "text-image",
          "url": "https://d2rstorage2.blob.core.windows.net/widget/June/12/964468a8-2f39-471a-9029-4c63979ad4e4/1749709532494.webp"
        }
      ],
      "footer": {
        "cta": {
          "text": "आगे बढ़े",
          "mixpanel_event_name": "",
          "icon": ""
        }
      }
    },
    {
      "id": 3,
      "media": [
        {
          "media_type": "rive",
          "url": "https://d2rstorage2.blob.core.windows.net/temp/cashback_rive",
          "config": {
            "gradient_colors": [
              "#FDF1CC",
              "#FDF1CC"
            ]
          },
          "height": 100
        },
        {
          "media_type": "image",
          "url": "https://d2rstorage2.blob.core.windows.net/widget/June/17/b9a20a72-204e-4463-a3c9-29df49743bb0/1750159652417.webp",
          "config": {
            "gradient_colors": [
              "#FDF1CC",
              "#FDF1CC"
            ]
          },
          "nav": {
            "name": "OrderingModule",
            "nav_type": "Redirect to Screen",
            "params": {
              "screen": "Seller"
            }
          }
        }
      ],
	  "config": {
            "gradient_colors": [
              "#FDF1CC",
              "#FDF1CC"
            ]
          },
      "footer": {
        "bottom_component": {
          "primary_text": "Bangalore के 1000+ दुकानदारों",
          "secondary_text": "ने इस ऑफ़र का उठाया फायदा ✅",
          "image_url": "https://d2rstorage2.blob.core.windows.net/widget/June/17/1659af86-3145-4a3f-90d3-8c6d8a612974/1750159757053.webp",
          "styles": {
            "bg_color": "#FFFFFF",
            "color": "#D94F29"
          }
        }
      }
    }
  ],
  "progress_bar": {
    "color": "#fhfhfh",
    "text": "🎁"
  }
}
`
const SUGGESTED_WIDGETS string = `
[
  {
    "MixPanelEventName": "Banner",
    "component_title": "Suggested See More Banner",
    "expiry_time": 1779361740000,
    "id": 1193,
    "image_url": "https://d2rstorage2.blob.core.windows.net/widget/June/16/8d86b383-fa2a-4741-9d2b-c59600992738/1750076247003.webp",
    "isStateWise": false,
    "sql_id": 5587,
    "type": 3,
    "updatedBy": "<EMAIL>",
    "versions": ">=6.3.0",
    "visibility": 1,
    "visible_from": 1747825798157,
    "widget_info": {
      "widget_name": "Suggested See More Banner"
    }
  }
]
`

const SUGGESTED_WIDGETS_2 string = `[
{
    "MixPanelEventName": "Banner",
    "component_title": "Ordering Home Milden Store Banner",
    "expiry_time": 1779361740000,
    "id": 1193,
    "image_url": "https://d2rstorage2.blob.core.windows.net/widget/June/16/aaa60dd0-e457-4418-9c72-cebe4135a582/1750076346281.webp",
    "isStateWise": false,
    "sql_id": 5587,
    "type": 3,
    "updatedBy": "<EMAIL>",
    "versions": ">=6.3.0",
    "visibility": 1,
    "visible_from": 1747825798157,
    "widget_info": {
      "widget_name": "Ordering Home Milden Store Banner"
    }
  }
]`
