package utils

// Package crypto provides cryptography-related functions needed for the ONDC subscription flow.

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/ecdh"
	"crypto/rand"
	"crypto/sha256"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/asn1"
	"encoding/base64"
	b64 "encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/zenazn/pkcs7pad"
)

var x25519Curve = ecdh.X25519()

type pkcs8 struct {
	Version    int
	Algo       pkix.AlgorithmIdentifier
	PrivateKey []byte
	// optional attributes omitted.
}

type publicKeyInfo struct {
	Raw       asn1.RawContent
	Algorithm pkix.AlgorithmIdentifier
	PublicKey asn1.BitString
}

// GenerateEncryptionKeyPair generates a new X25519 key pair.
//
// The public key is in DER form.
func GenerateEncryptionKeyPair() (privateKey, publicKey, publicKeyDER []byte, err error) {
	key, err := x25519Curve.GenerateKey(rand.Reader)
	if err != nil {
		return nil, nil, nil, err
	}

	publicKeyDER, err = x509.MarshalPKIXPublicKey(key.PublicKey())
	if err != nil {
		return nil, nil, nil, err
	}

	privateKey = key.Bytes()
	publicKey = key.PublicKey().Bytes()
	return privateKey, publicKey, publicKeyDER, nil
}

// DecryptMessage decrypts the encryptedMessage from the ONDC regsitry.
// privateKey and publicKey are X25519 keys. These keys are used to generate a shared secret.
// The shared secret is used as a key of the AES-ECB-PKCS7 algorithm.
func DecryptMessage(encryptedMessage string, privateKeyInString, publicKeyInString string) (string, error) {
	privateKey, _ := parseX25519PrivateKey(privateKeyInString)
	publicKey, _ := parseX25519PublicKey(publicKeyInString)
	aesCipher, err := createAESCipher(privateKey, publicKey)
	if err != nil {
		return "", err
	}

	messageByte, err := base64.StdEncoding.DecodeString(encryptedMessage)
	if err != nil {
		return "", err
	}

	for i := 0; i < len(messageByte); i += aesCipher.BlockSize() {
		executionSlice := messageByte[i : i+aesCipher.BlockSize()]
		aesCipher.Decrypt(executionSlice, executionSlice)
	}
	//
	messageByte, err = pkcs7pad.Unpad(messageByte)
	if err != nil {
		return "", err
	}
	messageByte = bytes.TrimRight(messageByte, " ")
	return string(messageByte), nil
}

// EncryptMessage encrypts a message like how ONDC registry would encrypt it.
// privateKey and publicKey are X25519 keys. These keys are used to generate a shared secret.
// The shared secret is used as a key of the AES-ECB-PKCS7 algorithm.
func EncryptMessage(message string, privateKey, publicKey []byte) (string, error) {
	messageByte := []byte(message)
	aesCipher, err := createAESCipher(privateKey, publicKey)
	if err != nil {
		return "", err
	}

	messageByte = pkcs7pad.Pad(messageByte, aesCipher.BlockSize())

	for i := 0; i < len(messageByte); i += aesCipher.BlockSize() {
		aesCipher.Encrypt(messageByte[i:i+aesCipher.BlockSize()], messageByte[i:i+aesCipher.BlockSize()])
	}

	return base64.StdEncoding.EncodeToString(messageByte), nil
}

func ExtractRawPubKeyFromDER(pubKeyDERB64 string) ([]byte, error) {
	publicKeyDER, err := base64.StdEncoding.DecodeString(pubKeyDERB64)
	if err != nil {
		return nil, fmt.Errorf("crypto: extract raw pub key error: %v", err)
	}

	publicKeyParsed, err := x509.ParsePKIXPublicKey(publicKeyDER)
	if err != nil {
		return nil, fmt.Errorf("crypto: extract raw pub key error: %v", err)
	}

	publicKeyCasted, ok := publicKeyParsed.(*ecdh.PublicKey)
	if !ok {
		return nil, errors.New("crypto: extract raw pub key error: incorrect key type")
	}

	publicKeyRaw := publicKeyCasted.Bytes()
	if keyLenght := len(publicKeyRaw); keyLenght != 32 {
		return nil, fmt.Errorf("crypto: extract raw pub key error: incorrect key type: invalid key length %d", keyLenght)
	}

	return publicKeyRaw, nil
}

func createAESCipher(privateKey, publicKey []byte) (cipher.Block, error) {
	x25519PrivateKey, err := x25519Curve.NewPrivateKey(privateKey)
	if err != nil {
		return nil, err
	}
	x25519PublicKey, err := x25519Curve.NewPublicKey(publicKey)
	if err != nil {
		return nil, err
	}
	sharedSecret, err := x25519PrivateKey.ECDH(x25519PublicKey)
	if err != nil {
		return nil, err
	}

	aesCipher, err := aes.NewCipher(sharedSecret)
	if err != nil {
		return nil, err
	}

	return aesCipher, nil
}

func parseX25519PrivateKey(key string) ([]byte, error) {
	var parsedKey []byte
	decoded, err := base64Decode(key)
	if err != nil {
		fmt.Println("Error base64 decoding x25519 private key", err)
		return parsedKey, err
	}

	var pkcsKey pkcs8
	_, err = asn1.Unmarshal(decoded, &pkcsKey)
	if err != nil {
		fmt.Println("Error asn1 unmarshaling x25519 private key", err)
		return parsedKey, err
	}

	_, err = asn1.Unmarshal(pkcsKey.PrivateKey, &parsedKey)
	if err != nil {
		fmt.Println("Error asn1 unmashaling pkcs privat key", err)
		return parsedKey, err
	}
	return parsedKey, nil
}

func parseX25519PublicKey(key string) ([]byte, error) {
	var parsedKey []byte

	decoded, err := base64Decode(key)
	if err != nil {
		fmt.Println("Error base64 decoding x25519 public key", err)
		return parsedKey, err
	}

	var x509Key publicKeyInfo
	_, err = asn1.Unmarshal(decoded, &x509Key)
	if err != nil {
		fmt.Println("Error asn1 unmarshaling x25519 public key", err)
		return parsedKey, err
	}

	return x509Key.PublicKey.RightAlign(), nil
}

func base64Decode(payload string) ([]byte, error) {
	return b64.StdEncoding.DecodeString(payload)
}

// Function to generate a short Base64 ID from the struct
func GenerateShortBase64ID(data any) (string, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	hash := sha256.Sum256(jsonData)
	shortHash := hash[:10]
	base64ID := base64.URLEncoding.EncodeToString(shortHash)

	return strings.TrimRight(base64ID, "="), nil
}
