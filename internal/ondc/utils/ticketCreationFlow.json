{"version": "1.3", "flowId": "defaultOrderingSupportFlow", "components": {"optionsComponent": {"type": "options", "properties": {"title": "", "description": "", "subtext": "", "options": []}}, "mediaUploadComponent": {"type": "mediaUpload", "properties": {"title": "", "description": "", "subtext": "", "allowedTypes": ["image", "video"], "maxFiles": 5}}, "productListingComponent": {"type": "productListing", "properties": {"title": "", "description": "", "subtext": "", "products": [], "selectable": false, "quantityEditable": false}}, "orderStatusComponent": {"type": "orderStatus", "properties": {"title": "", "description": "", "subtext": "", "orderDetails": {}, "trackingHistory": [], "orderStatusCard": {}}}, "textFieldComponent": {"type": "textField", "properties": {"title": "", "description": "", "subtext": "", "placeholder": "", "maxLength": 500, "required": true, "question": ""}}, "multipleQuestionsComponent": {"type": "multipleQuestions", "properties": {"title": "", "description": "", "subtext": "", "questions": [{"id": "question1", "text": "", "options": [{"id": "yes", "text": "हाँ"}, {"id": "no", "text": "नहीं"}]}]}}, "ticketSummaryComponent": {"type": "ticketSummary", "properties": {"title": "", "description": "", "subtext": "", "ticketDetails": {}}}, "thankYouComponent": {"type": "thankYou", "properties": {"title": "", "description": "", "subtext": "", "ticketId": ""}}}, "flow": {"start": "issueCategory", "screens": {"issueCategory": {"id": "issueCategory", "component": {"type": "optionsComponent", "properties": {"title": "समस्या का कारण चुनें", "description": "आपकी प्रॉब्लम किससे जुड़ी है, वो चुनें", "options": [{"id": "orderStatus", "text": "मेरा ऑर्डर कब आएगा?", "nextScreen": "orderStatusDetail"}, {"id": "itemMissing", "text": "ऑर्डर में सामान कम आया है", "nextScreen": "itemMissingDetail"}, {"id": "damagedProduct", "text": "डैमेज्ड प्रोडक्ट", "nextScreen": "damagedProductDetail"}, {"id": "wrongItemDelivered", "text": "गलत प्रोडक्ट आया", "nextScreen": "wrongItemDetail"}, {"id": "orderOffer", "text": "ऑफर और रिवॉर्ड से जुड़ी समस्या", "nextScreen": "orderOfferDetail"}, {"id": "otherIssues", "text": "कोई और समस्या", "nextScreen": "otherIssuesDetail"}]}}}, "orderStatusDetail": {"id": "orderStatusDetail", "component": {"type": "orderStatusComponent", "properties": {"title": "ऑर्डर स्टेटस", "description": "नीचे आपके ऑर्डर की जानकारी और status है", "orderDetails": {"orderNumber": "124452", "orderDate": "Apr 27, 2025", "expectedDelivery": "2025-04-27", "status": "IN TRANSIT", "courier": "Delhivery", "awbNumber": "36512410068526"}, "trackingHistory": [{"status": "Delivered", "time": "2025-04-26 19:15:56", "location": "Dhariyawad_Kalyanpura_DPP (Rajasthan)", "color": "#28a745"}, {"status": "Contacted Customer", "time": "2025-04-26 18:56:16", "location": "Dhariyawad_Kalyanpura_DPP (Rajasthan)", "color": "#6c757d"}, {"status": "Out for Delivery", "time": "2025-04-26 15:19:34", "location": "Dhariyawad_Kalyanpura_DPP (Rajasthan)", "color": "#ffc107"}, {"status": "In-Transit", "time": "2025-04-26 14:16:13", "location": "Dhariyawad_Kalyanpura_DPP (Rajasthan)", "color": "#ffc107"}, {"status": "In-Transit", "time": "2025-04-26 05:00:38", "location": "Mandsaur_Ajijkhedi_I (Madhya Pradesh)", "color": "#ffc107"}, {"status": "Picked Up", "time": "2025-04-24 23:40:43", "location": "Indore_Dakachya_GW (Madhya Pradesh)", "color": "#151B54"}]}}, "nextActions": [{"actionType": "button", "text": "प्रॉब्लम सॉल्व हो गई", "nextScreen": "thankYou", "color": "#28a745"}, {"actionType": "button", "text": "अभी भी प्रॉब्लम है", "nextScreen": "orderStatusIssue", "color": "#dc3545"}]}, "orderStatusIssue": {"id": "orderStatusIssue", "component": {"type": "optionsComponent", "properties": {"title": "क्या दिक्कत आई डिलीवरी में?", "description": "डिलीवरी से जुड़ी सही समस्या चुनें", "options": [{"id": "noDoorstep", "text": "डिलीवरी बॉय ने दरवाज़े तक देने से मना किया", "nextScreen": "orderStatusDescription"}, {"id": "noCall", "text": "डिलीवरी बॉय ने कॉल नहीं किया", "nextScreen": "orderStatusDescription"}, {"id": "delayInOrder", "text": "आर्डर आने में देरी हो रही है", "nextScreen": "orderStatusDescription"}]}}}, "orderStatusDescription": {"id": "orderStatusDescription", "component": {"type": "textFieldComponent", "properties": {"title": "और जानकारी दें", "description": "थोड़ी और डिटेल्स लिखिए", "placeholder": "यहाँ लिखें...", "required": true}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "itemMissingDetail": {"id": "itemMissingDetail", "component": {"type": "productListingComponent", "properties": {"title": "कौन-कौन सी चीज़ें नहीं आई, चुनें", "description": "जो आइटम मिसिंग हैं वो चुनें और क्वांटिटी एडजस्ट करें", "subtext": "अगर पूरी क्वांटिटी मिसिंग है तो वैसा ही रहने दें, अगर कुछ यूनिट्स हैं तो क्वांटिटी बदलें", "products": [{"name": "<PERSON><PERSON><PERSON>", "quantity": 10, "weight": "200g", "pack_size": 5, "mrp": "MRP: ₹84"}, {"name": "<PERSON><PERSON><PERSON>", "quantity": 10, "weight": "100g", "pack_size": 10, "mrp": "MRP: ₹42"}, {"name": "<PERSON><PERSON>", "quantity": 1, "weight": "1pc", "pack_size": 1, "mrp": "MRP: ₹100"}], "selectable": true, "quantityEditable": true}}, "nextActions": [{"actionType": "button", "text": "आगे बढ़ें", "nextScreen": "itemMissingQuestions"}]}, "itemMissingQuestions": {"id": "itemMissingQuestions", "component": {"type": "multipleQuestionsComponent", "properties": {"title": "सहायता केंद्र", "description": "ऑर्डर से जुड़े सवाल", "subtext": "3/3 पूरा हुआ", "questions": [{"id": "boxDamaged", "text": "क्या आपका बॉक्स बाहर से फटा हुआ था?", "options": [{"id": "yes", "text": "हाँ"}, {"id": "no", "text": "नहीं"}]}, {"id": "tapePresent", "text": "क्या आपके बॉक्स पे ब्रांडेड टेप के ऊपर ट्रांसपेरेंट टेप या कूरियर का टेप लगा था?", "options": [{"id": "yes", "text": "हाँ"}, {"id": "no", "text": "नहीं"}]}, {"id": "videoAvailable", "text": "क्या आपके पास बॉक्स को खोलते समय की वीडियो है?", "options": [{"id": "yes", "text": "हाँ"}, {"id": "no", "text": "नहीं"}]}]}}, "nextActions": [{"actionType": "button", "text": "जवाब जमा करें", "nextScreen": "itemMissingMedia"}]}, "itemMissingMedia": {"id": "itemMissingMedia", "component": {"type": "mediaUploadComponent", "properties": {"title": "फोटो/वीडियो अपलोड करें", "description": "बॉक्स खोलते समय का वीडियो या फोटो डालें", "subtext": "इससे हम जल्दी हेल्प कर पाएंगे", "allowedTypes": ["image", "video"]}}, "nextActions": [{"actionType": "button", "text": "आगे बढ़ें", "nextScreen": "itemMissingDescription"}]}, "itemMissingDescription": {"id": "itemMissingDescription", "component": {"type": "textFieldComponent", "properties": {"title": "समस्या को विस्तार से बताएं।", "description": "ज़्यादा जानकारी देने से हमें आपकी मदद करने में तेज़ी होगी।", "question": "आपका अनुमानित कितने का माल कम आया है?", "placeholder": "यहाँ लिखें...", "required": true}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "damagedProductDetail": {"id": "damagedProductDetail", "component": {"type": "optionsComponent", "properties": {"title": "डैमेज किस तरह का है?", "description": "डैमेज से जुड़ी सही चीज़ चुनें", "options": [{"id": "outsideBox", "text": "कूरियर बॉक्स बाहर से डैमेज्ड है", "nextScreen": "outsideBoxDamageMedia"}, {"id": "insideItems", "text": "अंदर का सामान डैमेज्ड आया है", "nextScreen": "insideItemsDamageDetail"}]}}}, "outsideBoxDamageMedia": {"id": "outsideBoxDamageMedia", "component": {"type": "mediaUploadComponent", "properties": {"title": "फोटो अपलोड करें", "description": "डैमेज्ड बॉक्स की फोटो डालें", "subtext": "फोटो क्लियर होगी तो जल्दी प्रोसेस होगा"}}, "nextActions": [{"actionType": "button", "text": "आगे बढ़ें", "nextScreen": "damagedProductQuestions"}]}, "damagedProductQuestions": {"id": "damagedProductQuestions", "component": {"type": "multipleQuestionsComponent", "properties": {"title": "सहायता केंद्र", "description": "ऑर्डर से जुड़े सवाल", "subtext": "3/3 पूरा हुआ", "questions": [{"id": "boxDamaged", "text": "क्या आपका बॉक्स बाहर से फटा हुआ था?", "options": [{"id": "yes", "text": "हाँ"}, {"id": "no", "text": "नहीं"}]}, {"id": "tapePresent", "text": "क्या आपके बॉक्स पे ब्रांडेड टेप के ऊपर ट्रांसपेरेंट टेप या कूरियर का टेप लगा था?", "options": [{"id": "yes", "text": "हाँ"}, {"id": "no", "text": "नहीं"}]}, {"id": "videoAvailable", "text": "क्या आपके पास बॉक्स को खोलते समय की वीडियो है?", "options": [{"id": "yes", "text": "हाँ"}, {"id": "no", "text": "नहीं"}]}]}}, "nextActions": [{"actionType": "button", "text": "जवाब जमा करें", "nextScreen": "outsideBoxDamageDescription"}]}, "outsideBoxDamageDescription": {"id": "outsideBoxDamageDescription", "component": {"type": "textFieldComponent", "properties": {"title": "डैमेज का डिटेल बताएं।", "description": "डैमेज्ड चीज़ के बारे में विस्तार से बताएं। ज़्यादा जानकारी देने से हमें आपकी मदद करने में तेज़ी होगी।", "placeholder": "यहाँ लिखें...", "question": "आपका अनुमानित कितने का ख़राब आया है?", "required": true}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "insideItemsDamageDetail": {"id": "insideItemsDamageDetail", "component": {"type": "productListingComponent", "properties": {"title": "डैमेज्ड आइटम्स चुनें", "description": "जो आइटम डैमेज्ड हैं वो चुनें", "subtext": "कृपया उन उत्पादों की मात्रा चुनें जो डैमेज्ड हैं", "products": [{"name": "<PERSON><PERSON><PERSON>", "quantity": 10, "weight": "200g", "pack_size": 5, "mrp": "MRP: ₹84"}, {"name": "<PERSON><PERSON><PERSON>", "quantity": 10, "weight": "100g", "pack_size": 10, "mrp": "MRP: ₹42"}, {"name": "<PERSON><PERSON>", "quantity": 1, "weight": "1pc", "pack_size": 1, "mrp": "MRP: ₹100"}], "selectable": true, "quantityEditable": true}}, "nextActions": [{"actionType": "button", "text": "आगे बढ़ें", "nextScreen": "insideItemsDamageQuestions"}]}, "insideItemsDamageQuestions": {"id": "insideItemsDamageQuestions", "component": {"type": "multipleQuestionsComponent", "properties": {"title": "सहायता केंद्र", "description": "ऑर्डर से जुड़े सवाल", "subtext": "3/3 पूरा हुआ", "questions": [{"id": "boxDamaged", "text": "क्या आपका बॉक्स बाहर से फटा हुआ था?", "options": [{"id": "yes", "text": "हाँ"}, {"id": "no", "text": "नहीं"}]}, {"id": "tapePresent", "text": "क्या आपके बॉक्स पे ब्रांडेड टेप के ऊपर ट्रांसपेरेंट टेप या कूरियर का टेप लगा था?", "options": [{"id": "yes", "text": "हाँ"}, {"id": "no", "text": "नहीं"}]}, {"id": "videoAvailable", "text": "क्या आपके पास बॉक्स को खोलते समय की वीडियो है?", "options": [{"id": "yes", "text": "हाँ"}, {"id": "no", "text": "नहीं"}]}]}}, "nextActions": [{"actionType": "button", "text": "जवाब जमा करें", "nextScreen": "insideItemsDamageMedia"}]}, "insideItemsDamageMedia": {"id": "insideItemsDamageMedia", "component": {"type": "mediaUploadComponent", "properties": {"title": "फोटो अपलोड करें", "description": "डैमेज्ड सामान की फोटो अपलोड करें", "subtext": "फोटो क्लियर होगी तो जल्दी प्रोसेस होगा"}}, "nextActions": [{"actionType": "button", "text": "आगे बढ़ें", "nextScreen": "insideItemsDamageDescription"}]}, "insideItemsDamageDescription": {"id": "insideItemsDamageDescription", "component": {"type": "textFieldComponent", "properties": {"title": "डैमेज का डिटेल बताएं।", "description": "क्या डैमेज हुआ है वो विस्तार से बताएं। ज़्यादा जानकारी देने से हमें आपकी मदद करने में तेज़ी होगी।", "placeholder": "यहाँ लिखें...", "question": "आपका अनुमानित कितने का ख़राब आया है?", "required": true}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "wrongItemDetail": {"id": "wrongItemDetail", "component": {"type": "productListingComponent", "properties": {"title": "गलत प्रोडक्ट चुनें", "description": "जो प्रोडक्ट नहीं आया है वो चुनें", "subtext": "कृपया उन उत्पादों की मात्रा चुनें जो आपको प्राप्त नहीं हुए हैं", "products": [{"name": "<PERSON><PERSON><PERSON>", "quantity": 10, "weight": "200g", "pack_size": 5, "mrp": "MRP: ₹84"}, {"name": "<PERSON><PERSON><PERSON>", "quantity": 10, "weight": "100g", "pack_size": 10, "mrp": "MRP: ₹42"}, {"name": "<PERSON><PERSON>", "quantity": 1, "weight": "1pc", "pack_size": 1, "mrp": "MRP: ₹100"}], "selectable": true, "quantityEditable": true}}, "nextActions": [{"actionType": "button", "text": "आगे बढ़ें", "nextScreen": "wrongItemQuestions"}]}, "wrongItemQuestions": {"id": "wrongItemQuestions", "component": {"type": "multipleQuestionsComponent", "properties": {"title": "सहायता केंद्र", "description": "ऑर्डर से जुड़े सवाल", "subtext": "3/3 पूरा हुआ", "questions": [{"id": "boxDamaged", "text": "क्या आपका बॉक्स बाहर से फटा हुआ था?", "options": [{"id": "yes", "text": "हाँ"}, {"id": "no", "text": "नहीं"}]}, {"id": "tapePresent", "text": "क्या आपके बॉक्स पे ब्रांडेड टेप के ऊपर ट्रांसपेरेंट टेप या कूरियर का टेप लगा था?", "options": [{"id": "yes", "text": "हाँ"}, {"id": "no", "text": "नहीं"}]}, {"id": "videoAvailable", "text": "क्या आपके पास बॉक्स को खोलते समय की वीडियो है?", "options": [{"id": "yes", "text": "हाँ"}, {"id": "no", "text": "नहीं"}]}]}}, "nextActions": [{"actionType": "button", "text": "जवाब जमा करें", "nextScreen": "wrongItemMedia"}]}, "wrongItemMedia": {"id": "wrongItemMedia", "component": {"type": "mediaUploadComponent", "properties": {"title": "फोटो/वीडियो अपलोड करें", "description": "गलत आइटम दिखाते हुए इमेज/वीडियो अपलोड करें", "subtext": "इससे हम जल्दी हेल्प कर पाएंगे", "allowedTypes": ["image", "video"]}}, "nextActions": [{"actionType": "button", "text": "आगे बढ़ें", "nextScreen": "wrongItemDescription"}]}, "wrongItemDescription": {"id": "wrongItemDescription", "component": {"type": "textFieldComponent", "properties": {"title": "समस्या को विस्तार से बताएं।", "description": "जो गलत प्रोडक्ट आया, वो बताएं", "placeholder": "यहाँ लिखें...", "question": "आपका अनुमानित कितने का गलत आया है?", "required": true}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "orderOfferDetail": {"id": "orderOfferDetail", "component": {"type": "optionsComponent", "properties": {"title": "ऑफर की समस्या चुनें", "description": "ऑफर से जुड़ी परेशानी चुनें", "options": [{"id": "cashbackNotApplied", "text": "कैशबैक नहीं मिला", "nextScreen": "cashbackDescription"}, {"id": "rewardPointsNotAdded", "text": "रिवॉर्ड पॉइंट्स नहीं मिले", "nextScreen": "rewardPointsDescription"}]}}}, "cashbackDescription": {"id": "cashbackDescription", "component": {"type": "textFieldComponent", "properties": {"title": "समस्या को विस्तार से बताएं।", "description": "ज़्यादा जानकारी देने से हमें आपकी मदद करने में तेज़ी होगी।", "placeholder": "यहाँ लिखें...", "required": true}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "rewardPointsDescription": {"id": "rewardPointsDescription", "component": {"type": "textFieldComponent", "properties": {"title": "समस्या को विस्तार से बताएं।", "description": "ज़्यादा जानकारी देने से हमें आपकी मदद करने में तेज़ी होगी।", "placeholder": "यहाँ लिखें...", "required": true}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "otherIssuesDetail": {"id": "otherIssuesDetail", "component": {"type": "optionsComponent", "properties": {"title": "कौन सी समस्या है?", "description": "अपनी समस्या का सही कारण चुनें", "options": [{"id": "orderCancel", "text": "ऑर्डर कैंसिल करना है", "nextScreen": "orderCancelDescription"}, {"id": "paymentIssue", "text": "पेमेंट या एडवांस रिलेटेड समस्या", "nextScreen": "paymentIssueDescription"}, {"id": "noDoorstep", "text": "डिलीवरी बॉय ने दरवाज़े तक देने से मना किया", "nextScreen": "orderStatusDescription"}, {"id": "otherGeneral", "text": "अन्य", "nextScreen": "otherGeneralDescription"}]}}}, "orderCancelDescription": {"id": "orderCancelDescription", "component": {"type": "textFieldComponent", "properties": {"title": "Order Cancel करने का कारण बताएं।", "description": "Cancel क्यों करना चाहते हैं?", "placeholder": "यहाँ लिखें...", "required": true}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "paymentIssueDescription": {"id": "paymentIssueDescription", "component": {"type": "textFieldComponent", "properties": {"title": "Payment/Advance की समस्या बताएं।", "description": "क्या दिक्कत आ रही है payment या advance में?", "placeholder": "यहाँ लिखें...", "required": true}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "otherGeneralDescription": {"id": "otherGeneralDescription", "component": {"type": "textFieldComponent", "properties": {"title": "समस्या को विस्तार से बताएं।", "description": "ज़्यादा जानकारी देने से हमें आपकी मदद करने में तेज़ी होगी।", "placeholder": "यहाँ लिखें...", "required": true}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "ticketSummary": {"id": "ticketSummary", "component": {"type": "ticketSummaryComponent", "properties": {"title": "फॉर्म सबमिट करने से पहले चेक करें", "description": "आपकी डाली गई डिटेल्स चेक कर लें", "ticketDetails": {}}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "thankYou"}]}, "thankYou": {"id": "thankYou", "component": {"type": "thankYouComponent", "properties": {"title": "धन्यवाद", "description": "आपकी शिकायत दर्ज हो गई है", "subtext": "हमारी टीम जल्द ही आपकी समस्या की समीक्षा करेगी और उसका समाधान करेगी। कृपया चिंता न करें।"}}, "nextActions": [{"actionType": "button", "text": "ब<PERSON><PERSON> करें", "nextScreen": null}]}}}}