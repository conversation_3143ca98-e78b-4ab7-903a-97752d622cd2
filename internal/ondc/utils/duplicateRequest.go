package utils

import (
	"encoding/json"
	"fmt"
	"hash/fnv"
	"sync"
	"time"
)

type Cache struct {
	mu    sync.Mutex
	store map[string]time.Time
	ttl   time.Duration
}

func NewCache(ttl time.Duration) *Cache {
	return &Cache{
		store: make(map[string]time.Time),
		ttl:   ttl,
	}
}

func (c *Cache) Add(hash string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.store[hash] = time.Now().Add(c.ttl)
}

func (c *Cache) Exists(hash string) bool {
	c.mu.Lock()
	defer c.mu.Unlock()

	expiry, exists := c.store[hash]
	if !exists {
		return false
	}
	if time.Now().After(expiry) {
		delete(c.store, hash) // Clean up expired entry
		return false
	}
	return true
}

func GenerateHash(request interface{}) (string, error) {
	body, err := json.Marshal(request)
	if err != nil {
		return "", err
	}
	hasher := fnv.New64a()
	hasher.Write(body)
	return fmt.Sprintf("%x", hasher.Sum(nil)), nil
}
