package utils

import (
	"encoding/json"
	"fmt"
	"kc/internal/ondc/models/dao"
	"kc/internal/ondc/models/shared"
	"math"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"
)

// Helper function to get meta field's type
func GetMetaFieldType(metaType reflect.Type, fieldName string) (string, error) {
	field, found := metaType.FieldByName(fieldName)
	if !found {
		return "", fmt.Errorf("field %s not found in meta struct", fieldName)
	}

	return GetTypeName(field.Type), nil
}

// Helper function to get type name
func GetTypeName(t reflect.Type) string {
	// Handle pointer types
	if t.Kind() == reflect.Ptr {
		return GetTypeName(t.Elem())
	}

	switch t.Kind() {
	case reflect.Bool:
		return "bool"
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return "int"
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return "uint"
	case reflect.Float32, reflect.Float64:
		return "float"
	case reflect.String:
		return "string"
	case reflect.Struct:
		if t == reflect.TypeOf(time.Time{}) {
			return "time"
		}
		return "struct"
	case reflect.Slice:
		return "slice"
	case reflect.Map:
		return "map"
	default:
		return t.Kind().String()
	}
}

// Enhanced function to convert and validate value types (now supports nested objects)
func ConvertToCorrectType(value interface{}, expectedType string) (interface{}, error) {
	switch expectedType {
	case "bool":
		switch v := value.(type) {
		case bool:
			return v, nil
		case string:
			if v == "true" {
				return true, nil
			} else if v == "false" {
				return false, nil
			}
		case float64:
			if v == 1 {
				return true, nil
			} else if v == 0 {
				return false, nil
			}
		}
		return nil, fmt.Errorf("expected boolean value")

	case "int", "uint":
		switch v := value.(type) {
		case int:
			return v, nil
		case float64:
			return int(v), nil
		case string:
			if i, err := strconv.Atoi(v); err == nil {
				return i, nil
			}
		}
		return nil, fmt.Errorf("expected integer value")

	case "float":
		switch v := value.(type) {
		case float64:
			return v, nil
		case int:
			return float64(v), nil
		case string:
			if f, err := strconv.ParseFloat(v, 64); err == nil {
				return f, nil
			}
		}
		return nil, fmt.Errorf("expected float value")

	case "string":
		switch v := value.(type) {
		case string:
			return strings.TrimSpace(v), nil
		case float64, int, bool:
			return strings.TrimSpace(fmt.Sprintf("%v", v)), nil
		}
		return nil, fmt.Errorf("expected string value")

	case "time":
		switch v := value.(type) {
		case string:
			if t, err := time.Parse(time.RFC3339, v); err == nil {
				return t, nil
			}
			if t, err := time.Parse("2006-01-02 15:04:05", v); err == nil {
				return t, nil
			}
		}
		return nil, fmt.Errorf("expected time value in RFC3339 or MySQL datetime format")

	case "slice":
		// Handle array/slice types
		switch v := value.(type) {
		case []interface{}:
			return v, nil
		case []string:
			// Convert to []interface{} for consistency
			result := make([]interface{}, len(v))
			for i, item := range v {
				result[i] = item
			}
			return result, nil
		case string:
			// Try to parse JSON array
			var arr []interface{}
			if err := json.Unmarshal([]byte(v), &arr); err == nil {
				return arr, nil
			}
			return nil, fmt.Errorf("expected array value or valid JSON array string")
		}
		return nil, fmt.Errorf("expected array value")

	case "map":
		// Handle map/object types
		switch v := value.(type) {
		case map[string]interface{}:
			return v, nil
		case string:
			// Try to parse JSON object
			var obj map[string]interface{}
			if err := json.Unmarshal([]byte(v), &obj); err == nil {
				return obj, nil
			}
			return nil, fmt.Errorf("expected object value or valid JSON object string")
		}
		return nil, fmt.Errorf("expected object value")

	default:
		// For unknown types, validate that the value is JSON-serializable
		if _, err := json.Marshal(value); err != nil {
			return nil, fmt.Errorf("value is not JSON-serializable: %v", err)
		}
		// Just pass the value through for unknown types
		return value, nil
	}
}

// FormatINR formats a float64 to a string with ₹ and up to two decimals (no trailing .00)
func FormatINR(value float64) string {
	if value == math.Trunc(value) {
		// No fractional part
		return fmt.Sprintf("₹ %.0f", value)
	}
	// Has fractional part, format with up to 2 decimal places
	return fmt.Sprintf("₹ %.2f", value)
}

func GetProductBannerImageUrls(mediaUrls []dao.KiranaBazarProductMediaUrl, sortVideoFirst bool) []shared.BannerImageUrls {
	bannerImageUrls := make([]shared.BannerImageUrls, 0)
	for _, media := range mediaUrls {
		if media.Url != "" {
			url := media.Url
			videoUrl := ""
			mediaType := ""
			if media.VideoUrl != nil && *media.VideoUrl != "" {
				videoUrl = *media.VideoUrl
				mediaType = "video"
			}
			bannerImageUrls = append(bannerImageUrls, shared.BannerImageUrls{
				Url:      url,
				Type:     mediaType,
				VideoUrl: videoUrl,
			})
		}
	}

	if sortVideoFirst {
		// Sort to ensure videos come first
		sort.Slice(bannerImageUrls, func(i, j int) bool {
			return bannerImageUrls[i].VideoUrl != "" && bannerImageUrls[j].VideoUrl == ""
		})
	}
	return bannerImageUrls
}

func GetManufacturerCartValue(productsPricing []shared.ProductPrice, manufacturers []string) float64 {
	totalProductPrice := 0.0
	for _, pric := range productsPricing {
		if !includes(manufacturers, pric.Manufacturer) {
			continue
		}
		tval, _ := strconv.ParseFloat(pric.TotalValue, 32)
		totalProductPrice += tval
	}
	return totalProductPrice
}