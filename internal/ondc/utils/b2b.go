package utils

import (
	"fmt"
	"hash/fnv"
)

func GetLabelColor(label string) (backgroundColor string, textColor string) {
	// Use a simple hash function to convert the label to an integer
	h := fnv.New32a()
	h.Write([]byte(label))
	hash := h.Sum32()
	
	// Convert the hash to a hex color code for background
	// Using modulo to keep colors in a visually pleasing range
	r := (hash & 0xFF) % 200 + 55          // Red component (55-255)
	g := ((hash >> 8) & 0xFF) % 200 + 55    // Green component (55-255)
	b := ((hash >> 16) & 0xFF) % 200 + 55   // Blue component (55-255)
	
	backgroundColor = fmt.Sprintf("#%02x%02x%02x", r, g, b)
	
	// Calculate luminance to determine if we need dark or light text base
	luminance := 0.299*float64(r) + 0.587*float64(g) + 0.114*float64(b)
	
	// Generate a custom text color based on the background
	// Use complementary colors (opposite on the color wheel)
	var tr, tg, tb uint32
	
	if luminance > 150 {
		// For light backgrounds, use darker complementary colors
		tr = (255 - r + 20) % 200
		tg = (255 - g + 20) % 200
		tb = (255 - b + 20) % 200
	} else {
		// For dark backgrounds, use lighter complementary colors
		tr = (255 - r + 150) % 255
		tg = (255 - g + 150) % 255
		tb = (255 - b + 150) % 255
	}
	
	// Ensure good contrast by adjusting brightness
	if luminance > 150 {
		// Make text darker for light backgrounds
		tr = tr * 60 / 100
		tg = tg * 60 / 100
		tb = tb * 60 / 100
	} else {
		// Make text lighter for dark backgrounds
		tr = tr * 100 / 100 + 55
		tg = tg * 100 / 100 + 55
		tb = tb * 100 / 100 + 55
	}
	
	// Clamp values to valid RGB range
	tr = min(tr, 255)
	tg = min(tg, 255)
	tb = min(tb, 255)
	
	textColor = fmt.Sprintf("#%02x%02x%02x", tr, tg, tb)
	
	return backgroundColor, textColor
}

// Helper function for min
func min(a, b uint32) uint32 {
	if a < b {
		return a
	}
	return b
}
