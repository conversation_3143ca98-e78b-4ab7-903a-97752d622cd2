{"version": "1.3", "flowId": "fofdPlusOneFlow", "components": {"optionsComponent": {"type": "options", "properties": {"title": "", "description": "", "subtext": "", "options": []}}, "textFieldComponent": {"type": "textField", "properties": {"title": "", "description": "", "subtext": "", "placeholder": "", "maxLength": 500, "required": true}}, "ticketSummaryComponent": {"type": "ticketSummary", "properties": {"title": "", "description": "", "subtext": "", "ticketDetails": {}}}, "thankYouComponent": {"type": "thankYou", "properties": {"title": "", "description": "", "subtext": "", "ticketId": ""}}}, "flow": {"start": "deliveryCallStatus", "screens": {"deliveryCallStatus": {"id": "deliveryCallStatus", "component": {"type": "optionsComponent", "properties": {"title": "क्या आपको डिलीवरी एजेंट का कॉल आया?", "description": "कृपया बताएं कि क्या डिलीवरी एजेंट ने आपसे संपर्क किया है", "options": [{"id": "deliveryCallStatusYes", "text": "हाँ", "nextScreen": "deliveryDifficulty"}, {"id": "deliveryCallStatusNo", "text": "नहीं, डिलीवरी एजेंट ने कॉल नहीं किया", "nextScreen": "orderUrgency"}, {"id": "cancelOrder", "text": "Order Cancel करना है\nआगे से आपको डिलीवरी चार्ज लग सकता है", "nextScreen": "cancelOrderDescription"}]}}}, "deliveryDifficulty": {"id": "deliveryDifficulty", "component": {"type": "optionsComponent", "properties": {"title": "क्या आपको डिलीवरी लेने में कोई दिक्कत आ रही है?", "description": "डिलीवरी से जुड़ी समस्या बताएं", "options": [{"id": "deliveryRefusing", "text": "डिलीवरी एजेंट आने से मना कर रहा है", "nextScreen": "deliveryRefusingDescription"}, {"id": "noIssueNeedAsap", "text": "नहीं, कोई दिक्कत नहीं है. डिलीवरी एजेंट से बात हो गयी है", "nextScreen": "ticketSummary"}]}}}, "orderUrgency": {"id": "orderUrgency", "component": {"type": "optionsComponent", "properties": {"title": "आपको ऑर्डर कब चाहिए?", "description": "अपनी जरूरत के अनुसार चुनें", "options": [{"id": "asapDelivery", "text": "जल्द से जल्द", "nextScreen": "ticketSummary"}, {"id": "customerCareConnect", "text": "Customer Care से बात करनी है", "nextScreen": "customerCareDescription"}]}}}, "cancelOrderDescription": {"id": "cancelOrderDescription", "component": {"type": "textFieldComponent", "properties": {"title": "ऑर्डर कैंसल करने का कारण बताएं", "description": "कृपया विस्तार से बताएं कि आप ऑर्डर क्यों कैंसल करना चाहते हैं", "placeholder": "यहाँ लिखें...", "required": true}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "deliveryRefusingDescription": {"id": "deliveryRefusingDescription", "component": {"type": "textFieldComponent", "properties": {"title": "समस्या को विस्तार से बताएं", "description": "डिलीवरी एजेंट क्यों आने से मना कर रहा है, इसकी जानकारी दें", "placeholder": "यहाँ लिखें...", "required": false}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "urgentOrderDescription": {"id": "urgentOrderDescription", "component": {"type": "textFieldComponent", "properties": {"title": "अतिरिक्त जानकारी दें", "description": "ऑर्डर की जल्दी जरूरत के बारे में कोई खास बात बताना चाहते हैं?", "placeholder": "यहाँ लिखें...", "required": false}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "asapDescription": {"id": "asapDescription", "component": {"type": "textFieldComponent", "properties": {"title": "समस्या को विस्तार से बताएं", "description": "ऑर्डर जल्दी चाहिए होने का कारण और कोई अन्य जानकारी दें", "placeholder": "यहाँ लिखें...", "required": false}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "customerCareDescription": {"id": "customerCareDescription", "component": {"type": "textFieldComponent", "properties": {"title": "समस्या को विस्तार से बताएं", "description": "समस्या बताए, हम आपसे जल्द संपर्क करेंगे", "placeholder": "यहाँ लिखें...", "required": true}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "ticketSummary"}]}, "ticketSummary": {"id": "ticketSummary", "component": {"type": "ticketSummaryComponent", "properties": {"title": "फॉर्म सबमिट करने से पहले चेक करें", "description": "आपकी डाली गई डिटेल्स चेक कर लें", "ticketDetails": {}}}, "nextActions": [{"actionType": "button", "text": "सबमिट करें", "nextScreen": "thankYou"}]}, "thankYou": {"id": "thankYou", "component": {"type": "thankYouComponent", "properties": {"title": "धन्यवाद", "description": "आपकी शिकायत दर्ज हो गई है", "subtext": "हमारी टीम जल्द ही आपकी समस्या की समीक्षा करेगी और उसका समाधान करेगी। कृपया चिंता न करें।"}}, "nextActions": [{"actionType": "button", "text": "ब<PERSON><PERSON> करें", "nextScreen": null}]}}}}