package exceptions

import "fmt"

const (
	InvalidRequestStruct ServerErrorCode = 1001
	MarshallingError     ServerErrorCode = 1002
	NoClaimsFound        ServerErrorCode = 1004
	ParsingError         ServerErrorCode = 1005
	StrconvError         ServerErrorCode = 1006
	UnmarshallingError   ServerErrorCode = 1007

	//DB and Cache Errors
	DBSaveError           ServerErrorCode = 2001
	DBReadError           ServerErrorCode = 2002
	DBUpdateError         ServerErrorCode = 2003
	DBDeleteError         ServerErrorCode = 2004
	DBConnectionError     ServerErrorCode = 2005
	DBRecordNotFoundError ServerErrorCode = 2006
	DBDuplicateKeyError   ServerErrorCode = 2007
	DBInvalidSQLError     ServerErrorCode = 2008

	//External Errors
	ImageDownloadError         ServerErrorCode = 3001
	ImageUploadError           ServerErrorCode = 3002
	BucketCreationError        ServerErrorCode = 3003
	GcpClientCreationError     ServerErrorCode = 3004
	URLParsingError            ServerErrorCode = 3005
	HTTPCallError              ServerErrorCode = 3006
	HTTPInvalidStatusCodeError ServerErrorCode = 3007

	// Auth Error
	JWTTokenNotFound         ServerErrorCode = 4001
	JWTTokenExpired          ServerErrorCode = 4002
	JWTTokenInvalid          ServerErrorCode = 4003
	VerificationTokenExpired ServerErrorCode = 4006
	VerificationTokenInvalid ServerErrorCode = 4007
	InvalidCredentials       ServerErrorCode = 4004
	UserNotFound             ServerErrorCode = 4005
	ForbiddenAction          ServerErrorCode = 4006
	RateLimitExceeded        ServerErrorCode = 4007
	MissingAuthToken         ServerErrorCode = 4008
	InvalidTokenFormat       ServerErrorCode = 4009
	InvalidAuthToken         ServerErrorCode = 4010
	RoleNotFound             ServerErrorCode = 4011
	InvalidRole              ServerErrorCode = 4012
	InsufficientPermissions  ServerErrorCode = 4013

	// ONDC Gateway Errors
	BadOrInvalidRequest     ServerErrorCode = 10000
	InvalidGatewaySignature ServerErrorCode = 10001
	InvalidCityCode         ServerErrorCode = 10002

	// ONDC Buyer App Errors
	InvalidCatalog                 ServerErrorCode = 20000
	InvalidSignature               ServerErrorCode = 20001
	StaleRequest                   ServerErrorCode = 20002
	ProviderNotFound               ServerErrorCode = 20003
	ProviderLocationNotFound       ServerErrorCode = 20004
	ItemNotFound                   ServerErrorCode = 20005
	InvalidResponse                ServerErrorCode = 20006
	InvalidOrderState              ServerErrorCode = 20007
	ResponseOutOfSequence          ServerErrorCode = 20008
	Timeout                        ServerErrorCode = 20009
	FeatureNotSupported            ServerErrorCode = 21001
	IncreaseInItemQuantity         ServerErrorCode = 21002
	ChangeInItemQuote              ServerErrorCode = 21003
	PartFillUnacceptable           ServerErrorCode = 22501
	CancellationUnacceptable1      ServerErrorCode = 22502
	CancellationUnacceptable2      ServerErrorCode = 22503
	InvalidFulfillmentTAT          ServerErrorCode = 22504
	InvalidCancellationTerms       ServerErrorCode = 22505
	InvalidTermsOfReference        ServerErrorCode = 22506
	InvalidQuote                   ServerErrorCode = 22507
	InvalidPartCancelRequest       ServerErrorCode = 22508
	CancelReturnRequest            ServerErrorCode = 22509
	InternalError                  ServerErrorCode = 23001
	OrderValidationFailure         ServerErrorCode = 23002
	OrderConfirmFailure            ServerErrorCode = 25001
	TermsAndConditionsUnacceptable ServerErrorCode = 27501
	OrderTerminated                ServerErrorCode = 27502
)

const (
	OosErrorMessage string = "माफ़ करें इनमे से कुछ सामान अभी हमारे स्टॉक में नहीं है।"
)

func GenerateNewServerError(code ServerErrorCode, actualErr error, msg string, httpcode int) ServerError {
	return ServerError{
		Code:      code,
		ActualErr: actualErr,
		Msg:       fmt.Sprintf("%s::%v", msg, actualErr),
		HttpCode:  httpcode,
	}
}
