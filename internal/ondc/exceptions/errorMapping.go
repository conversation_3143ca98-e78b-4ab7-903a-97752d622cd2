package exceptions

import (
	"encoding/json"
	"io"
	"os"
)

// ONDC error codes mapping
// @sanket handle all the error codes here

type ondcErrors struct {
	ErrorCode        int    `json:"error_code"`
	ErrorFrom        string `json:"error_from"`
	ErrorMessage     string `json:"error_message"`
	ErrorDescription string `json:"error_description"`
}

var ONDCErrorCodes = make(map[int]ondcErrors)

func CreateONDCErrorMapping() error {
	jsonFile, err := os.Open("users.json")
	if err != nil {
		return err
	}
	defer jsonFile.Close()
	bytvalue, err := io.ReadAll(jsonFile)
	if err != nil {
		return err
	}
	var ondcError []ondcErrors
	err = json.Unmarshal(bytvalue, &ondcError)
	if err != nil {
		return err
	}
	for _, v := range ondcError {
		ONDCErrorCodes[v.ErrorCode] = v
	}
	return nil
}
