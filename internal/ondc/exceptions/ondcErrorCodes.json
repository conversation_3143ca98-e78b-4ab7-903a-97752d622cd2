[{"error_code": 10000, "error_from": "Gateway", "error_message": "Bad or Invalid request error", "error_description": "Generic bad or invalid request error"}, {"error_code": 10001, "error_from": "Gateway", "error_message": "Invalid Signature", "error_description": "Cannot verify signature for request"}, {"error_code": 10002, "error_from": "Gateway", "error_message": "Invalid City Code", "error_description": "Valid city code needs to be passed for search"}, {"error_code": 20000, "error_from": "Buyer App", "error_message": "Invalid catalog", "error_description": "Catalog refresh is invalid"}, {"error_code": 20001, "error_from": "Buyer App", "error_message": "Invalid Signature", "error_description": "Cannot verify signature for response"}, {"error_code": 20002, "error_from": "Buyer App", "error_message": "Stale Request", "error_description": "Cannot process stale request"}, {"error_code": 20003, "error_from": "Buyer App", "error_message": "Provider not found", "error_description": "Provider not found"}, {"error_code": 20004, "error_from": "Buyer App", "error_message": "Provider location not found", "error_description": "Provider location not found"}, {"error_code": 20005, "error_from": "Buyer App", "error_message": "Item not found", "error_description": "Item not found"}, {"error_code": 20006, "error_from": "Buyer App", "error_message": "Invalid response", "error_description": "Invalid response does not meet API contract specifications"}, {"error_code": 20007, "error_from": "Buyer App", "error_message": "Invalid order state", "error_description": "Order/fulfillment state is stale or not valid"}, {"error_code": 20008, "error_from": "Buyer App", "error_message": "Response out of sequence", "error_description": "Callback received prior to ACK for request or out of sequence"}, {"error_code": 20009, "error_from": "Buyer App", "error_message": "Timeout", "error_description": "<PERSON><PERSON> received late, session timed out"}, {"error_code": 21001, "error_from": "Buyer App", "error_message": "Feature not supported", "error_description": "Feature not supported"}, {"error_code": 21002, "error_from": "Buyer App", "error_message": "Increase in item quantity", "error_description": "Increase in item quantity"}, {"error_code": 21003, "error_from": "Buyer App", "error_message": "Change in item quote", "error_description": "Change in item quote without change in quantity"}, {"error_code": 22501, "error_from": "Buyer App", "error_message": "Part Fill Unacceptable", "error_description": "Buyer doesn't accept part fill for the order, wants to cancel the order"}, {"error_code": 22502, "error_from": "Buyer App", "error_message": "Cancellation unacceptable", "error_description": "Invalid cancellation reason"}, {"error_code": 22503, "error_from": "Buyer App", "error_message": "Cancellation unacceptable", "error_description": "Updated quote does not match original order value and cancellation terms"}, {"error_code": 22504, "error_from": "Buyer App", "error_message": "Invalid Fulfillment TAT", "error_description": "Fulfillment TAT is different from what was quoted earlier"}, {"error_code": 22505, "error_from": "Buyer App", "error_message": "Invalid Cancellation Terms", "error_description": "Cancellation terms are different from what was quoted earlier"}, {"error_code": 22506, "error_from": "Buyer App", "error_message": "Invalid Terms of Reference", "error_description": "Terms of Reference are different from what was quoted earlier"}, {"error_code": 22507, "error_from": "Buyer App", "error_message": "<PERSON><PERSON><PERSON> Quote", "error_description": "Quote is invalid as it does not meet the API contract specifications"}, {"error_code": 22508, "error_from": "Buyer App", "error_message": "Invalid Part Cancel Request", "error_description": "Part cancel request is invalid"}, {"error_code": 22509, "error_from": "Buyer App", "error_message": "Cancel Return Request", "error_description": "Buyer cancelling return request"}, {"error_code": 23001, "error_from": "Buyer App", "error_message": "Internal Error", "error_description": "Cannot process response due to internal error, please retry"}, {"error_code": 23002, "error_from": "Buyer App", "error_message": "Order validation failure", "error_description": "Order validation failure"}, {"error_code": 25001, "error_from": "Buyer App", "error_message": "Order Confirm Failure", "error_description": "Buyer <PERSON><PERSON> cannot confirm order as no response from <PERSON><PERSON>pp"}, {"error_code": 27501, "error_from": "Buyer App", "error_message": "Terms and Conditions unacceptable", "error_description": "Seller App terms and conditions not acceptable to Buyer App"}, {"error_code": 27502, "error_from": "Buyer App", "error_message": "Order terminated", "error_description": "Order terminated as <PERSON>ller App did not accept terms and conditions proposed by Buyer App"}]