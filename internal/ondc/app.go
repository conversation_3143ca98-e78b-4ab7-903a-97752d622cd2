package ondc

import (
	"context"
	"fmt"
	"kc/internal/ondc/api"
	"kc/internal/ondc/cache"
	"kc/internal/ondc/config"
	"kc/internal/ondc/crons"
	"kc/internal/ondc/external/easyecom"
	"kc/internal/ondc/external/exotel/ivr"
	"kc/internal/ondc/external/godesi"
	"kc/internal/ondc/external/icarry"
	"kc/internal/ondc/external/panchvati"
	"kc/internal/ondc/external/razorpay"
	"kc/internal/ondc/external/shiprocket"
	"kc/internal/ondc/external/shipway"
	"kc/internal/ondc/external/slack"
	"kc/internal/ondc/external/unicommerce"
	"kc/internal/ondc/external/zoho"
	infraCache "kc/internal/ondc/infrastructure/cache"
	"kc/internal/ondc/infrastructure/datastore"
	"kc/internal/ondc/infrastructure/firebase"
	"kc/internal/ondc/infrastructure/keyClient"
	"kc/internal/ondc/infrastructure/mixpanel"
	"kc/internal/ondc/infrastructure/payments"
	internalLog "kc/internal/ondc/log"
	"kc/internal/ondc/middleware"
	"kc/internal/ondc/queue"
	"kc/internal/ondc/redis"
	"kc/internal/ondc/repositories/bifrostRepo"
	"kc/internal/ondc/repositories/cacheRepo"
	"kc/internal/ondc/repositories/elasticRepo"
	"kc/internal/ondc/repositories/firebaseRepo"
	"kc/internal/ondc/repositories/mixpanelRepo"
	"kc/internal/ondc/repositories/sqlRepo"
	"kc/internal/ondc/routes"
	"kc/internal/ondc/service"
	lspAllocation "kc/internal/ondc/service/LSPAllocation"
	"kc/internal/ondc/service/auth"
	"kc/internal/ondc/service/awbMaster/service/impl"
	"kc/internal/ondc/service/brands"
	"kc/internal/ondc/service/cart"
	"kc/internal/ondc/service/coupons"
	"kc/internal/ondc/service/inventory"
	"kc/internal/ondc/service/kcFinances/finance"
	"kc/internal/ondc/service/logistics/couriers"
	ordervalue "kc/internal/ondc/service/orderBill/orderValue"
	ordercomm "kc/internal/ondc/service/orderComm"
	orderstatus "kc/internal/ondc/service/orderStatus"
	ordertags "kc/internal/ondc/service/orderTags"
	postdelivery "kc/internal/ondc/service/postDelivery"
	"kc/internal/ondc/service/products"
	"kc/internal/ondc/syncify"
	"kc/internal/ondc/utils"
	"log"
	"os"
	"time"

	tplgo "github.com/Kirana-Club/3pl-go"
	"github.com/Kirana-Club/oms-go"
	"github.com/Kirana-Club/oms-go/pkg/models"
	"github.com/prometheus/client_golang/prometheus"
	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type App struct {
	router       *gin.Engine
	myService    *service.Service
	cronService  *crons.Service
	promRegistry *prometheus.Registry
}

func NewApp(router *gin.Engine, myService *service.Service, cronService *crons.Service, env string, promRegistry *prometheus.Registry) *App {
	app := &App{
		router:       router,
		myService:    myService,
		cronService:  cronService,
		promRegistry: promRegistry,
	}

	app.registerRoutes()
	if env == "prod" {
		app.registerCrons()
	}

	return app
}

func (app *App) Run(addr string) error {
	return app.router.Run(addr)
}

// register Crons initiates all the crons
func (app *App) registerCrons() {
	app.cronService.CronsInit()
}

func (app *App) registerRoutes() {
	handler := api.NewHandler(app.myService)
	routes.SetupRoutes(app.router, handler, app.promRegistry)
}

func initializeOMS(db *gorm.DB) (*oms.Service, error) {
	config := models.Config{
		DB:              db,
		SlackWebhookURL: "*********************************************************************************",
		SlackChannel:    "#ordering-operations",
		DefaultTimeout:  30 * time.Second,
		SellerConfigs: map[string]models.SellerProviderConfig{
			"soothe": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "soothe",
						},
					},
				},
			},
			"cravitos": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "cravitos",
						},
					},
				},
			},
			"michis": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "michis",
						},
					},
				},
			},
			"milden": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "milden",
						},
					},
				},
			},
			"hugs": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "hugs",
						},
					},
				},
			},
			"apsara_tea": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "apsara_tea",
						},
					},
				},
			},
			"mothers_kitchen": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "mothers_kitchen",
						},
					},
				},
			},
			"panchvati": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "panchvati",
						},
					},
				},
			},
			"rsb_super_stockist": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "rsb_super_stockist",
						},
					},
				},
			},
			"kirana_club": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "kirana_club",
						},
					},
				},
			},
			"bolas": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "bolas",
						},
					},
				},
			},
			"go_desi": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "go_desi",
						},
					},
				},
			},
			"zoff_foods": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "zoff_foods",
						},
					},
				},
			},
			"chuk_de": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "chuk_de",
						},
					},
				},
			},
			"kiranaclub_loyalty_rewards": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "kiranaclub_loyalty_rewards",
						},
					},
				},
			},
			"candylake": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "candylake",
						},
					},
				},
			},
			"sugandh": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "sugandh",
						},
					},
				},
			},
			"somnath": {
				Providers: map[string]models.ProviderConfig{
					"shipdelight": {
						ShipDelight: &models.ShipDelightConfig{
							BaseURL: "https://appapi.shipdelight.com",
							APIKey:  "67bd911799a40e66741d2f12",
							Seller:  "somnath",
						},
					},
				},
			},
		},
	}
	return oms.NewService(config)
}

func initializeTPL(db *gorm.DB) (*tplgo.Service, error) {
	// TPL_CONFIG is expected to contain absolute path to the config file
	tplConfigPath := os.Getenv("TPL_CONFIG")
	if tplConfigPath == "" {
		return nil, fmt.Errorf("TPL_CONFIG is not set")
	}

	// Create a TPL service instance using the new approach
	tplService, err := tplgo.NewService(tplConfigPath, db)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize TPL service: %v", err)
	}
	return tplService, nil
}

func InitializeApp() (*App, string) {
	// Initialize necessary dependencies like database, cache, logging, etc.

	// Create a router instance
	defaultLogger := logrus.New()
	router := gin.Default()
	router.LoadHTMLGlob("internal/ondc/templates/*")
	// Initialize and configure the database connection
	appConfig, ivrConfig := config.LoadConfig(defaultLogger)
	dbConfig := appConfig.DbConfig
	err := easyecom.InitEasyEcom(*appConfig.EasyEcom)
	if err != nil {
		log.Panic("not able to init easy ecom")
	}
	err = shiprocket.InitShipRocket(*appConfig.ShipRocketConfig)
	if err != nil {
		log.Panic("not able to initialise shiprocket")
	}
	err = shipway.InitShipway(*appConfig.ShipwayConfig)
	if err != nil {
		log.Panic("not able to initialise shipway")
	}
	err = icarry.InitIcarryConfig(*appConfig.IcarryConfig)
	if err != nil {
		log.Panic("not able to initilise icarry")
	}
	err = icarry.PreloadMothersKitchenServiceblePincode()
	if err != nil {
		log.Panic("not able to preload mothers kitchen delivery pincodes")
	}
	err = godesi.PreLoadGoDesiServiceAbilityPincode()
	if err != nil {
		log.Panic("not able to preload godesi delivery pincodes")
	}
	err = panchvati.PreLoadPanchvatiServiceAbilityPincode()
	if err != nil {
		log.Panic("not able to preload panchvati delivery pincodes")
	}
	err = utils.PreLoadUserActivationCohortMapping()
	if err != nil {
		log.Panic("not able to preload user activation cohort mapping")
	}
	err = unicommerce.InitUnicommerce(*appConfig.UnicommerceConfig)
	if err != nil {
		log.Panic("not able to init unicommerce")
	}
	ivr.CreateSellerIVRData(*ivrConfig)

	db := datastore.GetDatastore(dbConfig.Hostname, dbConfig.Username, dbConfig.Password, dbConfig.Database, dbConfig.Port, dbConfig.MaxOpenConnections, defaultLogger)
	financedb := datastore.GetDatastore(dbConfig.Hostname, dbConfig.Username, dbConfig.Password, "kc_finances", dbConfig.Port, dbConfig.MaxOpenConnections, defaultLogger)
	readOnlyDB := datastore.GetDatastore(`kiranaclub-sql-server-2-replica-1.mysql.database.azure.com`, dbConfig.Username, dbConfig.Password, dbConfig.Database, dbConfig.Port, dbConfig.MaxOpenConnections, defaultLogger)
	repo := sqlRepo.NewRepository(db)
	financeRepo := sqlRepo.NewRepository(financedb)
	readOnlyRepo := sqlRepo.NewRepository(readOnlyDB)
	firebaseApp := firebase.GetNewFirebaseApp(defaultLogger, appConfig.FirebaseKeyPath)
	fbRepo := firebaseRepo.NewFirebaseRepo(firebaseApp, appConfig.FirebaseMetaDbUrl)

	mixpanel := mixpanel.MixpanelInit("36e90f794ab91b0700637434bc8c1ace")
	bifrostRepo.SetEnvForRabbitMQ(appConfig.RabbitMQConfig.Hostname, appConfig.RabbitMQConfig.Port, appConfig.RabbitMQConfig.Username, appConfig.RabbitMQConfig.Password)
	bifrostRepo.GetInstance()

	redisCache := infraCache.NewCache(appConfig.RedisConfig.Hostname, appConfig.RedisConfig.Password)
	zohoNewClient := zoho.NewClient(appConfig.ZohoConfig)
	azureRedis := cacheRepo.NewRepository(redisCache.RedisClient)
	mixpanelRepository := mixpanelRepo.NewRepository(mixpanel)
	scyncifyClient := syncify.NewSyncifyClient(azureRedis.RedisClient)
	kClient, _ := keyClient.New(context.Background(), appConfig.Onboarding.ProjectID, appConfig.Onboarding.SecretID, nil)
	gcpRedis := redis.NewGcpRedisClient(appConfig.GcpRedisConfig.Host, appConfig.GcpRedisConfig.Port)

	// Initialize cache with Redis client
	genericCache := cache.GetInstanceWithConfig(cache.DefaultConfig(), azureRedis)

	brands.NewBrandCache(repo, appConfig.Env)
	products.InitializeService(repo)

	//brands

	postdelivery.InitRatingCache()
	ordercomm.NewOrderCommService(azureRedis.RedisClient, repo, mixpanelRepository)

	paymentService := payments.NewService()
	razorpayGateway := razorpay.NewRazorpayGateway(appConfig.PaymentConfig.RazorpayConfig.KeyID, appConfig.PaymentConfig.RazorpayConfig.KeySecret, appConfig.PaymentConfig.RazorpayConfig.WHSecret)
	paymentService.RegisterGateway(razorpayGateway)
	err = paymentService.SetDefaultGateway(razorpayGateway.Name())
	if err != nil {
		log.Panic("not able to set default gateway")
	}

	lspAllocationService := lspAllocation.NewLSPAllocationService()

	omsService, err := initializeOMS(repo.Db)
	if err != nil {
		log.Panic("not able to initiliase oms service")
	}

	tplService, err := initializeTPL(repo.Db)
	if err != nil {
		log.Panic("not able to initialize tpl service: %v", err)
	}

	financialService := finance.NewService(financeRepo.Db)

	couponsService := coupons.NewRepository(repo, coupons.NewMemoryCache(30*time.Minute))

	awbMasterService, err := impl.NewAWBMasterService(impl.ServiceConfig{
		RedisClient:        azureRedis.RedisClient,
		Repository:         repo,
		ReadOnlyRepository: readOnlyRepo,
		Mixpanel:           mixpanelRepository,
		FirebaseRepo:       fbRepo,
		TPLService:         tplService,
	}, appConfig.Env)
	if err != nil {
		log.Panic("not able to initilise the awbMasterService")
	}

	// Initialize the service
	elasticSearchClient, err := elasticRepo.NewElasticSearchClient([]string{appConfig.ElasticConfig.Host}, appConfig.ElasticConfig.Username, appConfig.ElasticConfig.Password)
	if err != nil {
		slack.SendSlackMessage("Not Able to Intialise Elastic Search Client. Search will not work.")
	}

	inventoryService := inventory.NewInventoryService(repo, mixpanelRepository)

	// auth service for b2b apis
	authRepo := auth.NewRepository(db)
	authSvc, err := auth.NewService([]byte(appConfig.AuthConfig.JwtSecret), authRepo)
	if err != nil {
		slack.SendSlackMessage(fmt.Sprintf(`Auth Service Not Initialised %v.`, err))
	}

	myService := service.NewService(repo, fbRepo, kClient, azureRedis, scyncifyClient, mixpanelRepository,
		zohoNewClient, gcpRedis, paymentService, couponsService, awbMasterService, elasticSearchClient,
		omsService, tplService, financialService, lspAllocationService, inventoryService, readOnlyRepo,
		genericCache, authSvc)

	internalLog.SqlRespository = repo
	orderstatus.SqlRespository = repo
	shipway.SqlRespository = repo
	ordervalue.NewSellerMinOrderService(repo)
	couriers.NewCourierService()
	cart.InitializeCartService(azureRedis.RedisClient, repo, mixpanelRepository, fbRepo)

	err = utils.PreloadDelhiveryServiceablePincodeMap(azureRedis)
	if err != nil {
		log.Panic("not able to preload delhivery serviceable pincode")
	}

	// initiatize the crons service
	cronService := crons.NewService(repo, kClient, mixpanelRepository, myService)

	// preloading all active products for search
	utils.PreloadProductsForSearch(repo)
	ordertags.LoadKiranaBazarOrderTags(repo)
	// Create a mapping of the ondc errors
	// exceptions.CreateONDCErrorMapping()

	// maxSize, batchSize
	newIVRQueue := queue.NewQueue(5000, 10)
	// defer newQueue.Stop()
	queue.IVRQueueInstance = newIVRQueue

	newOrderCancelQueue := queue.NewQueue(1000, 10)
	queue.OrderCancelQueueInstance = newOrderCancelQueue

	newPaymentQueue := queue.NewQueue(2000, 10)
	queue.TimedFunctionsQueueInstance = newPaymentQueue

	if appConfig.Env == "prod" {
		queue.PrefillQueueData(repo, mixpanelRepository)
		myService.PrefillQueueData(repo, mixpanelRepository)
	}

	// Initialize the middleware for prometheus
	promRegistry := middleware.PrometheusInit()
	middleware.SetAuthService(authSvc)
	middleware.InitRateLimiter(5 * time.Minute)

	// Create the app instance
	app := NewApp(router, myService, cronService, appConfig.Env, promRegistry)

	return app, appConfig.Env
}
