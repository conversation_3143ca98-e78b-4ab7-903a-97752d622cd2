<!-- templates/monitor.html -->
<!DOCTYPE html>
<html>

<head>
    <title>IVR Queue Monitor</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            color: #333;
        }

        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .metric-title {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .metric-value {
            font-size: 24px;
            font-weight: 600;
        }

        .progress-bar {
            background: #eee;
            height: 8px;
            border-radius: 4px;
            margin-top: 10px;
            overflow: hidden;
        }

        .progress-value {
            height: 100%;
            background: #4CAF50;
            transition: width 0.3s ease;
        }

        .items-table {
            width: 100%;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .items-table th,
        .items-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .items-table th {
            background: #f8f9fa;
            font-weight: 500;
        }

        .refresh-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .refresh-button:hover {
            background: #45a049;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>

<body>
    <div class="dashboard">
        <div class="header">
            <h1>IVR Queue Monitor</h1>
            <button class="refresh-button" onclick="refreshData()">Refresh Data</button>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-title">Queue Items</div>
                <div class="metric-value">
                    <span id="currentItems">0</span> / <span id="maxItems">0</span>
                </div>
                <div class="progress-bar">
                    <div id="itemsProgress" class="progress-value"></div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-title">Memory Usage</div>
                <div class="metric-value">
                    <span id="memoryUsage">0</span> MB / <span id="maxMemory">0</span> MB
                </div>
                <div class="progress-bar">
                    <div id="memoryProgress" class="progress-value"></div>
                </div>
            </div>
        </div>

        <div class="metric-card">
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Data</th>
                        <th>Trigger Time</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody id="queueItems">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function refreshData() {
            fetch('/queue/metrics/ivr_queue')
                .then(response => response.json())
                .then(data => {
                    // Update items metrics
                    document.getElementById('currentItems').textContent = data.currentItems;
                    document.getElementById('maxItems').textContent = data.maxItems;
                    const itemsPercent = (data.currentItems / data.maxItems * 100).toFixed(1);
                    document.getElementById('itemsProgress').style.width = itemsPercent + '%';

                    // Update memory metrics
                    document.getElementById('memoryUsage').textContent = data.memoryUsage.toFixed(2);
                    document.getElementById('maxMemory').textContent = data.maxMemory.toFixed(2);
                    const memoryPercent = (data.memoryUsage / data.maxMemory * 100).toFixed(1);
                    document.getElementById('memoryProgress').style.width = memoryPercent + '%';

                    // Update queue items
                    const tbody = document.getElementById('queueItems');
                    tbody.innerHTML = '';
                    data.items.forEach(item => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td><pre style="margin: 0">${JSON.stringify(item.data, null, 2)}</pre></td>
                            <td>${item.triggerAt}</td>
                            <td><span class="status-badge status-pending">${item.status}</span></td>
                        `;
                        tbody.appendChild(row);
                    });
                })
                .catch(error => console.error('Error:', error));
        }

        // Initial load
        refreshData();

        // Auto refresh every 50 seconds
        setInterval(refreshData, 50000);
    </script>
</body>

</html>