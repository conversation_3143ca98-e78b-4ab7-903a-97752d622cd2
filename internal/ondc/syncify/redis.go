package syncify

import (
	"context"
	"time"

	"github.com/go-redis/redis/v8"
)

type RedisSyncifyClient struct {
	Client *redis.Client
}

//func (r *RedisSyncifyClient) WaitForAsyncReq(ctx context.Context, identifier string) (interface{}, error) {
//	//wait for 2 seconds, check in redis and respond
//	time.Sleep(8 * time.Second)
//	val, err := r.Client.LRange(ctx, identifier, 0, -1).Result()
//	fmt.Println(val)
//	if err != nil && err != redis.Nil {
//		return nil, err
//	}
//	return val, nil
//}

func (r *RedisSyncifyClient) WaitForAsyncReq(ctx context.Context, identifier string) (interface{}, error) {
	// Wait for 2 seconds, check in Redis and respond
	time.Sleep(3 * time.Second)

	// Try getting the value as a list
	val, err := r.Client.LRange(ctx, identifier, 0, -1).Result()
	if err == nil {
		return val, nil
	}

	// If <PERSON><PERSON><PERSON><PERSON> failed, try getting the value as a hash
	val2, err := r.Client.Get(ctx, identifier).Result()
	if err == nil {
		return val2, nil
	}

	// If both LRange and HGetAll failed, return the last error
	return nil, err
}

//func (r *RedisSyncifyClient) WaitForAsyncReq(ctx context.Context, identifier string) (interface{}, error) {
//	timeout := time.After(10 * time.Second) // Timeout after 10 seconds
//	ticker := time.NewTicker(2 * time.Second)
//	defer ticker.Stop()
//
//	for {
//		select {
//		case <-ctx.Done():
//			return nil, ctx.Err()
//		case <-timeout:
//			return nil, nil // Timeout reached
//		case <-ticker.C:
//			val, err := r.Client.LRange(ctx, identifier, 0, -1).Result()
//			if err != nil && err != redis.Nil {
//				return nil, err
//			}
//			if len(val) > 0 {
//				return val, nil // Return non-empty response
//			}
//		}
//	}
//}
