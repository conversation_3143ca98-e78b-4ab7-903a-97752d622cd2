FROM golang:alpine AS builder

# Install git and bash
RUN apk add git
RUN apk add --no-cache bash

# Set necessary environment variables needed for our image
ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64\
    ONDC_CONFIG="/internal/ondc/config/config.json"\
    DELHIVERY_CONFIG="/internal/ondc/config/delhivery_config.json"\
    TPL_CONFIG="/internal/ondc/config/tpl_config.json"

# Add GitHub token as build argument
ARG GITHUB_TOKEN
ARG ONDC_DEPLOYMENT_ENV

# Configure Git to use token authentication for private repositories
RUN git config --global url."https://WableSanket:${GITHUB_TOKEN}@github.com/".insteadOf "https://github.com/"

# Set environment for private Go modules
ENV GOPRIVATE=github.com/Kirana-Club/*

# Move to working directory
WORKDIR /

# Copy and download dependency using go mod
COPY go.mod .
COPY go.sum .
RUN mkdir -p internal/ondc
COPY /internal/ondc internal/ondc
# MAKE CMD FOLDER AND COPY MONO INTO IT
RUN mkdir -p cmd
COPY /cmd/ondc /cmd/ondc

ENV TZ=Asia/Kolkata
RUN go mod download

RUN apk --no-cache add ca-certificates
RUN apk --no-cache add tzdata
# Build the application
WORKDIR cmd/ondc

RUN go build -o main .

# Move to /dist directory as the place for resulting binary folder
WORKDIR /dist

# Copy binary from build to main folder
RUN cp /cmd/ondc/main .
ADD / .

FROM alpine
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
ARG ONDC_DEPLOYMENT_ENV

ENV ONDC_CONFIG="config.json"
ENV DELHIVERY_CONFIG="delhivery_config.json"
ENV TPL_CONFIG="tpl_config.json"
ENV IVR_CONFIG="ivr.json"
ENV ONDC_DEPLOYMENT_ENV=${ONDC_DEPLOYMENT_ENV}
ENV GIN_MODE="release"
ENV TZ=Asia/Kolkata
COPY --from=builder /etc/ssl/certs/ca-certificates.crt  /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /internal/ondc/config.json /
COPY --from=builder /internal/ondc/firebase.json /
COPY --from=builder /internal/ondc/delhivery_config.json /
COPY --from=builder /internal/ondc/tpl_config.json /
COPY --from=builder /internal/ondc/ivr.json /
COPY --from=builder /dist /
RUN ls

# Command to run the executable
ENTRYPOINT ["/main"]

EXPOSE 80