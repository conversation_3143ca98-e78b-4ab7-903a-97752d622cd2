# Diagon Alley Backend - Complete Technical Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture Overview](#architecture-overview)
3. [Development Setup](#development-setup)
4. [Service Layer Documentation](#service-layer-documentation)
5. [API Documentation](#api-documentation)
6. [Database Models](#database-models)
7. [External Integrations](#external-integrations)
8. [Configuration Management](#configuration-management)
9. [Development Guidelines](#development-guidelines)
10. [Deployment & Operations](#deployment--operations)

---

## Project Overview

**Diagon Alley Backend** is a comprehensive e-commerce platform built for Kirana Club, implementing the ONDC (Open Network for Digital Commerce) protocol. The system serves as a backend for both B2C operations under the "KiranaBazar" brand and B2B functionality for sellers and partners.

### Key Features
- **ONDC Protocol Compliance**: Full implementation of ONDC standards for interoperability
- **Multi-Seller Platform**: Support for multiple sellers with individual configurations
- **Comprehensive E-commerce**: Complete order lifecycle management from search to delivery
- **Payment Integration**: Multiple payment gateway support with refund management
- **Logistics Management**: Integration with multiple courier partners
- **Customer Support**: Integrated ticketing and communication systems
- **Analytics**: Real-time tracking and business intelligence

### Technology Stack
- **Language**: Go 1.23.2
- **Framework**: Gin HTTP framework
- **Database**: MySQL with GORM ORM
- **Cache**: Redis (Azure Redis, GCP Redis)
- **Search**: Elasticsearch
- **Message Queue**: RabbitMQ
- **Real-time Data**: Firebase Firestore
- **Monitoring**: Prometheus metrics
- **External APIs**: 20+ third-party integrations

---

## Architecture Overview

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                          Client Layer                           │
├─────────────────────────────────────────────────────────────────┤
│  Mobile App (KiranaBazar)  │  Web Dashboard (B2B)  │  ONDC Network │
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                           API Layer                             │
├─────────────────────────────────────────────────────────────────┤
│  REST APIs  │  ONDC APIs  │  Webhook Handlers  │  Health Checks │
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                         Service Layer                           │
├─────────────────────────────────────────────────────────────────┤
│  Auth  │  Orders  │  Payments  │  Inventory  │  Logistics  │ etc │
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                       Repository Layer                          │
├─────────────────────────────────────────────────────────────────┤
│  SQL Repo  │  Firebase  │  Cache Repo  │  Elasticsearch  │ etc  │
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                         Data Layer                              │
├─────────────────────────────────────────────────────────────────┤
│  MySQL  │  Redis  │  Firebase  │  Elasticsearch  │  RabbitMQ    │
└─────────────────────────────────────────────────────────────────┘
```

### Core Components

#### 1. **Entry Point** (`cmd/ondc/main.go`)
- Application bootstrap and initialization
- Environment-based configuration loading
- Slack deployment notifications
- Graceful shutdown handling

#### 2. **Application Core** (`internal/ondc/app.go`)
- Dependency injection and service wiring
- Route registration
- Middleware setup
- Cron job initialization

#### 3. **Service Layer** (`internal/ondc/service/`)
- Business logic implementation
- Domain-specific services
- External service integration
- Data validation and processing

#### 4. **API Layer** (`internal/ondc/api/`)
- HTTP request handlers
- Request/response transformation
- Input validation
- Error handling

#### 5. **Infrastructure** (`internal/ondc/infrastructure/`)
- Database connections
- Cache management
- External service clients
- Logging and monitoring

---

## Development Setup

### Prerequisites
- Go 1.23.2 or later
- MySQL database
- Redis instance
- Firebase project

### Environment Variables
```bash
# Core configuration
export ONDC_DEPLOYMENT_ENV=local
export ONDC_CONFIG=/path/to/config.json
export IVR_CONFIG=/path/to/ivr.json
export TPL_CONFIG=/path/to/tpl_config.json

# Environment-specific settings
# local: Port 81
# prod: Port 80
# stage: Port 80
# default: Port 8080
```

### Installation and Setup
```bash
# Clone the repository
git clone <repository-url>
cd diagon-alley-BE

# Install dependencies
go mod download

# Run the application
go run cmd/ondc/main.go

# Run tests
go test ./...

# Run specific test
go test ./internal/ondc/service/test.go

# Build for production
go build -o bin/ondc cmd/ondc/main.go
```

### Configuration Files
The application requires JSON configuration files for:
- **Main Config** (`config.json`): Database, Redis, external service credentials
- **IVR Config** (`ivr.json`): Exotel IVR application configurations
- **TPL Config** (`tpl_config.json`): Third-party logistics configurations

---

## Service Layer Documentation

### Core Service Architecture

The service layer implements a domain-driven design with the following structure:

#### **Main Service** (`service.go`)
Central service aggregator that coordinates all domain services:

```go
type Service struct {
    // Core repositories
    Repository         *sqlRepo.Repository
    FirebaseRepo       *firebaseRepo.FirebaseRepo
    CacheRepo          *cacheRepo.Repository
    
    // External services
    PaymentService     *payments.Service
    OmsService         *oms.Service
    TplService         *tplgo.Service
    
    // Domain services
    AuthService        *auth.Service
    CouponsService     *coupons.Repository
    InventoryService   *inventory.Service
    // ... 20+ more services
}
```

### Domain Services

#### **Authentication Service** (`auth/service.go`)
**Purpose**: Handles user authentication and session management for customer support agents.

**Key Features**:
- **Enhanced Security**: IP tracking, User-Agent validation, token hashing
- **Session Management**: Secure refresh tokens with rotation
- **CSRF Protection**: Token generation and validation
- **Performance Optimization**: User data caching with 5-minute refresh

**Core Methods**:
- `Login(credentials)` → Authentication with security checks
- `RefreshToken(token)` → Generate new access tokens
- `Logout(token)` → Invalidate refresh tokens
- `GenerateCSRFToken()` / `ValidateCSRFToken()` → CSRF protection

#### **Coupons Service** (`coupons/coupons.go`)
**Purpose**: Manages discount coupons, validation, and application logic.

**Key Features**:
- **Validation Engine**: Complex rules for user eligibility, cart requirements
- **Discount Types**: Fixed amount, percentage, shipping discounts, freebies
- **Cohort-based Logic**: Different rules for user segments
- **Real-time Calculation**: Dynamic discount computation based on cart state

**Core Methods**:
- `ValidateCoupon(code, user, cart)` → Comprehensive validation
- `CalculateBackendDiscount(cart)` → Automatic discount calculation
- `GetNextApplicableCoupon(user, cart)` → Smart coupon suggestions

#### **Inventory Service** (`inventory/service.go`)
**Purpose**: Manages product stock levels and inventory operations.

**Key Features**:
- **Batch Operations**: Efficient bulk inventory updates
- **Transaction Safety**: Database transactions for consistency
- **Async Processing**: Background inventory updates
- **Stock Tracking**: Real-time availability monitoring

**Core Methods**:
- `UpdateProductsInventory(operations)` → Main inventory update entry point
- `calculateInventoryUpdates()` → Determine required changes
- `executeInventoryUpdates()` → Apply changes with transactions

#### **Payment Service** (`payments.go`)
**Purpose**: Comprehensive payment processing and gateway integration.

**Key Features**:
- **Multi-Gateway Support**: Razorpay integration with extensible architecture
- **Fraud Detection**: User validation, spam detection, order history analysis
- **Webhook Processing**: Real-time payment status updates
- **Refund Management**: Automated refund processing for cancellations

**Core Methods**:
- `InitializePayment(amount, user)` → Create payment orders
- `ValidatePayment(paymentId)` → Verify payment completion
- `ProcessWebhook(event)` → Handle gateway notifications
- `ProcessRefund(orderId, amount)` → Handle refund requests

#### **Cart Service** (`cart/cart.go`)
**Purpose**: Shopping cart management with dual storage strategy.

**Key Features**:
- **Dual Storage**: Redis for speed, SQL for persistence
- **Async Persistence**: Background SQL synchronization
- **Real-time Updates**: Instant cart modifications
- **Fallback Strategy**: Redis-first with SQL fallback

**Core Methods**:
- `getCart(userId)` → Retrieve cart with fallback
- `updateCart(userId, items)` → Update cart in both stores
- `syncCart(userId)` → Ensure data consistency

### Business Flow Integration

#### **Order Lifecycle**
1. **Product Discovery** → Search service → Elasticsearch
2. **Cart Management** → Cart service → Redis/SQL
3. **Coupon Application** → Coupons service → Validation engine
4. **Payment Processing** → Payment service → Gateway integration
5. **Order Creation** → Order service → Inventory deduction
6. **Fulfillment** → Logistics service → Courier assignment
7. **Tracking** → Status service → Real-time updates

#### **Cross-Service Communication**
- **Dependency Injection**: Services communicate through injected dependencies
- **Event-Driven Updates**: Async processing for non-critical operations
- **Data Consistency**: Transactional updates where required
- **Error Handling**: Comprehensive error propagation and logging

---

## API Documentation

### API Architecture

The API layer is built using the Gin framework with comprehensive middleware support:

#### **Core Components**
- **Handler Structure**: Centralized handler with service injection
- **Middleware Stack**: CORS, Authentication, Rate Limiting, CSRF Protection
- **Error Handling**: Standardized error responses with proper HTTP codes
- **Request Validation**: Input validation and sanitization

#### **Security Features**
- **Authentication**: JWT-based with secure cookie implementation
- **CSRF Protection**: Token-based CSRF protection for state-changing operations
- **Rate Limiting**: 100 requests/minute for sensitive endpoints
- **Input Validation**: Comprehensive request validation
- **Secure Headers**: Security headers for XSS and clickjacking protection

### API Categories

#### **1. ONDC Protocol Endpoints**
**Base Route**: `/`

Full ONDC protocol implementation for network interoperability:

| Endpoint | Method | Description | Response Format |
|----------|---------|-------------|-----------------|
| `/search` | POST | Product search across network | ONDC ACK/NACK |
| `/on_search` | POST | Search response handler | ONDC Catalog |
| `/select` | POST | Product selection | ONDC ACK/NACK |
| `/on_select` | POST | Selection response | ONDC Quote |
| `/init` | POST | Order initialization | ONDC ACK/NACK |
| `/on_init` | POST | Order init response | ONDC Payment |
| `/confirm` | POST | Order confirmation | ONDC ACK/NACK |
| `/on_confirm` | POST | Confirmation response | ONDC Order |
| `/status` | POST | Order status inquiry | ONDC ACK/NACK |
| `/on_status` | POST | Status response | ONDC Tracking |
| `/cancel` | POST | Order cancellation | ONDC ACK/NACK |
| `/on_cancel` | POST | Cancellation response | ONDC Refund |

**Protocol Features**:
- Standardized ONDC message structure
- Context-aware request/response handling
- Network participant validation
- Cryptographic signature verification

#### **2. Authentication & User Management**
**Base Route**: `/auth`

| Endpoint | Method | Description | Auth Required |
|----------|---------|-------------|---------------|
| `/auth/login` | POST | User authentication | Rate Limited |
| `/auth/refresh` | POST | Token refresh | Rate Limited |
| `/auth/logout` | POST | Session termination | Rate Limited |
| `/auth/profile` | GET | User profile data | JWT Required |

**Security Enhancements**:
- Enhanced cookie security with SameSite=None
- Client IP and User-Agent tracking
- Secure domain configuration
- Token rotation on refresh

#### **3. KiranaBazar Consumer APIs**
**Base Route**: `/kirana_bazar`

Consumer-facing APIs for the mobile application:

| Endpoint | Method | Description | Cache Strategy |
|----------|---------|-------------|---------------|
| `/kirana_bazar/get_cart` | POST | Retrieve cart contents | Redis Primary |
| `/kirana_bazar/sync_cart` | POST | Synchronize cart data | Dual Write |
| `/kirana_bazar/order` | POST | Create new order | Duplicate Detection |
| `/kirana_bazar/orders` | POST | Order history | Pagination |
| `/kirana_bazar/coupons` | POST | Available coupons | Real-time |
| `/kirana_bazar/bill_details` | POST | Order bill breakdown | Calculated |
| `/kirana_bazar/suggested_products` | POST | Recommendations | ML-based |

**Features**:
- Duplicate request detection using cache
- Localized error messages (Hindi support)
- Service availability checks
- Real-time inventory validation

#### **4. B2B Dashboard APIs**
**Base Route**: `/b2b`

Admin and seller dashboard functionality:

| Endpoint | Method | Description | Auth Level |
|----------|---------|-------------|------------|
| `/b2b/get_orders` | POST | Order management | None |
| `/b2b/get_orders_extended` | POST | Detailed order data | JWT + CSRF |
| `/b2b/export_orders` | POST | Order data export | None |
| `/b2b/update_order_status` | POST | Status updates | None |
| `/b2b/get_ndr_orders` | POST | NDR management | None |
| `/b2b/generate_picklist` | POST | Fulfillment docs | None |

**Advanced Features**:
- Bulk operations support
- Real-time inventory management
- Sales analytics and insights
- Multi-seller data isolation

#### **5. Payment Processing**
**Base Route**: `/payment`

| Endpoint | Method | Description | Integration |
|----------|---------|-------------|-------------|
| `/payment/initiate_payment` | POST | Payment initialization | Razorpay |
| `/payment/validate_payment` | POST | Payment verification | Razorpay |
| `/payment/rp_webhook_type1` | POST | Payment webhooks | Razorpay |
| `/payment/refund_advance_payment` | POST | Refund processing | Razorpay |

**Payment Features**:
- Automatic duplicate request prevention
- Webhook signature verification
- Advanced payment reconciliation
- Multi-gateway extensibility

#### **6. Logistics & Integration**
**Base Route**: `/integrations`

| Endpoint | Method | Description | Service |
|----------|---------|-------------|---------|
| `/zoho/webhook` | POST/GET | CRM integration | Zoho |
| `/exotel/webhook` | GET | Telephony events | Exotel |
| `/exotel/ivr` | POST | IVR call initiation | Exotel |
| `/exotel/ivr/callback` | POST | IVR status updates | Exotel |

### Error Handling Standards

#### **Response Format**
```json
{
  "data": null,
  "meta": {
    "success": false,
    "message": "Error description",
    "error_code": "ERROR_CODE"
  },
  "error": {
    "code": "SPECIFIC_ERROR",
    "message": "Detailed error message",
    "details": {}
  }
}
```

#### **Error Categories**
- **InvalidRequestStruct**: Malformed request body (400)
- **UserNotFound**: Authentication failures (401)
- **InvalidAuthToken**: Token validation failures (401)
- **InternalError**: Server-side errors (500)
- **RateLimitExceeded**: Too many requests (429)

### Performance Optimizations

#### **Caching Strategy**
- **Request Deduplication**: 1-minute TTL for identical requests
- **Response Caching**: Strategic caching of expensive operations
- **Database Query Optimization**: Efficient query patterns

#### **Rate Limiting**
- **Authentication Endpoints**: 100 requests/minute
- **Global Rate Limiting**: Configurable per endpoint
- **IP-based Limiting**: Protection against abuse

---

## Database Models

### Database Architecture

The system uses a sophisticated data model supporting complex e-commerce operations with ONDC compliance:

#### **Design Patterns**
- **DAO Pattern**: Data Access Objects for database operations
- **DTO Pattern**: Data Transfer Objects for API communication
- **Repository Pattern**: Clean separation of data access logic
- **Domain-Driven Design**: Business logic separation

### Core Business Entities

#### **User Management**

**User Entity** (`users.go`):
```go
type User struct {
    ID              int64     `gorm:"primaryKey"`
    UserID          string    `gorm:"uniqueIndex"`
    Phone           string    `gorm:"index"`
    Name            string
    StoreName       string
    City            string
    State           string
    UserType        string
    CreatedAt       time.Time
    UpdatedAt       time.Time
    LastSeen        *time.Time
    LastUninstalled *time.Time
}
```

**User Address** (`users.go`):
```go
type UserAddress struct {
    ID              int64   `gorm:"primaryKey"`
    UserID          string  `gorm:"index"`
    AddressLine     string
    District        string
    State           string
    PostalCode      string
    Latitude        float64
    Longitude       float64
    GSTNumber       string
    IsDefault       bool
    IsActive        bool
    BillingAddress  *UserAddress `gorm:"foreignKey:BillingAddressID"`
    CreatedAt       time.Time
    UpdatedAt       time.Time
}
```

#### **Product Catalog**

**Product Entity** (`kiranaBazarProducts.go`):
```go
type KiranaBazarProduct struct {
    ID                  int64           `gorm:"primaryKey"`
    ParentItemID        *int64          `gorm:"index"`
    CategoryID          int64           `gorm:"index"`
    SizeVariantCode     string
    ImageUrls           datatypes.JSON  // Array of image URLs
    MediaUrls           datatypes.JSON  // Array of media URLs
    Ranking             int
    PopularityScore     float64
    IsOOS               bool            // Out of Stock
    ExtendedMetadata    datatypes.JSON  // Flexible metadata
    CreatedAt           time.Time
    UpdatedAt           time.Time
    
    // Relationships
    Category            *KiranaBazarCategory `gorm:"foreignKey:CategoryID"`
    ChildProducts       []KiranaBazarProduct `gorm:"foreignKey:ParentItemID"`
}
```

**Category Hierarchy** (`kiranaBazarCategories.go`):
```go
type KiranaBazarCategory struct {
    ID          int64  `gorm:"primaryKey"`
    ParentID    *int64 `gorm:"index"`
    Name        string
    Domain      string // ONDC domain classification
    ImageURL    string
    Ranking     int
    Source      string
    CreatedAt   time.Time
    UpdatedAt   time.Time
    
    // Hierarchical relationships
    Parent      *KiranaBazarCategory   `gorm:"foreignKey:ParentID"`
    Children    []KiranaBazarCategory  `gorm:"foreignKey:ParentID"`
}
```

#### **Order Management System**

**Order Entity** (`kiranaBazarOrders.go`):
```go
type KiranaBazarOrder struct {
    ID              int64       `gorm:"primaryKey"`
    UserID          string      `gorm:"index"`
    OrderID         string      `gorm:"uniqueIndex"`
    TransactionID   string      `gorm:"index"` // ONDC transaction ID
    MessageID       string      // ONDC message ID
    OrderStatus     string      `gorm:"index"`
    PaymentStatus   string      `gorm:"index"`
    DeliveryStatus  string      `gorm:"index"`
    DisplayStatus   string      `gorm:"index"`
    ProcessingStatus string
    TrackingLink    string
    CreatedAt       time.Time
    UpdatedAt       time.Time
    
    // Relationships
    User            User                  `gorm:"foreignKey:UserID;references:UserID"`
    OrderDetails    KiranaBazarOrderDetail `gorm:"foreignKey:OrderID;references:OrderID"`
    OrderItems      []KiranabazarOrderItem `gorm:"foreignKey:OrderID;references:OrderID"`
    Payments        []KiranaBazarOrderPayment `gorm:"foreignKey:OrderID;references:OrderID"`
}
```

**Order Details** (`kiranaBazarOrders.go`):
```go
type KiranaBazarOrderDetail struct {
    ID           int64          `gorm:"primaryKey"`
    OrderID      string         `gorm:"uniqueIndex"`
    OrderDetails datatypes.JSON // Complete order information
    OnSelect     datatypes.JSON // ONDC on_select response
    OnInit       datatypes.JSON // ONDC on_init response
    OnConfirm    datatypes.JSON // ONDC on_confirm response
    PrintingLabel string
    Picklist     string
    CreatedAt    time.Time
    UpdatedAt    time.Time
}
```

**Order Items** (`kiranaBazarOrders.go`):
```go
type KiranabazarOrderItem struct {
    ID                  int64   `gorm:"primaryKey"`
    OrderID             string  `gorm:"index"`
    ProductID           int64   `gorm:"index"`
    SKU                 string
    Quantity            int
    
    // Pricing breakdown
    KCUnitPrice         float64 // Kirana Club unit price
    KCSellingPrice      float64 // Kirana Club selling price
    TotalSellingPrice   float64 // Total selling price
    
    // Tax calculations
    CGSTRate            float64
    SGSTRate            float64
    IGSTRate            float64
    CESSRate            float64
    CGSTAmount          float64
    SGSTAmount          float64
    IGSTAmount          float64
    CESSAmount          float64
    
    // Discounts
    PlatformDiscount    float64
    SellerDiscount      float64
    PaymentDiscount     float64
    
    // Additional fields
    HSNCode             string
    IsFreeItem          bool
    CreatedAt           time.Time
    UpdatedAt           time.Time
}
```

#### **Payment Management**

**Payment Transaction** (`payments.go`):
```go
type PaymentGatewayTransaction struct {
    ID                  int64           `gorm:"primaryKey"`
    OrderID             string          `gorm:"index"`
    TransactionID       string          `gorm:"uniqueIndex"`
    GatewayOrderID      string          `gorm:"index"`
    Gateway             string          // razorpay, payu, etc.
    Amount              float64
    Currency            string          `gorm:"default:INR"`
    Status              string          `gorm:"index"`
    PaymentMethod       string
    
    // Fee calculations
    GatewayFee          float64
    GatewayTax          float64
    
    // Refund management
    RefundAmount        float64
    RefundStatus        string
    RefundID            string
    
    // Error handling
    ErrorCode           string
    ErrorDescription    string
    
    // Metadata
    GatewayResponse     datatypes.JSON
    CreatedAt           time.Time
    UpdatedAt           time.Time
}
```

#### **Customer Support System**

**Support Ticket** (`customerSupport.go`):
```go
type CSTicket struct {
    ID              int64       `gorm:"primaryKey"`
    TicketID        string      `gorm:"uniqueIndex"`
    UserID          string      `gorm:"index"`
    OrderID         string      `gorm:"index"`
    
    // Categorization
    Category        string
    SubCategory     string
    Priority        string      // low, medium, high, urgent
    
    // Assignment
    AssignedTo      string
    TeamName        string
    
    // Status tracking
    Status          string      `gorm:"index"` // open, in_progress, resolved, closed
    ProgressState   string
    
    // Content
    Subject         string
    Description     string
    
    // Timestamps
    CreatedAt       time.Time
    UpdatedAt       time.Time
    ResolvedAt      *time.Time
    
    // Relationships
    Messages        []CSMessage    `gorm:"foreignKey:TicketID;references:TicketID"`
    Attachments     []CSAttachment `gorm:"foreignKey:TicketID;references:TicketID"`
}
```

### Database Relationships

#### **Primary Relationships**
- **User ↔ UserAddress**: One-to-Many (user can have multiple addresses)
- **User ↔ Order**: One-to-Many (user order history)
- **Order ↔ OrderItem**: One-to-Many (order line items)
- **Order ↔ Payment**: One-to-Many (multiple payments per order)
- **Product ↔ Category**: Many-to-One (product categorization)
- **Category ↔ Category**: Self-referential (category hierarchy)

#### **Data Integrity Constraints**
- **Foreign Key Constraints**: Referential integrity across entities
- **Unique Constraints**: Prevention of duplicate orders, users, transactions
- **Index Optimization**: Strategic indexing for query performance
- **JSON Validation**: Structured metadata with business rules

### Business Logic in Database

#### **Order State Management**
- **Status Progression**: Strict validation of order status transitions
- **Payment Reconciliation**: Amount validation across order and payment entities
- **Inventory Tracking**: Real-time stock level management
- **Audit Trails**: Complete history of changes with timestamps

#### **Financial Calculations**
- **Tax Compliance**: GST calculation with state-wise rates
- **Discount Management**: Complex discount stacking rules
- **Fee Calculation**: Platform and payment gateway fee tracking
- **Refund Processing**: Partial and full refund management

#### **Geographic and Delivery**
- **Address Validation**: Coordinate validation and pincode verification
- **Service Ability**: Delivery area coverage checking
- **Logistics Integration**: Multi-courier partner support

---

## External Integrations

### Integration Architecture

The system integrates with 20+ external services across multiple domains:

#### **Categories of Integration**
1. **E-commerce Platforms**: EasyEcom, Unicommerce
2. **Payment Gateways**: Razorpay
3. **Logistics Partners**: Delhivery, Shiprocket, Shipway, Shipdelight
4. **Communication**: WhatsApp, Exotel, Slack
5. **CRM & Support**: Zoho
6. **Brand/Seller Specific**: GoDesi, Panchvati, Loyalty programs

### Key Integrations

#### **EasyEcom Integration**
**Purpose**: Multi-channel e-commerce platform for order management and inventory synchronization.

**API Endpoints**:
- `POST /access/token` - Authentication with JWT
- `POST /webhook/v2/createOrder` - Order creation
- `GET /orders/V2/getOrderDetails` - Order retrieval
- `GET /Carriers/getTrackingDetails` - Shipment tracking

**Features**:
- **Token Management**: Automatic JWT refresh with expiration tracking
- **Multi-OMS Support**: Per-OMS configuration management
- **Retry Logic**: Exponential backoff (1, 2, 4, 8, 16 seconds)
- **Error Handling**: Specific handling for 504 (Gateway Timeout) and 429 (Rate Limit)

**Configuration**:
```json
{
  "email": "<EMAIL>",
  "password": "encrypted_password",
  "location_key": "warehouse_location",
  "market_place_id": 123,
  "oms": "seller_identifier"
}
```

#### **Razorpay Payment Integration**
**Purpose**: Comprehensive payment processing and financial transaction management.

**API Endpoints**:
- `POST /orders` - Create payment order
- `GET /payments/{id}` - Payment verification
- `POST /payments/{id}/refund` - Process refunds
- `GET /orders/{id}/payments` - Order payment history

**Features**:
- **Multi-Payment Methods**: Credit/Debit cards, UPI, Net banking, Wallets
- **Webhook Processing**: Real-time payment status updates
- **Refund Management**: Automatic and manual refund processing
- **Security**: HMAC SHA256 webhook signature verification

**Payment Flow**:
1. **Order Creation**: Generate Razorpay order with amount and currency
2. **Payment Initiation**: Customer completes payment on Razorpay
3. **Webhook Processing**: Real-time payment status updates
4. **Verification**: Server-side payment verification
5. **Order Confirmation**: Trigger order fulfillment

#### **Exotel IVR Integration**
**Purpose**: Voice communication and Interactive Voice Response for order confirmations.

**Features**:
- **Order Confirmation Calls**: Automated order confirmation
- **Out-for-Delivery Notifications**: Delivery status updates
- **Bad Delivery Handling**: Failed delivery management
- **Call Limiting**: Maximum 4 call attempts per order
- **Custom Fields**: Order-specific information in calls

**IVR Flow Configuration**:
```json
{
  "apps": [
    {
      "name": "order_confirmation",
      "app_id": 12345
    }
  ],
  "seller": "seller_identifier"
}
```

**Business Logic**:
- **Call Feasibility**: Order status-based call eligibility
- **Retry Logic**: Exponential backoff for failed calls
- **Analytics**: Mixpanel tracking for call performance
- **Multilingual Support**: Hindi and English IVR flows

#### **WhatsApp Business Integration**
**Purpose**: Customer communication through WhatsApp Business API.

**Message Types**:
- **Order Confirmation**: Order details and tracking information
- **Payment Reminders**: Payment retry notifications
- **Delivery Updates**: Out-for-delivery and delivery confirmation
- **Cancellation Notices**: Order cancellation notifications

**Features**:
- **Template Messaging**: Pre-approved message templates
- **Dynamic Content**: Real-time order information injection
- **Deep Linking**: App navigation through dynamic links
- **Media Support**: Image and document attachments

**Message Flow**:
1. **Trigger Event**: Order status change or payment event
2. **Template Selection**: Choose appropriate message template
3. **Dynamic Link Generation**: Create app-specific navigation links
4. **Parameter Injection**: Insert order/user-specific information
5. **Message Dispatch**: Send through WhatsApp Business API

#### **Shiprocket Logistics Integration**
**Purpose**: Shipping aggregator providing access to multiple courier partners.

**API Endpoints**:
- `POST /auth/login` - Authentication (5-day token validity)
- `GET /courier/serviceability/` - Check delivery serviceability
- `POST /orders/create/adhoc` - Create shipping orders
- `GET /orders/track` - Track shipment status

**Features**:
- **Multi-Courier Support**: Access to 15+ courier partners
- **Rate Comparison**: Compare shipping rates across providers
- **Service Ability**: Real-time pincode serviceability checking
- **COD Support**: Cash on delivery availability verification

**Logistics Flow**:
1. **Service Ability Check**: Verify delivery to destination pincode
2. **Rate Calculation**: Compare rates across courier partners
3. **Courier Selection**: Choose optimal courier based on cost/speed
4. **Order Creation**: Create shipping order with selected courier
5. **Tracking**: Real-time shipment status updates

#### **Slack Integration**
**Purpose**: Internal team communication and system alerting.

**Use Cases**:
- **Deployment Notifications**: Success/failure deployment alerts
- **Error Monitoring**: Critical error notifications
- **System Health**: Performance and availability alerts
- **Business Metrics**: Key performance indicator updates

**Message Types**:
- **Deployment Success**: "Diagon Alley BE deployment successful"
- **Error Alerts**: Critical system errors with stack traces
- **Performance Warnings**: High response time or error rate alerts
- **Business Notifications**: Order volume, revenue milestones

### Integration Patterns

#### **Authentication Patterns**
- **JWT Tokens**: Automatic refresh with expiration tracking
- **OAuth2**: Refresh token mechanism for long-term access
- **API Keys**: Simple key-based authentication
- **Basic Auth**: Username/password for simple services

#### **Error Handling & Retry Logic**
- **Exponential Backoff**: 1, 2, 4, 8, 16 seconds retry intervals
- **Circuit Breaker**: Fail-fast for consistently failing services
- **Rate Limit Handling**: Respect 429 status codes with backoff
- **Timeout Management**: Configurable timeout values per service

#### **Data Synchronization**
- **Real-time Webhooks**: Immediate status updates
- **Polling Mechanisms**: Scheduled status synchronization
- **Batch Processing**: Bulk data operations for efficiency
- **Event-Driven Updates**: Trigger-based data synchronization

#### **Configuration Management**
- **Multi-Tenant Support**: Per-seller/OMS configurations
- **Environment Isolation**: Different configs per environment
- **Secure Credential Storage**: Encrypted credential management
- **Dynamic Configuration**: Runtime configuration updates

### External Service Reliability

#### **Monitoring & Alerting**
- **Health Checks**: Regular service availability monitoring
- **Response Time Tracking**: Performance metric collection
- **Error Rate Monitoring**: Failure rate tracking and alerting
- **Slack Integration**: Real-time team notifications

#### **Fallback Strategies**
- **Service Degradation**: Graceful degradation for non-critical services
- **Alternative Providers**: Fallback to secondary service providers
- **Cached Responses**: Serve cached data when services are unavailable
- **Manual Intervention**: Admin tools for manual service operations

---

## Configuration Management

### Configuration Architecture

The system uses a comprehensive configuration management system supporting multiple environments and service integrations:

#### **Configuration Structure**
```go
type AppConfig struct {
    Name              string               `json:"name"`
    Mode              string               `json:"mode"`
    Env               string               `json:"env"`
    
    // Database configurations
    DbConfig          *DBConfig            `json:"db_config"`
    
    // Cache configurations
    RedisConfig       *RedisConfig         `json:"redis_config"`
    GcpRedisConfig    *GcpRedisConfig      `json:"gcp_redis_config"`
    
    // External service configurations
    EasyEcom          *[]EasyEcomConfig    `json:"easy_ecom_config"`
    UnicommerceConfig *[]UnicommerceConfig `json:"unicommerce_config"`
    ShipRocketConfig  *[]ShipRocketConfig  `json:"shiprocket_config"`
    PaymentConfig     PaymentConfig        `json:"payment_config"`
    
    // Infrastructure configurations
    AzureConfig       *AzureConfig         `json:"azure_config"`
    ElasticConfig     ElasticConfig        `json:"elastic_config"`
    RabbitMQConfig    RabbitMQConfig       `json:"rabbitmq_config"`
    
    // Firebase configuration
    FirebaseKeyPath   string               `json:"firebase_key_path"`
    FirebaseMetaDbUrl string               `json:"firebase_metadb_url"`
    
    // Security configuration
    AuthConfig        AuthConfig           `json:"auth_config"`
}
```

### Environment Configuration

#### **Environment Variables**
```bash
# Core Configuration
ONDC_DEPLOYMENT_ENV=local|stage|prod
ONDC_CONFIG=/path/to/config.json
IVR_CONFIG=/path/to/ivr.json
TPL_CONFIG=/path/to/tpl_config.json
```

#### **Environment-Specific Settings**
- **Local Environment**: Port 81, debug logging, test configurations
- **Staging Environment**: Port 80, production-like settings, test data
- **Production Environment**: Port 80, optimized settings, production data

### Database Configuration

#### **Database Settings**
```go
type DBConfig struct {
    Hostname           string `json:"hostname"`
    Username           string `json:"username"`
    Password           string `json:"password"`
    Database           string `json:"database"`
    Port               string `json:"port"`
    MaxOpenConnections int    `json:"max_open_connections"`
    DBDriverName       string `json:"db_driver_name"`
}
```

#### **Multi-Database Support**
- **Primary Database**: Main transactional database
- **Finance Database**: Separate database for financial operations
- **Read Replica**: Read-only database for analytics and reporting
- **Connection Pooling**: Optimized connection management

### Cache Configuration

#### **Redis Configuration**
```go
type RedisConfig struct {
    Hostname string `json:"hostname"`
    Password string `json:"password"`
    Username string `json:"username"`
    Database string `json:"database"`
}

type GcpRedisConfig struct {
    Host string `json:"host"`
    Port string `json:"port"`
}
```

#### **Multi-Cache Strategy**
- **Azure Redis**: Primary cache for application data
- **GCP Redis**: Secondary cache for specific operations
- **In-Memory Cache**: Local caching for frequently accessed data

### External Service Configuration

#### **Payment Gateway Configuration**
```go
type PaymentConfig struct {
    RazorpayConfig RazorpayConfig `json:"razorpay_config"`
}

type RazorpayConfig struct {
    KeyID     string `json:"key_id"`
    KeySecret string `json:"key_secret"`
    WHSecret  string `json:"wh_secret"`
}
```

#### **Logistics Configuration**
```go
type ShipRocketConfig struct {
    Email    string `json:"email"`
    Password string `json:"password"`
    OMS      string `json:"oms"`
}

type DelhiveryConfig struct {
    Token string `json:"token"`
    OMS   string `json:"oms"`
}
```

#### **Multi-Tenant Configuration**
- **Per-OMS Settings**: Different configurations for each Order Management System
- **Seller-Specific**: Individual seller configurations
- **Service-Specific**: Separate configs for different services

### Security Configuration

#### **Authentication Configuration**
```go
type AuthConfig struct {
    JwtSecret     string `json:"jwt_secret"`
    EncryptionKey string `json:"encryption_key"`
}
```

#### **Security Best Practices**
- **Secret Management**: Encrypted storage of sensitive credentials
- **Token Security**: Strong JWT secret keys
- **Environment Isolation**: Separate credentials per environment
- **Access Control**: Role-based access to configuration

### Configuration Loading

#### **Configuration Loader**
```go
func LoadConfig(logger *logrus.Logger) (*AppConfig, *IVRConfig) {
    config := &AppConfig{}
    ivrConfig := &IVRConfig{}
    
    // Load main configuration
    loadFileFromEnv(os.Getenv(EnvVar), config, logger)
    
    // Load IVR configuration
    loadIVRConfig(os.Getenv(IVRVar), ivrConfig, logger)
    
    // Set environment from environment variable
    config.Env = os.Getenv(DeploymentEnv)
    
    return config, ivrConfig
}
```

#### **Validation & Error Handling**
- **Configuration Validation**: Validate required fields on startup
- **Error Handling**: Graceful failure with informative error messages
- **Fallback Values**: Default values for non-critical configurations
- **Environment Validation**: Ensure environment consistency

### IVR Configuration

#### **IVR Configuration Structure**
```go
type IVRConfig []struct {
    Apps   []IVRAppData `json:"apps"`
    Seller string       `json:"seller"`
}

type IVRAppData struct {
    Name  string `json:"name"`
    AppID int    `json:"app_id"`
}
```

#### **IVR Features**
- **Multi-Seller Support**: Different IVR configurations per seller
- **App-Specific Settings**: Separate apps for different call types
- **Dynamic Configuration**: Runtime configuration updates

### Configuration Best Practices

#### **Security Practices**
- **Credential Encryption**: Encrypt sensitive configuration data
- **Environment Separation**: Isolate configurations by environment
- **Access Control**: Limit access to configuration files
- **Regular Rotation**: Rotate credentials periodically

#### **Operational Practices**
- **Version Control**: Track configuration changes
- **Documentation**: Document all configuration options
- **Validation**: Validate configurations before deployment
- **Monitoring**: Monitor configuration usage and performance

---

## Development Guidelines

### Code Organization

#### **Project Structure**
```
diagon-alley-BE/
├── cmd/ondc/                    # Application entry points
├── internal/ondc/               # Internal application code
│   ├── api/                     # HTTP handlers
│   ├── service/                 # Business logic
│   ├── models/                  # Data models
│   │   ├── dao/                 # Database models
│   │   └── dto/                 # API models
│   ├── repositories/            # Data access layer
│   ├── external/                # External integrations
│   ├── infrastructure/          # Infrastructure components
│   ├── middleware/              # HTTP middleware
│   ├── routes/                  # Route definitions
│   └── utils/                   # Utility functions
└── external/                    # External dependencies
```

#### **Naming Conventions**
- **Files**: snake_case for file names
- **Packages**: lowercase, descriptive names
- **Functions**: PascalCase for exported, camelCase for internal
- **Variables**: camelCase for local, PascalCase for exported
- **Constants**: UPPER_SNAKE_CASE for constants

### Code Patterns

#### **Repository Pattern**
```go
type Repository interface {
    Create(entity interface{}) error
    GetByID(id interface{}) (interface{}, error)
    Update(entity interface{}) error
    Delete(id interface{}) error
}
```

#### **Service Pattern**
```go
type Service struct {
    repo Repository
}

func (s *Service) BusinessLogic(input Input) (Output, error) {
    // Validate input
    if err := s.validateInput(input); err != nil {
        return Output{}, err
    }
    
    // Business logic
    result, err := s.processBusinessLogic(input)
    if err != nil {
        return Output{}, err
    }
    
    // Persist changes
    if err := s.repo.Create(result); err != nil {
        return Output{}, err
    }
    
    return result, nil
}
```

#### **Error Handling Pattern**
```go
func (s *Service) Operation() error {
    // Try operation
    if err := s.tryOperation(); err != nil {
        // Log error
        log.Error("Operation failed", zap.Error(err))
        
        // Return wrapped error
        return fmt.Errorf("operation failed: %w", err)
    }
    
    return nil
}
```

### Testing Guidelines

#### **Test Structure**
- **Unit Tests**: Test individual functions and methods
- **Integration Tests**: Test component interactions
- **End-to-End Tests**: Test complete workflows
- **Mock Testing**: Mock external dependencies

#### **Test Examples**
```go
func TestService_BusinessLogic(t *testing.T) {
    // Arrange
    mockRepo := &MockRepository{}
    service := NewService(mockRepo)
    input := Input{/* test data */}
    
    // Act
    result, err := service.BusinessLogic(input)
    
    // Assert
    assert.NoError(t, err)
    assert.Equal(t, expectedResult, result)
}
```

### API Development

#### **API Handler Pattern**
```go
func (h *Handler) HandleRequest(c *gin.Context) {
    // Parse request
    var req RequestDTO
    if err := c.ShouldBindJSON(&req); err != nil {
        utils.RespondError(c, err, utils.ConstructErrorAPIResp(err))
        return
    }
    
    // Validate request
    if err := h.validateRequest(req); err != nil {
        utils.RespondError(c, err, utils.ConstructErrorAPIResp(err))
        return
    }
    
    // Process request
    response, err := h.service.ProcessRequest(req)
    if err != nil {
        utils.RespondError(c, err, utils.ConstructErrorAPIResp(err))
        return
    }
    
    // Return response
    utils.RespondSuccess(c, response)
}
```

#### **Response Format**
```go
type APIResponse struct {
    Data  interface{} `json:"data"`
    Meta  Meta        `json:"meta"`
    Error *Error      `json:"error,omitempty"`
}

type Meta struct {
    Success bool   `json:"success"`
    Message string `json:"message"`
}
```

### Database Development

#### **Model Definition**
```go
type Model struct {
    ID        int64     `gorm:"primaryKey"`
    CreatedAt time.Time `gorm:"autoCreateTime"`
    UpdatedAt time.Time `gorm:"autoUpdateTime"`
    DeletedAt gorm.DeletedAt `gorm:"index"`
}
```

#### **Migration Pattern**
```go
func MigrateDatabase(db *gorm.DB) error {
    return db.AutoMigrate(
        &User{},
        &Order{},
        &Product{},
        // ... other models
    )
}
```

### External Integration

#### **HTTP Client Pattern**
```go
type HTTPClient struct {
    client  *http.Client
    baseURL string
    token   string
}

func (c *HTTPClient) makeRequest(method, endpoint string, body interface{}) (*http.Response, error) {
    // Prepare request
    req, err := c.prepareRequest(method, endpoint, body)
    if err != nil {
        return nil, err
    }
    
    // Add authentication
    c.addAuthentication(req)
    
    // Make request with retry
    return c.makeRequestWithRetry(req)
}
```

#### **Retry Pattern**
```go
func (c *HTTPClient) makeRequestWithRetry(req *http.Request) (*http.Response, error) {
    var resp *http.Response
    var err error
    
    for i := 0; i < MaxRetries; i++ {
        resp, err = c.client.Do(req)
        if err == nil && resp.StatusCode < 500 {
            return resp, nil
        }
        
        // Exponential backoff
        time.Sleep(time.Duration(1<<i) * time.Second)
    }
    
    return resp, err
}
```

### Performance Guidelines

#### **Database Performance**
- **Index Optimization**: Create indexes for frequently queried columns
- **Query Optimization**: Use efficient queries and avoid N+1 problems
- **Connection Pooling**: Configure optimal connection pool sizes
- **Read Replicas**: Use read replicas for read-heavy operations

#### **Caching Strategy**
- **Cache Frequently Accessed Data**: Product information, user data
- **Cache Invalidation**: Implement proper cache invalidation strategies
- **Cache Warming**: Pre-populate cache with essential data
- **Cache Monitoring**: Monitor cache hit rates and performance

#### **API Performance**
- **Response Compression**: Enable gzip compression
- **Request Validation**: Validate requests early to avoid unnecessary processing
- **Pagination**: Implement pagination for large datasets
- **Rate Limiting**: Implement rate limiting to prevent abuse

### Security Guidelines

#### **Authentication & Authorization**
- **JWT Tokens**: Use secure JWT tokens with proper expiration
- **Password Security**: Hash passwords with bcrypt
- **Session Management**: Implement secure session management
- **RBAC**: Implement role-based access control

#### **Input Validation**
- **Request Validation**: Validate all input parameters
- **SQL Injection Prevention**: Use parameterized queries
- **XSS Prevention**: Sanitize output data
- **CSRF Protection**: Implement CSRF protection for state-changing operations

#### **Data Security**
- **Encryption**: Encrypt sensitive data at rest and in transit
- **Secure Communication**: Use HTTPS for all communications
- **Secret Management**: Use secure secret management systems
- **Audit Logging**: Log all security-relevant events

---

## Deployment & Operations

### Deployment Architecture

#### **Environment Strategy**
- **Local Development**: Local environment with Docker for dependencies
- **Staging**: Production-like environment for testing
- **Production**: High-availability production environment

#### **Deployment Process**
1. **Code Review**: Peer review of all code changes
2. **Testing**: Automated testing pipeline
3. **Staging Deployment**: Deploy to staging for validation
4. **Production Deployment**: Deploy to production with monitoring
5. **Health Checks**: Verify deployment success
6. **Rollback Plan**: Immediate rollback capability

### Infrastructure Components

#### **Application Server**
- **Runtime**: Go application server
- **Process Management**: Systemd for process management
- **Health Checks**: HTTP health check endpoints
- **Graceful Shutdown**: Proper shutdown signal handling

#### **Database Infrastructure**
- **Primary Database**: MySQL with high availability
- **Read Replicas**: MySQL read replicas for scaling
- **Connection Pooling**: Optimized connection management
- **Backup Strategy**: Automated backups with point-in-time recovery

#### **Caching Layer**
- **Redis Cluster**: High-availability Redis setup
- **Cache Warming**: Automated cache warming on startup
- **Cache Monitoring**: Real-time cache performance monitoring
- **Failover Strategy**: Automatic failover for cache failures

#### **Message Queue**
- **RabbitMQ**: Message queue for async processing
- **Queue Management**: Dead letter queues for failed messages
- **Monitoring**: Queue depth and processing time monitoring
- **Scaling**: Horizontal scaling based on queue depth

### Monitoring & Observability

#### **Application Monitoring**
- **Prometheus Metrics**: Custom business and technical metrics
- **Health Checks**: Comprehensive health check endpoints
- **Performance Monitoring**: Response time and throughput tracking
- **Error Tracking**: Comprehensive error logging and alerting

#### **Infrastructure Monitoring**
- **Server Monitoring**: CPU, memory, disk, and network monitoring
- **Database Monitoring**: Query performance and connection monitoring
- **Cache Monitoring**: Hit rates and performance metrics
- **Network Monitoring**: API response times and availability

#### **Logging Strategy**
- **Structured Logging**: JSON-formatted logs with context
- **Log Aggregation**: Centralized log collection and analysis
- **Log Retention**: Appropriate log retention policies
- **Log Analysis**: Automated log analysis and alerting

#### **Alerting System**
- **Slack Integration**: Real-time alerts to team channels
- **Alert Prioritization**: Critical, warning, and info alerts
- **On-Call Rotation**: Automated on-call notification system
- **Incident Response**: Documented incident response procedures

### Performance Optimization

#### **Application Performance**
- **Connection Pooling**: Optimized database connection pools
- **Caching Strategy**: Multi-layer caching architecture
- **Async Processing**: Background job processing for heavy operations
- **Resource Management**: Proper resource cleanup and management

#### **Database Performance**
- **Query Optimization**: Optimized database queries
- **Index Strategy**: Strategic database indexing
- **Partitioning**: Table partitioning for large datasets
- **Read Scaling**: Read replica utilization

#### **API Performance**
- **Response Caching**: Strategic API response caching
- **Compression**: Response compression for large payloads
- **Rate Limiting**: Protect against API abuse
- **Load Balancing**: Distribute traffic across multiple instances

### Security Operations

#### **Security Monitoring**
- **Authentication Monitoring**: Failed login attempt tracking
- **API Security**: API abuse detection and prevention
- **Data Access Monitoring**: Sensitive data access logging
- **Vulnerability Scanning**: Regular security vulnerability scans

#### **Incident Response**
- **Security Incident Response**: Documented security incident procedures
- **Breach Notification**: Automated breach notification system
- **Forensic Logging**: Comprehensive audit trail maintenance
- **Recovery Procedures**: Data recovery and system restoration procedures

### Backup & Recovery

#### **Backup Strategy**
- **Database Backups**: Automated daily database backups
- **File System Backups**: Application and configuration backups
- **Cross-Region Backups**: Geographically distributed backups
- **Backup Validation**: Regular backup integrity verification

#### **Recovery Procedures**
- **Disaster Recovery**: Documented disaster recovery procedures
- **Recovery Time Objectives**: Defined RTO and RPO targets
- **Failover Procedures**: Automated failover capabilities
- **Data Recovery**: Point-in-time recovery capabilities

### Scaling Strategy

#### **Horizontal Scaling**
- **Load Balancing**: Distribute traffic across multiple instances
- **Auto Scaling**: Automatic scaling based on demand
- **Database Scaling**: Read replica scaling for read-heavy workloads
- **Cache Scaling**: Redis cluster scaling for cache performance

#### **Vertical Scaling**
- **Resource Optimization**: Optimize resource utilization
- **Performance Profiling**: Regular performance profiling
- **Capacity Planning**: Proactive capacity planning
- **Resource Monitoring**: Real-time resource utilization monitoring

### Cost Optimization

#### **Resource Optimization**
- **Right-sizing**: Optimize instance sizes for workload
- **Reserved Instances**: Use reserved instances for predictable workloads
- **Auto-scaling**: Scale resources based on demand
- **Resource Monitoring**: Monitor and optimize resource usage

#### **Cost Monitoring**
- **Cost Tracking**: Real-time cost monitoring and alerting
- **Budget Management**: Set and monitor budget limits
- **Cost Analysis**: Regular cost analysis and optimization
- **Resource Tagging**: Proper resource tagging for cost allocation

---

## Appendices

### Appendix A: Common Commands

#### **Development Commands**
```bash
# Start application
go run cmd/ondc/main.go

# Run tests
go test ./...
go test -v ./internal/ondc/service/
go test -cover ./...

# Build application
go build -o bin/ondc cmd/ondc/main.go

# Format code
go fmt ./...

# Check for issues
go vet ./...

# Download dependencies
go mod download
go mod tidy
```

#### **Database Commands**
```bash
# Connect to database
mysql -h hostname -u username -p database_name

# Run migrations
# (Application handles migrations automatically)

# Backup database
mysqldump -h hostname -u username -p database_name > backup.sql

# Restore database
mysql -h hostname -u username -p database_name < backup.sql
```

### Appendix B: Configuration Examples

#### **Local Development Config**
```json
{
  "name": "diagon-alley-local",
  "mode": "debug",
  "env": "local",
  "db_config": {
    "hostname": "localhost",
    "username": "root",
    "password": "password",
    "database": "kirana_local",
    "port": "3306",
    "max_open_connections": 10
  },
  "redis_config": {
    "hostname": "localhost:6379",
    "password": "",
    "username": "",
    "database": "0"
  }
}
```

#### **Production Config Template**
```json
{
  "name": "diagon-alley-prod",
  "mode": "release",
  "env": "prod",
  "db_config": {
    "hostname": "prod-db-host",
    "username": "prod_user",
    "password": "secure_password",
    "database": "kirana_prod",
    "port": "3306",
    "max_open_connections": 50
  },
  "payment_config": {
    "razorpay_config": {
      "key_id": "rzp_live_key",
      "key_secret": "secure_secret",
      "wh_secret": "webhook_secret"
    }
  }
}
```

### Appendix C: Error Codes

#### **API Error Codes**
- **INVALID_REQUEST_STRUCT**: Malformed request body
- **USER_NOT_FOUND**: User authentication failed
- **INVALID_AUTH_TOKEN**: Invalid or expired token
- **INTERNAL_ERROR**: Server-side error
- **RATE_LIMIT_EXCEEDED**: Too many requests
- **PAYMENT_FAILED**: Payment processing failed
- **ORDER_NOT_FOUND**: Order not found
- **PRODUCT_OUT_OF_STOCK**: Product unavailable
- **INVALID_COUPON**: Coupon validation failed
- **SERVICE_UNAVAILABLE**: External service unavailable

### Appendix D: Performance Benchmarks

#### **API Performance Targets**
- **Authentication**: < 100ms average response time
- **Product Search**: < 200ms average response time
- **Order Creation**: < 500ms average response time
- **Payment Processing**: < 1000ms average response time
- **Database Queries**: < 50ms average query time

#### **System Performance Targets**
- **Uptime**: 99.9% availability
- **Throughput**: 1000 requests/second peak
- **Concurrent Users**: 10,000 concurrent users
- **Database Connections**: 100 concurrent connections max
- **Memory Usage**: < 2GB per application instance

---

*This documentation provides a comprehensive overview of the Diagon Alley Backend system. For specific implementation details, refer to the source code and inline documentation.*