package main

import (
	"fmt"
	"kc/internal/ondc"
	"kc/internal/ondc/config"
	"kc/internal/ondc/external/slack"
	"log"
)

func main() {
	app, env := ondc.InitializeApp()
	port := config.GetDeploymentPort()
	if env == "prod" {
		slack.SendSlackMessage("Diagon Alley BE deployment successfull")
		if err := app.Run(fmt.Sprintf(":%s", port)); err != nil {
			slack.SendSlackMessage("Diagon Alley BE deployment failed")
			log.Fatal(err)
		}
	} else {
		if err := app.Run(fmt.Sprintf(":%s", port)); err != nil {
			slack.SendSlackMessage("Diagon Alley BE deployment failed")
			log.Fatal(err)
		}
	}

}
