name: Build and deploy container app to Azure Web App - kc-ondcv2

on:
  pull_request:
    branches:
      - main
  workflow_dispatch:

jobs:
  build:
    runs-on: 'ubuntu-latest'

    steps:
    - uses: actions/checkout@v2

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Log in to registry
      uses: docker/login-action@v2
      with:
        registry: https://kiranaclubv3.azurecr.io/
        username: ${{ secrets.AzureAppService_ContainerUsername_13707e3fa73f4352ba62c33387b3b2ba }}
        password: ${{ secrets.AzureAppService_ContainerPassword_127ba454a3c846e9994e422e39eb49c6 }}

    - name: Build and push container image to registry
      run: |
          # Decode secrets to files
          echo "${{ secrets.CONFIG_3APR }}" | base64 --decode > internal/ondc/config.json
          echo "${{ secrets.FIREBASE_JSON }}" | base64 --decode > internal/ondc/firebase.json
          echo "${{ secrets.DELHIVERY_CONFIG }}" | base64 --decode > internal/ondc/delhivery_config.json
          echo "${{ secrets.TPL_CONFIG }}" | base64 --decode > internal/ondc/tpl_config.json
          
          
          # Build with GitHub token as build arg
          docker build \
              --build-arg ONDC_DEPLOYMENT_ENV=stage \
              --build-arg GITHUB_TOKEN=${{ secrets.GH_TOKEN }} \
              -t kiranaclubv3.azurecr.io/${{ secrets.AzureAppService_ContainerUsername_13707e3fa73f4352ba62c33387b3b2ba }}/ondc:${{ github.sha }} \
              --file internal/ondc/Dockerfile .
              
          docker push kiranaclubv3.azurecr.io/${{ secrets.AzureAppService_ContainerUsername_13707e3fa73f4352ba62c33387b3b2ba }}/ondc:${{ github.sha }}
          
  deploy:
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: 'stage'
      url: ${{ steps.deploy-to-webapp.outputs.webapp-url }}

    steps:
    - name: Deploy to Azure Web App
      id: deploy-to-webapp
      uses: azure/webapps-deploy@v2
      with:
        app-name: 'kc-ondcv2'
        slot-name: 'stage'
        publish-profile: ${{ secrets.AZUREAPPSERVICE_PUBLISHPROFILE_STAGE_23JAN25 }}
        images: 'kiranaclubv3.azurecr.io/${{ secrets.AzureAppService_ContainerUsername_13707e3fa73f4352ba62c33387b3b2ba }}/ondc:${{ github.sha }}'
