# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is "Diagon Alley BE" - a comprehensive e-commerce backend for Kirana Club built in Go. The system handles ONDC (Open Network for Digital Commerce) operations, order management, logistics, payments, and various third-party integrations.

## Development Setup

### Environment Variables Required
```bash
export ONDC_DEPLOYMENT_ENV=local
export ONDC_CONFIG=/Users/<USER>/Desktop/diagon-alley-BE/internal/ondc/config.json
export IVR_CONFIG=/Users/<USER>/Desktop/diagon-alley-BE/internal/ondc/ivr.json
export TPL_CONFIG=<absolute_path_to_tpl_config>
```

### Running the Application
```bash
# Run the main application
go run cmd/ondc/main.go

# Run tests
go test ./...

# Run specific test
go test ./internal/ondc/service/test.go

# Build the application
go build -o bin/ondc cmd/ondc/main.go
```

## Architecture Overview

### Core Structure
- **cmd/ondc/main.go**: Application entry point
- **internal/ondc/**: Main application code following Go project layout
- **external/**: Third-party integrations (Delhivery, Shiprocket, Razorpay, etc.)

### Key Components

#### 1. Service Layer (`internal/ondc/service/`)
- **Core Business Logic**: Order management, inventory, payments, shipping
- **Integration Services**: LSP allocation, AWB management, cart operations
- **Specialized Services**: Coupons, loyalty, rewards, financial operations

#### 2. API Layer (`internal/ondc/api/`)
- REST API handlers organized by domain
- Authentication and authorization
- Request/response transformations

#### 3. Infrastructure (`internal/ondc/infrastructure/`)
- **Database**: MySQL with GORM ORM
- **Cache**: Redis (both Azure Redis and GCP Redis)
- **Firebase**: Document storage and real-time features
- **Messaging**: RabbitMQ for async operations
- **Monitoring**: Prometheus metrics

#### 4. External Integrations (`internal/ondc/external/`)
- **Logistics**: Delhivery, Shiprocket, Shipway, Shipdelight
- **Payments**: Razorpay integration
- **Communication**: Slack, WhatsApp, Exotel IVR
- **E-commerce**: EasyEcom, Unicommerce integration
- **Analytics**: Mixpanel tracking

#### 5. Queue System (`internal/ondc/queue/`)
- **IVR Queue**: Handles IVR call processing
- **Order Cancel Queue**: Manages order cancellation workflows
- **Payment Queue**: Processes payment-related operations

### Database Architecture
- **Primary DB**: MySQL for transactional data
- **Finance DB**: Separate database for financial operations
- **Read-only Replica**: For read-heavy operations
- **Firebase**: Real-time data and metadata
- **Redis**: Caching and session management

### Key Business Domains

#### Order Management
- Order lifecycle from creation to delivery
- Multi-seller support with different logistics providers
- Real-time order tracking and status updates
- Return and cancellation handling

#### Inventory Management
- Product catalog with search capabilities
- Stock management across multiple sellers
- Pricing and promotional logic

#### Logistics & Shipping
- Multi-courier integration (Delhivery, Shiprocket, etc.)
- AWB (Air Waybill) management
- Shipping rate calculation and optimization
- Real-time tracking integration

#### Payment Processing
- Razorpay integration for payment processing
- Refund management
- Payment reconciliation

#### Customer Support
- Ticket management system
- IVR integration for customer calls
- WhatsApp notifications

## Development Patterns

### Error Handling
- Custom exception types in `internal/ondc/exceptions/`
- Standardized error responses via `utils/apiResp.go`
- Slack notifications for critical errors

### Configuration Management
- Environment-based configuration loading
- Separate configs for different environments
- Centralized config struct in `config/config.go`

### Caching Strategy
- Redis for frequently accessed data
- In-memory caching for static reference data
- Cache invalidation patterns for data consistency

### Queue Processing
- Background job processing for async operations
- Retry mechanisms for failed operations
- Queue monitoring and alerting

## Testing

### Test Structure
- Unit tests alongside source code
- Integration tests for external service interactions
- Database tests with test fixtures

### Running Tests
```bash
# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run tests for specific package
go test ./internal/ondc/service/
```

## Monitoring & Observability

### Logging
- Structured logging with Logrus
- Different log levels for different environments
- Centralized logging configuration

### Metrics
- Prometheus metrics for API endpoints
- Custom business metrics tracking
- Performance monitoring

### Alerting
- Slack integration for error notifications
- Critical system alerts

## Common Development Tasks

### Adding New API Endpoints
1. Create handler in `internal/ondc/api/`
2. Add route in `internal/ondc/routes/`
3. Implement business logic in `internal/ondc/service/`
4. Add database models in `internal/ondc/models/`

### Adding New External Integration
1. Create integration package in `internal/ondc/external/`
2. Define constants and models
3. Implement service interface
4. Add configuration in main config struct

### Database Changes
- Use GORM migrations for schema changes
- Add new models in `internal/ondc/models/dao/`
- Create DTOs in `internal/ondc/models/dto/`

## Performance Considerations

### Database Optimization
- Use read replicas for read-heavy operations
- Implement proper indexing strategies
- Connection pooling configuration

### Caching Strategy
- Cache frequently accessed data
- Implement cache warming strategies
- Use appropriate TTL values

### Queue Processing
- Batch processing for efficiency
- Proper error handling and retries
- Monitor queue depth and processing times